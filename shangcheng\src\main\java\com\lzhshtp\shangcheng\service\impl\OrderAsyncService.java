package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.mapper.OrderMapper;
import com.lzhshtp.shangcheng.model.Order;
import com.lzhshtp.shangcheng.service.VerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class OrderAsyncService {

    private static final Logger logger = LoggerFactory.getLogger(OrderAsyncService.class);

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private VerificationService verificationService;

    @Async
    public void scheduleDelivery(Long orderId) {
        try {
            // Wait 10 seconds to simulate shipping time
            Thread.sleep(10000);

            // Update the order in a new transaction
            updateOrderToDelivered(orderId);

        } catch (InterruptedException e) {
            logger.error("Auto-update task after shipping was interrupted, Order ID: {}", orderId, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Auto-update task after shipping failed, Order ID: {}", orderId, e);
        }
    }

    @Transactional
    public void updateOrderToDelivered(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        // Double-check the order status to prevent changes if it was, for example, cancelled during transit
        if (order != null && Order.OrderStatus.SHIPPED.equals(order.getStatus())) {
            order.setStatus(Order.OrderStatus.DELIVERED);
            orderMapper.updateById(order);
            logger.info("Order ID {} status automatically updated to -> DELIVERED", orderId);
        } else {
            logger.warn("Order ID {} status is no longer 'SHIPPED', auto-update task cancelled.", orderId);
        }
    }

    /**
     * 异步任务：模拟商品送达验货中心
     */
    @Async
    public void scheduleVerificationArrival(Long orderId) {
        try {
            // 等待5秒模拟运输时间（比直接送达短一些）
            Thread.sleep(5000);

            // 更新订单状态为官方验货中
            updateOrderToPendingVerification(orderId);

        } catch (InterruptedException e) {
            logger.error("验货中心送达任务被中断，订单ID: {}", orderId, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("验货中心送达任务失败，订单ID: {}", orderId, e);
        }
    }

    /**
     * 更新订单状态为官方验货中
     */
    @Transactional
    public void updateOrderToPendingVerification(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        // 检查订单状态，确保是已发货状态
        if (order != null && Order.OrderStatus.SHIPPED.equals(order.getStatus())) {
            // 更新订单状态为官方验货中
            order.setStatus(Order.OrderStatus.VERIFYING);
            order.setCurrentLocation("官方验货中心");
            orderMapper.updateById(order);

        } else {
            logger.warn("订单 {} 状态不再是'已发货'，验货中心送达任务取消", orderId);
        }
    }
}
