package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.model.SellerReview;

import java.util.List;

/**
 * 评价服务接口
 */
public interface ReviewService {

    /**
     * 提交卖家评价
     * 
     * @param buyerId 买家ID
     * @param reviewRequest 评价请求
     * @return 评价响应
     */
    ReviewResponse submitSellerReview(Long buyerId, ReviewRequest reviewRequest);

    /**
     * 检查订单是否可以评价
     * 
     * @param orderId 订单ID
     * @param buyerId 买家ID
     * @return 是否可以评价
     */
    boolean canReviewOrder(Long orderId, Long buyerId);

    /**
     * 检查订单是否已经评价过
     * 
     * @param orderId 订单ID
     * @return 是否已评价
     */
    boolean hasReviewed(Long orderId);

    /**
     * 获取订单的评价信息
     * 
     * @param orderId 订单ID
     * @return 评价信息，如果未评价则返回null
     */
    ReviewResponse getOrderReview(Long orderId);

    /**
     * 获取卖家的信用信息
     * 
     * @param sellerId 卖家ID
     * @return 卖家信用信息
     */
    SellerCreditInfo getSellerCreditInfo(Long sellerId);

    /**
     * 获取卖家的评价列表
     * 
     * @param sellerId 卖家ID
     * @param page 页码
     * @param pageSize 页大小
     * @return 评价列表
     */
    List<ReviewResponse> getSellerReviews(Long sellerId, int page, int pageSize);

    /**
     * 获取买家的评价历史
     * 
     * @param buyerId 买家ID
     * @param page 页码
     * @param pageSize 页大小
     * @return 评价历史
     */
    List<ReviewResponse> getBuyerReviews(Long buyerId, int page, int pageSize);

    /**
     * 获取评价统计信息（管理员用）
     * 
     * @param days 统计天数
     * @return 统计信息
     */
    ReviewStatistics getReviewStatistics(int days);

    /**
     * 检测异常评价行为
     * 
     * @param sellerId 卖家ID
     * @param days 检测天数
     * @return 是否存在异常
     */
    boolean detectAbnormalReviews(Long sellerId, int days);

    /**
     * 申请评价申诉
     * 
     * @param reviewId 评价ID
     * @param reason 申诉原因
     * @param evidence 申诉证据
     * @return 申诉是否成功
     */
    boolean appealReview(Long reviewId, String reason, String evidence);

    /**
     * 管理员处理评价申诉
     * 
     * @param reviewId 评价ID
     * @param approved 是否批准申诉
     * @param adminNote 管理员备注
     * @return 处理是否成功
     */
    boolean handleReviewAppeal(Long reviewId, boolean approved, String adminNote);
}
