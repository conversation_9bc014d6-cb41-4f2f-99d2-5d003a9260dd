import request from '@/utils/request';

/**
 * 添加收藏
 * @param {number} productId - 商品ID
 * @returns {Promise} - 请求结果
 */
export function addFavorite(productId) {
  return request({
    url: `/favorites/${productId}`,
    method: 'post'
  });
}

/**
 * 取消收藏（通过商品ID）
 * @param {number} productId - 商品ID
 * @returns {Promise} - 请求结果
 */
export function removeFavorite(productId) {
  return request({
    url: `/favorites/product/${productId}`,
    method: 'delete'
  });
}

/**
 * 检查商品是否已收藏
 * @param {number} productId - 商品ID
 * @returns {Promise} - 请求结果
 */
export function checkFavorite(productId) {
  return request({
    url: `/favorites/check/${productId}`,
    method: 'get'
  });
}

/**
 * 获取用户收藏列表
 * @returns {Promise} - 请求结果
 */
export function getUserFavorites() {
  return request({
    url: '/favorites',
    method: 'get'
  });
}

/**
 * 获取商品被收藏次数
 * @param {number} productId - 商品ID
 * @returns {Promise} - 请求结果
 */
export function getProductFavoriteCount(productId) {
  return request({
    url: `/favorites/count/${productId}`,
    method: 'get'
  });
}
