package com.lzhshtp.shangcheng.controller.audit;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.audit.SecondReviewDecisionRequest;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import com.lzhshtp.shangcheng.service.audit.SecondReviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 二度复审任务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/audit/second-review")
@CrossOrigin(origins = "*")
public class SecondReviewTaskController {

    @Autowired
    private SecondReviewService secondReviewService;

    /**
     * 获取二度复审任务列表
     */
    @GetMapping("/tasks")
    public ApiResponse<PageResult<SecondReviewTask>> getSecondReviewTasks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "4") Integer pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String escalationReason,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) Long reviewerId) {

        try {
            PageResult<SecondReviewTask> result = secondReviewService.getSecondReviewTasks(
                page, pageSize, status, escalationReason, productId, reviewerId);

            return ApiResponse.success("获取二度复审任务成功", result);

        } catch (Exception e) {
            log.error("获取二度复审任务失败", e);
            return ApiResponse.fail("获取二度复审任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取二度复审统计数据
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getSecondReviewStats() {
        try {
            Map<String, Object> stats = secondReviewService.getSecondReviewStats();
            return ApiResponse.success("获取统计数据成功", stats);

        } catch (Exception e) {
            log.error("获取二度复审统计数据失败", e);
            return ApiResponse.fail("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 认领二度复审任务
     */
    @PostMapping("/tasks/{taskId}/claim")
    public ApiResponse<String> claimSecondReviewTask(
            @PathVariable Long taskId,
            @RequestBody Map<String, Long> request) {

        try {
            Long reviewerId = request.get("reviewerId");
            boolean success = secondReviewService.claimSecondReviewTask(taskId, reviewerId);

            if (success) {
                return ApiResponse.success("复审任务认领成功");
            } else {
                return ApiResponse.fail("复审任务认领失败，可能已被其他复审员认领");
            }

        } catch (Exception e) {
            log.error("认领二度复审任务失败，taskId: {}", taskId, e);
            return ApiResponse.fail("认领复审任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取二度复审任务详情
     */
    @GetMapping("/tasks/{taskId}")
    public ApiResponse<SecondReviewTask> getSecondReviewTaskDetail(@PathVariable Long taskId) {
        try {
            SecondReviewTask task = secondReviewService.getSecondReviewTaskDetail(taskId);

            if (task == null) {
                return ApiResponse.fail("复审任务不存在");
            }

            return ApiResponse.success("获取复审任务详情成功", task);

        } catch (Exception e) {
            log.error("获取二度复审任务详情失败，taskId: {}", taskId, e);
            return ApiResponse.fail("获取复审任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 提交二度复审决策
     */
    @PostMapping("/tasks/{taskId}/decision")
    public ApiResponse<String> submitSecondReviewDecision(
            @PathVariable Long taskId,
            @RequestBody SecondReviewDecisionRequest request) {

        try {
            secondReviewService.submitSecondReviewDecision(taskId, request);
            return ApiResponse.success("复审决策提交成功");

        } catch (Exception e) {
            log.error("提交二度复审决策失败，taskId: {}", taskId, e);
            return ApiResponse.fail("提交复审决策失败：" + e.getMessage());
        }
    }

    /**
     * 获取二度复审任务的所有材料
     */
    @GetMapping("/tasks/{taskId}/materials")
    public ApiResponse<Map<String, Object>> getSecondReviewTaskMaterials(@PathVariable Long taskId) {
        try {
            Map<String, Object> materials = secondReviewService.getSecondReviewTaskMaterials(taskId);
            return ApiResponse.success("获取复审材料成功", materials);

        } catch (Exception e) {
            log.error("获取二度复审材料失败，taskId: {}", taskId, e);
            return ApiResponse.fail("获取复审材料失败：" + e.getMessage());
        }
    }
}
