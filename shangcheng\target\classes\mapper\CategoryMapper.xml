<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.CategoryMapper">
    
    <!-- 查询所有根分类 -->
    <select id="selectRootCategories" resultType="com.lzhshtp.shangcheng.model.Category">
        SELECT *
        FROM tb_lzhshtp_product_categories
        WHERE lzhshtp_parent_category_id IS NULL
    </select>
    
    <!-- 根据父分类ID查询子分类 -->
    <select id="selectCategoriesByParentId" resultType="com.lzhshtp.shangcheng.model.Category">
        SELECT *
        FROM tb_lzhshtp_product_categories
        WHERE lzhshtp_parent_category_id = #{parentId}
    </select>

    <!-- 根据分类名称查询分类 -->
    <select id="findByName" resultType="com.lzhshtp.shangcheng.model.Category">
        SELECT *
        FROM tb_lzhshtp_product_categories
        WHERE lzhshtp_category_name = #{name}
    </select>

</mapper>