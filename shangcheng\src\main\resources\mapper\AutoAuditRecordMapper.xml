<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.AutoAuditRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="AutoAuditRecordResultMap" type="com.lzhshtp.shangcheng.model.AutoAuditRecord">
        <id column="lzhshtp_record_id" property="recordId"/>
        <result column="lzhshtp_product_id" property="productId"/>
        <result column="lzhshtp_text_audit_result" property="textAuditResult"/>
        <result column="lzhshtp_image_audit_result" property="imageAuditResult"/>
        <result column="lzhshtp_credit_audit_result" property="creditAuditResult"/>
        <result column="lzhshtp_price_audit_result" property="priceAuditResult"/>
        <result column="lzhshtp_publishing_behavior_result" property="publishingBehaviorResult"/>
        <result column="lzhshtp_final_decision" property="finalDecision"/>
        <result column="lzhshtp_decision_reason" property="decisionReason"/>
        <result column="lzhshtp_risk_score" property="riskScore"/>
        <result column="lzhshtp_created_time" property="createdTime"/>
        <result column="lzhshtp_updated_time" property="updatedTime"/>
        <!-- 非数据库字段，用于显示 -->
        <result column="product_title" property="productTitle"/>
        <result column="product_price" property="productPrice"/>
    </resultMap>

    <!-- 根据条件查询审核记录 -->
    <select id="findByConditions" parameterType="map" resultMap="AutoAuditRecordResultMap">
        SELECT * FROM tb_lzhshtp_auto_audit_records
        WHERE 1=1
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="finalDecision != null and finalDecision != ''">
            AND lzhshtp_final_decision = #{finalDecision}
        </if>
        <if test="startDate != null and startDate != ''">
            AND lzhshtp_created_time <![CDATA[>=]]> #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND lzhshtp_created_time <![CDATA[<=]]> #{endDate}
        </if>
        ORDER BY lzhshtp_created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计审核记录数量 -->
    <select id="countByConditions" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_auto_audit_records
        WHERE 1=1
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="finalDecision != null and finalDecision != ''">
            AND lzhshtp_final_decision = #{finalDecision}
        </if>
        <if test="startDate != null and startDate != ''">
            AND lzhshtp_created_time <![CDATA[>=]]> #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND lzhshtp_created_time <![CDATA[<=]]> #{endDate}
        </if>
    </select>

    <!-- 根据决策类型统计数量 -->
    <select id="countByDecision" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_auto_audit_records
        WHERE lzhshtp_final_decision = #{decision}
        <if test="params.startDate != null and params.startDate != ''">
            AND lzhshtp_created_time <![CDATA[>=]]> #{params.startDate}
        </if>
        <if test="params.endDate != null and params.endDate != ''">
            AND lzhshtp_created_time <![CDATA[<=]]> #{params.endDate}
        </if>
    </select>

</mapper>
