<template>
  <div class="seller-profile-page">
    <!-- 顶部导航 -->
    <nav class="top-nav">
      <div class="container nav-content">
        <router-link to="/home" class="logo">易转</router-link>
        <div class="search-area">
          <div class="search-bar">
            <input type="text" class="search-input" placeholder="搜索想要的商品...">
            <button class="search-button">搜索</button>
          </div>
        </div>
        <div class="nav-right">
          <router-link to="/profile" class="user-avatar" title="个人中心">
            <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 卖家信息区域 -->
    <div class="container">
      <div class="seller-header card-container">
        <!-- 个人信息展示 -->
        <div class="seller-info">
          <div class="seller-avatar-container">
            <img :src="seller.avatarUrl || defaultAvatar" :alt="seller.username" class="seller-avatar-large">
          </div>
          <div class="seller-details">
            <h1 class="seller-name">{{ seller.username }}
            </h1>
            <div class="seller-stats">
              <div class="stat-item credit-score-item">
                <div class="credit-score-display">
                  <span class="stat-value credit-score">{{ creditInfo.creditScore || 100 }}</span>
                  <span class="credit-level" :style="{ color: creditInfo.creditLevelColor }">
                    {{ creditInfo.creditLevel || '普通卖家' }}
                  </span>
                </div>
                <span class="stat-label">信用分</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ creditInfo.positiveRate || 0 }}%</span>
                <span class="stat-label">好评率</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ creditInfo.totalReviews || 0 }}</span>
                <span class="stat-label">总评价</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ followersCount }}</span>
                <span class="stat-label">粉丝</span>
              </div>
            </div>
            <div class="seller-bio">
              <p>{{ seller.bio || '这个卖家很懒，还没有填写个人简介...' }}</p>
            </div>
            <div class="seller-location">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
              <span>{{ seller.location || '未知地区' }}</span>
            </div>
            <div class="seller-join-date">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
              <span>{{ seller.registrationDate ? formatDate(seller.registrationDate) + ' 加入' : '加入时间未知' }}</span>
            </div>
          </div>
        </div>
        <div class="seller-actions">
          <button class="action-button message-button" @click="chatWithSeller">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
            发消息
          </button>
          <button class="action-button follow-button" :class="{ 'followed': isFollowed }" @click="toggleFollow">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line></svg>
            {{ isFollowed ? '已关注' : '关注' }}
          </button>
        </div>
      </div>

      <!-- 卖家商品列表 -->
      <div class="seller-products card-container">
        <div class="section-header">
          <h2 class="section-title">在售商品</h2>
          <div class="section-filters">
            <span class="filter-item" :class="{ active: currentFilter === 'all' }" @click="changeFilter('all')">全部</span>
            <span class="filter-item" :class="{ active: currentFilter === 'latest' }" @click="changeFilter('latest')">最新上架</span>
            <span class="filter-item" :class="{ active: currentFilter === 'price-asc' }" @click="changeFilter('price-asc')">价格从低到高</span>
            <span class="filter-item" :class="{ active: currentFilter === 'price-desc' }" @click="changeFilter('price-desc')">价格从高到低</span>
          </div>
        </div>

        <!-- 商品列表 -->
        <div v-if="loading" class="loading-container">
          <div class="spinner"></div>
          <p>加载商品中...</p>
        </div>

        <div v-else-if="products.length === 0" class="empty-products">
          <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
          <p>该卖家暂无在售商品</p>
        </div>

        <div v-else class="products-grid">
          <div v-for="product in products" :key="product.id || product.productId" class="product-card" @click="goToProductDetail(product.id || product.productId)">
            <div class="product-image">
              <img :src="product.imageUrls && product.imageUrls.length > 0 ? product.imageUrls[0] : 'https://via.placeholder.com/300x300'" :alt="product.title">
            </div>
            <div class="product-info">
              <h3 class="product-title">{{ product.title }}</h3>
              <div class="product-price">¥{{ formatPrice(product.price) }}</div>
              <div class="product-meta">
                <span class="product-condition">{{ product.condition }}</span>
                <span class="product-date">{{ product.createdAt ? formatDate(product.createdAt) : '时间未知' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="products.length > 0" class="pagination">
          <button class="page-button" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">上一页</button>
          <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
          <button class="page-button" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">下一页</button>
        </div>
      </div>


      <!-- 买家评价 -->
      <div class="seller-reviews card-container">
        <div class="section-header">
          <h2 class="section-title">买家评价 ({{ reviews.length }})</h2>
          <el-select v-model="reviewFilter" placeholder="筛选评价" style="width: 120px;">
            <el-option label="全部" value="all"></el-option>
            <el-option label="好评" value="positive"></el-option>
            <el-option label="中评" value="neutral"></el-option>
            <el-option label="差评" value="negative"></el-option>
          </el-select>
        </div>

        <div class="reviews-list">
          <div v-for="review in filteredReviews" :key="review.reviewId" class="review-item">
            <div class="review-header">
              <div class="reviewer-info">
                <span class="reviewer-name">{{ review.buyerName }}</span>
                <el-rate :model-value="review.rating || 0" disabled show-score text-color="#ff9900" score-template="{value}"></el-rate>
              </div>
              <span class="review-date">{{ review.createdTime ? formatDate(review.createdTime) : '时间未知' }}</span>
            </div>

            <div class="review-content">
              <p v-if="review.reviewContent">{{ review.reviewContent }}</p>
              <p v-else class="no-content">用户没有填写评价内容</p>

              <div v-if="review.reviewTags && review.reviewTags.length > 0" class="review-tags">
                <el-tag v-for="tag in review.reviewTags" :key="tag" size="small" type="info">
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <div class="review-product">
              <span class="product-name">商品：{{ review.productTitle }}</span>
            </div>
          </div>
        </div>

        <div v-if="filteredReviews.length === 0" class="empty-reviews">
          <p>暂无相关评价</p>
        </div>

        <!-- 分页 -->
        <div v-if="reviews.length > 0" class="review-pagination">
          <button class="page-button" :disabled="reviewPage === 1" @click="changeReviewPage(reviewPage - 1)">上一页</button>
          <span class="page-info">{{ reviewPage }} / {{ Math.ceil(reviewTotal / reviewPageSize) }}</span>
          <button class="page-button" :disabled="reviewPage >= Math.ceil(reviewTotal / reviewPageSize)" @click="changeReviewPage(reviewPage + 1)">下一页</button>
        </div>
      </div>
    </div>

    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getSellerInfo, getSellerProducts } from '@/api/user';
import { followUser, unfollowUser, checkFollowing, getFollowersCount } from '@/api/follow';
import { getOrCreateConversation } from '@/api/message';
import { getSellerCreditInfo, getSellerReviews } from '@/api/review';
import FloatingActionButtons from '@/components/FloatingActionButtons.vue';

// 路由参数
const route = useRoute();
const router = useRouter();

// 用户状态
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 加载状态
const loading = ref(false);

// 卖家信息
const seller = ref({
  id: null,
  username: '',
  avatarUrl: null,
  bio: '',
  location: '',
  registrationDate: null,
  creditScore: null
});

// 关注状态
const isFollowed = ref(false);
// 粉丝数量
const followersCount = ref(0);

// 商品列表
const products = ref([]);
const currentPage = ref(1);
const pageSize = ref(8);
const totalProducts = ref(0);
const totalPages = computed(() => Math.ceil(totalProducts.value / pageSize.value));
const currentFilter = ref('all'); // 当前选中的筛选条件

// 信用分相关数据
const creditInfo = ref({
  creditScore: 100,
  creditLevel: '普通卖家',
  creditLevelColor: '#8c8c8c',
  totalReviews: 0,
  averageRating: 0,
  positiveRate: 0,
  positiveReviews: 0,
  negativeReviews: 0,
  ratingDistribution: {}
});

// 评价相关数据
const reviews = ref([]);
const reviewFilter = ref('all');
const reviewPage = ref(1);
const reviewPageSize = ref(10);
const reviewTotal = ref(0);

// 切换关注状态
const toggleFollow = async () => {
  if (!userInfo.value) {
    // 用户未登录，跳转到登录页
    router.push('/login?redirect=' + encodeURIComponent(route.fullPath));
    return;
  }

  try {
    if (isFollowed.value) {
      // 取消关注
      const response = await unfollowUser(seller.value.id);
      if (response.code === 200) {
        isFollowed.value = false;
        // 刷新粉丝数量
        await fetchFollowersCount();
      } else {
        console.error('取消关注失败:', response.message);
      }
    } else {
      // 关注
      const response = await followUser(seller.value.id);
      if (response.code === 200) {
        isFollowed.value = true;
        // 刷新粉丝数量
        await fetchFollowersCount();
      } else {
        console.error('关注失败:', response.message);
      }
    }
  } catch (error) {
    console.error('关注操作失败:', error);
  }
};

// 检查关注状态
const checkFollowStatus = async () => {
  if (!userInfo.value || !seller.value.id) return;

  try {
    const response = await checkFollowing(seller.value.id);
    if (response.code === 200) {
      isFollowed.value = response.data;
    }
  } catch (error) {
    console.error('检查关注状态失败:', error);
  }
};

// 获取粉丝数量
const fetchFollowersCount = async () => {
  if (!seller.value.id) return;

  try {
    const response = await getFollowersCount(seller.value.id);
    if (response.code === 200) {
      followersCount.value = response.data;
    }
  } catch (error) {
    console.error('获取粉丝数量失败:', error);
  }
};

// 切换筛选条件
const changeFilter = (filter) => {
  if (currentFilter.value === filter) return;

  console.log('切换筛选条件:', filter);
  currentFilter.value = filter;
  currentPage.value = 1; // 切换筛选条件时重置为第一页
  fetchSellerProducts();
};

// 获取卖家信息
const fetchSellerInfo = async () => {
  const sellerId = route.params.id;
  if (!sellerId) return;

  try {
    loading.value = true;
    const response = await getSellerInfo(sellerId);
    if (response.code === 200 && response.data) {
      // 将API返回的数据映射到seller对象
  seller.value = {
        id: response.data.userId,
        username: response.data.username,
        avatarUrl: response.data.avatarUrl,
        bio: response.data.bio || '这个卖家很懒，还没有填写个人简介...',
        location: response.data.location || '未知地区',
        registrationDate: response.data.registrationDate,
        creditScore: response.data.creditScore || 100,
        // 这些字段后端暂时没有，保留默认值或从其他API获取
        productCount: 0,
        soldCount: 0,
        favoriteCount: 0
  };

      // 检查关注状态
      await checkFollowStatus();

      // 获取粉丝数量
      await fetchFollowersCount();
    } else {
      console.error('获取卖家信息失败:', response.message);
    }
  } catch (error) {
    console.error('获取卖家信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取卖家商品
const fetchSellerProducts = async () => {
  const sellerId = route.params.id;
  if (!sellerId) return;

  loading.value = true;

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value
    };

    // 根据筛选条件设置排序参数
    switch (currentFilter.value) {
      case 'latest':
        params.sortBy = 'date';
        params.sortDirection = 'desc';
        break;
      case 'price-asc':
        params.sortBy = 'price';
        params.sortDirection = 'asc';
        break;
      case 'price-desc':
        params.sortBy = 'price';
        params.sortDirection = 'desc';
        break;
      default:
        // 默认按最新上架排序
        params.sortBy = 'date';
        params.sortDirection = 'desc';
        break;
    }

    console.log('请求参数:', params);

    const response = await getSellerProducts(sellerId, params);

    console.log('响应数据:', response);

    if (response.code === 200) {
      products.value = response.data.records || [];
      totalProducts.value = response.data.total || 0;
    } else {
      console.error('获取卖家商品失败:', response.message);
      products.value = [];
      totalProducts.value = 0;
    }
  } catch (error) {
    console.error('获取卖家商品失败:', error);
    products.value = [];
    totalProducts.value = 0;
  } finally {
    loading.value = false;
  }
};

// 切换页码
const changePage = (page) => {
  currentPage.value = page;
  fetchSellerProducts();
};

// 跳转到商品详情
const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`);
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';

  try {
    let dateObj;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return '';
    }

    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    return dateObj.toLocaleDateString();
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
};

// 格式化价格
const formatPrice = (price) => {
  return price.toLocaleString();
};

// 信用分相关方法
const getCreditLevelIcon = (score) => {
  if (score >= 95) return '💎';  // 钻石
  if (score >= 85) return '🥇';  // 金牌
  if (score >= 70) return '🥈';  // 银牌
  if (score >= 50) return '🥉';  // 铜牌
  return '⭐';                   // 普通星星
};

const getCreditLevelDescription = (score) => {
  if (score >= 95) return '信誉极佳，值得信赖的顶级卖家';
  if (score >= 85) return '信誉优秀，服务质量很好';
  if (score >= 70) return '信誉良好，值得信赖';
  if (score >= 50) return '信誉一般，请谨慎选择';
  return '信誉较低，建议谨慎交易';
};

const getRatingPercentage = (count) => {
  const total = creditInfo.value.totalReviews || 1;
  return (count / total) * 100;
};

// 评价筛选计算属性
const filteredReviews = computed(() => {
  if (reviewFilter.value === 'all') {
    return reviews.value;
  } else if (reviewFilter.value === 'positive') {
    return reviews.value.filter(review => review.rating >= 4);
  } else if (reviewFilter.value === 'neutral') {
    return reviews.value.filter(review => review.rating === 3);
  } else if (reviewFilter.value === 'negative') {
    return reviews.value.filter(review => review.rating <= 2);
  }
  return reviews.value;
});

// 获取卖家信用信息
const fetchSellerCreditInfo = async () => {
  try {
    const response = await getSellerCreditInfo(route.params.id);
    if (response && response.code === 200) {
      creditInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取卖家信用信息失败:', error);
    // 临时使用模拟数据，展示信用功能效果
    loadMockCreditInfo();
  }
};

// 临时模拟信用信息函数（后端API实现后可删除）
const loadMockCreditInfo = () => {
  const mockCreditInfo = {
    creditScore: 92,
    creditLevel: '金牌卖家',
    creditLevelColor: '#faad14',
    totalReviews: 156,
    averageRating: 4.6,
    positiveRate: 94.2,
    positiveReviews: 147,
    negativeReviews: 9,
    ratingDistribution: {
      1: 2,
      2: 7,
      3: 15,
      4: 45,
      5: 87
    }
  };

  creditInfo.value = mockCreditInfo;
  console.log('已加载模拟信用信息:', mockCreditInfo);
};

// 获取卖家评价列表
const fetchSellerReviews = async () => {
  try {
    const response = await getSellerReviews(route.params.id, reviewPage.value, reviewPageSize.value);
    if (response && response.code === 200) {
      reviews.value = response.data || [];
      reviewTotal.value = response.data ? response.data.length : 0;
    }
  } catch (error) {
    console.error('获取卖家评价失败:', error);
    // 临时使用模拟数据，展示评价功能效果
    loadMockReviews();
  }
};

// 临时模拟数据函数（后端API实现后可删除）
const loadMockReviews = () => {
  const mockReviews = [
    {
      reviewId: 1,
      buyerName: '张三',
      rating: 5,
      reviewContent: '商品质量很好，卖家服务态度也很棒，发货速度很快，包装也很仔细，非常满意！',
      reviewTags: ['发货快', '质量好', '服务态度好'],
      productTitle: 'iPhone 14 Pro Max 256GB',
      createdTime: '2024-01-15T10:30:00'
    },
    {
      reviewId: 2,
      buyerName: '李四',
      rating: 4,
      reviewContent: '整体还不错，商品和描述基本一致，就是物流稍微慢了一点。',
      reviewTags: ['商品描述准确', '性价比高'],
      productTitle: 'MacBook Air M2',
      createdTime: '2024-01-12T14:20:00'
    },
    {
      reviewId: 3,
      buyerName: '王五',
      rating: 5,
      reviewContent: '非常棒的购物体验！商品完全符合预期，卖家人很好，推荐！',
      reviewTags: ['推荐购买', '会再次购买', '质量好'],
      productTitle: 'iPad Pro 11英寸',
      createdTime: '2024-01-10T09:15:00'
    },
    {
      reviewId: 4,
      buyerName: '匿名用户',
      rating: 3,
      reviewContent: '商品一般般，价格还可以接受。',
      reviewTags: ['性价比高'],
      productTitle: 'AirPods Pro 2',
      createdTime: '2024-01-08T16:45:00'
    },
    {
      reviewId: 5,
      buyerName: '赵六',
      rating: 4,
      reviewContent: '卖家很负责任，商品有小瑕疵但及时处理了，整体满意。',
      reviewTags: ['服务态度好', '包装好'],
      productTitle: 'Apple Watch Series 9',
      createdTime: '2024-01-05T11:30:00'
    }
  ];

  reviews.value = mockReviews;
  reviewTotal.value = mockReviews.length;
  console.log('已加载模拟评价数据:', mockReviews.length, '条');
};

// 切换评价页码
const changeReviewPage = (page) => {
  reviewPage.value = page;
  fetchSellerReviews();
};

// 跳转到聊天页面
const chatWithSeller = async () => {
  if (!userInfo.value) {
    router.push('/login?redirect=' + encodeURIComponent(route.fullPath));
    return;
  }

  if (!seller.value.id) {
    alert('卖家信息不完整，无法私聊');
    return;
  }

  try {
    // 获取或创建与卖家的会话
    const response = await getOrCreateConversation(seller.value.id);

    if (response && response.code === 200) {
      const conversationId = response.data;
      // 跳转到聊天界面，并传递会话ID
      router.push({
        path: '/chat',
        query: { conversationId }
      });
    } else {
      alert('创建会话失败，请稍后再试');
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    alert('创建会话失败，请稍后再试');
  }
};

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      fetchSellerInfo();
      fetchSellerProducts();
      fetchSellerCreditInfo();
      fetchSellerReviews();
    }
  }
);

// 监听用户登录状态变化
watch(
  () => userInfo.value,
  () => {
    checkFollowStatus();
  }
);

onMounted(() => {
  console.log('SellerProfile mounted, sellerId:', route.params.id);
  fetchSellerInfo();
  fetchSellerProducts();

  // 只有在有卖家ID时才获取信用信息和评价
  if (route.params.id) {
    fetchSellerCreditInfo();
    fetchSellerReviews();
  }
});
</script>

<style scoped>
.seller-profile-page {
  font-family: 'Noto Sans SC', sans-serif;
  background-color: #F5F5F5;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 卡片容器样式 */
.card-container {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  margin: 25px 0;
  padding: 30px;
}

/* 顶部导航 */
.top-nav {
  background: #FFFFFF;
  padding: 16px 0;
  border-bottom: 1px solid #EFEFEF;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: #FF0000;
  text-decoration: none;
}

.search-area {
  flex-grow: 1;
  margin: 0 32px;
  max-width: 600px;
}

.search-bar {
  display: flex;
  border: 2px solid #FF0000;
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: none;
  padding: 10px 20px;
  width: 100%;
  font-size: 16px;
}
.search-input:focus {
  outline: none;
}

.search-button {
  background: #FF0000;
  border: none;
  color: white;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}

.nav-right {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF0000;
}

/* 卖家头部信息 */
.seller-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.seller-info {
  display: flex;
  gap: 30px;
}

.seller-avatar-container {
  flex-shrink: 0;
}

.seller-avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #FF0000;
}

.seller-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.seller-name {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.seller-badge {
  background-color: #FF0000;
  color: white;
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 20px;
  font-weight: normal;
}

.seller-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item.credit-score-item {
  position: relative;
}

.credit-score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #FF0000;
}

.stat-value.credit-score {
  font-size: 28px;
  color: #1890ff;
}

.credit-level {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.seller-bio {
  max-width: 600px;
  line-height: 1.6;
}

.seller-location, .seller-join-date {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.seller-actions {
  display: flex;
  gap: 15px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.message-button {
  background-color: #FF0000;
  color: white;
}

.message-button:hover {
  background-color: #E60000;
}

.follow-button {
  background-color: #FFFFFF;
  color: #333;
  border: 1px solid #DEDEDE;
}

.follow-button:hover {
  border-color: #FF0000;
  color: #FF0000;
}

.follow-button.followed {
  background-color: #FFF0F0;
  color: #FF0000;
  border-color: #FF0000;
}

/* 商品区域 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.section-filters {
  display: flex;
  gap: 20px;
}

.filter-item {
  color: #666;
  cursor: pointer;
  padding: 5px 0;
  position: relative;
}

.filter-item:hover {
  color: #FF0000;
}

.filter-item.active {
  color: #FF0000;
  font-weight: 500;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #FF0000;
}

/* 商品列表 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
}

.product-card {
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #EFEFEF;
  cursor: pointer;
}

.product-card:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 10px;
}

.product-title {
  font-size: 14px;
  margin: 0 0 8px;
  line-height: 1.3;
  height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #FF0000;
  margin-bottom: 6px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  gap: 20px;
}

.page-button {
  background-color: #FFFFFF;
  border: 1px solid #DEDEDE;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-button:hover:not(:disabled) {
  border-color: #FF0000;
  color: #FF0000;
}

.page-button:disabled {
  color: #CCC;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255,0,0,0.1);
  border-radius: 50%;
  border-top-color: #FF0000;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-products svg {
  margin-bottom: 20px;
  color: #DDD;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .seller-header {
    flex-direction: column;
    gap: 20px;
  }

  .seller-actions {
    width: 100%;
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .seller-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .seller-stats {
    justify-content: center;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }
}

/* 信用分详情样式 */
.seller-credit {
  margin-top: 30px;
}

.credit-details {
  padding: 20px 0;
}

.credit-overview {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
  margin-bottom: 30px;
}

.credit-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.credit-score-large {
  margin-bottom: 15px;
}

.score-number {
  font-size: 4rem;
  font-weight: bold;
  color: #1890ff;
}

.score-max {
  font-size: 2rem;
  color: #999;
  margin-left: 5px;
}

.credit-level-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.level-badge {
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.level-description {
  color: #666;
  font-size: 14px;
  margin: 0;
  text-align: center;
}

.credit-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-name {
  color: #666;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.rating-distribution {
  margin-top: 30px;
}

.rating-distribution h3 {
  margin-bottom: 20px;
  color: #333;
}

.rating-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 15px;
}

.rating-label {
  width: 40px;
  font-size: 14px;
  color: #666;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff9900, #ffcc00);
  transition: width 0.3s ease;
}

.rating-count {
  width: 30px;
  text-align: right;
  font-size: 14px;
  color: #666;
}

/* 评价列表样式 */
.seller-reviews {
  margin-top: 30px;
}

.reviews-section {
  padding: 20px 0;
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.reviewer-name {
  font-weight: 600;
  color: #333;
}

.review-date {
  color: #999;
  font-size: 14px;
}

.review-content {
  margin-bottom: 15px;
}

.review-content p {
  margin: 0 0 10px 0;
  line-height: 1.6;
  color: #333;
}

.no-content {
  color: #999;
  font-style: italic;
}

.review-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.review-product {
  padding-top: 15px;
  border-top: 1px solid #e8e8e8;
}

.product-name {
  color: #666;
  font-size: 14px;
}

.empty-reviews {
  text-align: center;
  padding: 40px;
  color: #999;
}

.review-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
}

.page-button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-button:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .credit-overview {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .score-number {
    font-size: 3rem;
  }

  .reviews-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .reviewer-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
