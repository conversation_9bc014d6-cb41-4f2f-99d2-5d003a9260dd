package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


import javax.validation.constraints.Size;

/**
 * 知识库请求DTO
 */
@Data
public class KnowledgeRequest {

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;

    /**
     * 分类
     */
    @NotBlank(message = "分类不能为空")
    private String category;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;

    /**
     * 关键词
     */
    private String keywords;
}
