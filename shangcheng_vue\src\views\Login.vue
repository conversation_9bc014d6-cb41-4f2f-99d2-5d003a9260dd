<template>
  <div class="login-page">
    <div class="login-container">
      <h2 class="main-title">易转</h2>
      <h3 class="sub-title">用户登录</h3>
      <div class="form-group">
        <label for="username">用户名 / 手机号</label>
        <input
          type="text"
          id="username"
          v-model="loginForm.username"
          placeholder="请输入用户名/手机号"
        />
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input
          type="password"
          id="password"
          v-model="loginForm.password"
          placeholder="请输入密码"
        />
      </div>
      <div class="form-actions">
        <button @click="handleLogin" :disabled="loading">
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </div>
      <div class="register-prompt">
        还没有账户？ <a @click="goToRegister" class="link">立即注册</a>
      </div>
      <div class="sms-login-prompt">
        <a @click="showSmsLogin = true" class="sms-link">验证码登录</a>
      </div>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </div>

    <!-- 验证码登录弹窗 -->
    <div class="modal-overlay" v-if="showSmsLogin" @click.self="closeSmsLoginModal">
      <div class="modal-content">
      <div class="modal-header">
        <h3>验证码登录</h3>
        <button class="close-btn" @click="closeSmsLoginModal">&times;</button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="handleSmsLogin" class="sms-login-form">
          <div class="form-group">
            <label for="sms-phone">手机号</label>
            <input
              type="tel"
              id="sms-phone"
              v-model="smsLoginForm.phone"
              placeholder="请输入11位手机号"
              maxlength="11"
              autocomplete="off"
              :disabled="smsLoginForm.codeSent"
            >
          </div>

          <div class="form-group">
            <label for="sms-code">验证码</label>
            <div class="code-input-group">
              <input
                type="text"
                id="sms-code"
                v-model="smsLoginForm.code"
                placeholder="请输入6位验证码"
                maxlength="6"
                autocomplete="off"
                :readonly="false"
                :disabled="false"
                @focus="console.log('验证码输入框获得焦点')"
                @input="console.log('验证码输入:', $event.target.value)"
              >
              <button
                type="button"
                class="send-code-btn"
                @click="sendVerificationCode"
                :disabled="!canSendCode || sendCodeLoading"
              >
                {{ sendCodeText }}
              </button>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="closeSmsLoginModal">取消</button>
            <button
              type="submit"
              class="submit-btn"
              :disabled="!smsLoginForm.phone || !smsLoginForm.code || smsLoginLoading"
            >
              {{ smsLoginLoading ? '登录中...' : '登录' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { login, smsLogin } from '@/api/user';
import { sendSmsCode } from '@/api/sms';
import { useRouter } from 'vue-router';
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const errorMessage = ref('');
const loginForm = reactive({
  username: '',
  password: ''
});

// 短信登录相关
const showSmsLogin = ref(false);
const smsLoginForm = reactive({
  phone: '',
  code: '',
  codeSent: false
});
const sendCodeLoading = ref(false);
const smsLoginLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref(null);

// 在组件加载时清除所有token
onMounted(() => {
  // 清除所有token，确保登录状态干净
  localStorage.removeItem('token');
  localStorage.removeItem('adminToken');
  userStore.logout(); // 确保用户状态也被清除
});

const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    errorMessage.value = '用户名和密码不能为空';
    return;
  }

  try {
    loading.value = true;
    errorMessage.value = '';

    const res = await login(loginForm);
    console.log('登录响应:', res);

    // 检查响应格式，处理ApiResponse包装
    if (res.code === 200 && res.data && res.data.token) {
      // 检查用户状态是否被禁用
      if (res.data.isActive === false) {
        errorMessage.value = '您的账号已被禁用，请联系管理员';
        return;
      }

      // 使用userStore保存用户信息（内部会自动处理token）
      userStore.setUser(res.data);
      console.log('已设置用户信息:', userStore.userInfo);

      // 确保用户信息完整，主动调用一次API获取完整信息
      try {
        console.log('登录成功后主动获取完整用户信息');
        await userStore.fetchUserInfo();
        console.log('获取完整用户信息成功:', userStore.userInfo);
      } catch (fetchError) {
        console.error('获取完整用户信息失败:', fetchError);
        // 即使获取完整信息失败，也继续使用登录返回的基本信息
      }

      // 跳转到首页或其他页面
      router.push('/home');
    } else if (res.code === 400 && res.message && res.message.includes('禁用')) {
      // 特别处理用户被禁用的情况
      errorMessage.value = '您的账号已被禁用，请联系管理员';
    } else {
      errorMessage.value = res.message || '登录失败，请检查用户名和密码';
    }
  } catch (error) {
    console.error('登录错误:', error);
    // 检查是否是用户被禁用的错误
    if (error.response && error.response.data) {
      const errorData = error.response.data;
      if (errorData.message && errorData.message.includes('禁用')) {
        errorMessage.value = '您的账号已被禁用，请联系管理员';
      } else {
        errorMessage.value = errorData.message || '登录失败，请稍后再试';
      }
    } else {
      errorMessage.value = error.message || '登录失败，请稍后再试';
    }
  } finally {
    loading.value = false;
  }
};

const goToRegister = () => {
  router.push('/register');
};

// 短信登录相关方法
const canSendCode = computed(() => {
  return countdown.value === 0 &&
         smsLoginForm.phone &&
         /^1[3-9]\d{9}$/.test(smsLoginForm.phone);
});

const sendCodeText = computed(() => {
  if (sendCodeLoading.value) return '发送中...';
  if (countdown.value > 0) return `${countdown.value}s后重发`;
  return '获取验证码';
});

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return;

  sendCodeLoading.value = true;
  try {
    const response = await sendSmsCode(smsLoginForm.phone, 'login');
    if (response.success) {
      ElMessage.success('验证码发送成功');
      smsLoginForm.codeSent = true;
      startCountdown();
    } else {
      ElMessage.error(response.message || '验证码发送失败');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('验证码发送失败，请稍后重试');
  } finally {
    sendCodeLoading.value = false;
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }
  }, 1000);
};

// 处理短信登录
const handleSmsLogin = async () => {
  if (!smsLoginForm.phone || !smsLoginForm.code) {
    ElMessage.error('请填写完整信息');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(smsLoginForm.phone)) {
    ElMessage.error('手机号格式不正确');
    return;
  }

  if (!/^\d{6}$/.test(smsLoginForm.code)) {
    ElMessage.error('验证码格式不正确');
    return;
  }

  smsLoginLoading.value = true;
  errorMessage.value = '';

  try {
    const response = await smsLogin({
      phone: smsLoginForm.phone,
      code: smsLoginForm.code
    });

    if (response.success) {
      // 保存用户信息到store
      userStore.setUser(response.data);
      console.log('短信登录已设置用户信息:', userStore.userInfo);

      // 确保用户信息完整，主动调用一次API获取完整信息
      try {
        console.log('短信登录成功后主动获取完整用户信息');
        await userStore.fetchUserInfo();
        console.log('获取完整用户信息成功:', userStore.userInfo);
      } catch (fetchError) {
        console.error('获取完整用户信息失败:', fetchError);
        // 即使获取完整信息失败，也继续使用登录返回的基本信息
      }

      ElMessage.success('登录成功');

      // 跳转到首页
      router.push('/home');
    } else {
      errorMessage.value = response.message || '登录失败';
      ElMessage.error(errorMessage.value);
    }
  } catch (error) {
    console.error('短信登录失败:', error);
    errorMessage.value = '登录失败，请稍后重试';
    ElMessage.error(errorMessage.value);
  } finally {
    smsLoginLoading.value = false;
  }
};

// 关闭短信登录弹窗
const closeSmsLoginModal = () => {
  showSmsLogin.value = false;
  // 重置表单
  smsLoginForm.phone = '';
  smsLoginForm.code = '';
  smsLoginForm.codeSent = false;
  // 清除倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  countdown.value = 0;
  sendCodeLoading.value = false;
  smsLoginLoading.value = false;
  errorMessage.value = '';
};
</script>

<style scoped>
.login-container {
  width: 500px; /* 设置固定宽度 */
  margin: 100px auto;
  padding: 40px 50px;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.main-title {
  text-align: center;
  margin-bottom: 8px;
  color: #e04040;
  font-size: 2.5em;
  font-weight: bold;
}

.sub-title {
  text-align: center;
  margin-bottom: 30px;
  color: #666;
  font-size: 1.4em;
}

.form-group {
  margin-bottom: 25px; /* 增大表单组之间的间距 */
}

label {
  display: block;
  margin-bottom: 10px; /* 增大标签和输入框之间的间距 */
  font-weight: bold;
  color: #555;
  font-size: 1.2em; /* 增大标签字体 */
}

input {
  width: 100%;
  padding: 15px 20px; /* 增大输入框内边距 */
  border: 1px solid #ddd;
  border-radius: 6px; /* 增大圆角 */
  font-size: 1.2em; /* 增大输入框字体 */
  background-color: #f8f9fa; /* 添加浅色背景 */
}

.form-actions {
  margin-top: 30px; /* 增大按钮上方间距 */
  text-align: center;
}

button {
  padding: 15px 20px; /* 增大按钮内边距 */
  border: none;
  border-radius: 6px; /* 增大圆角 */
  cursor: pointer;
  font-size: 1.3em; /* 增大按钮字体 */
  background-color: #e04040;
  color: white;
  width: 100%;
  font-weight: bold; /* 加粗按钮文字 */
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.register-prompt {
  text-align: center;
  margin-top: 20px;
  font-size: 1.1em; /* 增大字体 */
  color: #555;
}

.register-prompt .link {
  color: #2196F3;
  text-decoration: none;
  cursor: pointer;
}

.error-message {
  color: red;
  text-align: center;
  margin-top: 20px;
  font-size: 1.1em;
}

/* 短信登录提示样式 */
.sms-login-prompt {
  text-align: center;
  margin-top: 10px;
  font-size: 0.9em;
}

.sms-login-prompt .sms-link {
  color: #409EFF;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.9em;
}

.sms-login-prompt .sms-link:hover {
  text-decoration: underline;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #666;
}

.modal-body {
  padding: 0 20px 20px;
}

/* 短信登录表单样式 */
.sms-login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.code-input-group {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.code-input-group input {
  flex: 1;
  min-width: 150px;
  pointer-events: auto;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.send-code-btn {
  padding: 12px 16px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  transition: background-color 0.3s;
  min-width: 110px;
  max-width: 120px;
  flex-shrink: 0;
}

.send-code-btn:hover:not(:disabled) {
  background-color: #337ecc;
}

.send-code-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.submit-btn {
  background-color: #409EFF;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background-color: #337ecc;
}

.submit-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
