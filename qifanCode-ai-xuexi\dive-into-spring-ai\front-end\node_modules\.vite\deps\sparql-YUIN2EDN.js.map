{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/sparql.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([\"str\", \"lang\", \"langmatches\", \"datatype\", \"bound\", \"sameterm\", \"isiri\", \"isuri\",\n                      \"iri\", \"uri\", \"bnode\", \"count\", \"sum\", \"min\", \"max\", \"avg\", \"sample\",\n                      \"group_concat\", \"rand\", \"abs\", \"ceil\", \"floor\", \"round\", \"concat\", \"substr\", \"strlen\",\n                      \"replace\", \"ucase\", \"lcase\", \"encode_for_uri\", \"contains\", \"strstarts\", \"strends\",\n                      \"strbefore\", \"strafter\", \"year\", \"month\", \"day\", \"hours\", \"minutes\", \"seconds\",\n                      \"timezone\", \"tz\", \"now\", \"uuid\", \"struuid\", \"md5\", \"sha1\", \"sha256\", \"sha384\",\n                      \"sha512\", \"coalesce\", \"if\", \"strlang\", \"strdt\", \"isnumeric\", \"regex\", \"exists\",\n                      \"isblank\", \"isliteral\", \"a\", \"bind\"]);\nvar keywords = wordRegexp([\"base\", \"prefix\", \"select\", \"distinct\", \"reduced\", \"construct\", \"describe\",\n                           \"ask\", \"from\", \"named\", \"where\", \"order\", \"limit\", \"offset\", \"filter\", \"optional\",\n                           \"graph\", \"by\", \"asc\", \"desc\", \"as\", \"having\", \"undef\", \"values\", \"group\",\n                           \"minus\", \"in\", \"not\", \"service\", \"silent\", \"using\", \"insert\", \"delete\", \"union\",\n                           \"true\", \"false\", \"with\",\n                           \"data\", \"copy\", \"to\", \"move\", \"add\", \"create\", \"drop\", \"clear\", \"load\", \"into\"]);\nvar operatorChars = /[*+\\-<>=&|\\^\\/!\\?]/;\nvar PN_CHARS = \"[A-Za-z_\\\\-0-9]\";\nvar PREFIX_START = new RegExp(\"[A-Za-z]\");\nvar PREFIX_REMAINDER = new RegExp(\"((\" + PN_CHARS + \"|\\\\.)*(\" + PN_CHARS + \"))?:\");\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"$\" || ch == \"?\") {\n    if(ch == \"?\" && stream.match(/\\s/, false)){\n      return \"operator\";\n    }\n    stream.match(/^[A-Za-z0-9_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][A-Za-z0-9_\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]*/);\n    return \"variableName.local\";\n  }\n  else if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"bracket\";\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    return \"operator\";\n  }\n  else if (ch == \":\") {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  else if (ch == \"@\") {\n    stream.eatWhile(/[a-z\\d\\-]/i);\n    return \"meta\";\n  }\n  else if (PREFIX_START.test(ch) && stream.match(PREFIX_REMAINDER)) {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  stream.eatWhile(/[_\\w\\d]/);\n  var word = stream.current();\n  if (ops.test(word))\n    return \"builtin\";\n  else if (keywords.test(word))\n    return \"keyword\";\n  else\n    return \"variable\";\n}\n\nfunction eatPnLocal(stream) {\n  stream.match(/(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/i);\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const sparql = {\n  name: \"sparql\",\n\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) {\n        popContext(state);\n        if (curPunc == \"}\" && state.context && state.context.type == \"pattern\")\n          popContext(state);\n      }\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI;AAEJ,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;AACxD;AACA,IAAI,MAAM,WAAW;AAAA,EAAC;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAY;AAAA,EAAS;AAAA,EAAY;AAAA,EAAS;AAAA,EACxE;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAC5D;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAa;AAAA,EACxE;AAAA,EAAa;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EACrE;AAAA,EAAY;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAU;AAAA,EACrE;AAAA,EAAU;AAAA,EAAY;AAAA,EAAM;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAS;AAAA,EACtE;AAAA,EAAW;AAAA,EAAa;AAAA,EAAK;AAAM,CAAC;AAC1D,IAAI,WAAW,WAAW;AAAA,EAAC;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAChE;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EACvE;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EACjE;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EACxE;AAAA,EAAQ;AAAA,EAAS;AAAA,EACjB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAM,CAAC;AAC1G,IAAI,gBAAgB;AACpB,IAAI,WAAW;AACf,IAAI,eAAe,IAAI,OAAO,UAAU;AACxC,IAAI,mBAAmB,IAAI,OAAO,OAAO,WAAW,YAAY,WAAW,MAAM;AAEjF,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,YAAU;AACV,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,QAAG,MAAM,OAAO,OAAO,MAAM,MAAM,KAAK,GAAE;AACxC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,gUAAgU;AAC7U,WAAO;AAAA,EACT,WACS,MAAM,OAAO,CAAC,OAAO,MAAM,gBAAgB,KAAK,GAAG;AAC1D,WAAO,MAAM,kBAAkB;AAC/B,WAAO;AAAA,EACT,WACS,MAAM,OAAQ,MAAM,KAAK;AAChC,UAAM,WAAW,aAAa,EAAE;AAChC,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC,WACS,mBAAmB,KAAK,EAAE,GAAG;AACpC,cAAU;AACV,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WACS,cAAc,KAAK,EAAE,GAAG;AAC/B,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,eAAW,MAAM;AACjB,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,WAAO,SAAS,YAAY;AAC5B,WAAO;AAAA,EACT,WACS,aAAa,KAAK,EAAE,KAAK,OAAO,MAAM,gBAAgB,GAAG;AAChE,eAAW,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAS;AACzB,MAAI,OAAO,OAAO,QAAQ;AAC1B,MAAI,IAAI,KAAK,IAAI;AACf,WAAO;AAAA,WACA,SAAS,KAAK,IAAI;AACzB,WAAO;AAAA;AAEP,WAAO;AACX;AAEA,SAAS,WAAW,QAAQ;AAC1B,SAAO,MAAM,yEAAyE;AACxF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,cAAM,WAAW;AACjB;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO,MAAM,KAAK;AACrC,QAAM,UAAU,EAAC,MAAM,MAAM,SAAS,QAAQ,MAAM,QAAQ,KAAU,KAAU;AAClF;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,SAAS,MAAM,QAAQ;AAC7B,QAAM,UAAU,MAAM,QAAQ;AAChC;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MAAC,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,IAAC;AAAA,EAChB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,MAAM,WAAW,MAAM,QAAQ,SAAS;AAAM,cAAM,QAAQ,QAAQ;AACxE,YAAM,SAAS,OAAO,YAAY;AAAA,IACpC;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AAExC,QAAI,SAAS,aAAa,MAAM,WAAW,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,QAAQ,WAAW;AACzG,YAAM,QAAQ,QAAQ;AAAA,IACxB;AAEA,QAAI,WAAW;AAAK,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aAClD,WAAW;AAAK,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aACvD,WAAW;AAAK,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aACvD,WAAW,KAAK,OAAO,GAAG;AACjC,aAAO,MAAM,WAAW,MAAM,QAAQ,QAAQ;AAAW,mBAAW,KAAK;AACzE,UAAI,MAAM,WAAW,WAAW,MAAM,QAAQ,MAAM;AAClD,mBAAW,KAAK;AAChB,YAAI,WAAW,OAAO,MAAM,WAAW,MAAM,QAAQ,QAAQ;AAC3D,qBAAW,KAAK;AAAA,MACpB;AAAA,IACF,WACS,WAAW,OAAO,MAAM,WAAW,MAAM,QAAQ,QAAQ;AAAW,iBAAW,KAAK;AAAA,aACpF,uBAAuB,KAAK,KAAK,KAAK,MAAM,SAAS;AAC5D,UAAI,SAAS,KAAK,MAAM,QAAQ,IAAI;AAClC,oBAAY,OAAO,WAAW,OAAO,OAAO,CAAC;AAAA,eACtC,MAAM,QAAQ,QAAQ,aAAa,CAAC,MAAM,QAAQ,OAAO;AAChE,cAAM,QAAQ,QAAQ;AACtB,cAAM,QAAQ,MAAM,OAAO,OAAO;AAAA,MACpC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC;AAC/C,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,KAAK,SAAS;AACzB,aAAO,WAAW,QAAQ,QAAQ;AAAW,kBAAU,QAAQ;AAEjE,QAAI,UAAU,WAAW,aAAa,QAAQ;AAC9C,QAAI,CAAC;AACH,aAAO;AAAA,aACA,QAAQ,QAAQ;AACvB,aAAO,QAAQ;AAAA,aACR,QAAQ;AACf,aAAO,QAAQ,OAAO,UAAU,IAAI;AAAA;AAEpC,aAAO,QAAQ,UAAU,UAAU,IAAI,GAAG;AAAA,EAC9C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}