package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.model.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 根据查询条件分页查询商品
     * 
     * @param page 分页参数
     * @param params 查询条件
     * @return 分页结果
     */
    IPage<Product> selectProductsByCondition(Page<Product> page, @Param("params") ProductQueryParams params);
    
    /**
     * 根据ID查询商品详情（包含分类名称和卖家信息）
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    Product selectProductDetail(@Param("productId") Long productId);

    // ========== 审核相关查询方法 ==========

    /**
     * 统计用户今日发布商品数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId} " +
            "AND DATE(lzhshtp_posted_date) = CURDATE()")
    int countTodayPostsByUser(@Param("userId") Long userId);

    /**
     * 统计用户本周发布商品数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId} " +
            "AND lzhshtp_posted_date >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)")
    int countWeekPostsByUser(@Param("userId") Long userId);

    /**
     * 统计用户本月发布商品数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId} " +
            "AND YEAR(lzhshtp_posted_date) = YEAR(CURDATE()) " +
            "AND MONTH(lzhshtp_posted_date) = MONTH(CURDATE())")
    int countMonthPostsByUser(@Param("userId") Long userId);

    /**
     * 统计用户重复标题商品数量
     */
    @Select("SELECT COUNT(*) - COUNT(DISTINCT lzhshtp_title) FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId}")
    int countDuplicateTitlesByUser(@Param("userId") Long userId);

    /**
     * 统计用户相似描述商品数量（简化版：检查描述长度相同的）
     */
    @Select("SELECT COUNT(*) - COUNT(DISTINCT LENGTH(lzhshtp_description)) FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId} " +
            "AND LENGTH(lzhshtp_description) > 10")
    int countSimilarDescriptionsByUser(@Param("userId") Long userId);

    /**
     * 获取用户最近发布的商品时间
     */
    @Select("SELECT lzhshtp_posted_date FROM tb_lzhshtp_products " +
            "WHERE lzhshtp_seller_id = #{userId} " +
            "ORDER BY lzhshtp_posted_date DESC " +
            "LIMIT #{limit}")
    List<LocalDateTime> getRecentPostTimesByUser(@Param("userId") Long userId, @Param("limit") int limit);
}