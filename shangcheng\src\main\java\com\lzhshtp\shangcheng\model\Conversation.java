package com.lzhshtp.shangcheng.model;

import java.time.LocalDateTime;

public class Conversation {
    private Long lzhshtp_conversation_id;  // 会话唯一标识ID
    private Long lzhshtp_user1_id;         // 会话参与者1的用户ID
    private Long lzhshtp_user2_id;         // 会话参与者2的用户ID
    private LocalDateTime lzhshtp_created_at;  // 会话创建时间
    private LocalDateTime lzhshtp_updated_at;  // 会话最后更新时间
    private String lzhshtp_last_message_preview;  // 最后一条消息的预览
    private Integer lzhshtp_unread_count_user1;   // 用户1的未读消息数量
    private Integer lzhshtp_unread_count_user2;   // 用户2的未读消息数量

    // Getters and Setters
    public Long getLzhshtp_conversation_id() {
        return lzhshtp_conversation_id;
    }

    public void setLzhshtp_conversation_id(Long lzhshtp_conversation_id) {
        this.lzhshtp_conversation_id = lzhshtp_conversation_id;
    }

    public Long getLzhshtp_user1_id() {
        return lzhshtp_user1_id;
    }

    public void setLzhshtp_user1_id(Long lzhshtp_user1_id) {
        this.lzhshtp_user1_id = lzhshtp_user1_id;
    }

    public Long getLzhshtp_user2_id() {
        return lzhshtp_user2_id;
    }

    public void setLzhshtp_user2_id(Long lzhshtp_user2_id) {
        this.lzhshtp_user2_id = lzhshtp_user2_id;
    }

    public LocalDateTime getLzhshtp_created_at() {
        return lzhshtp_created_at;
    }

    public void setLzhshtp_created_at(LocalDateTime lzhshtp_created_at) {
        this.lzhshtp_created_at = lzhshtp_created_at;
    }

    public LocalDateTime getLzhshtp_updated_at() {
        return lzhshtp_updated_at;
    }

    public void setLzhshtp_updated_at(LocalDateTime lzhshtp_updated_at) {
        this.lzhshtp_updated_at = lzhshtp_updated_at;
    }

    public String getLzhshtp_last_message_preview() {
        return lzhshtp_last_message_preview;
    }

    public void setLzhshtp_last_message_preview(String lzhshtp_last_message_preview) {
        this.lzhshtp_last_message_preview = lzhshtp_last_message_preview;
    }

    public Integer getLzhshtp_unread_count_user1() {
        return lzhshtp_unread_count_user1;
    }

    public void setLzhshtp_unread_count_user1(Integer lzhshtp_unread_count_user1) {
        this.lzhshtp_unread_count_user1 = lzhshtp_unread_count_user1;
    }

    public Integer getLzhshtp_unread_count_user2() {
        return lzhshtp_unread_count_user2;
    }

    public void setLzhshtp_unread_count_user2(Integer lzhshtp_unread_count_user2) {
        this.lzhshtp_unread_count_user2 = lzhshtp_unread_count_user2;
    }
} 