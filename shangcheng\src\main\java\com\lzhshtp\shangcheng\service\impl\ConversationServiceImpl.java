package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.ConversationDTO;
import com.lzhshtp.shangcheng.mapper.ConversationMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Conversation;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ConversationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConversationServiceImpl implements ConversationService {

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<ConversationDTO> getUserConversations(Long userId) {
        // 获取用户的所有会话
        List<Conversation> conversations = conversationMapper.findConversationsByUserId(userId);
        List<ConversationDTO> conversationDTOs = new ArrayList<>();

        // 转换为DTO
        for (Conversation conversation : conversations) {
            ConversationDTO dto = new ConversationDTO();
            dto.setConversationId(conversation.getLzhshtp_conversation_id());
            
            // 确定对方用户ID
            Long otherUserId;
            Integer unreadCount;
            
            if (conversation.getLzhshtp_user1_id().equals(userId)) {
                otherUserId = conversation.getLzhshtp_user2_id();
                unreadCount = conversation.getLzhshtp_unread_count_user1();
            } else {
                otherUserId = conversation.getLzhshtp_user1_id();
                unreadCount = conversation.getLzhshtp_unread_count_user2();
            }
            
            // 获取对方用户信息
            User otherUser = userMapper.selectById(otherUserId);
            if (otherUser != null) {
                dto.setOtherUserId(otherUserId);
                dto.setOtherUsername(otherUser.getUsername());
                dto.setOtherUserAvatar(otherUser.getAvatarUrl());
            }
            
            dto.setLastMessagePreview(conversation.getLzhshtp_last_message_preview());
            dto.setUpdatedAt(conversation.getLzhshtp_updated_at());
            dto.setUnreadCount(unreadCount);
            
            conversationDTOs.add(dto);
        }
        
        return conversationDTOs;
    }

    @Override
    public Long getOrCreateConversation(Long currentUserId, Long otherUserId) {
        // 首先检查会话是否已存在
        Conversation existingConversation = conversationMapper.findConversationBetweenUsers(currentUserId, otherUserId);
        
        if (existingConversation != null) {
            return existingConversation.getLzhshtp_conversation_id();
        }
        
        // 如果不存在，创建新会话
        Conversation newConversation = new Conversation();
        
        // 确保用户ID的顺序一致（较小的ID作为user1）
        if (currentUserId < otherUserId) {
            newConversation.setLzhshtp_user1_id(currentUserId);
            newConversation.setLzhshtp_user2_id(otherUserId);
        } else {
            newConversation.setLzhshtp_user1_id(otherUserId);
            newConversation.setLzhshtp_user2_id(currentUserId);
        }
        
        newConversation.setLzhshtp_last_message_preview("");
        newConversation.setLzhshtp_unread_count_user1(0);
        newConversation.setLzhshtp_unread_count_user2(0);
        
        conversationMapper.createConversation(newConversation);
        return newConversation.getLzhshtp_conversation_id();
    }

    @Override
    public boolean updateConversationPreview(Long conversationId, String messagePreview) {
        return conversationMapper.updateConversationPreview(conversationId, messagePreview) > 0;
    }

    @Override
    public boolean incrementUnreadCount(Long conversationId, Long senderId) {
        Conversation conversation = conversationMapper.findConversationById(conversationId);
        if (conversation == null) {
            return false;
        }
        
        // 根据发送者ID决定更新哪个用户的未读计数
        if (conversation.getLzhshtp_user1_id().equals(senderId)) {
            return conversationMapper.incrementUnreadCountUser2(conversationId) > 0;
        } else {
            return conversationMapper.incrementUnreadCountUser1(conversationId) > 0;
        }
    }

    @Override
    public boolean resetUnreadCount(Long conversationId, Long userId) {
        Conversation conversation = conversationMapper.findConversationById(conversationId);
        if (conversation == null) {
            return false;
        }
        
        // 根据用户ID决定重置哪个用户的未读计数
        if (conversation.getLzhshtp_user1_id().equals(userId)) {
            return conversationMapper.resetUnreadCountUser1(conversationId) > 0;
        } else {
            return conversationMapper.resetUnreadCountUser2(conversationId) > 0;
        }
    }

    @Override
    public boolean isConversationParticipant(Long conversationId, Long userId) {
        Conversation conversation = conversationMapper.findConversationById(conversationId);
        if (conversation == null) {
            return false;
        }
        
        return conversation.getLzhshtp_user1_id().equals(userId) || 
               conversation.getLzhshtp_user2_id().equals(userId);
    }
} 