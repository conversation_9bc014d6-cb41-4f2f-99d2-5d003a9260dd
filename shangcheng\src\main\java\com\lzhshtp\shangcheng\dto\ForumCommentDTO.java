package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 论坛评论数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumCommentDTO {
    
    /**
     * 评论ID
     */
    private Long commentId;
    
    /**
     * 所属帖子ID
     */
    private Long postId;
    
    /**
     * 评论作者ID
     */
    private Long authorId;
    
    /**
     * 评论作者名称
     */
    private String authorName;
    
    /**
     * 评论作者头像
     */
    private String authorAvatar;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 评论时间
     */
    private LocalDateTime commentedAt;
    
    /**
     * 父评论ID
     */
    private Long parentCommentId;
    
    /**
     * 父评论作者名称（如果是回复评论）
     */
    private String parentAuthorName;
    
    /**
     * 子评论列表（回复）
     */
    private List<ForumCommentDTO> replies;
} 