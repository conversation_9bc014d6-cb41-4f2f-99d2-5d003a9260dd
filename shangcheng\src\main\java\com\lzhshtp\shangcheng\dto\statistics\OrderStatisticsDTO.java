package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatisticsDTO {
    
    // 订单趋势数据
    private List<String> orderDates;           // 订单日期列表
    private List<Long> orderCounts;            // 对应日期的订单数量
    private List<BigDecimal> orderAmounts;     // 对应日期的订单金额
    private List<Long> cumulativeOrders;       // 累计订单数
    
    // 订单状态分布
    private Map<String, Long> statusDistribution; // 订单状态分布
    // {pending_payment: 50, paid: 200, shipped: 150, delivered: 300, completed: 250, cancelled: 30, refunded: 20}
    
    // 交易金额统计
    private BigDecimal totalAmount;            // 总交易金额
    private BigDecimal avgOrderAmount;         // 平均订单金额
    private BigDecimal maxOrderAmount;         // 最大订单金额
    private BigDecimal minOrderAmount;         // 最小订单金额
    private BigDecimal todayAmount;            // 今日交易金额
    private BigDecimal yesterdayAmount;        // 昨日交易金额
    
    // 订单完成率分析
    private Double completionRate;             // 订单完成率
    private Double cancellationRate;           // 订单取消率
    private Double refundRate;                 // 退款率
    
    // 支付方式分布
    private Map<String, Long> paymentMethodDistribution; // 支付方式分布
    
    // 订单金额区间分布
    private Map<String, Long> amountRangeDistribution; // 金额区间分布
    // {"0-100": 300, "100-500": 400, "500-1000": 200, "1000-5000": 80, "5000+": 20}
    
    // 热门商品（按订单量）
    private List<HotProductDTO> hotProductsByOrders;
    
    // 活跃买家排行
    private List<ActiveBuyerDTO> activeBuyers;
    
    // 活跃卖家排行
    private List<ActiveSellerDTO> activeSellers;
    
    // 订单时间分布（按小时）
    private Map<Integer, Long> hourlyDistribution; // 24小时订单分布
    
    // 订单地域分布
    private List<LocationStatisticsDTO> locationDistribution;
}


