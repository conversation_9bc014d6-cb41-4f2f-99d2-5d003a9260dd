<script setup>
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user';

// 获取用户状态
const userStore = useUserStore();

// 在应用启动时预先获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn) {
    try {
      await userStore.fetchUserInfo();
      console.log('App.vue: 用户信息预加载完成');
    } catch (error) {
      console.error('App.vue: 预加载用户信息失败', error);
    }
  }
});
</script>

<template>
  <div class="app-container">
    <!-- 路由视图 -->
    <router-view />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  width: 100%;
  height: 100%;
}

.app-container {
  min-height: 100vh;
  width: 100%;
  padding: 0;
  margin: 0;
}

button {
  cursor: pointer;
}

a {
  text-decoration: none;
  color: inherit;
}
</style>
