import request from '@/utils/request'

const BASE_URL = '/products'

/**
 * 获取商品列表
 * @param {Object} params - 查询参数
 * @param {String} params.status - 商品状态
 * @param {String} params.keyword - 搜索关键词
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 每页数量
 * @param {String} params.sortBy - 排序字段
 * @param {String} params.sortDirection - 排序方向
 * @returns {Promise} - 返回商品列表
 */
export function getProducts(params) {
  // 创建一个新的参数对象，确保参数名称与后端期望的一致
  const requestParams = { ...params };
  
  // 处理可能的参数名称不一致问题
  // 如果后端期望的是page而不是pageNum，做一下转换
  if (requestParams.pageNum !== undefined) {
    requestParams.page = requestParams.pageNum;
    // 可以选择删除原始参数，避免两个参数同时存在
    // delete requestParams.pageNum;
  }
  
  return request({
    url: BASE_URL,
    method: 'get',
    params: requestParams
  })
}

/**
 * 获取商品详情
 * @param {Number} id - 商品ID
 * @returns {Promise} - 返回商品详情
 */
export function getProductDetail(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'get'
  })
}

/**
 * 审核商品
 * @param {Number} id - 商品ID
 * @param {Boolean} approved - 是否通过审核
 * @returns {Promise} - 返回审核结果
 */
export function reviewProduct(id, approved) {
  return request({
    url: `${BASE_URL}/${id}/review`,
    method: 'put',
    params: { approved }
  })
}

/**
 * 更新商品状态
 * @param {Number} id - 商品ID
 * @param {String} status - 商品状态
 * @returns {Promise} - 返回更新结果
 */
export function updateProductStatus(id, status) {
  return request({
    url: `${BASE_URL}/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 更新商品信息
 * @param {Number} id - 商品ID
 * @param {Object} productData - 商品数据
 * @returns {Promise} - 返回更新结果
 */
export function updateProduct(id, productData) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'put',
    data: productData
  })
}

/**
 * 删除商品
 * @param {Number} id - 商品ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteProduct(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除商品
 * @param {Array} productIds - 商品ID数组
 * @returns {Promise} - 返回批量删除结果
 */
export function batchDeleteProducts(productIds) {
  return request({
    url: `${BASE_URL}/batch`,
    method: 'delete',
    data: { productIds }
  })
}

/**
 * 批量上架商品
 * @param {Array} productIds - 商品ID数组
 * @returns {Promise} - 返回批量上架结果
 */
export function batchApproveProducts(productIds) {
  return request({
    url: `${BASE_URL}/batch/approve`,
    method: 'put',
    data: { productIds }
  })
} 