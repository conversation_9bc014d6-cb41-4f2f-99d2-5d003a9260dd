package io.github.qifan777.knowledge.ai.messge;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.github.qifan777.knowledge.ai.session.AiSession;
import io.github.qifan777.knowledge.ai.session.AiSessionDraft;
import io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntityDraft;
import io.github.qifan777.knowledge.user.User;
import io.github.qifan777.knowledge.user.UserDraft;
import jakarta.validation.constraints.Null;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToOne;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.ai.chat.messages.MessageType;

@GeneratedBy(
        type = AiMessage.class
)
public interface AiMessageDraft extends AiMessage, BaseEntityDraft {
    AiMessageDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    AiMessageDraft setId(String id);

    @OldChain
    AiMessageDraft setCreatedTime(LocalDateTime createdTime);

    @OldChain
    AiMessageDraft setEditedTime(LocalDateTime editedTime);

    UserDraft editor();

    UserDraft editor(boolean autoCreate);

    @OldChain
    AiMessageDraft setEditor(User editor);

    @NotNull
    String editorId();

    @OldChain
    AiMessageDraft setEditorId(@NotNull String editorId);

    @OldChain
    AiMessageDraft applyEditor(DraftConsumer<UserDraft> block);

    @OldChain
    AiMessageDraft applyEditor(User base, DraftConsumer<UserDraft> block);

    UserDraft creator();

    UserDraft creator(boolean autoCreate);

    @OldChain
    AiMessageDraft setCreator(User creator);

    @NotNull
    String creatorId();

    @OldChain
    AiMessageDraft setCreatorId(@NotNull String creatorId);

    @OldChain
    AiMessageDraft applyCreator(DraftConsumer<UserDraft> block);

    @OldChain
    AiMessageDraft applyCreator(User base, DraftConsumer<UserDraft> block);

    @OldChain
    AiMessageDraft setType(MessageType type);

    @OldChain
    AiMessageDraft setTextContent(String textContent);

    @OldChain
    AiMessageDraft setMedias(List<AiMessage.Media> medias);

    @OldChain
    AiMessageDraft setSessionId(String sessionId);

    AiSessionDraft session();

    AiSessionDraft session(boolean autoCreate);

    @OldChain
    AiMessageDraft setSession(AiSession session);

    @OldChain
    AiMessageDraft applySession(DraftConsumer<AiSessionDraft> block);

    @OldChain
    AiMessageDraft applySession(AiSession base, DraftConsumer<AiSessionDraft> block);

    @GeneratedBy(
            type = AiMessage.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 0;

        public static final int SLOT_CREATED_TIME = 1;

        public static final int SLOT_EDITED_TIME = 2;

        public static final int SLOT_EDITOR = 3;

        public static final int SLOT_CREATOR = 4;

        public static final int SLOT_TYPE = 5;

        public static final int SLOT_TEXT_CONTENT = 6;

        public static final int SLOT_MEDIAS = 7;

        public static final int SLOT_SESSION_ID = 8;

        public static final int SLOT_SESSION = 9;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.134",
                AiMessage.class,
                Collections.singleton(BaseEntityDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (AiMessage)base)
            )
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("editedTime", SLOT_EDITED_TIME)
            .redefine("editor", SLOT_EDITOR)
            .redefine("creator", SLOT_CREATOR)
            .add(SLOT_TYPE, "type", ImmutablePropCategory.SCALAR, MessageType.class, false)
            .add(SLOT_TEXT_CONTENT, "textContent", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_MEDIAS, "medias", ImmutablePropCategory.SCALAR, List.class, true)
            .add(SLOT_SESSION_ID, "sessionId", ImmutablePropCategory.SCALAR, String.class, false)
            .add(SLOT_SESSION, "session", ManyToOne.class, AiSession.class, false)
            .build();

        private Producer() {
        }

        public AiMessage produce(DraftConsumer<AiMessageDraft> block) {
            return produce(null, block);
        }

        public AiMessage produce(AiMessage base, DraftConsumer<AiMessageDraft> block) {
            return (AiMessage)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = AiMessage.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "id", "createdTime", "editedTime", "editor", "creator", "type", "textContent", "medias", "sessionId", "session"})
        public abstract interface Implementor extends AiMessage, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_ID:
                    		return id();
                    case SLOT_CREATED_TIME:
                    		return createdTime();
                    case SLOT_EDITED_TIME:
                    		return editedTime();
                    case SLOT_EDITOR:
                    		return editor();
                    case SLOT_CREATOR:
                    		return creator();
                    case SLOT_TYPE:
                    		return type();
                    case SLOT_TEXT_CONTENT:
                    		return textContent();
                    case SLOT_MEDIAS:
                    		return medias();
                    case SLOT_SESSION_ID:
                    		return sessionId();
                    case SLOT_SESSION:
                    		return session();
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "id":
                    		return id();
                    case "createdTime":
                    		return createdTime();
                    case "editedTime":
                    		return editedTime();
                    case "editor":
                    		return editor();
                    case "creator":
                    		return creator();
                    case "type":
                    		return type();
                    case "textContent":
                    		return textContent();
                    case "medias":
                    		return medias();
                    case "sessionId":
                    		return sessionId();
                    case "session":
                    		return session();
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            default String getId() {
                return id();
            }

            default LocalDateTime getCreatedTime() {
                return createdTime();
            }

            default LocalDateTime getEditedTime() {
                return editedTime();
            }

            default User getEditor() {
                return editor();
            }

            default User getCreator() {
                return creator();
            }

            default MessageType getType() {
                return type();
            }

            default String getTextContent() {
                return textContent();
            }

            @Null
            default List<AiMessage.Media> getMedias() {
                return medias();
            }

            default String getSessionId() {
                return sessionId();
            }

            default AiSession getSession() {
                return session();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = AiMessage.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            String __idValue;

            LocalDateTime __createdTimeValue;

            LocalDateTime __editedTimeValue;

            User __editorValue;

            User __creatorValue;

            MessageType __typeValue;

            String __textContentValue;

            List<AiMessage.Media> __mediasValue;

            boolean __mediasLoaded = false;

            AiSession __sessionValue;

            Impl() {
                __visibility = Visibility.of(10);
                __visibility.show(SLOT_SESSION_ID, false);
            }

            @Override
            @JsonIgnore
            public String id() {
                if (__idValue == null) {
                    throw new UnloadedException(AiMessage.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public LocalDateTime createdTime() {
                if (__createdTimeValue == null) {
                    throw new UnloadedException(AiMessage.class, "createdTime");
                }
                return __createdTimeValue;
            }

            @Override
            @JsonIgnore
            public LocalDateTime editedTime() {
                if (__editedTimeValue == null) {
                    throw new UnloadedException(AiMessage.class, "editedTime");
                }
                return __editedTimeValue;
            }

            @Override
            @JsonIgnore
            public User editor() {
                if (__editorValue == null) {
                    throw new UnloadedException(AiMessage.class, "editor");
                }
                return __editorValue;
            }

            @Override
            @JsonIgnore
            public User creator() {
                if (__creatorValue == null) {
                    throw new UnloadedException(AiMessage.class, "creator");
                }
                return __creatorValue;
            }

            @Override
            @JsonIgnore
            public MessageType type() {
                if (__typeValue == null) {
                    throw new UnloadedException(AiMessage.class, "type");
                }
                return __typeValue;
            }

            @Override
            @JsonIgnore
            public String textContent() {
                if (__textContentValue == null) {
                    throw new UnloadedException(AiMessage.class, "textContent");
                }
                return __textContentValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public List<AiMessage.Media> medias() {
                if (!__mediasLoaded) {
                    throw new UnloadedException(AiMessage.class, "medias");
                }
                return __mediasValue;
            }

            @Override
            @JsonIgnore
            public String sessionId() {
                AiSession __target = session();
                return __target.id();
            }

            @Override
            @JsonIgnore
            public AiSession session() {
                if (__sessionValue == null) {
                    throw new UnloadedException(AiMessage.class, "session");
                }
                return __sessionValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_ID:
                    		return __idValue != null;
                    case SLOT_CREATED_TIME:
                    		return __createdTimeValue != null;
                    case SLOT_EDITED_TIME:
                    		return __editedTimeValue != null;
                    case SLOT_EDITOR:
                    		return __editorValue != null;
                    case SLOT_CREATOR:
                    		return __creatorValue != null;
                    case SLOT_TYPE:
                    		return __typeValue != null;
                    case SLOT_TEXT_CONTENT:
                    		return __textContentValue != null;
                    case SLOT_MEDIAS:
                    		return __mediasLoaded;
                    case SLOT_SESSION_ID:
                    		return __isLoaded(PropId.byIndex(SLOT_SESSION)) && (session() == null || 
                            	((ImmutableSpi)session()).__isLoaded(PropId.byIndex(AiSessionDraft.Producer.SLOT_ID)));
                    case SLOT_SESSION:
                    		return __sessionValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "id":
                    		return __idValue != null;
                    case "createdTime":
                    		return __createdTimeValue != null;
                    case "editedTime":
                    		return __editedTimeValue != null;
                    case "editor":
                    		return __editorValue != null;
                    case "creator":
                    		return __creatorValue != null;
                    case "type":
                    		return __typeValue != null;
                    case "textContent":
                    		return __textContentValue != null;
                    case "medias":
                    		return __mediasLoaded;
                    case "sessionId":
                    		return __isLoaded(PropId.byIndex(SLOT_SESSION)) && (session() == null || 
                            	((ImmutableSpi)session()).__isLoaded(PropId.byIndex(AiSessionDraft.Producer.SLOT_ID)));
                    case "session":
                    		return __sessionValue != null;
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_CREATED_TIME:
                    		return __visibility.visible(SLOT_CREATED_TIME);
                    case SLOT_EDITED_TIME:
                    		return __visibility.visible(SLOT_EDITED_TIME);
                    case SLOT_EDITOR:
                    		return __visibility.visible(SLOT_EDITOR);
                    case SLOT_CREATOR:
                    		return __visibility.visible(SLOT_CREATOR);
                    case SLOT_TYPE:
                    		return __visibility.visible(SLOT_TYPE);
                    case SLOT_TEXT_CONTENT:
                    		return __visibility.visible(SLOT_TEXT_CONTENT);
                    case SLOT_MEDIAS:
                    		return __visibility.visible(SLOT_MEDIAS);
                    case SLOT_SESSION_ID:
                    		return __visibility.visible(SLOT_SESSION_ID);
                    case SLOT_SESSION:
                    		return __visibility.visible(SLOT_SESSION);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "createdTime":
                    		return __visibility.visible(SLOT_CREATED_TIME);
                    case "editedTime":
                    		return __visibility.visible(SLOT_EDITED_TIME);
                    case "editor":
                    		return __visibility.visible(SLOT_EDITOR);
                    case "creator":
                    		return __visibility.visible(SLOT_CREATOR);
                    case "type":
                    		return __visibility.visible(SLOT_TYPE);
                    case "textContent":
                    		return __visibility.visible(SLOT_TEXT_CONTENT);
                    case "medias":
                    		return __visibility.visible(SLOT_MEDIAS);
                    case "sessionId":
                    		return __visibility.visible(SLOT_SESSION_ID);
                    case "session":
                    		return __visibility.visible(SLOT_SESSION);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idValue != null) {
                    hash = 31 * hash + __idValue.hashCode();
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createdTimeValue != null) {
                    hash = 31 * hash + __createdTimeValue.hashCode();
                }
                if (__editedTimeValue != null) {
                    hash = 31 * hash + __editedTimeValue.hashCode();
                }
                if (__editorValue != null) {
                    hash = 31 * hash + __editorValue.hashCode();
                }
                if (__creatorValue != null) {
                    hash = 31 * hash + __creatorValue.hashCode();
                }
                if (__typeValue != null) {
                    hash = 31 * hash + __typeValue.hashCode();
                }
                if (__textContentValue != null) {
                    hash = 31 * hash + __textContentValue.hashCode();
                }
                if (__mediasLoaded && __mediasValue != null) {
                    hash = 31 * hash + __mediasValue.hashCode();
                }
                if (__sessionValue != null) {
                    hash = 31 * hash + __sessionValue.hashCode();
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idValue != null) {
                    hash = 31 * hash + System.identityHashCode(__idValue);
                }
                if (__createdTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createdTimeValue);
                }
                if (__editedTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__editedTimeValue);
                }
                if (__editorValue != null) {
                    hash = 31 * hash + System.identityHashCode(__editorValue);
                }
                if (__creatorValue != null) {
                    hash = 31 * hash + System.identityHashCode(__creatorValue);
                }
                if (__typeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__typeValue);
                }
                if (__textContentValue != null) {
                    hash = 31 * hash + System.identityHashCode(__textContentValue);
                }
                if (__mediasLoaded) {
                    hash = 31 * hash + System.identityHashCode(__mediasValue);
                }
                if (__sessionValue != null) {
                    hash = 31 * hash + System.identityHashCode(__sessionValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = __idValue != null;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return Objects.equals(__idValue, __other.id());
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false;
                }
                boolean __createdTimeLoaded = __createdTimeValue != null;
                if (__createdTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false;
                }
                if (__createdTimeLoaded && !Objects.equals(__createdTimeValue, __other.createdTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EDITED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_EDITED_TIME))) {
                    return false;
                }
                boolean __editedTimeLoaded = __editedTimeValue != null;
                if (__editedTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EDITED_TIME))) {
                    return false;
                }
                if (__editedTimeLoaded && !Objects.equals(__editedTimeValue, __other.editedTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EDITOR)) != __other.__isVisible(PropId.byIndex(SLOT_EDITOR))) {
                    return false;
                }
                boolean __editorLoaded = __editorValue != null;
                if (__editorLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EDITOR))) {
                    return false;
                }
                if (__editorLoaded && !Objects.equals(__editorValue, __other.editor())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATOR)) != __other.__isVisible(PropId.byIndex(SLOT_CREATOR))) {
                    return false;
                }
                boolean __creatorLoaded = __creatorValue != null;
                if (__creatorLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATOR))) {
                    return false;
                }
                if (__creatorLoaded && !Objects.equals(__creatorValue, __other.creator())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                boolean __typeLoaded = __typeValue != null;
                if (__typeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                if (__typeLoaded && !Objects.equals(__typeValue, __other.type())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TEXT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_TEXT_CONTENT))) {
                    return false;
                }
                boolean __textContentLoaded = __textContentValue != null;
                if (__textContentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TEXT_CONTENT))) {
                    return false;
                }
                if (__textContentLoaded && !Objects.equals(__textContentValue, __other.textContent())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MEDIAS)) != __other.__isVisible(PropId.byIndex(SLOT_MEDIAS))) {
                    return false;
                }
                boolean __mediasLoaded = this.__mediasLoaded;
                if (__mediasLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MEDIAS))) {
                    return false;
                }
                if (__mediasLoaded && !Objects.equals(__mediasValue, __other.medias())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SESSION_ID)) != __other.__isVisible(PropId.byIndex(SLOT_SESSION_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SESSION)) != __other.__isVisible(PropId.byIndex(SLOT_SESSION))) {
                    return false;
                }
                boolean __sessionLoaded = __sessionValue != null;
                if (__sessionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SESSION))) {
                    return false;
                }
                if (__sessionLoaded && !Objects.equals(__sessionValue, __other.session())) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = __idValue != null;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false;
                }
                boolean __createdTimeLoaded = __createdTimeValue != null;
                if (__createdTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false;
                }
                if (__createdTimeLoaded && __createdTimeValue != __other.createdTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EDITED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_EDITED_TIME))) {
                    return false;
                }
                boolean __editedTimeLoaded = __editedTimeValue != null;
                if (__editedTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EDITED_TIME))) {
                    return false;
                }
                if (__editedTimeLoaded && __editedTimeValue != __other.editedTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_EDITOR)) != __other.__isVisible(PropId.byIndex(SLOT_EDITOR))) {
                    return false;
                }
                boolean __editorLoaded = __editorValue != null;
                if (__editorLoaded != __other.__isLoaded(PropId.byIndex(SLOT_EDITOR))) {
                    return false;
                }
                if (__editorLoaded && __editorValue != __other.editor()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATOR)) != __other.__isVisible(PropId.byIndex(SLOT_CREATOR))) {
                    return false;
                }
                boolean __creatorLoaded = __creatorValue != null;
                if (__creatorLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATOR))) {
                    return false;
                }
                if (__creatorLoaded && __creatorValue != __other.creator()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                boolean __typeLoaded = __typeValue != null;
                if (__typeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TYPE))) {
                    return false;
                }
                if (__typeLoaded && __typeValue != __other.type()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_TEXT_CONTENT)) != __other.__isVisible(PropId.byIndex(SLOT_TEXT_CONTENT))) {
                    return false;
                }
                boolean __textContentLoaded = __textContentValue != null;
                if (__textContentLoaded != __other.__isLoaded(PropId.byIndex(SLOT_TEXT_CONTENT))) {
                    return false;
                }
                if (__textContentLoaded && __textContentValue != __other.textContent()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MEDIAS)) != __other.__isVisible(PropId.byIndex(SLOT_MEDIAS))) {
                    return false;
                }
                boolean __mediasLoaded = this.__mediasLoaded;
                if (__mediasLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MEDIAS))) {
                    return false;
                }
                if (__mediasLoaded && __mediasValue != __other.medias()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SESSION_ID)) != __other.__isVisible(PropId.byIndex(SLOT_SESSION_ID))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_SESSION)) != __other.__isVisible(PropId.byIndex(SLOT_SESSION))) {
                    return false;
                }
                boolean __sessionLoaded = __sessionValue != null;
                if (__sessionLoaded != __other.__isLoaded(PropId.byIndex(SLOT_SESSION))) {
                    return false;
                }
                if (__sessionLoaded && __sessionValue != __other.session()) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = AiMessage.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, AiMessageDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            DraftImpl(DraftContext ctx, AiMessage base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString((__modified!= null ? __modified : __base));
            }

            @Override
            @JsonIgnore
            public String id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public AiMessageDraft setId(String id) {
                if (id == null) {
                    throw new IllegalArgumentException(
                        "'id' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                return this;
            }

            @Override
            @JsonIgnore
            public LocalDateTime createdTime() {
                return (__modified!= null ? __modified : __base).createdTime();
            }

            @Override
            public AiMessageDraft setCreatedTime(LocalDateTime createdTime) {
                if (createdTime == null) {
                    throw new IllegalArgumentException(
                        "'createdTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createdTimeValue = createdTime;
                return this;
            }

            @Override
            @JsonIgnore
            public LocalDateTime editedTime() {
                return (__modified!= null ? __modified : __base).editedTime();
            }

            @Override
            public AiMessageDraft setEditedTime(LocalDateTime editedTime) {
                if (editedTime == null) {
                    throw new IllegalArgumentException(
                        "'editedTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__editedTimeValue = editedTime;
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft editor() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).editor());
            }

            @Override
            public UserDraft editor(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_EDITOR)))) {
                    setEditor(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).editor());
            }

            @Override
            public AiMessageDraft setEditor(User editor) {
                if (editor == null) {
                    throw new IllegalArgumentException(
                        "'editor' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__editorValue = editor;
                return this;
            }

            @NotNull
            @Override
            public String editorId() {
                return editor().id();
            }

            @OldChain
            @Override
            public AiMessageDraft setEditorId(@NotNull String editorId) {
                editor(true).setId(Objects.requireNonNull(editorId, "\"editor\" cannot be null"));
                return this;
            }

            @Override
            public AiMessageDraft applyEditor(DraftConsumer<UserDraft> block) {
                applyEditor(null, block);
                return this;
            }

            @Override
            public AiMessageDraft applyEditor(User base, DraftConsumer<UserDraft> block) {
                setEditor(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public UserDraft creator() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).creator());
            }

            @Override
            public UserDraft creator(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_CREATOR)))) {
                    setCreator(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).creator());
            }

            @Override
            public AiMessageDraft setCreator(User creator) {
                if (creator == null) {
                    throw new IllegalArgumentException(
                        "'creator' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__creatorValue = creator;
                return this;
            }

            @NotNull
            @Override
            public String creatorId() {
                return creator().id();
            }

            @OldChain
            @Override
            public AiMessageDraft setCreatorId(@NotNull String creatorId) {
                creator(true).setId(Objects.requireNonNull(creatorId, "\"creator\" cannot be null"));
                return this;
            }

            @Override
            public AiMessageDraft applyCreator(DraftConsumer<UserDraft> block) {
                applyCreator(null, block);
                return this;
            }

            @Override
            public AiMessageDraft applyCreator(User base, DraftConsumer<UserDraft> block) {
                setCreator(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public MessageType type() {
                return (__modified!= null ? __modified : __base).type();
            }

            @Override
            public AiMessageDraft setType(MessageType type) {
                if (type == null) {
                    throw new IllegalArgumentException(
                        "'type' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__typeValue = type;
                return this;
            }

            @Override
            @JsonIgnore
            public String textContent() {
                return (__modified!= null ? __modified : __base).textContent();
            }

            @Override
            public AiMessageDraft setTextContent(String textContent) {
                if (textContent == null) {
                    throw new IllegalArgumentException(
                        "'textContent' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__textContentValue = textContent;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public List<AiMessage.Media> medias() {
                return (__modified!= null ? __modified : __base).medias();
            }

            @Override
            public AiMessageDraft setMedias(List<AiMessage.Media> medias) {
                Impl __tmpModified = __modified();
                __tmpModified.__mediasValue = medias;
                __tmpModified.__mediasLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String sessionId() {
                AiSession __target = session();
                return __target.id();
            }

            @Override
            public AiMessageDraft setSessionId(String sessionId) {
                if (sessionId != null) {
                    setSession(ImmutableObjects.makeIdOnly(AiSession.class, sessionId));
                } else {
                    setSession(null);
                }
                return this;
            }

            @Override
            @JsonIgnore
            public AiSessionDraft session() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).session());
            }

            @Override
            public AiSessionDraft session(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_SESSION)))) {
                    setSession(AiSessionDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).session());
            }

            @Override
            public AiMessageDraft setSession(AiSession session) {
                if (session == null) {
                    throw new IllegalArgumentException(
                        "'session' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__sessionValue = session;
                return this;
            }

            @Override
            public AiMessageDraft applySession(DraftConsumer<AiSessionDraft> block) {
                applySession(null, block);
                return this;
            }

            @Override
            public AiMessageDraft applySession(AiSession base,
                    DraftConsumer<AiSessionDraft> block) {
                setSession(AiSessionDraft.$.produce(base, block));
                return this;
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_ID:
                    		setId((String)value);break;
                    case SLOT_CREATED_TIME:
                    		setCreatedTime((LocalDateTime)value);break;
                    case SLOT_EDITED_TIME:
                    		setEditedTime((LocalDateTime)value);break;
                    case SLOT_EDITOR:
                    		setEditor((User)value);break;
                    case SLOT_CREATOR:
                    		setCreator((User)value);break;
                    case SLOT_TYPE:
                    		setType((MessageType)value);break;
                    case SLOT_TEXT_CONTENT:
                    		setTextContent((String)value);break;
                    case SLOT_MEDIAS:
                    		setMedias((List<AiMessage.Media>)value);break;
                    case SLOT_SESSION_ID:
                    		setSessionId((String)value);break;
                    case SLOT_SESSION:
                    		setSession((AiSession)value);break;
                    default: throw new IllegalArgumentException("Illegal property id for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "id":
                    		setId((String)value);break;
                    case "createdTime":
                    		setCreatedTime((LocalDateTime)value);break;
                    case "editedTime":
                    		setEditedTime((LocalDateTime)value);break;
                    case "editor":
                    		setEditor((User)value);break;
                    case "creator":
                    		setCreator((User)value);break;
                    case "type":
                    		setType((MessageType)value);break;
                    case "textContent":
                    		setTextContent((String)value);break;
                    case "medias":
                    		setMedias((List<AiMessage.Media>)value);break;
                    case "sessionId":
                    		setSessionId((String)value);break;
                    case "session":
                    		setSession((AiSession)value);break;
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(10);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_CREATED_TIME:
                    		__visibility.show(SLOT_CREATED_TIME, visible);break;
                    case SLOT_EDITED_TIME:
                    		__visibility.show(SLOT_EDITED_TIME, visible);break;
                    case SLOT_EDITOR:
                    		__visibility.show(SLOT_EDITOR, visible);break;
                    case SLOT_CREATOR:
                    		__visibility.show(SLOT_CREATOR, visible);break;
                    case SLOT_TYPE:
                    		__visibility.show(SLOT_TYPE, visible);break;
                    case SLOT_TEXT_CONTENT:
                    		__visibility.show(SLOT_TEXT_CONTENT, visible);break;
                    case SLOT_MEDIAS:
                    		__visibility.show(SLOT_MEDIAS, visible);break;
                    case SLOT_SESSION_ID:
                    		__visibility.show(SLOT_SESSION_ID, visible);break;
                    case SLOT_SESSION:
                    		__visibility.show(SLOT_SESSION, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(10);
                }
                switch (prop) {
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "createdTime":
                    		__visibility.show(SLOT_CREATED_TIME, visible);break;
                    case "editedTime":
                    		__visibility.show(SLOT_EDITED_TIME, visible);break;
                    case "editor":
                    		__visibility.show(SLOT_EDITOR, visible);break;
                    case "creator":
                    		__visibility.show(SLOT_CREATOR, visible);break;
                    case "type":
                    		__visibility.show(SLOT_TYPE, visible);break;
                    case "textContent":
                    		__visibility.show(SLOT_TEXT_CONTENT, visible);break;
                    case "medias":
                    		__visibility.show(SLOT_MEDIAS, visible);break;
                    case "sessionId":
                    		__visibility.show(SLOT_SESSION_ID, visible);break;
                    case "session":
                    		__visibility.show(SLOT_SESSION, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_ID:
                    		__modified().__idValue = null;break;
                    case SLOT_CREATED_TIME:
                    		__modified().__createdTimeValue = null;break;
                    case SLOT_EDITED_TIME:
                    		__modified().__editedTimeValue = null;break;
                    case SLOT_EDITOR:
                    		__modified().__editorValue = null;break;
                    case SLOT_CREATOR:
                    		__modified().__creatorValue = null;break;
                    case SLOT_TYPE:
                    		__modified().__typeValue = null;break;
                    case SLOT_TEXT_CONTENT:
                    		__modified().__textContentValue = null;break;
                    case SLOT_MEDIAS:
                    		__modified().__mediasLoaded = false;break;
                    case SLOT_SESSION_ID:
                    		__unload(PropId.byIndex(SLOT_SESSION));break;
                    case SLOT_SESSION:
                    		__modified().__sessionValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property id for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                switch (prop) {
                    case "id":
                    		__modified().__idValue = null;break;
                    case "createdTime":
                    		__modified().__createdTimeValue = null;break;
                    case "editedTime":
                    		__modified().__editedTimeValue = null;break;
                    case "editor":
                    		__modified().__editorValue = null;break;
                    case "creator":
                    		__modified().__creatorValue = null;break;
                    case "type":
                    		__modified().__typeValue = null;break;
                    case "textContent":
                    		__modified().__textContentValue = null;break;
                    case "medias":
                    		__modified().__mediasLoaded = false;break;
                    case "sessionId":
                    		__unload(PropId.byIndex(SLOT_SESSION));break;
                    case "session":
                    		__modified().__sessionValue = null;break;
                    default: throw new IllegalArgumentException("Illegal property name for \"io.github.qifan777.knowledge.ai.messge.AiMessage\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_EDITOR))) {
                            User oldValue = base.editor();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setEditor(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_CREATOR))) {
                            User oldValue = base.creator();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setCreator(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_SESSION))) {
                            AiSession oldValue = base.session();
                            AiSession newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setSession(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__editorValue = __ctx.resolveObject(__tmpModified.__editorValue);
                        __tmpModified.__creatorValue = __ctx.resolveObject(__tmpModified.__creatorValue);
                        __tmpModified.__sessionValue = __ctx.resolveObject(__tmpModified.__sessionValue);
                    }
                    if (__base != null && __tmpModified == null) {
                        return base;
                    }
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = AiMessage.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_SESSION_ID), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_SESSION), false);
        }

        public Builder id(String id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder createdTime(LocalDateTime createdTime) {
            if (createdTime != null) {
                __draft.setCreatedTime(createdTime);
            }
            return this;
        }

        public Builder editedTime(LocalDateTime editedTime) {
            if (editedTime != null) {
                __draft.setEditedTime(editedTime);
            }
            return this;
        }

        public Builder editor(User editor) {
            if (editor != null) {
                __draft.setEditor(editor);
            }
            return this;
        }

        public Builder creator(User creator) {
            if (creator != null) {
                __draft.setCreator(creator);
            }
            return this;
        }

        public Builder type(MessageType type) {
            if (type != null) {
                __draft.setType(type);
            }
            return this;
        }

        public Builder textContent(String textContent) {
            if (textContent != null) {
                __draft.setTextContent(textContent);
            }
            return this;
        }

        @Null
        public Builder medias(List<AiMessage.Media> medias) {
            __draft.setMedias(medias);
            return this;
        }

        public Builder sessionId(String sessionId) {
            if (sessionId != null) {
                __draft.setSessionId(sessionId);
                __draft.__show(PropId.byIndex(Producer.SLOT_SESSION_ID), true);
            }
            return this;
        }

        public Builder session(AiSession session) {
            if (session != null) {
                __draft.setSession(session);
                __draft.__show(PropId.byIndex(Producer.SLOT_SESSION), true);
            }
            return this;
        }

        public AiMessage build() {
            return (AiMessage)__draft.__modified();
        }
    }
}
