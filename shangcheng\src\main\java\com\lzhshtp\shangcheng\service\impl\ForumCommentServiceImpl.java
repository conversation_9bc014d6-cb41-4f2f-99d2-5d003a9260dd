package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.ForumCommentDTO;
import com.lzhshtp.shangcheng.dto.ForumCommentRequest;
import com.lzhshtp.shangcheng.exception.BusinessException;
import com.lzhshtp.shangcheng.mapper.ForumCommentMapper;
import com.lzhshtp.shangcheng.mapper.ForumPostMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.ForumComment;
import com.lzhshtp.shangcheng.model.ForumPost;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ForumCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 论坛评论服务实现类
 */
@Service
@RequiredArgsConstructor
public class ForumCommentServiceImpl extends ServiceImpl<ForumCommentMapper, ForumComment> implements ForumCommentService {

    private final ForumCommentMapper forumCommentMapper;
    private final ForumPostMapper forumPostMapper;
    private final UserMapper userMapper;

    /**
     * 创建评论
     *
     * @param request 评论请求
     * @param userId  用户ID
     * @return 评论ID
     */
    @Override
    @Transactional
    public Long createComment(ForumCommentRequest request, Long userId) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectPostDetail(request.getPostId());
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        // 如果是回复评论，检查父评论是否存在
        if (request.getParentCommentId() != null) {
            ForumComment parentComment = getById(request.getParentCommentId());
            if (parentComment == null) {
                throw new BusinessException("回复的评论不存在");
            }
            
            // 确保父评论属于同一个帖子
            if (!parentComment.getPostId().equals(request.getPostId())) {
                throw new BusinessException("回复的评论不属于该帖子");
            }
        }

        ForumComment comment = ForumComment.builder()
                .postId(request.getPostId())
                .authorId(userId)
                .content(request.getContent())
                .commentedAt(LocalDateTime.now())
                .parentCommentId(request.getParentCommentId())
                .build();

        save(comment);
        return comment.getCommentId();
    }

    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteComment(Long commentId, Long userId) {
        ForumComment comment = getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        // 检查是否是评论作者
        if (!comment.getAuthorId().equals(userId)) {
            throw new BusinessException("只有评论作者才能删除评论");
        }

        // 删除该评论下的所有回复
        LambdaQueryWrapper<ForumComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForumComment::getParentCommentId, commentId);
        remove(queryWrapper);

        // 删除评论
        return removeById(commentId);
    }

    /**
     * 获取帖子的所有评论（树形结构）
     *
     * @param postId 帖子ID
     * @return 评论列表
     */
    @Override
    public List<ForumCommentDTO> getCommentsByPostId(Long postId) {
        // 使用XML中定义的方法查询帖子的所有评论
        List<ForumComment> comments = forumCommentMapper.selectCommentsByPostId(postId);
        
        if (comments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 将评论按照父子关系组织
        Map<Long, List<ForumComment>> parentChildMap = new HashMap<>();
        List<ForumComment> topComments = new ArrayList<>();
        
        // 分类评论
        for (ForumComment comment : comments) {
            if (comment.getParentCommentId() == null) {
                // 顶级评论
                topComments.add(comment);
            } else {
                // 子评论
                Long parentId = comment.getParentCommentId();
                if (!parentChildMap.containsKey(parentId)) {
                    parentChildMap.put(parentId, new ArrayList<>());
                }
                parentChildMap.get(parentId).add(comment);
            }
        }
        
        // 转换为DTO并构建树形结构
        return topComments.stream()
                .map(comment -> convertToDTO(comment, parentChildMap))
                .collect(Collectors.toList());
    }

    /**
     * 获取评论的所有回复
     *
     * @param commentId 评论ID
     * @return 回复列表
     */
    @Override
    public List<ForumCommentDTO> getRepliesByCommentId(Long commentId) {
        // 使用XML中定义的方法查询评论的所有回复
        List<ForumComment> replies = forumCommentMapper.selectRepliesByCommentId(commentId);
        
        // 转换为DTO
        return replies.stream()
                .map(reply -> {
                    ForumCommentDTO dto = convertToDTO(reply, null);
                    
                    // 获取父评论作者名称
                    ForumComment parentComment = getById(commentId);
                    if (parentComment != null) {
                        User parentAuthor = userMapper.selectById(parentComment.getAuthorId());
                        if (parentAuthor != null) {
                            dto.setParentAuthorName(parentAuthor.getUsername());
                        }
                    }
                    
                    return dto;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 将评论实体转换为DTO
     * 
     * @param comment 评论实体
     * @param parentChildMap 父子评论映射
     * @return 评论DTO
     */
    private ForumCommentDTO convertToDTO(ForumComment comment, Map<Long, List<ForumComment>> parentChildMap) {
        ForumCommentDTO dto = new ForumCommentDTO();
        dto.setCommentId(comment.getCommentId());
        dto.setPostId(comment.getPostId());
        dto.setAuthorId(comment.getAuthorId());
        dto.setContent(comment.getContent());
        dto.setCommentedAt(comment.getCommentedAt());
        dto.setParentCommentId(comment.getParentCommentId());
        
        // 获取作者信息
        User author = userMapper.selectById(comment.getAuthorId());
        if (author != null) {
            dto.setAuthorName(author.getUsername());
            dto.setAuthorAvatar(author.getAvatarUrl());
        }
        
        // 如果有父评论，获取父评论作者名称
        if (comment.getParentCommentId() != null) {
            ForumComment parentComment = getById(comment.getParentCommentId());
            if (parentComment != null) {
                User parentAuthor = userMapper.selectById(parentComment.getAuthorId());
                if (parentAuthor != null) {
                    dto.setParentAuthorName(parentAuthor.getUsername());
                }
            }
        }
        
        // 如果有子评论，递归转换
        if (parentChildMap != null && parentChildMap.containsKey(comment.getCommentId())) {
            List<ForumComment> childComments = parentChildMap.get(comment.getCommentId());
            List<ForumCommentDTO> childDTOs = childComments.stream()
                    .map(childComment -> convertToDTO(childComment, parentChildMap))
                    .collect(Collectors.toList());
            dto.setReplies(childDTOs);
        } else {
            dto.setReplies(new ArrayList<>());
        }
        
        return dto;
    }
} 