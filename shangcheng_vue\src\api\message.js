import request from '@/utils/request';

/**
 * 获取当前用户的所有会话列表
 * @returns {Promise} 会话列表
 */
export function getUserConversations() {
  return request({
    url: '/messages/conversations',
    method: 'get'
  });
}

/**
 * 获取会话中的消息列表
 * @param {Number} conversationId 会话ID
 * @param {Number} page 页码
 * @param {Number} size 每页大小
 * @returns {Promise} 消息列表
 */
export function getConversationMessages(conversationId, page = 1, size = 20) {
  return request({
    url: `/messages/conversations/${conversationId}`,
    method: 'get',
    params: { page, size }
  });
}

/**
 * 发送消息
 * @param {Object} messageRequest 消息请求对象，包含receiverId和content
 * @returns {Promise} 发送的消息
 */
export function sendMessage(messageRequest) {
  return request({
    url: '/messages/send',
    method: 'post',
    data: messageRequest
  });
}

/**
 * 将会话中的消息标记为已读
 * @param {Number} conversationId 会话ID
 * @returns {Promise} 操作结果
 */
export function markMessagesAsRead(conversationId) {
  return request({
    url: `/messages/conversations/${conversationId}/read`,
    method: 'put'
  });
}

/**
 * 删除消息
 * @param {Number} messageId 消息ID
 * @returns {Promise} 操作结果
 */
export function deleteMessage(messageId) {
  return request({
    url: `/messages/${messageId}`,
    method: 'delete'
  });
}

/**
 * 获取当前用户的未读消息总数
 * @returns {Promise} 未读消息数
 */
export function getUnreadCount() {
  return request({
    url: '/messages/unread-count',
    method: 'get'
  });
}

/**
 * 获取或创建与指定用户的会话
 * @param {Number} userId 对方用户ID
 * @returns {Promise} 会话ID
 */
export function getOrCreateConversation(userId) {
  return request({
    url: `/messages/conversations/with-user/${userId}`,
    method: 'get'
  });
}
