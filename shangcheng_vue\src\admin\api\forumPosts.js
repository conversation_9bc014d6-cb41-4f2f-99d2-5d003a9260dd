import request from '@/utils/request'

const BASE_URL = '/forum/posts'

/**
 * 获取论坛帖子列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @param {number} params.categoryId - 分类ID
 * @param {boolean} params.onlyPinned - 是否只看置顶
 * @param {boolean} params.enablePinned - 是否启用置顶功能
 * @param {string} params.orderBy - 排序方式
 * @returns {Promise} - 返回帖子列表
 */
export function getPostList(params) {
  return request({
    url: BASE_URL,
    method: 'get',
    params
  })
}

/**
 * 获取帖子详情
 * @param {number} postId - 帖子ID
 * @returns {Promise} - 返回帖子详情
 */
export function getPostDetail(postId) {
  return request({
    url: `${BASE_URL}/${postId}`,
    method: 'get'
  })
}

/**
 * 设置帖子置顶状态
 * @param {number} postId - 帖子ID
 * @param {boolean} isPinned - 是否置顶
 * @returns {Promise} - 返回操作结果
 */
export function setPinned(postId, isPinned) {
  return request({
    url: `${BASE_URL}/${postId}/pinned`,
    method: 'put',
    params: { isPinned }
  })
}

/**
 * 设置帖子状态
 * @param {number} postId - 帖子ID
 * @param {number} status - 帖子状态 0-已发布 1-下架
 * @returns {Promise} - 返回操作结果
 */
export function setStatus(postId, status) {
  return request({
    url: `${BASE_URL}/${postId}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 删除帖子（管理员操作）
 * @param {number} postId - 帖子ID
 * @returns {Promise} - 返回操作结果
 */
export function deletePost(postId) {
  return request({
    url: `${BASE_URL}/${postId}`,
    method: 'delete'
  })
}

/**
 * 获取帖子状态选项
 * @returns {Array} - 帖子状态选项
 */
export function getPostStatusOptions() {
  return [
    { value: 0, label: '已发布' },
    { value: 1, label: '已下架' }
  ]
}

/**
 * 获取帖子置顶状态选项
 * @returns {Array} - 帖子置顶状态选项
 */
export function getPinnedStatusOptions() {
  return [
    { value: 0, label: '未置顶' },
    { value: 1, label: '已置顶' }
  ]
} 