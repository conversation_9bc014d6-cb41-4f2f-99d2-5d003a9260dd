package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 定时任务配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_scheduled_task_configs")
public class ScheduledTaskConfig {
    
    @TableId(value = "lzhshtp_task_id", type = IdType.AUTO)
    private Long taskId;
    
    @TableField("lzhshtp_task_name")
    private String taskName;
    
    @TableField("lzhshtp_task_type")
    private String taskType;
    
    @TableField("lzhshtp_cron_expression")
    private String cronExpression;
    
    @TableField("lzhshtp_is_enabled")
    private Boolean isEnabled;
    
    @TableField("lzhshtp_config_params")
    private String configParams; // JSON格式存储配置参数
    
    @TableField("lzhshtp_last_execution")
    private LocalDateTime lastExecution;
    
    @TableField("lzhshtp_next_execution")
    private LocalDateTime nextExecution;
    
    @TableField(value = "lzhshtp_created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "lzhshtp_updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        ORDER_TIMEOUT("order_timeout", "订单超时自动取消"),
        ES_SYNC("es_sync", "ElasticSearch索引同步"),
        MESSAGE_PUSH("message_push", "系统消息推送"),
        DATA_CLEANUP("data_cleanup", "数据清理"),
        STATISTICS("statistics", "统计报表生成");
        
        private final String code;
        private final String description;
        
        TaskType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
