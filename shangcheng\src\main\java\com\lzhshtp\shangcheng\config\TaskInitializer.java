package com.lzhshtp.shangcheng.config;

import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;
import com.lzhshtp.shangcheng.model.ScheduledTaskConfig;
import com.lzhshtp.shangcheng.service.DynamicScheduledTaskService;
import com.lzhshtp.shangcheng.service.OrderTimeoutService;
import com.lzhshtp.shangcheng.service.TaskConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 系统启动时初始化定时任务
 */
@Component
public class TaskInitializer implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskInitializer.class);
    
    @Autowired
    private TaskConfigService taskConfigService;
    
    @Autowired
    private DynamicScheduledTaskService dynamicScheduledTaskService;
    
    @Autowired
    private OrderTimeoutService orderTimeoutService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("开始初始化定时任务...");
        
        try {
            // 获取所有启用的任务配置
            List<ScheduledTaskConfig> enabledTasks = taskConfigService.getAllTaskConfigs()
                .stream()
                .filter(ScheduledTaskConfig::getIsEnabled)
                .toList();
            
            for (ScheduledTaskConfig taskConfig : enabledTasks) {
                initializeTask(taskConfig);
            }
            
            logger.info("定时任务初始化完成，共启动 {} 个任务", enabledTasks.size());
            
        } catch (Exception e) {
            logger.error("定时任务初始化失败", e);
        }
    }
    
    /**
     * 初始化单个任务
     */
    private void initializeTask(ScheduledTaskConfig taskConfig) {
        try {
            String taskName = taskConfig.getTaskName();
            String cronExpression = taskConfig.getCronExpression();
            
            switch (taskName) {
                case "order_timeout_cancel":
                    // 初始化订单超时取消任务
                    dynamicScheduledTaskService.scheduleTask(
                        taskName,
                        cronExpression,
                        () -> {
                            // 在任务执行时获取最新配置
                            OrderTimeoutConfigDTO config = taskConfigService.getOrderTimeoutConfig();
                            orderTimeoutService.cancelExpiredOrders(config);
                        }
                    );
                    logger.info("订单超时取消任务已启动，Cron表达式：{}", cronExpression);
                    break;
                    
                default:
                    logger.warn("未知的任务类型：{}", taskName);
                    break;
            }
            
        } catch (Exception e) {
            logger.error("初始化任务失败：{}", taskConfig.getTaskName(), e);
        }
    }
}
