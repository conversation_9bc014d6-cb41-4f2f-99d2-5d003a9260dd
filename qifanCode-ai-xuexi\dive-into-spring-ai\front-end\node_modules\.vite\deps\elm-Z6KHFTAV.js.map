{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/elm.js"], "sourcesContent": ["function switchState(source, setState, f)\n{\n  setState(f);\n  return f(source, setState);\n}\n\nvar lowerRE = /[a-z]/;\nvar upperRE = /[A-Z]/;\nvar innerRE = /[a-zA-Z0-9_]/;\n\nvar digitRE = /[0-9]/;\nvar hexRE = /[0-9A-Fa-f]/;\nvar symbolRE = /[-&*+.\\\\/<>=?^|:]/;\nvar specialRE = /[(),[\\]{}]/;\nvar spacesRE = /[ \\v\\f]/; // newlines are handled in tokenizer\n\nfunction normal()\n{\n  return function(source, setState)\n  {\n    if (source.eatWhile(spacesRE))\n    {\n      return null;\n    }\n\n    var char = source.next();\n\n    if (specialRE.test(char))\n    {\n      return (char === '{' && source.eat('-'))\n        ? switchState(source, setState, chompMultiComment(1))\n        : (char === '[' && source.match('glsl|'))\n        ? switchState(source, setState, chompGlsl)\n        : 'builtin';\n    }\n\n    if (char === '\\'')\n    {\n      return switchState(source, setState, chompChar);\n    }\n\n    if (char === '\"')\n    {\n      return source.eat('\"')\n        ? source.eat('\"')\n        ? switchState(source, setState, chompMultiString)\n        : 'string'\n      : switchState(source, setState, chompSingleString);\n    }\n\n    if (upperRE.test(char))\n    {\n      source.eatWhile(innerRE);\n      return 'type';\n    }\n\n    if (lowerRE.test(char))\n    {\n      var isDef = source.pos === 1;\n      source.eatWhile(innerRE);\n      return isDef ? \"def\" : \"variable\";\n    }\n\n    if (digitRE.test(char))\n    {\n      if (char === '0')\n      {\n        if (source.eat(/[xX]/))\n        {\n          source.eatWhile(hexRE); // should require at least 1\n          return \"number\";\n        }\n      }\n      else\n      {\n        source.eatWhile(digitRE);\n      }\n      if (source.eat('.'))\n      {\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      if (source.eat(/[eE]/))\n      {\n        source.eat(/[-+]/);\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      return \"number\";\n    }\n\n    if (symbolRE.test(char))\n    {\n      if (char === '-' && source.eat('-'))\n      {\n        source.skipToEnd();\n        return \"comment\";\n      }\n      source.eatWhile(symbolRE);\n      return \"keyword\";\n    }\n\n    if (char === '_')\n    {\n      return \"keyword\";\n    }\n\n    return \"error\";\n  }\n}\n\nfunction chompMultiComment(nest)\n{\n  if (nest == 0)\n  {\n    return normal();\n  }\n  return function(source, setState)\n  {\n    while (!source.eol())\n    {\n      var char = source.next();\n      if (char == '{' && source.eat('-'))\n      {\n        ++nest;\n      }\n      else if (char == '-' && source.eat('}'))\n      {\n        --nest;\n        if (nest === 0)\n        {\n          setState(normal());\n          return 'comment';\n        }\n      }\n    }\n    setState(chompMultiComment(nest));\n    return 'comment';\n  }\n}\n\nfunction chompMultiString(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '\"' && source.eat('\"') && source.eat('\"'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nfunction chompSingleString(source, setState)\n{\n  while (source.skipTo('\\\\\"')) { source.next(); source.next(); }\n  if (source.skipTo('\"'))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompChar(source, setState)\n{\n  while (source.skipTo(\"\\\\'\")) { source.next(); source.next(); }\n  if (source.skipTo(\"'\"))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompGlsl(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '|' && source.eat(']'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nvar wellKnownWords = {\n  case: 1,\n  of: 1,\n  as: 1,\n  if: 1,\n  then: 1,\n  else: 1,\n  let: 1,\n    in: 1,\n  type: 1,\n  alias: 1,\n  module: 1,\n  where: 1,\n  import: 1,\n  exposing: 1,\n  port: 1\n};\n\nexport const elm = {\n  name: \"elm\",\n  startState: function ()  { return { f: normal() }; },\n  copyState:  function (s) { return { f: s.f }; },\n\n  token: function(stream, state) {\n    var type = state.f(stream, function(s) { state.f = s; });\n    var word = stream.current();\n    return (wellKnownWords.hasOwnProperty(word)) ? 'keyword' : type;\n  },\n\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,YAAY,QAAQ,UAAU,GACvC;AACE,WAAS,CAAC;AACV,SAAO,EAAE,QAAQ,QAAQ;AAC3B;AAEA,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AAEd,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,WAAW;AAEf,SAAS,SACT;AACE,SAAO,SAAS,QAAQ,UACxB;AACE,QAAI,OAAO,SAAS,QAAQ,GAC5B;AACE,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,OAAO,KAAK;AAEvB,QAAI,UAAU,KAAK,IAAI,GACvB;AACE,aAAQ,SAAS,OAAO,OAAO,IAAI,GAAG,IAClC,YAAY,QAAQ,UAAU,kBAAkB,CAAC,CAAC,IACjD,SAAS,OAAO,OAAO,MAAM,OAAO,IACrC,YAAY,QAAQ,UAAU,SAAS,IACvC;AAAA,IACN;AAEA,QAAI,SAAS,KACb;AACE,aAAO,YAAY,QAAQ,UAAU,SAAS;AAAA,IAChD;AAEA,QAAI,SAAS,KACb;AACE,aAAO,OAAO,IAAI,GAAG,IACjB,OAAO,IAAI,GAAG,IACd,YAAY,QAAQ,UAAU,gBAAgB,IAC9C,WACF,YAAY,QAAQ,UAAU,iBAAiB;AAAA,IACnD;AAEA,QAAI,QAAQ,KAAK,IAAI,GACrB;AACE,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,KAAK,IAAI,GACrB;AACE,UAAI,QAAQ,OAAO,QAAQ;AAC3B,aAAO,SAAS,OAAO;AACvB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAEA,QAAI,QAAQ,KAAK,IAAI,GACrB;AACE,UAAI,SAAS,KACb;AACE,YAAI,OAAO,IAAI,MAAM,GACrB;AACE,iBAAO,SAAS,KAAK;AACrB,iBAAO;AAAA,QACT;AAAA,MACF,OAEA;AACE,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,UAAI,OAAO,IAAI,GAAG,GAClB;AACE,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,UAAI,OAAO,IAAI,MAAM,GACrB;AACE,eAAO,IAAI,MAAM;AACjB,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,KAAK,IAAI,GACtB;AACE,UAAI,SAAS,OAAO,OAAO,IAAI,GAAG,GAClC;AACE,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AACA,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,KACb;AACE,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,MAC3B;AACE,MAAI,QAAQ,GACZ;AACE,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,SAAS,QAAQ,UACxB;AACE,WAAO,CAAC,OAAO,IAAI,GACnB;AACE,UAAI,OAAO,OAAO,KAAK;AACvB,UAAI,QAAQ,OAAO,OAAO,IAAI,GAAG,GACjC;AACE,UAAE;AAAA,MACJ,WACS,QAAQ,OAAO,OAAO,IAAI,GAAG,GACtC;AACE,UAAE;AACF,YAAI,SAAS,GACb;AACE,mBAAS,OAAO,CAAC;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,aAAS,kBAAkB,IAAI,CAAC;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,QAAQ,UAClC;AACE,SAAO,CAAC,OAAO,IAAI,GACnB;AACE,QAAI,OAAO,OAAO,KAAK;AACvB,QAAI,SAAS,OAAO,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,GACrD;AACE,eAAS,OAAO,CAAC;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,QAAQ,UACnC;AACE,SAAO,OAAO,OAAO,KAAK,GAAG;AAAE,WAAO,KAAK;AAAG,WAAO,KAAK;AAAA,EAAG;AAC7D,MAAI,OAAO,OAAO,GAAG,GACrB;AACE,WAAO,KAAK;AACZ,aAAS,OAAO,CAAC;AACjB,WAAO;AAAA,EACT;AACA,SAAO,UAAU;AACjB,WAAS,OAAO,CAAC;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,UAC3B;AACE,SAAO,OAAO,OAAO,KAAK,GAAG;AAAE,WAAO,KAAK;AAAG,WAAO,KAAK;AAAA,EAAG;AAC7D,MAAI,OAAO,OAAO,GAAG,GACrB;AACE,WAAO,KAAK;AACZ,aAAS,OAAO,CAAC;AACjB,WAAO;AAAA,EACT;AACA,SAAO,UAAU;AACjB,WAAS,OAAO,CAAC;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,UAC3B;AACE,SAAO,CAAC,OAAO,IAAI,GACnB;AACE,QAAI,OAAO,OAAO,KAAK;AACvB,QAAI,SAAS,OAAO,OAAO,IAAI,GAAG,GAClC;AACE,eAAS,OAAO,CAAC;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACH,IAAI;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AACR;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,WAAa;AAAE,WAAO,EAAE,GAAG,OAAO,EAAE;AAAA,EAAG;AAAA,EACnD,WAAY,SAAU,GAAG;AAAE,WAAO,EAAE,GAAG,EAAE,EAAE;AAAA,EAAG;AAAA,EAE9C,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,MAAM,EAAE,QAAQ,SAAS,GAAG;AAAE,YAAM,IAAI;AAAA,IAAG,CAAC;AACvD,QAAI,OAAO,OAAO,QAAQ;AAC1B,WAAQ,eAAe,eAAe,IAAI,IAAK,YAAY;AAAA,EAC7D;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,KAAI;AAAA,EAC5B;AACF;", "names": []}