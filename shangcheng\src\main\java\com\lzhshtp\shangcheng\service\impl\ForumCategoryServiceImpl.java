package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ForumCategoryDTO;
import com.lzhshtp.shangcheng.dto.ForumCategoryRequest;
import com.lzhshtp.shangcheng.mapper.ForumCategoryMapper;
import com.lzhshtp.shangcheng.mapper.ForumPostMapper;
import com.lzhshtp.shangcheng.model.ForumCategory;
import com.lzhshtp.shangcheng.model.ForumPost;
import com.lzhshtp.shangcheng.service.ForumCategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 论坛分类服务实现类
 */
@Service
public class ForumCategoryServiceImpl implements ForumCategoryService {

    @Autowired
    private ForumCategoryMapper forumCategoryMapper;
    
    @Autowired
    private ForumPostMapper forumPostMapper;

    @Override
    public List<ForumCategoryDTO> getAllCategories() {
        List<ForumCategory> categories = forumCategoryMapper.findAllCategories();
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ForumCategoryDTO getCategoryById(Integer id) {
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            return null;
        }
        return convertToDTO(category);
    }

    @Override
    @Transactional
    public ApiResponse<ForumCategoryDTO> createCategory(ForumCategoryRequest request) {
        // 检查分类名称是否已存在
        ForumCategory existingCategory = forumCategoryMapper.findByCategoryName(request.getCategoryName());
        if (existingCategory != null) {
            return ApiResponse.fail("分类名称已存在", 400);
        }

        ForumCategory category = new ForumCategory();
        category.setCategoryName(request.getCategoryName());
        category.setDescription(request.getDescription());

        forumCategoryMapper.insert(category);

        return ApiResponse.success(convertToDTO(category));
    }

    @Override
    @Transactional
    public ApiResponse<ForumCategoryDTO> updateCategory(Integer id, ForumCategoryRequest request) {
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            return ApiResponse.fail("论坛分类不存在", 404);
        }

        // 检查分类名称是否已被其他分类使用
        ForumCategory existingCategory = forumCategoryMapper.findByCategoryName(request.getCategoryName());
        if (existingCategory != null && !existingCategory.getForumCategoryId().equals(id)) {
            return ApiResponse.fail("分类名称已存在", 400);
        }

        category.setCategoryName(request.getCategoryName());
        category.setDescription(request.getDescription());

        forumCategoryMapper.updateById(category);

        return ApiResponse.success(convertToDTO(category));
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteCategory(Integer id) {
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            return ApiResponse.fail("论坛分类不存在", 404);
        }

        // 检查是否有论坛帖子属于该分类，如果有则不能删除
        LambdaQueryWrapper<ForumPost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForumPost::getForumCategoryId, id);
        // 只需要检查是否存在，所以只查询数量
        Long count = forumPostMapper.selectCount(queryWrapper);
        
        if (count > 0) {
            return ApiResponse.fail("该分类下存在帖子，无法删除", 400);
        }
        
        forumCategoryMapper.deleteById(id);

        return ApiResponse.success(null);
    }

    /**
     * 将实体对象转换为DTO对象
     */
    private ForumCategoryDTO convertToDTO(ForumCategory category) {
        ForumCategoryDTO dto = new ForumCategoryDTO();
        BeanUtils.copyProperties(category, dto);
        return dto;
    }
} 