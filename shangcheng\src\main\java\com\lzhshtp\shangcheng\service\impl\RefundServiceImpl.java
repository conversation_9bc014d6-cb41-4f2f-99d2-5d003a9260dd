package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.RefundRequestDTO;
import com.lzhshtp.shangcheng.dto.RefundResponseDTO;
import com.lzhshtp.shangcheng.mapper.*;
import com.lzhshtp.shangcheng.model.*;
import com.lzhshtp.shangcheng.service.RefundService;
import com.lzhshtp.shangcheng.utils.OssUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * 退款服务实现类
 */
@Service
public class RefundServiceImpl implements RefundService {
    
    private static final Logger logger = LoggerFactory.getLogger(RefundServiceImpl.class);
    
    @Autowired
    private RefundRequestMapper refundRequestMapper;
    
    @Autowired
    private AdminForcedRefundMapper adminForcedRefundMapper;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    /**
     * 创建退款申请
     */
    @Override
    @Transactional
    public Long createRefundRequest(Long userId, RefundRequestDTO request) {
        try {
            // 查询订单
            Order order = orderMapper.selectById(request.getOrderId());
            if (order == null) {
                throw new IllegalArgumentException("订单不存在");
            }
            
            // 验证订单归属
            if (!order.getBuyerId().equals(userId)) {
                throw new IllegalArgumentException("无权申请退款此订单");
            }
            
            // 检查是否已有退款申请
            RefundRequest existingRefund = refundRequestMapper.selectByOrderId(request.getOrderId());
            if (existingRefund != null) {
                throw new IllegalArgumentException("该订单已有退款申请");
            }
            
            // 检查订单状态，如果未发货则自动退款
            if (Order.OrderStatus.PAID.equals(order.getStatus())) {
                return processAutoRefund(order, request);
            } else {
                return createRefundRequestRecord(order, request);
            }
        } catch (Exception e) {
            logger.error("创建退款申请失败", e);
            throw e;
        }
    }
    
    /**
     * 处理自动退款（未发货订单）
     */
    private Long processAutoRefund(Order order, RefundRequestDTO request) {
        // 创建退款记录
        RefundRequest refundRequest = RefundRequest.builder()
                .orderId(order.getOrderId())
                .buyerId(order.getBuyerId())
                .sellerId(order.getSellerId())
                .refundAmount(request.getRefundAmount())
                .refundReason(request.getRefundReason())
                .refundType(RefundRequest.RefundType.REFUND_ONLY)
                .status(RefundRequest.RefundStatus.COMPLETED)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();
        
        refundRequestMapper.insert(refundRequest);
        
        // 执行退款
        processRefund(order, request.getRefundAmount());
        
        logger.info("自动退款完成，订单ID：{}，金额：{}", order.getOrderId(), request.getRefundAmount());
        return refundRequest.getRefundId();
    }
    
    /**
     * 创建退款申请记录（需要商家审核）
     */
    private Long createRefundRequestRecord(Order order, RefundRequestDTO request) {
        RefundRequest refundRequest = RefundRequest.builder()
                .orderId(order.getOrderId())
                .buyerId(order.getBuyerId())
                .sellerId(order.getSellerId())
                .refundAmount(request.getRefundAmount())
                .refundReason(request.getRefundReason())
                .refundType(request.getRefundType())
                .status(RefundRequest.RefundStatus.PENDING_SELLER)
                .evidenceUrls(String.join(",", request.getEvidenceUrls() != null ? request.getEvidenceUrls() : new ArrayList<>()))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();
        
        refundRequestMapper.insert(refundRequest);
        
        logger.info("退款申请创建成功，订单ID：{}，等待商家处理", order.getOrderId());
        return refundRequest.getRefundId();
    }
    
    /**
     * 商家处理退款申请
     */
    @Override
    @Transactional
    public boolean handleSellerResponse(Long userId, RefundResponseDTO response) {
        try {
            RefundRequest refundRequest = refundRequestMapper.selectById(response.getRefundId());
            if (refundRequest == null) {
                throw new IllegalArgumentException("退款申请不存在");
            }
            
            // 验证权限
            if (!refundRequest.getSellerId().equals(userId)) {
                throw new IllegalArgumentException("无权处理此退款申请");
            }
            
            // 验证状态
            if (!RefundRequest.RefundStatus.PENDING_SELLER.equals(refundRequest.getStatus())) {
                throw new IllegalArgumentException("退款申请状态不正确");
            }
            
            // 更新退款申请
            refundRequest.setSellerResponse(response.getResponse());
            refundRequest.setUpdatedTime(LocalDateTime.now());
            
            if (response.getApproved()) {
                // 商家同意退款
                refundRequest.setStatus(RefundRequest.RefundStatus.SELLER_APPROVED);
                refundRequestMapper.updateById(refundRequest);
                
                // 执行退款
                Order order = orderMapper.selectById(refundRequest.getOrderId());
                processRefund(order, refundRequest.getRefundAmount());
                
                // 更新为已完成
                refundRequest.setStatus(RefundRequest.RefundStatus.COMPLETED);
                refundRequestMapper.updateById(refundRequest);
                
                logger.info("商家同意退款，订单ID：{}，金额：{}", order.getOrderId(), refundRequest.getRefundAmount());
            } else {
                // 商家拒绝退款
                refundRequest.setStatus(RefundRequest.RefundStatus.SELLER_REJECTED);
                refundRequestMapper.updateById(refundRequest);
                
                logger.info("商家拒绝退款，订单ID：{}", refundRequest.getOrderId());
            }
            
            return true;
        } catch (Exception e) {
            logger.error("商家处理退款申请失败", e);
            throw e;
        }
    }
    
    /**
     * 执行退款操作
     */
    private void processRefund(Order order, BigDecimal refundAmount) {
        // 查询买家和卖家
        User buyer = userMapper.selectById(order.getBuyerId());
        User seller = userMapper.selectById(order.getSellerId());
        
        // 扣除卖家余额
        BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
        BigDecimal newSellerBalance = sellerBalance.subtract(refundAmount);
        seller.setBalance(newSellerBalance);
        userMapper.updateById(seller);
        
        // 增加买家余额
        BigDecimal buyerBalance = buyer.getBalance() != null ? buyer.getBalance() : BigDecimal.ZERO;
        BigDecimal newBuyerBalance = buyerBalance.add(refundAmount);
        buyer.setBalance(newBuyerBalance);
        userMapper.updateById(buyer);
        
        // 更新订单状态
        order.setStatus(Order.OrderStatus.REFUNDED);
        orderMapper.updateById(order);
        
        logger.info("退款执行完成，买家余额：{} -> {}，卖家余额：{} -> {}", 
                buyerBalance, newBuyerBalance, sellerBalance, newSellerBalance);
    }
    
    /**
     * 申请管理员介入
     */
    @Override
    public boolean requestAdminIntervention(Long userId, Long refundId, String evidence) {
        try {
            RefundRequest refundRequest = refundRequestMapper.selectById(refundId);
            if (refundRequest == null) {
                throw new IllegalArgumentException("退款申请不存在");
            }
            
            // 验证权限
            if (!refundRequest.getBuyerId().equals(userId)) {
                throw new IllegalArgumentException("无权申请管理员介入");
            }
            
            // 验证状态
            if (!RefundRequest.RefundStatus.SELLER_REJECTED.equals(refundRequest.getStatus())) {
                throw new IllegalArgumentException("只有被商家拒绝的退款申请才能申请管理员介入");
            }
            
            // 更新状态
            refundRequest.setStatus(RefundRequest.RefundStatus.PENDING_ADMIN);
            refundRequest.setEvidenceUrls(evidence);
            refundRequest.setUpdatedTime(LocalDateTime.now());
            refundRequestMapper.updateById(refundRequest);
            
            logger.info("申请管理员介入成功，退款申请ID：{}", refundId);
            return true;
        } catch (Exception e) {
            logger.error("申请管理员介入失败", e);
            throw e;
        }
    }

    /**
     * 申请管理员介入（支持多文件上传）
     */
    @Override
    public boolean requestAdminIntervention(Long userId, Long refundId, String evidence, MultipartFile[] files) {
        try {
            RefundRequest refundRequest = refundRequestMapper.selectById(refundId);
            if (refundRequest == null) {
                throw new IllegalArgumentException("退款申请不存在");
            }

            // 验证权限
            if (!refundRequest.getBuyerId().equals(userId)) {
                throw new IllegalArgumentException("无权申请管理员介入");
            }

            // 验证状态
            if (!RefundRequest.RefundStatus.SELLER_REJECTED.equals(refundRequest.getStatus())) {
                throw new IllegalArgumentException("只有被商家拒绝的退款申请才能申请管理员介入");
            }

            // 处理文件上传
            List<String> fileUrls = new ArrayList<>();
            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        try {
                            String fileUrl = ossUtil.uploadFile(file, "refund-evidence");
                            fileUrls.add(fileUrl);
                            logger.info("退款证据文件上传成功，URL: {}", fileUrl);
                        } catch (Exception e) {
                            logger.error("退款证据文件上传失败", e);
                            throw new RuntimeException("文件上传失败: " + e.getMessage());
                        }
                    }
                }
            }

            // 构建证据信息
            Map<String, Object> evidenceData = new HashMap<>();
            if (evidence != null && !evidence.trim().isEmpty()) {
                evidenceData.put("description", evidence);
            }
            if (!fileUrls.isEmpty()) {
                evidenceData.put("files", fileUrls);
            }

            // 转换为JSON字符串
            String evidenceJson;
            try {
                evidenceJson = objectMapper.writeValueAsString(evidenceData);
            } catch (Exception e) {
                logger.error("转换证据数据为JSON失败", e);
                throw new RuntimeException("证据数据格式转换失败");
            }

            // 更新状态
            refundRequest.setStatus(RefundRequest.RefundStatus.PENDING_ADMIN);
            refundRequest.setEvidenceUrls(evidenceJson);
            refundRequest.setUpdatedTime(LocalDateTime.now());
            refundRequestMapper.updateById(refundRequest);

            logger.info("申请管理员介入成功，退款申请ID：{}，上传文件数：{}", refundId, fileUrls.size());
            return true;
        } catch (Exception e) {
            logger.error("申请管理员介入失败", e);
            throw e;
        }
    }
    
    /**
     * 管理员处理退款申请
     */
    @Override
    @Transactional
    public boolean handleAdminResponse(Long adminId, RefundResponseDTO response) {
        try {
            RefundRequest refundRequest = refundRequestMapper.selectById(response.getRefundId());
            if (refundRequest == null) {
                throw new IllegalArgumentException("退款申请不存在");
            }
            
            // 验证状态
            if (!RefundRequest.RefundStatus.PENDING_ADMIN.equals(refundRequest.getStatus())) {
                throw new IllegalArgumentException("退款申请状态不正确");
            }
            
            // 更新退款申请
            refundRequest.setAdminResponse(response.getResponse());
            refundRequest.setUpdatedTime(LocalDateTime.now());
            
            if (response.getApproved()) {
                // 管理员同意强制退款
                refundRequest.setStatus(RefundRequest.RefundStatus.ADMIN_APPROVED);
                refundRequestMapper.updateById(refundRequest);
                
                // 检查卖家余额是否充足
                User seller = userMapper.selectById(refundRequest.getSellerId());
                BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
                
                if (sellerBalance.compareTo(refundRequest.getRefundAmount()) >= 0) {
                    // 余额充足，直接退款
                    Order order = orderMapper.selectById(refundRequest.getOrderId());
                    processRefund(order, refundRequest.getRefundAmount());
                    
                    refundRequest.setStatus(RefundRequest.RefundStatus.COMPLETED);
                    refundRequestMapper.updateById(refundRequest);
                    
                    logger.info("管理员强制退款完成，订单ID：{}", order.getOrderId());
                } else {
                    // 余额不足，创建强制退款任务
                    createForcedRefundTask(refundRequest, adminId);
                    
                    // 禁止卖家上架商品
                    seller.setCanPublishProduct(false);
                    userMapper.updateById(seller);
                    
                    logger.info("卖家余额不足，创建强制退款任务，卖家ID：{}", seller.getUserId());
                }
            } else {
                // 管理员拒绝退款
                refundRequest.setStatus(RefundRequest.RefundStatus.ADMIN_REJECTED);
                refundRequestMapper.updateById(refundRequest);
                
                logger.info("管理员拒绝退款申请，退款申请ID：{}", response.getRefundId());
            }
            
            return true;
        } catch (Exception e) {
            logger.error("管理员处理退款申请失败", e);
            throw e;
        }
    }
    
    /**
     * 创建强制退款任务
     */
    private void createForcedRefundTask(RefundRequest refundRequest, Long adminId) {
        AdminForcedRefund forcedRefund = AdminForcedRefund.builder()
                .refundRequestId(refundRequest.getRefundId())
                .sellerId(refundRequest.getSellerId())
                .requiredAmount(refundRequest.getRefundAmount())
                .status(AdminForcedRefund.ForcedRefundStatus.PENDING)
                .adminId(adminId)
                .adminNote("管理员强制退款，卖家余额不足")
                .createdTime(LocalDateTime.now())
                .build();
        
        adminForcedRefundMapper.insert(forcedRefund);
    }
    
    /**
     * 处理强制退款任务
     */
    @Override
    @Transactional
    public boolean processForcedRefund(Long sellerId, Long forcedRefundId) {
        try {
            AdminForcedRefund forcedRefund = adminForcedRefundMapper.selectById(forcedRefundId);
            if (forcedRefund == null) {
                throw new IllegalArgumentException("强制退款任务不存在");
            }
            
            // 验证权限
            if (!forcedRefund.getSellerId().equals(sellerId)) {
                throw new IllegalArgumentException("无权处理此强制退款任务");
            }
            
            // 验证状态
            if (!AdminForcedRefund.ForcedRefundStatus.PENDING.equals(forcedRefund.getStatus())) {
                throw new IllegalArgumentException("强制退款任务状态不正确");
            }
            
            // 检查卖家余额
            User seller = userMapper.selectById(sellerId);
            BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
            
            if (sellerBalance.compareTo(forcedRefund.getRequiredAmount()) < 0) {
                throw new IllegalArgumentException("余额不足，无法完成强制退款任务");
            }
            
            // 执行退款
            RefundRequest refundRequest = refundRequestMapper.selectById(forcedRefund.getRefundRequestId());
            Order order = orderMapper.selectById(refundRequest.getOrderId());
            processRefund(order, forcedRefund.getRequiredAmount());
            
            // 更新强制退款任务状态
            forcedRefund.setStatus(AdminForcedRefund.ForcedRefundStatus.COMPLETED);
            forcedRefund.setCompletedTime(LocalDateTime.now());
            adminForcedRefundMapper.updateById(forcedRefund);
            
            // 更新退款申请状态
            refundRequest.setStatus(RefundRequest.RefundStatus.COMPLETED);
            refundRequestMapper.updateById(refundRequest);
            
            // 恢复卖家上架权限
            seller.setCanPublishProduct(true);
            userMapper.updateById(seller);
            
            logger.info("强制退款任务完成，卖家ID：{}，金额：{}", sellerId, forcedRefund.getRequiredAmount());
            return true;
        } catch (Exception e) {
            logger.error("处理强制退款任务失败", e);
            throw e;
        }
    }
    
    /**
     * 查询退款申请列表
     */
    @Override
    public List<RefundRequestDTO> getRefundRequests(Long userId, String userType) {
        List<RefundRequest> refundRequests;
        
        switch (userType) {
            case "buyer":
                refundRequests = refundRequestMapper.selectByBuyerId(userId);
                break;
            case "seller":
                refundRequests = refundRequestMapper.selectBySellerId(userId);
                break;
            case "admin":
                refundRequests = refundRequestMapper.selectPendingAdminRefunds();
                break;
            default:
                throw new IllegalArgumentException("无效的用户类型");
        }
        
        return convertToDTO(refundRequests);
    }
    
    /**
     * 查询强制退款任务列表
     */
    @Override
    public List<RefundRequestDTO> getForcedRefundTasks(Long sellerId) {
        List<AdminForcedRefund> forcedRefunds = adminForcedRefundMapper.selectPendingBySellerId(sellerId);
        List<RefundRequestDTO> result = new ArrayList<>();
        
        for (AdminForcedRefund forcedRefund : forcedRefunds) {
            RefundRequest refundRequest = refundRequestMapper.selectById(forcedRefund.getRefundRequestId());
            if (refundRequest != null) {
                RefundRequestDTO dto = convertToDTO(refundRequest);
                if (dto != null) {
                    // 设置强制退款任务的特殊字段
                    dto.setForcedRefundId(forcedRefund.getForcedRefundId());
                    dto.setAdminResponse("强制退款任务，需要充值 ¥" + forcedRefund.getRequiredAmount());
                    result.add(dto);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 检查卖家是否有待处理的强制退款任务
     */
    @Override
    public boolean hasPendingForcedRefunds(Long sellerId) {
        return adminForcedRefundMapper.countPendingBySellerId(sellerId) > 0;
    }
    
    /**
     * 转换为DTO
     */
    private List<RefundRequestDTO> convertToDTO(List<RefundRequest> refundRequests) {
        List<RefundRequestDTO> result = new ArrayList<>();

        if (refundRequests != null) {
            for (RefundRequest refundRequest : refundRequests) {
                if (refundRequest != null) {
                    try {
                        RefundRequestDTO dto = convertToDTO(refundRequest);
                        if (dto != null) {  // 只添加非null的DTO
                            result.add(dto);
                        }
                    } catch (Exception e) {
                        logger.error("转换退款申请DTO失败，退款申请ID: {}", refundRequest.getRefundId(), e);
                        // 跳过有问题的记录，继续处理其他记录
                    }
                }
            }
        }

        return result;
    }
    
    private RefundRequestDTO convertToDTO(RefundRequest refundRequest) {
        if (refundRequest == null) {
            logger.warn("退款申请对象为空，无法转换为DTO");
            return null;
        }

        try {
            // 查询关联信息
            Order order = orderMapper.selectById(refundRequest.getOrderId());
            if (order == null) {
                logger.warn("订单不存在，订单ID: {}", refundRequest.getOrderId());
                return null;
            }

            Product product = productMapper.selectById(order.getProductId());
            User buyer = userMapper.selectById(refundRequest.getBuyerId());
            User seller = userMapper.selectById(refundRequest.getSellerId());

            return RefundRequestDTO.builder()
                    .refundId(refundRequest.getRefundId())
                    .orderId(refundRequest.getOrderId())
                    .refundAmount(refundRequest.getRefundAmount())
                    .refundReason(refundRequest.getRefundReason())
                    .refundType(refundRequest.getRefundType())
                    .status(refundRequest.getStatus())
                    .sellerResponse(refundRequest.getSellerResponse())
                    .adminResponse(refundRequest.getAdminResponse())
                    .evidenceUrls(parseEvidenceUrls(refundRequest.getEvidenceUrls()))
                    .createdTime(refundRequest.getCreatedTime())
                    .updatedTime(refundRequest.getUpdatedTime())
                    .buyerName(buyer != null ? buyer.getUsername() : "未知用户")
                    .sellerName(seller != null ? seller.getUsername() : "未知用户")
                    .productTitle(product != null ? product.getTitle() : "未知商品")
                    .orderStatus(order.getStatus())
                    .build();
        } catch (Exception e) {
            logger.error("转换退款申请DTO失败，退款申请ID: {}", refundRequest.getRefundId(), e);
            return null;
        }
    }

    /**
     * 管理员查询退款申请列表（只显示需要管理员处理的）
     */
    @Override
    public List<RefundRequestDTO> getAdminRefundList(int page, int pageSize, String status, String keyword) {
        try {
            // 构建查询条件 - 管理员只处理需要介入的退款申请
            List<RefundRequest> refundRequests;

            if (status != null && !status.trim().isEmpty()) {
                // 按状态筛选，但仍然限制在管理员相关状态
                if ("pending_admin".equals(status) || "completed".equals(status) || "rejected".equals(status)) {
                    refundRequests = refundRequestMapper.selectList(
                        new QueryWrapper<RefundRequest>()
                            .eq("lzhshtp_status", status)
                            .orderByDesc("lzhshtp_created_time")
                    );
                } else {
                    // 如果状态不是管理员相关的，返回空列表
                    return new ArrayList<>();
                }
            } else {
                // 默认只查询需要管理员处理的退款申请
                refundRequests = refundRequestMapper.selectList(
                    new QueryWrapper<RefundRequest>()
                        .in("lzhshtp_status", "pending_admin", "completed", "rejected")
                        .orderByDesc("lzhshtp_created_time")
                );
            }

            // 如果有关键词搜索，进一步过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                // 这里可以根据需要添加关键词搜索逻辑
                // 比如搜索商品标题、买家名称等
            }

            return convertToDTO(refundRequests);
        } catch (Exception e) {
            logger.error("管理员查询退款申请列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 管理员查询退款统计数据
     */
    @Override
    public Object getAdminRefundStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总退款申请数
            long totalCount = refundRequestMapper.selectCount(null);
            statistics.put("totalCount", totalCount);

            // 待处理申请数
            long pendingCount = refundRequestMapper.selectCount(
                new QueryWrapper<RefundRequest>()
                    .eq("lzhshtp_status", "pending_admin")
            );
            statistics.put("pendingCount", pendingCount);

            // 已完成申请数
            long completedCount = refundRequestMapper.selectCount(
                new QueryWrapper<RefundRequest>()
                    .eq("lzhshtp_status", "completed")
            );
            statistics.put("completedCount", completedCount);

            // 强制退款任务数
            long forcedTaskCount = adminForcedRefundMapper.selectCount(
                new QueryWrapper<AdminForcedRefund>()
                    .eq("lzhshtp_status", "pending")
            );
            statistics.put("forcedTaskCount", forcedTaskCount);

            // 总退款金额（已完成的）
            List<RefundRequest> completedRefunds = refundRequestMapper.selectList(
                new QueryWrapper<RefundRequest>()
                    .eq("lzhshtp_status", "completed")
            );
            double totalAmount = completedRefunds.stream()
                .mapToDouble(refund -> refund.getRefundAmount().doubleValue())
                .sum();
            statistics.put("totalAmount", totalAmount);

            return statistics;
        } catch (Exception e) {
            logger.error("管理员查询退款统计数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 解析证据URLs
     * 支持旧格式（逗号分隔）和新格式（JSON）
     */
    private List<String> parseEvidenceUrls(String evidenceUrls) {
        if (evidenceUrls == null || evidenceUrls.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 尝试解析为JSON格式
            if (evidenceUrls.trim().startsWith("{")) {
                Map<String, Object> evidenceData = objectMapper.readValue(evidenceUrls, Map.class);
                Object filesObj = evidenceData.get("files");
                if (filesObj instanceof List) {
                    return (List<String>) filesObj;
                }
                return new ArrayList<>();
            } else {
                // 兼容旧格式（逗号分隔）
                return List.of(evidenceUrls.split(","));
            }
        } catch (Exception e) {
            logger.error("解析证据URLs失败: {}", evidenceUrls, e);
            // 如果JSON解析失败，尝试按逗号分隔
            try {
                return List.of(evidenceUrls.split(","));
            } catch (Exception ex) {
                logger.error("按逗号分隔解析证据URLs也失败: {}", evidenceUrls, ex);
                return new ArrayList<>();
            }
        }
    }
}
