{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.doneIndentState.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "mappings": ";AAAO,SAAS,WAAW,QAAQ;AACjC,cAAY,QAAQ,OAAO;AAC3B,MAAI,UAAU,CAAC,GAAG,OAAO,OAAO,gBAAgB,CAAC,GAAG,iBAAiB;AACrE,WAAS,SAAS;AAAQ,QAAI,SAAS,QAAQ,OAAO,eAAe,KAAK,GAAG;AAC3E,UAAI,OAAO,QAAQ,KAAK,IAAI,CAAC,GAAG,OAAO,OAAO,KAAK;AACnD,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,OAAO,KAAK,CAAC;AACjB,aAAK,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AAChC,YAAI,KAAK,UAAU,KAAK;AAAQ,2BAAiB;AAAA,MACnD;AAAA,IACF;AACA,SAAO;AAAA,IACL,MAAM,KAAK;AAAA,IACX,YAAY,WAAW;AACrB,aAAO,EAAC,OAAO,SAAS,SAAS,MAAM,QAAQ,iBAAiB,CAAC,IAAI,KAAI;AAAA,IAC3E;AAAA,IACA,WAAW,SAASA,QAAO;AACzB,UAAI,IAAI,EAAC,OAAOA,OAAM,OAAO,SAASA,OAAM,SAAS,QAAQA,OAAM,UAAUA,OAAM,OAAO,MAAM,CAAC,EAAC;AAClG,UAAIA,OAAM;AACR,UAAE,QAAQA,OAAM,MAAM,MAAM,CAAC;AAC/B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,cAAc,OAAO;AAAA,IAC5B,QAAQ,eAAe,SAAS,IAAI;AAAA,IACpC,cAAc;AAAA,EAChB;AACF;AAEA,SAAS,YAAY,QAAQ,MAAM;AACjC,MAAI,CAAC,OAAO,eAAe,IAAI;AAC7B,UAAM,IAAI,MAAM,qBAAqB,OAAO,iBAAiB;AACjE;AAEA,SAAS,QAAQ,KAAK,OAAO;AAC3B,MAAI,CAAC;AAAK,WAAO;AACjB,MAAI,QAAQ;AACZ,MAAI,eAAe,QAAQ;AACzB,QAAI,IAAI;AAAY,cAAQ;AAC5B,UAAM,IAAI;AAAA,EACZ,OAAO;AACL,UAAM,OAAO,GAAG;AAAA,EAClB;AACA,SAAO,IAAI,QAAQ,UAAU,QAAQ,KAAK,OAAO,QAAQ,MAAM,KAAK,KAAK;AAC3E;AAEA,SAAS,QAAQ,KAAK;AACpB,MAAI,CAAC;AAAK,WAAO;AACjB,MAAI,IAAI;AAAO,WAAO;AACtB,MAAI,OAAO,OAAO;AAAU,WAAO,IAAI,QAAQ,OAAO,GAAG;AACzD,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC9B,WAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAG,CAAC;AAClD,SAAO;AACT;AAEA,SAAS,KAAK,MAAM,QAAQ;AAC1B,MAAI,KAAK,QAAQ,KAAK;AAAM,gBAAY,QAAQ,KAAK,QAAQ,KAAK,IAAI;AACtE,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,OAAK,OAAO;AACd;AAEA,SAAS,cAAc,QAAQ;AAC7B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,SAAS;AACjB,UAAI,OAAO,MAAM,QAAQ,MAAM;AAC/B,UAAI,MAAM,QAAQ,UAAU;AAAG,cAAM,UAAU;AAC/C,aAAO,OAAO,KAAK,KAAK;AACxB,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,WAAW,OAAO,MAAM,KAAK;AACjC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,OAAO,SAAS,CAAC;AACrB,UAAI,WAAW,CAAC,KAAK,KAAK,OAAO,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK,KAAK;AACzE,UAAI,SAAS;AACX,YAAI,KAAK,KAAK,MAAM;AAClB,gBAAM,QAAQ,KAAK,KAAK;AAAA,QAC1B,WAAW,KAAK,KAAK,MAAM;AACzB,WAAC,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK;AACpD,gBAAM,QAAQ,KAAK,KAAK;AAAA,QAC1B,WAAW,KAAK,KAAK,OAAO,MAAM,SAAS,MAAM,MAAM,QAAQ;AAC7D,gBAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,QAChC;AAEA,YAAI,KAAK,KAAK;AACZ,gBAAM,OAAO,KAAK,OAAO,YAAY,IAAI,OAAO,UAAU;AAC5D,YAAI,KAAK,KAAK;AACZ,gBAAM,OAAO,IAAI;AACnB,YAAI,QAAQ,KAAK;AACjB,YAAI,SAAS,MAAM;AAAO,kBAAQ,MAAM,OAAO;AAC/C,YAAI,QAAQ,SAAS,KAAK,KAAK,SAAS,OAAO,KAAK,SAAS,UAAU;AACrE,gBAAM,UAAU,CAAC;AACjB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,gBAAI,QAAQ,CAAC;AACX,oBAAM,QAAQ,KAAK,EAAC,MAAM,QAAQ,CAAC,GAAG,OAAO,KAAK,MAAM,IAAI,CAAC,EAAC,CAAC;AACnE,iBAAO,OAAO,QAAQ,CAAC,EAAE,UAAU,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,SAAS,EAAE;AACtE,iBAAO,MAAM,CAAC;AAAA,QAChB,WAAW,SAAS,MAAM,MAAM;AAC9B,iBAAO,MAAM,CAAC;AAAA,QAChB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,QAAQ,MAAM;AACpC,SAAO,SAAS,OAAO,WAAW;AAChC,QAAI,MAAM,UAAU,QAAQ,KAAK,oBAAoB,KAAK,gBAAgB,QAAQ,MAAM,KAAK,IAAI;AAC/F,aAAO;AAET,QAAI,MAAM,MAAM,OAAO,SAAS,GAAG,QAAQ,OAAO,MAAM,KAAK;AAC7D;AAAM,iBAAS;AACb,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,OAAO,MAAM,CAAC;AAClB,cAAI,KAAK,KAAK,UAAU,KAAK,KAAK,sBAAsB,OAAO;AAC7D,gBAAI,IAAI,KAAK,MAAM,KAAK,SAAS;AACjC,gBAAI,KAAK,EAAE,CAAC,GAAG;AACb;AACA,kBAAI,KAAK,QAAQ,KAAK;AAAM,wBAAQ,OAAO,KAAK,QAAQ,KAAK,IAAI;AACjE,0BAAY,UAAU,MAAM,EAAE,CAAC,EAAE,MAAM;AACvC,uBAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AACA,WAAO,MAAM,IAAI,IAAI,MAAM,OAAO,GAAG;AAAA,EACvC;AACF;", "names": ["state"]}