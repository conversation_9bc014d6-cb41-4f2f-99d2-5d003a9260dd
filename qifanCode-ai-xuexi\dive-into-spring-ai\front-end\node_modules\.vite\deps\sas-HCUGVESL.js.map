{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/sas.js"], "sourcesContent": ["var words = {};\nvar isDoubleOperatorSym = {\n  eq: 'operator',\n  lt: 'operator',\n  le: 'operator',\n  gt: 'operator',\n  ge: 'operator',\n  \"in\": 'operator',\n  ne: 'operator',\n  or: 'operator'\n};\nvar isDoubleOperatorChar = /(<=|>=|!=|<>)/;\nvar isSingleOperatorChar = /[=\\(:\\),{}.*<>+\\-\\/^\\[\\]]/;\n\n// Takes a string of words separated by spaces and adds them as\n// keys with the value of the first argument 'style'\nfunction define(style, string, context) {\n  if (context) {\n    var split = string.split(' ');\n    for (var i = 0; i < split.length; i++) {\n      words[split[i]] = {style: style, state: context};\n    }\n  }\n}\n//datastep\ndefine('def', 'stack pgm view source debug nesting nolist', ['inDataStep']);\ndefine('def', 'if while until for do do; end end; then else cancel', ['inDataStep']);\ndefine('def', 'label format _n_ _error_', ['inDataStep']);\ndefine('def', 'ALTER BUFNO BUFSIZE CNTLLEV COMPRESS DLDMGACTION ENCRYPT ENCRYPTKEY EXTENDOBSCOUNTER GENMAX GENNUM INDEX LABEL OBSBUF OUTREP PW PWREQ READ REPEMPTY REPLACE REUSE ROLE SORTEDBY SPILL TOBSNO TYPE WRITE FILECLOSE FIRSTOBS IN OBS POINTOBS WHERE WHEREUP IDXNAME IDXWHERE DROP KEEP RENAME', ['inDataStep']);\ndefine('def', 'filevar finfo finv fipname fipnamel fipstate first firstobs floor', ['inDataStep']);\ndefine('def', 'varfmt varinfmt varlabel varlen varname varnum varray varrayx vartype verify vformat vformatd vformatdx vformatn vformatnx vformatw vformatwx vformatx vinarray vinarrayx vinformat vinformatd vinformatdx vinformatn vinformatnx vinformatw vinformatwx vinformatx vlabel vlabelx vlength vlengthx vname vnamex vnferr vtype vtypex weekday', ['inDataStep']);\ndefine('def', 'zipfips zipname zipnamel zipstate', ['inDataStep']);\ndefine('def', 'put putc putn', ['inDataStep']);\ndefine('builtin', 'data run', ['inDataStep']);\n\n\n//proc\ndefine('def', 'data', ['inProc']);\n\n// flow control for macros\ndefine('def', '%if %end %end; %else %else; %do %do; %then', ['inMacro']);\n\n//everywhere\ndefine('builtin', 'proc run; quit; libname filename %macro %mend option options', ['ALL']);\n\ndefine('def', 'footnote title libname ods', ['ALL']);\ndefine('def', '%let %put %global %sysfunc %eval ', ['ALL']);\n// automatic macro variables http://support.sas.com/documentation/cdl/en/mcrolref/61885/HTML/default/viewer.htm#a003167023.htm\ndefine('variable', '&sysbuffr &syscc &syscharwidth &syscmd &sysdate &sysdate9 &sysday &sysdevic &sysdmg &sysdsn &sysencoding &sysenv &syserr &syserrortext &sysfilrc &syshostname &sysindex &sysinfo &sysjobid &syslast &syslckrc &syslibrc &syslogapplname &sysmacroname &sysmenv &sysmsg &sysncpu &sysodspath &sysparm &syspbuff &sysprocessid &sysprocessname &sysprocname &sysrc &sysscp &sysscpl &sysscpl &syssite &sysstartid &sysstartname &systcpiphostname &systime &sysuserid &sysver &sysvlong &sysvlong4 &syswarningtext', ['ALL']);\n\n//footnote[1-9]? title[1-9]?\n\n//options statement\ndefine('def', 'source2 nosource2 page pageno pagesize', ['ALL']);\n\n//proc and datastep\ndefine('def', '_all_ _character_ _cmd_ _freq_ _i_ _infile_ _last_ _msg_ _null_ _numeric_ _temporary_ _type_ abort abs addr adjrsq airy alpha alter altlog altprint and arcos array arsin as atan attrc attrib attrn authserver autoexec awscontrol awsdef awsmenu awsmenumerge awstitle backward band base betainv between blocksize blshift bnot bor brshift bufno bufsize bxor by byerr byline byte calculated call cards cards4 catcache cbufno cdf ceil center cexist change chisq cinv class cleanup close cnonct cntllev coalesce codegen col collate collin column comamid comaux1 comaux2 comdef compbl compound compress config continue convert cos cosh cpuid create cross crosstab css curobs cv daccdb daccdbsl daccsl daccsyd dacctab dairy datalines datalines4 datejul datepart datetime day dbcslang dbcstype dclose ddfm ddm delete delimiter depdb depdbsl depsl depsyd deptab dequote descending descript design= device dflang dhms dif digamma dim dinfo display distinct dkricond dkrocond dlm dnum do dopen doptname doptnum dread drop dropnote dsname dsnferr echo else emaildlg emailid emailpw emailserver emailsys encrypt end endsas engine eof eov erf erfc error errorcheck errors exist exp fappend fclose fcol fdelete feedback fetch fetchobs fexist fget file fileclose fileexist filefmt filename fileref  fmterr fmtsearch fnonct fnote font fontalias  fopen foptname foptnum force formatted formchar formdelim formdlim forward fpoint fpos fput fread frewind frlen from fsep fuzz fwrite gaminv gamma getoption getvarc getvarn go goto group gwindow hbar hbound helpenv helploc hms honorappearance hosthelp hostprint hour hpct html hvar ibessel ibr id if index indexc indexw initcmd initstmt inner input inputc inputn inr insert int intck intnx into intrr invaliddata irr is jbessel join juldate keep kentb kurtosis label lag last lbound leave left length levels lgamma lib  library libref line linesize link list log log10 log2 logpdf logpmf logsdf lostcard lowcase lrecl ls macro macrogen maps mautosource max maxdec maxr mdy mean measures median memtype merge merror min minute missing missover mlogic mod mode model modify month mopen mort mprint mrecall msglevel msymtabmax mvarsize myy n nest netpv new news nmiss no nobatch nobs nocaps nocardimage nocenter nocharcode nocmdmac nocol nocum nodate nodbcs nodetails nodmr nodms nodmsbatch nodup nodupkey noduplicates noechoauto noequals noerrorabend noexitwindows nofullstimer noicon noimplmac noint nolist noloadlist nomiss nomlogic nomprint nomrecall nomsgcase nomstored nomultenvappl nonotes nonumber noobs noovp nopad nopercent noprint noprintinit normal norow norsasuser nosetinit  nosplash nosymbolgen note notes notitle notitles notsorted noverbose noxsync noxwait npv null number numkeys nummousekeys nway obs  on open     order ordinal otherwise out outer outp= output over ovp p(1 5 10 25 50 75 90 95 99) pad pad2  paired parm parmcards path pathdll pathname pdf peek peekc pfkey pmf point poisson poke position printer probbeta probbnml probchi probf probgam probhypr probit probnegb probnorm probsig probt procleave prt ps  pw pwreq qtr quote r ranbin rancau random ranexp rangam range ranks rannor ranpoi rantbl rantri ranuni rcorr read recfm register regr remote remove rename repeat repeated replace resolve retain return reuse reverse rewind right round rsquare rtf rtrace rtraceloc s s2 samploc sasautos sascontrol sasfrscr sasmsg sasmstore sasscript sasuser saving scan sdf second select selection separated seq serror set setcomm setot sign simple sin sinh siteinfo skewness skip sle sls sortedby sortpgm sortseq sortsize soundex  spedis splashlocation split spool sqrt start std stderr stdin stfips stimer stname stnamel stop stopover sub subgroup subpopn substr sum sumwgt symbol symbolgen symget symput sysget sysin sysleave sysmsg sysparm sysprint sysprintfont sysprod sysrc system t table tables tan tanh tapeclose tbufsize terminal test then timepart tinv  tnonct to today tol tooldef totper transformout translate trantab tranwrd trigamma trim trimn trunc truncover type unformatted uniform union until upcase update user usericon uss validate value var  weight when where while wincharset window work workinit workterm write wsum xsync xwait yearcutoff yes yyq  min max', ['inDataStep', 'inProc']);\ndefine('operator', 'and not ', ['inDataStep', 'inProc']);\n\n// Main function\nfunction tokenize(stream, state) {\n  // Finally advance the stream\n  var ch = stream.next();\n\n  // BLOCKCOMMENT\n  if (ch === '/' && stream.eat('*')) {\n    state.continueComment = true;\n    return \"comment\";\n  } else if (state.continueComment === true) { // in comment block\n    //comment ends at the beginning of the line\n    if (ch === '*' && stream.peek() === '/') {\n      stream.next();\n      state.continueComment = false;\n    } else if (stream.skipTo('*')) { //comment is potentially later in line\n      stream.skipTo('*');\n      stream.next();\n      if (stream.eat('/'))\n        state.continueComment = false;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"comment\";\n  }\n\n  if (ch == \"*\" && stream.column() == stream.indentation()) {\n    stream.skipToEnd()\n    return \"comment\"\n  }\n\n  // DoubleOperator match\n  var doubleOperator = ch + stream.peek();\n\n  if ((ch === '\"' || ch === \"'\") && !state.continueString) {\n    state.continueString = ch\n    return \"string\"\n  } else if (state.continueString) {\n    if (state.continueString == ch) {\n      state.continueString = null;\n    } else if (stream.skipTo(state.continueString)) {\n      // quote found on this line\n      stream.next();\n      state.continueString = null;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"string\";\n  } else if (state.continueString !== null && stream.eol()) {\n    stream.skipTo(state.continueString) || stream.skipToEnd();\n    return \"string\";\n  } else if (/[\\d\\.]/.test(ch)) { //find numbers\n    if (ch === \".\")\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    else if (ch === \"0\")\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    else\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    return \"number\";\n  } else if (isDoubleOperatorChar.test(ch + stream.peek())) { // TWO SYMBOL TOKENS\n    stream.next();\n    return \"operator\";\n  } else if (isDoubleOperatorSym.hasOwnProperty(doubleOperator)) {\n    stream.next();\n    if (stream.peek() === ' ')\n      return isDoubleOperatorSym[doubleOperator.toLowerCase()];\n  } else if (isSingleOperatorChar.test(ch)) { // SINGLE SYMBOL TOKENS\n    return \"operator\";\n  }\n\n  // Matches one whole word -- even if the word is a character\n  var word;\n  if (stream.match(/[%&;\\w]+/, false) != null) {\n    word = ch + stream.match(/[%&;\\w]+/, true);\n    if (/&/.test(word)) return 'variable'\n  } else {\n    word = ch;\n  }\n  // the word after DATA PROC or MACRO\n  if (state.nextword) {\n    stream.match(/[\\w]+/);\n    // match memname.libname\n    if (stream.peek() === '.') stream.skipTo(' ');\n    state.nextword = false;\n    return 'variableName.special';\n  }\n\n  word = word.toLowerCase()\n  // Are we in a DATA Step?\n  if (state.inDataStep) {\n    if (word === 'run;' || stream.match(/run\\s;/)) {\n      state.inDataStep = false;\n      return 'builtin';\n    }\n    // variable formats\n    if ((word) && stream.next() === '.') {\n      //either a format or libname.memname\n      if (/\\w/.test(stream.peek())) return 'variableName.special';\n      else return 'variable';\n    }\n    // do we have a DATA Step keyword\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inDataStep\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      //backup to the start of the word\n      if (stream.start < stream.pos)\n        stream.backUp(stream.pos - stream.start);\n      //advance the length of the word and return\n      for (var i = 0; i < word.length; ++i) stream.next();\n      return words[word].style;\n    }\n  }\n  // Are we in an Proc statement?\n  if (state.inProc) {\n    if (word === 'run;' || word === 'quit;') {\n      state.inProc = false;\n      return 'builtin';\n    }\n    // do we have a proc keyword\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inProc\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      stream.match(/[\\w]+/);\n      return words[word].style;\n    }\n  }\n  // Are we in a Macro statement?\n  if (state.inMacro) {\n    if (word === '%mend') {\n      if (stream.peek() === ';') stream.next();\n      state.inMacro = false;\n      return 'builtin';\n    }\n    if (word && words.hasOwnProperty(word) &&\n        (words[word].state.indexOf(\"inMacro\") !== -1 ||\n         words[word].state.indexOf(\"ALL\") !== -1)) {\n      stream.match(/[\\w]+/);\n      return words[word].style;\n    }\n\n    return 'atom';\n  }\n  // Do we have Keywords specific words?\n  if (word && words.hasOwnProperty(word)) {\n    // Negates the initial next()\n    stream.backUp(1);\n    // Actually move the stream\n    stream.match(/[\\w]+/);\n    if (word === 'data' && /=/.test(stream.peek()) === false) {\n      state.inDataStep = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (word === 'proc') {\n      state.inProc = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (word === '%macro') {\n      state.inMacro = true;\n      state.nextword = true;\n      return 'builtin';\n    }\n    if (/title[1-9]/.test(word)) return 'def';\n\n    if (word === 'footnote') {\n      stream.eat(/[1-9]/);\n      return 'def';\n    }\n\n    // Returns their value as state in the prior define methods\n    if (state.inDataStep === true && words[word].state.indexOf(\"inDataStep\") !== -1)\n      return words[word].style;\n    if (state.inProc === true && words[word].state.indexOf(\"inProc\") !== -1)\n      return words[word].style;\n    if (state.inMacro === true && words[word].state.indexOf(\"inMacro\") !== -1)\n      return words[word].style;\n    if (words[word].state.indexOf(\"ALL\") !== -1)\n      return words[word].style;\n    return null;\n  }\n  // Unrecognized syntax\n  return null;\n}\n\nexport const sas = {\n  name: \"sas\",\n  startState: function () {\n    return {\n      inDataStep: false,\n      inProc: false,\n      inMacro: false,\n      nextword: false,\n      continueString: null,\n      continueComment: false\n    };\n  },\n  token: function (stream, state) {\n    // Strip the spaces, but regex will account for them either way\n    if (stream.eatSpace()) return null;\n    // Go through the main process\n    return tokenize(stream, state);\n  },\n\n  languageData: {\n    commentTokens: {block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,QAAQ,CAAC;AACb,IAAI,sBAAsB;AAAA,EACxB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB;AAI3B,SAAS,OAAO,OAAO,QAAQ,SAAS;AACtC,MAAI,SAAS;AACX,QAAI,QAAQ,OAAO,MAAM,GAAG;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,MAAM,CAAC,CAAC,IAAI,EAAC,OAAc,OAAO,QAAO;AAAA,IACjD;AAAA,EACF;AACF;AAEA,OAAO,OAAO,8CAA8C,CAAC,YAAY,CAAC;AAC1E,OAAO,OAAO,uDAAuD,CAAC,YAAY,CAAC;AACnF,OAAO,OAAO,4BAA4B,CAAC,YAAY,CAAC;AACxD,OAAO,OAAO,8RAA8R,CAAC,YAAY,CAAC;AAC1T,OAAO,OAAO,qEAAqE,CAAC,YAAY,CAAC;AACjG,OAAO,OAAO,gVAAgV,CAAC,YAAY,CAAC;AAC5W,OAAO,OAAO,qCAAqC,CAAC,YAAY,CAAC;AACjE,OAAO,OAAO,iBAAiB,CAAC,YAAY,CAAC;AAC7C,OAAO,WAAW,YAAY,CAAC,YAAY,CAAC;AAI5C,OAAO,OAAO,QAAQ,CAAC,QAAQ,CAAC;AAGhC,OAAO,OAAO,8CAA8C,CAAC,SAAS,CAAC;AAGvE,OAAO,WAAW,gEAAgE,CAAC,KAAK,CAAC;AAEzF,OAAO,OAAO,8BAA8B,CAAC,KAAK,CAAC;AACnD,OAAO,OAAO,qCAAqC,CAAC,KAAK,CAAC;AAE1D,OAAO,YAAY,ofAAof,CAAC,KAAK,CAAC;AAK9gB,OAAO,OAAO,0CAA0C,CAAC,KAAK,CAAC;AAG/D,OAAO,OAAO,koIAAkoI,CAAC,cAAc,QAAQ,CAAC;AACxqI,OAAO,YAAY,YAAY,CAAC,cAAc,QAAQ,CAAC;AAGvD,SAAS,SAAS,QAAQ,OAAO;AAE/B,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACjC,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT,WAAW,MAAM,oBAAoB,MAAM;AAEzC,QAAI,OAAO,OAAO,OAAO,KAAK,MAAM,KAAK;AACvC,aAAO,KAAK;AACZ,YAAM,kBAAkB;AAAA,IAC1B,WAAW,OAAO,OAAO,GAAG,GAAG;AAC7B,aAAO,OAAO,GAAG;AACjB,aAAO,KAAK;AACZ,UAAI,OAAO,IAAI,GAAG;AAChB,cAAM,kBAAkB;AAAA,IAC5B,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,OAAO,OAAO,KAAK,OAAO,YAAY,GAAG;AACxD,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,iBAAiB,KAAK,OAAO,KAAK;AAEtC,OAAK,OAAO,OAAO,OAAO,QAAQ,CAAC,MAAM,gBAAgB;AACvD,UAAM,iBAAiB;AACvB,WAAO;AAAA,EACT,WAAW,MAAM,gBAAgB;AAC/B,QAAI,MAAM,kBAAkB,IAAI;AAC9B,YAAM,iBAAiB;AAAA,IACzB,WAAW,OAAO,OAAO,MAAM,cAAc,GAAG;AAE9C,aAAO,KAAK;AACZ,YAAM,iBAAiB;AAAA,IACzB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB,QAAQ,OAAO,IAAI,GAAG;AACxD,WAAO,OAAO,MAAM,cAAc,KAAK,OAAO,UAAU;AACxD,WAAO;AAAA,EACT,WAAW,SAAS,KAAK,EAAE,GAAG;AAC5B,QAAI,OAAO;AACT,aAAO,MAAM,4BAA4B;AAAA,aAClC,OAAO;AACd,aAAO,MAAM,mBAAmB,KAAK,OAAO,MAAM,UAAU;AAAA;AAE5D,aAAO,MAAM,qCAAqC;AACpD,WAAO;AAAA,EACT,WAAW,qBAAqB,KAAK,KAAK,OAAO,KAAK,CAAC,GAAG;AACxD,WAAO,KAAK;AACZ,WAAO;AAAA,EACT,WAAW,oBAAoB,eAAe,cAAc,GAAG;AAC7D,WAAO,KAAK;AACZ,QAAI,OAAO,KAAK,MAAM;AACpB,aAAO,oBAAoB,eAAe,YAAY,CAAC;AAAA,EAC3D,WAAW,qBAAqB,KAAK,EAAE,GAAG;AACxC,WAAO;AAAA,EACT;AAGA,MAAI;AACJ,MAAI,OAAO,MAAM,YAAY,KAAK,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,MAAM,YAAY,IAAI;AACzC,QAAI,IAAI,KAAK,IAAI;AAAG,aAAO;AAAA,EAC7B,OAAO;AACL,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU;AAClB,WAAO,MAAM,OAAO;AAEpB,QAAI,OAAO,KAAK,MAAM;AAAK,aAAO,OAAO,GAAG;AAC5C,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,YAAY;AAExB,MAAI,MAAM,YAAY;AACpB,QAAI,SAAS,UAAU,OAAO,MAAM,QAAQ,GAAG;AAC7C,YAAM,aAAa;AACnB,aAAO;AAAA,IACT;AAEA,QAAK,QAAS,OAAO,KAAK,MAAM,KAAK;AAEnC,UAAI,KAAK,KAAK,OAAO,KAAK,CAAC;AAAG,eAAO;AAAA;AAChC,eAAO;AAAA,IACd;AAEA,QAAI,QAAQ,MAAM,eAAe,IAAI,MAChC,MAAM,IAAI,EAAE,MAAM,QAAQ,YAAY,MAAM,MAC5C,MAAM,IAAI,EAAE,MAAM,QAAQ,KAAK,MAAM,KAAK;AAE7C,UAAI,OAAO,QAAQ,OAAO;AACxB,eAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAEzC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE;AAAG,eAAO,KAAK;AAClD,aAAO,MAAM,IAAI,EAAE;AAAA,IACrB;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ;AAChB,QAAI,SAAS,UAAU,SAAS,SAAS;AACvC,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,MAAM,eAAe,IAAI,MAChC,MAAM,IAAI,EAAE,MAAM,QAAQ,QAAQ,MAAM,MACxC,MAAM,IAAI,EAAE,MAAM,QAAQ,KAAK,MAAM,KAAK;AAC7C,aAAO,MAAM,OAAO;AACpB,aAAO,MAAM,IAAI,EAAE;AAAA,IACrB;AAAA,EACF;AAEA,MAAI,MAAM,SAAS;AACjB,QAAI,SAAS,SAAS;AACpB,UAAI,OAAO,KAAK,MAAM;AAAK,eAAO,KAAK;AACvC,YAAM,UAAU;AAChB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,MAAM,eAAe,IAAI,MAChC,MAAM,IAAI,EAAE,MAAM,QAAQ,SAAS,MAAM,MACzC,MAAM,IAAI,EAAE,MAAM,QAAQ,KAAK,MAAM,KAAK;AAC7C,aAAO,MAAM,OAAO;AACpB,aAAO,MAAM,IAAI,EAAE;AAAA,IACrB;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,MAAM,eAAe,IAAI,GAAG;AAEtC,WAAO,OAAO,CAAC;AAEf,WAAO,MAAM,OAAO;AACpB,QAAI,SAAS,UAAU,IAAI,KAAK,OAAO,KAAK,CAAC,MAAM,OAAO;AACxD,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAQ;AACnB,YAAM,SAAS;AACf,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,UAAU;AACrB,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AACA,QAAI,aAAa,KAAK,IAAI;AAAG,aAAO;AAEpC,QAAI,SAAS,YAAY;AACvB,aAAO,IAAI,OAAO;AAClB,aAAO;AAAA,IACT;AAGA,QAAI,MAAM,eAAe,QAAQ,MAAM,IAAI,EAAE,MAAM,QAAQ,YAAY,MAAM;AAC3E,aAAO,MAAM,IAAI,EAAE;AACrB,QAAI,MAAM,WAAW,QAAQ,MAAM,IAAI,EAAE,MAAM,QAAQ,QAAQ,MAAM;AACnE,aAAO,MAAM,IAAI,EAAE;AACrB,QAAI,MAAM,YAAY,QAAQ,MAAM,IAAI,EAAE,MAAM,QAAQ,SAAS,MAAM;AACrE,aAAO,MAAM,IAAI,EAAE;AACrB,QAAI,MAAM,IAAI,EAAE,MAAM,QAAQ,KAAK,MAAM;AACvC,aAAO,MAAM,IAAI,EAAE;AACrB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAE9B,QAAI,OAAO,SAAS;AAAG,aAAO;AAE9B,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAClD;AACF;", "names": []}