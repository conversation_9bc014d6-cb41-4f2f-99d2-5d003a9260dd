<template>
  <div>
    <div class="fab-container">
      <router-link to="/publish" class="fab" title="发闲置">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
        </span>
        <span>发闲置</span>
      </router-link>
      <router-link to="/chat" class="fab" title="消息">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
        </span>
        <span>消息</span>
        <el-badge :value="unreadCount" class="message-badge" type="danger" :hidden="unreadCount === 0" />
      </router-link>
      <a href="#" class="fab" title="反馈" @click.prevent="showFeedbackDialog">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
        </span>
        <span>反馈</span>
      </a>
      <router-link v-if="!isForumPage" to="/forum" class="fab" title="论坛">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
        </span>
        <span>论坛</span>
      </router-link>
      <router-link v-if="isForumPage" to="/home" class="fab" title="商城">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
        </span>
        <span>商城</span>
      </router-link>
      <a href="#" class="fab" title="客服" @click.prevent="showAiChatDialog">
        <span class="fab-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
            <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
            <line x1="12" y1="19" x2="12" y2="23"></line>
            <line x1="8" y1="23" x2="16" y2="23"></line>
          </svg>
        </span>
        <span>客服</span>
      </a>
    </div>
    
    <!-- 反馈弹窗 -->
    <FeedbackDialog 
      :visible="feedbackDialogVisible" 
      @close="feedbackDialogVisible = false"
      @success="handleFeedbackSuccess" 
    />
    
    <!-- 反馈成功提示 -->
    <FeedbackSuccess
      :visible="feedbackSuccessVisible"
      @close="feedbackSuccessVisible = false"
    />

    <!-- AI客服聊天弹窗 -->
    <AiChatDialog
      :visible="aiChatDialogVisible"
      @close="aiChatDialogVisible = false"
    />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElBadge } from 'element-plus';
import { getUnreadCount } from '@/api/message';
import FeedbackDialog from './FeedbackDialog.vue';
import FeedbackSuccess from './FeedbackSuccess.vue';
import AiChatDialog from './AiChatDialog.vue';

const route = useRoute();
const feedbackDialogVisible = ref(false);
const feedbackSuccessVisible = ref(false);
const aiChatDialogVisible = ref(false);
const unreadCount = ref(0);
let refreshTimer = null;

// 判断当前是否在论坛相关页面
const isForumPage = computed(() => {
  return route.path.includes('/forum');
});

// 显示反馈弹窗
const showFeedbackDialog = () => {
  feedbackDialogVisible.value = true;
};

// 显示AI客服聊天弹窗
const showAiChatDialog = () => {
  aiChatDialogVisible.value = true;
};

// 处理反馈提交成功
const handleFeedbackSuccess = () => {
  feedbackSuccessVisible.value = true;
};

// 获取未读消息总数
const fetchUnreadCount = async () => {
  try {
    const response = await getUnreadCount();
    if (response && response.data !== undefined) {
      unreadCount.value = response.data;
    }
  } catch (error) {
    console.error('获取未读消息数失败:', error);
  }
};

onMounted(() => {
  // 初始获取未读消息数
  fetchUnreadCount();
  
  // 设置定时器，每5秒刷新一次未读消息数
  refreshTimer = setInterval(fetchUnreadCount, 5000);
});

onUnmounted(() => {
  // 组件卸载时清除定时器
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<style scoped>
/* Floating Action Buttons */
.fab-container {
  position: fixed;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 998;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  width: 72px;
}

.fab {
  background-color: transparent;
  color: #333;
  width: 60px;
  height: auto;
  padding: 8px 0;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  gap: 6px;
  transition: background-color 0.2s ease;
  cursor: pointer;
  position: relative;
}

.fab:hover {
  background-color: #f5f5f5;
}

.fab .fab-icon {
  font-size: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 消息徽章样式 */
:deep(.message-badge) {
  position: absolute;
  top: -5px;
  right: -5px;
}
</style> 