package com.lzhshtp.shangcheng.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;
import com.lzhshtp.shangcheng.mapper.ScheduledTaskConfigMapper;
import com.lzhshtp.shangcheng.model.ScheduledTaskConfig;
import com.lzhshtp.shangcheng.service.DynamicScheduledTaskService;
import com.lzhshtp.shangcheng.service.OrderTimeoutService;
import com.lzhshtp.shangcheng.service.TaskConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 定时任务配置服务实现类
 */
@Service
public class TaskConfigServiceImpl implements TaskConfigService {

    private static final Logger logger = LoggerFactory.getLogger(TaskConfigServiceImpl.class);

    private static final String ORDER_TIMEOUT_TASK_NAME = "order_timeout_cancel";

    @Autowired
    private ScheduledTaskConfigMapper scheduledTaskConfigMapper;

    @Autowired
    private DynamicScheduledTaskService dynamicScheduledTaskService;

    @Autowired
    private OrderTimeoutService orderTimeoutService;

    @Override
    public OrderTimeoutConfigDTO getOrderTimeoutConfig() {
        try {
            ScheduledTaskConfig taskConfig = scheduledTaskConfigMapper.findByTaskName(ORDER_TIMEOUT_TASK_NAME);

            if (taskConfig == null) {
                // 返回默认配置
                return createDefaultOrderTimeoutConfig();
            }

            // 解析JSON配置
            String configParams = taskConfig.getConfigParams();
            if (configParams == null || configParams.trim().isEmpty()) {
                return createDefaultOrderTimeoutConfig();
            }

            OrderTimeoutConfigDTO config = JSON.parseObject(configParams, OrderTimeoutConfigDTO.class);
            config.setEnabled(taskConfig.getIsEnabled());

            return config;

        } catch (Exception e) {
            logger.error("获取订单超时配置失败", e);
            return createDefaultOrderTimeoutConfig();
        }
    }

    @Override
    @Transactional
    public boolean updateOrderTimeoutConfig(OrderTimeoutConfigDTO config) {
        try {
            // 验证配置参数
            if (!validateOrderTimeoutConfig(config)) {
                return false;
            }

            // 生成Cron表达式
            String cronExpression = config.generateCronExpression();

            // 使用MyBatis-Plus的方式查找现有配置
            QueryWrapper<ScheduledTaskConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("lzhshtp_task_name", ORDER_TIMEOUT_TASK_NAME);
            ScheduledTaskConfig taskConfig = scheduledTaskConfigMapper.selectOne(queryWrapper);

            if (taskConfig == null) {
                // 创建新配置
                taskConfig = ScheduledTaskConfig.builder()
                        .taskName(ORDER_TIMEOUT_TASK_NAME)
                        .taskType("order_timeout")
                        .cronExpression(cronExpression)
                        .isEnabled(config.getEnabled())
                        .configParams(JSON.toJSONString(config))
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();

                scheduledTaskConfigMapper.insert(taskConfig);
            } else {
                // 更新现有配置
                taskConfig.setCronExpression(cronExpression);
                taskConfig.setIsEnabled(config.getEnabled());
                taskConfig.setConfigParams(JSON.toJSONString(config));
                taskConfig.setUpdatedAt(LocalDateTime.now());

                scheduledTaskConfigMapper.updateById(taskConfig);
            }

            // 重新调度任务
            if (config.getEnabled()) {
                dynamicScheduledTaskService.rescheduleTask(
                    ORDER_TIMEOUT_TASK_NAME,
                    cronExpression,
                    () -> orderTimeoutService.cancelExpiredOrders(config)
                );
                logger.info("订单超时任务已重新调度，间隔：{}分钟", config.getCheckInterval());
            } else {
                dynamicScheduledTaskService.cancelTask(ORDER_TIMEOUT_TASK_NAME);
                logger.info("订单超时任务已禁用");
            }

            return true;

        } catch (Exception e) {
            logger.error("更新订单超时配置失败", e);
            return false;
        }
    }

    @Override
    public List<ScheduledTaskConfig> getAllTaskConfigs() {
        try {
            return scheduledTaskConfigMapper.selectList(null);
        } catch (Exception e) {
            logger.error("获取所有任务配置失败", e);
            return List.of();
        }
    }

    @Override
    public ScheduledTaskConfig getTaskConfigByName(String taskName) {
        try {
            return scheduledTaskConfigMapper.findByTaskName(taskName);
        } catch (Exception e) {
            logger.error("获取任务配置失败，任务名：{}", taskName, e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean toggleTask(String taskName, boolean enabled) {
        try {
            ScheduledTaskConfig taskConfig = scheduledTaskConfigMapper.findByTaskName(taskName);
            if (taskConfig == null) {
                logger.warn("任务配置不存在：{}", taskName);
                return false;
            }

            taskConfig.setIsEnabled(enabled);
            taskConfig.setUpdatedAt(LocalDateTime.now());
            scheduledTaskConfigMapper.updateById(taskConfig);

            if (ORDER_TIMEOUT_TASK_NAME.equals(taskName)) {
                if (enabled) {
                    // 启用任务
                    OrderTimeoutConfigDTO config = JSON.parseObject(taskConfig.getConfigParams(), OrderTimeoutConfigDTO.class);
                    dynamicScheduledTaskService.rescheduleTask(
                        taskName,
                        taskConfig.getCronExpression(),
                        () -> orderTimeoutService.cancelExpiredOrders(config)
                    );
                } else {
                    // 禁用任务
                    dynamicScheduledTaskService.cancelTask(taskName);
                }
            }

            logger.info("任务 {} 已{}，状态：{}", taskName, enabled ? "启用" : "禁用", enabled);
            return true;

        } catch (Exception e) {
            logger.error("切换任务状态失败，任务名：{}，目标状态：{}", taskName, enabled, e);
            return false;
        }
    }

    @Override
    public String executeTaskManually(String taskName) {
        try {
            if (ORDER_TIMEOUT_TASK_NAME.equals(taskName)) {
                OrderTimeoutConfigDTO config = getOrderTimeoutConfig();
                return orderTimeoutService.cancelExpiredOrders(config);
            }

            return "不支持的任务类型：" + taskName;

        } catch (Exception e) {
            logger.error("手动执行任务失败，任务名：{}", taskName, e);
            return "执行失败：" + e.getMessage();
        }
    }

    /**
     * 创建默认订单超时配置
     */
    private OrderTimeoutConfigDTO createDefaultOrderTimeoutConfig() {
        OrderTimeoutConfigDTO config = new OrderTimeoutConfigDTO();
        config.setEnabled(true);
        config.setTimeoutMinutes(30);
        config.setCheckInterval(5);
        config.setSendNotification(true);
        config.setNotificationMethods(Arrays.asList("message"));
        config.setNotificationTargets(Arrays.asList("buyer", "seller"));
        return config;
    }

    /**
     * 验证订单超时配置
     */
    private boolean validateOrderTimeoutConfig(OrderTimeoutConfigDTO config) {
        if (config == null) {
            logger.warn("配置对象为空");
            return false;
        }

        if (config.getTimeoutMinutes() == null || config.getTimeoutMinutes() <= 0) {
            logger.warn("超时时间无效：{}", config.getTimeoutMinutes());
            return false;
        }

        if (config.getCheckInterval() == null || config.getCheckInterval() <= 0) {
            logger.warn("检查间隔无效：{}", config.getCheckInterval());
            return false;
        }

        return true;
    }
}
