<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ForumCommentMapper">
    
    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.ForumComment">
        <id column="lzhshtp_comment_id" property="commentId" />
        <result column="lzhshtp_post_id" property="postId" />
        <result column="lzhshtp_author_id" property="authorId" />
        <result column="lzhshtp_content" property="content" />
        <result column="lzhshtp_commented_at" property="commentedAt" />
        <result column="lzhshtp_parent_comment_id" property="parentCommentId" />
    </resultMap>
    
    <!-- 查询帖子的所有评论 -->
    <select id="selectCommentsByPostId" resultMap="BaseResultMap">
        SELECT 
            c.lzhshtp_comment_id,
            c.lzhshtp_post_id,
            c.lzhshtp_author_id,
            c.lzhshtp_content,
            c.lzhshtp_commented_at,
            c.lzhshtp_parent_comment_id
        FROM 
            tb_lzhshtp_forum_comments c
        WHERE 
            c.lzhshtp_post_id = #{postId}
        ORDER BY 
            c.lzhshtp_commented_at ASC
    </select>
    
    <!-- 查询评论的所有回复 -->
    <select id="selectRepliesByCommentId" resultMap="BaseResultMap">
        SELECT 
            c.lzhshtp_comment_id,
            c.lzhshtp_post_id,
            c.lzhshtp_author_id,
            c.lzhshtp_content,
            c.lzhshtp_commented_at,
            c.lzhshtp_parent_comment_id
        FROM 
            tb_lzhshtp_forum_comments c
        WHERE 
            c.lzhshtp_parent_comment_id = #{commentId}
        ORDER BY 
            c.lzhshtp_commented_at ASC
    </select>
    
    <!-- 统计帖子评论数量 -->
    <select id="countCommentsByPostId" resultType="java.lang.Integer">
        SELECT 
            COUNT(1)
        FROM 
            tb_lzhshtp_forum_comments
        WHERE 
            lzhshtp_post_id = #{postId}
    </select>
    
</mapper> 