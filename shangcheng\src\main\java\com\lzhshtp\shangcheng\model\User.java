package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户实体类，对应数据库表tb_lzhshtp_users
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_users")
public class User {
    
    @TableId(value = "lzhshtp_user_id", type = IdType.AUTO)
    private Long userId;
    
    @TableField("lzhshtp_username")
    private String username;
    
    @TableField("lzhshtp_password_hash")
    private String passwordHash;
    
    @TableField("lzhshtp_email")
    private String email;
    
    @TableField("lzhshtp_phone_number")
    private String phoneNumber;
    
    @TableField("lzhshtp_avatar_url")
    private String avatarUrl;
    
    @TableField("lzhshtp_registration_date")
    private LocalDateTime registrationDate;
    
    @TableField("lzhshtp_last_login_date")
    private LocalDateTime lastLoginDate;
    
    @TableField("lzhshtp_credit_score")
    private Integer creditScore;
    
    @TableField("lzhshtp_bio")
    private String bio;
    
    @TableField("lzhshtp_location")
    private String location;

    @TableField("lzhshtp_balance")
    private BigDecimal balance;

    @TableField("lzhshtp_can_publish_product")
    private Boolean canPublishProduct;

    @TableField("lzhshtp_is_active")
    private Boolean isActive;

    @TableField("lzhshtp_role")
    private String role;
} 