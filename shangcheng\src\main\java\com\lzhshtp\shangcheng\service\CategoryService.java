package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.CategoryDTO;
import com.lzhshtp.shangcheng.model.Category;

import java.util.List;

public interface CategoryService {
    
    /**
     * 获取所有分类（树形结构）
     * 
     * @return 分类树
     */
    List<CategoryDTO> getAllCategoriesTree();
    
    /**
     * 获取所有分类（平铺结构）
     * 
     * @return 分类列表
     */
    List<CategoryDTO> getAllCategories();
    
    /**
     * 根据ID获取分类
     * 
     * @param categoryId 分类ID
     * @return 分类信息
     */
    CategoryDTO getCategoryById(Integer categoryId);
    
    /**
     * 创建分类
     * 
     * @param categoryDTO 分类信息
     * @return 创建后的分类ID
     */
    Integer createCategory(CategoryDTO categoryDTO);
    
    /**
     * 更新分类
     * 
     * @param categoryId 分类ID
     * @param categoryDTO 分类信息
     * @return 是否更新成功
     */
    boolean updateCategory(Integer categoryId, CategoryDTO categoryDTO);
    
    /**
     * 删除分类
     * 
     * @param categoryId 分类ID
     * @return 是否删除成功
     */
    boolean deleteCategory(Integer categoryId);
} 