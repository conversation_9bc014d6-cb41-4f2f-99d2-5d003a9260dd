<template>
  <div class="refund-management">
    <!-- 页面标题和统计 -->
    <div class="page-header">
      <h1>退款管理</h1>
    </div>

    <!-- 筛选和操作栏 -->
    <div class="filter-bar">
      <div class="filter-left">
        <el-select v-model="filters.status" placeholder="退款状态" clearable @change="loadRefunds">
          <el-option label="全部" value=""></el-option>
          <el-option label="等待管理员处理" value="pending_admin"></el-option>
          <el-option label="管理员已同意" value="admin_approved"></el-option>
          <el-option label="管理员已拒绝" value="admin_rejected"></el-option>
          <el-option label="已完成" value="completed"></el-option>
        </el-select>

        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadRefunds">
        </el-date-picker>

        <el-input
          v-model="filters.keyword"
          placeholder="搜索订单号/用户名"
          clearable
          @keyup.enter="loadRefunds"
          style="width: 200px;">
          <template #append>
            <el-button @click="loadRefunds" icon="Search"></el-button>
          </template>
        </el-input>
      </div>

      <div class="filter-right">
        <el-button @click="loadRefunds" icon="Refresh">刷新</el-button>
      </div>
    </div>

    <!-- 退款申请列表 -->
    <div class="refund-list">
      <el-table
        :data="refunds"
        v-loading="loading"
        stripe
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>

        <el-table-column prop="refundId" label="退款单号" width="120">
          <template #default="{ row }">
            <el-link @click="viewDetail(row)" type="primary">{{ row.refundId }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="orderId" label="订单号" width="120"></el-table-column>

        <el-table-column label="商品信息" width="200">
          <template #default="{ row }">
            <div class="product-info">
              <img :src="row.productImage" class="product-image" />
              <div class="product-details">
                <div class="product-title">{{ row.productTitle }}</div>
                <div class="product-price">¥{{ row.productPrice }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="买家信息" width="120">
          <template #default="{ row }">
            <div>{{ row.buyerName }}</div>
            <div class="text-gray">{{ row.buyerPhone }}</div>
          </template>
        </el-table-column>

        <el-table-column label="卖家信息" width="120">
          <template #default="{ row }">
            <div>{{ row.sellerName }}</div>
            <div class="text-gray">{{ row.sellerPhone }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="refundAmount" label="退款金额" width="100">
          <template #default="{ row }">
            <span class="amount">¥{{ row.refundAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="refundReason" label="退款原因" width="150" show-overflow-tooltip></el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdTime" label="申请时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending_admin'"
              @click="handleRefund(row, true)"
              type="success"
              size="small">
              同意
            </el-button>
            <el-button
              v-if="row.status === 'pending_admin'"
              @click="showRejectDialog(row)"
              type="danger"
              size="small">
              拒绝
            </el-button>
            <el-button @click="viewDetail(row)" type="primary" size="small">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRefunds"
          @current-change="loadRefunds">
        </el-pagination>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedRefunds.length > 0" class="batch-actions">
      <span>已选择 {{ selectedRefunds.length }} 项</span>
      <el-button @click="batchApprove" type="success">批量同意</el-button>
      <el-button @click="batchReject" type="danger">批量拒绝</el-button>
    </div>

    <!-- 拒绝退款对话框 -->
    <el-dialog v-model="showRejectModal" title="拒绝退款" width="500px">
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因..."
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRejectModal = false">取消</el-button>
        <el-button @click="confirmReject" type="danger">确认拒绝</el-button>
      </template>
    </el-dialog>

    <!-- 退款详情对话框 -->
    <el-dialog v-model="showDetailModal" title="退款详情" width="800px">
      <div v-if="selectedRefund" class="refund-detail">
        <!-- 详情内容将在这里显示 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>退款单号：</label>
              <span>{{ selectedRefund.refundId }}</span>
            </div>
            <div class="detail-item">
              <label>订单号：</label>
              <span>{{ selectedRefund.orderId }}</span>
            </div>
            <div class="detail-item">
              <label>退款金额：</label>
              <span class="amount">¥{{ selectedRefund.refundAmount }}</span>
            </div>
            <div class="detail-item">
              <label>申请时间：</label>
              <span>{{ formatDate(selectedRefund.createdTime) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>退款原因</h3>
          <p>{{ selectedRefund.refundReason }}</p>
        </div>

        <div v-if="selectedRefund.evidenceUrls && selectedRefund.evidenceUrls.length > 0" class="detail-section">
          <h3>申请管理员介入材料</h3>
          <div class="evidence-content">
            <!-- 文字说明 -->
            <div v-if="getEvidenceDescription(selectedRefund.evidenceUrls)" class="evidence-description">
              <h4>补充说明：</h4>
              <p>{{ getEvidenceDescription(selectedRefund.evidenceUrls) }}</p>
            </div>

            <!-- 证据文件 -->
            <div v-if="getEvidenceFiles(selectedRefund.evidenceUrls).length > 0" class="evidence-files">
              <h4>证据材料：</h4>
              <div class="file-grid">
                <div
                  v-for="(fileUrl, index) in getEvidenceFiles(selectedRefund.evidenceUrls)"
                  :key="index"
                  class="evidence-file">
                  <!-- 图片预览 -->
                  <div v-if="isImageFile(fileUrl)" class="image-preview">
                    <img :src="fileUrl" :alt="`证据图片${index + 1}`" @click="previewImage(fileUrl)" />
                    <div class="file-actions">
                      <el-button size="small" @click="previewImage(fileUrl)">预览</el-button>
                      <el-button size="small" @click="downloadFile(fileUrl)">下载</el-button>
                    </div>
                  </div>

                  <!-- 文档文件 -->
                  <div v-else class="document-file">
                    <div class="file-icon">📄</div>
                    <div class="file-info">
                      <div class="file-name">{{ getFileName(fileUrl) }}</div>
                      <div class="file-actions">
                        <el-button size="small" type="primary" @click="downloadFile(fileUrl)">下载</el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="showImagePreview" title="图片预览" width="80%" center>
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAdminRefundList,
  handleAdminRefund,
  getAdminRefundStatistics,
  getForcedRefundTasks
} from '@/api/refund'

// 响应式数据
const loading = ref(false)
const refunds = ref([])
const selectedRefunds = ref([])
const statistics = ref({})

// 筛选条件
const filters = reactive({
  status: '',
  dateRange: null,
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框控制
const showRejectModal = ref(false)
const showDetailModal = ref(false)
const showImagePreview = ref(false)
const selectedRefund = ref(null)
const previewImageUrl = ref('')

// 拒绝表单
const rejectForm = reactive({
  reason: ''
})

// 页面初始化
onMounted(() => {
  loadRefunds()
  loadStatistics()
})

// 加载退款列表
const loadRefunds = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      status: filters.status,
      keyword: filters.keyword
    }

    if (filters.dateRange) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await getAdminRefundList(params)
    if (response.success) {
      refunds.value = response.data || []
      pagination.total = response.data ? response.data.length : 0
    }
  } catch (error) {
    ElMessage.error('加载退款列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getAdminRefundStatistics()
    if (response.success) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 处理退款
const handleRefund = async (refund, approved) => {
  try {
    await handleAdminRefund({
      refundId: refund.refundId,
      approved,
      response: approved ? '管理员同意退款' : '管理员拒绝退款'
    })

    ElMessage.success(approved ? '已同意退款' : '已拒绝退款')
    loadRefunds()
    loadStatistics()
  } catch (error) {
    ElMessage.error('处理失败')
  }
}

// 显示拒绝对话框
const showRejectDialog = (refund) => {
  selectedRefund.value = refund
  rejectForm.reason = ''
  showRejectModal.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.reason.trim()) {
    ElMessage.warning('请输入拒绝原因')
    return
  }

  try {
    await handleAdminRefund({
      refundId: selectedRefund.value.refundId,
      approved: false,
      response: rejectForm.reason
    })

    ElMessage.success('已拒绝退款')
    showRejectModal.value = false
    loadRefunds()
    loadStatistics()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 查看详情
const viewDetail = (refund) => {
  selectedRefund.value = refund
  showDetailModal.value = true
}

// 解析证据描述
const getEvidenceDescription = (evidenceUrls) => {
  if (!evidenceUrls || evidenceUrls.length === 0) return ''

  try {
    // 尝试解析第一个元素作为JSON
    const firstItem = evidenceUrls[0]
    if (typeof firstItem === 'string' && firstItem.startsWith('{')) {
      const evidenceData = JSON.parse(firstItem + evidenceUrls.slice(1).join(''))
      return evidenceData.description || ''
    }
  } catch (error) {
    console.error('解析证据描述失败:', error)
  }

  return ''
}

// 获取证据文件列表
const getEvidenceFiles = (evidenceUrls) => {
  if (!evidenceUrls || evidenceUrls.length === 0) return []

  try {
    // 尝试解析JSON格式
    const firstItem = evidenceUrls[0]
    if (typeof firstItem === 'string' && firstItem.startsWith('{')) {
      const evidenceData = JSON.parse(firstItem + evidenceUrls.slice(1).join(''))
      return evidenceData.files || []
    } else {
      // 兼容旧格式（直接是URL数组）
      return evidenceUrls.filter(url => url && url.trim())
    }
  } catch (error) {
    console.error('解析证据文件失败:', error)
    // 如果解析失败，尝试作为URL数组处理
    return evidenceUrls.filter(url => url && url.trim())
  }
}

// 判断是否为图片文件
const isImageFile = (url) => {
  if (!url) return false
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
  const lowerUrl = url.toLowerCase()
  return imageExtensions.some(ext => lowerUrl.includes(ext))
}

// 获取文件名
const getFileName = (url) => {
  if (!url) return '未知文件'
  try {
    const urlParts = url.split('/')
    const fileName = urlParts[urlParts.length - 1]
    return fileName.split('?')[0] || '未知文件'
  } catch (error) {
    return '未知文件'
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

// 下载文件
const downloadFile = (fileUrl) => {
  try {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = getFileName(fileUrl)
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRefunds.value = selection
}

// 批量同意
const batchApprove = async () => {
  try {
    await ElMessageBox.confirm('确认批量同意选中的退款申请？', '批量操作')

    // 循环处理每个退款申请
    for (const refund of selectedRefunds.value) {
      await handleAdminRefund({
        refundId: refund.refundId,
        approved: true,
        response: '管理员批量同意退款'
      })
    }

    ElMessage.success('批量操作成功')
    loadRefunds()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量拒绝
const batchReject = async () => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入拒绝原因', '批量拒绝')

    // 循环处理每个退款申请
    for (const refund of selectedRefunds.value) {
      await handleAdminRefund({
        refundId: refund.refundId,
        approved: false,
        response: reason
      })
    }

    ElMessage.success('批量操作成功')
    loadRefunds()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

// 导出数据（暂未实现）
const exportData = async () => {
  ElMessage.info('导出功能暂未实现')
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    'pending_admin': 'warning',
    'admin_approved': 'success',
    'admin_rejected': 'danger',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'pending_admin': '等待管理员处理',
    'admin_approved': '管理员已同意',
    'admin_rejected': '管理员已拒绝',
    'completed': '已完成'
  }
  return textMap[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

// 显示强制退款任务
const showForcedRefundTasks = () => {
  filters.status = 'forced_refund'
  loadRefunds()
}

// 显示待处理申请
const showPendingRefunds = () => {
  filters.status = 'pending_admin'
  loadRefunds()
}
</script>

<style scoped>
.refund-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 20px 0;
  color: #333;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-left {
  display: flex;
  gap: 15px;
  align-items: center;
}

.refund-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.product-details {
  flex: 1;
}

.product-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.product-price {
  font-size: 12px;
  color: #666;
}

.text-gray {
  color: #999;
  font-size: 12px;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination {
  padding: 20px;
  text-align: center;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  gap: 15px;
}

.refund-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h3 {
  margin: 0 0 10px 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  display: flex;
}

.detail-item label {
  font-weight: bold;
  color: #666;
  min-width: 80px;
}

/* 证据材料样式 */
.evidence-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.evidence-description {
  margin-bottom: 20px;
}

.evidence-description h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.evidence-description p {
  margin: 0;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  line-height: 1.5;
}

.evidence-files h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 14px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.evidence-file {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.evidence-file:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-preview {
  position: relative;
}

.image-preview img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  cursor: pointer;
}

.document-file {
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-icon {
  font-size: 24px;
  color: #6c757d;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  word-break: break-all;
}

.file-actions {
  padding: 10px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.file-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 图片预览对话框样式 */
.image-preview-container {
  text-align: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
