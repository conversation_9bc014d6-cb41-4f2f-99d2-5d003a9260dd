package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.SellerReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商家评价Mapper接口
 */
@Mapper
public interface SellerReviewMapper extends BaseMapper<SellerReview> {

    /**
     * 检查订单是否已经评价过
     * @param orderId 订单ID
     * @return 评价记录，如果没有评价过则返回null
     */
    @Select("SELECT * FROM tb_lzhshtp_seller_reviews WHERE lzhshtp_order_id = #{orderId}")
    SellerReview selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取卖家的评价统计信息
     * @param sellerId 卖家ID
     * @return 统计信息Map
     */
    @Select("SELECT " +
            "COUNT(*) as totalReviews, " +
            "AVG(lzhshtp_rating) as averageRating, " +
            "SUM(CASE WHEN lzhshtp_rating >= 4 THEN 1 ELSE 0 END) as positiveReviews, " +
            "SUM(CASE WHEN lzhshtp_rating <= 2 THEN 1 ELSE 0 END) as negativeReviews " +
            "FROM tb_lzhshtp_seller_reviews " +
            "WHERE lzhshtp_seller_id = #{sellerId}")
    Map<String, Object> getSellerReviewStatistics(@Param("sellerId") Long sellerId);

    /**
     * 获取卖家的最近评价列表
     * @param sellerId 卖家ID
     * @param limit 限制数量
     * @return 评价列表
     */
    @Select("SELECT sr.*, u.lzhshtp_username as buyerName, p.lzhshtp_title as productTitle " +
            "FROM tb_lzhshtp_seller_reviews sr " +
            "LEFT JOIN tb_lzhshtp_users u ON sr.lzhshtp_buyer_id = u.lzhshtp_user_id " +
            "LEFT JOIN tb_lzhshtp_products p ON sr.lzhshtp_product_id = p.lzhshtp_product_id " +
            "WHERE sr.lzhshtp_seller_id = #{sellerId} " +
            "ORDER BY sr.lzhshtp_created_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getSellerRecentReviews(@Param("sellerId") Long sellerId, @Param("limit") Integer limit);

    /**
     * 获取买家对特定卖家的评价次数（用于防刷分）
     * @param buyerId 买家ID
     * @param sellerId 卖家ID
     * @param days 天数
     * @return 评价次数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_seller_reviews " +
            "WHERE lzhshtp_buyer_id = #{buyerId} " +
            "AND lzhshtp_seller_id = #{sellerId} " +
            "AND lzhshtp_created_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    Integer countReviewsByBuyerInDays(@Param("buyerId") Long buyerId, 
                                     @Param("sellerId") Long sellerId, 
                                     @Param("days") Integer days);

    /**
     * 获取卖家各星级评价数量分布
     * @param sellerId 卖家ID
     * @return 星级分布Map
     */
    @Select("SELECT " +
            "lzhshtp_rating as rating, " +
            "COUNT(*) as count " +
            "FROM tb_lzhshtp_seller_reviews " +
            "WHERE lzhshtp_seller_id = #{sellerId} " +
            "GROUP BY lzhshtp_rating " +
            "ORDER BY lzhshtp_rating")
    List<Map<String, Object>> getSellerRatingDistribution(@Param("sellerId") Long sellerId);

    /**
     * 获取卖家好评率
     * @param sellerId 卖家ID
     * @return 好评率（百分比）
     */
    @Select("SELECT " +
            "CASE WHEN COUNT(*) = 0 THEN 0 " +
            "ELSE ROUND(SUM(CASE WHEN lzhshtp_rating >= 4 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) " +
            "END as positiveRate " +
            "FROM tb_lzhshtp_seller_reviews " +
            "WHERE lzhshtp_seller_id = #{sellerId}")
    BigDecimal getSellerPositiveRate(@Param("sellerId") Long sellerId);

    /**
     * 获取最近N天内的评价列表（用于异常检测）
     * @param sellerId 卖家ID
     * @param days 天数
     * @return 评价列表
     */
    @Select("SELECT * FROM tb_lzhshtp_seller_reviews " +
            "WHERE lzhshtp_seller_id = #{sellerId} " +
            "AND lzhshtp_created_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "ORDER BY lzhshtp_created_time DESC")
    List<SellerReview> getRecentReviews(@Param("sellerId") Long sellerId, @Param("days") Integer days);
}
