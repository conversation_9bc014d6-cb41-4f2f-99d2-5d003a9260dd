{"name": "uni-ai-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "api": "node scripts/generate-api.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.4", "dayjs": "^1.11.10", "element-plus": "^2.4.2", "lodash": "^4.17.21", "md-editor-v3": "^4.13.2", "pinia": "^2.1.7", "sse.js": "^2.4.1", "vue": "^3.4.21", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "adm-zip": "^0.5.10", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "fs-extra": "^11.1.1", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "sass": "^1.69.7", "typescript": "~5.3.0", "uuid": "^9.0.1", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}