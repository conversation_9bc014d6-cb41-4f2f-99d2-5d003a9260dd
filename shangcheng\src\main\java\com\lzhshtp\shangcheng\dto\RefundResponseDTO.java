package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退款处理响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefundResponseDTO {
    
    @NotNull(message = "退款申请ID不能为空")
    private Long refundId;
    
    @NotNull(message = "处理结果不能为空")
    private Boolean approved; // true: 同意, false: 拒绝
    
    @NotBlank(message = "处理意见不能为空")
    private String response; // 处理意见
}
