<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ManualAuditTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="ManualAuditTaskResultMap" type="com.lzhshtp.shangcheng.model.ManualAuditTask">
        <id column="lzhshtp_task_id" property="taskId"/>
        <result column="lzhshtp_product_id" property="productId"/>
        <result column="lzhshtp_seller_id" property="sellerId"/>
        <result column="lzhshtp_auto_audit_record_id" property="autoAuditRecordId"/>
        <result column="lzhshtp_audit_reasons" property="auditReasons"/>
        <result column="lzhshtp_status" property="status"/>
        <result column="lzhshtp_priority" property="priority"/>
        <result column="lzhshtp_admin_id" property="adminId"/>
        <result column="lzhshtp_admin_decision" property="adminDecision"/>
        <result column="lzhshtp_admin_comments" property="adminComments"/>
        <result column="lzhshtp_created_time" property="createdTime"/>
        <result column="lzhshtp_assigned_time" property="assignedTime"/>
        <result column="lzhshtp_completed_time" property="completedTime"/>
        <result column="lzhshtp_deadline" property="deadline"/>
    </resultMap>

    <!-- 根据条件查询审核任务 -->
    <select id="findByConditions" parameterType="map" resultMap="ManualAuditTaskResultMap">
        SELECT
            lzhshtp_task_id,
            lzhshtp_product_id,
            lzhshtp_seller_id,
            lzhshtp_auto_audit_record_id,
            lzhshtp_audit_reasons,
            lzhshtp_status,
            lzhshtp_priority,
            lzhshtp_admin_id,
            lzhshtp_admin_decision,
            lzhshtp_admin_comments,
            lzhshtp_created_time,
            lzhshtp_assigned_time,
            lzhshtp_completed_time,
            lzhshtp_deadline
        FROM tb_lzhshtp_manual_audit_tasks
        WHERE 1=1
        <if test="status != null and status != ''">
            AND lzhshtp_status = #{status}
        </if>
        <if test="priority != null">
            AND lzhshtp_priority <![CDATA[>=]]> #{priority}
        </if>
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="adminId != null">
            AND lzhshtp_admin_id = #{adminId}
        </if>
        ORDER BY lzhshtp_priority DESC, lzhshtp_created_time ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询待审核的任务列表（管理员未认领的任务） -->
    <select id="findPendingTasks" resultMap="ManualAuditTaskResultMap">
        SELECT
            lzhshtp_task_id,
            lzhshtp_product_id,
            lzhshtp_seller_id,
            lzhshtp_auto_audit_record_id,
            lzhshtp_audit_reasons,
            lzhshtp_status,
            lzhshtp_priority,
            lzhshtp_admin_id,
            lzhshtp_admin_decision,
            lzhshtp_admin_comments,
            lzhshtp_created_time,
            lzhshtp_assigned_time,
            lzhshtp_completed_time,
            lzhshtp_deadline
        FROM tb_lzhshtp_manual_audit_tasks
        WHERE lzhshtp_status = 'pending' AND lzhshtp_admin_id IS NULL
        ORDER BY lzhshtp_priority DESC, lzhshtp_created_time ASC
    </select>

    <!-- 根据条件统计审核任务数量 -->
    <select id="countByConditions" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_manual_audit_tasks
        WHERE 1=1
        <if test="status != null and status != ''">
            AND lzhshtp_status = #{status}
        </if>
        <if test="priority != null">
            AND lzhshtp_priority <![CDATA[>=]]> #{priority}
        </if>
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="adminId != null">
            AND lzhshtp_admin_id = #{adminId}
        </if>
    </select>

    <!-- 统计超时任务数量 -->
    <select id="countOverdueTasks" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_manual_audit_tasks
        WHERE lzhshtp_status IN ('pending', 'processing')
        AND lzhshtp_deadline <![CDATA[<]]> #{currentTime}
    </select>

</mapper>
