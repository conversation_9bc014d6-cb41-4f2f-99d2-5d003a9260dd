<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ProductMapper">
    
    <!-- 根据条件查询商品 -->
    <select id="selectProductsByCondition" resultType="com.lzhshtp.shangcheng.model.Product">
        SELECT
            p.lzhshtp_product_id as id,
            p.lzhshtp_title as title,
            p.lzhshtp_description as description,
            p.lzhshtp_price as price,
            p.lzhshtp_category_id as categoryId,
            p.lzhshtp_condition as `condition`,
            p.lzhshtp_location as location,
            p.lzhshtp_delivery_method as deliveryMethod,
            p.lzhshtp_image_urls as imageUrls,
            p.lzhshtp_posted_date as postedDate,
            p.lzhshtp_status as status,
            p.lzhshtp_seller_id as sellerId,
            p.lzhshtp_support_official_verification as supportOfficialVerification,
            p.lzhshtp_verification_fee as verificationFee
        FROM tb_lzhshtp_products p
        <where>
            <!-- 根据状态筛选 -->
            <if test="params.status != null and params.status != ''">
                AND UPPER(p.lzhshtp_status) = UPPER(#{params.status})
            </if>
            <!-- 根据卖家ID筛选 -->
            <if test="params.sellerId != null">
                AND p.lzhshtp_seller_id = #{params.sellerId}
            </if>
            <!-- 排除指定卖家的商品 -->
            <if test="params.excludeSellerId != null">
                AND p.lzhshtp_seller_id != #{params.excludeSellerId}
            </if>
            <!-- 根据分类ID筛选 -->
            <if test="params.categoryId != null">
                AND p.lzhshtp_category_id = #{params.categoryId}
            </if>
            <!-- 根据商品条件筛选 -->
            <if test="params.condition != null and params.condition != ''">
                AND p.lzhshtp_condition = #{params.condition}
            </if>
            <!-- 根据位置筛选 -->
            <if test="params.location != null and params.location != ''">
                AND p.lzhshtp_location LIKE CONCAT('%', #{params.location}, '%')
            </if>
            <!-- 根据交易方式筛选 -->
            <if test="params.deliveryMethod != null and params.deliveryMethod != ''">
                AND p.lzhshtp_delivery_method = #{params.deliveryMethod}
            </if>
            <!-- 价格范围筛选 -->
            <if test="params.minPrice != null">
                AND p.lzhshtp_price &gt;= #{params.minPrice}
            </if>
            <if test="params.maxPrice != null">
                AND p.lzhshtp_price &lt;= #{params.maxPrice}
            </if>
            <!-- 关键词搜索 -->
            <if test="params.keyword != null and params.keyword != ''">
                AND (
                    p.lzhshtp_title LIKE CONCAT('%', #{params.keyword}, '%')
                    OR p.lzhshtp_description LIKE CONCAT('%', #{params.keyword}, '%')
                )
            </if>
        </where>
        <!-- 排序 -->
        <choose>
            <when test="params.sortBy != null and params.sortBy != ''">
                <choose>
                    <when test="params.sortBy == 'price' and params.sortDirection == 'asc'">
                        ORDER BY p.lzhshtp_price ASC
                    </when>
                    <when test="params.sortBy == 'price' and params.sortDirection == 'desc'">
                        ORDER BY p.lzhshtp_price DESC
                    </when>
                    <when test="params.sortBy == 'date' and params.sortDirection == 'asc'">
                        ORDER BY p.lzhshtp_posted_date ASC
                    </when>
                    <when test="params.sortBy == 'date' and params.sortDirection == 'desc'">
                        ORDER BY p.lzhshtp_posted_date DESC
                    </when>
                    <when test="params.sortBy == 'random'">
                        ORDER BY RAND()
                    </when>
                    <otherwise>
                        ORDER BY p.lzhshtp_posted_date DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY p.lzhshtp_posted_date DESC
            </otherwise>
        </choose>
    </select>
    
    <!-- 查询商品详情 -->
    <select id="selectProductDetail" resultType="com.lzhshtp.shangcheng.model.Product">
        SELECT
            p.lzhshtp_product_id as id,
            p.lzhshtp_title as title,
            p.lzhshtp_description as description,
            p.lzhshtp_price as price,
            p.lzhshtp_category_id as categoryId,
            p.lzhshtp_condition as `condition`,
            p.lzhshtp_location as location,
            p.lzhshtp_delivery_method as deliveryMethod,
            p.lzhshtp_image_urls as imageUrls,
            p.lzhshtp_posted_date as postedDate,
            p.lzhshtp_status as status,
            p.lzhshtp_seller_id as sellerId,
            p.lzhshtp_support_official_verification as supportOfficialVerification,
            p.lzhshtp_verification_fee as verificationFee
        FROM tb_lzhshtp_products p
        WHERE p.lzhshtp_product_id = #{productId}
    </select>
    
    <!-- 重写BaseMapper的selectById方法，确保正确处理关键字 -->
    <select id="selectById" resultType="com.lzhshtp.shangcheng.model.Product">
        SELECT
            lzhshtp_product_id AS id,
            lzhshtp_title AS title,
            lzhshtp_description AS description,
            lzhshtp_price AS price,
            lzhshtp_category_id AS categoryId,
            `lzhshtp_condition` AS `condition`,
            lzhshtp_location AS location,
            lzhshtp_delivery_method AS deliveryMethod,
            lzhshtp_image_urls AS imageUrls,
            lzhshtp_posted_date AS postedDate,
            lzhshtp_status AS status,
            lzhshtp_seller_id AS sellerId,
            lzhshtp_support_official_verification AS supportOfficialVerification,
            lzhshtp_verification_fee AS verificationFee
        FROM tb_lzhshtp_products 
        WHERE lzhshtp_product_id=#{id}
    </select>
    
    <!-- 重写BaseMapper的selectBatchIds方法，确保正确处理关键字 -->
    <select id="selectBatchIds" resultType="com.lzhshtp.shangcheng.model.Product">
        SELECT
            lzhshtp_product_id AS id,
            lzhshtp_title AS title,
            lzhshtp_description AS description,
            lzhshtp_price AS price,
            lzhshtp_category_id AS categoryId,
            `lzhshtp_condition` AS `condition`,
            lzhshtp_location AS location,
            lzhshtp_delivery_method AS deliveryMethod,
            lzhshtp_image_urls AS imageUrls,
            lzhshtp_posted_date AS postedDate,
            lzhshtp_status AS status,
            lzhshtp_seller_id AS sellerId,
            lzhshtp_support_official_verification AS supportOfficialVerification,
            lzhshtp_verification_fee AS verificationFee
        FROM
            tb_lzhshtp_products
        WHERE
            lzhshtp_product_id IN
        <foreach item="item" collection="coll" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    
</mapper> 