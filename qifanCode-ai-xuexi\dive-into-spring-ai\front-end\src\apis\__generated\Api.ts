import type {Executor} from './';
import {
    <PERSON><PERSON><PERSON>ageController, 
    AiSessionController, 
    DocumentController, 
    DocumentDemoController, 
    MessageDemoController, 
    UserController
} from './services/';

export class Api {
    
    readonly documentController: DocumentController
    
    readonly aiMessageController: AiMessageController
    
    readonly aiSessionController: AiSessionController
    
    readonly documentDemoController: DocumentDemoController
    
    readonly messageDemoController: MessageDemoController
    
    readonly userController: UserController
    
    constructor(executor: Executor) {
        this.documentController = new DocumentController(executor);
        this.aiMessageController = new AiMessageController(executor);
        this.aiSessionController = new AiSessionController(executor);
        this.documentDemoController = new DocumentDemoController(executor);
        this.messageDemoController = new MessageDemoController(executor);
        this.userController = new UserController(executor);
    }
}