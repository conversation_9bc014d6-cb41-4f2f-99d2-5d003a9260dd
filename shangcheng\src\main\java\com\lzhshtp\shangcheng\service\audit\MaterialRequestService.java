package com.lzhshtp.shangcheng.service.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lzhshtp.shangcheng.mapper.ManualAuditTaskMapper;
import com.lzhshtp.shangcheng.mapper.MaterialRequestMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.SecondReviewTaskMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 材料请求服务
 */
@Slf4j
@Service
public class MaterialRequestService {

    @Autowired
    private MaterialRequestMapper materialRequestMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ManualAuditTaskMapper manualAuditTaskMapper;

    @Autowired
    private SecondReviewTaskMapper secondReviewTaskMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 创建材料请求
     */
    @Transactional
    public MaterialRequest createMaterialRequest(Long productId, String requesterType,
                                                Long requesterTaskId, Long adminId,
                                                List<String> requiredMaterials, String requestReason) {
        log.info("创建材料请求，商品ID: {}, 请求类型: {}, 任务ID: {}, 管理员ID: {}",
                productId, requesterType, requesterTaskId, adminId);

//        // 检查是否已存在相同的材料请求
//        MaterialRequest existingRequest = materialRequestMapper.findByRequesterTask(requesterType, requesterTaskId);
//        if (existingRequest != null) {
//            log.warn("已存在相同的材料请求，请求ID: {}", existingRequest.getRequestId());
//            return existingRequest;
//        }

        // 计算截止时间（7天后）
        LocalDateTime deadline = LocalDateTime.now().plusDays(7);

        MaterialRequest request = MaterialRequest.builder()
                .productId(productId)
                .requesterType(requesterType)
                .requesterTaskId(requesterTaskId)
                .adminId(adminId)
                .requiredMaterials(toJsonString(requiredMaterials))
                .requestReason(requestReason)
                .status("waiting")
                .requestTime(LocalDateTime.now())
                .deadline(deadline)
                .sellerNotified(false)
                .build();

        materialRequestMapper.insert(request);

        log.info("材料请求创建成功，请求ID: {}", request.getRequestId());
        return request;
    }

    /**
     * 获取商品的材料请求列表
     */
    public List<MaterialRequest> getMaterialRequestsByProduct(Long productId) {
        List<MaterialRequest> requests = materialRequestMapper.findByProductId(productId);

        // 补充相关信息
        for (MaterialRequest request : requests) {
            if (request != null) {
                enrichRequestWithDetails(request);
            }
        }

        return requests;
    }

    /**
     * 获取待处理的材料请求
     */
    public List<MaterialRequest> getPendingMaterialRequests() {
        List<MaterialRequest> requests = materialRequestMapper.findPendingRequests();

        // 补充相关信息
        for (MaterialRequest request : requests) {
            if (request != null) {
                enrichRequestWithDetails(request);
            }
        }

        return requests;
    }

    /**
     * 更新材料请求状态
     */
    @Transactional
    public void updateMaterialRequestStatus(Long requestId, String status) {
        MaterialRequest request = materialRequestMapper.selectById(requestId);
        if (request == null) {
            throw new RuntimeException("材料请求不存在");
        }

        request.setStatus(status);
        materialRequestMapper.updateById(request);

        log.info("材料请求状态已更新，请求ID: {}, 新状态: {}", requestId, status);

        // 如果状态更新为已提交，需要重新激活对应的审核任务
        if ("submitted".equals(status)) {
            reactivateAuditTask(request);
        }
    }

    /**
     * 重新激活审核任务
     */
    @Transactional
    public void reactivateAuditTask(MaterialRequest request) {
        log.info("重新激活审核任务，材料请求ID: {}", request.getRequestId());

        String requesterType = request.getRequesterType();
        Long taskId = request.getRequesterTaskId();

        log.info("重新激活审核任务，任务类型: {}, 任务ID: {}", requesterType, taskId);

        try {
            if ("manual_audit".equals(requesterType)) {
                // 更新人工审核任务状态为进行中
                ManualAuditTask auditTask = manualAuditTaskMapper.selectById(taskId);
                if (auditTask != null) {
                    auditTask.setStatus("in_progress");
                    manualAuditTaskMapper.updateById(auditTask);
                    log.info("已重新激活人工审核任务，任务ID: {}", taskId);
                } else {
                    log.warn("人工审核任务不存在，任务ID: {}", taskId);
                }
            } else if ("second_review".equals(requesterType)) {
                // 更新二度复审任务状态为进行中
                SecondReviewTask secondReviewTask = secondReviewTaskMapper.selectById(taskId);
                if (secondReviewTask != null) {
                    // 检查当前状态是否为material_requested
                    if ("material_requested".equals(secondReviewTask.getStatus())) {
                        secondReviewTask.setStatus("in_progress");
                        secondReviewTaskMapper.updateById(secondReviewTask);
                        log.info("已重新激活二度复审任务，任务ID: {}", taskId);

                        // 同时更新商品状态为二度复审中
                        Product product = productMapper.selectById(secondReviewTask.getProductId());
                        if (product != null) {
                            product.setStatus("second_review");
                            productMapper.updateById(product);
                            log.info("商品状态已更新为二度复审中，商品ID: {}", product.getId());
                        }
                    } else {
                        log.warn("二度复审任务状态不正确，无法重新激活，当前状态: {}, 任务ID: {}",
                                secondReviewTask.getStatus(), taskId);
                    }
                } else {
                    log.warn("二度复审任务不存在，任务ID: {}", taskId);
                }
            } else {
                log.warn("未知的请求者类型: {}", requesterType);
            }
        } catch (Exception e) {
            log.error("重新激活审核任务失败，任务类型: {}, 任务ID: {}", requesterType, taskId, e);
        }
    }

    /**
     * 标记卖家已通知
     */
    @Transactional
    public void markSellerNotified(Long requestId) {
        materialRequestMapper.updateNotificationStatus(requestId, true);
        log.info("材料请求已标记为已通知卖家，请求ID: {}", requestId);
    }

    /**
     * 补充请求详细信息
     */
    private void enrichRequestWithDetails(MaterialRequest request) {
        // 添加空值检查
        if (request == null) {
            log.warn("MaterialRequest对象为空，跳过详细信息补充");
            return;
        }

        // 获取商品信息
        Product product = null;
        if (request.getProductId() != null) {
            product = productMapper.selectById(request.getProductId());
            if (product != null) {
                request.setProductTitle(product.getTitle());
            } else {
                log.warn("未找到对应的商品信息，商品ID: {}", request.getProductId());
            }
        }

        // 获取管理员信息
        if (request.getAdminId() != null) {
            User admin = userMapper.selectById(request.getAdminId());
            if (admin != null) {
                request.setAdminNickname(admin.getUsername());
            } else {
                log.warn("未找到对应的管理员信息，管理员ID: {}", request.getAdminId());
            }
        }

        // 获取卖家信息
        if (product != null && product.getSellerId() != null) {
            User seller = userMapper.selectById(product.getSellerId());
            if (seller != null) {
                request.setSellerNickname(seller.getUsername());
            } else {
                log.warn("未找到对应的卖家信息，卖家ID: {}", product.getSellerId());
            }
        }
    }

    /**
     * 根据用户ID获取材料请求（用户端）
     */
    public List<MaterialRequest> getMaterialRequestsByUser(Long userId) {
        log.info("获取用户的材料请求，用户ID: {}", userId);

        // 首先获取用户的所有商品ID
        List<Product> userProducts = productMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Product>()
                .eq("lzhshtp_seller_id", userId)
        );

        if (userProducts.isEmpty()) {
            log.info("用户没有发布任何商品，用户ID: {}", userId);
            return new ArrayList<>();
        }

        // 提取商品ID列表
        List<Long> productIds = userProducts.stream()
            .map(Product::getId)
            .toList();

        log.info("用户商品ID列表: {}", productIds);

        // 根据商品ID列表查询材料请求
        List<MaterialRequest> userRequests = materialRequestMapper.findByProductIds(productIds);

        // 补充详细信息并过滤null值
        List<MaterialRequest> validRequests = new ArrayList<>();
        for (MaterialRequest request : userRequests) {
            if (request != null) {
                enrichRequestWithDetails(request);
                validRequests.add(request);
            } else {
                log.warn("发现null的材料请求对象");
            }
        }

        log.info("找到 {} 个有效材料请求", validRequests.size());
        return validRequests;
    }

    /**
     * 获取材料请求详情（验证用户权限）
     */
    public MaterialRequest getMaterialRequestDetail(Long requestId, Long userId) {
        log.info("获取材料请求详情，请求ID: {}, 用户ID: {}", requestId, userId);

        MaterialRequest request = materialRequestMapper.selectById(requestId);
        if (request == null) {
            return null;
        }

        // 验证用户权限
        Product product = productMapper.selectById(request.getProductId());
        if (product == null || !userId.equals(product.getSellerId())) {
            log.warn("用户无权访问该材料请求，用户ID: {}, 请求ID: {}", userId, requestId);
            return null;
        }

        enrichRequestWithDetails(request);
        return request;
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("转换JSON失败", e);
            return null;
        }
    }
}
