<template>
  <div class="register-container">
    <h2 class="main-title">易转</h2>
    <h3 class="sub-title">用户注册</h3>
    <div class="form-group">
      <label for="username">用户名</label>
      <input 
        type="text" 
        id="username" 
        v-model="registerForm.username" 
        placeholder="请输入用户名"
      />
    </div>
    <div class="form-group">
      <label for="password">密码</label>
      <input 
        type="password" 
        id="password" 
        v-model="registerForm.password" 
        placeholder="请输入密码"
      />
    </div>
    <div class="form-group">
      <label for="confirmPassword">确认密码</label>
      <input 
        type="password" 
        id="confirmPassword" 
        v-model="confirmPassword" 
        placeholder="请再次输入密码"
      />
    </div>
    <div class="form-actions">
      <button @click="handleRegister" :disabled="loading">
        {{ loading ? '注册中...' : '注册' }}
      </button>
    </div>
    <div class="login-prompt">
      已有账户？ <a @click="goToLogin" class="link">返回登录</a>
    </div>
    <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { register } from '@/api/user';
import { useRouter } from 'vue-router';

const router = useRouter();
const loading = ref(false);
const errorMessage = ref('');
const confirmPassword = ref('');
const registerForm = reactive({
  username: '',
  password: ''
});

const handleRegister = async () => {
  // 表单验证
  if (!registerForm.username || !registerForm.password) {
    errorMessage.value = '请填写用户名和密码';
    return;
  }
  
  if (registerForm.password !== confirmPassword.value) {
    errorMessage.value = '两次输入的密码不一致';
    return;
  }
  
  try {
    loading.value = true;
    errorMessage.value = '';
    
    const res = await register(registerForm);
    console.log('注册响应:', res);
    
    // 检查响应格式，处理ApiResponse包装
    if (res.code === 200) {
      // 注册成功，跳转到登录页
      alert(res.message || '注册成功，请登录');
      goToLogin();
    } else {
      errorMessage.value = res.message || '注册失败，请稍后再试';
    }
  } catch (error) {
    console.error('注册错误:', error);
    if (error.response && error.response.data) {
      errorMessage.value = error.response.data.message || '注册失败，请稍后再试';
    } else {
      errorMessage.value = error.message || '注册失败，请稍后再试';
    }
  } finally {
    loading.value = false;
  }
};

const goToLogin = () => {
  router.push('/login');
};
</script>

<style scoped>
.register-container {
  width: 450px; /* 设置固定宽度 */
  margin: 100px auto;
  padding: 40px 50px;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.main-title {
  text-align: center;
  margin-bottom: 8px;
  color: #e04040;
  font-size: 2.5em;
  font-weight: bold;
}

.sub-title {
  text-align: center;
  margin-bottom: 30px;
  color: #666;
  font-size: 1.4em;
}

.form-group {
  margin-bottom: 25px; /* 增大表单组之间的间距 */
}

label {
  display: block;
  margin-bottom: 10px; /* 增大标签和输入框之间的间距 */
  font-weight: bold;
  color: #555;
  font-size: 1.2em; /* 增大标签字体 */
}

input {
  width: 100%;
  padding: 15px 20px; /* 增大输入框内边距 */
  border: 1px solid #ddd;
  border-radius: 6px; /* 增大圆角 */
  font-size: 1.2em; /* 增大输入框字体 */
  background-color: #f8f9fa; /* 添加浅色背景 */
}

.form-actions {
  margin-top: 30px; /* 增大按钮上方间距 */
  text-align: center;
}

button {
  padding: 15px 20px; /* 增大按钮内边距 */
  border: none;
  border-radius: 6px; /* 增大圆角 */
  cursor: pointer;
  font-size: 1.3em; /* 增大按钮字体 */
  background-color: #e04040;
  color: white;
  width: 100%;
  font-weight: bold; /* 加粗按钮文字 */
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.login-prompt {
  text-align: center;
  margin-top: 25px; /* 增大链接上方间距 */
  font-size: 1.1em; /* 增大链接字体 */
  color: #555;
}

.login-prompt .link {
  color: #2196F3;
  text-decoration: none;
  cursor: pointer;
}

.error-message {
  color: red;
  text-align: center;
  margin-top: 20px;
  font-size: 1.1em;
}
</style> 