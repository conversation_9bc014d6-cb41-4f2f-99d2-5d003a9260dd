package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lzhshtp.shangcheng.model.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 查询买家订单列表
     */
    @Select("SELECT " +
            "lzhshtp_order_id as orderId, " +
            "lzhshtp_product_id as productId, " +
            "lzhshtp_buyer_id as buyerId, " +
            "lzhshtp_seller_id as sellerId, " +
            "lzhshtp_order_date as orderDate, " +
            "lzhshtp_total_amount as totalAmount, " +
            "lzhshtp_status as status, " +
            "lzhshtp_shipping_address as shippingAddress, " +
            "lzhshtp_payment_method as paymentMethod, " +
            "lzhshtp_transaction_id as transactionId " +
            "FROM tb_lzhshtp_orders WHERE lzhshtp_buyer_id = #{buyerId} ORDER BY lzhshtp_order_date DESC")
    IPage<Order> selectBuyerOrders(Page<Order> page, @Param("buyerId") Long buyerId);
    
    /**
     * 查询卖家订单列表
     */
    @Select("SELECT " +
            "lzhshtp_order_id as orderId, " +
            "lzhshtp_product_id as productId, " +
            "lzhshtp_buyer_id as buyerId, " +
            "lzhshtp_seller_id as sellerId, " +
            "lzhshtp_order_date as orderDate, " +
            "lzhshtp_total_amount as totalAmount, " +
            "lzhshtp_status as status, " +
            "lzhshtp_shipping_address as shippingAddress, " +
            "lzhshtp_payment_method as paymentMethod, " +
            "lzhshtp_transaction_id as transactionId " +
            "FROM tb_lzhshtp_orders WHERE lzhshtp_seller_id = #{sellerId} ORDER BY lzhshtp_order_date DESC")
    IPage<Order> selectSellerOrders(Page<Order> page, @Param("sellerId") Long sellerId);
    
    /**
     * 根据状态查询买家订单列表
     */
    @Select("SELECT " +
            "lzhshtp_order_id as orderId, " +
            "lzhshtp_product_id as productId, " +
            "lzhshtp_buyer_id as buyerId, " +
            "lzhshtp_seller_id as sellerId, " +
            "lzhshtp_order_date as orderDate, " +
            "lzhshtp_total_amount as totalAmount, " +
            "lzhshtp_status as status, " +
            "lzhshtp_shipping_address as shippingAddress, " +
            "lzhshtp_payment_method as paymentMethod, " +
            "lzhshtp_transaction_id as transactionId " +
            "FROM tb_lzhshtp_orders WHERE lzhshtp_buyer_id = #{buyerId} AND lzhshtp_status = #{status} ORDER BY lzhshtp_order_date DESC")
    IPage<Order> selectBuyerOrdersByStatus(Page<Order> page, @Param("buyerId") Long buyerId, @Param("status") String status);
    
    /**
     * 根据状态查询卖家订单列表
     */
    @Select("SELECT " +
            "lzhshtp_order_id as orderId, " +
            "lzhshtp_product_id as productId, " +
            "lzhshtp_buyer_id as buyerId, " +
            "lzhshtp_seller_id as sellerId, " +
            "lzhshtp_order_date as orderDate, " +
            "lzhshtp_total_amount as totalAmount, " +
            "lzhshtp_status as status, " +
            "lzhshtp_shipping_address as shippingAddress, " +
            "lzhshtp_payment_method as paymentMethod, " +
            "lzhshtp_transaction_id as transactionId " +
            "FROM tb_lzhshtp_orders WHERE lzhshtp_seller_id = #{sellerId} AND lzhshtp_status = #{status} ORDER BY lzhshtp_order_date DESC")
    IPage<Order> selectSellerOrdersByStatus(Page<Order> page, @Param("sellerId") Long sellerId, @Param("status") String status);
    
    /**
     * 管理员查询订单列表（支持关键词搜索和状态筛选）
     */
    @Select("<script>" +
            "SELECT o.lzhshtp_order_id as orderId, " +
            "o.lzhshtp_product_id as productId, " +
            "o.lzhshtp_buyer_id as buyerId, " +
            "o.lzhshtp_seller_id as sellerId, " +
            "o.lzhshtp_order_date as orderDate, " +
            "o.lzhshtp_total_amount as totalAmount, " +
            "o.lzhshtp_status as status, " +
            "o.lzhshtp_shipping_address as shippingAddress, " +
            "o.lzhshtp_payment_method as paymentMethod, " +
            "o.lzhshtp_transaction_id as transactionId " +
            "FROM tb_lzhshtp_orders o " +
            "LEFT JOIN tb_lzhshtp_products p ON o.lzhshtp_product_id = p.lzhshtp_product_id " +
            "LEFT JOIN tb_lzhshtp_users buyer ON o.lzhshtp_buyer_id = buyer.lzhshtp_user_id " +
            "LEFT JOIN tb_lzhshtp_users seller ON o.lzhshtp_seller_id = seller.lzhshtp_user_id " +
            "<where>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "    (o.lzhshtp_order_id LIKE CONCAT('%', #{keyword}, '%') " +
            "    OR buyer.lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') " +
            "    OR seller.lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') " +
            "    OR p.lzhshtp_title LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "    AND o.lzhshtp_status = #{status}" +
            "</if>" +
            "</where>" +
            "ORDER BY o.lzhshtp_order_date DESC" +
            "</script>")
    IPage<Order> selectAdminOrders(Page<Order> page, @Param("keyword") String keyword, @Param("status") String status);
} 