package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.CategoryDTO;
import com.lzhshtp.shangcheng.mapper.CategoryMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.model.Category;
import com.lzhshtp.shangcheng.service.CategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Override
    public List<CategoryDTO> getAllCategoriesTree() {
        // 获取所有根分类
        List<Category> rootCategories = categoryMapper.selectRootCategories();

        // 转换为DTO并构建树形结构，过滤掉null值
        return rootCategories.stream()
                .filter(category -> category != null)  // 过滤null值
                .map(this::buildCategoryTree)
                .filter(dto -> dto != null)  // 过滤转换后的null值
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CategoryDTO> getAllCategories() {
        // 获取所有分类
        List<Category> allCategories = categoryMapper.selectList(null);
        
        // 转换为DTO
        return allCategories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public CategoryDTO getCategoryById(Integer categoryId) {
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            return null;
        }
        return convertToDTO(category);
    }
    
    @Override
    @Transactional
    public Integer createCategory(CategoryDTO categoryDTO) {
        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);
        
        categoryMapper.insert(category);
        return category.getId();
    }
    
    @Override
    @Transactional
    public boolean updateCategory(Integer categoryId, CategoryDTO categoryDTO) {
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            return false;
        }
        
        BeanUtils.copyProperties(categoryDTO, category);
        category.setId(categoryId);  // 确保ID不变
        
        return categoryMapper.updateById(category) > 0;
    }
    
    @Override
    @Transactional
    public boolean deleteCategory(Integer categoryId) {
        // 检查是否有子分类
        LambdaQueryWrapper<Category> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(Category::getParentId, categoryId);
        long childCount = categoryMapper.selectCount(childQuery);
        if (childCount > 0) {
            return false;  // 存在子分类，不能删除
        }
        
        // 检查是否有商品使用该分类
        LambdaQueryWrapper<com.lzhshtp.shangcheng.model.Product> productQuery = new LambdaQueryWrapper<>();
        productQuery.eq(com.lzhshtp.shangcheng.model.Product::getCategoryId, categoryId);
        long productCount = productMapper.selectCount(productQuery);
        if (productCount > 0) {
            return false;  // 存在商品使用该分类，不能删除
        }
        
        return categoryMapper.deleteById(categoryId) > 0;
    }
    
    /**
     * 递归构建分类树
     */
    private CategoryDTO buildCategoryTree(Category category) {
        if (category == null) {
            return null;
        }

        CategoryDTO dto = convertToDTO(category);
        if (dto == null) {
            return null;
        }

        // 获取子分类
        List<Category> children = categoryMapper.selectCategoriesByParentId(category.getId());
        if (children != null && !children.isEmpty()) {
            List<CategoryDTO> childrenDTOs = children.stream()
                    .filter(child -> child != null)  // 过滤null值
                    .map(this::buildCategoryTree)
                    .filter(childDto -> childDto != null)  // 过滤转换后的null值
                    .collect(Collectors.toList());
            dto.setChildren(childrenDTOs);
        } else {
            dto.setChildren(new ArrayList<>());
        }

        return dto;
    }
    
    /**
     * 将Category实体转换为CategoryDTO
     */
    private CategoryDTO convertToDTO(Category category) {
        if (category == null) {
            return null;
        }
        CategoryDTO dto = new CategoryDTO();
        BeanUtils.copyProperties(category, dto);
        return dto;
    }
} 