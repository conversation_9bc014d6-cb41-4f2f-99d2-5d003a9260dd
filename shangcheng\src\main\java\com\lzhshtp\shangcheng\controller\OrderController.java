package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.OrderCreateRequest;
import com.lzhshtp.shangcheng.dto.OrderDTO;
import com.lzhshtp.shangcheng.dto.OrderCompleteResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.OrderService;
import com.lzhshtp.shangcheng.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/orders")
public class OrderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderService orderService;

    @Autowired
    private UserService userService;

    /**
     * 创建订单
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> createOrder(@Valid @RequestBody OrderCreateRequest request) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 创建订单
            Long orderId = orderService.createOrder(userId, request);
            return ApiResponse.success("订单创建成功", orderId);
        } catch (Exception e) {
            logger.error("创建订单失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<OrderDTO> getOrderById(@PathVariable("id") Long orderId) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取订单详情
            OrderDTO order = orderService.getOrderById(orderId);

            // 检查权限
            if (order == null) {
                return ApiResponse.fail("订单不存在", 404);
            }

            if (!order.getBuyerId().equals(userId) && !order.getSellerId().equals(userId)) {
                return ApiResponse.fail("无权查看此订单", 403);
            }

            return ApiResponse.success(order);
        } catch (Exception e) {
            logger.error("获取订单详情失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取买家订单列表
     */
    @GetMapping("/buyer")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<PageResult<OrderDTO>> getBuyerOrders(
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取买家订单列表
            PageResult<OrderDTO> result = orderService.getBuyerOrders(userId, status, page, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取买家订单列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取卖家订单列表
     */
    @GetMapping("/seller")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<PageResult<OrderDTO>> getSellerOrders(
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取卖家订单列表
            PageResult<OrderDTO> result = orderService.getSellerOrders(userId, status, page, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取卖家订单列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/{id}/cancel")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> cancelOrder(@PathVariable("id") Long orderId) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 取消订单
            boolean success = orderService.cancelOrder(orderId, userId);
            if (!success) {
                return ApiResponse.fail("取消订单失败");
            }
            return ApiResponse.success("订单已取消", null);
        } catch (Exception e) {
            logger.error("取消订单失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> updateOrderStatus(
            @PathVariable("id") Long orderId,
            @RequestParam String status) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 更新订单状态
            boolean success = orderService.updateOrderStatus(orderId, status, userId);
            if (!success) {
                return ApiResponse.fail("更新订单状态失败");
            }
            return ApiResponse.success("订单状态已更新", null);
        } catch (Exception e) {
            logger.error("更新订单状态失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 卖家发货
     */
    @PostMapping("/{id}/ship")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> shipOrder(@PathVariable("id") Long orderId) {
        try {
            Long userId = getCurrentUserId();
            orderService.shipOrder(orderId, userId);
            return ApiResponse.success("发货成功，订单状态将稍后更新", null);
        } catch (Exception e) {
            logger.error("发货失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 买家确认收货
     */
    @PostMapping("/{id}/complete")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<OrderCompleteResponse> completeOrder(@PathVariable("id") Long orderId) {
        try {
            Long userId = getCurrentUserId();
            OrderCompleteResponse response = orderService.completeOrder(orderId, userId);
            if (!response.getSuccess()) {
                return ApiResponse.fail(response.getMessage());
            }
            return ApiResponse.success(response.getMessage(), response);
        } catch (Exception e) {
            logger.error("确认收货失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 余额支付订单
     */
    @PostMapping("/{id}/pay/balance")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<String> payWithBalance(@PathVariable("id") Long orderId) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 使用余额支付
            boolean success = orderService.payWithBalance(orderId, userId);
            if (success) {
                return ApiResponse.success("支付成功", "订单支付成功");
            } else {
                return ApiResponse.fail("支付失败");
            }
        } catch (Exception e) {
            logger.error("余额支付失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取订单支付信息（包含用户余额）
     */
    @GetMapping("/{id}/payment-info")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Map<String, Object>> getPaymentInfo(@PathVariable("id") Long orderId) {
        try {
            // 获取当前用户ID
            Long userId = getCurrentUserId();

            // 获取支付信息
            Map<String, Object> paymentInfo = orderService.getPaymentInfo(orderId, userId);
            return ApiResponse.success("获取支付信息成功", paymentInfo);
        } catch (Exception e) {
            logger.error("获取支付信息失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 支付宝同步回调
     */
    @GetMapping("/pay/return")
    public void alipayReturn(HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("收到支付宝同步回调，开始处理...");

        try {
            // 将支付宝回调的参数转为Map
            Map<String, String> params = new HashMap<>();
            request.getParameterMap().forEach((key, values) -> {
                String value = values[0];
                params.put(key, value);
            });

            // 【重要】在这里调用异步通知的处理逻辑，模拟状态更新
            // 在生产环境中，这部分逻辑应该只在异步通知中执行
            boolean success = orderService.handleAlipayCallback(params);
            if (success) {
                logger.info("同步回调触发订单状态更新成功。");
            } else {
                logger.warn("同步回调触发订单状态更新失败或已处理。");
            }
        } catch (Exception e) {
            logger.error("同步回调处理订单状态时发生异常", e);
            // 即使处理失败，也要确保用户能跳转到前端页面
        }

        // 从支付宝回调的请求中获取订单号
        String orderId = request.getParameter("out_trade_no");

        // 拼接前端支付成功页面的URL，并带上订单号作为查询参数
        String frontendSuccessUrl = "http://localhost:5000/order/success?orderId=" + orderId;

        // 重定向到前端的支付成功页面
        logger.info("处理完成，重定向到前端页面: {}", frontendSuccessUrl);
        response.sendRedirect(frontendSuccessUrl);
    }

    /**
     * 支付宝异步通知
     */
    //不进行内外穿透，暂时无效，更新由同步回调完成。
    @PostMapping("/pay/notify")
    public String alipayNotify(HttpServletRequest request) {
        logger.info("收到支付宝异步通知");

        // 获取所有请求参数
        Map<String, String> params = new HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            String value = values[0];
            params.put(key, value);
        });

        // 处理回调
        boolean success = orderService.handleAlipayCallback(params);

        // 返回结果
        return success ? "success" : "fail";
    }

    /**
     * 管理员查询所有订单
     */
    @GetMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<PageResult<OrderDTO>> getAdminOrders(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            logger.info("管理员查询订单，关键词: {}, 状态: {}, 页码: {}, 每页数量: {}", keyword, status, page, pageSize);

            // 调用服务层方法查询订单
            PageResult<OrderDTO> result = orderService.getAdminOrders(keyword, status, page, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("管理员查询订单失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return user.getUserId();
    }
}
