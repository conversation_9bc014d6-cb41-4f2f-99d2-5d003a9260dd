<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论坛 - 二手交易平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #e53935;
            --dark-red: #c62828;
            --light-grey: #f0f2f5;
            --medium-grey: #e8e8e8;
            --dark-grey: #333;
            --text-color-medium: #555;
            --text-color-light: #777;
            --white: #ffffff;
            --border-color: #eee;
            --shadow-light: rgba(0,0,0,0.05);
            --shadow-medium: rgba(0,0,0,0.1);
            --shadow-dark: rgba(0,0,0,0.15);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--light-grey);
            margin: 0;
            padding: 0;
            color: var(--dark-grey);
            line-height: 1.6;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        .top-header {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 10px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px var(--shadow-medium);
        }
        .top-header .logo {
            font-size: 24px;
            font-weight: 700;
        }
        .top-header .search-box {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 600px;
            margin: 0 20px;
        }
        .top-header .search-box input {
            flex-grow: 1;
            max-width: 400px;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            margin-right: 10px;
            background-color: rgba(255,255,255,0.2);
            color: var(--white);
            outline: none;
            transition: background-color 0.3s ease;
        }
        .top-header .search-box input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        .top-header .search-box input:focus {
            background-color: rgba(255,255,255,0.3);
        }
        .top-header .search-box button {
            background-color: var(--dark-red);
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            color: var(--white);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .top-header .search-box button:hover {
            background-color: #a01a1a;
        }

        .top-nav-right {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .top-nav-right .nav-icon-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--white);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s ease;
        }
        .top-nav-right .nav-icon-link:hover {
            color: rgba(255,255,255,0.8);
        }

        .secondary-nav {
            background-color: var(--dark-red);
            padding: 10px 30px;
            box-shadow: 0 2px 5px var(--shadow-light);
        }
        .secondary-nav .inner-nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 25px;
        }
        .secondary-nav a {
            color: var(--white);
            font-weight: 500;
            padding: 5px 0;
            position: relative;
            transition: color 0.3s ease;
        }
        .secondary-nav a.active::after, .secondary-nav a:hover::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -5px;
            width: 100%;
            height: 3px;
            background-color: var(--white);
            border-radius: 2px;
        }

        .main-wrapper {
            display: flex;
            justify-content: center;
            gap: 20px;
            max-width: 1200px;
            margin: 30px auto;
            align-items: flex-start;
        }

        .sidebar-left,
        .sidebar-right {
            flex-shrink: 0;
            width: 250px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            padding: 20px;
        }

        .main-content {
            flex-grow: 1;
            max-width: 650px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            padding: 20px;
        }

        .module-header {
            font-size: 1.2em;
            font-weight: 600;
            color: var(--primary-red);
            padding-bottom: 10px;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--medium-grey);
        }
        .module-header span {
            color: var(--text-color-light);
            font-size: 0.9em;
            font-weight: 400;
            float: right;
            cursor: pointer;
        }

        .my-focus-module {
            margin-bottom: 30px;
        }
        .my-focus-module .login-prompt {
            background-color: var(--light-grey);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            font-size: 0.95em;
            color: var(--text-color-medium);
        }
        .my-focus-module .login-prompt a {
            color: var(--primary-red);
            font-weight: 500;
            margin-left: 5px;
            transition: color 0.3s ease;
        }
        .my-focus-module .login-prompt a:hover {
            color: var(--dark-red);
            text-decoration: underline;
        }

        .category-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .category-list li {
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .category-list li:last-child {
            border-bottom: none;
        }
        .category-list a {
            display: block;
            color: var(--dark-grey);
            font-weight: 400;
            transition: color 0.3s ease;
        }
        .category-list a:hover {
            color: var(--primary-red);
        }
        .category-list .sub-categories {
            margin-top: 5px;
            font-size: 0.9em;
            color: var(--text-color-light);
        }
        .category-list .sub-categories a {
            display: inline-block;
            margin-right: 10px;
            color: var(--text-color-light);
        }
        .category-list .sub-categories a:hover {
            color: var(--primary-red);
        }

        .post-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .post-list li {
            display: flex;
            align-items: flex-start;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .post-list li:last-child {
            border-bottom: none;
        }
        .post-list .post-meta {
            flex-grow: 1;
            margin-right: 15px;
        }
        .post-list .post-meta .title {
            font-size: 1.1em;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .post-list .post-meta .title a {
            color: var(--dark-grey);
            transition: color 0.3s ease;
        }
        .post-list .post-meta .title a:hover {
            color: var(--primary-red);
        }
        .post-list .post-meta .info {
            font-size: 0.85em;
            color: var(--text-color-light);
        }
        .post-list .post-stats {
            text-align: right;
            font-size: 0.85em;
            color: var(--text-color-medium);
            white-space: nowrap;
        }
        .post-list .post-stats span {
            display: block;
        }
        .post-list .post-category {
            background-color: var(--medium-grey);
            color: var(--text-color-medium);
            padding: 3px 8px;
            border-radius: 5px;
            font-size: 0.8em;
            margin-left: 10px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        .action-buttons .btn {
            flex: 1;
            padding: 20px 10px;
            text-align: center;
            background-color: var(--light-grey);
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 8px var(--shadow-light);
        }
        .action-buttons .btn:hover {
            background-color: var(--medium-grey);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-medium);
        }
        .action-buttons .btn svg {
            width: 30px;
            height: 30px;
            margin-bottom: 10px;
            color: var(--primary-red);
        }
        .action-buttons .btn span {
            display: block;
            font-size: 1em;
            font-weight: 500;
            color: var(--dark-grey);
        }

        .hot-searches ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .hot-searches li {
            padding: 8px 0;
            font-size: 0.95em;
            border-bottom: 1px dashed var(--border-color);
        }
        .hot-searches li:last-child {
            border-bottom: none;
        }
        .hot-searches li .rank {
            display: inline-block;
            width: 20px;
            text-align: center;
            font-weight: 700;
            color: var(--text-color-light);
            margin-right: 5px;
        }
        .hot-searches li:nth-child(1) .rank,
        .hot-searches li:nth-child(2) .rank,
        .hot-searches li:nth-child(3) .rank {
            color: var(--primary-red);
        }
        .hot-searches li a {
            transition: color 0.3s ease;
        }
        .hot-searches li a:hover {
            color: var(--primary-red);
        }

        .chat-window {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            height: 400px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 8px 30px var(--shadow-medium);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            transform: translateY(100%) scale(0.8);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .chat-window.open {
            transform: translateY(0) scale(1);
            opacity: 1;
            visibility: visible;
        }

        .chat-header {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1em;
            font-weight: 600;
            border-bottom: 1px solid var(--dark-red);
        }

        .chat-header h3 {
            margin: 0;
            color: var(--white);
        }

        .chat-header .close-chat-btn {
            background: none;
            border: none;
            color: var(--white);
            font-size: 1.5em;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .chat-header .close-chat-btn:hover {
            color: rgba(255,255,255,0.8);
        }

        .chat-body {
            flex-grow: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: var(--light-grey);
            display: flex;
            flex-direction: column;
        }

        .message {
            margin-bottom: 10px;
            display: flex;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message.received {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 75%;
            padding: 10px 15px;
            border-radius: 18px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.sent .message-bubble {
            background-color: var(--primary-red);
            color: var(--white);
            border-bottom-right-radius: 5px;
        }

        .message.received .message-bubble {
            background-color: var(--white);
            color: var(--dark-grey);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            display: flex;
            padding: 10px 15px;
            border-top: 1px solid var(--border-color);
            background-color: var(--white);
        }

        .chat-input input {
            flex-grow: 1;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 0.95em;
            margin-right: 10px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .chat-input input:focus {
            border-color: var(--primary-red);
        }

        .chat-input button {
            background-color: var(--primary-red);
            color: var(--white);
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 0.95em;
            transition: background-color 0.2s ease;
        }

        .chat-input button:hover {
            background-color: var(--dark-red);
        }
    </style>
</head>
<body>
    <div class="top-header">
        <a href="main.html" class="logo">二手交易</a>
        <div class="search-box">
            <input type="text" placeholder="输入关键词">
            <button>搜索</button>
        </div>
        <div class="top-nav-right">
            <a href="#" class="nav-icon-link" id="customerServiceLink">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 15C21 16.6569 19.6569 18 18 18H7.993C7.69741 18 7.41117 18.1215 7.20017 18.3475L4 22V5C4 3.34315 5.34315 2 7 2H17C18.6569 2 20 3.34315 20 5V15H21ZM12 8C12.5523 8 13 8.44772 13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8ZM12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11ZM12 14C12.5523 14 13 14.4477 13 15C13 15.5522 12.5523 16 12 16C11.4477 16 11 15.5522 11 15C11 14.4477 11.4477 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>客服</span>
            </a>
        </div>
    </div>

    <div class="secondary-nav">
        <div class="inner-nav">
            <a href="#" class="active">全部话题</a>
            <a href="#">数码</a>
            <a href="#">服饰</a>
            <a href="#">家居</a>
            <a href="#">图书</a>
            <a href="#">虚拟</a>
            <a href="#">其他</a>
            <a href="#">我的</a>
        </div>
    </div>

    <div class="main-wrapper">
        <div class="sidebar-left">
            <div class="my-focus-module">
                <div class="module-header">我的关注 <span class="icon-fold">收起 ^</span></div>
                <div class="login-prompt">
                    登录后的世界更精彩！ <a href="login.html">登录</a>
                </div>
            </div>

            <div class="category-list">
                <div class="module-header">话题分类 <span class="icon-fold">收起 ^</span></div>
                <ul>
                    <li>
                        <a href="#">数码产品</a>
                        <div class="sub-categories">
                            <a href="#">手机</a> <a href="#">电脑</a> <a href="#">平板</a> <a href="#">相机</a>
                        </div>
                    </li>
                    <li>
                        <a href="#">服饰箱包</a>
                        <div class="sub-categories">
                            <a href="#">男装</a> <a href="#">女装</a> <a href="#">鞋履</a> <a href="#">包包</a>
                        </div>
                    </li>
                    <li>
                        <a href="#">家居用品</a>
                        <div class="sub-categories">
                            <a href="#">家具</a> <a href="#">家电</a> <a href="#">厨房</a> <a href="#">日用</a>
                        </div>
                    </li>
                    <li>
                        <a href="#">图书文具</a>
                        <div class="sub-categories">
                            <a href="#">教材</a> <a href="#">小说</a> <a href="#">文具</a> <a href="#">画材</a>
                        </div>
                    </li>
                    <li>
                        <a href="#">虚拟物品</a>
                        <div class="sub-categories">
                            <a href="#">游戏账号</a> <a href="#">电子书</a> <a href="#">优惠券</a>
                        </div>
                    </li>
                    <li>
                        <a href="#">其他分类</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="main-content">
            <div class="module-header">热门话题</div>
            <div class="post-list">
                <ul>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">求购一台八成新iPad Pro，预算3000元左右，面交</a></div>
                            <div class="info">发布者: 小明 · 2小时前</div>
                        </div>
                        <div class="post-stats">
                            <span>12 回复</span>
                            <span>150 浏览</span>
                        </div>
                        <div class="post-category">数码产品</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">出售99新iPhone 13 Pro Max，国行256G，可小刀</a></div>
                            <div class="info">发布者: 小红 · 5小时前</div>
                        </div>
                        <div class="post-stats">
                            <span>8 回复</span>
                            <span>230 浏览</span>
                        </div>
                        <div class="post-category">数码产品</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">大家平时都去哪里淘二手书？有什么推荐的平台或渠道吗？</a></div>
                            <div class="info">发布者: 老王 · 1天前</div>
                        </div>
                        <div class="post-stats">
                            <span>25 回复</span>
                            <span>400 浏览</span>
                        </div>
                        <div class="post-category">图书文具</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">关于二手交易安全的一些小建议，避免踩坑！</a></div>
                            <div class="info">发布者: 官方小助手 · 2天前</div>
                        </div>
                        <div class="post-stats">
                            <span>50 回复</span>
                            <span>800 浏览</span>
                        </div>
                        <div class="post-category">交易指南</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">求助：旧电脑升级硬件，CPU和显卡如何选择？</a></div>
                            <div class="info">发布者: 科技迷 · 3天前</div>
                        </div>
                        <div class="post-stats">
                            <span>18 回复</span>
                            <span>320 浏览</span>
                        </div>
                        <div class="post-category">数码产品</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">分享最近淘到的vintage包包，超划算！</a></div>
                            <div class="info">发布者: 时尚达人 · 4天前</div>
                        </div>
                        <div class="post-stats">
                            <span>30 回复</span>
                            <span>600 浏览</span>
                        </div>
                        <div class="post-category">服饰箱包</div>
                    </li>
                    <li>
                        <div class="post-meta">
                            <div class="title"><a href="post-detail.html">急转租：市中心一室一厅，拎包入住，租金可议</a></div>
                            <div class="info">发布者: 房东小李 · 5天前</div>
                        </div>
                        <div class="post-stats">
                            <span>7 回复</span>
                            <span>200 浏览</span>
                        </div>
                        <div class="post-category">家居生活</div>
                    </li>
                </ul>
            </div>
        </div>

        <div class="sidebar-right">
            <div class="publish-section">
                <button class="publish-button" onclick="location.href='create-post.html'">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                    发帖子
                </button>
            </div>

            <div class="hot-searches">
                <div class="module-header">热门搜索</div>
                <ul>
                    <li><span class="rank">1</span> <a href="#">iPhone 14 Pro Max</a></li>
                    <li><span class="rank">2</span> <a href="#">机械键盘</a></li>
                    <li><span class="rank">3</span> <a href="#">二手书籍</a></li>
                    <li><span class="rank">4</span> <a href="#">阿迪达斯球鞋</a></li>
                    <li><span class="rank">5</span> <a href="#">switch游戏机</a></li>
                    <li><span class="rank">6</span> <a href="#">戴森吹风机</a></li>
                    <li><span class="rank">7</span> <a href="#">考研资料</a></li>
                    <li><span class="rank">8</span> <a href="#">露营装备</a></li>
                    <li><span class="rank">9</span> <a href="#">闲置家电</a></li>
                    <li><span class="rank">10</span> <a href="#">PS5</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 客服聊天窗口 HTML -->
    <div id="chat-window" class="chat-window">
        <div class="chat-header">
            <h3>客服中心</h3>
            <button class="close-chat-btn">&times;</button>
        </div>
        <div class="chat-body" id="chat-body">
            <!-- 聊天消息会在这里显示 -->
            <div class="message received">
                <div class="message-bubble">您好，有什么可以帮助您的？</div>
            </div>
            <div class="message sent">
                <div class="message-bubble">我想咨询一下关于商品发布的问题。</div>
            </div>
        </div>
        <div class="chat-input">
            <input type="text" id="chat-message-input" placeholder="输入消息...">
            <button id="send-message-btn">发送</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const customerServiceLink = document.getElementById('customerServiceLink');
            const chatWindow = document.getElementById('chat-window');
            const closeChatBtn = document.querySelector('.close-chat-btn');
            const chatBody = document.getElementById('chat-body');
            const chatMessageInput = document.getElementById('chat-message-input');
            const sendMessageBtn = document.getElementById('send-message-btn');

            customerServiceLink.addEventListener('click', function(e) {
                e.preventDefault();
                chatWindow.classList.toggle('open');
                if (chatWindow.classList.contains('open')) {
                    chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部
                }
            });

            closeChatBtn.addEventListener('click', function() {
                chatWindow.classList.remove('open');
            });

            sendMessageBtn.addEventListener('click', sendMessage);
            chatMessageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            function sendMessage() {
                const messageText = chatMessageInput.value.trim();
                if (messageText !== '') {
                    const messageElement = document.createElement('div');
                    messageElement.classList.add('message', 'sent');
                    messageElement.innerHTML = `<div class="message-bubble">${messageText}</div>`;
                    chatBody.appendChild(messageElement);
                    chatMessageInput.value = '';
                    chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部

                    // 模拟客服回复
                    setTimeout(() => {
                        const replyElement = document.createElement('div');
                        replyElement.classList.add('message', 'received');
                        replyElement.innerHTML = `<div class="message-bubble">您的问题已收到，请稍候，客服将尽快回复。</div>`;
                        chatBody.appendChild(replyElement);
                        chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部
                    }, 1000);
                }
            }
        });
    </script>
</body>
</html> 