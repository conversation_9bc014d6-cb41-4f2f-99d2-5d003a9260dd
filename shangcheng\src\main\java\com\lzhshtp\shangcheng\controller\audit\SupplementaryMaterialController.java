package com.lzhshtp.shangcheng.controller.audit;


import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.SupplementaryMaterial;
import com.lzhshtp.shangcheng.service.audit.SupplementaryMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 补充材料控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/audit/supplementary-materials")
public class SupplementaryMaterialController {

    @Autowired
    private SupplementaryMaterialService supplementaryMaterialService;

    /**
     * 根据材料请求ID获取补充材料
     */
    @GetMapping("/request/{requestId}")
    public ApiResponse<List<SupplementaryMaterial>> getMaterialsByRequestId(@PathVariable Long requestId) {
        try {
            log.info("获取材料请求的补充材料，请求ID: {}", requestId);

            List<SupplementaryMaterial> materials = supplementaryMaterialService.getMaterialsByRequestId(requestId);

            return ApiResponse.success("获取补充材料成功", materials);
        } catch (Exception e) {
            log.error("获取补充材料失败，请求ID: {}", requestId, e);
            return ApiResponse.fail("获取补充材料失败：" + e.getMessage());
        }
    }

    /**
     * 根据商品ID获取补充材料
     */
    @GetMapping("/product/{productId}")
    public ApiResponse<List<SupplementaryMaterial>> getMaterialsByProductId(@PathVariable Long productId) {
        try {
            log.info("获取商品的补充材料，商品ID: {}", productId);

            List<SupplementaryMaterial> materials = supplementaryMaterialService.getMaterialsByProductId(productId);

            return ApiResponse.success("获取补充材料成功", materials);
        } catch (Exception e) {
            log.error("获取补充材料失败，商品ID: {}", productId, e);
            return ApiResponse.fail("获取补充材料失败：" + e.getMessage());
        }
    }

    /**
     * 获取未审核的补充材料
     */
    @GetMapping("/unreviewed")
    public ApiResponse<List<SupplementaryMaterial>> getUnreviewedMaterials() {
        try {
            log.info("获取未审核的补充材料");

            List<SupplementaryMaterial> materials = supplementaryMaterialService.getUnreviewedMaterials();

            return ApiResponse.success("获取未审核材料成功", materials);
        } catch (Exception e) {
            log.error("获取未审核材料失败", e);
            return ApiResponse.fail("获取未审核材料失败：" + e.getMessage());
        }
    }

    /**
     * 更新材料审核状态
     */
    @PutMapping("/{materialId}/review")
    public ApiResponse<Void> updateReviewStatus(
            @PathVariable Long materialId,
            @RequestParam Boolean reviewed,
            @RequestParam(required = false) String comments) {
        try {
            log.info("更新材料审核状态，材料ID: {}, 已审核: {}", materialId, reviewed);

            supplementaryMaterialService.updateReviewStatus(materialId, reviewed, comments);

            return ApiResponse.successVoid("更新审核状态成功");
        } catch (Exception e) {
            log.error("更新审核状态失败，材料ID: {}", materialId, e);
            return ApiResponse.fail("更新审核状态失败：" + e.getMessage());
        }
    }
}
