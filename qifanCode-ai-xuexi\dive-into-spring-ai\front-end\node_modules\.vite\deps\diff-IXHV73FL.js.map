{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/diff.js"], "sourcesContent": ["var TOKEN_NAMES = {\n  '+': 'inserted',\n  '-': 'deleted',\n  '@': 'meta'\n};\n\nexport const diff = {\n  name: \"diff\",\n  token: function(stream) {\n    var tw_pos = stream.string.search(/[\\t ]+?$/);\n\n    if (!stream.sol() || tw_pos === 0) {\n      stream.skipToEnd();\n      return (\"error \" + (\n        TOKEN_NAMES[stream.string.charAt(0)] || '')).replace(/ $/, '');\n    }\n\n    var token_name = TOKEN_NAMES[stream.peek()] || stream.skipToEnd();\n\n    if (tw_pos === -1) {\n      stream.skipToEnd();\n    } else {\n      stream.pos = tw_pos;\n    }\n\n    return token_name;\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,cAAc;AAAA,EAChB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ;AACtB,QAAI,SAAS,OAAO,OAAO,OAAO,UAAU;AAE5C,QAAI,CAAC,OAAO,IAAI,KAAK,WAAW,GAAG;AACjC,aAAO,UAAU;AACjB,cAAQ,YACN,YAAY,OAAO,OAAO,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,MAAM,EAAE;AAAA,IACjE;AAEA,QAAI,aAAa,YAAY,OAAO,KAAK,CAAC,KAAK,OAAO,UAAU;AAEhE,QAAI,WAAW,IAAI;AACjB,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,MAAM;AAAA,IACf;AAEA,WAAO;AAAA,EACT;AACF;", "names": []}