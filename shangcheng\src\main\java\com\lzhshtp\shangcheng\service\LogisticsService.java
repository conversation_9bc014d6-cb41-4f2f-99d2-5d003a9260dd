package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.LogisticsTrackingDTO;

import java.util.List;

/**
 * 物流服务接口
 */
public interface LogisticsService {
    
    /**
     * 创建物流跟踪记录
     * 
     * @param tracking 物流跟踪信息
     * @return 物流跟踪ID
     */
    Long createLogisticsTracking(LogisticsTrackingDTO tracking);
    
    /**
     * 更新物流状态
     * 
     * @param trackingId 物流跟踪ID
     * @param status 新状态
     * @param location 当前位置
     * @return 是否更新成功
     */
    boolean updateLogisticsStatus(Long trackingId, String status, String location);
    
    /**
     * 获取订单物流信息
     * 
     * @param orderId 订单ID
     * @return 物流跟踪列表
     */
    List<LogisticsTrackingDTO> getOrderLogistics(Long orderId);
    
    /**
     * 更新订单当前位置
     * 
     * @param orderId 订单ID
     * @param location 当前位置
     * @return 是否更新成功
     */
    boolean updateOrderLocation(Long orderId, String location);
}
