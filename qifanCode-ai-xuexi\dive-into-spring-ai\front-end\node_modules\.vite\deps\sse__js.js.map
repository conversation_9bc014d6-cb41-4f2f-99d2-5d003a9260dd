{"version": 3, "sources": ["../../sse.js/lib/sse.js"], "sourcesContent": ["/**\n * sse.js - A flexible EventSource polyfill/replacement.\n * https://github.com/mpetazzoni/sse.js\n *\n * Copyright (C) 2016-2024 Maxime Petazzoni <<EMAIL>>.\n * All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n/**\n * @type SSE\n * @param {string} url\n * @param {SSEOptions} options\n * @return {SSE}\n */\nvar SSE = function (url, options) {\n  if (!(this instanceof SSE)) {\n    return new SSE(url, options);\n  }\n\n  /** @type {number} */\n  this.INITIALIZING = -1;\n  /** @type {number} */\n  this.CONNECTING = 0;\n  /** @type {number} */\n  this.OPEN = 1;\n  /** @type {number} */\n  this.CLOSED = 2;\n\n  /** @type {string} */\n  this.url = url;\n\n  options = options || {};\n  this.headers = options.headers || {};\n  this.payload = options.payload !== undefined ? options.payload : '';\n  this.method = options.method || (this.payload && 'POST' || 'GET');\n  this.withCredentials = !!options.withCredentials;\n  this.debug = !!options.debug;\n\n  /** @type {string} */\n  this.FIELD_SEPARATOR = ':';\n\n  /** @type { {[key: string]: [EventListener]} } */\n  this.listeners = {};\n\n  /** @type {XMLHttpRequest} */\n  this.xhr = null;\n  /** @type {number} */\n  this.readyState = this.INITIALIZING;\n  /** @type {number} */\n  this.progress = 0;\n  /** @type {string} */\n  this.chunk = '';\n  /** @type {string} */\n  this.lastEventId = '';\n\n  /**\n   * @type AddEventListener\n   */\n  this.addEventListener = function(type, listener) {\n    if (this.listeners[type] === undefined) {\n      this.listeners[type] = [];\n    }\n\n    if (this.listeners[type].indexOf(listener) === -1) {\n      this.listeners[type].push(listener);\n    }\n  };\n\n  /**\n   * @type RemoveEventListener\n   */\n  this.removeEventListener = function(type, listener) {\n    if (this.listeners[type] === undefined) {\n      return;\n    }\n\n    const filtered = [];\n    this.listeners[type].forEach(function(element) {\n      if (element !== listener) {\n        filtered.push(element);\n      }\n    });\n    if (filtered.length === 0) {\n      delete this.listeners[type];\n    } else {\n      this.listeners[type] = filtered;\n    }\n  };\n\n  /**\n   * @type DispatchEvent\n   */\n  this.dispatchEvent = function(e) {\n    if (!e) {\n      return true;\n    }\n\n    if (this.debug) {\n      console.debug(e);\n    }\n\n    e.source = this;\n\n    const onHandler = 'on' + e.type;\n    if (this.hasOwnProperty(onHandler)) {\n      this[onHandler].call(this, e);\n      if (e.defaultPrevented) {\n        return false;\n      }\n    }\n\n    if (this.listeners[e.type]) {\n      return this.listeners[e.type].every(function(callback) {\n        callback(e);\n        return !e.defaultPrevented;\n      });\n    }\n\n    return true;\n  };\n\n  /** @private */\n  this._setReadyState = function(state) {\n    const event = new CustomEvent('readystatechange');\n    event.readyState = state;\n    this.readyState = state;\n    this.dispatchEvent(event);\n  };\n\n  this._onStreamFailure = function(e) {\n    const event = new CustomEvent('error');\n    event.data = e.currentTarget.response;\n    this.dispatchEvent(event);\n    this.close();\n  }\n\n  this._onStreamAbort = function() {\n    this.dispatchEvent(new CustomEvent('abort'));\n    this.close();\n  }\n\n  /** @private */\n  this._onStreamProgress = function(e) {\n    if (!this.xhr) {\n      return;\n    }\n\n    if (this.xhr.status !== 200) {\n      this._onStreamFailure(e);\n      return;\n    }\n\n    if (this.readyState === this.CONNECTING) {\n      this.dispatchEvent(new CustomEvent('open'));\n      this._setReadyState(this.OPEN);\n    }\n\n    const data = this.xhr.responseText.substring(this.progress);\n\n    this.progress += data.length;\n    const parts = (this.chunk + data).split(/(\\r\\n\\r\\n|\\r\\r|\\n\\n)/g);\n\n    /*\n     * We assume that the last chunk can be incomplete because of buffering or other network effects,\n     * so we always save the last part to merge it with the next incoming packet\n     */\n    const lastPart = parts.pop();\n    parts.forEach(function(part) {\n        if (part.trim().length > 0) {\n            this.dispatchEvent(this._parseEventChunk(part));\n        }\n    }.bind(this));\n    this.chunk = lastPart;\n  };\n\n  /** @private */\n  this._onStreamLoaded = function(e) {\n    this._onStreamProgress(e);\n\n    // Parse the last chunk.\n    this.dispatchEvent(this._parseEventChunk(this.chunk));\n    this.chunk = '';\n  };\n\n  /**\n   * Parse a received SSE event chunk into a constructed event object.\n   *\n   * Reference: https://html.spec.whatwg.org/multipage/server-sent-events.html#dispatchMessage\n   */\n  this._parseEventChunk = function(chunk) {\n    if (!chunk || chunk.length === 0) {\n      return null;\n    }\n\n    if (this.debug) {\n      console.debug(chunk);\n    }\n\n    const e = {'id': null, 'retry': null, 'data': null, 'event': null};\n    chunk.split(/\\n|\\r\\n|\\r/).forEach(function(line) {\n      const index = line.indexOf(this.FIELD_SEPARATOR);\n      let field, value;\n      if (index > 0) {\n        // only first whitespace should be trimmed\n        const skip = (line[index + 1] === ' ') ? 2 : 1;\n        field = line.substring(0, index);\n        value = line.substring(index + skip);\n      } else if (index < 0) {\n        // Interpret the entire line as the field name, and use the empty string as the field value\n        field = line;\n        value = '';\n      } else {\n        // A colon is the first character. This is a comment; ignore it.\n        return;\n      }\n\n      if (!(field in e)) {\n        return;\n      }\n\n      // consecutive 'data' is concatenated with newlines\n      if (field === 'data' && e[field] !== null) {\n          e['data'] += \"\\n\" + value;\n      } else {\n        e[field] = value;\n      }\n    }.bind(this));\n\n    if (e.id !== null) {\n      this.lastEventId = e.id;\n    }\n\n    const event = new CustomEvent(e.event || 'message');\n    event.id = e.id;\n    event.data = e.data || '';\n    event.lastEventId = this.lastEventId;\n    return event;\n  };\n\n  this._checkStreamClosed = function() {\n    if (!this.xhr) {\n      return;\n    }\n\n    if (this.xhr.readyState === XMLHttpRequest.DONE) {\n      this._setReadyState(this.CLOSED);\n    }\n  };\n\n  /**\n   * starts the streaming\n   * @type Stream\n   * @return {void}\n   */\n  this.stream = function() {\n    if (this.xhr) {\n      // Already connected.\n      return;\n    }\n\n    this._setReadyState(this.CONNECTING);\n\n    this.xhr = new XMLHttpRequest();\n    this.xhr.addEventListener('progress', this._onStreamProgress.bind(this));\n    this.xhr.addEventListener('load', this._onStreamLoaded.bind(this));\n    this.xhr.addEventListener('readystatechange', this._checkStreamClosed.bind(this));\n    this.xhr.addEventListener('error', this._onStreamFailure.bind(this));\n    this.xhr.addEventListener('abort', this._onStreamAbort.bind(this));\n    this.xhr.open(this.method, this.url);\n    for (let header in this.headers) {\n      this.xhr.setRequestHeader(header, this.headers[header]);\n    }\n    if (this.lastEventId.length > 0) {\n      this.xhr.setRequestHeader(\"Last-Event-ID\", this.lastEventId);\n    }\n    this.xhr.withCredentials = this.withCredentials;\n    this.xhr.send(this.payload);\n  };\n\n  /**\n   * closes the stream\n   * @type Close\n   * @return {void}\n   */\n  this.close = function() {\n    if (this.readyState === this.CLOSED) {\n      return;\n    }\n\n    this.xhr.abort();\n    this.xhr = null;\n    this._setReadyState(this.CLOSED);\n  };\n\n  if (options.start === undefined || options.start) {\n    this.stream();\n  }\n};\n\n// Export our SSE module for npm.js\nif (typeof exports !== 'undefined') {\n  exports.SSE = SSE;\n}\n\n// Export as an ECMAScript module\nexport { SSE };\n\n/**\n * @typedef { {[key: string]: string} } SSEHeaders\n */\n/**\n * @typedef {Object} SSEOptions\n * @property {SSEHeaders} [headers] - headers\n * @property {string} [payload] - payload as a string\n * @property {string} [method] - HTTP Method\n * @property {boolean} [withCredentials] - flag, if credentials needed\n * @property {boolean} [start] - flag, if streaming should start automatically\n * @property {boolean} [debug] - debugging flag\n */\n/**\n * @typedef {Object} _SSEvent\n * @property {string} id\n * @property {string} data\n */\n/**\n * @typedef {Object} _ReadyStateEvent\n * @property {number} readyState\n */\n/**\n * @typedef {Event & _SSEvent} SSEvent\n */\n/**\n * @typedef {SSEvent & _ReadyStateEvent} ReadyStateEvent\n */\n/**\n * @callback AddEventListener\n * @param {string} type\n * @param {function} listener\n * @returns {void}\n */\n/**\n * @callback RemoveEventListener\n * @param {string} type\n * @param {function} listener\n * @returns {void}\n */\n/**\n * @callback DispatchEvent\n * @param {string} type\n * @param {function} listener\n * @returns {boolean}\n */\n/**\n * @callback Stream\n * @returns {void}\n */\n/**\n * @callback Close\n * @returns {void}\n */\n/**\n * @callback OnMessage\n * @param {SSEvent} event\n * @returns {void}\n */\n/**\n * @callback OnOpen\n * @param {SSEvent} event\n * @returns {void}\n */\n/**\n * @callback OnLoad\n * @param {SSEvent} event\n * @returns {void}\n */\n/**\n * @callback OnReadystatechange\n * @param {ReadyStateEvent} event\n * @returns {void}\n */\n/**\n * @callback OnError\n * @param {SSEvent} event\n * @returns {void}\n */\n/**\n * @callback OnAbort\n * @param {SSEvent} event\n * @returns {void}\n */\n/**\n * @typedef {Object} SSE\n * @property {SSEHeaders} headers - headers\n * @property {string} payload - payload as a string\n * @property {string} method - HTTP Method\n * @property {boolean} withCredentials - flag, if credentials needed\n * @property {boolean} debug - debugging flag\n * @property {string} FIELD_SEPARATOR\n * @property {Record<string, Function[]>} listeners\n * @property {XMLHttpRequest | null} xhr\n * @property {number} readyState\n * @property {number} progress\n * @property {string} chunk\n * @property {-1} INITIALIZING\n * @property {0} CONNECTING\n * @property {1} OPEN\n * @property {2} CLOSED\n * @property {AddEventListener} addEventListener\n * @property {RemoveEventListener} removeEventListener\n * @property {DispatchEvent} dispatchEvent\n * @property {Stream} stream\n * @property {Close} close\n * @property {OnMessage} onmessage\n * @property {OnOpen} onopen\n * @property {OnLoad} onload\n * @property {OnReadystatechange} onreadystatechange\n * @property {OnError} onerror\n * @property {OnAbort} onabort\n */\n"], "mappings": ";;;AA2BA,IAAI,MAAM,SAAU,KAAK,SAAS;AAChC,MAAI,EAAE,gBAAgB,MAAM;AAC1B,WAAO,IAAI,IAAI,KAAK,OAAO;AAAA,EAC7B;AAGA,OAAK,eAAe;AAEpB,OAAK,aAAa;AAElB,OAAK,OAAO;AAEZ,OAAK,SAAS;AAGd,OAAK,MAAM;AAEX,YAAU,WAAW,CAAC;AACtB,OAAK,UAAU,QAAQ,WAAW,CAAC;AACnC,OAAK,UAAU,QAAQ,YAAY,SAAY,QAAQ,UAAU;AACjE,OAAK,SAAS,QAAQ,WAAW,KAAK,WAAW,UAAU;AAC3D,OAAK,kBAAkB,CAAC,CAAC,QAAQ;AACjC,OAAK,QAAQ,CAAC,CAAC,QAAQ;AAGvB,OAAK,kBAAkB;AAGvB,OAAK,YAAY,CAAC;AAGlB,OAAK,MAAM;AAEX,OAAK,aAAa,KAAK;AAEvB,OAAK,WAAW;AAEhB,OAAK,QAAQ;AAEb,OAAK,cAAc;AAKnB,OAAK,mBAAmB,SAAS,MAAM,UAAU;AAC/C,QAAI,KAAK,UAAU,IAAI,MAAM,QAAW;AACtC,WAAK,UAAU,IAAI,IAAI,CAAC;AAAA,IAC1B;AAEA,QAAI,KAAK,UAAU,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI;AACjD,WAAK,UAAU,IAAI,EAAE,KAAK,QAAQ;AAAA,IACpC;AAAA,EACF;AAKA,OAAK,sBAAsB,SAAS,MAAM,UAAU;AAClD,QAAI,KAAK,UAAU,IAAI,MAAM,QAAW;AACtC;AAAA,IACF;AAEA,UAAM,WAAW,CAAC;AAClB,SAAK,UAAU,IAAI,EAAE,QAAQ,SAAS,SAAS;AAC7C,UAAI,YAAY,UAAU;AACxB,iBAAS,KAAK,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AACD,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO,KAAK,UAAU,IAAI;AAAA,IAC5B,OAAO;AACL,WAAK,UAAU,IAAI,IAAI;AAAA,IACzB;AAAA,EACF;AAKA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,QAAI,CAAC,GAAG;AACN,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO;AACd,cAAQ,MAAM,CAAC;AAAA,IACjB;AAEA,MAAE,SAAS;AAEX,UAAM,YAAY,OAAO,EAAE;AAC3B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,SAAS,EAAE,KAAK,MAAM,CAAC;AAC5B,UAAI,EAAE,kBAAkB;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1B,aAAO,KAAK,UAAU,EAAE,IAAI,EAAE,MAAM,SAAS,UAAU;AACrD,iBAAS,CAAC;AACV,eAAO,CAAC,EAAE;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAGA,OAAK,iBAAiB,SAAS,OAAO;AACpC,UAAM,QAAQ,IAAI,YAAY,kBAAkB;AAChD,UAAM,aAAa;AACnB,SAAK,aAAa;AAClB,SAAK,cAAc,KAAK;AAAA,EAC1B;AAEA,OAAK,mBAAmB,SAAS,GAAG;AAClC,UAAM,QAAQ,IAAI,YAAY,OAAO;AACrC,UAAM,OAAO,EAAE,cAAc;AAC7B,SAAK,cAAc,KAAK;AACxB,SAAK,MAAM;AAAA,EACb;AAEA,OAAK,iBAAiB,WAAW;AAC/B,SAAK,cAAc,IAAI,YAAY,OAAO,CAAC;AAC3C,SAAK,MAAM;AAAA,EACb;AAGA,OAAK,oBAAoB,SAAS,GAAG;AACnC,QAAI,CAAC,KAAK,KAAK;AACb;AAAA,IACF;AAEA,QAAI,KAAK,IAAI,WAAW,KAAK;AAC3B,WAAK,iBAAiB,CAAC;AACvB;AAAA,IACF;AAEA,QAAI,KAAK,eAAe,KAAK,YAAY;AACvC,WAAK,cAAc,IAAI,YAAY,MAAM,CAAC;AAC1C,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAEA,UAAM,OAAO,KAAK,IAAI,aAAa,UAAU,KAAK,QAAQ;AAE1D,SAAK,YAAY,KAAK;AACtB,UAAM,SAAS,KAAK,QAAQ,MAAM,MAAM,uBAAuB;AAM/D,UAAM,WAAW,MAAM,IAAI;AAC3B,UAAM,SAAQ,SAAS,MAAM;AACzB,UAAI,KAAK,KAAK,EAAE,SAAS,GAAG;AACxB,aAAK,cAAc,KAAK,iBAAiB,IAAI,CAAC;AAAA,MAClD;AAAA,IACJ,GAAE,KAAK,IAAI,CAAC;AACZ,SAAK,QAAQ;AAAA,EACf;AAGA,OAAK,kBAAkB,SAAS,GAAG;AACjC,SAAK,kBAAkB,CAAC;AAGxB,SAAK,cAAc,KAAK,iBAAiB,KAAK,KAAK,CAAC;AACpD,SAAK,QAAQ;AAAA,EACf;AAOA,OAAK,mBAAmB,SAAS,OAAO;AACtC,QAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO;AACd,cAAQ,MAAM,KAAK;AAAA,IACrB;AAEA,UAAM,IAAI,EAAC,MAAM,MAAM,SAAS,MAAM,QAAQ,MAAM,SAAS,KAAI;AACjE,UAAM,MAAM,YAAY,EAAE,SAAQ,SAAS,MAAM;AAC/C,YAAM,QAAQ,KAAK,QAAQ,KAAK,eAAe;AAC/C,UAAI,OAAO;AACX,UAAI,QAAQ,GAAG;AAEb,cAAM,OAAQ,KAAK,QAAQ,CAAC,MAAM,MAAO,IAAI;AAC7C,gBAAQ,KAAK,UAAU,GAAG,KAAK;AAC/B,gBAAQ,KAAK,UAAU,QAAQ,IAAI;AAAA,MACrC,WAAW,QAAQ,GAAG;AAEpB,gBAAQ;AACR,gBAAQ;AAAA,MACV,OAAO;AAEL;AAAA,MACF;AAEA,UAAI,EAAE,SAAS,IAAI;AACjB;AAAA,MACF;AAGA,UAAI,UAAU,UAAU,EAAE,KAAK,MAAM,MAAM;AACvC,UAAE,MAAM,KAAK,OAAO;AAAA,MACxB,OAAO;AACL,UAAE,KAAK,IAAI;AAAA,MACb;AAAA,IACF,GAAE,KAAK,IAAI,CAAC;AAEZ,QAAI,EAAE,OAAO,MAAM;AACjB,WAAK,cAAc,EAAE;AAAA,IACvB;AAEA,UAAM,QAAQ,IAAI,YAAY,EAAE,SAAS,SAAS;AAClD,UAAM,KAAK,EAAE;AACb,UAAM,OAAO,EAAE,QAAQ;AACvB,UAAM,cAAc,KAAK;AACzB,WAAO;AAAA,EACT;AAEA,OAAK,qBAAqB,WAAW;AACnC,QAAI,CAAC,KAAK,KAAK;AACb;AAAA,IACF;AAEA,QAAI,KAAK,IAAI,eAAe,eAAe,MAAM;AAC/C,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC;AAAA,EACF;AAOA,OAAK,SAAS,WAAW;AACvB,QAAI,KAAK,KAAK;AAEZ;AAAA,IACF;AAEA,SAAK,eAAe,KAAK,UAAU;AAEnC,SAAK,MAAM,IAAI,eAAe;AAC9B,SAAK,IAAI,iBAAiB,YAAY,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACvE,SAAK,IAAI,iBAAiB,QAAQ,KAAK,gBAAgB,KAAK,IAAI,CAAC;AACjE,SAAK,IAAI,iBAAiB,oBAAoB,KAAK,mBAAmB,KAAK,IAAI,CAAC;AAChF,SAAK,IAAI,iBAAiB,SAAS,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACnE,SAAK,IAAI,iBAAiB,SAAS,KAAK,eAAe,KAAK,IAAI,CAAC;AACjE,SAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,GAAG;AACnC,aAAS,UAAU,KAAK,SAAS;AAC/B,WAAK,IAAI,iBAAiB,QAAQ,KAAK,QAAQ,MAAM,CAAC;AAAA,IACxD;AACA,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,WAAK,IAAI,iBAAiB,iBAAiB,KAAK,WAAW;AAAA,IAC7D;AACA,SAAK,IAAI,kBAAkB,KAAK;AAChC,SAAK,IAAI,KAAK,KAAK,OAAO;AAAA,EAC5B;AAOA,OAAK,QAAQ,WAAW;AACtB,QAAI,KAAK,eAAe,KAAK,QAAQ;AACnC;AAAA,IACF;AAEA,SAAK,IAAI,MAAM;AACf,SAAK,MAAM;AACX,SAAK,eAAe,KAAK,MAAM;AAAA,EACjC;AAEA,MAAI,QAAQ,UAAU,UAAa,QAAQ,OAAO;AAChD,SAAK,OAAO;AAAA,EACd;AACF;AAGA,IAAI,OAAO,YAAY,aAAa;AAClC,UAAQ,MAAM;AAChB;", "names": []}