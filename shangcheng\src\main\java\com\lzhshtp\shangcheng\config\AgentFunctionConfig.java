package com.lzhshtp.shangcheng.config;

import com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Description;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * Agent Function配置类
 * 注册Agent的工具函数为Spring Bean
 */
@Configuration
public class AgentFunctionConfig {

    private final ProductAssistant productAssistant;

    public AgentFunctionConfig(ProductAssistant productAssistant) {
        this.productAssistant = productAssistant;
    }

    /**
     * 商品搜索Function
     */
    @Bean
    @Description("根据关键词、分类、价格范围等条件搜索商品")
    public Function<ProductSearchRequest, String> productSearchFunction() {
        return request -> productAssistant.searchProducts(
            request.keyword(),
            request.categoryId(),
            request.minPrice(),
            request.maxPrice(),
            request.condition(),
            request.location(),
            request.page() != null ? request.page() : 1,
            request.pageSize() != null ? request.pageSize() : 10
        );
    }

    /**
     * 商品详情Function
     */
    @Bean
    @Description("根据商品ID获取详细信息")
    public Function<ProductDetailRequest, String> productDetailFunction() {
        return request -> productAssistant.getProductDetail(request.productId());
    }

    /**
     * 商品比较Function
     */
    @Bean
    @Description("比较多个商品的特性和价格")
    public Function<ProductCompareRequest, String> productCompareFunction() {
        return request -> productAssistant.compareProducts(request.productIds());
    }

    /**
     * 商品推荐Function
     */
    @Bean
    @Description("根据商品ID推荐相似商品")
    public Function<ProductRecommendRequest, String> productRecommendFunction() {
        return request -> productAssistant.recommendSimilarProducts(
            request.productId(),
            request.limit() != null ? request.limit() : 5
        );
    }

    /**
     * 分类列表Function
     */
    @Bean
    @Description("获取商品分类列表")
    public Function<CategoryListRequest, String> categoryListFunction() {
        return request -> productAssistant.getCategoryList();
    }

    // ==================== Request记录类 ====================

    /**
     * 商品搜索请求
     */
    public record ProductSearchRequest(
        @Description("搜索关键词") String keyword,
        @Description("分类ID，可选") Integer categoryId,
        @Description("最低价格，可选") BigDecimal minPrice,
        @Description("最高价格，可选") BigDecimal maxPrice,
        @Description("商品新旧程度，可选") String condition,
        @Description("地区，可选") String location,
        @Description("页码，默认1") Integer page,
        @Description("每页数量，默认10") Integer pageSize
    ) {}

    /**
     * 商品详情请求
     */
    public record ProductDetailRequest(
        @Description("商品ID") Long productId
    ) {}

    /**
     * 商品比较请求
     */
    public record ProductCompareRequest(
        @Description("要比较的商品ID列表，用逗号分隔") String productIds
    ) {}

    /**
     * 商品推荐请求
     */
    public record ProductRecommendRequest(
        @Description("参考商品ID") Long productId,
        @Description("推荐数量，默认5") Integer limit
    ) {}

    /**
     * 分类列表请求
     */
    public record CategoryListRequest(
        @Description("占位参数，可以为空") String placeholder
    ) {}
}
