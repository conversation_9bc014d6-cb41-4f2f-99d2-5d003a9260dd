package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.AiChatRequest;
import com.lzhshtp.shangcheng.dto.AiChatResponse;
import com.lzhshtp.shangcheng.model.AiMessage;
import com.lzhshtp.shangcheng.model.AiSession;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * AI聊天服务接口
 */
public interface AiChatService {

    /**
     * 流式聊天对话
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 流式响应
     */
    Flux<AiChatResponse> chatStream(AiChatRequest request, Long userId);

    /**
     * 非流式聊天对话
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 聊天响应
     */
    AiChatResponse chat(AiChatRequest request, Long userId);

    /**
     * 创建新的AI会话
     *
     * @param sessionName 会话名称
     * @param userId      用户ID
     * @return 会话信息
     */
    AiSession createSession(String sessionName, Long userId);

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<AiSession> getUserSessions(Long userId);

    /**
     * 获取会话详情（包含消息列表）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 会话详情
     */
    AiSession getSessionDetail(String sessionId, Long userId);

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否删除成功
     */
    boolean deleteSession(String sessionId, Long userId);

    /**
     * 清空会话历史消息
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否清空成功
     */
    boolean clearSessionHistory(String sessionId, Long userId);

    /**
     * 获取会话的消息列表
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param limit     消息数量限制
     * @return 消息列表
     */
    List<AiMessage> getSessionMessages(String sessionId, Long userId, Integer limit);

    /**
     * 保存用户消息
     *
     * @param sessionId 会话ID
     * @param content   消息内容
     * @param medias    媒体内容
     * @param userId    用户ID
     * @return 消息信息
     */
    AiMessage saveUserMessage(String sessionId, String content, List<AiMessage.MediaContent> medias, Long userId);

    /**
     * 保存AI回复消息
     *
     * @param sessionId 会话ID
     * @param content   消息内容
     * @param userId    用户ID
     * @return 消息信息
     */
    AiMessage saveAssistantMessage(String sessionId, String content, Long userId);
}
