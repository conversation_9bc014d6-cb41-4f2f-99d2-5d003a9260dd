<template>
  <div class="forum-management">
    <div class="page-header">
      <h2 class="page-title">论坛管理</h2>
      <div class="header-actions">
        <button 
          class="refresh-btn" 
          @click="fetchPostList" 
          :disabled="loading"
        >
          刷新数据
        </button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <div class="search-box">
        <input 
          v-model="queryParams.keyword" 
          type="text" 
          placeholder="搜索帖子标题或内容" 
          @keyup.enter="handleSearch"
        >
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
      
      <div class="filter-box">
        <select v-model="queryParams.categoryId" @change="handleSearch">
          <option :value="null">全部分类</option>
          <option v-for="category in categories" :key="category.forumCategoryId" :value="category.forumCategoryId">
            {{ category.categoryName }}
          </option>
        </select>
      </div>
      
      <div class="filter-box">
        <select v-model="queryParams.onlyPinned" @change="handleSearch">
          <option :value="false">全部帖子</option>
          <option :value="true">仅置顶帖子</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="postList.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <p>暂无帖子数据</p>
      </div>
      
      <!-- 帖子表格 -->
      <table v-else class="data-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>标题</th>
            <th>作者</th>
            <th>分类</th>
            <th>发布时间</th>
            <th>浏览量</th>
            <th>评论数</th>
            <th>置顶</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="post in postList" :key="post.postId">
            <td>{{ post.postId }}</td>
            <td class="title-cell">
              <div class="post-title" :title="post.title">{{ post.title }}</div>
            </td>
            <td>{{ post.authorName }}</td>
            <td>{{ post.categoryName }}</td>
            <td>{{ formatDate(post.postedAt) }}</td>
            <td>{{ post.viewsCount }}</td>
            <td>{{ post.commentCount }}</td>
            <td>
              <span 
                class="status-tag" 
                :class="post.isPinned === 1 ? 'status-success' : 'status-default'"
              >
                {{ post.isPinned === 1 ? '已置顶' : '未置顶' }}
              </span>
            </td>
            <td>
              <span 
                class="status-tag" 
                :class="post.status === 0 ? 'status-success' : 'status-danger'"
              >
                {{ post.status === 0 ? '已发布' : '已下架' }}
              </span>
            </td>
            <td class="actions">
              <button class="action-btn view-btn" @click="viewPostDetail(post)">查看</button>
              <button 
                class="action-btn pin-btn" 
                :class="post.isPinned === 1 ? 'unpin-btn' : 'pin-btn'"
                @click="togglePinned(post)"
              >
                {{ post.isPinned === 1 ? '取消置顶' : '置顶' }}
              </button>
              <button 
                class="action-btn status-btn" 
                :class="post.status === 0 ? 'offline-btn' : 'online-btn'"
                @click="toggleStatus(post)"
              >
                {{ post.status === 0 ? '下架' : '发布' }}
              </button>
              <button class="action-btn delete-btn" @click="confirmDelete(post)">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="!loading && total > 0">
      <button 
        class="page-btn" 
        :disabled="queryParams.pageNum === 1"
        @click="changePage(queryParams.pageNum - 1)"
      >
        上一页
      </button>
      
      <span class="page-info">{{ queryParams.pageNum }} / {{ totalPages }}</span>
      
      <button 
        class="page-btn" 
        :disabled="queryParams.pageNum === totalPages"
        @click="changePage(queryParams.pageNum + 1)"
      >
        下一页
      </button>
    </div>

    <!-- 帖子详情对话框 -->
    <div v-if="detailDialogVisible" class="dialog-backdrop" @click="closeDialogOnBackdrop">
      <div class="dialog post-detail-dialog" @click.stop>
        <div class="dialog-header">
          <h3>帖子详情</h3>
          <button class="close-btn" @click="detailDialogVisible = false">&times;</button>
        </div>
        
        <div class="dialog-body" v-if="currentPost">
          <h2 class="post-title">{{ currentPost.title }}</h2>
          
          <div class="post-meta">
            <span>作者: {{ currentPost.authorName }}</span>
            <span>分类: {{ currentPost.categoryName }}</span>
            <span>发布时间: {{ formatDate(currentPost.postedAt) }}</span>
            <span>浏览量: {{ currentPost.viewsCount }}</span>
            <span>评论数: {{ currentPost.commentCount }}</span>
          </div>
          
          <div class="post-content">
            {{ currentPost.content }}
          </div>
          
          <div class="post-status">
            <div class="status-item">
              <span class="status-label">置顶状态:</span>
              <span 
                class="status-tag" 
                :class="currentPost.isPinned === 1 ? 'status-success' : 'status-default'"
              >
                {{ currentPost.isPinned === 1 ? '已置顶' : '未置顶' }}
              </span>
            </div>
            
            <div class="status-item">
              <span class="status-label">发布状态:</span>
              <span 
                class="status-tag" 
                :class="currentPost.status === 0 ? 'status-success' : 'status-danger'"
              >
                {{ currentPost.status === 0 ? '已发布' : '已下架' }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <button class="btn" @click="detailDialogVisible = false">关闭</button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="deleteDialogVisible" class="dialog-backdrop" @click="closeDialogOnBackdrop">
      <div class="dialog delete-dialog" @click.stop>
        <div class="dialog-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="deleteDialogVisible = false">&times;</button>
        </div>
        
        <div class="dialog-body">
          <p class="delete-message">
            确定要删除帖子 <strong>{{ currentPost?.title }}</strong> 吗？
          </p>
          <p class="delete-warning">
            警告：删除后不可恢复，该帖子下的所有评论也将被删除。
          </p>
        </div>
        
        <div class="dialog-footer">
          <button class="cancel-btn" @click="deleteDialogVisible = false">取消</button>
          <button 
            class="delete-confirm-btn" 
            @click="deletePost" 
            :disabled="deleteSubmitting"
          >
            {{ deleteSubmitting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message.show" class="message-toast" :class="message.type">
      {{ message.content }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  getPostList, 
  getPostDetail, 
  setPinned, 
  setStatus, 
  deletePost as deletePostApi 
} from '@/admin/api/forumPosts'
import { getAllCategories } from '@/admin/api/forumCategories'

// 数据状态
const postList = ref([])
const categories = ref([])
const loading = ref(false)
const total = ref(0)
const totalPages = computed(() => Math.ceil(total.value / queryParams.pageSize))

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  categoryId: null,
  onlyPinned: false,
  enablePinned: true,
  orderBy: 'postedAt'
})

// 对话框状态
const detailDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const currentPost = ref(null)
const deleteSubmitting = ref(false)

// 消息提示
const message = reactive({
  show: false,
  content: '',
  type: 'success'
})

// 获取帖子列表
const fetchPostList = async () => {
  loading.value = true
  try {
    const res = await getPostList(queryParams)
    if (res.code === 200) {
      postList.value = res.data.records
      total.value = res.data.total
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('获取帖子列表失败:', error)
    showMessage('获取帖子列表失败，请刷新重试', 'error')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await getAllCategories()
    if (res.code === 200) {
      categories.value = res.data
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1
  fetchPostList()
}

// 切换页码
const changePage = (page) => {
  queryParams.pageNum = page
  fetchPostList()
}

// 查看帖子详情
const viewPostDetail = async (post) => {
  try {
    const res = await getPostDetail(post.postId)
    if (res.code === 200) {
      currentPost.value = res.data
      detailDialogVisible.value = true
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('获取帖子详情失败:', error)
    showMessage('获取帖子详情失败', 'error')
  }
}

// 切换置顶状态
const togglePinned = async (post) => {
  const newPinnedStatus = post.isPinned === 1 ? false : true
  try {
    const res = await setPinned(post.postId, newPinnedStatus)
    if (res.code === 200) {
      showMessage(newPinnedStatus ? '帖子已置顶' : '帖子已取消置顶', 'success')
      // 更新本地数据
      post.isPinned = newPinnedStatus ? 1 : 0
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('设置置顶状态失败:', error)
    showMessage('设置置顶状态失败', 'error')
  }
}

// 切换发布状态
const toggleStatus = async (post) => {
  const newStatus = post.status === 0 ? 1 : 0
  try {
    const res = await setStatus(post.postId, newStatus)
    if (res.code === 200) {
      showMessage(newStatus === 0 ? '帖子已发布' : '帖子已下架', 'success')
      // 更新本地数据
      post.status = newStatus
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('设置帖子状态失败:', error)
    showMessage('设置帖子状态失败', 'error')
  }
}

// 确认删除
const confirmDelete = (post) => {
  currentPost.value = post
  deleteDialogVisible.value = true
}

// 删除帖子
const deletePost = async () => {
  if (!currentPost.value) return
  
  deleteSubmitting.value = true
  try {
    const res = await deletePostApi(currentPost.value.postId)
    if (res.code === 200) {
      showMessage('删除帖子成功', 'success')
      deleteDialogVisible.value = false
      
      // 从列表中移除
      postList.value = postList.value.filter(item => item.postId !== currentPost.value.postId)
      
      // 如果当前页没有数据了，且不是第一页，则跳转到上一页
      if (postList.value.length === 0 && queryParams.pageNum > 1) {
        queryParams.pageNum--
        fetchPostList()
      }
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('删除帖子失败:', error)
    showMessage('删除帖子失败', 'error')
  } finally {
    deleteSubmitting.value = false
  }
}

// 在背景点击时关闭对话框
const closeDialogOnBackdrop = (e) => {
  if (e.target.classList.contains('dialog-backdrop')) {
    detailDialogVisible.value = false
    deleteDialogVisible.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 显示消息提示
const showMessage = (content, type = 'success') => {
  message.content = content
  message.type = type
  message.show = true
  
  // 3秒后自动关闭
  setTimeout(() => {
    message.show = false
  }, 3000)
}

// 初始化
onMounted(() => {
  fetchCategories()
  fetchPostList()
})
</script>

<style scoped>
.forum-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 500px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.refresh-btn {
  padding: 8px 16px;
  background-color: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-btn:hover {
  background-color: #73d13d;
}

.refresh-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

/* 搜索和筛选区域样式 */
.filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-box {
  display: flex;
  flex: 1;
  min-width: 250px;
}

.search-box input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.search-box input:focus {
  border-color: #1890ff;
}

.search-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-btn:hover {
  background-color: #40a9ff;
}

.filter-box select {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  outline: none;
  min-width: 120px;
}

/* 表格样式 */
.table-container {
  margin-bottom: 20px;
  min-height: 300px;
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.data-table th {
  background-color: #fafafa;
  font-weight: 500;
  white-space: nowrap;
}

.title-cell {
  max-width: 250px;
}

.post-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.actions {
  white-space: nowrap;
  display: flex;
  gap: 5px;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

.view-btn {
  background-color: #1890ff;
  color: white;
}

.view-btn:hover {
  background-color: #40a9ff;
}

.pin-btn {
  background-color: #faad14;
  color: white;
}

.pin-btn:hover {
  background-color: #ffc53d;
}

.unpin-btn {
  background-color: #52c41a;
  color: white;
}

.unpin-btn:hover {
  background-color: #73d13d;
}

.online-btn {
  background-color: #52c41a;
  color: white;
}

.online-btn:hover {
  background-color: #73d13d;
}

.offline-btn {
  background-color: #ff4d4f;
  color: white;
}

.offline-btn:hover {
  background-color: #ff7875;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

.delete-btn:hover {
  background-color: #ff7875;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.status-success {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-danger {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-default {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.page-btn {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  color: #1890ff;
  border-color: #1890ff;
}

.page-btn:disabled {
  cursor: not-allowed;
  color: #d9d9d9;
}

.page-info {
  color: #666;
}

/* 对话框样式 */
.dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background-color: #fff;
  border-radius: 4px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.post-detail-dialog {
  width: 700px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #666;
}

.dialog-body {
  padding: 24px;
}

.post-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 20px;
  color: #333;
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.post-content {
  margin-bottom: 16px;
  line-height: 1.6;
  white-space: pre-wrap;
  border: 1px solid #f0f0f0;
  padding: 16px;
  border-radius: 4px;
  background-color: #fafafa;
  max-height: 300px;
  overflow-y: auto;
}

.post-status {
  display: flex;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
}

.dialog-footer {
  padding: 10px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.btn {
  padding: 6px 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 删除对话框样式 */
.delete-dialog {
  max-width: 450px;
}

.delete-message {
  margin-top: 0;
  font-size: 15px;
}

.delete-warning {
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 0;
}

.cancel-btn {
  padding: 6px 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.cancel-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.delete-confirm-btn {
  padding: 6px 16px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.delete-confirm-btn:hover {
  background-color: #ff7875;
}

.delete-confirm-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1001;
  animation: fadeInOut 3s ease-in-out;
}

.message-toast.success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.message-toast.error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-10px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
  }
  
  .refresh-btn {
    width: 100%;
  }
  
  .filter-container {
    flex-direction: column;
  }
  
  .search-box,
  .filter-box {
    width: 100%;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    margin-bottom: 5px;
  }
}
</style> 