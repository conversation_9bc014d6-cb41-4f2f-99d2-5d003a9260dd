{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/nsis.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\nexport const nsis = simpleMode({\n  start:[\n    // Numbers\n    {regex: /(?:[+-]?)(?:0x[\\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\\d+.?\\d*)/, token: \"number\"},\n\n    // Strings\n    { regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/, token: \"string\" },\n    { regex: /'(?:[^\\\\']|\\\\.)*'?/, token: \"string\" },\n    { regex: /`(?:[^\\\\`]|\\\\.)*`?/, token: \"string\" },\n\n    // Compile Time Commands\n    {regex: /^\\s*(?:\\!(addincludedir|addplugindir|appendfile|assert|cd|define|delfile|echo|error|execute|finalize|getdllversion|gettlbversion|include|insertmacro|macro|macroend|makensis|packhdr|pragma|searchparse|searchreplace|system|tempfile|undef|uninstfinalize|verbose|warning))\\b/i, token: \"keyword\"},\n\n    // Conditional Compilation\n    {regex: /^\\s*(?:\\!(if(?:n?def)?|ifmacron?def|macro))\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:\\!(else|endif|macroend))\\b/i, token: \"keyword\", dedent: true},\n\n    // Runtime Commands\n    {regex: /^\\s*(?:Abort|AddBrandingImage|AddSize|AllowRootDirInstall|AllowSkipFiles|AutoCloseWindow|BGFont|BGGradient|BrandingText|BringToFront|Call|CallInstDLL|Caption|ChangeUI|CheckBitmap|ClearErrors|CompletedText|ComponentText|CopyFiles|CRCCheck|CreateDirectory|CreateFont|CreateShortCut|Delete|DeleteINISec|DeleteINIStr|DeleteRegKey|DeleteRegValue|DetailPrint|DetailsButtonText|DirText|DirVar|DirVerify|EnableWindow|EnumRegKey|EnumRegValue|Exch|Exec|ExecShell|ExecShellWait|ExecWait|ExpandEnvStrings|File|FileBufSize|FileClose|FileErrorText|FileOpen|FileRead|FileReadByte|FileReadUTF16LE|FileReadWord|FileWriteUTF16LE|FileSeek|FileWrite|FileWriteByte|FileWriteWord|FindClose|FindFirst|FindNext|FindWindow|FlushINI|GetCurInstType|GetCurrentAddress|GetDlgItem|GetDLLVersion|GetDLLVersionLocal|GetErrorLevel|GetFileTime|GetFileTimeLocal|GetFullPathName|GetFunctionAddress|GetInstDirError|GetKnownFolderPath|GetLabelAddress|GetTempFileName|GetWinVer|Goto|HideWindow|Icon|IfAbort|IfErrors|IfFileExists|IfRebootFlag|IfRtlLanguage|IfShellVarContextAll|IfSilent|InitPluginsDir|InstallButtonText|InstallColors|InstallDir|InstallDirRegKey|InstProgressFlags|InstType|InstTypeGetText|InstTypeSetText|Int64Cmp|Int64CmpU|Int64Fmt|IntCmp|IntCmpU|IntFmt|IntOp|IntPtrCmp|IntPtrCmpU|IntPtrOp|IsWindow|LangString|LicenseBkColor|LicenseData|LicenseForceSelection|LicenseLangString|LicenseText|LoadAndSetImage|LoadLanguageFile|LockWindow|LogSet|LogText|ManifestDPIAware|ManifestLongPathAware|ManifestMaxVersionTested|ManifestSupportedOS|MessageBox|MiscButtonText|Name|Nop|OutFile|Page|PageCallbacks|PEAddResource|PEDllCharacteristics|PERemoveResource|PESubsysVer|Pop|Push|Quit|ReadEnvStr|ReadINIStr|ReadRegDWORD|ReadRegStr|Reboot|RegDLL|Rename|RequestExecutionLevel|ReserveFile|Return|RMDir|SearchPath|SectionGetFlags|SectionGetInstTypes|SectionGetSize|SectionGetText|SectionIn|SectionSetFlags|SectionSetInstTypes|SectionSetSize|SectionSetText|SendMessage|SetAutoClose|SetBrandingImage|SetCompress|SetCompressor|SetCompressorDictSize|SetCtlColors|SetCurInstType|SetDatablockOptimize|SetDateSave|SetDetailsPrint|SetDetailsView|SetErrorLevel|SetErrors|SetFileAttributes|SetFont|SetOutPath|SetOverwrite|SetRebootFlag|SetRegView|SetShellVarContext|SetSilent|ShowInstDetails|ShowUninstDetails|ShowWindow|SilentInstall|SilentUnInstall|Sleep|SpaceTexts|StrCmp|StrCmpS|StrCpy|StrLen|SubCaption|Target|Unicode|UninstallButtonText|UninstallCaption|UninstallIcon|UninstallSubCaption|UninstallText|UninstPage|UnRegDLL|Var|VIAddVersionKey|VIFileVersion|VIProductVersion|WindowIcon|WriteINIStr|WriteRegBin|WriteRegDWORD|WriteRegExpandStr|WriteRegMultiStr|WriteRegNone|WriteRegStr|WriteUninstaller|XPStyle)\\b/i, token: \"keyword\"},\n    {regex: /^\\s*(?:Function|PageEx|Section(?:Group)?)\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:(Function|PageEx|Section(?:Group)?)End)\\b/i, token: \"keyword\", dedent: true},\n\n    // Command Options\n    {regex: /\\b(?:ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HIDDEN|HKCC|HKCR(32|64)?|HKCU(32|64)?|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM(32|64)?|HKPD|HKU|IDABORT|IDCANCEL|IDD_DIR|IDD_INST|IDD_INSTFILES|IDD_LICENSE|IDD_SELCOM|IDD_UNINST|IDD_VERIFY|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|MB_YESNOCANCEL|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SW_HIDE|SW_SHOWDEFAULT|SW_SHOWMAXIMIZED|SW_SHOWMINIMIZED|SW_SHOWNORMAL|SYSTEM|TEMPORARY)\\b/i, token: \"atom\"},\n    {regex: /\\b(?:admin|all|amd64-unicode|auto|both|bottom|bzip2|components|current|custom|directory|false|force|hide|highest|ifdiff|ifnewer|instfiles|lastused|leave|left|license|listonly|lzma|nevershow|none|normal|notset|off|on|right|show|silent|silentlog|textonly|top|true|try|un\\.components|un\\.custom|un\\.directory|un\\.instfiles|un\\.license|uninstConfirm|user|Win10|Win7|Win8|WinVista|x-86-(ansi|unicode)|zlib)\\b/i, token: \"builtin\"},\n\n    // LogicLib.nsh\n    {regex: /\\$\\{(?:And(?:If(?:Not)?|Unless)|Break|Case(?:2|3|4|5|Else)?|Continue|Default|Do(?:Until|While)?|Else(?:If(?:Not)?|Unless)?|End(?:If|Select|Switch)|Exit(?:Do|For|While)|For(?:Each)?|If(?:Cmd|Not(?:Then)?|Then)?|Loop(?:Until|While)?|Or(?:If(?:Not)?|Unless)|Select|Switch|Unless|While)\\}/i, token: \"variable-2\", indent: true},\n\n    // FileFunc.nsh\n    {regex: /\\$\\{(?:BannerTrimPath|DirState|DriveSpace|Get(BaseName|Drives|ExeName|ExePath|FileAttributes|FileExt|FileName|FileVersion|Options|OptionsS|Parameters|Parent|Root|Size|Time)|Locate|RefreshShellIcons)\\}/i, token: \"variable-2\", dedent: true},\n\n    // Memento.nsh\n    {regex: /\\$\\{(?:Memento(?:Section(?:Done|End|Restore|Save)?|UnselectedSection))\\}/i, token: \"variable-2\", dedent: true},\n\n    // TextFunc.nsh\n    {regex: /\\$\\{(?:Config(?:Read|ReadS|Write|WriteS)|File(?:Join|ReadFromEnd|Recode)|Line(?:Find|Read|Sum)|Text(?:Compare|CompareS)|TrimNewLines)\\}/i, token: \"variable-2\", dedent: true},\n\n    // WinVer.nsh\n    {regex: /\\$\\{(?:(?:At(?:Least|Most)|Is)(?:ServicePack|Win(?:7|8|10|95|98|200(?:0|3|8(?:R2)?)|ME|NT4|Vista|XP))|Is(?:NT|Server))\\}/i, token: \"variable\", dedent: true},\n\n    // WordFunc.nsh\n    {regex: /\\$\\{(?:StrFilterS?|Version(?:Compare|Convert)|Word(?:AddS?|Find(?:(?:2|3)X)?S?|InsertS?|ReplaceS?))\\}/i, token: \"keyword\", dedent: true},\n\n    // x64.nsh\n    {regex: /\\$\\{(?:RunningX64)\\}/i, token: \"variable\", dedent: true},\n    {regex: /\\$\\{(?:Disable|Enable)X64FSRedirection\\}/i, token: \"keyword\", dedent: true},\n\n    // Line Comment\n    {regex: /(#|;).*/, token: \"comment\"},\n\n    // Block Comment\n    {regex: /\\/\\*/, token: \"comment\", next: \"comment\"},\n\n    // Operator\n    {regex: /[-+\\/*=<>!]+/, token: \"operator\"},\n\n    // Variable\n    {regex: /\\$\\w[\\w\\.]*/, token: \"variable\"},\n\n    // Constant\n    {regex: /\\${[\\!\\w\\.:-]+}/, token: \"variableName.constant\"},\n\n    // Language String\n    {regex: /\\$\\([\\!\\w\\.:-]+\\)/, token: \"atom\"}\n  ],\n  comment: [\n    {regex: /.*?\\*\\//, token: \"comment\", next: \"start\"},\n    {regex: /.*/, token: \"comment\"}\n  ],\n  languageData: {\n    name: \"nsis\",\n    indentOnInput: /^\\s*((Function|PageEx|Section|Section(Group)?)End|(\\!(endif|macroend))|\\$\\{(End(If|Unless|While)|Loop(Until)|Next)\\})$/i,\n    commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n  }\n});\n\n"], "mappings": ";;;;;;AACO,IAAM,OAAO,WAAW;AAAA,EAC7B,OAAM;AAAA;AAAA,IAEJ,EAAC,OAAO,mEAAmE,OAAO,SAAQ;AAAA;AAAA,IAG1F,EAAE,OAAO,sBAAsB,OAAO,SAAS;AAAA,IAC/C,EAAE,OAAO,sBAAsB,OAAO,SAAS;AAAA,IAC/C,EAAE,OAAO,sBAAsB,OAAO,SAAS;AAAA;AAAA,IAG/C,EAAC,OAAO,mRAAmR,OAAO,UAAS;AAAA;AAAA,IAG3S,EAAC,OAAO,kDAAkD,OAAO,WAAW,QAAQ,KAAI;AAAA,IACxF,EAAC,OAAO,sCAAsC,OAAO,WAAW,QAAQ,KAAI;AAAA;AAAA,IAG5E,EAAC,OAAO,8mFAA8mF,OAAO,UAAS;AAAA,IACtoF,EAAC,OAAO,gDAAgD,OAAO,WAAW,QAAQ,KAAI;AAAA,IACtF,EAAC,OAAO,qDAAqD,OAAO,WAAW,QAAQ,KAAI;AAAA;AAAA,IAG3F,EAAC,OAAO,w3BAAw3B,OAAO,OAAM;AAAA,IAC74B,EAAC,OAAO,wZAAwZ,OAAO,UAAS;AAAA;AAAA,IAGhb,EAAC,OAAO,iSAAiS,OAAO,cAAc,QAAQ,KAAI;AAAA;AAAA,IAG1U,EAAC,OAAO,6MAA6M,OAAO,cAAc,QAAQ,KAAI;AAAA;AAAA,IAGtP,EAAC,OAAO,6EAA6E,OAAO,cAAc,QAAQ,KAAI;AAAA;AAAA,IAGtH,EAAC,OAAO,4IAA4I,OAAO,cAAc,QAAQ,KAAI;AAAA;AAAA,IAGrL,EAAC,OAAO,6HAA6H,OAAO,YAAY,QAAQ,KAAI;AAAA;AAAA,IAGpK,EAAC,OAAO,0GAA0G,OAAO,WAAW,QAAQ,KAAI;AAAA;AAAA,IAGhJ,EAAC,OAAO,yBAAyB,OAAO,YAAY,QAAQ,KAAI;AAAA,IAChE,EAAC,OAAO,6CAA6C,OAAO,WAAW,QAAQ,KAAI;AAAA;AAAA,IAGnF,EAAC,OAAO,WAAW,OAAO,UAAS;AAAA;AAAA,IAGnC,EAAC,OAAO,QAAQ,OAAO,WAAW,MAAM,UAAS;AAAA;AAAA,IAGjD,EAAC,OAAO,gBAAgB,OAAO,WAAU;AAAA;AAAA,IAGzC,EAAC,OAAO,eAAe,OAAO,WAAU;AAAA;AAAA,IAGxC,EAAC,OAAO,mBAAmB,OAAO,wBAAuB;AAAA;AAAA,IAGzD,EAAC,OAAO,qBAAqB,OAAO,OAAM;AAAA,EAC5C;AAAA,EACA,SAAS;AAAA,IACP,EAAC,OAAO,WAAW,OAAO,WAAW,MAAM,QAAO;AAAA,IAClD,EAAC,OAAO,MAAM,OAAO,UAAS;AAAA,EAChC;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC7D;AACF,CAAC;", "names": []}