package com.lzhshtp.shangcheng.repository.search;

import com.lzhshtp.shangcheng.document.ForumPostDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 论坛帖子搜索Repository
 * 基于ElasticSearch的帖子搜索功能
 */
@Repository
public interface ForumPostSearchRepository extends ElasticsearchRepository<ForumPostDocument, Long> {

    /**
     * 根据标题搜索帖子
     */
    Page<ForumPostDocument> findByTitleContaining(String title, Pageable pageable);

    /**
     * 根据标题或内容搜索帖子
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title^3\", \"content^1\"]}}], \"filter\": [{\"term\": {\"status\": 0}}]}}")
    Page<ForumPostDocument> findByTitleOrContentContaining(String keyword, Pageable pageable);

    /**
     * 根据分类搜索帖子
     */
    Page<ForumPostDocument> findByCategoryIdAndStatus(Integer categoryId, Integer status, Pageable pageable);

    /**
     * 根据作者搜索帖子
     */
    Page<ForumPostDocument> findByAuthorIdAndStatus(Long authorId, Integer status, Pageable pageable);

    /**
     * 复合搜索 - 关键词 + 分类
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title^3\", \"content^1\", \"categoryName^2\"]}}], \"filter\": [{\"term\": {\"status\": 0}}, {\"term\": {\"categoryId\": \"?1\"}}]}}")
    Page<ForumPostDocument> findByKeywordAndCategory(String keyword, Integer categoryId, Pageable pageable);

    /**
     * 获取热门帖子（按浏览量排序）
     */
    Page<ForumPostDocument> findByStatusOrderByViewsCountDesc(Integer status, Pageable pageable);

    /**
     * 获取最新帖子
     */
    Page<ForumPostDocument> findByStatusOrderByPostedAtDesc(Integer status, Pageable pageable);

    /**
     * 获取置顶帖子
     */
    Page<ForumPostDocument> findByIsPinnedTrueAndStatusOrderByPostedAtDesc(Integer status, Pageable pageable);

    /**
     * 搜索建议 - 根据标题前缀
     */
    @Query("{\"bool\": {\"must\": [{\"prefix\": {\"title\": \"?0\"}}], \"filter\": [{\"term\": {\"status\": 0}}]}}")
    List<ForumPostDocument> findSuggestionsByTitlePrefix(String prefix);

    /**
     * 相似帖子推荐
     */
    @Query("{\"more_like_this\": {\"fields\": [\"title\", \"content\", \"categoryName\"], \"like\": [{\"_index\": \"forum_posts\", \"_id\": \"?0\"}], \"min_term_freq\": 1, \"max_query_terms\": 12}}")
    List<ForumPostDocument> findSimilarPosts(Long postId);

    /**
     * 按综合热度排序（浏览量 + 评论数 + 点赞数）
     */
    @Query("{\"bool\": {\"filter\": [{\"term\": {\"status\": 0}}]}, \"sort\": [{\"_script\": {\"type\": \"number\", \"script\": {\"source\": \"doc['viewsCount'].value * 0.5 + doc['commentCount'].value * 2 + doc['likeCount'].value * 3\"}, \"order\": \"desc\"}}]}")
    Page<ForumPostDocument> findByStatusOrderByHotScore(Integer status, Pageable pageable);
}
