<template>
  <div class="user-profile-page">
    <!-- 顶部导航 -->
    <nav class="top-nav">
      <div class="nav-content">
        <router-link to="/home" class="logo" style="color: #FF0000;">易转</router-link>
        <div class="nav-right">
          <a href="#" class="user-avatar" title="个人中心">
            <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
          </a>
        </div>
      </div>
    </nav>

    <!-- 编辑个人资料弹窗 -->
    <div class="modal-overlay" v-if="showEditProfileModal" @click.self="showEditProfileModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑个人资料</h3>
          <button class="close-btn" @click="showEditProfileModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="avatar-upload-section">
            <div class="avatar-preview" @click="triggerAvatarUpload">
              <img :src="avatarPreview || userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="modal-avatar">
              <div class="avatar-overlay">
                <span>点击更换头像</span>
              </div>
            </div>
            <input type="file" id="avatar-upload" @change="handleAvatarChange" accept="image/*" ref="avatarInput" style="display: none">
          </div>
          <form @submit.prevent="updateProfile" class="profile-form">
            <div class="form-group">
              <label for="username">用户名</label>
              <input type="text" id="username" v-model="profileForm.username" placeholder="用户名">
            </div>
            <div class="form-group">
              <label for="email">邮箱</label>
              <input type="email" id="email" v-model="profileForm.email" placeholder="邮箱地址">
            </div>
            <div class="form-group">
              <label for="password">密码</label>
              <input type="password" id="password" v-model="profileForm.password" placeholder="留空表示不修改密码">
            </div>
            <div class="form-group">
              <label for="location">地区</label>
              <input type="text" id="location" v-model="profileForm.location" placeholder="您所在的地区">
            </div>
            <div class="form-group">
              <label for="bio">个人简介</label>
              <textarea id="bio" v-model="profileForm.bio" placeholder="介绍一下自己吧" rows="4"></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="showEditProfileModal = false">取消</button>
              <button type="submit" class="submit-btn">保存修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 绑定手机号弹窗 -->
    <div class="modal-overlay" v-if="showBindPhoneModal" @click.self="showBindPhoneModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ userInfo?.phoneNumber ? '更换手机号' : '绑定手机号' }}</h3>
          <button class="close-btn" @click="closeBindPhoneModal">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitBindPhone" class="bind-phone-form">
            <div class="form-group">
              <label for="phone">手机号</label>
              <input
                type="tel"
                id="phone"
                v-model="bindPhoneForm.phone"
                placeholder="请输入11位手机号"
                maxlength="11"
                :disabled="bindPhoneForm.codeSent"
              >
            </div>

            <div class="form-group">
              <label for="verification-code">验证码</label>
              <div class="code-input-group">
                <input
                  type="text"
                  id="verification-code"
                  v-model="bindPhoneForm.code"
                  placeholder="请输入6位验证码"
                  maxlength="6"
                >
                <button
                  type="button"
                  class="send-code-btn"
                  @click="sendVerificationCode"
                  :disabled="!canSendCode || sendCodeLoading"
                >
                  {{ sendCodeText }}
                </button>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="closeBindPhoneModal">取消</button>
              <button
                type="submit"
                class="submit-btn"
                :disabled="!bindPhoneForm.phone || !bindPhoneForm.code || bindPhoneLoading"
              >
                {{ bindPhoneLoading ? '绑定中...' : '确认绑定' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 充值弹窗 -->
    <div class="modal-overlay" v-if="showRechargeModal" @click.self="showRechargeModal = false">
      <div class="modal-content">
        <div class="modal-header">
          <h3>账户充值</h3>
          <button class="close-btn" @click="showRechargeModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="current-balance">
            <span class="balance-label">当前余额：</span>
            <span class="balance-amount">¥{{ formatBalance(userInfo?.balance) }}</span>
          </div>

          <form @submit.prevent="submitRecharge" class="recharge-form">
            <div class="form-group">
              <label for="recharge-amount">充值金额</label>
              <div class="amount-input-group">
                <span class="currency-symbol">¥</span>
                <input
                  type="number"
                  id="recharge-amount"
                  v-model="rechargeForm.amount"
                  placeholder="请输入充值金额"
                  min="0.01"
                  step="0.01"
                  :disabled="rechargeLoading"
                >
              </div>
            </div>

            <div class="quick-amount-buttons">
              <button
                type="button"
                v-for="amount in quickAmounts"
                :key="amount"
                class="quick-amount-btn"
                @click="selectQuickAmount(amount)"
                :disabled="rechargeLoading"
              >
                ¥{{ amount }}
              </button>
            </div>

            <div class="payment-method">
              <label>支付方式</label>
              <div class="payment-option">
                <input type="radio" id="alipay" value="alipay" v-model="rechargeForm.paymentMethod" disabled checked>
                <label for="alipay" class="payment-label">
                  <span class="payment-icon">💰</span>
                  支付宝支付
                </label>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="showRechargeModal = false">取消</button>
              <button
                type="submit"
                class="submit-btn recharge-submit-btn"
                :disabled="!rechargeForm.amount || rechargeForm.amount <= 0 || rechargeLoading"
              >
                {{ rechargeLoading ? '处理中...' : '立即充值' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="container">
      <div class="profile-wrapper">
        <aside class="sidebar">
          <div class="sidebar-header">
            <img :src="userInfo?.avatarUrl || defaultAvatar" :alt="userInfo?.username || '用户名'">
            <h2>{{ userInfo?.username || '用户名' }}</h2>
            <p>ID: {{ userInfo?.userId || '12345678' }}</p>
            <p v-if="userInfo?.phoneNumber" class="phone-info">手机号: {{ formatPhone(userInfo.phoneNumber) }}</p>
            <p v-else class="phone-info no-phone">未绑定手机号</p>

            <!-- 账户余额显示 -->
            <div class="balance-section">
              <div class="balance-info">
                <span class="balance-label">账户余额</span>
                <span class="balance-amount">¥{{ formatBalance(userInfo?.balance) }}</span>
              </div>
              <button class="recharge-btn" @click="showRechargeModal = true">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                </svg>
                充值
              </button>
            </div>

            <button class="edit-profile-btn" @click="editProfile">编辑资料</button>
            <button class="bind-phone-btn" @click="showBindPhoneModal = true">
              {{ userInfo?.phoneNumber ? '更换手机号' : '绑定手机号' }}
            </button>
          </div>
          <nav class="sidebar-nav">
            <ul>
              <li v-for="item in navItems" :key="item.id">
                <!-- 主菜单项 -->
                <a
                  :href="'#' + item.id"
                  :class="{
                    active: activeSection === item.id || (item.subItems && item.subItems.some(sub => sub.id === activeSection)),
                    'has-submenu': item.subItems
                  }"
                  @click.prevent="handleMenuClick(item)"
                >
                  <svg v-html="item.icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></svg>
                  {{ item.text }}
                  <svg v-if="item.subItems" class="submenu-arrow" :class="{ expanded: expandedMenus.includes(item.id) }" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </a>

                <!-- 子菜单 -->
                <ul v-if="item.subItems && expandedMenus.includes(item.id)" class="submenu">
                  <li v-for="subItem in item.subItems" :key="subItem.id">
                    <a
                      :href="'#' + subItem.id"
                      :class="{ active: activeSection === subItem.id }"
                      @click.prevent="setActiveSection(subItem.id)"
                    >
                      <svg v-html="subItem.icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></svg>
                      {{ subItem.text }}
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </nav>
        </aside>

        <main class="profile-content" id="profileContent">
          <div v-if="activeSection === 'my-products'">
            <h2 class="content-section-title">我的发布</h2>

            <!-- 加载指示器 -->
            <div v-if="productsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>

            <!-- 商品列表 -->
            <div v-else-if="myProducts.length > 0" class="my-products-grid">
              <div class="product-card" :class="{ 'non-editable': !canEditProduct(product.status) }" v-for="product in myProducts" :key="product.id">
                <div class="product-image">
                  <img :src="product.image" :alt="product.title">
                  <div class="product-status" :class="product.status.toLowerCase()">{{ getStatusText(product.status) }}</div>
                </div>
                <div class="product-info">
                  <h3 class="product-title">{{ product.title }}</h3>
                  <!-- 不可编辑提示 -->
                  <div v-if="!canEditProduct(product.status)" class="non-editable-tip">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="15" y1="9" x2="9" y2="15"></line>
                      <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    不可编辑
                  </div>
                  <div class="product-meta">
                    <span class="product-price">¥{{ product.price }}</span>
                    <span class="product-date">{{ formatDate(product.postedDate) }}</span>
                  </div>
                  <div class="product-actions">
                    <!-- 编辑按钮：管理员下架和已删除状态不可编辑 -->
                    <button
                      class="edit-btn"
                      @click="openEditModal(product)"
                      :disabled="!canEditProduct(product.status)"
                      :title="canEditProduct(product.status) ? '编辑商品' : '该状态下不可编辑'"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                      </svg>
                      编辑
                    </button>
                    <!-- 删除按钮：已删除状态不显示删除按钮 -->
                    <button
                      v-if="product.status !== 'deleted'"
                      class="delete-btn"
                      @click="confirmDeleteProduct(product.id)"
                      :disabled="product.status === 'off_shelf_by_admin'"
                      :title="product.status === 'off_shelf_by_admin' ? '管理员下架的商品不可删除' : '删除商品'"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                      </svg>
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="placeholder-content">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
              </svg>
              <p>您还没有发布任何商品</p>
              <router-link to="/publish" class="publish-product-btn">发布您的第一个商品</router-link>
            </div>

            <!-- 编辑商品弹窗 -->
            <div class="modal-overlay" v-if="showEditProductModal" @click.self="showEditProductModal = false">
              <div class="modal-content product-edit-modal">
                <div class="modal-header">
                  <h3>编辑商品</h3>
                  <button class="close-btn" @click="showEditProductModal = false">&times;</button>
                </div>
                <div class="modal-body">
                  <form @submit.prevent="updateProductInfo" class="product-form">
                    <!-- 添加商品图片上传区域 -->
                    <div class="form-group">
                      <label>商品图片</label>
                      <div class="product-image-upload">
                        <div class="product-image-preview" @click="triggerProductImageUpload">
                          <img :src="productImagePreview || (editProductForm.image ? editProductForm.image : 'https://via.placeholder.com/300x300')" alt="商品图片">
                          <div class="image-overlay">
                            <span>点击更换图片</span>
                          </div>
                        </div>
                        <input type="file" ref="productImageInput" @change="handleProductImageChange" accept="image/*" style="display: none">
                      </div>
                    </div>
                    <div class="form-group">
                      <label for="product-title">商品标题</label>
                      <input type="text" id="product-title" v-model="editProductForm.title" placeholder="商品标题">
                    </div>
                    <div class="form-group">
                      <label for="product-price">价格</label>
                      <input type="number" id="product-price" v-model="editProductForm.price" placeholder="价格" step="0.01">
                    </div>
                    <div class="form-group">
                      <label for="product-description">描述</label>
                      <textarea id="product-description" v-model="editProductForm.description" placeholder="商品描述" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                      <label for="product-status">商品状态</label>
                      <select id="product-status" v-model="editProductForm.status">
                        <option value="available">在售中</option>
                        <option value="sold">已售出</option>
                        <option value="off_shelf_by_seller">下架</option>
                      </select>
                    </div>
                    <div class="form-actions">
                      <button type="button" class="cancel-btn" @click="showEditProductModal = false">取消</button>
                      <button type="submit" class="submit-btn">保存修改</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>

          <div v-if="activeSection === 'my-orders'">
            <h2 class="content-section-title">我的订单</h2>
            <div class="order-tabs">
              <button :class="{ active: activeOrderTab === 'buyer' }" @click="activeOrderTab = 'buyer'">我买到的</button>
              <button :class="{ active: activeOrderTab === 'seller' }" @click="activeOrderTab = 'seller'">我卖出的</button>
            </div>

            <!-- Buyer Orders -->
            <div v-show="activeOrderTab === 'buyer'">
              <div class="order-filters">
                <select v-model="buyerStatusFilter" class="status-filter-select">
                  <option value="">全部状态</option>
                  <option value="pending_payment">待支付</option>
                  <option value="paid">已支付</option>
                  <option value="shipped">已发货</option>
                  <option value="delivered">已送达</option>
                  <option value="completed">已完成</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>

              <div v-if="buyerOrdersLoading" class="loading-indicator">
                <div class="spinner"></div>
                <span>加载中...</span>
              </div>
              <div v-else-if="buyerOrders.length > 0" class="order-list">
                <div v-for="order in buyerOrders" :key="order.orderId" class="order-item">
                  <div class="order-header">
                    <span class="order-date">下单时间：{{ formatDate(order.orderDate) }}</span>
                    <span class="order-id">订单号：{{ order.orderId }}</span>
                    <div class="seller-info">
                      <span>卖家：</span>
                      <span class="seller-username" @click="goToSellerProfile(order.sellerId)">{{ order.sellerUsername }}</span>
                    </div>
                  </div>
                  <div class="order-body">
                    <div class="product-details">
                      <img :src="order.productImage || defaultProductImage" alt="商品图片" class="product-image-thumbnail" @click="goToProductDetail(order.productId)">
                      <div class="product-info">
                        <p class="product-title" @click="goToProductDetail(order.productId)">{{ order.productTitle }}</p>
                      </div>
                    </div>
                    <div class="order-price">¥{{ order.totalAmount.toFixed(2) }}</div>
                    <div class="order-status">{{ getOrderStatusText(order.status) }}</div>
                    <div class="order-actions-container">
                      <!-- Buyer Actions -->
                      <div v-if="order.status === 'pending_payment'" class="pending-payment-actions">
                        <button @click="handlePayOrder(order.orderId)" class="action-btn-pay">立即支付</button>
                        <button @click="handleCancelOrder(order.orderId)" class="action-btn-cancel">取消订单</button>
                      </div>
                      <button v-if="order.status === 'delivered'" @click="handleCompleteOrder(order.orderId)" class="action-btn-confirm">确认收货</button>
                      <!-- 退款申请按钮 -->
                      <button v-if="canRefund(order.status)" @click="showRefundDialog(order)" class="action-btn-refund">申请退款</button>
                      <!-- 查看验货按钮 -->
                      <button v-if="order.officialVerification" @click="viewVerification(order.orderId)" class="action-btn-verification">查看验货</button>
                    </div>
                  </div>
                  <div class="order-footer">
                    <span class="address-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                    </span>
                    <span class="shipping-address">收货地址: {{ order.shippingAddress }}</span>
                  </div>
                </div>
                <div class="pagination">
                  <button class="page-button" :disabled="buyerPage <= 1" @click="changeBuyerPage(buyerPage - 1)">上一页</button>
                  <span class="page-info">{{ buyerPage }} / {{ buyerTotalPages }}</span>
                  <button class="page-button" :disabled="buyerPage >= buyerTotalPages" @click="changeBuyerPage(buyerPage + 1)">下一页</button>
                </div>
              </div>
              <div v-else class="placeholder-content">
                <p>您还没有买到任何商品哦~</p>
              </div>
            </div>

            <!-- Seller Orders -->
            <div v-show="activeOrderTab === 'seller'">
              <div class="order-filters">
                <select v-model="sellerStatusFilter" class="status-filter-select">
                  <option value="">全部状态</option>
                  <option value="pending_payment">待支付</option>
                  <option value="paid">已支付</option>
                  <option value="shipped">已发货</option>
                  <option value="delivered">已送达</option>
                  <option value="completed">已完成</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>

              <div v-if="sellerOrdersLoading" class="loading-indicator">
                <div class="spinner"></div>
                <span>加载中...</span>
              </div>
              <div v-else-if="sellerOrders.length > 0" class="order-list">
                <div v-for="order in sellerOrders" :key="order.orderId" class="order-item">
                   <div class="order-header">
                    <span class="order-date">下单时间：{{ formatDate(order.orderDate) }}</span>
                    <span class="order-id">订单号：{{ order.orderId }}</span>
                    <div class="seller-info">
                      <span>买家：</span>
                      <span class="seller-username">{{ order.buyerUsername }}</span>
                    </div>
                  </div>
                  <div class="order-body">
                    <div class="product-details">
                      <img :src="order.productImage || defaultProductImage" alt="商品图片" class="product-image-thumbnail" @click="goToProductDetail(order.productId)">
                      <div class="product-info">
                        <p class="product-title" @click="goToProductDetail(order.productId)">{{ order.productTitle }}</p>
                      </div>
                    </div>
                    <div class="order-price">¥{{ order.totalAmount.toFixed(2) }}</div>
                    <div class="order-status">{{ getOrderStatusText(order.status) }}</div>
                    <div class="order-actions-container">
                      <!-- Seller Actions -->
                      <button v-if="order.status === 'paid'" @click="handleShipOrder(order.orderId)" class="action-btn-ship">发货</button>
                    </div>
                  </div>
                  <div class="order-footer">
                    <span class="address-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                    </span>
                    <span class="shipping-address">收货地址: {{ order.shippingAddress }}</span>
                  </div>
                </div>
                <div class="pagination">
                  <button class="page-button" :disabled="sellerPage <= 1" @click="changeSellerPage(sellerPage - 1)">上一页</button>
                  <span class="page-info">{{ sellerPage }} / {{ sellerTotalPages }}</span>
                  <button class="page-button" :disabled="sellerPage >= sellerTotalPages" @click="changeSellerPage(sellerPage + 1)">下一页</button>
                </div>
              </div>
              <div v-else class="placeholder-content">
                <p>您还没有卖出任何商品哦~</p>
              </div>
            </div>
          </div>

          <!-- 退款管理 -->
          <div v-if="activeSection === 'my-refunds'">
            <h2 class="content-section-title">我的退款申请</h2>
            <div v-if="refundsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>
            <div v-else-if="myRefunds.length > 0" class="refunds-list">
              <div v-for="refund in myRefunds" :key="refund?.refundId || Math.random()" class="refund-item">
                <div v-if="refund && refund.refundId" class="refund-header">
                  <span class="refund-id">退款单号：{{ refund.refundId }}</span>
                  <span class="refund-status" :class="getRefundStatusClass(refund.status)">
                    {{ getRefundStatusText(refund.status) }}
                  </span>
                </div>
                <div v-if="refund" class="refund-content">
                  <div class="refund-info">
                    <h3>{{ refund.productTitle || '未知商品' }}</h3>
                    <p class="refund-amount">退款金额：¥{{ refund.refundAmount || 0 }}</p>
                    <p class="refund-reason">退款原因：{{ refund.refundReason || '无' }}</p>
                  </div>
                  <div class="refund-actions">
                    <button v-if="refund.status === 'seller_rejected'" @click="requestAdminIntervention(refund)" class="action-btn-intervention">
                      申请管理员介入
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>您还没有退款申请记录</p>
            </div>
          </div>

          <!-- 待处理退款（卖家） -->
          <div v-if="activeSection === 'seller-refunds'">
            <h2 class="content-section-title">待处理退款</h2>
            <div v-if="refundsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>
            <div v-else-if="sellerRefunds.length > 0" class="refunds-list">
              <div v-for="refund in sellerRefunds" :key="refund?.refundId || Math.random()" class="refund-item">
                <div v-if="refund && refund.refundId" class="refund-header">
                  <span class="refund-id">退款单号：{{ refund.refundId }}</span>
                  <span class="refund-status" :class="getRefundStatusClass(refund.status)">
                    {{ getRefundStatusText(refund.status) }}
                  </span>
                </div>
                <div v-if="refund" class="refund-content">
                  <div class="refund-info">
                    <h3>{{ refund.productTitle || '未知商品' }}</h3>
                    <p class="refund-amount">退款金额：¥{{ refund.refundAmount || 0 }}</p>
                    <p class="refund-reason">退款原因：{{ refund.refundReason || '无' }}</p>
                    <p class="buyer-name">买家：{{ refund.buyerName || '未知' }}</p>
                  </div>
                  <div class="refund-actions" v-if="refund.status === 'pending_seller'">
                    <button @click="handleRefund(refund, true)" class="action-btn-approve">同意退款</button>
                    <button @click="showRejectDialog(refund)" class="action-btn-reject">拒绝退款</button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>暂无待处理的退款申请</p>
            </div>
          </div>

          <!-- 强制退款任务 -->
          <div v-if="activeSection === 'forced-refunds'">
            <h2 class="content-section-title">强制退款任务</h2>
            <div class="warning-notice">
              <h3>⚠️ 重要提醒</h3>
              <p>您有未完成的强制退款任务，在完成所有任务前，您将无法发布新商品。</p>
            </div>
            <div v-if="forcedRefundsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>
            <div v-else-if="forcedRefunds.length > 0" class="forced-refunds-list">
              <div v-for="task in forcedRefunds" :key="task?.refundId || Math.random()" class="forced-refund-item">
                <div v-if="task && task.refundId" class="task-header">
                  <span class="task-id">任务ID：{{ task.refundId }}</span>
                  <span class="task-amount">需退款：¥{{ task.refundAmount || 0 }}</span>
                </div>
                <div v-if="task" class="task-content">
                  <div class="task-info">
                    <h3>{{ task.productTitle || '未知商品' }}</h3>
                    <p class="buyer-info">买家：{{ task.buyerName || '未知' }}</p>
                    <p class="admin-note">管理员说明：{{ task.adminResponse || '无' }}</p>
                  </div>
                  <div class="task-actions">
                    <button v-if="canProcessForcedRefund(task.refundAmount)" @click="processForcedRefund(task)" class="action-btn-process">
                      立即处理
                    </button>
                    <button v-else class="action-btn-recharge" @click="goToRecharge">
                      余额不足，去充值
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>🎉 恭喜！您没有待处理的强制退款任务</p>
            </div>
          </div>

          <!-- 我的验货记录 -->
          <div v-if="activeSection === 'my-verifications'">
            <h2 class="content-section-title">我的验货记录</h2>
            <div v-if="verificationsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>
            <div v-else-if="myVerifications.length > 0" class="verifications-list">
              <div v-for="verification in myVerifications" :key="verification.verificationId" class="verification-item">
                <div class="verification-header">
                  <span class="verification-id">验货单号：{{ verification.verificationId }}</span>
                  <span class="verification-status" :class="getVerificationStatusClass(verification.verificationStatus)">
                    {{ getVerificationStatusText(verification.verificationStatus) }}
                  </span>
                </div>
                <div class="verification-content">
                  <div class="verification-info">
                    <h3>{{ verification.productTitle }}</h3>
                    <p class="verification-result" v-if="verification.verificationResult">
                      验货结果：{{ verification.verificationResult }}
                    </p>
                    <p class="verification-date">创建时间：{{ formatDate(verification.createdTime) }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>您还没有验货记录</p>
            </div>
          </div>

          <!-- 待验货列表（管理员） -->
          <div v-if="activeSection === 'pending-verifications'">
            <h2 class="content-section-title">待验货列表</h2>
            <div v-if="verificationsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>
            <div v-else-if="pendingVerifications.length > 0" class="verifications-list">
              <div v-for="verification in pendingVerifications" :key="verification.verificationId" class="verification-item">
                <div class="verification-header">
                  <span class="verification-id">验货单号：{{ verification.verificationId }}</span>
                  <span class="verification-status" :class="getVerificationStatusClass(verification.verificationStatus)">
                    {{ getVerificationStatusText(verification.verificationStatus) }}
                  </span>
                </div>
                <div class="verification-content">
                  <div class="verification-info">
                    <h3>{{ verification.productTitle }}</h3>
                    <p class="buyer-seller-info">买家：{{ verification.buyerName }} | 卖家：{{ verification.sellerName }}</p>
                  </div>
                  <div class="verification-actions" v-if="verification.verificationStatus === 'verifying'">
                    <button @click="showVerificationResultDialog(verification)" class="action-btn-verify">
                      提交验货结果
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="placeholder-content">
              <p>暂无待验货商品</p>
            </div>
          </div>

          <div v-if="activeSection === 'my-favorites'">
            <h2 class="content-section-title">我的收藏</h2>

            <!-- 加载指示器 -->
            <div v-if="favoritesLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>

            <!-- 收藏列表 -->
            <div v-else-if="favorites.length > 0" class="favorites-grid">
              <div class="favorite-card" v-for="favorite in favorites" :key="favorite.id">
                <div class="favorite-image" @click="goToProductDetail(favorite.productId)">
                  <img :src="favorite.productImage" :alt="favorite.productTitle">
                </div>
                <div class="favorite-info">
                  <h3 class="favorite-title" @click="goToProductDetail(favorite.productId)">{{ favorite.productTitle }}</h3>
                  <div class="favorite-meta">
                    <span class="favorite-price">¥{{ favorite.productPrice }}</span>
                    <button class="unfavorite-btn" @click="removeFavoriteItem(favorite.productId)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#FF0000" stroke="#FF0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                      </svg>
                      取消收藏
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="placeholder-content">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
              <p>您还没有收藏任何商品</p>
              <router-link to="/home" class="browse-products-btn">去浏览商品</router-link>
            </div>
          </div>

          <div v-if="activeSection === 'my-followings'">
            <h2 class="content-section-title">我的关注</h2>

            <!-- 加载指示器 -->
            <div v-if="followingsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>

            <!-- 关注列表 -->
            <div v-else-if="followings.length > 0" class="followings-list">
              <div class="following-item" v-for="following in followings" :key="following.followId">
                <div class="following-avatar" @click="goToSellerProfile(following.followingId)">
                  <img :src="following.followingAvatarUrl || defaultAvatar.value" :alt="following.followingUsername">
                </div>
                <div class="following-info">
                  <h3 class="following-username" @click="goToSellerProfile(following.followingId)">{{ following.followingUsername }}</h3>
                  <span class="following-date">关注于 {{ formatDate(following.followedAt) }}</span>
                </div>
                <div class="following-actions">
                  <button class="view-profile-btn" @click="goToSellerProfile(following.followingId)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    查看主页
                  </button>
                  <button class="unfollow-btn" @click="removeFollowing(following.followingId)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <line x1="18" y1="8" x2="23" y2="13"></line>
                      <line x1="23" y1="8" x2="18" y2="13"></line>
                    </svg>
                    取消关注
                  </button>
            </div>
          </div>

              <!-- 分页控件 -->
              <div class="pagination">
                <button class="page-button" :disabled="followingsPage <= 1" @click="changeFollowingsPage(followingsPage - 1)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                  上一页
                </button>
                <span class="page-info">{{ followingsPage }} / {{ followingsTotalPages }}</span>
                <button class="page-button" :disabled="followingsPage >= followingsTotalPages" @click="changeFollowingsPage(followingsPage + 1)">
                  下一页
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="placeholder-content">
              <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <p>您还没有关注任何商家</p>
              <router-link to="/home" class="browse-sellers-btn">去浏览商家</router-link>
            </div>
          </div>

          <div v-if="activeSection === 'my-addresses'">
            <h2 class="content-section-title">地址管理</h2>

            <!-- 加载指示器 -->
            <div v-if="addressesLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>

            <!-- 添加地址按钮 -->
            <div v-else class="address-actions">
              <button class="add-address-btn" @click="openAddAddressForm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                添加新地址
              </button>
            </div>

            <!-- 地址列表 -->
            <div v-if="!addressesLoading" class="addresses-list">
              <div v-if="addresses.length === 0" class="no-addresses">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <p>您还没有添加任何地址</p>
              </div>

              <div v-else class="address-cards">
                <div v-for="address in currentPageAddresses" :key="address.addressId" class="address-card">
                  <div class="address-content">
                    <div class="address-header">
                      <h3 class="address-name">{{ address.recipientName }} <span class="address-phone">{{ address.phoneNumber }}</span></h3>
                      <div v-if="address.isDefault" class="default-badge">默认地址</div>
                    </div>
                    <p class="address-detail">{{ address.province }}{{ address.city }}{{ address.district }}{{ address.streetAddress }}</p>
                    <div class="address-card-actions">
                      <button class="address-edit-btn" @click="openEditAddressForm(address)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                        </svg>
                        编辑
                      </button>
                      <button v-if="!address.isDefault" class="address-default-btn" @click="setAddressAsDefault(address.addressId)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                          <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                        设为默认
                      </button>
                      <button class="address-delete-btn" @click="removeAddress(address.addressId)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                        删除
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 分页控件 -->
                <div v-if="addresses.length > 0" class="pagination">
                  <button class="page-button" :disabled="addressesPage <= 1" @click="changeAddressesPage(addressesPage - 1)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                    上一页
                  </button>
                  <span class="page-info">{{ addressesPage }} / {{ addressesTotalPages }}</span>
                  <button class="page-button" :disabled="addressesPage >= addressesTotalPages" @click="changeAddressesPage(addressesPage + 1)">
                    下一页
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 地址表单弹窗 -->
            <div class="modal-overlay" v-if="showAddressForm" @click.self="showAddressForm = false">
              <div class="modal-content address-form-modal">
                <div class="modal-header">
                  <h3>{{ isEditingAddress ? '编辑地址' : '添加新地址' }}</h3>
                  <button class="close-btn" @click="showAddressForm = false">&times;</button>
                </div>
                <div class="modal-body">
                  <form @submit.prevent="submitAddressForm" class="address-form">
                    <div class="form-group">
                      <label for="recipientName">收货人姓名</label>
                      <input
                        type="text"
                        id="recipientName"
                        v-model="addressForm.recipientName"
                        placeholder="请输入收货人姓名"
                        :class="{ 'error-input': addressFormErrors.recipientName }"
                      >
                      <div class="error-message" v-if="addressFormErrors.recipientName">{{ addressFormErrors.recipientName }}</div>
                    </div>

                    <div class="form-group">
                      <label for="phoneNumber">手机号码</label>
                      <input
                        type="text"
                        id="phoneNumber"
                        v-model="addressForm.phoneNumber"
                        placeholder="请输入手机号码"
                        :class="{ 'error-input': addressFormErrors.phoneNumber }"
                      >
                      <div class="error-message" v-if="addressFormErrors.phoneNumber">{{ addressFormErrors.phoneNumber }}</div>
                    </div>

                    <div class="form-row">
                      <div class="form-group">
                        <label for="province">省份</label>
                        <input
                          type="text"
                          id="province"
                          v-model="addressForm.province"
                          placeholder="请输入省份"
                          :class="{ 'error-input': addressFormErrors.province }"
                        >
                        <div class="error-message" v-if="addressFormErrors.province">{{ addressFormErrors.province }}</div>
                      </div>

                      <div class="form-group">
                        <label for="city">城市</label>
                        <input
                          type="text"
                          id="city"
                          v-model="addressForm.city"
                          placeholder="请输入城市"
                          :class="{ 'error-input': addressFormErrors.city }"
                        >
                        <div class="error-message" v-if="addressFormErrors.city">{{ addressFormErrors.city }}</div>
                      </div>
                    </div>

                    <div class="form-group">
                      <label for="district">区/县</label>
                      <input
                        type="text"
                        id="district"
                        v-model="addressForm.district"
                        placeholder="请输入区/县（可选）"
                      >
                    </div>

                    <div class="form-group">
                      <label for="streetAddress">详细地址</label>
                      <textarea
                        id="streetAddress"
                        v-model="addressForm.streetAddress"
                        placeholder="请输入详细地址"
                        :class="{ 'error-input': addressFormErrors.streetAddress }"
                        rows="3"
                      ></textarea>
                      <div class="error-message" v-if="addressFormErrors.streetAddress">{{ addressFormErrors.streetAddress }}</div>
                    </div>

                    <div class="form-group checkbox">
                      <div class="checkbox-wrapper">
                        <input type="checkbox" id="isDefault" v-model="addressForm.isDefault">
                        <label for="isDefault">设为默认地址</label>
                      </div>
                    </div>

                    <div class="form-actions">
                      <button type="button" class="cancel-btn" @click="showAddressForm = false">取消</button>
                      <button type="submit" class="submit-btn">保存</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>

          <!-- 补充材料 -->
          <div v-if="activeSection === 'supplementary-materials'">
            <h2 class="content-section-title">补充材料</h2>

            <!-- 材料请求列表 -->
            <div v-if="materialRequestsLoading" class="loading-indicator">
              <div class="spinner"></div>
              <span>加载中...</span>
            </div>

            <div v-else-if="materialRequests.length === 0" class="empty-state">
              <div class="empty-icon">📄</div>
              <h3>暂无材料请求</h3>
              <p>当前没有需要补充的材料</p>
            </div>

            <div v-else class="material-requests-list">
              <div
                v-for="request in materialRequests"
                :key="request.requestId"
                class="material-request-card"
              >
                <div class="request-header">
                  <div class="request-info">
                    <h4>材料请求 #{{ request.requestId }}</h4>
                    <span class="request-status" :class="getRequestStatusClass(request.status)">
                      {{ getRequestStatusText(request.status) }}
                    </span>
                  </div>
                  <div class="request-time">
                    <span>请求时间：{{ formatDateTime(request.requestTime) }}</span>
                    <span v-if="request.deadline">截止时间：{{ formatDateTime(request.deadline) }}</span>
                  </div>
                </div>

                <div class="request-content">
                  <div class="product-info">
                    <strong>相关商品：</strong>{{ request.productTitle || '未知商品' }}
                  </div>

                  <div class="request-reason">
                    <strong>请求原因：</strong>
                    <p>{{ request.requestReason }}</p>
                  </div>

                  <div class="required-materials">
                    <strong>需要提供的材料：</strong>
                    <ul>
                      <li v-for="material in parseRequiredMaterials(request.requiredMaterials)" :key="material">
                        {{ material }}
                      </li>
                    </ul>
                  </div>

                  <!-- 已提交的材料 -->
                  <div v-if="request.submittedMaterials && request.submittedMaterials.length > 0" class="submitted-materials">
                    <strong>已提交的材料：</strong>
                    <div class="materials-list">
                      <div
                        v-for="material in request.submittedMaterials"
                        :key="material.materialId"
                        class="material-item"
                      >
                        <div class="material-header">
                          <span class="material-type">{{ material.materialType }}</span>
                          <span class="submit-time">{{ formatDateTime(material.submitTime) }}</span>
                        </div>
                        <div class="material-description">{{ material.description }}</div>
                        <div class="material-files">
                          <a
                            v-for="(url, index) in parseMaterialUrls(material.materialUrls)"
                            :key="index"
                            :href="url"
                            target="_blank"
                            class="material-file-link"
                          >
                            查看文件{{ index + 1 }}
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 提交材料按钮 -->
                  <div v-if="request.status === 'waiting'" class="request-actions">
                    <button @click="openSubmitMaterialModal(request)" class="submit-material-btn">
                      提交材料
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- <div v-if="activeSection === 'message-center'">
            <h2 class="content-section-title">消息中心</h2>
            <div class="placeholder-content">
              <p>这里将显示您的消息。</p>
            </div>
          </div> -->
        </main>
      </div>
    </div>

    <!-- 提交材料弹窗 -->
    <div class="modal-overlay" v-if="showSubmitMaterialModal" @click.self="showSubmitMaterialModal = false">
      <div class="modal-content submit-material-modal">
        <div class="modal-header">
          <h3>提交补充材料</h3>
          <button class="close-btn" @click="showSubmitMaterialModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitMaterial" class="material-form">
            <div class="form-group">
              <label for="materialType">材料类型</label>
              <select id="materialType" v-model="materialForm.materialType" required>
                <option value="">请选择材料类型</option>
                <option value="invoice">商品发票</option>
                <option value="authorization">品牌授权书</option>
                <option value="certificate">正品证明</option>
                <option value="specification">产品规格书</option>
                <option value="photo">商品照片</option>
                <option value="other">其他材料</option>
              </select>
            </div>

            <div class="form-group">
              <label for="description">材料说明</label>
              <textarea
                id="description"
                v-model="materialForm.description"
                placeholder="请详细说明提交的材料内容"
                rows="4"
                required
              ></textarea>
            </div>

            <div class="form-group">
              <label>上传文件</label>
              <div class="file-upload-area" @click="triggerFileUpload">
                <input
                  type="file"
                  ref="materialFileInput"
                  @change="handleFileUpload"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  style="display: none"
                >
                <div v-if="materialForm.files.length === 0" class="upload-placeholder">
                  <div class="upload-icon">📁</div>
                  <p>点击上传文件</p>
                  <p class="upload-hint">支持图片、PDF、Word文档</p>
                </div>
                <div v-else class="uploaded-files">
                  <div v-for="(file, index) in materialForm.files" :key="index" class="file-item">
                    <span class="file-name">{{ file.name }}</span>
                    <button type="button" @click.stop="removeFile(index)" class="remove-file-btn">&times;</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="showSubmitMaterialModal = false">取消</button>
              <button type="submit" class="submit-btn" :disabled="materialSubmitting">
                {{ materialSubmitting ? '提交中...' : '提交材料' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 退款申请模态框 -->
    <div v-if="showRefundModal" class="modal-overlay" @click="showRefundModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>申请退款</h3>
          <button class="close-btn" @click="showRefundModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="refund-form">
            <div class="form-group">
              <label>退款类型</label>
              <select v-model="refundForm.refundType" class="form-control">
                <option value="refund_only">仅退款</option>
                <option value="return_refund">退货退款</option>
              </select>
            </div>

            <div class="form-group">
              <label>退款金额</label>
              <input
                type="number"
                v-model="refundForm.refundAmount"
                class="form-control"
                :max="selectedOrder?.totalAmount"
                step="0.01"
                placeholder="请输入退款金额">
              <div class="form-hint">最大可退款金额：¥{{ selectedOrder?.totalAmount }}</div>
            </div>

            <div class="form-group">
              <label>退款原因</label>
              <textarea
                v-model="refundForm.refundReason"
                class="form-control"
                rows="4"
                placeholder="请详细说明退款原因..."
                maxlength="500"></textarea>
              <div class="form-hint">{{ refundForm.refundReason?.length || 0 }}/500</div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showRefundModal = false">取消</button>
          <button class="submit-btn" @click="submitRefund" :disabled="!refundForm.refundReason?.trim()">
            提交申请
          </button>
        </div>
      </div>
    </div>

    <!-- 拒绝退款模态框 -->
    <div v-if="showRejectModal" class="modal-overlay" @click="showRejectModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>拒绝退款</h3>
          <button class="close-btn" @click="showRejectModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="reject-form">
            <div class="form-group">
              <label>拒绝原因</label>
              <textarea
                v-model="rejectReason"
                class="form-control"
                rows="4"
                placeholder="请详细说明拒绝退款的原因..."
                maxlength="500"></textarea>
              <div class="form-hint">{{ rejectReason?.length || 0 }}/500</div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showRejectModal = false">取消</button>
          <button class="submit-btn" @click="confirmReject" :disabled="!rejectReason?.trim()">
            确认拒绝
          </button>
        </div>
      </div>
    </div>

    <!-- 申请管理员介入模态框 -->
    <div v-if="showInterventionModal" class="modal-overlay" @click="cancelInterventionRequest">
      <div class="modal-content intervention-modal" @click.stop>
        <div class="modal-header">
          <h3>申请管理员介入</h3>
          <button class="close-btn" @click="cancelInterventionRequest">&times;</button>
        </div>
        <div class="modal-body">
          <div class="intervention-form">
            <div class="form-group">
              <label>补充说明</label>
              <textarea
                v-model="interventionForm.evidence"
                class="form-control"
                rows="4"
                placeholder="请详细说明您的情况，提供更多证据..."
                maxlength="1000"></textarea>
              <div class="form-hint">{{ interventionForm.evidence?.length || 0 }}/1000</div>
            </div>

            <div class="form-group">
              <label>上传证据材料</label>
              <div class="file-upload-area">
                <input
                  type="file"
                  ref="fileInput"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  @change="handleFileChange"
                  style="display: none"
                />
                <button type="button" class="upload-btn" @click="$refs.fileInput.click()">
                  <i class="upload-icon">📎</i>
                  {{ interventionForm.files.length > 0 ? '继续添加文件' : '选择文件' }}
                </button>
                <div class="upload-hint">
                  支持图片、PDF、Word文档，单个文件最大10MB，最多上传10个文件<br>
                  可以多次点击选择不同的文件进行累加
                </div>
              </div>

              <!-- 文件列表 -->
              <div v-if="interventionForm.files.length > 0" class="file-list">
                <div class="file-list-header">
                  <span class="file-count">已选择 {{ interventionForm.files.length }} 个文件</span>
                  <button type="button" class="clear-all-btn" @click="clearAllFiles">清空所有</button>
                </div>
                <div v-for="(file, index) in interventionForm.files" :key="index" class="file-item">
                  <div class="file-info">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ (file.size / 1024 / 1024).toFixed(2) }}MB</span>
                  </div>
                  <button type="button" class="remove-file-btn" @click="removeInterventionFile(index)">
                    ×
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="cancelInterventionRequest">取消</button>
          <button
            class="submit-btn"
            @click="submitInterventionRequest"
            :disabled="!interventionForm.evidence?.trim() && interventionForm.files.length === 0">
            提交申请
          </button>
        </div>
      </div>
    </div>

    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />

    <!-- 评价弹窗 -->
    <ReviewDialog
      v-model="showReviewDialog"
      :order-info="reviewOrderInfo"
      @success="handleReviewSuccess"
      @skip="handleReviewSkip" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useUserStore } from '@/stores/user';
import { updateUserInfo, updateAvatar, bindPhone } from '@/api/user';
import { sendSmsCode } from '@/api/sms';
import { getUserFavorites, removeFavorite } from '@/api/favorite';
import { getUserProducts, deleteProduct, updateProduct, updateProductStatus, updateProductImage } from '@/api/product';
import { getUserFollowings, unfollowUser } from '@/api/follow';
import { getUserAddresses, addAddress, updateAddress, deleteAddress, setDefaultAddress } from '@/api/address';
import { getBuyerOrders, getSellerOrders, cancelOrder, shipOrder, completeOrder, getPaymentInfo, payWithBalance } from '@/api/order';
import ReviewDialog from '@/components/ReviewDialog.vue';
import { getMaterialRequests, submitSupplementaryMaterial, getSupplementaryMaterials } from '@/api/material';
import { createRecharge } from '@/api/recharge';
import { getRefundList, createRefund, handleSellerRefund, requestAdminIntervention as requestAdminInterventionAPI, requestAdminInterventionWithFiles, processForcedRefund as processForcedRefundAPI, getForcedRefundTasks } from '@/api/refund';
import { getPendingVerifications, getAllPendingVerifications, submitVerificationResult as submitVerificationResultAPI, getVerificationByOrderId } from '@/api/verification';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import FloatingActionButtons from '@/components/FloatingActionButtons.vue';

// 路由实例
const router = useRouter();

// 用户状态管理
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 表单数据
const profileForm = ref({
  username: '',
  email: '',
  password: '',
  location: '',
  bio: ''
});

// 控制编辑弹窗显示
const showEditProfileModal = ref(false);

// 充值相关
const showRechargeModal = ref(false);
const rechargeLoading = ref(false);
const rechargeForm = ref({
  amount: '',
  paymentMethod: 'alipay'
});
const quickAmounts = [10, 50, 100, 200, 500, 1000];

// 绑定手机号相关
const showBindPhoneModal = ref(false);
const bindPhoneForm = ref({
  phone: '',
  code: '',
  codeSent: false
});
const sendCodeLoading = ref(false);
const bindPhoneLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref(null);

// 评价相关
const showReviewDialog = ref(false);
const reviewOrderInfo = ref({});

// 头像上传
const avatarInput = ref(null);
const avatarFile = ref(null);
const avatarPreview = ref('');

const user = ref({
  id: '12345678',
  name: '用户名',
  avatar: 'https://via.placeholder.com/80/FF6347/FFFFFF?text=User',
});

const activeSection = ref('my-products');

const navItems = ref([
  { id: 'my-products', text: '我的发布', icon: '<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path>' },
  { id: 'my-orders', text: '我的订单', icon: '<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path>' },
  {
    id: 'refund-management',
    text: '退款管理',
    icon: '<path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>',
    subItems: [
      { id: 'my-refunds', text: '我的退款申请', icon: '<path d="M9 12l2 2 4-4"></path><path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.35 0 4.49.91 6.08 2.4"></path>' },
      { id: 'seller-refunds', text: '待处理退款', icon: '<path d="M12 8v4l3 3"></path><circle cx="12" cy="12" r="10"></circle>' },
      { id: 'forced-refunds', text: '强制退款任务', icon: '<path d="M12 9v3m0 3h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>' }
    ]
  },
  { id: 'my-favorites', text: '我的收藏', icon: '<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>' },
  { id: 'my-followings', text: '我的关注', icon: '<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>' },
  { id: 'my-addresses', text: '地址管理', icon: '<path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle>' },
  {
    id: 'others',
    text: '其他',
    icon: '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>',
    subItems: [
      { id: 'supplementary-materials', text: '补充材料', icon: '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline>' }
    ]
  }
]);

// 展开的菜单
const expandedMenus = ref([]);

// 补充材料相关
const materialRequests = ref([]);
const materialRequestsLoading = ref(false);
const showSubmitMaterialModal = ref(false);
const materialSubmitting = ref(false);
const currentMaterialRequest = ref(null);

const materialForm = ref({
  materialType: '',
  description: '',
  files: []
});

const materialFileInput = ref(null);

const setActiveSection = (sectionId) => {
  activeSection.value = sectionId;
};

// 处理菜单点击
const handleMenuClick = (item) => {
  if (item.subItems) {
    // 如果有子菜单，切换展开状态
    const index = expandedMenus.value.indexOf(item.id);
    if (index > -1) {
      expandedMenus.value.splice(index, 1);
    } else {
      expandedMenus.value.push(item.id);
    }
  } else {
    // 如果没有子菜单，直接设置为活动状态
    setActiveSection(item.id);
  }
};

// 获取材料请求列表
const fetchMaterialRequests = async () => {
  if (activeSection.value !== 'supplementary-materials') return;

  materialRequestsLoading.value = true;
  try {
    const response = await getMaterialRequests();
    if (response.success) {
      // 过滤掉null值
      materialRequests.value = (response.data || []).filter(request => request !== null);

      // 获取每个请求的已提交材料
      for (const request of materialRequests.value) {
        if (request && request.requestId) {
          try {
            const materialsResponse = await getSupplementaryMaterials(request.requestId);
            if (materialsResponse.success) {
              request.submittedMaterials = materialsResponse.data || [];
            }
          } catch (error) {
            console.error('获取已提交材料失败:', error);
            request.submittedMaterials = [];
          }
        } else {
          console.warn('发现无效的材料请求对象:', request);
        }
      }
    } else {
      ElMessage.error(response.message || '获取材料请求失败');
      materialRequests.value = [];
    }
  } catch (error) {
    console.error('获取材料请求失败:', error);
    ElMessage.error('获取材料请求失败');
    materialRequests.value = [];
  } finally {
    materialRequestsLoading.value = false;
  }
};

// 解析需要的材料
const parseRequiredMaterials = (materials) => {
  if (!materials) return [];
  try {
    return JSON.parse(materials);
  } catch (e) {
    return [materials];
  }
};

// 解析材料文件URL
const parseMaterialUrls = (urls) => {
  if (!urls) return [];
  try {
    return JSON.parse(urls);
  } catch (e) {
    return [urls];
  }
};

// 获取请求状态样式
const getRequestStatusClass = (status) => {
  const statusMap = {
    'waiting': 'status-waiting',
    'submitted': 'status-submitted',
    'approved': 'status-approved',
    'rejected': 'status-rejected'
  };
  return statusMap[status] || 'status-default';
};

// 获取请求状态文本
const getRequestStatusText = (status) => {
  const statusMap = {
    'waiting': '等待提交',
    'submitted': '已提交',
    'approved': '已通过',
    'rejected': '已拒绝'
  };
  return statusMap[status] || status;
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';

  // 处理数组格式的时间 [year, month, day, hour, minute, second]
  if (Array.isArray(dateTime)) {
    const [year, month, day, hour, minute, second] = dateTime;
    const date = new Date(year, month - 1, day, hour, minute, second);
    return date.toLocaleString('zh-CN');
  }

  // 处理标准格式的时间
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 打开提交材料弹窗
const openSubmitMaterialModal = (request) => {
  currentMaterialRequest.value = request;
  materialForm.value = {
    materialType: '',
    description: '',
    files: []
  };
  showSubmitMaterialModal.value = true;
};

// 触发文件上传
const triggerFileUpload = () => {
  materialFileInput.value?.click();
};

// 处理文件上传
const handleFileUpload = (event) => {
  const files = Array.from(event.target.files);
  materialForm.value.files.push(...files);
};

// 移除文件
const removeFile = (index) => {
  materialForm.value.files.splice(index, 1);
};

// 提交材料
const submitMaterial = async () => {
  if (!currentMaterialRequest.value) return;

  materialSubmitting.value = true;
  try {
    const formData = new FormData();
    formData.append('requestId', currentMaterialRequest.value.requestId);
    formData.append('materialType', materialForm.value.materialType);
    formData.append('description', materialForm.value.description);

    // 添加文件
    materialForm.value.files.forEach(file => {
      formData.append('files', file);
    });

    const response = await submitSupplementaryMaterial(formData);

    if (response.success) {
      ElMessage.success('材料提交成功');
      showSubmitMaterialModal.value = false;
      fetchMaterialRequests(); // 重新获取列表
    } else {
      ElMessage.error(response.message || '提交材料失败');
    }
  } catch (error) {
    console.error('提交材料失败:', error);
    ElMessage.error('提交材料失败');
  } finally {
    materialSubmitting.value = false;
  }
};

const editProfile = () => {
  // 打开编辑个人资料弹窗
  // 初始化表单数据
  if (userInfo.value) {
    profileForm.value.username = userInfo.value.username || '';
    profileForm.value.email = userInfo.value.email || '';
    profileForm.value.password = ''; // 密码字段始终为空，不从服务器获取
    profileForm.value.location = userInfo.value.location || '';
    profileForm.value.bio = userInfo.value.bio || '';
  }
  showEditProfileModal.value = true;
};

// 触发文件选择器
const triggerAvatarUpload = () => {
  avatarInput.value.click();
};

// 处理头像选择变化
const handleAvatarChange = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  avatarFile.value = file;
  avatarPreview.value = URL.createObjectURL(file);
};

// 上传头像文件
const uploadAvatarFile = async () => {
  if (!avatarFile.value) return;

  try {
    const formData = new FormData();
    formData.append('file', avatarFile.value);

    const response = await updateAvatar(formData);
    if (response.code !== 200) {
      console.error('头像上传失败:', response.message);
      alert('头像上传失败: ' + response.message);
    }
  } catch (error) {
    console.error('上传头像出错:', error);
    alert('上传头像失败，请稍后重试');
  }
};

// 更新个人资料
const updateProfile = async () => {
  try {
    // 创建请求对象，只包含非空值
    const updateData = {};
    if (profileForm.value.username) updateData.username = profileForm.value.username;
    if (profileForm.value.email) updateData.email = profileForm.value.email;
    if (profileForm.value.password) updateData.password = profileForm.value.password;
    if (profileForm.value.location) updateData.location = profileForm.value.location;
    if (profileForm.value.bio) updateData.bio = profileForm.value.bio;

    const response = await updateUserInfo(updateData);

    if (response.code === 200) {
      // 如果有新头像，上传头像
      if (avatarFile.value) {
        await uploadAvatarFile();
      }

      // 更新用户信息
      await userStore.fetchUserInfo();
      alert('个人资料更新成功');
      // 关闭弹窗
      showEditProfileModal.value = false;
      // 清空头像预览和文件
      avatarPreview.value = '';
      avatarFile.value = null;
    } else {
      alert('更新失败: ' + response.message);
    }
  } catch (error) {
    console.error('更新个人资料出错:', error);
    alert('更新失败，请稍后重试');
  }
};

// 收藏列表数据
const favorites = ref([]);
const favoritesLoading = ref(false);

// 关注列表数据
const followings = ref([]);
const followingsLoading = ref(false);
const followingsPage = ref(1);
const followingsPageSize = ref(10);
const followingsTotalPages = ref(1);

// 地址列表数据
const addresses = ref([]);
const addressesLoading = ref(false);
const addressesPage = ref(1);
const addressesPageSize = ref(3);
const addressesTotalPages = ref(1);

// 地址表单
const showAddressForm = ref(false);
const isEditingAddress = ref(false);
const addressForm = ref({
  addressId: null,
  recipientName: '',
  phoneNumber: '',
  province: '',
  city: '',
  district: '',
  streetAddress: '',
  isDefault: false
});

// 地址表单错误
const addressFormErrors = ref({
  recipientName: '',
  phoneNumber: '',
  province: '',
  city: '',
  streetAddress: ''
});

// 获取用户收藏列表
const fetchUserFavorites = async () => {
  if (activeSection.value !== 'my-favorites') return;

  favoritesLoading.value = true;
  try {
    const response = await getUserFavorites();
    if (response && response.code === 200 && response.data) {
      favorites.value = response.data.map(item => ({
        id: item.id,
        productId: item.productId,
        productTitle: item.productTitle,
        productPrice: item.productPrice,
        productImage: item.productImage || 'https://via.placeholder.com/300x300',
        favoriteDate: item.favoriteDate
      }));
    } else {
      favorites.value = [];
    }
  } catch (error) {
    console.error('获取收藏列表失败:', error);
    favorites.value = [];
  } finally {
    favoritesLoading.value = false;
  }
};

// 取消收藏
const removeFavoriteItem = async (productId) => {
  try {
    const response = await removeFavorite(productId);
    if (response && response.code === 200) {
      // 从列表中移除该商品
      favorites.value = favorites.value.filter(item => item.productId !== productId);
    } else {
      alert('取消收藏失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('取消收藏失败:', error);
    alert('取消收藏失败，请稍后重试');
  }
};

// 跳转到商品详情页
const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`);
};

// 我的发布列表数据
const myProducts = ref([]);
const productsLoading = ref(false);

// 编辑商品相关
const showEditProductModal = ref(false);
const editProductForm = ref({
  id: null,
  title: '',
  price: 0,
  description: '',
  status: 'available',
  image: '' // 添加图片字段
});

// 商品图片上传相关
const productImageInput = ref(null);
const productImageFile = ref(null);
const productImagePreview = ref('');

// 获取用户发布的商品列表
const fetchUserProducts = async () => {
  if (activeSection.value !== 'my-products') return;

  productsLoading.value = true;
  try {
    // 调用API获取当前用户发布的商品
    const response = await getUserProducts();
    if (response && response.code === 200 && response.data) {
      // 转换返回的商品数据
      if (response.data.records && response.data.records.length > 0) {
        myProducts.value = response.data.records.map(item => ({
          id: item.id,
          title: item.title,
          price: item.price,
          description: item.description,
          image: item.imageUrls && item.imageUrls.length > 0 ? item.imageUrls[0] : 'https://via.placeholder.com/300x300',
          status: item.status,
          postedDate: item.postedDate
        }));
      } else {
        myProducts.value = [];
      }
    } else {
      myProducts.value = [];
    }
  } catch (error) {
    console.error('获取我的发布列表失败:', error);
    myProducts.value = [];
  } finally {
    productsLoading.value = false;
  }
};

// 打开编辑商品弹窗
const openEditModal = (product) => {
  // 检查商品是否可以编辑
  if (!canEditProduct(product.status)) {
    alert('该商品当前状态下不可编辑');
    return;
  }

  editProductForm.value = {
    id: product.id,
    title: product.title,
    price: product.price,
    description: product.description || '',
    status: product.status,
    image: product.image || (product.imageUrls && product.imageUrls.length > 0 ? product.imageUrls[0] : '')
  };
  // 重置图片预览
  productImagePreview.value = '';
  productImageFile.value = null;
  showEditProductModal.value = true;
};

// 触发商品图片上传
const triggerProductImageUpload = () => {
  productImageInput.value.click();
};

// 处理商品图片选择变化
const handleProductImageChange = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  productImageFile.value = file;
  productImagePreview.value = URL.createObjectURL(file);
};

// 更新商品信息
const updateProductInfo = async () => {
  try {
    const productId = editProductForm.value.id;
    const productData = {
      title: editProductForm.value.title,
      price: editProductForm.value.price,
      description: editProductForm.value.description
    };

    // 如果状态发生变化，单独调用更新状态API
    const product = myProducts.value.find(p => p.id === productId);
    if (product && product.status !== editProductForm.value.status) {
      await updateProductStatus(productId, editProductForm.value.status);
    }

    // 更新商品基本信息
    const response = await updateProduct(productId, productData);

    if (response && response.code === 200) {
      // 如果有新图片，上传图片
      if (productImageFile.value) {
        const formData = new FormData();
        formData.append('image', productImageFile.value);

        const imageResponse = await updateProductImage(productId, formData);
        if (imageResponse && imageResponse.code === 200) {
          // 更新本地列表中的商品图片
          const index = myProducts.value.findIndex(p => p.id === productId);
          if (index !== -1) {
            myProducts.value[index].image = imageResponse.data;
          }
        } else {
          alert('更新商品图片失败: ' + (imageResponse?.message || '未知错误'));
        }
      }

      // 更新本地列表中的商品数据
      const index = myProducts.value.findIndex(p => p.id === productId);
      if (index !== -1) {
        myProducts.value[index] = {
          ...myProducts.value[index],
          title: editProductForm.value.title,
          price: editProductForm.value.price,
          description: editProductForm.value.description,
          status: editProductForm.value.status
        };
      }

      alert('商品更新成功');
      showEditProductModal.value = false;
      // 重置图片预览和文件
      productImagePreview.value = '';
      productImageFile.value = null;
    } else {
      alert('更新失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('更新商品失败:', error);
    alert('更新失败，请稍后重试');
  }
};

// 确认删除商品
const confirmDeleteProduct = (productId) => {
  if (confirm('确定要删除这个商品吗？此操作不可恢复。')) {
    removeProduct(productId);
  }
};

// 删除商品
const removeProduct = async (productId) => {
  try {
    const response = await deleteProduct(productId);
    if (response && response.code === 200) {
      // 从列表中移除该商品
      myProducts.value = myProducts.value.filter(item => item.id !== productId);
    } else {
      alert('删除商品失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('删除商品失败:', error);
    alert('删除商品失败，请稍后重试');
  }
};

// 获取用户关注列表
const fetchUserFollowings = async () => {
  if (activeSection.value !== 'my-followings') return;

  followingsLoading.value = true;
  try {
    const response = await getUserFollowings(followingsPage.value, followingsPageSize.value);
    console.log('关注列表响应:', response);

    if (response && response.code === 200 && response.data) {
      // 处理分页数据结构
      const followingsData = response.data.records || [];
      console.log('关注列表数据:', followingsData);

      followings.value = followingsData.map(item => ({
        followId: item.followId,
        followingId: item.followingId,
        followingUsername: item.followingUsername,
        followingAvatarUrl: item.followingAvatarUrl || defaultAvatar.value,
        followedAt: item.followedAt
      }));

      // 更新分页信息
      followingsTotalPages.value = response.data.totalPages || 1;

      console.log('处理后的关注列表:', followings.value);
    } else {
      followings.value = [];
      console.log('没有获取到关注列表数据');
    }
  } catch (error) {
    console.error('获取关注列表失败:', error);
    followings.value = [];
  } finally {
    followingsLoading.value = false;
  }
};

// 切换关注列表页码
const changeFollowingsPage = (page) => {
  followingsPage.value = page;
  fetchUserFollowings();
};

// 取消关注
const removeFollowing = async (userId) => {
  try {
    // 添加确认提示
    if (!confirm('确定要取消关注该用户吗？')) {
      return;
    }

    console.log('取消关注用户ID:', userId);
    const response = await unfollowUser(userId);
    console.log('取消关注响应:', response);

    if (response && response.code === 200) {
      // 从列表中移除该关注
      followings.value = followings.value.filter(item => item.followingId !== userId);
      alert('取消关注成功');
    } else {
      alert('取消关注失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('取消关注失败:', error);
    alert('取消关注失败，请稍后重试');
  }
};

// 跳转到商家主页
const goToSellerProfile = (sellerId) => {
  router.push(`/seller/${sellerId}`);
};

// 获取用户地址列表
const fetchUserAddresses = async () => {
  if (activeSection.value !== 'my-addresses') return;

  addressesLoading.value = true;
  try {
    const response = await getUserAddresses();
    if (response && response.code === 200 && response.data) {
      // 将地址数据映射到本地格式
      addresses.value = response.data;

      // 计算总页数
      addressesTotalPages.value = Math.ceil(response.data.length / addressesPageSize.value);
      if (addressesPage.value > addressesTotalPages.value && addressesTotalPages.value > 0) {
        addressesPage.value = addressesTotalPages.value;
      }
    } else {
      addresses.value = [];
      addressesTotalPages.value = 1;
    }
  } catch (error) {
    console.error('获取地址列表失败:', error);
    addresses.value = [];
    addressesTotalPages.value = 1;
  } finally {
    addressesLoading.value = false;
  }
};

// 切换地址列表页码
const changeAddressesPage = (page) => {
  addressesPage.value = page;
};

// 获取当前页的地址
const currentPageAddresses = computed(() => {
  const start = (addressesPage.value - 1) * addressesPageSize.value;
  const end = start + addressesPageSize.value;
  return addresses.value.slice(start, end);
});

// 打开新增地址表单
const openAddAddressForm = () => {
  // 重置表单
  addressForm.value = {
    addressId: null,
    recipientName: '',
    phoneNumber: '',
    province: '',
    city: '',
    district: '',
    streetAddress: '',
    isDefault: false
  };
  clearAddressFormErrors();
  isEditingAddress.value = false;
  showAddressForm.value = true;
};

// 打开编辑地址表单
const openEditAddressForm = (address) => {
  addressForm.value = {
    addressId: address.addressId,
    recipientName: address.recipientName,
    phoneNumber: address.phoneNumber,
    province: address.province,
    city: address.city,
    district: address.district || '',
    streetAddress: address.streetAddress,
    isDefault: address.isDefault
  };
  clearAddressFormErrors();
  isEditingAddress.value = true;
  showAddressForm.value = true;
};

// 清除地址表单错误
const clearAddressFormErrors = () => {
  Object.keys(addressFormErrors.value).forEach(key => {
    addressFormErrors.value[key] = '';
  });
};

// 验证地址表单
const validateAddressForm = () => {
  clearAddressFormErrors();
  let isValid = true;

  if (!addressForm.value.recipientName) {
    addressFormErrors.value.recipientName = '请输入收货人姓名';
    isValid = false;
  }

  if (!addressForm.value.phoneNumber) {
    addressFormErrors.value.phoneNumber = '请输入手机号码';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(addressForm.value.phoneNumber)) {
    addressFormErrors.value.phoneNumber = '请输入正确的手机号码';
    isValid = false;
  }

  if (!addressForm.value.province) {
    addressFormErrors.value.province = '请输入省份';
    isValid = false;
  }

  if (!addressForm.value.city) {
    addressFormErrors.value.city = '请输入城市';
    isValid = false;
  }

  if (!addressForm.value.streetAddress) {
    addressFormErrors.value.streetAddress = '请输入详细地址';
    isValid = false;
  }

  return isValid;
};

// 提交地址表单
const submitAddressForm = async () => {
  // 验证表单
  if (!validateAddressForm()) {
    return;
  }

  try {
    let response;

    if (isEditingAddress.value) {
      // 更新地址
      response = await updateAddress(addressForm.value.addressId, addressForm.value);
    } else {
      // 新增地址
      response = await addAddress(addressForm.value);
    }

    if (response && response.code === 200) {
      alert(isEditingAddress.value ? '地址更新成功' : '地址添加成功');
      showAddressForm.value = false;
      // 重新加载地址列表
      await fetchUserAddresses();
    } else {
      alert((isEditingAddress.value ? '更新' : '添加') + '地址失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error((isEditingAddress.value ? '更新' : '添加') + '地址失败:', error);
    alert((isEditingAddress.value ? '更新' : '添加') + '地址失败，请稍后重试');
  }
};

// 设置默认地址
const setAddressAsDefault = async (addressId) => {
  try {
    const response = await setDefaultAddress(addressId);
    if (response && response.code === 200) {
      alert('设置默认地址成功');
      // 重新加载地址列表
      await fetchUserAddresses();
    } else {
      alert('设置默认地址失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('设置默认地址失败:', error);
    alert('设置默认地址失败，请稍后重试');
  }
};

// 删除地址
const removeAddress = async (addressId) => {
  if (!confirm('确定要删除这个地址吗？')) {
    return;
  }

  try {
    const response = await deleteAddress(addressId);
    if (response && response.code === 200) {
      alert('地址删除成功');
      // 重新加载地址列表
      await fetchUserAddresses();
    } else {
      alert('删除地址失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('删除地址失败:', error);
    alert('删除地址失败，请稍后重试');
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'available': '在售中',
    'sold': '已售出',
    'pending_review': '审核中',
    'manual_review': '人工审核中',
    'second_review': '二度复审中',
    'material_requested': '等待补充材料',
    'off_shelf_by_seller': '商家已下架',
    'off_shelf_by_admin': '管理员已下架',
    'deleted': '已删除'
  };
  return statusMap[status.toLowerCase()] || status;
};

// 判断商品是否可以编辑
const canEditProduct = (status) => {
  // 以下状态的商品不可编辑：
  // - 审核相关状态：pending_review, manual_review, second_review, material_requested
  // - 管理员操作状态：off_shelf_by_admin, deleted
  const nonEditableStatuses = [
    'pending_review',
    'manual_review',
    'second_review',
    'material_requested',
    'off_shelf_by_admin',
    'deleted'
  ];
  return !nonEditableStatuses.includes(status.toLowerCase());
};

// 我的订单
const activeOrderTab = ref('buyer'); // 'buyer' or 'seller'
const buyerOrders = ref([]);
const sellerOrders = ref([]);
const buyerOrdersLoading = ref(false);
const sellerOrdersLoading = ref(false);
const buyerStatusFilter = ref('');
const sellerStatusFilter = ref('');
const buyerPage = ref(1);
const buyerTotalPages = ref(1);
const sellerPage = ref(1);
const sellerTotalPages = ref(1);
const ordersPageSize = 4;
const defaultProductImage = 'https://via.placeholder.com/80x80';

const orderStatusMap = {
  'pending_payment': '待支付',
  'paid': '已支付',
  'shipped': '已发货',
  'verifying': '官方验货中',
  'delivered': '已送达',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
};

const getOrderStatusText = (status) => {
  return orderStatusMap[status] || status;
};

const fetchBuyerOrders = async () => {
  if (activeSection.value !== 'my-orders') return;
  buyerOrdersLoading.value = true;
  try {
    const params = {
      page: buyerPage.value,
      pageSize: ordersPageSize,
      status: buyerStatusFilter.value || null,
    };
    const response = await getBuyerOrders(params);
    if (response.code === 200 && response.data) {
      buyerOrders.value = response.data.records;
      buyerTotalPages.value = response.data.pages;
    } else {
      buyerOrders.value = [];
      buyerTotalPages.value = 1;
    }
  } catch (error) {
    console.error('获取买家订单失败:', error);
    alert('获取买家订单失败，请检查网络或联系管理员。');
    buyerOrders.value = [];
    buyerTotalPages.value = 1;
  } finally {
    buyerOrdersLoading.value = false;
  }
};

const fetchSellerOrders = async () => {
  if (activeSection.value !== 'my-orders') return;
  sellerOrdersLoading.value = true;
  try {
    const params = {
      page: sellerPage.value,
      pageSize: ordersPageSize,
      status: sellerStatusFilter.value || null,
    };
    const response = await getSellerOrders(params);
    if (response.code === 200 && response.data) {
      sellerOrders.value = response.data.records;
      sellerTotalPages.value = response.data.pages;
    } else {
      sellerOrders.value = [];
      sellerTotalPages.value = 1;
    }
  } catch (error) {
    console.error('获取卖家订单失败:', error);
    alert('获取卖家订单失败，请检查网络或联系管理员。');
    sellerOrders.value = [];
    sellerTotalPages.value = 1;
  } finally {
    sellerOrdersLoading.value = false;
  }
};

/**
 * 处理支付逻辑
 * @param {string} payData - 支付数据，可能是HTML表单或URL
 */
const processPayment = async (payData) => {
  try {
    await ElMessageBox.confirm(
      '是否立即前往支付？',
      '支付确认',
      {
        confirmButtonText: '立即支付',
        cancelButtonText: '稍后支付',
        type: 'success',
        center: true,
      }
    );

    // 用户选择立即支付
    if (payData.startsWith('http')) {
      // 如果是URL，直接跳转
      window.location.href = payData;
    } else {
      // 如果是HTML表单，创建一个隐藏的div来渲染它
      const div = document.createElement('div');
      div.innerHTML = payData;
      document.body.appendChild(div);

      const form = div.getElementsByTagName('form')[0];
      if (form) {
        form.submit();
      } else {
        ElMessage.error('支付表单格式错误，请联系客服');
        console.error('支付表单格式错误:', payData);
      }
    }
  } catch {
    // 用户选择 "稍后支付" 或关闭了对话框
    ElMessage.info('您可以稍后在"我的订单"中完成支付。');
  }
};

/**
 * 处理订单余额支付逻辑
 * @param {Object} paymentInfo - 支付信息对象
 */
const processOrderPayment = async (paymentInfo) => {
  try {
    const { orderId, orderAmount, userBalance, balanceEnough } = paymentInfo;

    // 构建支付确认消息
    let confirmMessage = `订单金额：¥${orderAmount}\n您的余额：¥${userBalance}\n`;

    if (balanceEnough) {
      confirmMessage += '\n余额充足，是否立即支付？';
    } else {
      confirmMessage += '\n余额不足，请先充值或选择稍后支付。';
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '支付确认',
      {
        confirmButtonText: balanceEnough ? '立即支付' : '去充值',
        cancelButtonText: '稍后支付',
        type: 'info',
        center: true,
      }
    );

    // 用户选择立即支付或去充值
    if (balanceEnough) {
      // 余额充足，直接支付
      await performOrderBalancePayment(orderId);
    } else {
      // 余额不足，跳转到个人页面充值
      ElMessage.info('请先充值后再支付订单');
      // 刷新当前页面以显示充值区域
      window.location.reload();
    }
  } catch {
    // 用户选择 "稍后支付" 或关闭了对话框
    ElMessage.info('您可以稍后再支付此订单。');
  }
};

/**
 * 执行订单余额支付
 * @param {Number} orderId - 订单ID
 */
const performOrderBalancePayment = async (orderId) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在支付...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const response = await payWithBalance(orderId);

    loading.close();

    if (response && response.success) {
      ElMessage.success('支付成功！');
      // 刷新订单列表
      await fetchBuyerOrders();
    } else {
      ElMessage.error(response?.message || '支付失败');
    }
  } catch (error) {
    loading.close();
    console.error('余额支付失败:', error);
    ElMessage.error('支付失败，请稍后重试');
  }
};

/**
 * 处理订单支付
 * @param {number} orderId - 订单ID
 */
const handlePayOrder = async (orderId) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在获取支付信息...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const payInfoResponse = await getPaymentInfo(orderId);
    loading.close();

    if (payInfoResponse && payInfoResponse.success) {
      await processOrderPayment(payInfoResponse.data);
    } else {
      ElMessage.error(payInfoResponse?.message || '获取支付信息失败');
    }
  } catch (error) {
    loading.close();
    console.error('获取支付信息失败:', error);
    ElMessage.error('获取支付信息失败，请稍后重试');
  }
};

const handleCancelOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？',
      '取消订单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      }
    );

    const response = await cancelOrder(orderId);
    if (response.code === 200) {
      ElMessage({
        type: 'success',
        message: '订单已取消',
        center: true,
      });
      const orderIndex = buyerOrders.value.findIndex(o => o.orderId === orderId);
      if (orderIndex !== -1) {
        buyerOrders.value[orderIndex].status = 'cancelled';
      }
    } else {
      ElMessage({
        type: 'error',
        message: `操作失败: ${response.message}`,
        center: true,
      });
    }
  } catch (error) {
    if (error !== 'cancel') {
    console.error('取消订单失败:', error);
      ElMessage({
        type: 'error',
        message: '操作失败，请稍后重试',
        center: true,
      });
    }
  }
};

const handleShipOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm(
      '确定要发货吗？',
      '发货确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
        center: true,
      }
    );

    const response = await shipOrder(orderId);
    if (response.code === 200) {
      ElMessage({
        type: 'success',
        message: '发货成功',
        center: true,
      });

      const orderIndex = sellerOrders.value.findIndex(o => o.orderId === orderId);
      if (orderIndex !== -1) {
        sellerOrders.value[orderIndex].status = 'shipped';

        setTimeout(() => {
          if (sellerOrders.value[orderIndex] && sellerOrders.value[orderIndex].status === 'shipped') {
            sellerOrders.value[orderIndex].status = 'delivered';
          }
        }, 10000);
      }
    } else {
      ElMessage({
        type: 'error',
        message: `发货失败: ${response.message}`,
        center: true,
      });
    }
  } catch (error) {
    if (error !== 'cancel') {
    console.error('发货失败:', error);
      ElMessage({
        type: 'error',
        message: '操作失败，请稍后重试',
        center: true,
      });
    }
  }
};

const handleCompleteOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm(
      '请确认您已收到商品。确认后订单将完成。',
      '确认收货',
      {
        confirmButtonText: '确认收货',
        cancelButtonText: '取消',
        type: 'success',
        center: true,
      }
    );

    const response = await completeOrder(orderId);
    if (response.code === 200) {
      ElMessage({
        type: 'success',
        message: '订单已完成！',
        center: true,
      });

      // 更新订单状态
      const orderIndex = buyerOrders.value.findIndex(o => o.orderId === orderId);
      if (orderIndex !== -1) {
        buyerOrders.value[orderIndex].status = 'completed';
      }

      // 检查是否需要显示评价弹窗
      if (response.data && response.data.showReviewDialog) {
        reviewOrderInfo.value = {
          orderId: response.data.orderId,
          sellerId: response.data.sellerId,
          sellerName: response.data.sellerName,
          productId: response.data.productId,
          productTitle: response.data.productTitle,
          productImage: response.data.productImage,
          totalAmount: response.data.totalAmount
        };
        showReviewDialog.value = true;
      }
    } else {
      ElMessage({
        type: 'error',
        message: `确认收货失败: ${response.message}`,
        center: true,
      });
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error);
      ElMessage({
        type: 'error',
        message: '操作失败，请稍后重试',
        center: true,
      });
    }
  }
};

// 评价成功处理
const handleReviewSuccess = (reviewData) => {
  ElMessage({
    type: 'success',
    message: '评价提交成功，感谢您的反馈！',
    center: true,
  });
  // 可以在这里刷新订单列表或做其他处理
};

// 跳过评价处理
const handleReviewSkip = () => {
  ElMessage({
    type: 'info',
    message: '您可以稍后在订单详情中进行评价',
    center: true,
  });
};

const changeBuyerPage = (page) => {
  if (page > 0 && page <= buyerTotalPages.value) {
    buyerPage.value = page;
  }
};

const changeSellerPage = (page) => {
  if (page > 0 && page <= sellerTotalPages.value) {
    sellerPage.value = page;
  }
};

watch(activeOrderTab, (newTab) => {
  if (newTab === 'buyer') {
    buyerPage.value = 1;
    fetchBuyerOrders();
  } else if (newTab === 'seller') {
    sellerPage.value = 1;
    fetchSellerOrders();
  }
});

watch(buyerStatusFilter, () => {
  buyerPage.value = 1;
  fetchBuyerOrders();
});

watch(buyerPage, fetchBuyerOrders);

watch(sellerStatusFilter, () => {
  sellerPage.value = 1;
  fetchSellerOrders();
});

watch(sellerPage, fetchSellerOrders);

// 监听activeSection变化，当切换到不同页时加载数据
watch(activeSection, (newValue) => {
  if (newValue === 'my-products') {
    fetchUserProducts();
  } else if (newValue === 'my-favorites') {
    fetchUserFavorites();
  } else if (newValue === 'my-followings') {
    fetchUserFollowings();
  } else if (newValue === 'my-addresses') {
    fetchUserAddresses();
  } else if (newValue === 'my-orders') {
    if (activeOrderTab.value === 'buyer') {
      fetchBuyerOrders();
    } else {
      fetchSellerOrders();
    }
  } else if (newValue === 'supplementary-materials') {
    fetchMaterialRequests();
  } else if (newValue === 'my-refunds') {
    fetchMyRefunds();
  } else if (newValue === 'seller-refunds') {
    fetchSellerRefunds();
  } else if (newValue === 'forced-refunds') {
    fetchForcedRefunds();
  } else if (newValue === 'my-verifications') {
    fetchMyVerifications();
  } else if (newValue === 'pending-verifications') {
    fetchPendingVerifications();
  }
});

// 绑定手机号相关方法
const canSendCode = computed(() => {
  return countdown.value === 0 &&
         bindPhoneForm.value.phone &&
         /^1[3-9]\d{9}$/.test(bindPhoneForm.value.phone);
});

const sendCodeText = computed(() => {
  if (sendCodeLoading.value) return '发送中...';
  if (countdown.value > 0) return `${countdown.value}s后重发`;
  return '获取验证码';
});

// 格式化手机号显示
const formatPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
};

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return;

  sendCodeLoading.value = true;
  try {
    const response = await sendSmsCode(bindPhoneForm.value.phone, 'bind');
    if (response.success) {
      ElMessage.success('验证码发送成功');
      bindPhoneForm.value.codeSent = true;
      startCountdown();
    } else {
      ElMessage.error(response.message || '验证码发送失败');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('验证码发送失败，请稍后重试');
  } finally {
    sendCodeLoading.value = false;
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }
  }, 1000);
};

// 提交绑定手机号
const submitBindPhone = async () => {
  if (!bindPhoneForm.value.phone || !bindPhoneForm.value.code) {
    ElMessage.error('请填写完整信息');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(bindPhoneForm.value.phone)) {
    ElMessage.error('手机号格式不正确');
    return;
  }

  if (!/^\d{6}$/.test(bindPhoneForm.value.code)) {
    ElMessage.error('验证码格式不正确');
    return;
  }

  bindPhoneLoading.value = true;
  try {
    const response = await bindPhone({
      phone: bindPhoneForm.value.phone,
      code: bindPhoneForm.value.code
    });

    if (response.success) {
      ElMessage.success('手机号绑定成功');
      // 刷新用户信息
      await userStore.fetchUserInfo();
      closeBindPhoneModal();
    } else {
      ElMessage.error(response.message || '绑定失败');
    }
  } catch (error) {
    console.error('绑定手机号失败:', error);
    ElMessage.error('绑定失败，请稍后重试');
  } finally {
    bindPhoneLoading.value = false;
  }
};

// 关闭绑定手机号弹窗
const closeBindPhoneModal = () => {
  showBindPhoneModal.value = false;
  // 重置表单
  bindPhoneForm.value = {
    phone: '',
    code: '',
    codeSent: false
  };
  // 清除倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  countdown.value = 0;
  sendCodeLoading.value = false;
  bindPhoneLoading.value = false;
};

// 格式化余额显示
const formatBalance = (balance) => {
  if (balance === null || balance === undefined) return '0.00';
  return Number(balance).toFixed(2);
};

// 选择快捷金额
const selectQuickAmount = (amount) => {
  rechargeForm.value.amount = amount;
};

// 提交充值
const submitRecharge = async () => {
  if (!rechargeForm.value.amount || rechargeForm.value.amount <= 0) {
    ElMessage.error('请输入有效的充值金额');
    return;
  }

  rechargeLoading.value = true;

  try {
    // 调用充值API
    const response = await createRecharge({
      amount: parseFloat(rechargeForm.value.amount),
      paymentMethod: rechargeForm.value.paymentMethod
    });

    if (response.success) {
      // 关闭充值弹窗
      showRechargeModal.value = false;

      // 创建一个新窗口显示支付页面
      const paymentWindow = window.open('', '_blank', 'width=800,height=600');
      paymentWindow.document.write(response.data.paymentForm);
      paymentWindow.document.close();

      ElMessage.success('充值订单创建成功，请在新窗口中完成支付');

      // 重置表单
      rechargeForm.value.amount = '';
    } else {
      ElMessage.error(response.message || '充值失败');
    }
  } catch (error) {
    console.error('充值失败:', error);
    ElMessage.error('充值失败，请稍后重试');
  } finally {
    rechargeLoading.value = false;
  }
};

// 退款相关数据
const myRefunds = ref([]);
const sellerRefunds = ref([]);
const forcedRefunds = ref([]);
const refundsLoading = ref(false);
const forcedRefundsLoading = ref(false);
const showRefundModal = ref(false);
const showRejectModal = ref(false);
const selectedRefund = ref(null);
const selectedOrder = ref(null);
const refundForm = ref({
  refundType: 'refund_only',
  refundAmount: 0,
  refundReason: ''
});
const rejectReason = ref('');

// 验货相关数据
const myVerifications = ref([]);
const pendingVerifications = ref([]);
const verificationsLoading = ref(false);
const showVerificationResultModal = ref(false);
const selectedVerification = ref(null);
const verificationResultForm = ref({
  passed: true,
  result: '',
  imageUrls: []
});

// 退款相关方法
const fetchMyRefunds = async () => {
  refundsLoading.value = true;
  try {
    const response = await getRefundList('buyer');
    console.log('买家退款数据响应:', response);

    // 确保数据是数组，并过滤掉null/undefined元素
    const refundData = response.data || [];
    myRefunds.value = Array.isArray(refundData)
      ? refundData.filter(refund => refund && refund.refundId)
      : [];

    console.log('处理后的买家退款数据:', myRefunds.value);
  } catch (error) {
    console.error('获取退款申请失败:', error);
    ElMessage.error('获取退款申请失败');
    myRefunds.value = []; // 确保出错时数组为空
  } finally {
    refundsLoading.value = false;
  }
};

const fetchSellerRefunds = async () => {
  refundsLoading.value = true;
  try {
    const response = await getRefundList('seller');
    console.log('卖家退款数据响应:', response);

    // 确保数据是数组，并过滤掉null/undefined元素
    const refundData = response.data || [];
    sellerRefunds.value = Array.isArray(refundData)
      ? refundData.filter(refund => refund && refund.refundId)
      : [];

    console.log('处理后的卖家退款数据:', sellerRefunds.value);
  } catch (error) {
    console.error('获取待处理退款失败:', error);
    ElMessage.error('获取待处理退款失败');
    sellerRefunds.value = []; // 确保出错时数组为空
  } finally {
    refundsLoading.value = false;
  }
};

const fetchForcedRefunds = async () => {
  forcedRefundsLoading.value = true;
  try {
    const response = await getForcedRefundTasks();
    console.log('强制退款任务数据响应:', response);

    // 确保数据是数组，并过滤掉null/undefined元素
    const refundData = response.data || [];
    forcedRefunds.value = Array.isArray(refundData)
      ? refundData.filter(refund => refund && refund.refundId)
      : [];

    console.log('处理后的强制退款任务数据:', forcedRefunds.value);
  } catch (error) {
    console.error('获取强制退款任务失败:', error);
    ElMessage.error('获取强制退款任务失败');
    forcedRefunds.value = []; // 确保出错时数组为空
  } finally {
    forcedRefundsLoading.value = false;
  }
};

// 验货相关方法
const fetchMyVerifications = async () => {
  verificationsLoading.value = true;
  try {
    // 这里需要根据用户订单获取验货记录
    myVerifications.value = [];
  } catch (error) {
    console.error('获取验货记录失败:', error);
    ElMessage.error('获取验货记录失败');
  } finally {
    verificationsLoading.value = false;
  }
};

const fetchPendingVerifications = async () => {
  verificationsLoading.value = true;
  try {
    const response = await getAllPendingVerifications();
    pendingVerifications.value = response.data || [];
  } catch (error) {
    console.error('获取待验货列表失败:', error);
    ElMessage.error('获取待验货列表失败');
  } finally {
    verificationsLoading.value = false;
  }
};

// 退款操作方法
const canRefund = (status) => {
  return ['paid', 'shipped', 'delivered'].includes(status);
};

const showRefundDialog = (order) => {
  selectedOrder.value = order;
  refundForm.value = {
    refundType: 'refund_only',
    refundAmount: order.totalAmount,
    refundReason: ''
  };
  showRefundModal.value = true;
};

const submitRefund = async () => {
  try {
    await createRefund({
      orderId: selectedOrder.value.orderId,
      refundAmount: refundForm.value.refundAmount,
      refundReason: refundForm.value.refundReason,
      refundType: refundForm.value.refundType
    });
    ElMessage.success('退款申请提交成功');
    showRefundModal.value = false;
    fetchBuyerOrders();
  } catch (error) {
    console.error('提交退款申请失败:', error);
    ElMessage.error('提交退款申请失败');
  }
};

const handleRefund = async (refund, approved) => {
  try {
    await handleSellerRefund({
      refundId: refund.refundId,
      approved,
      response: approved ? '同意退款' : rejectReason.value
    });
    ElMessage.success(approved ? '已同意退款' : '已拒绝退款');
    fetchSellerRefunds();
  } catch (error) {
    console.error('处理退款失败:', error);
    ElMessage.error('处理退款失败');
  }
};

const showRejectDialog = (refund) => {
  selectedRefund.value = refund;
  rejectReason.value = '';
  showRejectModal.value = true;
};

const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }

  try {
    await handleSellerRefund({
      refundId: selectedRefund.value.refundId,
      approved: false,
      response: rejectReason.value
    });
    ElMessage.success('已拒绝退款');
    showRejectModal.value = false;
    fetchSellerRefunds();
  } catch (error) {
    console.error('拒绝退款失败:', error);
    ElMessage.error('拒绝退款失败');
  }
};

// 申请管理员介入相关数据
const showInterventionModal = ref(false);
const selectedRefundForIntervention = ref(null);
const interventionForm = ref({
  evidence: '',
  files: []
});

const requestAdminIntervention = (refund) => {
  selectedRefundForIntervention.value = refund;
  interventionForm.value = {
    evidence: '',
    files: []
  };
  showInterventionModal.value = true;
};

// 处理文件选择（累积模式）
const handleFileChange = (event) => {
  const newFiles = Array.from(event.target.files);

  // 检查文件数量限制
  const currentCount = interventionForm.value.files.length;
  const maxFiles = 10;

  if (currentCount + newFiles.length > maxFiles) {
    ElMessage.warning(`最多只能上传${maxFiles}个文件，当前已有${currentCount}个文件`);
    return;
  }

  // 检查文件大小和类型
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp',
                       'application/pdf', 'application/msword',
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

  const validFiles = [];
  for (const file of newFiles) {
    // 检查文件大小
    if (file.size > maxSize) {
      ElMessage.warning(`文件"${file.name}"超过10MB限制，已跳过`);
      continue;
    }

    // 检查文件类型
    if (!allowedTypes.includes(file.type)) {
      ElMessage.warning(`文件"${file.name}"类型不支持，已跳过`);
      continue;
    }

    // 检查是否重复
    const isDuplicate = interventionForm.value.files.some(existingFile =>
      existingFile.name === file.name && existingFile.size === file.size
    );

    if (isDuplicate) {
      ElMessage.warning(`文件"${file.name}"已存在，已跳过`);
      continue;
    }

    validFiles.push(file);
  }

  // 累加有效文件
  interventionForm.value.files.push(...validFiles);

  // 清空input，允许重复选择相同文件
  event.target.value = '';

  if (validFiles.length > 0) {
    ElMessage.success(`成功添加${validFiles.length}个文件`);
  }
};

// 移除退款申请文件
const removeInterventionFile = (index) => {
  const fileName = interventionForm.value.files[index].name;
  interventionForm.value.files.splice(index, 1);
  ElMessage.success(`已移除文件"${fileName}"`);
};

// 清空所有文件
const clearAllFiles = () => {
  ElMessageBox.confirm('确定要清空所有已选择的文件吗？', '确认清空', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    interventionForm.value.files = [];
    ElMessage.success('已清空所有文件');
  }).catch(() => {
    // 用户取消
  });
};

// 提交申请管理员介入
const submitInterventionRequest = async () => {
  try {
    const formData = new FormData();
    formData.append('refundId', selectedRefundForIntervention.value.refundId);

    if (interventionForm.value.evidence) {
      formData.append('evidence', interventionForm.value.evidence);
    }

    // 添加文件
    interventionForm.value.files.forEach((file, index) => {
      formData.append('files', file);
    });

    await requestAdminInterventionWithFiles(formData);
    ElMessage.success('已申请管理员介入');
    showInterventionModal.value = false;
    fetchMyRefunds();
  } catch (error) {
    console.error('申请管理员介入失败:', error);
    ElMessage.error('申请管理员介入失败');
  }
};

// 取消申请
const cancelInterventionRequest = () => {
  showInterventionModal.value = false;
  selectedRefundForIntervention.value = null;
  interventionForm.value = {
    evidence: '',
    files: []
  };
};

const processForcedRefund = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确认处理强制退款任务？\n退款金额：¥${task.refundAmount}`,
      '确认处理',
      {
        confirmButtonText: '确认处理',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await processForcedRefundAPI(task.forcedRefundId);
    ElMessage.success('强制退款任务处理成功');
    fetchForcedRefunds();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('处理强制退款任务失败:', error);
      ElMessage.error('处理强制退款任务失败');
    }
  }
};

const canProcessForcedRefund = (amount) => {
  return userStore.userInfo?.balance >= amount;
};

const goToRecharge = () => {
  showRechargeModal.value = true;
};

// 验货操作方法
const viewVerification = async (orderId) => {
  try {
    const response = await getVerificationByOrderId(orderId);
    if (response.data) {
      // 显示验货详情
      ElMessage.info('查看验货记录功能');
    } else {
      ElMessage.info('该订单暂无验货记录');
    }
  } catch (error) {
    console.error('查看验货记录失败:', error);
    ElMessage.error('查看验货记录失败');
  }
};

const showVerificationResultDialog = (verification) => {
  selectedVerification.value = verification;
  verificationResultForm.value = {
    passed: true,
    result: '',
    imageUrls: []
  };
  showVerificationResultModal.value = true;
};

const submitVerificationResult = async () => {
  try {
    await submitVerificationResultAPI({
      verificationId: selectedVerification.value.verificationId,
      passed: verificationResultForm.value.passed,
      result: verificationResultForm.value.result,
      imageUrls: verificationResultForm.value.imageUrls
    });
    ElMessage.success('验货结果提交成功');
    showVerificationResultModal.value = false;
    fetchPendingVerifications();
  } catch (error) {
    console.error('提交验货结果失败:', error);
    ElMessage.error('提交验货结果失败');
  }
};

// 状态文本和样式方法
const getRefundStatusText = (status) => {
  const statusMap = {
    'pending_seller': '等待商家处理',
    'seller_approved': '商家已同意',
    'seller_rejected': '商家已拒绝',
    'pending_admin': '等待管理员审核',
    'admin_approved': '管理员已同意',
    'admin_rejected': '管理员已拒绝',
    'completed': '退款完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

const getRefundStatusClass = (status) => {
  const statusMap = {
    'pending_seller': 'status-pending',
    'seller_approved': 'status-approved',
    'seller_rejected': 'status-rejected',
    'pending_admin': 'status-admin',
    'admin_approved': 'status-approved',
    'admin_rejected': 'status-rejected',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  };
  return statusMap[status] || '';
};

const getVerificationStatusText = (status) => {
  const statusMap = {
    'waiting_goods': '等待收货',
    'verifying': '验货中',
    'passed': '验货通过',
    'failed': '验货失败',
    'forwarded': '已转发'
  };
  return statusMap[status] || status;
};

const getVerificationStatusClass = (status) => {
  const statusMap = {
    'waiting_goods': 'status-waiting',
    'verifying': 'status-verifying',
    'passed': 'status-passed',
    'failed': 'status-failed',
    'forwarded': 'status-forwarded'
  };
  return statusMap[status] || '';
};



onMounted(async () => {
  // 检查是否有从其他页面设置的激活标签
  const savedSection = localStorage.getItem('activeProfileSection');
  if (savedSection) {
    activeSection.value = savedSection;
    // 使用后立即清除，避免影响下次正常访问
    localStorage.removeItem('activeProfileSection');
  }

  // 根据当前激活的标签加载对应数据
  if (activeSection.value === 'my-products') {
    fetchUserProducts();
  } else if (activeSection.value === 'my-favorites') {
    fetchUserFavorites();
  } else if (activeSection.value === 'my-followings') {
    fetchUserFollowings();
  } else if (activeSection.value === 'my-addresses') {
    fetchUserAddresses();
  } else if (activeSection.value === 'my-orders') {
    if (activeOrderTab.value === 'buyer') {
      fetchBuyerOrders();
    } else {
      fetchSellerOrders();
    }
  }
});
</script>

<style scoped>
:root {
    --primary-color: #FF0000; /* 纯红色 */
    --secondary-color: #FF3333;
    --text-color-dark: #333;
    --text-color-light: #666;
    --bg-color: #EAEAEA; /* 保持较深的页面背景色 */
    --white: #FFFFFF;
    --border-color: #EFEFEF;
    --shadow-light: rgba(0,0,0,0.05);
}

/* 移除全局body样式，改为组件内样式 */
.user-profile-page {
    font-family: 'Noto Sans SC', sans-serif;
    color: var(--text-color-dark);
    line-height: 1.6;
    min-height: 100vh; /* 确保至少占满整个视口高度 */
}

.container {
    max-width: 1400px; /* 进一步增加容器最大宽度 */
    margin: 30px auto;
    padding: 0 16px;
}

/* Top Nav - Reused */
.top-nav {
    background: #FFFFFF;
    padding: 18px 0; /* 增加上下内边距 */
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}
.top-nav .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px; /* 与容器宽度保持一致 */
    margin: 0 auto;
    padding: 0 20px; /* 增加左右内边距 */
}
.top-nav .logo {
    font-size: 36px; /* 增加logo字体大小 */
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}
.top-nav .nav-right {
  display: flex;
  align-items: center;
}
.user-avatar {
  display: flex;
  align-items: center;
}
.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF0000; /* 使用硬编码的红色，加粗边框 */
  transition: transform 0.2s ease;
}
.avatar-image:hover {
  transform: scale(1.1);
}

/* Main Profile Layout */
.profile-wrapper {
    display: flex;
    gap: 35px; /* 增加间距 */
    align-items: flex-start;
}

.sidebar {
    flex-shrink: 0;
    width: 280px; /* 调整侧边栏宽度，变细一些 */
    background-color: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    padding: 30px 0; /* 增加上下内边距 */
    border: 1px solid #E0E0E0;
}

.sidebar-header {
    text-align: center;
    padding: 25px 15px 30px; /* 增加上下内边距，减少左右内边距 */
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 30px; /* 增加下边距 */
}
.sidebar-header img {
    width: 90px; /* 稍微调整头像大小 */
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 18px; /* 增加下边距 */
    border: 2px solid #FF0000;
}
.sidebar-header h2 {
    font-size: 22px; /* 增加字体大小 */
    font-weight: 500;
    margin-bottom: 6px; /* 增加下边距 */
}
.sidebar-header p {
    font-size: 16px; /* 增加字体大小 */
    color: var(--text-color-light);
}

.edit-profile-btn {
    margin-top: 15px;
    padding: 8px 20px;
    background-color: #FF0000;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-profile-btn:hover {
    background-color: #FF3333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

.phone-info {
    margin: 8px 0;
    font-size: 14px;
    color: #666;
}

.phone-info.no-phone {
    color: #999;
    font-style: italic;
}

.bind-phone-btn {
    margin-top: 10px;
    padding: 8px 20px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.bind-phone-btn:hover {
    background-color: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.sidebar-nav li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 18px 20px; /* 增加上下内边距，减少左右内边距 */
    color: var(--text-color-dark);
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}
.sidebar-nav li a:hover,
.sidebar-nav li a.active {
    background-color: #FFF0F0; /* 更淡的红色背景 */
    color: #FF0000; /* 纯红色文字 */
    border-left-color: #FF0000; /* 纯红色边框 */
}
.sidebar-nav svg {
    width: 22px; /* 增加图标大小 */
    height: 22px; /* 增加图标大小 */
}

.profile-content {
    flex-grow: 1;
    background-color: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    padding: 50px; /* 进一步增加内边距 */
    border: 1px solid #E0E0E0;
}

.content-section-title {
    font-size: 28px; /* 增加标题字体大小 */
    font-weight: 700;
    color: #FF0000;
    margin-bottom: 25px; /* 增加下边距 */
    border-bottom: 2px solid #E0E0E0;
    padding-bottom: 15px; /* 增加下内边距 */
}

/* Placeholder for content */
.placeholder-content {
    background-color: #F5F5F5;
    border-radius: 8px;
    padding: 50px; /* 增加内边距 */
    text-align: center;
    color: var(--text-color-light);
    font-size: 20px; /* 增加字体大小 */
    border: 1px solid #E5E5E5;
}

/* 账户设置样式 */
.account-settings-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.avatar-section, .profile-section {
    background-color: #F5F5F5;
    border-radius: 8px;
    padding: 30px;
    border: 1px solid #E5E5E5;
}

.avatar-section h3, .profile-section h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #E0E0E0;
}

.avatar-upload {
    display: flex;
    align-items: center;
    gap: 30px;
}

.preview-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #FF0000;
}

.upload-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.upload-btn {
    padding: 8px 15px;
    background-color: #FF0000;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.upload-btn:hover {
    background-color: #FF3333;
}

.upload-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: #333;
}

.form-group input, .form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    font-family: inherit;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* 绑定手机号表单样式 */
.bind-phone-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.code-input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.code-input-group input {
    flex: 1;
}

.send-code-btn {
    padding: 12px 16px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
    transition: background-color 0.3s;
    min-width: 100px;
}

.send-code-btn:hover:not(:disabled) {
    background-color: #337ecc;
}

.send-code-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.form-actions {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
}

.submit-btn {
    padding: 10px 25px;
    background-color: #FF0000;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.submit-btn:hover {
    background-color: #FF3333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

/* 头像上传样式 */
.avatar-upload-section {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.avatar-preview {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    border: 3px solid #FF0000;
    transition: all 0.3s ease;
}

.avatar-preview:hover .avatar-overlay {
    opacity: 1;
}

.modal-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-overlay span {
    color: white;
    font-size: 12px;
    text-align: center;
    padding: 0 5px;
}

.cancel-btn {
    padding: 10px 20px;
    background-color: #e0e0e0;
    color: #333;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    background-color: #d0d0d0;
}

/* Responsive */
@media (max-width: 900px) {
    .profile-wrapper { flex-direction: column; }
    .sidebar { width: 100%; padding: 0; margin-bottom: 20px; }
    .sidebar-nav ul { display: flex; flex-wrap: wrap; justify-content: center; }
    .sidebar-nav li { width: 50%; /* Adjust as needed */ }
    .sidebar-nav li a { justify-content: center; border-left: none; border-bottom: 3px solid transparent; }
    .sidebar-nav li a:hover, .sidebar-nav li a.active { border-bottom-color: #FF0000; } /* 更新边框颜色 */
    .profile-content { padding: 20px; }
    .content-section-title { font-size: 20px; }
}

@media (max-width: 600px) {
    .sidebar-nav li { width: 100%; }
    .sidebar-nav li a { justify-content: flex-start; padding: 12px 16px; }
}

/* Floating action buttons styles are now in the component */

/* 收藏列表样式 */
.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.favorite-card {
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #EFEFEF;
}

.favorite-card:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.favorite-image {
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.favorite-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.favorite-image:hover img {
  transform: scale(1.05);
}

.favorite-info {
  padding: 15px;
}

.favorite-title {
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 1.4;
  height: 44px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
}

.favorite-title:hover {
  color: #FF0000;
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorite-price {
  font-size: 18px;
  font-weight: bold;
  color: #FF0000;
}

.unfavorite-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: transparent;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 5px;
  transition: all 0.2s ease;
}

.unfavorite-btn:hover {
  color: #FF0000;
  transform: scale(1.05);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 0, 0, 0.1);
  border-left-color: #FF0000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.browse-products-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 10px 20px;
  background-color: #FF0000;
  color: white;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.browse-products-btn:hover {
  background-color: #FF3333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 900px) {
  .favorites-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
  }

  .favorite-image {
    height: 150px;
  }
}

@media (max-width: 600px) {
  .favorites-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
  }

  .favorite-image {
    height: 120px;
  }

  .favorite-title {
    font-size: 14px;
    height: 40px;
  }

  .favorite-price {
    font-size: 16px;
  }

  .unfavorite-btn {
    font-size: 12px;
  }
}

/* 我的发布列表样式 */
.my-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.product-card {
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.06);
  transition: all 0.3s ease;
  border: 1px solid #EFEFEF;
}

.product-card:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.08);
  transform: translateY(-2px);
}

/* 不可编辑的商品卡片样式 */
.product-card.non-editable {
  opacity: 0.8;
  border: 1px solid #d9d9d9;
  background: #fafafa;
}

.product-card.non-editable:hover {
  transform: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.06);
}

.product-card.non-editable .product-title {
  color: #8c8c8c;
}

/* 不可编辑提示样式 */
.non-editable-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #ff4d4f;
  margin-bottom: 6px;
  font-weight: 500;
}

.product-image {
  height: 160px;
  overflow: hidden;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image:hover img {
  transform: scale(1.05);
}

.product-status {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  background-color: rgba(0, 0, 0, 0.6);
}

.product-status.available {
  background-color: #52c41a;
}

.product-status.sold {
  background-color: #f5222d;
}

.product-status.pending_review {
  background-color: #faad14;
}

.product-status.off_shelf_by_seller,
.product-status.off_shelf_by_admin,
.product-status.deleted {
  background-color: #8c8c8c;
}

.product-info {
  padding: 12px;
}

.product-title {
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #FF0000;
}

.product-date {
  font-size: 12px;
  color: #999;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.edit-btn, .delete-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.edit-btn {
  background-color: #f0f0f0;
  color: #333;
}

.edit-btn:hover {
  background-color: #e0e0e0;
}

.delete-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.delete-btn:hover {
  background-color: #ffd4d4;
}

/* 禁用状态的按钮样式 */
.edit-btn:disabled,
.delete-btn:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  opacity: 0.6;
}

.edit-btn:disabled:hover,
.delete-btn:disabled:hover {
  background-color: #f5f5f5;
  color: #bfbfbf;
}

.publish-product-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 10px 20px;
  background-color: #FF0000;
  color: white;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.publish-product-btn:hover {
  background-color: #FF3333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 900px) {
  .my-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .product-image {
    height: 130px;
  }

  .product-actions {
    flex-direction: column;
    gap: 8px;
  }

  .edit-btn, .delete-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .my-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
  }

  .product-image {
    height: 100px;
  }

  .product-title {
    font-size: 12px;
    height: 34px;
  }

  .product-price {
    font-size: 14px;
  }

  .product-date {
    font-size: 10px;
  }

  .product-actions {
    flex-direction: column;
    gap: 6px;
  }

  .edit-btn, .delete-btn {
    width: 100%;
    justify-content: center;
    padding: 4px 6px;
    font-size: 11px;
  }
}

/* 商品编辑弹窗样式 */
.product-edit-modal {
  width: 90%;
  max-width: 600px;
}

.product-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-form label {
  font-weight: 500;
  color: #333;
}

.product-form input,
.product-form textarea,
.product-form select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  font-family: inherit;
}

/* 商品图片上传样式 */
.product-image-upload {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.product-image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #FF0000;
  transition: all 0.3s ease;
}

.product-image-preview:hover .image-overlay {
  opacity: 1;
}

.product-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-overlay span {
  color: white;
  font-size: 14px;
  text-align: center;
  padding: 0 5px;
}

.product-form select {
  background-color: white;
}

.product-form textarea {
  resize: vertical;
  min-height: 100px;
}

/* 状态选择器样式 */
.status-selector {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.status-option {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-option.active {
  border-color: #FF0000;
  background-color: #FFF0F0;
  color: #FF0000;
}

.status-option:hover:not(.active) {
  background-color: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .product-edit-modal {
    width: 95%;
  }

  .product-form input,
  .product-form textarea,
  .product-form select {
    padding: 10px;
    font-size: 14px;
  }
}

/* 关注列表样式 */
.followings-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.following-item {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  border: 1px solid #EFEFEF;
}

.following-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transform: translateY(-2px);
}

.following-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  cursor: pointer;
  border: 2px solid #FF0000;
}

.following-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.following-info {
  flex-grow: 1;
  margin-left: 15px;
  display: flex;
  flex-direction: column;
}

.following-username {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 5px 0;
  cursor: pointer;
}

.following-username:hover {
  color: #FF0000;
}

.following-date {
  font-size: 12px;
  color: #999;
}

.following-actions {
  display: flex;
  gap: 10px;
}

.view-profile-btn, .unfollow-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.view-profile-btn {
  background-color: #f0f0f0;
  color: #333;
}

.view-profile-btn:hover {
  background-color: #e0e0e0;
}

.unfollow-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.unfollow-btn:hover {
  background-color: #ffd4d4;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 15px;
}

.page-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-button:hover:not(:disabled) {
  border-color: #FF0000;
  color: #FF0000;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 浏览商家按钮样式 */
.browse-sellers-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 10px 20px;
  background-color: #FF0000;
  color: white;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.browse-sellers-btn:hover {
  background-color: #FF3333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .following-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .following-info {
    margin: 10px 0;
  }

  .following-actions {
    width: 100%;
    justify-content: space-between;
  }
}

/* 地址管理样式 */
.address-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.add-address-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #FF0000;
  color: white;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-address-btn:hover {
  background-color: #FF3333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

.addresses-list {
  margin-top: 20px;
}

.no-addresses {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.no-addresses svg {
  margin-bottom: 15px;
}

.address-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.address-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.address-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.address-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.address-phone {
  margin-left: 10px;
  color: #666;
  font-weight: normal;
}

.default-badge {
  background-color: #FF0000;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.address-detail {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
}

.address-card-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.address-edit-btn,
.address-default-btn,
.address-delete-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.address-edit-btn {
  background-color: #f0f0f0;
  color: #333;
}

.address-edit-btn:hover {
  background-color: #e0e0e0;
}

.address-default-btn {
  background-color: #f0f9ff;
  color: #1890ff;
}

.address-default-btn:hover {
  background-color: #e6f7ff;
}

.address-delete-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.address-delete-btn:hover {
  background-color: #ffccc7;
}

/* 地址表单样式 */
.address-form-modal {
  max-width: 550px;
}

.error-message {
  color: #FF0000;
  font-size: 12px;
  margin-top: 4px;
}

.error-input {
  border-color: #FF0000 !important;
}

.checkbox {
  padding: 10px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: #FF0000;
}

.checkbox label {
  margin: 0;
  cursor: pointer;
}

@media (max-width: 768px) {
  .address-card-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

/* 我的订单样式 */
.order-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}
.order-tabs button {
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  position: relative;
  top: 1px;
}
.order-tabs button.active {
  color: #FF0000;
  border-bottom: 2px solid #FF0000;
  font-weight: 500;
}

.order-filters {
  margin-bottom: 20px;
}

.status-filter-select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
}

.order-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 12px 15px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
}

.order-header .seller-info {
  margin-left: auto;
}
.order-header .seller-username {
  color: #337ab7;
  cursor: pointer;
}
.order-header .seller-username:hover {
  text-decoration: underline;
}

.order-body {
  display: flex;
  align-items: center;
  padding: 15px;
  gap: 15px;
}

.product-details {
  flex-grow: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-image-thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.product-title {
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.product-title:hover {
  color: #FF0000;
}

.order-price {
  width: 120px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.order-status {
  width: 100px;
  text-align: center;
  font-weight: 500;
  color: #FF0000;
}

.order-actions-container {
  width: 120px;
  text-align: center;
}

.action-btn-details,
.action-btn-cancel,
.action-btn-ship,
.action-btn-confirm,
.action-btn-pay {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  margin: 0 5px;
}

.action-btn-details {
  background-color: #1890ff;
  color: white;
}
.action-btn-details:hover {
  background-color: #40a9ff;
}

.action-btn-cancel {
  background-color: #f5222d;
  color: white;
}
.action-btn-cancel:hover {
  background-color: #ff4d4f;
}

.action-btn-ship {
  background-color: #52c41a;
  color: white;
}
.action-btn-ship:hover {
  background-color: #73d13d;
}

.action-btn-confirm {
  background-color: #faad14;
  color: white;
}
.action-btn-confirm:hover {
  background-color: #ffc53d;
}

.action-btn-pay {
  background-color: #FF0000;
  color: white;
  font-weight: 500;
}

.action-btn-pay:hover {
  background-color: #e60000;
}

.pending-payment-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

@media (max-width: 768px) {
  .order-body {
    flex-wrap: wrap;
  }
  .product-details {
    width: 100%;
  }
  .order-price, .order-status, .order-actions-container {
    width: 33.33%;
    text-align: left;
    margin-top: 10px;
  }
  .order-actions-container {
    text-align: right;
  }
}

.order-footer {
  padding: 12px 15px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.address-icon {
  display: inline-flex;
  align-items: center;
}

@media (max-width: 768px) {
  .order-body {
    flex-wrap: wrap;
  }
  .product-details {
    width: 100%;
  }
  .order-price, .order-status, .order-actions-container {
    width: 33.33%;
    text-align: left;
    margin-top: 10px;
  }
  .order-actions-container {
    text-align: right;
  }
}

/* 子菜单样式 */
.has-submenu {
  position: relative;
}

.submenu-arrow {
  margin-left: auto;
  transition: transform 0.2s ease;
}

.submenu-arrow.expanded {
  transform: rotate(180deg);
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: #f8f9fa;
}

.submenu li a {
  padding: 12px 20px 12px 50px !important;
  font-size: 14px;
  color: #666;
  border-left: none !important;
}

.submenu li a:hover,
.submenu li a.active {
  background: #e9ecef;
  color: #FF0000;
}

/* 补充材料样式 */
.material-requests-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.material-request-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.request-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.request-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-waiting {
  background: #fff3cd;
  color: #856404;
}

.status-submitted {
  background: #d1ecf1;
  color: #0c5460;
}

.status-approved {
  background: #d4edda;
  color: #155724;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.request-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
  text-align: right;
}

.request-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.product-info,
.request-reason {
  line-height: 1.5;
}

.required-materials ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.required-materials li {
  margin-bottom: 4px;
}

.submitted-materials {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.materials-list {
  margin-top: 10px;
}

.material-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 10px;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.material-type {
  font-weight: 500;
  color: #333;
}

.submit-time {
  font-size: 12px;
  color: #666;
}

.material-description {
  margin-bottom: 8px;
  color: #555;
  line-height: 1.4;
}

.material-files {
  display: flex;
  gap: 8px;
}

.material-file-link {
  color: #007bff;
  text-decoration: none;
  font-size: 12px;
  padding: 2px 6px;
  border: 1px solid #007bff;
  border-radius: 3px;
  transition: all 0.2s;
}

.material-file-link:hover {
  background: #007bff;
  color: white;
}

.request-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.submit-material-btn {
  background: #FF0000;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.submit-material-btn:hover {
  background: #e60000;
}

/* 提交材料弹窗样式 */
.submit-material-modal {
  max-width: 600px;
  width: 90%;
}

.material-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.file-upload-area:hover {
  border-color: #FF0000;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
}

.upload-hint {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.uploaded-files {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
}

.file-name {
  font-size: 14px;
  color: #333;
}

.remove-file-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-btn:hover {
  background: #c82333;
}

/* 余额和充值样式 */
.balance-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  color: white;
}

.balance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
}

.balance-amount {
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.recharge-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.recharge-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.recharge-btn svg {
  width: 16px;
  height: 16px;
}

/* 充值弹窗样式 */
.recharge-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.current-balance {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
}

.current-balance .balance-label {
  color: #666;
  font-size: 14px;
}

.current-balance .balance-amount {
  color: #333;
  font-size: 20px;
  font-weight: bold;
  margin-left: 8px;
}

.amount-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 12px;
  color: #666;
  font-size: 16px;
  z-index: 1;
}

.amount-input-group input {
  padding-left: 32px;
  font-size: 16px;
  font-weight: 500;
}

.quick-amount-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.quick-amount-btn {
  padding: 10px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.quick-amount-btn:hover {
  border-color: #FF0000;
  color: #FF0000;
}

.payment-method {
  margin-top: 10px;
}

.payment-method label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #f8f9fa;
}

.payment-option input[type="radio"] {
  margin-right: 10px;
}

.payment-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.payment-icon {
  font-size: 18px;
}

.recharge-submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.recharge-submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.recharge-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 退款相关样式 */
.refunds-list, .forced-refunds-list, .verifications-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.refund-item, .forced-refund-item, .verification-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.refund-header, .task-header, .verification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.refund-id, .task-id, .verification-id {
  font-weight: bold;
  color: #666;
}

.refund-status, .verification-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.task-amount {
  font-size: 18px;
  font-weight: bold;
  color: #f44336;
}

.status-pending { background-color: #ff9800; color: white; }
.status-approved { background-color: #4caf50; color: white; }
.status-rejected { background-color: #f44336; color: white; }
.status-admin { background-color: #9c27b0; color: white; }
.status-completed { background-color: #8bc34a; color: white; }
.status-cancelled { background-color: #607d8b; color: white; }
.status-waiting { background-color: #ff9800; color: white; }
.status-verifying { background-color: #2196f3; color: white; }
.status-passed { background-color: #4caf50; color: white; }
.status-failed { background-color: #f44336; color: white; }
.status-forwarded { background-color: #8bc34a; color: white; }

.refund-content, .task-content, .verification-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.refund-info, .task-info, .verification-info {
  flex: 1;
}

.refund-info h3, .task-info h3, .verification-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.refund-amount {
  color: #f44336;
  font-weight: bold;
  font-size: 18px;
  margin: 5px 0;
}

.refund-reason, .refund-date, .buyer-name, .buyer-info, .admin-note, .verification-result, .verification-date, .buyer-seller-info {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.admin-note {
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.refund-actions, .task-actions, .verification-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn-refund {
  background-color: #ff9800;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-verification {
  background-color: #00bcd4;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-intervention {
  background-color: #ff9800;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-approve {
  background-color: #4caf50;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-reject {
  background-color: #f44336;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-process {
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-recharge {
  background-color: #ffc107;
  color: #212529;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn-verify {
  background-color: #2196f3;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.warning-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.warning-notice h3 {
  color: #856404;
  margin: 0 0 10px 0;
}

.warning-notice p {
  color: #856404;
  margin: 5px 0;
}

/* 退款申请模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #666;
}

.modal-body {
  padding: 20px;
}

.refund-form .form-group {
  margin-bottom: 20px;
}

.refund-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.refund-form .form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.refund-form .form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.refund-form textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.modal-footer .cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.modal-footer .cancel-btn:hover {
  background: #e9ecef;
}

.modal-footer .submit-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.modal-footer .submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.modal-footer .submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 拒绝退款表单样式 */
.reject-form .form-group {
  margin-bottom: 20px;
}

.reject-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.reject-form .form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
  resize: vertical;
  min-height: 100px;
}

.reject-form .form-control:focus {
  outline: none;
  border-color: #f44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.25);
}

.reject-form .form-hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 申请管理员介入模态框样式 */
.intervention-modal {
  max-width: 600px;
  width: 90%;
}

.intervention-form .form-group {
  margin-bottom: 20px;
}

.intervention-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  transition: border-color 0.3s;
}

.file-upload-area:hover {
  border-color: #007bff;
}

.upload-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.upload-btn:hover {
  background: #0056b3;
}

.upload-icon {
  font-size: 16px;
}

.upload-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.file-list {
  margin-top: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.file-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.clear-all-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-all-btn:hover {
  background: #5a6268;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.remove-file-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.remove-file-btn:hover {
  background: #c82333;
}

.intervention-form .form-hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style>
