<template>
  <div class="audit-test-container">
    <div class="page-header">
      <h1>🧪 审核功能测试页面</h1>
      <p>测试自动审核、人工审核和二度复审的各种功能</p>
    </div>

    <!-- 测试功能区域 -->
    <div class="test-sections">

      <!-- 自动审核测试 -->
      <div class="test-section">
        <h2>🤖 自动审核测试</h2>
        <div class="test-buttons">
          <button @click="testAutoAudit" class="test-btn primary">
            测试自动审核API
          </button>
          <button @click="testAutoAuditStats" class="test-btn">
            测试统计数据
          </button>
          <button @click="createTestProduct" class="test-btn success">
            创建正常商品
          </button>
          <button @click="createSensitiveProduct" class="test-btn warning">
            创建敏感词商品
          </button>
          <button @click="createBannedProduct" class="test-btn danger">
            创建违禁词商品
          </button>
          <button @click="viewSensitiveWords" class="test-btn info">
            查看敏感词库
          </button>
          <button @click="quickTestAutoAudit" class="test-btn primary" style="font-weight: bold;">
            🚀 快速测试自动审核
          </button>
        </div>
        <div v-if="autoAuditResult" class="test-result">
          <h3>自动审核结果：</h3>
          <pre>{{ JSON.stringify(autoAuditResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 人工审核测试 -->
      <div class="test-section">
        <h2>👨‍💼 人工审核测试</h2>
        <div class="test-buttons">
          <button @click="testManualAudit" class="test-btn primary">
            测试人工审核API
          </button>
          <button @click="testClaimTask" class="test-btn">
            测试认领任务
          </button>
          <button @click="testSubmitDecision" class="test-btn warning">
            测试提交决策
          </button>
        </div>
        <div v-if="manualAuditResult" class="test-result">
          <h3>人工审核结果：</h3>
          <pre>{{ JSON.stringify(manualAuditResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 二度复审测试 -->
      <div class="test-section">
        <h2>🔍 二度复审测试</h2>
        <div class="test-buttons">
          <button @click="testSecondReview" class="test-btn primary">
            测试二度复审API
          </button>
          <button @click="testReviewDecision" class="test-btn warning">
            测试复审决策
          </button>
        </div>
        <div v-if="secondReviewResult" class="test-result">
          <h3>二度复审结果：</h3>
          <pre>{{ JSON.stringify(secondReviewResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 阿里云图片审核测试 -->
      <div class="test-section">
        <h2>🖼️ 阿里云图片审核测试（增强版）</h2>
        <div class="version-info">
          <span class="version-badge enhanced">增强版</span>
          <span class="version-desc">使用阿里云内容安全增强版API，提供AI智能内容识别</span>
        </div>
        <div class="test-buttons">
          <button @click="testAliyunImageAudit" class="test-btn primary">
            测试阿里云图片审核
          </button>
          <button @click="testImageAuditWithUrl" class="test-btn">
            测试指定URL图片审核
          </button>
          <button @click="testMultipleImages" class="test-btn info">
            测试多张图片审核
          </button>
          <button @click="checkAliyunConfig" class="test-btn secondary">
            检查阿里云配置
          </button>
          <button @click="testNetworkConnectivity" class="test-btn warning">
            测试网络连通性
          </button>
          <button @click="showSetupGuide" class="test-btn info">
            📖 配置指南
          </button>
          <button @click="openAliyunConsole" class="test-btn success">
            🚀 开通服务
          </button>
          <button @click="detailedAliyunCheck" class="test-btn warning">
            🔍 详细诊断
          </button>
        </div>

        <!-- 图片URL输入 -->
        <div class="image-test-input">
          <label for="imageUrl">测试图片URL：</label>
          <input
            id="imageUrl"
            v-model="testImageUrl"
            type="url"
            placeholder="输入图片URL，例如：https://example.com/image.jpg"
            class="url-input"
          />
          <button @click="testSingleImageUrl" class="test-btn small">测试此URL</button>
        </div>

        <!-- 预设图片测试 -->
        <div class="preset-images">
          <h4>预设测试图片：</h4>
          <div class="preset-buttons">
            <button @click="testPresetImage('normal')" class="preset-btn normal">
              正常图片
            </button>
            <button @click="testPresetImage('sensitive')" class="preset-btn sensitive">
              敏感图片
            </button>
            <button @click="testPresetImage('banned')" class="preset-btn banned">
              违禁图片
            </button>
          </div>
        </div>

        <div v-if="imageAuditResult" class="test-result">
          <h3>阿里云图片审核结果：</h3>
          <div class="image-audit-details">
            <div class="audit-summary">
              <span class="status" :class="imageAuditResult.passed ? 'passed' : 'failed'">
                {{ imageAuditResult.passed ? '✅ 通过' : '❌ 不通过' }}
              </span>
              <span class="score">评分: {{ imageAuditResult.score || 0 }}</span>
              <span class="risk">风险: {{ getRiskLevelText(imageAuditResult.riskLevel) }}</span>
            </div>
            <div class="audit-reason">
              <strong>原因:</strong> {{ imageAuditResult.reason || '无' }}
            </div>
            <details class="audit-raw-data">
              <summary>查看原始数据</summary>
              <pre>{{ JSON.stringify(imageAuditResult, null, 2) }}</pre>
            </details>
          </div>
        </div>
      </div>

      <!-- 后端连接测试 -->
      <div class="test-section">
        <h2>🔗 后端连接测试</h2>
        <div class="test-buttons">
          <button @click="testBackendConnection" class="test-btn info">
            测试后端连接
          </button>
          <button @click="testAllAPIs" class="test-btn danger">
            测试所有API
          </button>
        </div>
        <div class="connection-status">
          <div class="status-item">
            <span class="status-label">后端状态:</span>
            <span :class="['status-value', backendStatus.connected ? 'connected' : 'disconnected']">
              {{ backendStatus.connected ? '✅ 已连接' : '❌ 未连接' }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">最后测试时间:</span>
            <span class="status-value">{{ backendStatus.lastTest || '未测试' }}</span>
          </div>
        </div>
        <div v-if="connectionResult" class="test-result">
          <h3>连接测试结果：</h3>
          <pre>{{ JSON.stringify(connectionResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 测试日志 -->
      <div class="test-section">
        <h2>📋 测试日志</h2>
        <div class="test-buttons">
          <button @click="clearLogs" class="test-btn secondary">
            清空日志
          </button>
        </div>
        <div class="test-logs">
          <div v-for="(log, index) in testLogs" :key="index" :class="['log-item', log.type]">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { autoAuditApi } from '@/admin/api/autoAudit'
import { manualAuditApi } from '@/admin/api/manualAudit'
import { secondReviewApi } from '@/admin/api/secondReview'
import request from '@/utils/request'

// 响应式数据
const autoAuditResult = ref(null)
const manualAuditResult = ref(null)
const secondReviewResult = ref(null)
const connectionResult = ref(null)
const imageAuditResult = ref(null)
const testLogs = ref([])
const testImageUrl = ref('https://via.placeholder.com/400x300.jpg?text=Test+Image')

const backendStatus = reactive({
  connected: false,
  lastTest: null
})

// 添加日志
const addLog = (message, type = 'info') => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })

  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 测试自动审核API
const testAutoAudit = async () => {
  try {
    addLog('开始测试自动审核API...', 'info')

    // 首先创建一个测试商品
    addLog('正在创建测试商品...', 'info')
    const createResult = await request.post('/admin/audit/test/create-test-product', null, {
      params: {
        title: '自动审核测试商品',
        description: '这是一个用于测试自动审核的商品',
        price: '199.99',
        sellerId: 2
      }
    })

    const productId = createResult.data.productId
    addLog(`测试商品创建成功，商品ID: ${productId}`, 'success')

    // 然后对该商品执行自动审核
    addLog(`开始对商品 ${productId} 执行自动审核...`, 'info')
    const auditResult = await request.post(`/admin/audit/test/auto-audit/${productId}`)

    autoAuditResult.value = {
      createResult: createResult.data,
      auditResult: auditResult.data,
      productId: productId
    }

    addLog('自动审核执行成功', 'success')
    addLog('可以查看自动审核记录页面查看详细结果', 'info')

  } catch (error) {
    addLog(`自动审核API测试失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 测试自动审核统计
const testAutoAuditStats = async () => {
  try {
    addLog('开始测试自动审核统计API...', 'info')
    const result = await autoAuditApi.getStats()
    autoAuditResult.value = result
    addLog('自动审核统计API测试成功', 'success')
  } catch (error) {
    addLog(`自动审核统计API测试失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 查看敏感词库
const viewSensitiveWords = async () => {
  try {
    addLog('开始获取敏感词库...', 'info')
    const result = await request.get('/admin/audit/test/sensitive-words')

    autoAuditResult.value = result
    addLog('敏感词库获取成功', 'success')

    const data = result.data
    addLog(`违禁词数量: ${data.banned_words?.length || 0}`, 'info')
    addLog(`敏感词数量: ${data.sensitive_words?.length || 0}`, 'info')
    addLog(`品牌词数量: ${data.brand_words?.length || 0}`, 'info')

  } catch (error) {
    addLog(`获取敏感词库失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 创建正常测试商品
const createTestProduct = async () => {
  try {
    addLog('开始创建正常测试商品...', 'info')
    const result = await request.post('/admin/audit/test/create-test-product', null, {
      params: {
        title: '正常测试商品',
        description: '这是一个正常的商品，应该通过自动审核',
        price: '99.99',
        sellerId: 2
      }
    })

    autoAuditResult.value = result
    addLog(`正常商品创建成功，商品ID: ${result.data.productId}`, 'success')
    addLog('可以点击"测试自动审核API"来测试该商品', 'info')
  } catch (error) {
    addLog(`创建正常商品失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 创建敏感词商品
const createSensitiveProduct = async () => {
  try {
    addLog('开始创建包含敏感词的商品...', 'info')
    const result = await request.post('/admin/audit/test/create-sensitive-product', null, {
      params: {
        sellerId: 1
      }
    })

    autoAuditResult.value = result
    addLog(`敏感词商品创建成功，商品ID: ${result.data.productId}`, 'success')
    addLog('该商品包含敏感词，应该进入人工审核', 'warning')
  } catch (error) {
    addLog(`创建敏感词商品失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 创建违禁词商品
const createBannedProduct = async () => {
  try {
    addLog('开始创建包含违禁词的商品...', 'info')
    const result = await request.post('/admin/audit/test/create-banned-product', null, {
      params: {
        sellerId: 1
      }
    })

    autoAuditResult.value = result
    addLog(`违禁词商品创建成功，商品ID: ${result.data.productId}`, 'success')
    addLog('该商品包含违禁词，应该被自动拒绝', 'error')
  } catch (error) {
    addLog(`创建违禁词商品失败: ${error.message}`, 'error')
    autoAuditResult.value = { error: error.message }
  }
}

// 测试人工审核API
const testManualAudit = async () => {
  try {
    addLog('开始测试人工审核API...', 'info')
    const result = await manualAuditApi.getTasks({ page: 1, pageSize: 5 })
    manualAuditResult.value = result
    addLog('人工审核API测试成功', 'success')
  } catch (error) {
    addLog(`人工审核API测试失败: ${error.message}`, 'error')
    manualAuditResult.value = { error: error.message }
  }
}

// 测试认领任务
const testClaimTask = () => {
  addLog('认领任务功能需要具体任务ID...', 'warning')
}

// 测试提交决策
const testSubmitDecision = () => {
  addLog('提交决策功能需要具体任务ID...', 'warning')
}

// 测试二度复审API
const testSecondReview = async () => {
  try {
    addLog('开始测试二度复审API...', 'info')
    const result = await secondReviewApi.getTasks({ page: 1, pageSize: 5 })
    secondReviewResult.value = result
    addLog('二度复审API测试成功', 'success')
  } catch (error) {
    addLog(`二度复审API测试失败: ${error.message}`, 'error')
    secondReviewResult.value = { error: error.message }
  }
}

// 测试复审决策
const testReviewDecision = () => {
  addLog('复审决策功能需要具体任务ID...', 'warning')
}

// 测试后端连接
const testBackendConnection = async () => {
  try {
    addLog('开始测试后端连接...', 'info')

    // 测试健康检查接口
    const result = await request.get('/admin/audit/test/health')

    backendStatus.connected = true
    backendStatus.lastTest = new Date().toLocaleString()
    connectionResult.value = {
      status: 'connected',
      message: '后端连接正常',
      data: result
    }
    addLog('后端连接测试成功', 'success')

  } catch (error) {
    backendStatus.connected = false
    backendStatus.lastTest = new Date().toLocaleString()
    connectionResult.value = {
      status: 'error',
      message: error.message
    }
    addLog(`后端连接测试失败: ${error.message}`, 'error')
  }
}

// 测试所有API
const testAllAPIs = async () => {
  addLog('开始测试所有API...', 'info')

  // 1. 测试后端连接
  await testBackendConnection()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 2. 查看敏感词库
  await viewSensitiveWords()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 3. 创建并测试正常商品
  await createTestProduct()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 4. 创建并测试敏感词商品
  await createSensitiveProduct()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 5. 创建并测试违禁词商品
  await createBannedProduct()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 6. 测试统计数据
  await testAutoAuditStats()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 7. 测试人工审核
  await testManualAudit()
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 8. 测试二度复审
  await testSecondReview()

  addLog('所有API测试完成！', 'success')
  addLog('请查看各个测试结果和日志', 'info')
}

// 快速测试自动审核
const quickTestAutoAudit = async () => {
  addLog('🚀 开始快速测试自动审核功能...', 'info')

  try {
    // 1. 测试后端连接
    addLog('1️⃣ 测试后端连接...', 'info')
    await testBackendConnection()

    if (!backendStatus.connected) {
      addLog('❌ 后端连接失败，无法继续测试', 'error')
      return
    }

    await new Promise(resolve => setTimeout(resolve, 500))

    // 2. 创建正常商品并测试
    addLog('2️⃣ 创建正常商品并执行自动审核...', 'info')
    await testAutoAudit()

    await new Promise(resolve => setTimeout(resolve, 1000))

    // 3. 创建敏感词商品
    addLog('3️⃣ 创建敏感词商品...', 'info')
    await createSensitiveProduct()

    await new Promise(resolve => setTimeout(resolve, 1000))

    // 4. 创建违禁词商品
    addLog('4️⃣ 创建违禁词商品...', 'info')
    await createBannedProduct()

    addLog('✅ 快速测试完成！请查看自动审核记录页面查看详细结果', 'success')
    addLog('💡 提示：不同类型的商品会有不同的审核结果', 'info')

  } catch (error) {
    addLog(`❌ 快速测试失败: ${error.message}`, 'error')
  }
}

// 阿里云图片审核测试方法
const testAliyunImageAudit = async () => {
  try {
    addLog('🖼️ 开始测试阿里云图片审核...', 'info')

    const testUrl = 'https://via.placeholder.com/400x300.jpg?text=Aliyun+Test'
    const result = await request.post('/admin/audit/test/image-audit', null, {
      params: {
        imageUrl: testUrl
      }
    })

    imageAuditResult.value = result.data
    addLog('✅ 阿里云图片审核测试完成', 'success')

  } catch (error) {
    addLog(`❌ 阿里云图片审核测试失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const testImageAuditWithUrl = async () => {
  if (!testImageUrl.value) {
    addLog('❌ 请输入图片URL', 'error')
    return
  }

  try {
    addLog(`🖼️ 开始审核图片: ${testImageUrl.value}`, 'info')

    const result = await request.post('/admin/audit/test/image-audit', null, {
      params: {
        imageUrl: testImageUrl.value
      }
    })

    imageAuditResult.value = result.data
    addLog('✅ 图片审核完成', 'success')

  } catch (error) {
    addLog(`❌ 图片审核失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const testSingleImageUrl = () => {
  testImageAuditWithUrl()
}

const testMultipleImages = async () => {
  const testUrls = [
    'https://via.placeholder.com/400x300.jpg?text=Image1',
    'https://via.placeholder.com/400x300.jpg?text=Image2',
    'https://via.placeholder.com/400x300.jpg?text=Image3'
  ]

  addLog('🖼️ 开始批量图片审核测试...', 'info')

  for (let i = 0; i < testUrls.length; i++) {
    try {
      addLog(`审核第 ${i + 1} 张图片: ${testUrls[i]}`, 'info')

      const result = await request.post('/admin/audit/test/image-audit', null, {
        params: {
          imageUrl: testUrls[i]
        }
      })

      addLog(`第 ${i + 1} 张图片审核完成: ${result.data.passed ? '通过' : '不通过'}`, 'success')

      // 显示最后一个结果
      if (i === testUrls.length - 1) {
        imageAuditResult.value = result.data
      }

      // 间隔1秒
      await new Promise(resolve => setTimeout(resolve, 1000))

    } catch (error) {
      addLog(`第 ${i + 1} 张图片审核失败: ${error.message}`, 'error')
    }
  }

  addLog('✅ 批量图片审核测试完成', 'info')
}

const testPresetImage = async (type) => {
  const presetUrls = {
    normal: 'https://via.placeholder.com/400x300.jpg?text=Normal+Image',
    sensitive: 'https://via.placeholder.com/400x300.jpg?text=Sensitive+Content',
    banned: 'https://via.placeholder.com/400x300.jpg?text=Banned+Content'
  }

  const url = presetUrls[type]
  if (!url) {
    addLog('❌ 未知的预设图片类型', 'error')
    return
  }

  try {
    addLog(`🖼️ 测试${type}类型图片: ${url}`, 'info')

    const result = await request.post('/admin/audit/test/image-audit', null, {
      params: {
        imageUrl: url
      }
    })

    imageAuditResult.value = result.data
    addLog(`✅ ${type}图片审核完成: ${result.data.passed ? '通过' : '不通过'}`, 'success')

  } catch (error) {
    addLog(`❌ ${type}图片审核失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const checkAliyunConfig = async () => {
  try {
    addLog('🔧 检查阿里云配置...', 'info')

    const result = await request.get('/admin/audit/test/aliyun-config')

    imageAuditResult.value = result.data
    addLog('✅ 阿里云配置检查完成', 'success')

    // 显示详细配置信息
    const data = result.data
    addLog(`服务状态: ${data.serviceAvailable ? '可用' : '不可用'}`, data.serviceAvailable ? 'success' : 'error')
    addLog(`配置状态: ${data.configStatus}`, 'info')
    addLog(`服务类型: ${data.serviceType}`, 'info')

  } catch (error) {
    addLog(`❌ 阿里云配置检查失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const testNetworkConnectivity = async () => {
  try {
    addLog('🌐 测试网络连通性...', 'info')

    const result = await request.get('/admin/audit/test/network-test')

    imageAuditResult.value = result.data
    addLog('✅ 网络连通性测试完成', 'success')

    // 显示测试结果
    const data = result.data
    if (data.tests) {
      data.tests.forEach(test => {
        const status = test.success ? '✅' : '❌'
        addLog(`${status} ${test.name}: ${test.message}`, test.success ? 'success' : 'error')
      })
    }

  } catch (error) {
    addLog(`❌ 网络连通性测试失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const showSetupGuide = async () => {
  try {
    addLog('📖 获取阿里云配置指南...', 'info')

    const result = await request.get('/admin/audit/test/aliyun-setup-guide')

    imageAuditResult.value = result.data
    addLog('✅ 配置指南获取成功', 'success')

    // 显示配置步骤
    const data = result.data
    addLog('📋 阿里云内容安全配置步骤：', 'info')

    if (data.setupSteps) {
      data.setupSteps.forEach(step => {
        addLog(`${step.step}. ${step.title}: ${step.description}`, 'info')
      })
    }

    addLog('💡 请查看测试结果区域获取详细配置信息', 'info')

  } catch (error) {
    addLog(`❌ 获取配置指南失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

const openAliyunConsole = () => {
  addLog('🚀 正在打开阿里云控制台...', 'info')

  // 打开阿里云内容安全控制台
  window.open('https://yundun.console.aliyun.com/', '_blank')

  addLog('📋 开通步骤：', 'info')
  addLog('1. 在打开的页面中找到"内容安全"服务', 'info')
  addLog('2. 点击"立即开通"或"免费试用"', 'info')
  addLog('3. 购买资源包或开启按量付费', 'info')
  addLog('4. 开通完成后回来重新测试', 'info')

  // 也可以直接打开购买页面
  setTimeout(() => {
    addLog('💰 如需购买资源包，请访问：', 'info')
    addLog('https://common-buy.aliyun.com/?commodityCode=lvwang_cip_public_cn', 'info')
  }, 2000)
}

const detailedAliyunCheck = async () => {
  try {
    addLog('🔍 开始阿里云详细诊断...', 'info')

    const result = await request.get('/admin/audit/test/aliyun-detailed-check')

    imageAuditResult.value = result.data
    addLog('✅ 详细诊断完成', 'success')

    // 显示诊断结果
    const data = result.data
    addLog('📋 诊断结果：', 'info')

    if (data.solutionSteps) {
      addLog('🔧 解决步骤：', 'info')
      data.solutionSteps.forEach((step, index) => {
        addLog(`${step}`, 'info')
      })
    }

    if (data.errorCodes) {
      addLog('❌ 常见错误码说明：', 'info')
      Object.entries(data.errorCodes).forEach(([code, desc]) => {
        addLog(`${code}: ${desc}`, 'info')
      })
    }

    addLog('💡 请查看测试结果区域获取详细诊断信息', 'info')

  } catch (error) {
    addLog(`❌ 详细诊断失败: ${error.message}`, 'error')
    imageAuditResult.value = { error: error.message }
  }
}

// 清空日志
const clearLogs = () => {
  testLogs.value = []
  addLog('日志已清空', 'info')
}
</script>

<style scoped>
.audit-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.test-sections {
  display: grid;
  gap: 20px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h2 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.test-btn.primary { background: #3498db; color: white; }
.test-btn.success { background: #2ecc71; color: white; }
.test-btn.warning { background: #f39c12; color: white; }
.test-btn.danger { background: #e74c3c; color: white; }
.test-btn.info { background: #17a2b8; color: white; }
.test-btn.secondary { background: #6c757d; color: white; }

.test-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.test-result {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.test-result h3 {
  margin-top: 0;
  color: #495057;
}

.test-result pre {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 300px;
}

.connection-status {
  margin: 15px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 100px;
}

.status-value.connected {
  color: #28a745;
}

.status-value.disconnected {
  color: #dc3545;
}

.test-logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background: #d4edda;
  color: #155724;
}

.log-item.error {
  background: #f8d7da;
  color: #721c24;
}

.log-item.warning {
  background: #fff3cd;
  color: #856404;
}

.log-item.info {
  background: #d1ecf1;
  color: #0c5460;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-message {
  color: inherit;
}

/* 版本信息样式 */
.version-info {
  margin-bottom: 15px;
  padding: 8px 12px;
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.version-badge {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.version-badge.enhanced {
  background: #007bff;
  background: linear-gradient(45deg, #007bff, #6610f2);
}

.version-desc {
  font-size: 13px;
  color: #155724;
}

/* 图片审核测试样式 */
.image-test-input {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.image-test-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.url-input {
  width: 70%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
}

.url-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.test-btn.small {
  padding: 6px 12px;
  font-size: 13px;
}

.preset-images {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.preset-images h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.preset-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.preset-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.preset-btn.normal {
  background: #28a745;
  color: white;
}

.preset-btn.sensitive {
  background: #ffc107;
  color: #212529;
}

.preset-btn.banned {
  background: #dc3545;
  color: white;
}

.preset-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.image-audit-details {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;
}

.audit-summary {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.audit-summary .status {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
}

.audit-summary .status.passed {
  background: #d4edda;
  color: #155724;
}

.audit-summary .status.failed {
  background: #f8d7da;
  color: #721c24;
}

.audit-summary .score,
.audit-summary .risk {
  padding: 4px 8px;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  color: #495057;
}

.audit-reason {
  margin: 10px 0;
  padding: 8px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.audit-raw-data {
  margin-top: 10px;
}

.audit-raw-data summary {
  cursor: pointer;
  color: #007bff;
  font-size: 12px;
  padding: 4px 0;
  user-select: none;
}

.audit-raw-data summary:hover {
  color: #0056b3;
}

.audit-raw-data pre {
  margin-top: 8px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
