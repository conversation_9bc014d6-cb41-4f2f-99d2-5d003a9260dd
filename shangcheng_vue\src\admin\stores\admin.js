import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getAdminInfo } from '../api/auth'

export const useAdminStore = defineStore('admin', () => {
  // 尝试从localStorage恢复adminInfo
  let savedAdminInfo = null;
  try {
    const savedAdminInfoStr = localStorage.getItem('adminInfo');
    if (savedAdminInfoStr) {
      savedAdminInfo = JSON.parse(savedAdminInfoStr);
    }
  } catch (e) {
    console.error('解析保存的管理员信息失败:', e);
  }

  const adminInfo = ref(savedAdminInfo)
  const token = ref(localStorage.getItem('adminToken') || '')
  const isLoggedIn = ref(!!localStorage.getItem('adminToken'))

  // 获取管理员信息
  async function fetchAdminInfo() {
    if (!token.value) {
      console.log('fetchAdminInfo: 没有token，无法获取管理员信息');
      return;
    }
    
    try {
      console.log('fetchAdminInfo: 开始获取管理员信息');
      const res = await getAdminInfo()
      console.log('fetchAdminInfo: 获取管理员信息响应', res);
      
      if (res.code === 200 && res.data) {
        // 验证是否为管理员
        if (res.data.role !== 'admin') {
          console.error('fetchAdminInfo: 用户不是管理员', res.data);
          logout()
          throw new Error('非管理员用户')
        }
        adminInfo.value = res.data
        
        // 保存管理员信息到localStorage
        localStorage.setItem('adminInfo', JSON.stringify(res.data))
        console.log('fetchAdminInfo: 管理员信息已保存', adminInfo.value);
        
        return adminInfo.value;
      } else {
        console.error('fetchAdminInfo: 获取管理员信息失败', res);
        return null;
      }
    } catch (error) {
      console.error('fetchAdminInfo: 获取管理员信息失败:', error)
      
      // 如果是401错误，清除登录状态
      if (error.response && error.response.status === 401) {
        logout()
      }
      
      // 如果有缓存的管理员信息，尝试使用它
      if (adminInfo.value) {
        console.log('fetchAdminInfo: 使用缓存的管理员信息');
        return adminInfo.value;
      }
      
      throw error;
    }
  }

  // 设置管理员登录状态
  function setToken(newToken) {
    console.log('setToken: 设置管理员token', newToken);
    token.value = newToken
    localStorage.setItem('adminToken', newToken)
    isLoggedIn.value = true
  }

  // 清除管理员状态
  function logout() {
    console.log('logout: 清除管理员状态');
    token.value = ''
    adminInfo.value = null
    isLoggedIn.value = false
    localStorage.removeItem('adminToken')
    localStorage.removeItem('adminInfo')
  }

  return { 
    adminInfo, 
    token, 
    isLoggedIn,
    fetchAdminInfo,
    setToken,
    logout
  }
}) 