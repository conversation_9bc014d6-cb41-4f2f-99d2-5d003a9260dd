package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.model.Product;

import java.util.List;
import java.util.Map;

public interface ProductService {
    
    /**
     * 分页查询商品列表
     * 
     * @param params 查询参数
     * @return 分页商品列表
     */
    PageResult<ProductDTO> getProductsByCondition(ProductQueryParams params);
    
    /**
     * 根据ID查询商品详情
     * 
     * @param productId 商品ID
     * @return 商品详情
     */
    ProductDTO getProductById(Long productId);
    
    /**
     * 创建商品
     * 
     * @param productDTO 商品信息
     * @return 创建后的商品ID
     */
    Long createProduct(ProductDTO productDTO);
    
    /**
     * 更新商品信息
     * 
     * @param productId 商品ID
     * @param productDTO 商品信息
     * @return 是否更新成功
     */
    boolean updateProduct(Long productId, ProductDTO productDTO);
    
    /**
     * 删除商品
     * 
     * @param productId 商品ID
     * @return 是否删除成功
     */
    boolean deleteProduct(Long productId);
    
    /**
     * 更改商品状态
     * 
     * @param productId 商品ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateProductStatus(Long productId, String status);
    
    /**
     * 更新商品图片
     * 
     * @param productId 商品ID
     * @param imageUrl 新图片URL
     * @return 是否更新成功
     */
    boolean updateProductImage(Long productId, String imageUrl);
    
    /**
     * 批量删除商品
     * 
     * @param productIds 商品ID列表
     * @return 成功删除的商品数量和失败的商品ID映射
     */
    Map<String, Object> batchDeleteProducts(List<Long> productIds);
    
    /**
     * 批量上架商品（将待审核商品改为可用状态）
     * 
     * @param productIds 商品ID列表
     * @return 成功上架的商品数量和失败的商品ID映射
     */
    Map<String, Object> batchApproveProducts(List<Long> productIds);
} 