<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车与结算 - 二手交易平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FF4D4F; /* Red */
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
            --shadow-light: rgba(0,0,0,0.05);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 16px;
        }

        /* Top Nav - Reused */
        .top-nav {
            background: var(--white);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .top-nav .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }
        .top-nav .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }
        .top-nav .back-link {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            margin-left: 20px;
            transition: color 0.2s ease;
        }
        .top-nav .back-link:hover { color: var(--primary-color); }

        .cart-wrapper {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .cart-items-section {
            flex: 2; /* Takes 2/3 of space */
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            padding: 30px;
        }

        .cart-items-section h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 30px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
        }

        .cart-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px 0;
            border-bottom: 1px dashed var(--border-color);
        }
        .cart-item:last-child { border-bottom: none; }

        .cart-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .item-details {
            flex-grow: 1;
        }
        .item-details h3 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        .item-details .price {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
        }
        .item-details .quantity-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }
        .item-details .quantity-selector button {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            width: 30px; height: 30px;
            display: flex; justify-content: center; align-items: center;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s ease;
        }
        .item-details .quantity-selector button:hover { background-color: var(--medium-grey); }
        .item-details .quantity-selector input {
            width: 40px;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 0;
            font-size: 14px;
        }

        .item-actions button {
            background: none;
            border: none;
            color: var(--text-color-light);
            font-size: 14px;
            cursor: pointer;
            margin-left: 15px;
            transition: color 0.2s ease;
        }
        .item-actions button:hover { color: var(--primary-color); }

        .checkout-summary {
            flex: 1; /* Takes 1/3 of space */
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            padding: 30px;
            position: sticky;
            top: 80px; /* Aligned with top nav */
        }

        .checkout-summary h2 {
            font-size: 22px;
            font-weight: 700;
            color: var(--text-color-dark);
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .summary-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 15px;
        }
        .summary-line.total {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
            margin-top: 20px;
            border-top: 1px dashed var(--border-color);
            padding-top: 15px;
        }

        .checkout-button {
            width: 100%;
            padding: 14px;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 28px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 20px;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        .checkout-button:hover { background-color: var(--secondary-color); transform: translateY(-2px); }

        /* Empty Cart State */
        .empty-cart {
            text-align: center;
            padding: 50px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            margin-top: 30px;
        }
        .empty-cart p { font-size: 18px; color: var(--text-color-light); margin-bottom: 20px; }
        .empty-cart a {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }
        .empty-cart a:hover { background-color: var(--secondary-color); }

        /* Responsive */
        @media (max-width: 768px) {
            .cart-wrapper { flex-direction: column; }
            .checkout-summary { width: 100%; position: static; margin-top: 24px; }
            .cart-items-section, .checkout-summary { padding: 20px; }
            .cart-items-section h1 { font-size: 24px; }
            .cart-item { flex-wrap: wrap; text-align: center; justify-content: center; }
            .cart-item img { margin-bottom: 10px; }
            .item-details { width: 100%; text-align: center; }
            .item-details .quantity-selector { justify-content: center; margin-left: auto; margin-right: auto; }
            .item-actions { width: 100%; text-align: center; margin-top: 10px; }
        }
    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="nav-content">
            <a href="main.html" class="logo">二手交易</a>
            <a href="main.html" class="back-link">继续购物</a>
        </div>
    </nav>

    <div class="container">
        <div class="cart-wrapper">
            <section class="cart-items-section">
                <h1>我的购物车 (2)</h1>
                <div class="cart-item">
                    <img src="https://via.placeholder.com/100x100/FF5733/FFFFFF?text=iPhone" alt="商品图片">
                    <div class="item-details">
                        <h3>iPhone 14 Pro Max 256G (99新)</h3>
                        <p class="price">¥7988.00</p>
                        <div class="quantity-selector">
                            <button>-</button>
                            <input type="text" value="1" readonly>
                            <button>+</button>
                        </div>
                    </div>
                    <div class="item-actions">
                        <button>删除</button>
                    </div>
                </div>

                <div class="cart-item">
                    <img src="https://via.placeholder.com/100x100/33FF57/FFFFFF?text=Keyboard" alt="商品图片">
                    <div class="item-details">
                        <h3>机械键盘 RK68 樱桃轴 (9成新)</h3>
                        <p class="price">¥280.00</p>
                        <div class="quantity-selector">
                            <button>-</button>
                            <input type="text" value="1" readonly>
                            <button>+</button>
                        </div>
                    </div>
                    <div class="item-actions">
                        <button>删除</button>
                    </div>
                </div>

                <!-- Empty Cart State (Hidden by default, show if cart is empty) -->
                <!--
                <div class="empty-cart" style="display: none;">
                    <p>您的购物车是空的！</p>
                    <a href="main.html">去逛逛</a>
                </div>
                -->

            </section>

            <aside class="checkout-summary">
                <h2>订单总结</h2>
                <div class="summary-line">
                    <span>商品总价</span>
                    <span>¥8268.00</span>
                </div>
                <div class="summary-line">
                    <span>运费</span>
                    <span>¥0.00</span>
                </div>
                <div class="summary-line">
                    <span>优惠</span>
                    <span>-¥0.00</span>
                </div>
                <div class="summary-line total">
                    <span>应付总额</span>
                    <span>¥8268.00</span>
                </div>
                <button class="checkout-button">去结算</button>
            </aside>
        </div>
    </div>
</body>
</html> 