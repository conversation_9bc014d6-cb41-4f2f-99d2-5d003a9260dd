package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 用户充值请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RechargeRequest {
    
    /**
     * 充值金额
     */
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0.01元")
    private BigDecimal amount;
    
    /**
     * 支付方式（目前只支持支付宝）
     */
    private String paymentMethod = "alipay";
}
