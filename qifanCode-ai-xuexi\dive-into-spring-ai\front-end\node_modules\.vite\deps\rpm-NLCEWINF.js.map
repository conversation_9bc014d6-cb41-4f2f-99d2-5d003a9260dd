{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/rpm.js"], "sourcesContent": ["var headerSeparator = /^-+$/;\nvar headerLine = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\\d{1,2} \\d{2}:\\d{2}(:\\d{2})? [A-Z]{3,4} \\d{4} - /;\nvar simpleEmail = /^[\\w+.-]+@[\\w.-]+/;\n\nexport const rpmChanges = {\n  name: \"rpmchanges\",\n  token: function(stream) {\n    if (stream.sol()) {\n      if (stream.match(headerSeparator)) { return 'tag'; }\n      if (stream.match(headerLine)) { return 'tag'; }\n    }\n    if (stream.match(simpleEmail)) { return 'string'; }\n    stream.next();\n    return null;\n  }\n}\n\n// Quick and dirty spec file highlighting\n\nvar arch = /^(i386|i586|i686|x86_64|ppc64le|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/;\n\nvar preamble = /^[a-zA-Z0-9()]+:/;\nvar section = /^%(debug_package|package|description|prep|build|install|files|clean|changelog|preinstall|preun|postinstall|postun|pretrans|posttrans|pre|post|triggerin|triggerun|verifyscript|check|triggerpostun|triggerprein|trigger)/;\nvar control_flow_complex = /^%(ifnarch|ifarch|if)/; // rpm control flow macros\nvar control_flow_simple = /^%(else|endif)/; // rpm control flow macros\nvar operators = /^(\\!|\\?|\\<\\=|\\<|\\>\\=|\\>|\\=\\=|\\&\\&|\\|\\|)/; // operators in control flow macros\n\nexport const rpmSpec = {\n  name: \"rpmspec\",\n  startState: function () {\n    return {\n      controlFlow: false,\n      macroParameters: false,\n      section: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    if (ch == \"#\") { stream.skipToEnd(); return \"comment\"; }\n\n    if (stream.sol()) {\n      if (stream.match(preamble)) { return \"header\"; }\n      if (stream.match(section)) { return \"atom\"; }\n    }\n\n    if (stream.match(/^\\$\\w+/)) { return \"def\"; } // Variables like '$RPM_BUILD_ROOT'\n    if (stream.match(/^\\$\\{\\w+\\}/)) { return \"def\"; } // Variables like '${RPM_BUILD_ROOT}'\n\n    if (stream.match(control_flow_simple)) { return \"keyword\"; }\n    if (stream.match(control_flow_complex)) {\n      state.controlFlow = true;\n      return \"keyword\";\n    }\n    if (state.controlFlow) {\n      if (stream.match(operators)) { return \"operator\"; }\n      if (stream.match(/^(\\d+)/)) { return \"number\"; }\n      if (stream.eol()) { state.controlFlow = false; }\n    }\n\n    if (stream.match(arch)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"number\";\n    }\n\n    // Macros like '%make_install' or '%attr(0775,root,root)'\n    if (stream.match(/^%[\\w]+/)) {\n      if (stream.match('(')) { state.macroParameters = true; }\n      return \"keyword\";\n    }\n    if (state.macroParameters) {\n      if (stream.match(/^\\d+/)) { return \"number\";}\n      if (stream.match(')')) {\n        state.macroParameters = false;\n        return \"keyword\";\n      }\n    }\n\n    // Macros like '%{defined fedora}'\n    if (stream.match(/^%\\{\\??[\\w \\-\\:\\!]+\\}/)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"def\";\n    }\n\n    stream.next();\n    return null;\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,cAAc;AAEX,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ;AACtB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,OAAO,MAAM,eAAe,GAAG;AAAE,eAAO;AAAA,MAAO;AACnD,UAAI,OAAO,MAAM,UAAU,GAAG;AAAE,eAAO;AAAA,MAAO;AAAA,IAChD;AACA,QAAI,OAAO,MAAM,WAAW,GAAG;AAAE,aAAO;AAAA,IAAU;AAClD,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACF;AAIA,IAAI,OAAO;AAEX,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAC1B,IAAI,YAAY;AAET,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,KAAK;AAAE,aAAO,UAAU;AAAG,aAAO;AAAA,IAAW;AAEvD,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAU;AAC/C,UAAI,OAAO,MAAM,OAAO,GAAG;AAAE,eAAO;AAAA,MAAQ;AAAA,IAC9C;AAEA,QAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,aAAO;AAAA,IAAO;AAC5C,QAAI,OAAO,MAAM,YAAY,GAAG;AAAE,aAAO;AAAA,IAAO;AAEhD,QAAI,OAAO,MAAM,mBAAmB,GAAG;AAAE,aAAO;AAAA,IAAW;AAC3D,QAAI,OAAO,MAAM,oBAAoB,GAAG;AACtC,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,aAAa;AACrB,UAAI,OAAO,MAAM,SAAS,GAAG;AAAE,eAAO;AAAA,MAAY;AAClD,UAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAU;AAC/C,UAAI,OAAO,IAAI,GAAG;AAAE,cAAM,cAAc;AAAA,MAAO;AAAA,IACjD;AAEA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAI,OAAO,IAAI,GAAG;AAAE,cAAM,cAAc;AAAA,MAAO;AAC/C,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,UAAI,OAAO,MAAM,GAAG,GAAG;AAAE,cAAM,kBAAkB;AAAA,MAAM;AACvD,aAAO;AAAA,IACT;AACA,QAAI,MAAM,iBAAiB;AACzB,UAAI,OAAO,MAAM,MAAM,GAAG;AAAE,eAAO;AAAA,MAAS;AAC5C,UAAI,OAAO,MAAM,GAAG,GAAG;AACrB,cAAM,kBAAkB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,OAAO,MAAM,uBAAuB,GAAG;AACzC,UAAI,OAAO,IAAI,GAAG;AAAE,cAAM,cAAc;AAAA,MAAO;AAC/C,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACF;", "names": []}