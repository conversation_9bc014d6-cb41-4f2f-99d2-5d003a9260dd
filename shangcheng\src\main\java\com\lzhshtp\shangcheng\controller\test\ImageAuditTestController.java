package com.lzhshtp.shangcheng.controller.test;

import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.service.audit.AliyunImageAuditService;
import com.lzhshtp.shangcheng.service.audit.ImageAuditService;
import com.lzhshtp.shangcheng.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 图片审核测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/test/image-audit")
public class ImageAuditTestController {
    
    @Autowired
    private AliyunImageAuditService aliyunImageAuditService;
    
    @Autowired
    private ImageAuditService imageAuditService;
    
    /**
     * 测试阿里云单张图片审核
     */
    @PostMapping("/aliyun/single")
    public AuditResultDTO testAliyunSingleImage(@RequestParam String imageUrl) {
        log.info("测试阿里云单张图片审核，图片URL: {}", imageUrl);
        return aliyunImageAuditService.auditSingleImage(imageUrl);
    }
    
    /**
     * 测试完整图片审核流程
     */
    @PostMapping("/full")
    public AuditResultDTO testFullImageAudit(@RequestBody TestProductRequest request) {
        log.info("测试完整图片审核流程，商品: {}", request.getTitle());
        
        // 创建测试商品
        Product product = new Product();
        product.setId(999L);
        product.setTitle(request.getTitle());
        product.setDescription(request.getDescription());
        product.setImageUrls(request.getImageUrls());
        product.setPrice(new BigDecimal(request.getPrice()));
        
        return imageAuditService.auditImages(product);
    }
    
    /**
     * 检查阿里云服务状态
     */
    @GetMapping("/aliyun/status")
    public String checkAliyunStatus() {
        boolean available = aliyunImageAuditService.isServiceAvailable();
        return available ? "阿里云内容安全服务可用" : "阿里云内容安全服务不可用";
    }
    
    /**
     * 测试请求对象
     */
    public static class TestProductRequest {
        private String title;
        private String description;
        private String imageUrls;
        private String price;
        
        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getImageUrls() { return imageUrls; }
        public void setImageUrls(String imageUrls) { this.imageUrls = imageUrls; }
        
        public String getPrice() { return price; }
        public void setPrice(String price) { this.price = price; }
    }
}
