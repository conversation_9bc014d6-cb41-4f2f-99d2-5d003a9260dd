import type {Executor} from '../';

export class DocumentController {
    
    constructor(private executor: Executor) {}
    
    /**
     * 嵌入文件
     * 
     * @param file 待嵌入的文件
     * @return 是否成功
     */
    embedding: (options: DocumentControllerOptions['embedding']) => Promise<
        boolean | undefined
    > = async(options) => {
        let _uri = '/document/embedding';
        const _formData = new FormData();
        const _body = options.body;
        _formData.append("file", _body.file);
        return (await this.executor({uri: _uri, method: 'POST', body: _formData})) as Promise<boolean | undefined>;
    }
}

export type DocumentControllerOptions = {
    'embedding': {
        body: {
            file: File
        }
    }
}
