package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 论坛评论请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumCommentRequest {
    
    /**
     * 所属帖子ID
     */
    @NotNull(message = "帖子ID不能为空")
    private Long postId;
    
    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(min = 1, max = 1000, message = "评论内容长度应在1-1000字符之间")
    private String content;
    
    /**
     * 父评论ID（回复评论时使用）
     */
    private Long parentCommentId;
} 