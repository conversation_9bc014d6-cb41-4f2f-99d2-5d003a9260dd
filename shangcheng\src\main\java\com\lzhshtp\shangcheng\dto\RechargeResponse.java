package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 充值响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeResponse {
    
    /**
     * 充值订单号
     */
    private String rechargeOrderNo;
    
    /**
     * 充值金额
     */
    private BigDecimal amount;
    
    /**
     * 支付宝支付页面HTML
     */
    private String paymentForm;
    
    /**
     * 支付状态
     */
    private String status;
}
