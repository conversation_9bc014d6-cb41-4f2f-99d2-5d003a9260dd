import request from '@/utils/request'

/**
 * 获取用户所有收货地址
 * @returns {Promise}
 */
export function getUserAddresses() {
  return request({
    url: '/addresses',
    method: 'get'
  })
}

/**
 * 获取收货地址详情
 * @param {number} addressId 地址ID
 * @returns {Promise}
 */
export function getAddressById(addressId) {
  return request({
    url: `/addresses/${addressId}`,
    method: 'get'
  })
}

/**
 * 获取默认收货地址
 * @returns {Promise}
 */
export function getDefaultAddress() {
  return request({
    url: '/addresses/default',
    method: 'get'
  })
}

/**
 * 添加收货地址
 * @param {Object} addressData 地址数据
 * @returns {Promise}
 */
export function addAddress(addressData) {
  return request({
    url: '/addresses',
    method: 'post',
    data: addressData
  })
}

/**
 * 更新收货地址
 * @param {number} addressId 地址ID
 * @param {Object} addressData 地址数据
 * @returns {Promise}
 */
export function updateAddress(addressId, addressData) {
  return request({
    url: `/addresses/${addressId}`,
    method: 'put',
    data: addressData
  })
}

/**
 * 删除收货地址
 * @param {number} addressId 地址ID
 * @returns {Promise}
 */
export function deleteAddress(addressId) {
  return request({
    url: `/addresses/${addressId}`,
    method: 'delete'
  })
}

/**
 * 设置默认收货地址
 * @param {number} addressId 地址ID
 * @returns {Promise}
 */
export function setDefaultAddress(addressId) {
  return request({
    url: `/addresses/${addressId}/default`,
    method: 'put'
  })
}
