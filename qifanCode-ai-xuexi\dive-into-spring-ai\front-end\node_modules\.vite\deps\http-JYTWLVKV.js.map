{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/http.js"], "sourcesContent": ["function failFirstLine(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return \"error\";\n}\n\nfunction start(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d/)) {\n    state.cur = responseStatusCode;\n    return \"keyword\";\n  } else if (stream.match(/^[A-Z]+/) && /[ \\t]/.test(stream.peek())) {\n    state.cur = requestPath;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction responseStatusCode(stream, state) {\n  var code = stream.match(/^\\d+/);\n  if (!code) return failFirstLine(stream, state);\n\n  state.cur = responseStatusText;\n  var status = Number(code[0]);\n  if (status >= 100 && status < 400) {\n    return \"atom\";\n  } else {\n    return \"error\";\n  }\n}\n\nfunction responseStatusText(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return null;\n}\n\nfunction requestPath(stream, state) {\n  stream.eatWhile(/\\S/);\n  state.cur = requestProtocol;\n  return \"string.special\";\n}\n\nfunction requestProtocol(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d$/)) {\n    state.cur = header;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction header(stream) {\n  if (stream.sol() && !stream.eat(/[ \\t]/)) {\n    if (stream.match(/^.*?:/)) {\n      return \"atom\";\n    } else {\n      stream.skipToEnd();\n      return \"error\";\n    }\n  } else {\n    stream.skipToEnd();\n    return \"string\";\n  }\n}\n\nfunction body(stream) {\n  stream.skipToEnd();\n  return null;\n}\n\nexport const http = {\n  name: \"http\",\n  token: function(stream, state) {\n    var cur = state.cur;\n    if (cur != header && cur != body && stream.eatSpace()) return null;\n    return cur(stream, state);\n  },\n\n  blankLine: function(state) {\n    state.cur = body;\n  },\n\n  startState: function() {\n    return {cur: start};\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,cAAc,QAAQ,OAAO;AACpC,SAAO,UAAU;AACjB,QAAM,MAAM;AACZ,SAAO;AACT;AAEA,SAAS,MAAM,QAAQ,OAAO;AAC5B,MAAI,OAAO,MAAM,eAAe,GAAG;AACjC,UAAM,MAAM;AACZ,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,SAAS,KAAK,QAAQ,KAAK,OAAO,KAAK,CAAC,GAAG;AACjE,UAAM,MAAM;AACZ,WAAO;AAAA,EACT,OAAO;AACL,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI,OAAO,OAAO,MAAM,MAAM;AAC9B,MAAI,CAAC;AAAM,WAAO,cAAc,QAAQ,KAAK;AAE7C,QAAM,MAAM;AACZ,MAAI,SAAS,OAAO,KAAK,CAAC,CAAC;AAC3B,MAAI,UAAU,OAAO,SAAS,KAAK;AACjC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,SAAO,UAAU;AACjB,QAAM,MAAM;AACZ,SAAO;AACT;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,SAAO,SAAS,IAAI;AACpB,QAAM,MAAM;AACZ,SAAO;AACT;AAEA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,UAAM,MAAM;AACZ,WAAO;AAAA,EACT,OAAO;AACL,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AACF;AAEA,SAAS,OAAO,QAAQ;AACtB,MAAI,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,OAAO,GAAG;AACxC,QAAI,OAAO,MAAM,OAAO,GAAG;AACzB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,KAAK,QAAQ;AACpB,SAAO,UAAU;AACjB,SAAO;AACT;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS;AAAG,aAAO;AAC9D,WAAO,IAAI,QAAQ,KAAK;AAAA,EAC1B;AAAA,EAEA,WAAW,SAAS,OAAO;AACzB,UAAM,MAAM;AAAA,EACd;AAAA,EAEA,YAAY,WAAW;AACrB,WAAO,EAAC,KAAK,MAAK;AAAA,EACpB;AACF;", "names": []}