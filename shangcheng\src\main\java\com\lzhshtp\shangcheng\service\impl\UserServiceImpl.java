package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.mapper.AdminForcedRefundMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.SmsService;
import com.lzhshtp.shangcheng.utils.JwtUtils;
import com.lzhshtp.shangcheng.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final AdminForcedRefundMapper adminForcedRefundMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final SmsService smsService;

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 认证响应
     */
    @Override
    @Transactional
    public AuthResponse register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            throw new IllegalArgumentException("用户名已存在");
        }

        // 只有当邮箱不为空时才检查是否已存在
        if (StringUtils.hasText(request.getEmail()) && userMapper.findByEmail(request.getEmail()) != null) {
            throw new IllegalArgumentException("邮箱已存在");
        }

        // 只有当手机号不为空时才检查是否已存在
        if (StringUtils.hasText(request.getPhoneNumber()) && userMapper.findByPhoneNumber(request.getPhoneNumber()) != null) {
            throw new IllegalArgumentException("手机号已存在");
        }

        // 创建用户
        User user = User.builder()
                .username(request.getUsername())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .email(request.getEmail()) // email可以为null或空字符串
                .phoneNumber(request.getPhoneNumber()) // phoneNumber可以为null或空字符串
                .bio(request.getBio())
                .location(request.getLocation())
                .registrationDate(LocalDateTime.now())
                .creditScore(100)
                .balance(BigDecimal.ZERO) // 初始余额为0
                .isActive(true)
                .role("general_user")
                .build();

        // 保存用户
        userMapper.insert(user);

        // 生成包含用户ID的JWT令牌
        String token = jwtUtils.generateTokenWithUserId(user.getUsername(), user.getUserId());

        // 返回认证响应
        return AuthResponse.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .token(token)
                .build();
    }

    /**
     * 管理员注册
     *
     * @param request 管理员注册请求
     * @return 认证响应
     */
    @Override
    @Transactional
    public AuthResponse adminRegister(AdminRegisterRequest request) {
        // 检查用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            throw new IllegalArgumentException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) && userMapper.findByEmail(request.getEmail()) != null) {
            throw new IllegalArgumentException("邮箱已存在");
        }

        // 创建管理员用户
        User user = User.builder()
                .username(request.getUsername())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .email(request.getEmail())
                .registrationDate(LocalDateTime.now())
                .creditScore(100)
                .balance(BigDecimal.ZERO) // 初始余额为0
                .isActive(true)
                .role("admin")
                .build();

        // 保存用户
        userMapper.insert(user);

        // 记录管理员注册日志
        log.info("新管理员注册: {}", user.getUsername());

        // 生成包含用户ID的JWT令牌
        String token = jwtUtils.generateTokenWithUserId(user.getUsername(), user.getUserId());

        // 返回认证响应
        return AuthResponse.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .token(token)
                .build();
    }

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 认证响应
     */
    @Override
    public AuthResponse login(LoginRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 检查用户角色，只允许普通用户登录
        if (!"general_user".equals(user.getRole())) {
            throw new IllegalArgumentException("只有普通用户可以登录");
        }
        
        // 检查用户是否被禁用
        if (!user.getIsActive()) {
            throw new IllegalArgumentException("您的账号已被禁用，请联系管理员");
        }

        // 验证密码
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(user.getUsername(), request.getPassword())
        );

        // 更新最后登录时间
        user.setLastLoginDate(LocalDateTime.now());
        userMapper.updateById(user);

        // 生成包含用户ID的JWT令牌
        String token = jwtUtils.generateTokenWithUserId(user.getUsername(), user.getUserId());

        // 返回认证响应
        return AuthResponse.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .token(token)
                .build();
    }

    /**
     * 管理员登录
     *
     * @param request 管理员登录请求
     * @return 认证响应
     */
    @Override
    public AuthResponse adminLogin(AdminLoginRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 检查用户角色，只允许管理员登录
        if (!"admin".equals(user.getRole())) {
            throw new IllegalArgumentException("只有管理员可以登录");
        }

        // 验证密码
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(user.getUsername(), request.getPassword())
        );

        // 更新最后登录时间
        user.setLastLoginDate(LocalDateTime.now());
        userMapper.updateById(user);

        // 生成包含用户ID的JWT令牌
        String token = jwtUtils.generateTokenWithUserId(user.getUsername(), user.getUserId());

        // 返回认证响应
        return AuthResponse.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(user.getRole())
                .token(token)
                .build();
    }

    /**
     * 更新用户信息
     *
     * @param username 用户名
     * @param request 更新请求
     * @return 更新后的用户对象
     */
    @Override
    @Transactional
    public User updateUserInfo(String username, UserUpdateRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 如果要更新用户名，检查新用户名是否已被使用
        if (StringUtils.hasText(request.getUsername()) && !request.getUsername().equals(user.getUsername())) {
            User existingUser = userMapper.findByUsername(request.getUsername());
            if (existingUser != null) {
                throw new IllegalArgumentException("用户名已被使用");
            }
            user.setUsername(request.getUsername());
        }

        // 如果要更新邮箱，检查新邮箱是否已被使用
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            User existingUser = userMapper.findByEmail(request.getEmail());
            if (existingUser != null) {
                throw new IllegalArgumentException("邮箱已被使用");
            }
            user.setEmail(request.getEmail());
        }

        // 更新密码
        if (StringUtils.hasText(request.getPassword())) {
            user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        }

        // 更新个人简介
        if (request.getBio() != null) {
            user.setBio(request.getBio());
        }

        // 更新地区信息
        if (request.getLocation() != null) {
            user.setLocation(request.getLocation());
        }

        // 保存更新
        userMapper.updateById(user);

        return user;
    }

    /**
     * 更新用户头像
     *
     * @param username 用户名
     * @param avatarUrl 头像URL
     * @return 更新后的用户对象
     */
    @Override
    @Transactional
    public User updateAvatar(String username, String avatarUrl) {
        // 查找用户
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 更新头像URL
        user.setAvatarUrl(avatarUrl);

        // 保存更新
        userMapper.updateById(user);

        return user;
    }

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    /**
     * 根据手机号查找用户
     *
     * @param phoneNumber 手机号
     * @return 用户对象
     */
    @Override
    public User findByPhoneNumber(String phoneNumber) {
        return userMapper.findByPhoneNumber(phoneNumber);
    }

    /**
     * 根据ID查找用户
     *
     * @param userId 用户ID
     * @return 用户对象
     */
    @Override
    public User findById(Long userId) {
        return userMapper.selectById(userId);
    }


    /**
     * 根据标识符（用户名、邮箱或手机号）查找用户
     *
     * @param identifier 标识符
     * @return 用户对象
     */
    private User findUserByIdentifier(String identifier) {
        User user = userMapper.findByUsername(identifier);
        if (user != null) {
            return user;
        }

        user = userMapper.findByEmail(identifier);
        if (user != null) {
            return user;
        }

        return userMapper.findByPhoneNumber(identifier);
    }

    /**
     * 获取用户列表（管理员专用）
     *
     * @param params 查询参数
     * @return 分页用户列表
     */
    @Override
    public PageResult<UserDTO> getUserList(UserQueryParams params) {
        // 解析查询参数
        String keyword = params.getKeyword();
        String role = params.getRole();
        Boolean isActive = params.getIsActive();
        int offset = params.getOffset();
        int pageSize = params.getPageSize();
        
        // 尝试将关键字解析为用户ID
        Long userId = null;
        if (keyword != null && !keyword.trim().isEmpty()) {
            try {
                userId = Long.parseLong(keyword);
            } catch (NumberFormatException ignored) {
                // 不是数字，忽略ID查询
            }
        }
        
        // 查询用户列表
        List<UserDTO> users = userMapper.getUserList(keyword, role, isActive, userId, offset, pageSize);
        
        // 查询总记录数
        int total = userMapper.countUsers(keyword, role, isActive, userId);
        
        // 构建分页结果
        return new PageResult<>(users, total, params.getPage(), params.getPageSize());
    }

    /**
     * 获取用户详情（管理员专用）
     *
     * @param userId 用户ID
     * @return 用户详情，包含统计数据
     */
    @Override
    public UserDetailDTO getUserDetail(Long userId) {
        // 获取用户基本信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 获取用户统计数据
        int productCount = userMapper.getProductCount(userId);
        int soldProductCount = userMapper.getSoldProductCount(userId);
        int buyOrderCount = userMapper.getBuyOrderCount(userId);
        int sellOrderCount = userMapper.getSellOrderCount(userId);
        int forumPostCount = userMapper.getForumPostCount(userId);
        int forumCommentCount = userMapper.getForumCommentCount(userId);
        
        // 构建用户详情DTO
        return UserDetailDTO.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRole())
                .isActive(user.getIsActive())
                .registrationDate(user.getRegistrationDate())
                .lastLoginDate(user.getLastLoginDate())
                .creditScore(user.getCreditScore())
                .avatarUrl(user.getAvatarUrl())
                .location(user.getLocation())
                .bio(user.getBio())
                .balance(user.getBalance())
                .productCount(productCount)
                .soldProductCount(soldProductCount)
                .buyOrderCount(buyOrderCount)
                .sellOrderCount(sellOrderCount)
                .forumPostCount(forumPostCount)
                .forumCommentCount(forumCommentCount)
                .build();
    }
    
    /**
     * 更新用户状态（管理员专用）
     *
     * @param request 用户状态更新请求
     * @return 更新后的用户
     */
    @Override
    @Transactional
    public User updateUserStatus(UserStatusUpdateRequest request) {
        // 获取用户
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 不允许修改管理员状态
        if ("admin".equals(user.getRole())) {
            throw new IllegalArgumentException("不允许修改管理员状态");
        }
        
        // 更新用户状态
        user.setIsActive(request.getIsActive());
        userMapper.updateById(user);
        
        // 返回更新后的用户
        return user;
    }
    
    /**
     * 批量更新用户状态（管理员专用）
     *
     * @param request 批量用户状态更新请求
     * @return 更新的用户数量
     */
    @Override
    @Transactional
    public int batchUpdateUserStatus(BatchUserStatusUpdateRequest request) {
        // 批量更新用户状态
        return userMapper.batchUpdateUserStatus(request.getUserIds(), request.getIsActive());
    }

    /**
     * 获取管理员用户列表（管理员专用）
     *
     * @param params 查询参数
     * @return 分页管理员用户列表
     */
    @Override
    public PageResult<UserDTO> getAdminList(UserQueryParams params) {
        // 解析查询参数
        String keyword = params.getKeyword();
        Boolean isActive = params.getIsActive();
        int offset = params.getOffset();
        int pageSize = params.getPageSize();

        // 尝试将关键字解析为用户ID
        Long userId = null;
        if (keyword != null && !keyword.trim().isEmpty()) {
            try {
                userId = Long.parseLong(keyword);
            } catch (NumberFormatException ignored) {
                // 不是数字，忽略ID查询
            }
        }

        // 查询管理员列表（使用专门的方法，不排除admin）
        List<UserDTO> admins = userMapper.getAdminList(keyword, isActive, userId, offset, pageSize);

        // 查询管理员总记录数
        int total = userMapper.countAdmins(keyword, isActive, userId);

        // 构建分页结果
        return new PageResult<>(admins, total, params.getPage(), params.getPageSize());
    }

    /**
     * 删除管理员（管理员专用）
     *
     * @param userId 要删除的管理员用户ID
     * @return 是否删除成功
     * @throws IllegalArgumentException 如果用户不存在或不是管理员
     */
    @Override
    @Transactional
    public boolean deleteAdmin(Long userId) {
        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 检查用户角色，只允许删除管理员
        if (!"admin".equals(user.getRole())) {
            throw new IllegalArgumentException("只能删除管理员账号");
        }
        
        // 记录管理员删除日志
        log.info("删除管理员: {}, ID: {}", user.getUsername(), userId);
        
        // 从数据库删除
        int result = userMapper.deleteById(userId);
        return result > 0;
    }
    
    /**
     * 更新管理员信息（管理员专用）
     *
     * @param userId 要更新的管理员用户ID
     * @param request 管理员更新请求
     * @return 更新后的用户对象
     * @throws IllegalArgumentException 如果用户不存在或不是管理员
     */
    @Override
    @Transactional
    public User updateAdmin(Long userId, AdminUpdateRequest request) {
        // 查找用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 检查用户角色，只允许更新管理员
        if (!"admin".equals(user.getRole())) {
            throw new IllegalArgumentException("只能更新管理员账号");
        }
        
        // 如果要更新用户名，检查新用户名是否已被使用
        if (StringUtils.hasText(request.getUsername()) && !request.getUsername().equals(user.getUsername())) {
            User existingUser = userMapper.findByUsername(request.getUsername());
            if (existingUser != null) {
                throw new IllegalArgumentException("用户名已被使用");
            }
            user.setUsername(request.getUsername());
        }
        
        // 如果要更新邮箱，检查新邮箱是否已被使用
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            User existingUser = userMapper.findByEmail(request.getEmail());
            if (existingUser != null) {
                throw new IllegalArgumentException("邮箱已被使用");
            }
            user.setEmail(request.getEmail());
        }
        
        // 如果要更新密码
        if (StringUtils.hasText(request.getPassword())) {
            user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        }
        
        // 记录管理员更新日志
        log.info("更新管理员信息: {}, ID: {}", user.getUsername(), userId);
        
        // 保存更新
        userMapper.updateById(user);
        
        return user;
    }

    /**
     * 绑定手机号
     *
     * @param userId 用户ID
     * @param request 绑定手机号请求
     * @return 是否绑定成功
     */
    @Override
    public boolean bindPhone(Long userId, BindPhoneRequest request) {
        try {
            log.info("用户 {} 尝试绑定手机号: {}", userId, request.getPhone());

            // 1. 验证验证码
            boolean codeValid = smsService.verifyCode(request.getPhone(), request.getCode(), "bind");
            if (!codeValid) {
                log.warn("用户 {} 绑定手机号失败：验证码错误", userId);
                return false;
            }

            // 2. 检查手机号是否已被其他用户绑定
            User existingUser = userMapper.findByPhoneNumber(request.getPhone());
            if (existingUser != null && !existingUser.getUserId().equals(userId)) {
                log.warn("用户 {} 绑定手机号失败：手机号 {} 已被其他用户绑定", userId, request.getPhone());
                return false;
            }

            // 3. 获取当前用户
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户 {} 不存在", userId);
                return false;
            }

            // 4. 更新用户手机号
            user.setPhoneNumber(request.getPhone());
            userMapper.updateById(user);

            log.info("用户 {} 成功绑定手机号: {}", userId, request.getPhone());
            return true;

        } catch (Exception e) {
            log.error("用户 {} 绑定手机号异常", userId, e);
            return false;
        }
    }

    /**
     * 短信验证码登录
     *
     * @param request 短信登录请求
     * @return 认证响应
     */
    @Override
    public AuthResponse smsLogin(SmsLoginRequest request) {
        try {
            log.info("手机号 {} 尝试短信验证码登录", request.getPhone());

            // 1. 验证验证码
            boolean codeValid = smsService.verifyCode(request.getPhone(), request.getCode(), "login");
            if (!codeValid) {
                log.warn("手机号 {} 登录失败：验证码错误", request.getPhone());
                throw new IllegalArgumentException("验证码错误或已过期");
            }

            // 2. 根据手机号查找用户
            User user = userMapper.findByPhoneNumber(request.getPhone());
            if (user == null) {
                log.warn("手机号 {} 登录失败：用户不存在", request.getPhone());
                throw new IllegalArgumentException("该手机号未绑定用户");
            }

            // 3. 检查用户状态
            if (!user.getIsActive()) {
                log.warn("手机号 {} 登录失败：用户已被禁用", request.getPhone());
                throw new IllegalArgumentException("用户已被禁用");
            }

            // 4. 更新最后登录时间
            user.setLastLoginDate(LocalDateTime.now());
            userMapper.updateById(user);

            // 5. 生成JWT Token
            String token = jwtUtils.generateTokenWithUserId(user.getUsername(), user.getUserId());

            log.info("手机号 {} 登录成功，用户: {}", request.getPhone(), user.getUsername());

            // 6. 返回认证响应
            return AuthResponse.builder()
                    .userId(user.getUserId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .role(user.getRole())
                    .token(token)
                    .build();

        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            log.error("手机号 {} 登录异常", request.getPhone(), e);
            throw new RuntimeException("登录失败，请稍后重试");
        }
    }

    /**
     * 检查用户是否可以发布商品
     */
    @Override
    public boolean canPublishProduct(String username) {
        try {
            User user = userMapper.findByUsername(username);
            if (user == null) {
                log.warn("用户不存在：{}", username);
                return false;
            }

            // 检查用户是否被禁止上架商品
            Boolean canPublish = user.getCanPublishProduct();
            if (canPublish == null || !canPublish) {
                log.info("用户 {} 被禁止发布商品", username);
                return false;
            }

            // 暂时跳过强制退款检查，避免数据库问题
            // TODO: 后续可以重新启用强制退款检查
            // int pendingCount = adminForcedRefundMapper.countPendingBySellerId(user.getUserId());
            // return pendingCount == 0;

            return true;
        } catch (Exception e) {
            log.error("检查用户发布商品权限失败，用户名：{}", username, e);
            return false;
        }
    }
}
