<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帖子详情 - 二手交易平台论坛</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #e53935;
            --dark-red: #c62828;
            --light-grey: #f0f2f5;
            --medium-grey: #e8e8e8;
            --dark-grey: #333;
            --text-color-medium: #555;
            --text-color-light: #777;
            --white: #ffffff;
            --border-color: #eee;
            --shadow-light: rgba(0,0,0,0.05);
            --shadow-medium: rgba(0,0,0,0.1);
            --shadow-dark: rgba(0,0,0,0.15);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--light-grey);
            margin: 0;
            padding: 0;
            color: var(--dark-grey);
            line-height: 1.6;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        /* Top Header - Reused from forum.html */
        .top-header {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 10px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px var(--shadow-medium);
        }
        .top-header .logo {
            font-size: 24px;
            font-weight: 700;
        }
        .top-header .search-box {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 600px;
            margin: 0 20px;
        }
        .top-header .search-box input {
            flex-grow: 1;
            max-width: 400px;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            margin-right: 10px;
            background-color: rgba(255,255,255,0.2);
            color: var(--white);
            outline: none;
            transition: background-color 0.3s ease;
        }
        .top-header .search-box input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        .top-header .search-box input:focus {
            background-color: rgba(255,255,255,0.3);
        }
        .top-header .search-box button {
            background-color: var(--dark-red);
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            color: var(--white);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .top-header .search-box button:hover {
            background-color: #a01a1a;
        }

        .top-nav-right {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .top-nav-right .nav-icon-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--white);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s ease;
        }
        .top-nav-right .nav-icon-link:hover {
            color: rgba(255,255,255,0.8);
        }

        /* Main Content Wrapper */
        .main-wrapper {
            max-width: 900px; /* Wider for detail content */
            margin: 30px auto;
            padding: 0 20px;
        }

        .post-detail-card {
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            padding: 30px;
            margin-bottom: 30px;
        }

        .post-detail-card .post-title {
            font-size: 2em;
            font-weight: 700;
            color: var(--dark-grey);
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .post-detail-card .post-meta {
            font-size: 0.9em;
            color: var(--text-color-light);
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 15px;
        }
        .post-detail-card .post-meta span {
            margin-right: 15px;
        }
        .post-detail-card .post-meta a {
            color: var(--primary-red);
            font-weight: 500;
        }

        .post-detail-card .post-content {
            font-size: 1.05em;
            color: var(--text-color-medium);
            line-height: 1.8;
            margin-bottom: 30px;
        }
        .post-detail-card .post-content p {
            margin-bottom: 1em;
        }
        .post-detail-card .post-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 15px 0;
            display: block;
        }

        /* Replies Section */
        .replies-section {
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 15px var(--shadow-light);
            padding: 30px;
        }

        .replies-section h2 {
            font-size: 1.6em;
            font-weight: 600;
            color: var(--dark-grey);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--medium-grey);
        }

        .reply-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .reply-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px dashed var(--border-color);
        }
        .reply-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .reply-item img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
            border: 1px solid var(--medium-grey);
        }

        .reply-content {
            flex-grow: 1;
        }

        .reply-author {
            font-weight: 500;
            color: var(--primary-red);
            font-size: 1.05em;
            margin-bottom: 4px;
        }

        .reply-text {
            font-size: 0.95em;
            color: var(--text-color-medium);
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .reply-meta {
            font-size: 0.8em;
            color: var(--text-color-light);
        }

        .reply-input-area {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid var(--medium-grey);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .reply-input-area h3 {
            font-size: 1.2em;
            font-weight: 600;
            color: var(--dark-grey);
            margin-bottom: 5px;
        }
        .reply-input-area textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Noto Sans SC', sans-serif;
            font-size: 1em;
            min-height: 100px;
            resize: vertical;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .reply-input-area textarea:focus {
            border-color: var(--primary-red);
        }
        .reply-input-area button {
            align-self: flex-end;
            background-color: var(--primary-red);
            color: var(--white);
            padding: 10px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        .reply-input-area button:hover {
            background-color: var(--dark-red);
            transform: translateY(-1px);
        }

        /* 客服聊天窗口 HTML - Reused from forum.html */
        .chat-window {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            height: 400px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 8px 30px var(--shadow-medium);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            transform: translateY(100%) scale(0.8);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .chat-window.open {
            transform: translateY(0) scale(1);
            opacity: 1;
            visibility: visible;
        }

        .chat-header {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1em;
            font-weight: 600;
            border-bottom: 1px solid var(--dark-red);
        }

        .chat-header h3 {
            margin: 0;
            color: var(--white);
        }

        .chat-header .close-chat-btn {
            background: none;
            border: none;
            color: var(--white);
            font-size: 1.5em;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .chat-header .close-chat-btn:hover {
            color: rgba(255,255,255,0.8);
        }

        .chat-body {
            flex-grow: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: var(--light-grey);
            display: flex;
            flex-direction: column;
        }

        .message {
            margin-bottom: 10px;
            display: flex;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message.received {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 75%;
            padding: 10px 15px;
            border-radius: 18px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.sent .message-bubble {
            background-color: var(--primary-red);
            color: var(--white);
            border-bottom-right-radius: 5px;
        }

        .message.received .message-bubble {
            background-color: var(--white);
            color: var(--dark-grey);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            display: flex;
            padding: 10px 15px;
            border-top: 1px solid var(--border-color);
            background-color: var(--white);
        }

        .chat-input input {
            flex-grow: 1;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 0.95em;
            margin-right: 10px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .chat-input input:focus {
            border-color: var(--primary-red);
        }

        .chat-input button {
            background-color: var(--primary-red);
            color: var(--white);
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 0.95em;
            transition: background-color 0.2s ease;
        }

        .chat-input button:hover {
            background-color: var(--dark-red);
        }
    </style>
</head>
<body>
    <div class="top-header">
        <a href="main.html" class="logo">二手交易</a>
        <div class="search-box">
            <input type="text" placeholder="输入关键词">
            <button>搜索</button>
        </div>
        <div class="top-nav-right">
            <a href="#" class="nav-icon-link" id="customerServiceLink">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 15C21 16.6569 19.6569 18 18 18H7.993C7.69741 18 7.41117 18.1215 7.20017 18.3475L4 22V5C4 3.34315 5.34315 2 7 2H17C18.6569 2 20 3.34315 20 5V15H21ZM12 8C12.5523 8 13 8.44772 13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8ZM12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11ZM12 14C12.5523 14 13 14.4477 13 15C13 15.5522 12.5523 16 12 16C11.4477 16 11 15.5522 11 15C11 14.4477 11.4477 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>客服</span>
            </a>
        </div>
    </div>

    <div class="main-wrapper">
        <div class="post-detail-card">
            <h1 class="post-title">求购一台八成新iPad Pro，预算3000元左右，面交</h1>
            <div class="post-meta">
                <span>发布者: <a href="#">小明</a></span>
                <span>发布时间: 2小时前</span>
                <span>分类: <a href="#">数码产品</a></span>
            </div>
            <div class="post-content">
                <p>如题，本人学生党，想求一台八成新左右的iPad Pro，不限型号，但希望屏幕和电池健康度良好，没有大的磕碰划痕。预算大概在3000元左右，如果成色特别好或者带Apple Pencil可以适当加钱。</p>
                <p>最好是同城面交（上海），可以当场验货。希望卖家能提供购买凭证或者序列号方便查询。有闲置的可以私信我，或者直接在评论区留言，我会尽快回复！谢谢大家！</p>
                <img src="https://via.placeholder.com/600x400/FFDDC1/A0522D?text=iPad+Pro" alt="iPad Pro示例图片">
                <p>Ps: 对iPad Air或者其他平板有好的推荐也可以留言！</p>
            </div>
        </div>

        <div class="replies-section">
            <h2>全部回复 (12)</h2>
            <ul class="reply-list">
                <li class="reply-item">
                    <img src="https://via.placeholder.com/40/FFD700" alt="用户头像">
                    <div class="reply-content">
                        <div class="reply-author">数码爱好者</div>
                        <p class="reply-text">我有台2018款iPad Pro 11寸，256G，一直贴膜带壳，成色很新，电池健康度90%以上，带二代Pencil，要不要考虑下？价格可以再聊。</p>
                        <div class="reply-meta">1小时前</div>
                    </div>
                </li>
                <li class="reply-item">
                    <img src="https://via.placeholder.com/40/ADD8E6" alt="用户头像">
                    <div class="reply-content">
                        <div class="reply-author">小王同学</div>
                        <p class="reply-text">同求，我也是学生，预算有限，希望能在论坛找到靠谱的二手平板。</p>
                        <div class="reply-meta">50分钟前</div>
                    </div>
                </li>
                <li class="reply-item">
                    <img src="https://via.placeholder.com/40/90EE90" alt="用户头像">
                    <div class="reply-content">
                        <div class="reply-author">Apple达人</div>
                        <p class="reply-text">建议找2020款11寸的，性价比高，性能也够用。注意检查屏幕有没有坏点和白斑。</p>
                        <div class="reply-meta">30分钟前</div>
                    </div>
                </li>
                <li class="reply-item">
                    <img src="https://via.placeholder.com/40/DDA0DD" alt="用户头像">
                    <div class="reply-content">
                        <div class="reply-author">上海老乡</div>
                        <p class="reply-text">我在闵行，有台iPad Air 4，带键盘套，去年买的，你要是考虑Air也可以看看。</p>
                        <div class="reply-meta">15分钟前</div>
                    </div>
                </li>
            </ul>

            <div class="reply-input-area">
                <h3>发表回复</h3>
                <textarea placeholder="写下你的回复..."></textarea>
                <button>提交回复</button>
            </div>
        </div>
    </div>

    <!-- 客服聊天窗口 HTML -->
    <div id="chat-window" class="chat-window">
        <div class="chat-header">
            <h3>客服中心</h3>
            <button class="close-chat-btn">&times;</button>
        </div>
        <div class="chat-body" id="chat-body">
            <!-- 聊天消息会在这里显示 -->
            <div class="message received">
                <div class="message-bubble">您好，有什么可以帮助您的？</div>
            </div>
            <div class="message sent">
                <div class="message-bubble">我想咨询一下关于帖子发布的问题。</div>
            </div>
        </div>
        <div class="chat-input">
            <input type="text" id="chat-message-input" placeholder="输入消息...">
            <button id="send-message-btn">发送</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const customerServiceLink = document.getElementById('customerServiceLink');
            const chatWindow = document.getElementById('chat-window');
            const closeChatBtn = document.querySelector('.close-chat-btn');
            const chatBody = document.getElementById('chat-body');
            const chatMessageInput = document.getElementById('chat-message-input');
            const sendMessageBtn = document.getElementById('send-message-btn');

            customerServiceLink.addEventListener('click', function(e) {
                e.preventDefault();
                chatWindow.classList.toggle('open');
                if (chatWindow.classList.contains('open')) {
                    chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部
                }
            });

            closeChatBtn.addEventListener('click', function() {
                chatWindow.classList.remove('open');
            });

            sendMessageBtn.addEventListener('click', sendMessage);
            chatMessageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            function sendMessage() {
                const messageText = chatMessageInput.value.trim();
                if (messageText !== '') {
                    const messageElement = document.createElement('div');
                    messageElement.classList.add('message', 'sent');
                    messageElement.innerHTML = `<div class="message-bubble">${messageText}</div>`;
                    chatBody.appendChild(messageElement);
                    chatMessageInput.value = '';
                    chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部

                    // 模拟客服回复
                    setTimeout(() => {
                        const replyElement = document.createElement('div');
                        replyElement.classList.add('message', 'received');
                        replyElement.innerHTML = `<div class="message-bubble">您的问题已收到，请稍候，客服将尽快回复。</div>`;
                        chatBody.appendChild(replyElement);
                        chatBody.scrollTop = chatBody.scrollHeight; // 滚动到底部
                    }, 1000);
                }
            }
        });
    </script>
</body>
</html> 