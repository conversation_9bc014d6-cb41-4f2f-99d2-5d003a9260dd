package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 商家评价实体类
 * 对应数据库表：tb_lzhshtp_seller_reviews
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_seller_reviews")
public class SellerReview {

    /**
     * 评价记录唯一标识ID
     */
    @TableId(value = "lzhshtp_review_id", type = IdType.AUTO)
    private Long reviewId;

    /**
     * 订单ID，确保一个订单只能评价一次
     */
    @TableField("lzhshtp_order_id")
    private Long orderId;

    /**
     * 买家用户ID
     */
    @TableField("lzhshtp_buyer_id")
    private Long buyerId;

    /**
     * 卖家用户ID
     */
    @TableField("lzhshtp_seller_id")
    private Long sellerId;

    /**
     * 商品ID
     */
    @TableField("lzhshtp_product_id")
    private Long productId;

    /**
     * 评分：1-5星
     */
    @TableField("lzhshtp_rating")
    private Integer rating;

    /**
     * 评价内容（可选）
     */
    @TableField("lzhshtp_review_content")
    private String reviewContent;

    /**
     * 评价标签（JSON格式）
     */
    @TableField("lzhshtp_review_tags")
    private String reviewTags;

    /**
     * 是否匿名评价
     */
    @TableField("lzhshtp_is_anonymous")
    private Boolean isAnonymous;

    /**
     * 此次评价导致的信用分变化
     */
    @TableField("lzhshtp_credit_score_change")
    private Integer creditScoreChange;

    /**
     * 评价前卖家信用分
     */
    @TableField("lzhshtp_seller_credit_before")
    private Integer sellerCreditBefore;

    /**
     * 评价后卖家信用分
     */
    @TableField("lzhshtp_seller_credit_after")
    private Integer sellerCreditAfter;

    /**
     * 评价时间
     */
    @TableField(value = "lzhshtp_created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "lzhshtp_updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 评价状态枚举
     */
    public enum ReviewStatus {
        NORMAL("normal", "正常"),
        DISPUTED("disputed", "有争议"),
        INVALID("invalid", "无效");

        private final String code;
        private final String description;

        ReviewStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
