import axios from 'axios'

// 请求拦截器配置
axios.interceptors.request.use(config => {
  const adminToken = localStorage.getItem('adminToken')
  if (adminToken) {
    config.headers.Authorization = `Bearer ${adminToken}`
  }
  return config
})

// 获取管理员列表
export const getAdminList = (params) => {
  return axios.get('/api/users/admin/admins', {
    params: {
      ...params,
      role: 'admin'  // 指定只查询管理员角色
    }
  })
}

// 获取管理员详情
export const getAdminDetail = (userId) => {
  return axios.get(`/api/users/admin/users/${userId}`)
}

// 创建新管理员
export const createAdmin = (adminData) => {
  return axios.post('/api/auth/admin/register', adminData)
}

// 更新管理员信息
export const updateAdmin = (userId, adminData) => {
  return axios.put(`/api/users/admins/${userId}`, adminData)
}

// 删除管理员
export const deleteAdmin = (userId) => {
  return axios.delete(`/api/users/admins/${userId}`)
}

// 更新管理员状态（启用/禁用）
export const updateAdminStatus = (request) => {
  return axios.put('/api/users/admin/users/status', request)
}

// 批量更新管理员状态
export const batchUpdateAdminStatus = (request) => {
  return axios.put('/api/users/admin/users/batch-status', request)
}
