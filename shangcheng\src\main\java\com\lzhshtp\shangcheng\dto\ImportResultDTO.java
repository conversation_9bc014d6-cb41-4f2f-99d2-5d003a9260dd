package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量导入结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportResultDTO {
    
    /**
     * 总处理数量
     */
    private int totalCount;
    
    /**
     * 成功导入数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failCount;
    
    /**
     * 错误信息列表
     */
    private List<String> errorMessages;
    
    /**
     * 成功导入的数据ID列表
     */
    private List<Long> successIds;
    
    /**
     * 导入类型（user/category等）
     */
    private String importType;
    
    /**
     * 导入开始时间
     */
    private String startTime;
    
    /**
     * 导入结束时间
     */
    private String endTime;
    
    /**
     * 导入耗时（毫秒）
     */
    private long duration;
}
