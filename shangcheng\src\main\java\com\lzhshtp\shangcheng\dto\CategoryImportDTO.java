package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 分类批量导入DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CategoryImportDTO {
    
    /**
     * 分类名称（必填）
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 父级分类名称
     */
    private String parentCategoryName;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 行号（用于错误提示）
     */
    private int rowNumber;
}
