package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.RefundRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

import java.util.List;

/**
 * 退款申请Mapper接口
 */
@Mapper
public interface RefundRequestMapper extends BaseMapper<RefundRequest> {
    
    /**
     * 根据订单ID查询退款申请
     */
    @Select("SELECT " +
            "lzhshtp_refund_id, lzhshtp_order_id, lzhshtp_buyer_id, lzhshtp_seller_id, " +
            "lzhshtp_refund_amount, lzhshtp_refund_reason, lzhshtp_refund_type, lzhshtp_status, " +
            "lzhshtp_seller_response, lzhshtp_admin_response, lzhshtp_evidence_urls, " +
            "lzhshtp_created_time, lzhshtp_updated_time " +
            "FROM tb_lzhshtp_refund_requests WHERE lzhshtp_order_id = #{orderId}")
    @Results({
        @Result(column = "lzhshtp_refund_id", property = "refundId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_buyer_id", property = "buyerId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_refund_amount", property = "refundAmount"),
        @Result(column = "lzhshtp_refund_reason", property = "refundReason"),
        @Result(column = "lzhshtp_refund_type", property = "refundType"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_seller_response", property = "sellerResponse"),
        @Result(column = "lzhshtp_admin_response", property = "adminResponse"),
        @Result(column = "lzhshtp_evidence_urls", property = "evidenceUrls"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_updated_time", property = "updatedTime")
    })
    RefundRequest selectByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据买家ID查询退款申请列表
     */
    @Select("SELECT " +
            "lzhshtp_refund_id, lzhshtp_order_id, lzhshtp_buyer_id, lzhshtp_seller_id, " +
            "lzhshtp_refund_amount, lzhshtp_refund_reason, lzhshtp_refund_type, lzhshtp_status, " +
            "lzhshtp_seller_response, lzhshtp_admin_response, lzhshtp_evidence_urls, " +
            "lzhshtp_created_time, lzhshtp_updated_time " +
            "FROM tb_lzhshtp_refund_requests WHERE lzhshtp_buyer_id = #{buyerId} ORDER BY lzhshtp_created_time DESC")
    @Results({
        @Result(column = "lzhshtp_refund_id", property = "refundId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_buyer_id", property = "buyerId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_refund_amount", property = "refundAmount"),
        @Result(column = "lzhshtp_refund_reason", property = "refundReason"),
        @Result(column = "lzhshtp_refund_type", property = "refundType"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_seller_response", property = "sellerResponse"),
        @Result(column = "lzhshtp_admin_response", property = "adminResponse"),
        @Result(column = "lzhshtp_evidence_urls", property = "evidenceUrls"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_updated_time", property = "updatedTime")
    })
    List<RefundRequest> selectByBuyerId(@Param("buyerId") Long buyerId);
    
    /**
     * 根据卖家ID查询退款申请列表
     */
    @Select("SELECT " +
            "lzhshtp_refund_id, lzhshtp_order_id, lzhshtp_buyer_id, lzhshtp_seller_id, " +
            "lzhshtp_refund_amount, lzhshtp_refund_reason, lzhshtp_refund_type, lzhshtp_status, " +
            "lzhshtp_seller_response, lzhshtp_admin_response, lzhshtp_evidence_urls, " +
            "lzhshtp_created_time, lzhshtp_updated_time " +
            "FROM tb_lzhshtp_refund_requests WHERE lzhshtp_seller_id = #{sellerId} ORDER BY lzhshtp_created_time DESC")
    @Results({
        @Result(column = "lzhshtp_refund_id", property = "refundId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_buyer_id", property = "buyerId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_refund_amount", property = "refundAmount"),
        @Result(column = "lzhshtp_refund_reason", property = "refundReason"),
        @Result(column = "lzhshtp_refund_type", property = "refundType"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_seller_response", property = "sellerResponse"),
        @Result(column = "lzhshtp_admin_response", property = "adminResponse"),
        @Result(column = "lzhshtp_evidence_urls", property = "evidenceUrls"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_updated_time", property = "updatedTime")
    })
    List<RefundRequest> selectBySellerId(@Param("sellerId") Long sellerId);
    
    /**
     * 查询待管理员处理的退款申请
     */
    @Select("SELECT " +
            "lzhshtp_refund_id, lzhshtp_order_id, lzhshtp_buyer_id, lzhshtp_seller_id, " +
            "lzhshtp_refund_amount, lzhshtp_refund_reason, lzhshtp_refund_type, lzhshtp_status, " +
            "lzhshtp_seller_response, lzhshtp_admin_response, lzhshtp_evidence_urls, " +
            "lzhshtp_created_time, lzhshtp_updated_time " +
            "FROM tb_lzhshtp_refund_requests WHERE lzhshtp_status = 'pending_admin' ORDER BY lzhshtp_created_time ASC")
    @Results({
        @Result(column = "lzhshtp_refund_id", property = "refundId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_buyer_id", property = "buyerId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_refund_amount", property = "refundAmount"),
        @Result(column = "lzhshtp_refund_reason", property = "refundReason"),
        @Result(column = "lzhshtp_refund_type", property = "refundType"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_seller_response", property = "sellerResponse"),
        @Result(column = "lzhshtp_admin_response", property = "adminResponse"),
        @Result(column = "lzhshtp_evidence_urls", property = "evidenceUrls"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_updated_time", property = "updatedTime")
    })
    List<RefundRequest> selectPendingAdminRefunds();
}
