-- 测试评价数据插入脚本
-- 注意：请根据实际的用户ID、订单ID、商品ID来调整这些数据

-- 插入测试评价数据
INSERT INTO tb_lzhshtp_seller_reviews (
    lzhshtp_order_id,
    lzhshtp_buyer_id,
    lzhshtp_seller_id,
    lzhshtp_product_id,
    lzhshtp_rating,
    lzhshtp_review_content,
    lzhshtp_review_tags,
    lzhshtp_is_anonymous,
    lzhshtp_credit_score_change,
    lzhshtp_seller_credit_before,
    lzhshtp_seller_credit_after,
    lzhshtp_created_time
) VALUES 
-- 评价1：5星好评
(1001, 2, 1, 101, 5, '商品质量很好，卖家服务态度也很棒，发货速度很快，包装也很仔细，非常满意！', 
 '["发货快", "质量好", "服务态度好"]', false, 2, 98, 100, '2024-01-15 10:30:00'),

-- 评价2：4星好评
(1002, 3, 1, 102, 4, '整体还不错，商品和描述基本一致，就是物流稍微慢了一点。', 
 '["商品描述准确", "性价比高"]', false, 1, 97, 98, '2024-01-12 14:20:00'),

-- 评价3：5星好评
(1003, 4, 1, 103, 5, '非常棒的购物体验！商品完全符合预期，卖家人很好，推荐！', 
 '["推荐购买", "会再次购买", "质量好"]', false, 2, 95, 97, '2024-01-10 09:15:00'),

-- 评价4：3星中评
(1004, 5, 1, 104, 3, '商品一般般，价格还可以接受。', 
 '["性价比高"]', true, 0, 95, 95, '2024-01-08 16:45:00'),

-- 评价5：4星好评
(1005, 6, 1, 105, 4, '卖家很负责任，商品有小瑕疵但及时处理了，整体满意。', 
 '["服务态度好", "包装好"]', false, 1, 94, 95, '2024-01-05 11:30:00'),

-- 评价6：2星差评
(1006, 7, 1, 106, 2, '商品质量不太好，和描述不符，有点失望。', 
 '[]', false, -2, 96, 94, '2024-01-03 08:20:00'),

-- 评价7：5星好评
(1007, 8, 1, 107, 5, '超级满意！质量超出预期，卖家人很好，强烈推荐！', 
 '["质量好", "推荐购买", "服务态度好", "发货快"]', false, 2, 94, 96, '2024-01-01 15:45:00');

-- 插入对应的信用分变更日志
INSERT INTO tb_lzhshtp_credit_score_logs (
    lzhshtp_user_id,
    lzhshtp_change_type,
    lzhshtp_change_reason,
    lzhshtp_score_before,
    lzhshtp_score_after,
    lzhshtp_score_change,
    lzhshtp_related_order_id,
    lzhshtp_related_review_id,
    lzhshtp_operator_id,
    lzhshtp_created_time
) VALUES 
(1, 'review', '买家评价：5星', 98, 100, 2, 1001, 1, 2, '2024-01-15 10:30:00'),
(1, 'review', '买家评价：4星', 97, 98, 1, 1002, 2, 3, '2024-01-12 14:20:00'),
(1, 'review', '买家评价：5星', 95, 97, 2, 1003, 3, 4, '2024-01-10 09:15:00'),
(1, 'review', '买家评价：3星', 95, 95, 0, 1004, 4, 5, '2024-01-08 16:45:00'),
(1, 'review', '买家评价：4星', 94, 95, 1, 1005, 5, 6, '2024-01-05 11:30:00'),
(1, 'review', '买家评价：2星', 96, 94, -2, 1006, 6, 7, '2024-01-03 08:20:00'),
(1, 'review', '买家评价：5星', 94, 96, 2, 1007, 7, 8, '2024-01-01 15:45:00');

-- 更新卖家的信用分（假设卖家ID为1）
UPDATE tb_lzhshtp_users SET lzhshtp_credit_score = 100 WHERE lzhshtp_user_id = 1;

-- 查询验证数据
SELECT '=== 评价数据 ===' as info;
SELECT 
    r.lzhshtp_review_id,
    r.lzhshtp_order_id,
    u.lzhshtp_username as buyer_name,
    r.lzhshtp_rating,
    r.lzhshtp_review_content,
    r.lzhshtp_review_tags,
    r.lzhshtp_is_anonymous,
    r.lzhshtp_created_time
FROM tb_lzhshtp_seller_reviews r
LEFT JOIN tb_lzhshtp_users u ON r.lzhshtp_buyer_id = u.lzhshtp_user_id
WHERE r.lzhshtp_seller_id = 1
ORDER BY r.lzhshtp_created_time DESC;

SELECT '=== 信用分变更日志 ===' as info;
SELECT 
    l.lzhshtp_log_id,
    l.lzhshtp_change_type,
    l.lzhshtp_change_reason,
    l.lzhshtp_score_before,
    l.lzhshtp_score_after,
    l.lzhshtp_score_change,
    l.lzhshtp_created_time
FROM tb_lzhshtp_credit_score_logs l
WHERE l.lzhshtp_user_id = 1
ORDER BY l.lzhshtp_created_time DESC;

SELECT '=== 卖家当前信用分 ===' as info;
SELECT 
    lzhshtp_user_id,
    lzhshtp_username,
    lzhshtp_credit_score
FROM tb_lzhshtp_users 
WHERE lzhshtp_user_id = 1;
