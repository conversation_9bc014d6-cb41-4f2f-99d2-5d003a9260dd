package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.CreditScoreLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 信用分变更日志Mapper接口
 */
@Mapper
public interface CreditScoreLogMapper extends BaseMapper<CreditScoreLog> {

    /**
     * 获取用户的信用分变更历史
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 变更记录列表
     */
    @Select("SELECT csl.*, " +
            "u1.lzhshtp_username as userName, " +
            "u2.lzhshtp_username as operatorName, " +
            "o.lzhshtp_order_id as orderNumber, " +
            "sr.lzhshtp_rating as reviewRating " +
            "FROM tb_lzhshtp_credit_score_logs csl " +
            "LEFT JOIN tb_lzhshtp_users u1 ON csl.lzhshtp_user_id = u1.lzhshtp_user_id " +
            "LEFT JOIN tb_lzhshtp_users u2 ON csl.lzhshtp_operator_id = u2.lzhshtp_user_id " +
            "LEFT JOIN tb_lzhshtp_orders o ON csl.lzhshtp_related_order_id = o.lzhshtp_order_id " +
            "LEFT JOIN tb_lzhshtp_seller_reviews sr ON csl.lzhshtp_related_review_id = sr.lzhshtp_review_id " +
            "WHERE csl.lzhshtp_user_id = #{userId} " +
            "ORDER BY csl.lzhshtp_created_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserCreditHistory(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户最近N天的信用分变化统计
     * @param userId 用户ID
     * @param days 天数
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalChanges, " +
            "SUM(CASE WHEN lzhshtp_score_change > 0 THEN lzhshtp_score_change ELSE 0 END) as totalGain, " +
            "SUM(CASE WHEN lzhshtp_score_change < 0 THEN ABS(lzhshtp_score_change) ELSE 0 END) as totalLoss, " +
            "SUM(lzhshtp_score_change) as netChange " +
            "FROM tb_lzhshtp_credit_score_logs " +
            "WHERE lzhshtp_user_id = #{userId} " +
            "AND lzhshtp_created_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    Map<String, Object> getUserCreditStatistics(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 获取系统信用分变更统计（管理员用）
     * @param days 天数
     * @return 统计信息
     */
    @Select("SELECT " +
            "lzhshtp_change_type as changeType, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN lzhshtp_score_change > 0 THEN lzhshtp_score_change ELSE 0 END) as totalGain, " +
            "SUM(CASE WHEN lzhshtp_score_change < 0 THEN ABS(lzhshtp_score_change) ELSE 0 END) as totalLoss " +
            "FROM tb_lzhshtp_credit_score_logs " +
            "WHERE lzhshtp_created_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY lzhshtp_change_type")
    List<Map<String, Object>> getSystemCreditStatistics(@Param("days") Integer days);

    /**
     * 获取特定类型的信用分变更记录
     * @param changeType 变更类型
     * @param limit 限制数量
     * @return 变更记录列表
     */
    @Select("SELECT csl.*, u.lzhshtp_username as userName " +
            "FROM tb_lzhshtp_credit_score_logs csl " +
            "LEFT JOIN tb_lzhshtp_users u ON csl.lzhshtp_user_id = u.lzhshtp_user_id " +
            "WHERE csl.lzhshtp_change_type = #{changeType} " +
            "ORDER BY csl.lzhshtp_created_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getLogsByChangeType(@Param("changeType") String changeType, @Param("limit") Integer limit);

    /**
     * 检查用户在指定时间内的信用分变更频率（用于异常检测）
     * @param userId 用户ID
     * @param hours 小时数
     * @return 变更次数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_credit_score_logs " +
            "WHERE lzhshtp_user_id = #{userId} " +
            "AND lzhshtp_created_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR)")
    Integer countRecentChanges(@Param("userId") Long userId, @Param("hours") Integer hours);
}
