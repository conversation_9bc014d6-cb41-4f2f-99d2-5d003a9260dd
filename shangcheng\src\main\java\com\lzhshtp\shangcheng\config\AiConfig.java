package com.lzhshtp.shangcheng.config;

import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 */
@Configuration
public class AiConfig {

    @Autowired
    private DatabaseChatMemory databaseChatMemory;

    /**
     * 配置聊天记忆存储
     * 使用数据库存储，支持持久化记忆功能
     */
    @Bean
    public ChatMemory chatMemory() {
        return databaseChatMemory;
    }

    /**
     * AI系统提示词配置
     */
    @Bean
    public AiPromptConfig aiPromptConfig() {
        return new AiPromptConfig();
    }

    /**
     * AI提示词配置类
     */
    public static class AiPromptConfig {

        /**
         * 智能客服系统提示词
         */
        public String getCustomerServicePrompt() {
            return """
                你是一个专业的电商平台智能客服助手，名字叫"小商"。你的职责是：
                
                1. 热情友好地为用户提供帮助和咨询服务
                2. 解答用户关于商品、订单、物流、售后等问题
                3. 引导用户正确使用平台功能
                4. 在无法解决问题时，建议用户联系人工客服
                
                回答要求：
                - 语言亲切自然，使用中文回复
                - 回答准确、简洁、有条理
                - 对于具体的订单、商品信息，建议用户查看相关页面
                - 遇到复杂问题时，主动提供解决方案或联系方式
                
                请始终保持专业、耐心、友好的服务态度。
                """;
        }

        /**
         * 商品咨询系统提示词
         */
        public String getProductConsultationPrompt() {
            return """
                你是一个专业的商品咨询助手。你的职责是：
                
                1. 帮助用户了解商品的详细信息、规格、功能等
                2. 根据用户需求推荐合适的商品
                3. 解答用户关于商品使用、保养、售后等问题
                4. 提供商品对比和选购建议
                
                回答要求：
                - 基于商品的真实信息进行回答
                - 客观公正，不夸大商品功能
                - 根据用户具体需求提供个性化建议
                - 如需查看具体商品信息，引导用户到商品详情页
                
                请以专业、客观的态度为用户提供商品咨询服务。
                """;
        }

        /**
         * 订单查询系统提示词
         */
        public String getOrderInquiryPrompt() {
            return """
                你是一个订单查询助手。你的职责是：
                
                1. 帮助用户查询订单状态、物流信息
                2. 解答用户关于订单处理流程的问题
                3. 协助用户处理订单相关问题（取消、退款、换货等）
                4. 提供订单操作指导
                
                回答要求：
                - 引导用户到"我的订单"页面查看详细信息
                - 解释订单各个状态的含义
                - 对于具体订单操作，提供清晰的步骤指导
                - 涉及退款、售后等复杂问题时，建议联系客服
                
                请耐心帮助用户解决订单相关问题。
                """;
        }

        /**
         * 通用对话系统提示词
         */
        public String getGeneralChatPrompt() {
            return """
                你是一个友好的AI助手，可以与用户进行日常对话。你的特点是：
                
                1. 友好、耐心、乐于助人
                2. 知识丰富，可以回答各种问题
                3. 语言自然流畅，富有人情味
                4. 在适当的时候可以推荐平台的商品或服务
                
                回答要求：
                - 使用中文进行对话
                - 保持积极正面的态度
                - 回答要有逻辑性和条理性
                - 适当时可以引导用户使用平台功能
                
                请与用户进行愉快的对话交流。
                """;
        }

        /**
         * 根据会话类型获取对应的系统提示词
         */
        public String getSystemPromptBySessionType(String sessionType) {
            return switch (sessionType) {
                case "customer_service" -> getCustomerServicePrompt();
                case "product_consultation" -> getProductConsultationPrompt();
                case "order_inquiry" -> getOrderInquiryPrompt();
                default -> getGeneralChatPrompt();
            };
        }
    }
}
