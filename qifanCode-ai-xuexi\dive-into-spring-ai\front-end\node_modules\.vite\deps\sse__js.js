import "./chunk-TXPGJST7.js";

// node_modules/sse.js/lib/sse.js
var SSE = function(url, options) {
  if (!(this instanceof SSE)) {
    return new SSE(url, options);
  }
  this.INITIALIZING = -1;
  this.CONNECTING = 0;
  this.OPEN = 1;
  this.CLOSED = 2;
  this.url = url;
  options = options || {};
  this.headers = options.headers || {};
  this.payload = options.payload !== void 0 ? options.payload : "";
  this.method = options.method || (this.payload && "POST" || "GET");
  this.withCredentials = !!options.withCredentials;
  this.debug = !!options.debug;
  this.FIELD_SEPARATOR = ":";
  this.listeners = {};
  this.xhr = null;
  this.readyState = this.INITIALIZING;
  this.progress = 0;
  this.chunk = "";
  this.lastEventId = "";
  this.addEventListener = function(type, listener) {
    if (this.listeners[type] === void 0) {
      this.listeners[type] = [];
    }
    if (this.listeners[type].indexOf(listener) === -1) {
      this.listeners[type].push(listener);
    }
  };
  this.removeEventListener = function(type, listener) {
    if (this.listeners[type] === void 0) {
      return;
    }
    const filtered = [];
    this.listeners[type].forEach(function(element) {
      if (element !== listener) {
        filtered.push(element);
      }
    });
    if (filtered.length === 0) {
      delete this.listeners[type];
    } else {
      this.listeners[type] = filtered;
    }
  };
  this.dispatchEvent = function(e) {
    if (!e) {
      return true;
    }
    if (this.debug) {
      console.debug(e);
    }
    e.source = this;
    const onHandler = "on" + e.type;
    if (this.hasOwnProperty(onHandler)) {
      this[onHandler].call(this, e);
      if (e.defaultPrevented) {
        return false;
      }
    }
    if (this.listeners[e.type]) {
      return this.listeners[e.type].every(function(callback) {
        callback(e);
        return !e.defaultPrevented;
      });
    }
    return true;
  };
  this._setReadyState = function(state) {
    const event = new CustomEvent("readystatechange");
    event.readyState = state;
    this.readyState = state;
    this.dispatchEvent(event);
  };
  this._onStreamFailure = function(e) {
    const event = new CustomEvent("error");
    event.data = e.currentTarget.response;
    this.dispatchEvent(event);
    this.close();
  };
  this._onStreamAbort = function() {
    this.dispatchEvent(new CustomEvent("abort"));
    this.close();
  };
  this._onStreamProgress = function(e) {
    if (!this.xhr) {
      return;
    }
    if (this.xhr.status !== 200) {
      this._onStreamFailure(e);
      return;
    }
    if (this.readyState === this.CONNECTING) {
      this.dispatchEvent(new CustomEvent("open"));
      this._setReadyState(this.OPEN);
    }
    const data = this.xhr.responseText.substring(this.progress);
    this.progress += data.length;
    const parts = (this.chunk + data).split(/(\r\n\r\n|\r\r|\n\n)/g);
    const lastPart = parts.pop();
    parts.forEach((function(part) {
      if (part.trim().length > 0) {
        this.dispatchEvent(this._parseEventChunk(part));
      }
    }).bind(this));
    this.chunk = lastPart;
  };
  this._onStreamLoaded = function(e) {
    this._onStreamProgress(e);
    this.dispatchEvent(this._parseEventChunk(this.chunk));
    this.chunk = "";
  };
  this._parseEventChunk = function(chunk) {
    if (!chunk || chunk.length === 0) {
      return null;
    }
    if (this.debug) {
      console.debug(chunk);
    }
    const e = { "id": null, "retry": null, "data": null, "event": null };
    chunk.split(/\n|\r\n|\r/).forEach((function(line) {
      const index = line.indexOf(this.FIELD_SEPARATOR);
      let field, value;
      if (index > 0) {
        const skip = line[index + 1] === " " ? 2 : 1;
        field = line.substring(0, index);
        value = line.substring(index + skip);
      } else if (index < 0) {
        field = line;
        value = "";
      } else {
        return;
      }
      if (!(field in e)) {
        return;
      }
      if (field === "data" && e[field] !== null) {
        e["data"] += "\n" + value;
      } else {
        e[field] = value;
      }
    }).bind(this));
    if (e.id !== null) {
      this.lastEventId = e.id;
    }
    const event = new CustomEvent(e.event || "message");
    event.id = e.id;
    event.data = e.data || "";
    event.lastEventId = this.lastEventId;
    return event;
  };
  this._checkStreamClosed = function() {
    if (!this.xhr) {
      return;
    }
    if (this.xhr.readyState === XMLHttpRequest.DONE) {
      this._setReadyState(this.CLOSED);
    }
  };
  this.stream = function() {
    if (this.xhr) {
      return;
    }
    this._setReadyState(this.CONNECTING);
    this.xhr = new XMLHttpRequest();
    this.xhr.addEventListener("progress", this._onStreamProgress.bind(this));
    this.xhr.addEventListener("load", this._onStreamLoaded.bind(this));
    this.xhr.addEventListener("readystatechange", this._checkStreamClosed.bind(this));
    this.xhr.addEventListener("error", this._onStreamFailure.bind(this));
    this.xhr.addEventListener("abort", this._onStreamAbort.bind(this));
    this.xhr.open(this.method, this.url);
    for (let header in this.headers) {
      this.xhr.setRequestHeader(header, this.headers[header]);
    }
    if (this.lastEventId.length > 0) {
      this.xhr.setRequestHeader("Last-Event-ID", this.lastEventId);
    }
    this.xhr.withCredentials = this.withCredentials;
    this.xhr.send(this.payload);
  };
  this.close = function() {
    if (this.readyState === this.CLOSED) {
      return;
    }
    this.xhr.abort();
    this.xhr = null;
    this._setReadyState(this.CLOSED);
  };
  if (options.start === void 0 || options.start) {
    this.stream();
  }
};
if (typeof exports !== "undefined") {
  exports.SSE = SSE;
}
export {
  SSE
};
//# sourceMappingURL=sse__js.js.map
