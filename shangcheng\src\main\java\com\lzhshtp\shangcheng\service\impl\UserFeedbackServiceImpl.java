package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.FeedbackStatusUpdateRequest;
import com.lzhshtp.shangcheng.dto.UserFeedbackDTO;
import com.lzhshtp.shangcheng.dto.UserFeedbackQueryParams;
import com.lzhshtp.shangcheng.dto.UserFeedbackRequest;
import com.lzhshtp.shangcheng.exception.BusinessException;
import com.lzhshtp.shangcheng.mapper.UserFeedbackMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.model.UserFeedback;
import com.lzhshtp.shangcheng.service.UserFeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户反馈/举报服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFeedbackServiceImpl extends ServiceImpl<UserFeedbackMapper, UserFeedback> implements UserFeedbackService {

    private final UserFeedbackMapper userFeedbackMapper;
    private final UserMapper userMapper;
    
    // 反馈类型描述映射
    private static final Map<String, String> FEEDBACK_TYPE_DESC_MAP = new HashMap<>();
    static {
        FEEDBACK_TYPE_DESC_MAP.put("bug_report", "问题反馈");
        FEEDBACK_TYPE_DESC_MAP.put("suggestion", "建议");
        FEEDBACK_TYPE_DESC_MAP.put("complaint", "投诉");
        FEEDBACK_TYPE_DESC_MAP.put("abuse_report", "举报");
    }
    
    // 状态描述映射
    private static final Map<String, String> STATUS_DESC_MAP = new HashMap<>();
    static {
        STATUS_DESC_MAP.put("pending", "待处理");
        STATUS_DESC_MAP.put("in_progress", "处理中");
        STATUS_DESC_MAP.put("resolved", "已解决");
        STATUS_DESC_MAP.put("rejected", "已拒绝");
    }

    /**
     * 提交反馈/举报
     *
     * @param request 反馈请求
     * @param userId 当前用户ID
     * @return 反馈ID
     */
    @Override
    @Transactional
    public Long submitFeedback(UserFeedbackRequest request, Long userId) {
        // 验证反馈类型
        if (!FEEDBACK_TYPE_DESC_MAP.containsKey(request.getFeedbackType())) {
            throw new BusinessException("无效的反馈类型");
        }
        
        // 如果是举报类型，需要验证关联实体信息
        if ("abuse_report".equals(request.getFeedbackType())) {
            if (request.getRelatedEntityType() == null || request.getRelatedEntityId() == null) {
                throw new BusinessException("举报必须指定关联实体类型和ID");
            }
        }
        
        // 创建反馈记录
        UserFeedback feedback = UserFeedback.builder()
                .reporterId(userId)
                .feedbackType(request.getFeedbackType())
                .relatedEntityType(request.getRelatedEntityType())
                .relatedEntityId(request.getRelatedEntityId())
                .content(request.getContent())
                .submittedAt(LocalDateTime.now())
                .status("pending") // 默认状态为待处理
                .build();
        
        // 保存反馈
        save(feedback);
        log.info("用户 {} 提交了新的反馈/举报，ID: {}, 类型: {}", userId, feedback.getFeedbackId(), request.getFeedbackType());
        
        return feedback.getFeedbackId();
    }

    /**
     * 分页查询反馈列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    @Override
    public IPage<UserFeedbackDTO> getFeedbackList(UserFeedbackQueryParams queryParams) {
        Page<UserFeedback> page = new Page<>(queryParams.getPageNum(), queryParams.getPageSize());
        
        // 调用自定义查询
        IPage<UserFeedback> feedbackPage = userFeedbackMapper.selectFeedbackList(
                page,
                queryParams.getReporterId(),
                queryParams.getFeedbackType(),
                queryParams.getStatus(),
                queryParams.getRelatedEntityType(),
                queryParams.getRelatedEntityId(),
                queryParams.getKeyword()
        );
        
        // 转换为DTO
        return feedbackPage.convert(this::convertToDTO);
    }

    /**
     * 获取反馈详情
     *
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    @Override
    public UserFeedbackDTO getFeedbackDetail(Long feedbackId) {
        if (feedbackId == null) {
            log.error("获取反馈详情失败：feedbackId为null");
            throw new BusinessException("反馈ID不能为空");
        }
        
        try {
            UserFeedback feedback = getById(feedbackId);
            if (feedback == null) {
                log.error("获取反馈详情失败：ID为{}的反馈不存在", feedbackId);
                throw new BusinessException("反馈不存在");
            }
            
            return convertToDTO(feedback);
        } catch (Exception e) {
            log.error("获取反馈详情时发生异常，feedbackId: {}", feedbackId, e);
            throw new BusinessException("获取反馈详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新反馈状态（管理员功能）
     *
     * @param feedbackId 反馈ID
     * @param request 状态更新请求
     * @param adminId 管理员ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateFeedbackStatus(Long feedbackId, FeedbackStatusUpdateRequest request, Long adminId) {
        // 验证状态
        if (!STATUS_DESC_MAP.containsKey(request.getStatus())) {
            throw new BusinessException("无效的状态");
        }
        
        // 检查反馈是否存在
        UserFeedback feedback = getById(feedbackId);
        if (feedback == null) {
            throw new BusinessException("反馈不存在");
        }
        
        // 更新状态
        int rows = userFeedbackMapper.updateFeedbackStatus(
                feedbackId,
                request.getStatus(),
                adminId,
                request.getAdminNotes()
        );
        
        log.info("管理员 {} 更新了反馈 {} 的状态为 {}", adminId, feedbackId, request.getStatus());
        
        return rows > 0;
    }

    /**
     * 删除反馈
     *
     * @param feedbackId 反馈ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteFeedback(Long feedbackId, Long userId) {
        // 检查反馈是否存在
        UserFeedback feedback = getById(feedbackId);
        if (feedback == null) {
            throw new BusinessException("反馈不存在");
        }
        
        // 只有反馈提交者或管理员可以删除
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        boolean isAdmin = "admin".equals(user.getRole());
        if (!isAdmin && !feedback.getReporterId().equals(userId)) {
            throw new BusinessException("无权删除此反馈");
        }
        
        boolean result = removeById(feedbackId);
        if (result) {
            log.info("用户 {} 删除了反馈 {}", userId, feedbackId);
        }
        
        return result;
    }
    
    /**
     * 将实体转换为DTO
     *
     * @param feedback 反馈实体
     * @return 反馈DTO
     */
    private UserFeedbackDTO convertToDTO(UserFeedback feedback) {
        if (feedback == null) {
            return null;
        }
        
        try {
            UserFeedbackDTO dto = new UserFeedbackDTO();
            dto.setFeedbackId(feedback.getFeedbackId());
            dto.setReporterId(feedback.getReporterId());
            dto.setFeedbackType(feedback.getFeedbackType());
            dto.setRelatedEntityType(feedback.getRelatedEntityType());
            dto.setRelatedEntityId(feedback.getRelatedEntityId());
            dto.setContent(feedback.getContent());
            dto.setSubmittedAt(feedback.getSubmittedAt());
            dto.setStatus(feedback.getStatus());
            dto.setAdminNotes(feedback.getAdminNotes());
            dto.setResolvedByAdminId(feedback.getResolvedByAdminId());
            
            // 设置类型描述
            if (feedback.getFeedbackType() != null) {
                dto.setFeedbackTypeDesc(FEEDBACK_TYPE_DESC_MAP.getOrDefault(feedback.getFeedbackType(), feedback.getFeedbackType()));
            } else {
                dto.setFeedbackTypeDesc("未知类型");
            }
            
            // 设置状态描述
            if (feedback.getStatus() != null) {
                dto.setStatusDesc(STATUS_DESC_MAP.getOrDefault(feedback.getStatus(), feedback.getStatus()));
            } else {
                dto.setStatusDesc("未知状态");
            }
            
            // 获取用户名
            try {
                if (feedback.getReporterId() != null) {
                    User reporter = userMapper.selectById(feedback.getReporterId());
                    if (reporter != null) {
                        dto.setReporterName(reporter.getUsername());
                    } else {
                        dto.setReporterName("未知用户");
                    }
                }
                
                if (feedback.getResolvedByAdminId() != null) {
                    User admin = userMapper.selectById(feedback.getResolvedByAdminId());
                    if (admin != null) {
                        dto.setResolvedByAdminName(admin.getUsername());
                    } else {
                        dto.setResolvedByAdminName("未知管理员");
                    }
                }
            } catch (Exception e) {
                log.error("获取用户信息失败", e);
                // 设置默认值，避免前端显示出错
                if (dto.getReporterName() == null) {
                    dto.setReporterName("未知用户");
                }
                if (feedback.getResolvedByAdminId() != null && dto.getResolvedByAdminName() == null) {
                    dto.setResolvedByAdminName("未知管理员");
                }
            }
            
            return dto;
        } catch (Exception e) {
            log.error("转换反馈DTO时发生异常", e);
            // 返回一个基本的DTO对象，避免前端显示出错
            UserFeedbackDTO fallbackDto = new UserFeedbackDTO();
            fallbackDto.setFeedbackId(feedback.getFeedbackId());
            fallbackDto.setContent("数据转换出错，请联系管理员");
            fallbackDto.setStatusDesc("未知");
            return fallbackDto;
        }
    }
} 