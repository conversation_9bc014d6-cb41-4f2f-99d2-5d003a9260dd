package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.ForumComment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 论坛评论Mapper接口
 */
public interface ForumCommentMapper extends BaseMapper<ForumComment> {
    /**
     * 查询帖子的所有评论
     * 
     * @param postId 帖子ID
     * @return 评论列表
     */
    List<ForumComment> selectCommentsByPostId(@Param("postId") Long postId);
    
    /**
     * 查询评论的所有回复
     * 
     * @param commentId 评论ID
     * @return 回复列表
     */
    List<ForumComment> selectRepliesByCommentId(@Param("commentId") Long commentId);
    
    /**
     * 统计帖子评论数量
     * 
     * @param postId 帖子ID
     * @return 评论数量
     */
    Integer countCommentsByPostId(@Param("postId") Long postId);
} 