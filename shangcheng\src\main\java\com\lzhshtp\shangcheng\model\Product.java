package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_products")
public class Product {

    @TableId(value = "lzhshtp_product_id", type = IdType.AUTO)
    private Long id;

    @TableField("lzhshtp_title")
    private String title;

    @TableField("lzhshtp_description")
    private String description;

    @TableField("lzhshtp_price")
    private BigDecimal price;

    @TableField("lzhshtp_category_id")
    private Integer categoryId;

    @TableField(value = "`lzhshtp_condition`")
    private String productCondition;

    @TableField("lzhshtp_location")
    private String location;

    @TableField("lzhshtp_delivery_method")
    private String deliveryMethod;

    @TableField("lzhshtp_image_urls")
    private String imageUrls;

    @TableField("lzhshtp_posted_date")
    private LocalDateTime postedDate;

    @TableField("lzhshtp_status")
    private String status;

    @TableField("lzhshtp_support_official_verification")
    private Boolean supportOfficialVerification;

    @TableField("lzhshtp_verification_fee")
    private BigDecimal verificationFee;

    @TableField("lzhshtp_seller_id")
    private Long sellerId;

    // 兼容性方法，保持原有API不变
    public String getCondition() {
        return productCondition;
    }

    public void setCondition(String condition) {
        this.productCondition = condition;
    }

    // 商品状态常量
    public static class ProductStatus {
        public static final String AVAILABLE = "available";
        public static final String SOLD = "sold";
        public static final String PENDING_REVIEW = "pending_review";
        public static final String OFF_SHELF_BY_SELLER = "off_shelf_by_seller";
        public static final String OFF_SHELF_BY_ADMIN = "off_shelf_by_admin";
        public static final String DELETED = "deleted";
    }
}
