<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.UserFeedbackMapper">
    
    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.UserFeedback">
        <id column="lzhshtp_feedback_id" property="feedbackId" />
        <result column="lzhshtp_reporter_id" property="reporterId" />
        <result column="lzhshtp_feedback_type" property="feedbackType" />
        <result column="lzhshtp_related_entity_type" property="relatedEntityType" />
        <result column="lzhshtp_related_entity_id" property="relatedEntityId" />
        <result column="lzhshtp_content" property="content" />
        <result column="lzhshtp_submitted_at" property="submittedAt" />
        <result column="lzhshtp_status" property="status" />
        <result column="lzhshtp_admin_notes" property="adminNotes" />
        <result column="lzhshtp_resolved_by_admin_id" property="resolvedByAdminId" />
    </resultMap>
    
    <!-- 插入用户反馈 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="feedbackId">
        INSERT INTO tb_lzhshtp_user_feedback (
            lzhshtp_reporter_id, 
            lzhshtp_feedback_type, 
            lzhshtp_related_entity_type, 
            lzhshtp_related_entity_id,
            lzhshtp_content, 
            lzhshtp_submitted_at, 
            lzhshtp_status
        ) VALUES (
            #{reporterId}, 
            #{feedbackType}, 
            #{relatedEntityType}, 
            #{relatedEntityId},
            #{content}, 
            #{submittedAt}, 
            #{status}
        )
    </insert>
    
    <!-- 分页查询用户反馈列表 -->
    <select id="selectFeedbackList" resultMap="BaseResultMap">
        SELECT 
            f.lzhshtp_feedback_id,
            f.lzhshtp_reporter_id,
            f.lzhshtp_feedback_type,
            f.lzhshtp_related_entity_type,
            f.lzhshtp_related_entity_id,
            f.lzhshtp_content,
            f.lzhshtp_submitted_at,
            f.lzhshtp_status,
            f.lzhshtp_admin_notes,
            f.lzhshtp_resolved_by_admin_id
        FROM 
            tb_lzhshtp_user_feedback f
        LEFT JOIN
            tb_lzhshtp_users u ON f.lzhshtp_reporter_id = u.lzhshtp_user_id
        <where>
            <if test="reporterId != null">
                AND f.lzhshtp_reporter_id = #{reporterId}
            </if>
            <if test="feedbackType != null and feedbackType != ''">
                AND f.lzhshtp_feedback_type = #{feedbackType}
            </if>
            <if test="status != null and status != ''">
                AND f.lzhshtp_status = #{status}
            </if>
            <if test="relatedEntityType != null and relatedEntityType != ''">
                AND f.lzhshtp_related_entity_type = #{relatedEntityType}
            </if>
            <if test="relatedEntityId != null">
                AND f.lzhshtp_related_entity_id = #{relatedEntityId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    f.lzhshtp_content LIKE CONCAT('%', #{keyword}, '%') OR
                    u.lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') OR
                    f.lzhshtp_feedback_id LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY f.lzhshtp_submitted_at DESC
    </select>
    
    <!-- 更新反馈状态 -->
    <update id="updateFeedbackStatus">
        UPDATE tb_lzhshtp_user_feedback
        SET 
            lzhshtp_status = #{status},
            lzhshtp_resolved_by_admin_id = #{adminId},
            lzhshtp_admin_notes = #{adminNotes}
        WHERE 
            lzhshtp_feedback_id = #{feedbackId}
    </update>
    
</mapper> 