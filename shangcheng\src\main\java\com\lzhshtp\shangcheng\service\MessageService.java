package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.MessageDTO;
import com.lzhshtp.shangcheng.dto.MessageRequest;
import com.lzhshtp.shangcheng.dto.PageResult;

import java.util.List;

public interface MessageService {

    /**
     * 发送消息
     * @param senderId 发送者ID
     * @param messageRequest 消息请求
     * @return 消息DTO
     */
    MessageDTO sendMessage(Long senderId, MessageRequest messageRequest);
    
    /**
     * 发送系统消息（管理员专用）
     * @param adminId 管理员ID
     * @param messageRequest 消息请求
     * @return 消息DTO
     */
    MessageDTO sendSystemMessage(Long adminId, MessageRequest messageRequest);

    /**
     * 获取会话中的消息列表
     * @param conversationId 会话ID
     * @param currentUserId 当前用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息DTO分页结果
     */
    PageResult<MessageDTO> getConversationMessages(Long conversationId, Long currentUserId, int page, int size);

    /**
     * 将会话中的消息标记为已读
     * @param conversationId 会话ID
     * @param currentUserId 当前用户ID
     * @return 是否标记成功
     */
    boolean markMessagesAsRead(Long conversationId, Long currentUserId);

    /**
     * 删除消息
     * @param messageId 消息ID
     * @param currentUserId 当前用户ID
     * @return 是否删除成功
     */
    boolean deleteMessage(Long messageId, Long currentUserId);

    /**
     * 获取用户的所有未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getTotalUnreadCount(Long userId);
} 