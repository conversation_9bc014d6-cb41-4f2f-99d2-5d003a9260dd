<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手交易平台 - 主页</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF4D4F;
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* 顶部导航 */
        .top-nav {
            background: var(--white);
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }

        .search-area {
            flex-grow: 1;
            margin: 0 32px;
            max-width: 600px;
        }

        .search-bar {
            display: flex;
            border: 2px solid var(--primary-color);
            border-radius: 24px;
            overflow: hidden;
        }

        .search-input {
            border: none;
            background: none;
            padding: 10px 20px;
            width: 100%;
            font-size: 14px;
        }
        .search-input:focus {
            outline: none;
        }

        .search-button {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0 24px;
            font-weight: 500;
            cursor: pointer;
            font-size: 15px;
        }
        
        .search-tags {
            display: flex;
            gap: 16px;
            margin-top: 8px;
            font-size: 12px;
        }
        
        .search-tags a {
            color: var(--text-color-light);
            text-decoration: none;
        }
        .search-tags a:hover {
            color: var(--primary-color);
        }

        .nav-right {
            display: flex;
            align-items: center;
        }

        .nav-right a {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 14px;
            margin-left: 20px;
            transition: color 0.2s ease;
        }

        .nav-right a:hover {
            color: var(--primary-color);
        }

        .nav-right .nav-icon-link {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .nav-right .separator {
            height: 16px;
            width: 1px;
            background-color: var(--border-color);
            margin: 0 12px;
        }

        /* 主要内容 */
        main {
            padding-top: 24px;
        }

        /* 频道分区 */
        .channels-section {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
        }

        .large-channel-card {
            flex: 1;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            padding: 24px;
            color: var(--white);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .large-channel-card h2 { font-size: 24px; }
        .large-channel-card p { font-size: 14px; opacity: 0.9; }
        .large-channel-card .cta-button {
            background: rgba(255,255,255,0.9);
            color: var(--primary-color);
            padding: 10px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            align-self: flex-start;
            margin-top: 16px;
        }
        
        .small-channels-grid {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
        }

        .channel-card {
            border-radius: 12px;
            padding: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            background-size: cover;
        }
        
        .channel-card h3 { font-size: 16px; }
        .channel-card p { font-size: 12px; color: var(--text-color-light); }
        .channel-card .products { display: flex; gap: 8px; margin-top: 8px; }
        .channel-card .product-item img { width: 100%; border-radius: 8px; }
        .channel-card .product-item span { font-size: 13px; font-weight: 500; color: var(--text-color-dark); }
        
        .channel-card.yellow { background-color: #FFF1F0; }
        .channel-card.blue { background-color: #FFF7F7; }
        .channel-card.green { background-color: #FFF1F0; }
        .channel-card.pink { background-color: #FFF7F7; }

        /* 兴趣标签 */
        .interest-tags {
            margin-bottom: 24px;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }
        .interest-tag {
            background: var(--white);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            text-decoration: none;
            color: var(--text-color-dark);
            transition: all 0.2s ease;
        }
        .interest-tag:hover {
            background: #FFF1F0;
            color: var(--primary-color);
        }

        /* 瀑布流商品 */
        .products-feed {
            column-count: 4;
            column-gap: 16px;
        }
        
        .product-card {
            background: var(--white);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;
            break-inside: avoid;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .product-card:hover {
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .product-card img {
            width: 100%;
            display: block;
        }
        
        .product-info {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }
        
        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-color);
        }
        .product-price span { font-size: 12px; }

        .product-seller {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .product-seller img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
        }
        .product-seller span {
            font-size: 12px;
            color: var(--text-color-light);
        }
        
        /* Floating Action Buttons */
        .fab-container {
            position: fixed;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            z-index: 998;
            background-color: var(--white);
            border-radius: 15px;
            padding: 8px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 60px;
        }

        .fab {
            background-color: transparent;
            color: var(--text-color-dark);
            width: 48px;
            height: auto;
            padding: 6px 0;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            gap: 4px;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .fab:hover {
            background-color: var(--bg-color);
            transform: none;
            box-shadow: none;
        }
        
        .fab .fab-icon {
            font-size: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }
        
        @media(max-width: 1024px) {
            .products-feed { column-count: 3; }
            .search-area { max-width: 400px; }
        }
        @media(max-width: 768px) {
            .products-feed { column-count: 2; }
            .channels-section { flex-direction: column; }
            .nav-content { flex-wrap: wrap; gap: 16px; }
            .search-area { order: 3; width: 100%; margin: 0; }
        }

        /* 动画 */
        .reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Chat Window */
        .chat-window {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--white);
            width: 100%;
            max-width: 420px;
            height: 80vh;
            max-height: 600px;
            border-radius: 12px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            z-index: 1999;
            transform: translateX(calc(100% + 40px));
            transition: transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);
            visibility: hidden;
        }

        .chat-window.visible {
            transform: translateX(0);
            visibility: visible;
        }

        .chat-header {
            background: #4A4A4A;
            color: white;
            padding: 12px 20px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-header .logo-icon {
            width: 32px;
            height: 32px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 14px;
        }

        .chat-header h3 {
            font-size: 16px;
            font-weight: 500;
        }

        .close-chat {
            background: none;
            border: none;
            color: white;
            font-size: 28px;
            font-weight: 300;
            cursor: pointer;
            line-height: 1;
        }

        .chat-body {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: var(--bg-color);
        }

        .chat-message {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
            max-width: 85%;
        }

        .chat-message.bot {
            align-items: flex-start;
        }

        .chat-message.user {
            align-items: flex-end;
            margin-left: auto;
        }

        .chat-message .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.6;
        }

        .chat-message.user .message-content {
            background: var(--primary-color);
            color: white;
            border-bottom-right-radius: 4px;
        }
        .chat-message.bot .message-content {
            background: var(--white);
            color: var(--text-color-dark);
            border-bottom-left-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        .chat-input-area {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
            background: var(--white);
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            flex-shrink: 0;
        }

        .chat-input-area .chat-input-wrapper {
            flex-grow: 1;
            display: flex;
            align-items: center;
            background: var(--bg-color);
            border-radius: 20px;
            padding: 0 8px;
        }

        .chat-input-area input {
            flex-grow: 1;
            border: none;
            background: none;
            padding: 8px;
            font-size: 14px;
            color: var(--text-color-dark);
        }
        .chat-input-area input:focus {
            outline: none;
        }

        .chat-input-area .chat-tool-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 22px;
            color: var(--text-color-light);
            margin: 0 8px;
            transition: color 0.2s;
        }

        .chat-input-area .chat-tool-btn:hover {
            color: var(--text-color-dark);
        }

    </style>
</head>
<body>

    <!-- 顶部导航 -->
    <nav class="top-nav">
        <div class="container nav-content">
            <a href="index.html" class="logo">二手交易</a>
            <div class="search-area">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索想要的商品...">
                    <button class="search-button">搜索</button>
                </div>
                <div class="search-tags">
                    <a href="#">穿戴甲</a>
                    <a href="#">电动车</a>
                    <a href="#">打印机</a>
                    <a href="#">手机挂绳</a>
                    <a href="#">休闲裤</a>
                    <a href="#">洞洞鞋</a>
                </div>
            </div>
            <div class="nav-right">
                <a href="user-profile.html" class="nav-icon-link">
                     <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5.52 19c.64-2.2 1.84-3.98 3.36-5.18 1.46-1.15 3.24-1.74 5.09-1.74 1.84 0 3.63.59 5.09 1.74 1.52 1.2 2.72 2.97 3.36 5.18M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4z"></path></svg>
                     <span>个人中心</span>
                </a>
                <a href="login.html">登录/注册</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container">

        <!-- 频道分区 -->
        <section class="channels-section reveal">
            <div class="large-channel-card">
                <div>
                    <h2>闲置抄底好物</h2>
                    <p>超绝性价比, 省到底</p>
                </div>
                <a href="#" class="cta-button">去看看 ></a>
            </div>
            <div class="small-channels-grid">
                <div class="channel-card yellow">
                    <div><h3>衣橱捡漏</h3><p>时尚美衣低价淘</p></div>
                    <div class="products"><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥169</span></div><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥199</span></div></div>
                </div>
                <div class="channel-card blue">
                    <div><h3>手机数码</h3><p>热门装备省心入</p></div>
                    <div class="products"><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥108</span></div><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥999</span></div></div>
                </div>
                <div class="channel-card green">
                    <div><h3>二次元</h3><p>烫门新品随手入</p></div>
                    <div class="products"><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥60</span></div><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥5</span></div></div>
                </div>
                <div class="channel-card pink">
                    <div><h3>省钱卡券</h3><p>吃喝玩乐放心购</p></div>
                    <div class="products"><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥3.99</span></div><div class="product-item"><img src="https://via.placeholder.com/100x100"><span class="price">¥4000</span></div></div>
                </div>
            </div>
        </section>

        <!-- 兴趣标签 -->
        <section class="interest-tags reveal">
            <a href="#" class="interest-tag">BJD娃娃</a>
            <a href="#" class="interest-tag">垂钓</a>
            <a href="#" class="interest-tag">吉他乐器</a>
            <a href="#" class="interest-tag">台球</a>
            <a href="#" class="interest-tag">摄影摄像</a>
            <a href="#" class="interest-tag">钱币收藏</a>
            <a href="#" class="interest-tag">女装穿搭</a>
            <a href="#" class="interest-tag">居家好物</a>
            <a href="#" class="interest-tag">大牌美妆</a>
            <a href="#" class="interest-tag">机车</a>
        </section>
        
        <!-- 商品瀑布流 -->
        <section class="products-feed reveal">
            <div class="product-card">
                <img src="https://via.placeholder.com/300x200" alt="二手车">
                <div class="product-info">
                    <h3 class="product-title">白色起亚K3，自动挡带天窗，车况精品</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>28800</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>车主直卖</span></div>
                    </div>
                </div>
            </div>
            <div class="product-card">
                <img src="https://via.placeholder.com/300x400" alt="猫爬架">
                <div class="product-info">
                    <h3 class="product-title">几乎全新猫爬架，我家猫主子不喜欢</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>80</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>爱猫人士</span></div>
                    </div>
                </div>
            </div>
            <div class="product-card">
                <img src="https://via.placeholder.com/300x300" alt="卫衣">
                <div class="product-info">
                    <h3 class="product-title">三丽鸥联名卫衣，只穿过一次，99新</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>120</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>潮流女孩</span></div>
                    </div>
                </div>
            </div>
            <div class="product-card">
                <img src="https://via.placeholder.com/300x350" alt="摩托车">
                <div class="product-info">
                    <h3 class="product-title">本田裂行125，上下班代步神器</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>6500</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>机车男孩</span></div>
                    </div>
                </div>
            </div>
             <div class="product-card">
                <img src="https://via.placeholder.com/300x320" alt="相机">
                <div class="product-info">
                    <h3 class="product-title">富士XT-30二代，带18-55镜头，新手神机</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>6200</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>摄影爱好者</span></div>
                    </div>
                </div>
            </div>
             <div class="product-card">
                <img src="https://via.placeholder.com/300x250" alt="乐高">
                <div class="product-info">
                    <h3 class="product-title">乐高布加迪威龙，仅拼一次，带原盒说明书</h3>
                    <div class="product-meta">
                        <span class="product-price"><span>¥</span>1500</span>
                        <div class="product-seller"><img src="https://via.placeholder.com/24" alt="卖家头像"><span>乐高大神</span></div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Floating Action Buttons -->
    <div class="fab-container">
        <a href="publish-product.html" class="fab" title="发闲置">
            <span class="fab-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
            </span>
            <span>发闲置</span>
        </a>
        <a href="message-center.html" class="fab" title="消息">
            <span class="fab-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
            </span>
            <span>消息</span>
        </a>
        <a href="#" class="fab" title="反馈">
            <span class="fab-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
            </span>
            <span>反馈</span>
        </a>
        <a href="forum.html" class="fab" title="论坛">
            <span class="fab-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
            </span>
            <span>论坛</span>
        </a>
        <a href="#" class="fab" id="customerServiceFab" title="客服">
            <span class="fab-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
            </span>
            <span>客服</span>
        </a>
    </div>

    <!-- Chat Window -->
    <div class="chat-window" id="chatWindow">
        <div class="chat-header">
            <div class="chat-header-left">
                <div class="logo-icon">二手</div>
                <h3 id="chatTitle">客户支持</h3>
            </div>
            <button class="close-chat" id="closeChatBtn">&times;</button>
        </div>
        <div class="chat-body" id="chatBody">
            <!-- Dynamic Messages Appear Here -->
        </div>
        <div class="chat-input-area">
             <button class="chat-tool-btn">+</button>
            <div class="chat-input-wrapper">
                <input type="text" placeholder="输入信息..." id="chatInput">
            </div>
             <button class="chat-tool-btn">☺</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const revealElements = document.querySelectorAll('.reveal');

            const revealObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1
            });

            revealElements.forEach(elem => {
                revealObserver.observe(elem);
            });

            // Chat Modal Logic
            const chatWindow = document.getElementById('chatWindow');
            const closeChatBtn = document.getElementById('closeChatBtn');
            const chatTitle = document.getElementById('chatTitle');
            const chatBody = document.getElementById('chatBody');
            const fabContainer = document.querySelector('.fab-container');
            
            let currentlyOpenChat = null;

            const openChat = (type, title, messages) => {
                if (chatWindow.classList.contains('visible') && currentlyOpenChat === type) {
                    closeChat();
                    return;
                }

                chatTitle.textContent = title;
                chatBody.innerHTML = ''; 
                messages.forEach(msg => {
                    const messageEl = document.createElement('div');
                    messageEl.classList.add('chat-message', msg.type);
                    messageEl.innerHTML = `<div class="message-content">${msg.text}</div>`;
                    chatBody.appendChild(messageEl);
                });
                chatWindow.classList.add('visible');
                currentlyOpenChat = type;
            };

            const closeChat = () => {
                chatWindow.classList.remove('visible');
                currentlyOpenChat = null;
            };

            const customerServiceFab = document.getElementById('customerServiceFab');
            if (customerServiceFab) {
                customerServiceFab.addEventListener('click', (e) => {
                    e.preventDefault();
                    const messages = [
                        { type: 'bot', text: '你好! 👋 我是您的专属客服，有什么可以帮您？' },
                        { type: 'user', text: '你好，我想咨询一下退货流程。' },
                        { type: 'bot', text: '好的，请您提供一下您的订单号，我为您查询一下。' },
                    ];
                    openChat('service', '平台客服', messages);
                });
            }
            
            closeChatBtn.addEventListener('click', closeChat);
        });
    </script>
</body>
</html> 