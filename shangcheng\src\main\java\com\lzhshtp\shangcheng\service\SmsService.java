package com.lzhshtp.shangcheng.service;

/**
 * 短信服务接口
 */
public interface SmsService {
    
    /**
     * 发送验证码
     * @param phone 手机号
     * @param type 验证码类型 (login, register, reset_password)
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone, String type);
    
    /**
     * 验证验证码
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    boolean verifyCode(String phone, String code, String type);
    
    /**
     * 检查是否可以发送验证码（限流检查）
     * @param phone 手机号
     * @return 是否可以发送
     */
    boolean canSendCode(String phone);
    
    /**
     * 获取剩余等待时间（秒）
     * @param phone 手机号
     * @return 剩余等待时间，0表示可以立即发送
     */
    long getRemainingWaitTime(String phone);
}
