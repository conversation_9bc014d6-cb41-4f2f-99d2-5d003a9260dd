package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.statistics.*;
import com.lzhshtp.shangcheng.mapper.StatisticsMapper;
import com.lzhshtp.shangcheng.service.AdminStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.HashMap;
import java.util.Arrays;

/**
 * 管理员统计服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdminStatisticsServiceImpl implements AdminStatisticsService {

    private final StatisticsMapper statisticsMapper;

    @Override
    // @Cacheable(value = "overview-statistics", unless = "#result == null") // 暂时禁用缓存
    public OverviewStatisticsDTO getOverviewStatistics() {
        try {
            // 只使用真实数据，获取不到就设为null
            Long totalUsers = null;
            Long activeUsers = null;
            Long totalProducts = null;
            Long availableProducts = null;
            Long totalOrders = null;
            Long completedOrders = null;
            BigDecimal totalRevenue = null;
            Long totalForumPosts = null;
            Long totalForumComments = null;

            // 获取真实数据
            try {
                totalUsers = statisticsMapper.getTotalUsers();
                activeUsers = statisticsMapper.getActiveUsers(30);
                totalProducts = statisticsMapper.getTotalProducts();
                availableProducts = statisticsMapper.getAvailableProducts();
                totalOrders = statisticsMapper.getTotalOrders();
                completedOrders = statisticsMapper.getCompletedOrders();
                totalRevenue = statisticsMapper.getTotalRevenue();
                totalForumPosts = statisticsMapper.getTotalForumPosts();
                totalForumComments = statisticsMapper.getTotalForumComments();
            } catch (Exception e) {
                log.warn("数据库查询失败: {}", e.getMessage());
            }
            
            // 获取今日统计
            LocalDate today = LocalDate.now();
            Long todayNewUsers = statisticsMapper.getDailyNewUsers(today);
            Long todayNewProducts = statisticsMapper.getDailyNewProducts(today);
            Long todayOrders = statisticsMapper.getDailyOrders(today);
            BigDecimal todayRevenue = statisticsMapper.getDailyRevenue(today);
            
            // 获取昨日统计（用于计算增长率）
            LocalDate yesterday = today.minusDays(1);
            Long yesterdayNewUsers = statisticsMapper.getDailyNewUsers(yesterday);
            Long yesterdayNewProducts = statisticsMapper.getDailyNewProducts(yesterday);
            Long yesterdayOrders = statisticsMapper.getDailyOrders(yesterday);
            BigDecimal yesterdayRevenue = statisticsMapper.getDailyRevenue(yesterday);
            
            // 计算增长率
            Double userGrowthRate = calculateGrowthRate(todayNewUsers, yesterdayNewUsers);
            Double productGrowthRate = calculateGrowthRate(todayNewProducts, yesterdayNewProducts);
            Double orderGrowthRate = calculateGrowthRate(todayOrders, yesterdayOrders);
            Double revenueGrowthRate = calculateGrowthRate(todayRevenue, yesterdayRevenue);
            
            // 计算健康度指标
            Double orderCompletionRate = totalOrders > 0 ? 
                (completedOrders.doubleValue() / totalOrders.doubleValue()) * 100 : 0.0;
            Double userActiveRate = totalUsers > 0 ? 
                (activeUsers.doubleValue() / totalUsers.doubleValue()) * 100 : 0.0;
            Double productAvailableRate = totalProducts > 0 ? 
                (availableProducts.doubleValue() / totalProducts.doubleValue()) * 100 : 0.0;
            BigDecimal avgOrderAmount = totalOrders > 0 ?
                totalRevenue.divide(BigDecimal.valueOf(totalOrders), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            
            // 获取最近7天趋势数据
            List<String> last7Days = new ArrayList<>();
            List<Long> last7DaysUsers = new ArrayList<>();
            List<Long> last7DaysProducts = new ArrayList<>();
            List<Long> last7DaysOrders = new ArrayList<>();
            List<BigDecimal> last7DaysRevenue = new ArrayList<>();
            
            for (int i = 6; i >= 0; i--) {
                LocalDate date = today.minusDays(i);
                last7Days.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                last7DaysUsers.add(statisticsMapper.getDailyNewUsers(date));
                last7DaysProducts.add(statisticsMapper.getDailyNewProducts(date));
                last7DaysOrders.add(statisticsMapper.getDailyOrders(date));
                last7DaysRevenue.add(statisticsMapper.getDailyRevenue(date));
            }
            
            return OverviewStatisticsDTO.builder()
                    .totalUsers(totalUsers)
                    .activeUsers(activeUsers)
                    .totalProducts(totalProducts)
                    .availableProducts(availableProducts)
                    .totalOrders(totalOrders)
                    .completedOrders(completedOrders)
                    .totalRevenue(totalRevenue)
                    .todayNewUsers(todayNewUsers)
                    .todayNewProducts(todayNewProducts)
                    .todayOrders(todayOrders)
                    .todayRevenue(todayRevenue)
                    .yesterdayNewUsers(yesterdayNewUsers)
                    .yesterdayNewProducts(yesterdayNewProducts)
                    .yesterdayOrders(yesterdayOrders)
                    .yesterdayRevenue(yesterdayRevenue)
                    .userGrowthRate(userGrowthRate)
                    .productGrowthRate(productGrowthRate)
                    .orderGrowthRate(orderGrowthRate)
                    .revenueGrowthRate(revenueGrowthRate)
                    .orderCompletionRate(orderCompletionRate)
                    .userActiveRate(userActiveRate)
                    .productAvailableRate(productAvailableRate)
                    .avgOrderAmount(avgOrderAmount)
                    .last7Days(last7Days.toArray(new String[0]))
                    .last7DaysUsers(last7DaysUsers.toArray(new Long[0]))
                    .last7DaysProducts(last7DaysProducts.toArray(new Long[0]))
                    .last7DaysOrders(last7DaysOrders.toArray(new Long[0]))
                    .last7DaysRevenue(last7DaysRevenue.toArray(new BigDecimal[0]))
                    .build();
                    
        } catch (Exception e) {
            log.error("获取概览统计数据失败", e);
            throw new RuntimeException("获取概览统计数据失败", e);
        }
    }

    @Override
    // @Cacheable(value = "user-statistics", key = "#days", unless = "#result == null") // 暂时禁用缓存
    public UserStatisticsDTO getUserStatistics(Integer days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            // 获取真实的用户注册趋势数据
            List<String> registrationDates = new ArrayList<>();
            List<Long> registrationCounts = new ArrayList<>();
            List<Long> cumulativeUsers = new ArrayList<>();

            Long cumulative = 0L;
            try {
                // 获取基础累计用户数（截止到开始日期前）
                cumulative = statisticsMapper.getTotalUsers();
                if (cumulative == null) cumulative = 0L;

                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    registrationDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));

                    // 获取每日新增用户数
                    Long dailyCount = statisticsMapper.getDailyNewUsers(date);
                    if (dailyCount == null) dailyCount = 0L;

                    registrationCounts.add(dailyCount);
                    cumulative += dailyCount;
                    cumulativeUsers.add(cumulative);
                }
            } catch (Exception e) {
                log.warn("获取用户注册趋势数据失败，使用默认数据: {}", e.getMessage());
                // 使用默认数据
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    registrationDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                    registrationCounts.add(0L);
                    cumulativeUsers.add(cumulative);
                }
            }

            // 获取真实的用户角色分布
            Map<String, Long> roleDistribution = new HashMap<>();
            try {
                List<Map<String, Object>> roleDistributionList = statisticsMapper.getUserRoleDistribution();

                // 转换为Map<String, Long>格式
                for (Map<String, Object> item : roleDistributionList) {
                    String role = (String) item.get("role");
                    Long count = ((Number) item.get("count")).longValue();
                    roleDistribution.put(role, count);
                }
            } catch (Exception e) {
                log.warn("获取用户角色分布失败，使用默认数据: {}", e.getMessage());
                // 使用默认角色分布
                roleDistribution.put("general_user", 100L);
                roleDistribution.put("admin", 1L);
            }

            // 获取真实的用户状态分布
            Long activeUsers = 0L;
            Long totalUsers = 0L;
            Long inactiveUsers = 0L;
            try {
                activeUsers = statisticsMapper.getActiveUsers(30);
                totalUsers = statisticsMapper.getTotalUsers();
                if (activeUsers == null) activeUsers = 0L;
                if (totalUsers == null) totalUsers = 0L;
                inactiveUsers = totalUsers - activeUsers;
            } catch (Exception e) {
                log.warn("获取用户状态分布失败，使用默认数据: {}", e.getMessage());
                activeUsers = 50L;
                totalUsers = 100L;
                inactiveUsers = 50L;
            }

            // 获取真实的地域分布（Top 10）
            List<LocationStatisticsDTO> topLocations = new ArrayList<>();
            try {
                topLocations = statisticsMapper.getTopUserLocations(10);
                if (topLocations == null) topLocations = new ArrayList<>();
            } catch (Exception e) {
                log.warn("获取用户地域分布失败，使用默认数据: {}", e.getMessage());
                // 使用默认地域分布
                topLocations = Arrays.asList(
                    LocationStatisticsDTO.builder().location("未知").userCount(totalUsers).percentage(100.0).build()
                );
            }

            return UserStatisticsDTO.builder()
                    .registrationDates(registrationDates)
                    .registrationCounts(registrationCounts)
                    .cumulativeUsers(cumulativeUsers)
                    .roleDistribution(roleDistribution)
                    .activeUsers(activeUsers)
                    .inactiveUsers(inactiveUsers)
                    .topLocations(topLocations)
                    .build();

        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            throw new RuntimeException("获取用户统计数据失败", e);
        }
    }

    @Override
    public ProductStatisticsDTO getProductStatistics(Integer days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            // 获取商品发布趋势数据
            List<String> publishDates = new ArrayList<>();
            List<Long> publishCounts = new ArrayList<>();
            List<Long> cumulativeProducts = new ArrayList<>();

            Long cumulative = 0L;
            try {
                // 获取基础累计商品数
                cumulative = statisticsMapper.getTotalProducts();
                if (cumulative == null) cumulative = 0L;

                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    publishDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));

                    // 获取每日新增商品数
                    Long dailyCount = statisticsMapper.getDailyNewProducts(date);
                    if (dailyCount == null) dailyCount = 0L;

                    publishCounts.add(dailyCount);
                    cumulative += dailyCount;
                    cumulativeProducts.add(cumulative);
                }
            } catch (Exception e) {
                log.warn("获取商品发布趋势数据失败，使用默认数据: {}", e.getMessage());
                // 使用默认数据
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    publishDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                    publishCounts.add(0L);
                    cumulativeProducts.add(cumulative);
                }
            }

            // 获取商品状态分布
            Map<String, Long> statusDistribution = new HashMap<>();
            try {
                List<Map<String, Object>> statusDistributionList = statisticsMapper.getProductStatusDistribution();

                // 转换为Map<String, Long>格式
                for (Map<String, Object> item : statusDistributionList) {
                    String status = (String) item.get("status");
                    Long count = ((Number) item.get("count")).longValue();
                    statusDistribution.put(status, count);
                }
            } catch (Exception e) {
                log.warn("获取商品状态分布失败，使用默认数据: {}", e.getMessage());
                // 使用默认状态分布
                statusDistribution.put("available", 100L);
                statusDistribution.put("sold", 20L);
                statusDistribution.put("pending_review", 10L);
            }

            // 获取商品分类分布
            List<CategoryStatisticsDTO> categoryDistribution = new ArrayList<>();
            try {
                List<Map<String, Object>> categoryDistributionList = statisticsMapper.getProductCategoryDistribution();

                // 转换为CategoryStatisticsDTO格式
                for (Map<String, Object> item : categoryDistributionList) {
                    String categoryName = (String) item.get("categoryName");
                    Long productCount = ((Number) item.get("productCount")).longValue();
                    Double percentage = ((Number) item.get("percentage")).doubleValue();

                    CategoryStatisticsDTO categoryStats = CategoryStatisticsDTO.builder()
                            .categoryName(categoryName)
                            .productCount(productCount)
                            .percentage(percentage)
                            .avgPrice(BigDecimal.ZERO) // 暂时设为0，后续可以扩展
                            .soldRate(0.0) // 暂时设为0，后续可以扩展
                            .build();

                    categoryDistribution.add(categoryStats);
                }
            } catch (Exception e) {
                log.warn("获取商品分类分布失败: {}", e.getMessage());
                // 不使用默认数据，保持空列表
            }

            // 移除价格区间分布的模拟数据
            // 如果需要价格统计，应该通过SQL查询获取真实数据

            // 计算成交率
            Double soldRate = 0.0;
            try {
                Long totalProducts = statisticsMapper.getTotalProducts();
                Long soldProducts = statusDistribution.getOrDefault("sold", 0L);
                if (totalProducts != null && totalProducts > 0) {
                    soldRate = (soldProducts.doubleValue() / totalProducts.doubleValue()) * 100;
                }
            } catch (Exception e) {
                log.warn("计算商品成交率失败: {}", e.getMessage());
            }

            // 获取热门商品（按收藏量）
            List<HotProductDTO> hotProductsByFavorites = new ArrayList<>();
            List<HotProductDTO> hotProductsByViews = new ArrayList<>();
            List<HotProductDTO> hotProductsByOrders = new ArrayList<>();

            try {
                // 获取按收藏量排行的热门商品
                List<Map<String, Object>> favoriteProducts = statisticsMapper.getHotProductsByFavorites(10);
                for (Map<String, Object> item : favoriteProducts) {
                    HotProductDTO hotProduct = HotProductDTO.builder()
                            .productId(((Number) item.get("productId")).longValue())
                            .title((String) item.get("title"))
                            .categoryName((String) item.get("categoryName"))
                            .price((BigDecimal) item.get("price"))
                            .sellerName((String) item.get("sellerName"))
                            .value(((Number) item.get("value")).longValue())
                            .valueType("favorites")
                            .build();
                    hotProductsByFavorites.add(hotProduct);
                }

                // 获取按订单量排行的热门商品
                List<Map<String, Object>> orderProducts = statisticsMapper.getHotProductsByOrders(10);
                for (Map<String, Object> item : orderProducts) {
                    HotProductDTO hotProduct = HotProductDTO.builder()
                            .productId(((Number) item.get("productId")).longValue())
                            .title((String) item.get("title"))
                            .categoryName((String) item.get("categoryName"))
                            .price((BigDecimal) item.get("price"))
                            .sellerName((String) item.get("sellerName"))
                            .value(((Number) item.get("value")).longValue())
                            .valueType("orders")
                            .build();
                    hotProductsByOrders.add(hotProduct);
                }
            } catch (Exception e) {
                log.warn("获取热门商品失败: {}", e.getMessage());
            }

            return ProductStatisticsDTO.builder()
                    .publishDates(publishDates)
                    .publishCounts(publishCounts)
                    .cumulativeProducts(cumulativeProducts)
                    .statusDistribution(statusDistribution)
                    .categoryDistribution(categoryDistribution)
                    // 移除模拟的价格相关数据
                    .soldRate(soldRate)
                    .hotProductsByFavorites(hotProductsByFavorites)
                    .hotProductsByOrders(hotProductsByOrders)
                    .build();

        } catch (Exception e) {
            log.error("获取商品统计数据失败", e);
            throw new RuntimeException("获取商品统计数据失败", e);
        }
    }

    @Override
    public OrderStatisticsDTO getOrderStatistics(Integer days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            // 获取订单趋势数据
            List<String> orderDates = new ArrayList<>();
            List<Long> orderCounts = new ArrayList<>();
            List<BigDecimal> orderAmounts = new ArrayList<>();
            List<Long> cumulativeOrders = new ArrayList<>();

            Long cumulative = 0L;
            try {
                // 获取基础累计订单数
                cumulative = statisticsMapper.getTotalOrders();
                if (cumulative == null) cumulative = 0L;

                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    orderDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));

                    // 获取每日订单数和交易额
                    Long dailyCount = statisticsMapper.getDailyOrders(date);
                    BigDecimal dailyAmount = statisticsMapper.getDailyRevenue(date);

                    if (dailyCount == null) dailyCount = 0L;
                    if (dailyAmount == null) dailyAmount = BigDecimal.ZERO;

                    orderCounts.add(dailyCount);
                    orderAmounts.add(dailyAmount);
                    cumulative += dailyCount;
                    cumulativeOrders.add(cumulative);
                }
            } catch (Exception e) {
                log.warn("获取订单趋势数据失败，使用默认数据: {}", e.getMessage());
                // 使用默认数据
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    orderDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                    orderCounts.add(0L);
                    orderAmounts.add(BigDecimal.ZERO);
                    cumulativeOrders.add(cumulative);
                }
            }

            // 获取订单状态分布
            Map<String, Long> statusDistribution = new HashMap<>();
            try {
                List<Map<String, Object>> statusDistributionList = statisticsMapper.getOrderStatusDistribution();

                // 转换为Map<String, Long>格式
                for (Map<String, Object> item : statusDistributionList) {
                    String status = (String) item.get("status");
                    Long count = ((Number) item.get("count")).longValue();
                    statusDistribution.put(status, count);
                }
            } catch (Exception e) {
                log.warn("获取订单状态分布失败，使用默认数据: {}", e.getMessage());
                // 使用默认状态分布
                statusDistribution.put("pending_payment", 50L);
                statusDistribution.put("paid", 200L);
                statusDistribution.put("shipped", 150L);
                statusDistribution.put("delivered", 300L);
                statusDistribution.put("completed", 250L);
                statusDistribution.put("cancelled", 30L);
                statusDistribution.put("refunded", 20L);
            }

            // 获取交易金额统计
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal avgOrderAmount = BigDecimal.ZERO;
            BigDecimal maxOrderAmount = BigDecimal.ZERO;
            BigDecimal minOrderAmount = BigDecimal.ZERO;
            BigDecimal todayAmount = BigDecimal.ZERO;
            BigDecimal yesterdayAmount = BigDecimal.ZERO;

            try {
                totalAmount = statisticsMapper.getTotalRevenue();
                if (totalAmount == null) totalAmount = BigDecimal.ZERO;

                todayAmount = statisticsMapper.getDailyRevenue(LocalDate.now());
                if (todayAmount == null) todayAmount = BigDecimal.ZERO;

                yesterdayAmount = statisticsMapper.getDailyRevenue(LocalDate.now().minusDays(1));
                if (yesterdayAmount == null) yesterdayAmount = BigDecimal.ZERO;

                Long totalOrders = statisticsMapper.getTotalOrders();
                if (totalOrders != null && totalOrders > 0) {
                    avgOrderAmount = totalAmount.divide(BigDecimal.valueOf(totalOrders), 2, RoundingMode.HALF_UP);
                }

                // 移除模拟的最大最小订单金额
                // 如果需要这些数据，应该通过SQL查询获取真实数据

            } catch (Exception e) {
                log.warn("获取交易金额统计失败: {}", e.getMessage());
            }

            // 计算订单完成率、取消率、退款率
            Double completionRate = 0.0;
            Double cancellationRate = 0.0;
            Double refundRate = 0.0;

            try {
                Long totalOrders = statusDistribution.values().stream().mapToLong(Long::longValue).sum();
                if (totalOrders > 0) {
                    Long completedOrders = statusDistribution.getOrDefault("completed", 0L);
                    Long cancelledOrders = statusDistribution.getOrDefault("cancelled", 0L);
                    Long refundedOrders = statusDistribution.getOrDefault("refunded", 0L);

                    completionRate = (completedOrders.doubleValue() / totalOrders.doubleValue()) * 100;
                    cancellationRate = (cancelledOrders.doubleValue() / totalOrders.doubleValue()) * 100;
                    refundRate = (refundedOrders.doubleValue() / totalOrders.doubleValue()) * 100;
                }
            } catch (Exception e) {
                log.warn("计算订单率失败: {}", e.getMessage());
            }

            // 移除支付方式分布和订单金额区间分布的模拟数据
            // 如果需要这些数据，应该通过扩展数据库表结构来获取真实数据

            // 获取热门商品（按订单量）
            List<HotProductDTO> hotProductsByOrders = new ArrayList<>();
            try {
                List<Map<String, Object>> orderProducts = statisticsMapper.getHotProductsByOrders(10);
                for (Map<String, Object> item : orderProducts) {
                    HotProductDTO hotProduct = HotProductDTO.builder()
                            .productId(((Number) item.get("productId")).longValue())
                            .title((String) item.get("title"))
                            .categoryName((String) item.get("categoryName"))
                            .price((BigDecimal) item.get("price"))
                            .sellerName((String) item.get("sellerName"))
                            .value(((Number) item.get("value")).longValue())
                            .valueType("orders")
                            .build();
                    hotProductsByOrders.add(hotProduct);
                }
            } catch (Exception e) {
                log.warn("获取热门商品失败: {}", e.getMessage());
            }

            // 移除活跃买家卖家排行、24小时分布和地域分布的模拟数据
            // 如果需要这些数据，应该通过复杂的SQL查询获取真实数据

            return OrderStatisticsDTO.builder()
                    .orderDates(orderDates)
                    .orderCounts(orderCounts)
                    .orderAmounts(orderAmounts)
                    .cumulativeOrders(cumulativeOrders)
                    .statusDistribution(statusDistribution)
                    .totalAmount(totalAmount)
                    .avgOrderAmount(avgOrderAmount)
                    // 移除模拟的最大最小订单金额
                    .todayAmount(todayAmount)
                    .yesterdayAmount(yesterdayAmount)
                    .completionRate(completionRate)
                    .cancellationRate(cancellationRate)
                    .refundRate(refundRate)
                    // 移除模拟的支付方式分布、金额区间分布、活跃买家卖家、时间分布、地域分布
                    .hotProductsByOrders(hotProductsByOrders)
                    .build();

        } catch (Exception e) {
            log.error("获取订单统计数据失败", e);
            throw new RuntimeException("获取订单统计数据失败", e);
        }
    }

    @Override
    public ForumStatisticsDTO getForumStatistics(Integer days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            // 获取论坛活跃度趋势数据
            List<String> activityDates = new ArrayList<>();
            List<Long> postCounts = new ArrayList<>();
            List<Long> commentCounts = new ArrayList<>();
            List<Long> viewCounts = new ArrayList<>();

            try {
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    activityDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));

                    // 获取每日帖子数量
                    Long dailyPosts = statisticsMapper.getDailyNewForumPosts(date);
                    if (dailyPosts == null) dailyPosts = 0L;
                    postCounts.add(dailyPosts);

                    // 获取每日评论数量
                    Long dailyComments = statisticsMapper.getDailyNewForumComments(date);
                    if (dailyComments == null) dailyComments = 0L;
                    commentCounts.add(dailyComments);

                    // 获取每日浏览量（真实数据）
                    Long dailyViews = statisticsMapper.getDailyForumViews(date);
                    if (dailyViews == null) dailyViews = 0L;
                    viewCounts.add(dailyViews);
                }
            } catch (Exception e) {
                log.warn("获取论坛活跃度趋势数据失败，使用默认数据: {}", e.getMessage());
                // 使用默认数据
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    activityDates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
                    postCounts.add(0L);
                    commentCounts.add(0L);
                    viewCounts.add(0L);
                }
            }

            // 获取论坛基础统计数据
            Long totalPosts = 0L;
            Long totalComments = 0L;
            Long totalViews = 0L;
            Long activeUserCount = 0L;

            try {
                totalPosts = statisticsMapper.getTotalForumPosts();
                if (totalPosts == null) totalPosts = 0L;

                totalComments = statisticsMapper.getTotalForumComments();
                if (totalComments == null) totalComments = 0L;

                // 获取真实的总浏览量
                totalViews = statisticsMapper.getTotalForumViews();
                if (totalViews == null) totalViews = 0L;

                // 获取真实的活跃用户数
                activeUserCount = statisticsMapper.getActiveForumUsers();
                if (activeUserCount == null) activeUserCount = 0L;

            } catch (Exception e) {
                log.warn("获取论坛基础统计数据失败: {}", e.getMessage());
            }

            // 计算平均值
            Double avgPostsPerUser = activeUserCount > 0 ? totalPosts.doubleValue() / activeUserCount.doubleValue() : 0.0;
            Double avgCommentsPerUser = activeUserCount > 0 ? totalComments.doubleValue() / activeUserCount.doubleValue() : 0.0;
            Double avgCommentsPerPost = totalPosts > 0 ? totalComments.doubleValue() / totalPosts.doubleValue() : 0.0;
            Double avgViewsPerPost = totalPosts > 0 ? totalViews.doubleValue() / totalPosts.doubleValue() : 0.0;

            // 计算用户参与率（需要总用户数）
            Double userParticipationRate = 0.0;
            try {
                Long totalUsers = statisticsMapper.getTotalUsers();
                if (totalUsers != null && totalUsers > 0) {
                    userParticipationRate = (activeUserCount.doubleValue() / totalUsers.doubleValue()) * 100;
                }
            } catch (Exception e) {
                log.warn("计算用户参与率失败: {}", e.getMessage());
            }

            // 获取真实的论坛分类统计
            List<ForumCategoryStatisticsDTO> categoryStatistics = new ArrayList<>();
            try {
                List<Map<String, Object>> categoryData = statisticsMapper.getForumCategoryStatistics();
                for (Map<String, Object> item : categoryData) {
                    Long postCount = ((Number) item.get("postCount")).longValue();
                    Long commentCount = ((Number) item.get("commentCount")).longValue();

                    ForumCategoryStatisticsDTO categoryStats = ForumCategoryStatisticsDTO.builder()
                            .categoryId(((Number) item.get("categoryId")).intValue())
                            .categoryName((String) item.get("categoryName"))
                            .postCount(postCount)
                            .commentCount(commentCount)
                            .viewCount(((Number) item.get("viewCount")).longValue())
                            .percentage(((Number) item.get("percentage")).doubleValue())
                            .avgCommentsPerPost(postCount > 0 ? commentCount.doubleValue() / postCount.doubleValue() : 0.0)
                            .build();
                    categoryStatistics.add(categoryStats);
                }
            } catch (Exception e) {
                log.warn("获取论坛分类统计失败: {}", e.getMessage());
            }

            // 获取真实的热门帖子排行
            List<HotPostDTO> hotPostsByViews = new ArrayList<>();
            List<HotPostDTO> hotPostsByComments = new ArrayList<>();

            try {
                // 按浏览量排行
                List<Map<String, Object>> viewsData = statisticsMapper.getHotPostsByViews(10);
                for (Map<String, Object> item : viewsData) {
                    HotPostDTO hotPost = HotPostDTO.builder()
                            .postId(((Number) item.get("postId")).longValue())
                            .title((String) item.get("title"))
                            .categoryName((String) item.get("categoryName"))
                            .authorName((String) item.get("authorName"))
                            .viewCount(((Number) item.get("viewCount")).longValue())
                            .commentCount(((Number) item.get("commentCount")).longValue())
                            .postedAt(item.get("postedAt").toString())
                            .build();
                    hotPostsByViews.add(hotPost);
                }

                // 按评论数排行
                List<Map<String, Object>> commentsData = statisticsMapper.getHotPostsByComments(10);
                for (Map<String, Object> item : commentsData) {
                    HotPostDTO hotPost = HotPostDTO.builder()
                            .postId(((Number) item.get("postId")).longValue())
                            .title((String) item.get("title"))
                            .categoryName((String) item.get("categoryName"))
                            .authorName((String) item.get("authorName"))
                            .viewCount(((Number) item.get("viewCount")).longValue())
                            .commentCount(((Number) item.get("commentCount")).longValue())
                            .postedAt(item.get("postedAt").toString())
                            .build();
                    hotPostsByComments.add(hotPost);
                }
            } catch (Exception e) {
                log.warn("获取热门帖子排行失败: {}", e.getMessage());
            }

            // 获取真实的帖子状态分布
            Map<String, Long> postStatusDistribution = new HashMap<>();
            try {
                List<Map<String, Object>> statusData = statisticsMapper.getForumPostStatusDistribution();
                for (Map<String, Object> item : statusData) {
                    String status = item.get("status").toString();
                    Long count = ((Number) item.get("count")).longValue();

                    // 转换状态码为可读名称
                    String statusName;
                    switch (status) {
                        case "0":
                            statusName = "published";
                            break;
                        case "1":
                            statusName = "off_shelf";
                            break;
                        default:
                            statusName = "unknown";
                            break;
                    }
                    postStatusDistribution.put(statusName, count);
                }
            } catch (Exception e) {
                log.warn("获取帖子状态分布失败: {}", e.getMessage());
            }

            return ForumStatisticsDTO.builder()
                    .activityDates(activityDates)
                    .postCounts(postCounts)
                    .commentCounts(commentCounts)
                    .viewCounts(viewCounts)
                    .categoryStatistics(categoryStatistics)
                    .totalPosts(totalPosts)
                    .totalComments(totalComments)
                    .totalViews(totalViews)
                    .activeUserCount(activeUserCount)
                    .avgPostsPerUser(avgPostsPerUser)
                    .avgCommentsPerUser(avgCommentsPerUser)
                    .hotPostsByViews(hotPostsByViews)
                    .hotPostsByComments(hotPostsByComments)
                    .postStatusDistribution(postStatusDistribution)
                    .userParticipationRate(userParticipationRate)
                    .avgCommentsPerPost(avgCommentsPerPost)
                    .avgViewsPerPost(avgViewsPerPost)
                    // 移除模拟的24小时分布数据和活跃用户列表
                    .build();

        } catch (Exception e) {
            log.error("获取论坛统计数据失败", e);
            throw new RuntimeException("获取论坛统计数据失败", e);
        }
    }

    @Override
    public List<HotProductDTO> getHotProducts(Integer limit, String type) {
        try {
            List<HotProductDTO> hotProducts = new ArrayList<>();

            switch (type.toLowerCase()) {
                case "favorites":
                    // 按收藏量排行
                    try {
                        List<Map<String, Object>> favoriteProducts = statisticsMapper.getHotProductsByFavorites(limit);
                        for (Map<String, Object> item : favoriteProducts) {
                            HotProductDTO hotProduct = HotProductDTO.builder()
                                    .productId(((Number) item.get("productId")).longValue())
                                    .title((String) item.get("title"))
                                    .categoryName((String) item.get("categoryName"))
                                    .price((BigDecimal) item.get("price"))
                                    .sellerName((String) item.get("sellerName"))
                                    .value(((Number) item.get("value")).longValue())
                                    .valueType("favorites")
                                    .build();
                            hotProducts.add(hotProduct);
                        }
                    } catch (Exception e) {
                        log.warn("获取按收藏量排行的热门商品失败: {}", e.getMessage());
                        // 不返回模拟数据，保持空列表
                        hotProducts = new ArrayList<>();
                    }
                    break;

                case "orders":
                    // 按订单量排行
                    try {
                        List<Map<String, Object>> orderProducts = statisticsMapper.getHotProductsByOrders(limit);
                        for (Map<String, Object> item : orderProducts) {
                            HotProductDTO hotProduct = HotProductDTO.builder()
                                    .productId(((Number) item.get("productId")).longValue())
                                    .title((String) item.get("title"))
                                    .categoryName((String) item.get("categoryName"))
                                    .price((BigDecimal) item.get("price"))
                                    .sellerName((String) item.get("sellerName"))
                                    .value(((Number) item.get("value")).longValue())
                                    .valueType("orders")
                                    .build();
                            hotProducts.add(hotProduct);
                        }
                    } catch (Exception e) {
                        log.warn("获取按订单量排行的热门商品失败: {}", e.getMessage());
                        // 不返回模拟数据，保持空列表
                        hotProducts = new ArrayList<>();
                    }
                    break;

                case "views":
                default:
                    // 按浏览量排行功能暂不支持，返回空列表
                    log.info("按浏览量排行功能暂不支持，返回空列表");
                    hotProducts = new ArrayList<>();
                    break;
            }

            return hotProducts;

        } catch (Exception e) {
            log.error("获取热门商品排行失败", e);
            // 不返回模拟数据，返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public List<LocationStatisticsDTO> getUserLocationStatistics() {
        try {
            List<LocationStatisticsDTO> locationStatistics = new ArrayList<>();

            try {
                // 从数据库获取真实的地域分布数据
                locationStatistics = statisticsMapper.getTopUserLocations(20); // 获取Top 20

                if (locationStatistics == null || locationStatistics.isEmpty()) {
                    log.warn("数据库中没有地域分布数据");
                    locationStatistics = new ArrayList<>();
                }

            } catch (Exception e) {
                log.warn("获取用户地域分布失败: {}", e.getMessage());
                locationStatistics = new ArrayList<>();
            }

            return locationStatistics;

        } catch (Exception e) {
            log.error("获取用户地域分布统计失败", e);
            // 不返回模拟数据，返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public RealtimeStatisticsDTO getRealtimeStatistics() {
        try {
            LocalDate today = LocalDate.now();
            
            return RealtimeStatisticsDTO.builder()
                    .todayNewUsers(statisticsMapper.getDailyNewUsers(today))
                    .todayNewProducts(statisticsMapper.getDailyNewProducts(today))
                    .todayOrders(statisticsMapper.getDailyOrders(today))
                    .todayRevenue(statisticsMapper.getDailyRevenue(today))
                    .updateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .build();
        } catch (Exception e) {
            log.error("获取实时统计数据失败", e);
            throw new RuntimeException("获取实时统计数据失败", e);
        }
    }

    @Override
    public Map<String, Object> getCustomStatistics(LocalDate startDate, LocalDate endDate, String type) {
        // 实现自定义统计逻辑
        return new HashMap<>();
    }

    /**
     * 计算增长率
     */
    private Double calculateGrowthRate(Number current, Number previous) {
        if (previous == null || previous.doubleValue() == 0) {
            return current != null && current.doubleValue() > 0 ? 100.0 : 0.0;
        }
        if (current == null) {
            return -100.0;
        }
        return ((current.doubleValue() - previous.doubleValue()) / previous.doubleValue()) * 100;
    }

    /**
     * 计算增长率（BigDecimal版本）
     */
    private Double calculateGrowthRate(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            return current != null && current.compareTo(BigDecimal.ZERO) > 0 ? 100.0 : 0.0;
        }
        if (current == null) {
            return -100.0;
        }
        return current.subtract(previous)
                .divide(previous, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }






}
