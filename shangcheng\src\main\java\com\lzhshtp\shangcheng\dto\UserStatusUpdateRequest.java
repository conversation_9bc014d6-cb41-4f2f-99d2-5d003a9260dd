package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户状态更新请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserStatusUpdateRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户状态（true-启用，false-禁用）
     */
    @NotNull(message = "用户状态不能为空")
    private Boolean isActive;
} 