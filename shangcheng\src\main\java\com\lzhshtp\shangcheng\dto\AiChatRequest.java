package com.lzhshtp.shangcheng.dto;

import com.lzhshtp.shangcheng.model.AiMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * AI聊天请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiChatRequest {

    /**
     * 会话ID，如果为空则创建新会话
     */
    private String sessionId;

    /**
     * 会话名称，创建新会话时使用
     */
    private String sessionName;

    /**
     * 用户消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;

    /**
     * 媒体内容列表（图片、语音等）
     */
    private List<AiMessage.MediaContent> medias;

    /**
     * 聊天参数配置
     */
    @NotNull(message = "聊天参数不能为空")
    private ChatParams params;

    /**
     * 上下文数据（如当前商品ID、订单ID等）
     */
    private Map<String, Object> contextData;

    /**
     * 是否使用RAG模式
     */
    public boolean isUseRAG() {
        return params != null && params.getEnableRag() != null && params.getEnableRag();
    }

    /**
     * 聊天参数配置类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChatParams {

        /**
         * 是否启用Agent功能
         */
        @Builder.Default
        private Boolean enableAgent = false;

        /**
         * 是否启用RAG检索增强
         */
        @Builder.Default
        private Boolean enableRag = false;

        /**
         * 是否启用记忆功能
         */
        @Builder.Default
        private Boolean enableMemory = true;

        /**
         * 携带的历史消息数量
         */
        @Builder.Default
        private Integer historyMessageCount = 10;

        /**
         * AI模型参数
         */
        private ModelOptions modelOptions;
    }

    /**
     * AI模型参数配置类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelOptions {

        /**
         * 温度参数，控制回复的随机性 (0.0-1.0)
         */
        @Builder.Default
        private Double temperature = 0.7;

        /**
         * 最大token数量
         */
        @Builder.Default
        private Integer maxTokens = 2000;

        /**
         * Top-p参数
         */
        @Builder.Default
        private Double topP = 0.9;

        /**
         * 频率惩罚
         */
        @Builder.Default
        private Double frequencyPenalty = 0.0;

        /**
         * 存在惩罚
         */
        @Builder.Default
        private Double presencePenalty = 0.0;
    }
}
