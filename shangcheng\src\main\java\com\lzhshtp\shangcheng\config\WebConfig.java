package com.lzhshtp.shangcheng.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类，处理CORS跨域问题
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 全局CORS配置
     * @param registry CORS注册表
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")  // 在生产环境中，应该限制为特定的域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)  // 由于allowedOrigins设置为*，allowCredentials必须为false
                .maxAge(3600);  // 预检请求的有效期，单位为秒
    }

    // CORS配置已移至SecurityConfig中统一管理
    // 避免重复配置导致冲突
} 