package io.github.qifan777.knowledge.ai.session.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import io.github.qifan777.knowledge.ai.session.AiSession;
import io.github.qifan777.knowledge.ai.session.AiSessionDraft;
import io.github.qifan777.knowledge.ai.session.AiSessionFetcher;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.util.Objects;
import org.babyfish.jimmer.Input;
import org.babyfish.jimmer.impl.util.DtoPropAccessor;
import org.babyfish.jimmer.internal.FixedInputField;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.fetcher.ViewMetadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 会话
 */
@GeneratedBy(
        file = "<dive-into-spring-ai>/src/main/dto/AiSession.dto"
)
@JsonDeserialize(
        builder = AiSessionInput.Builder.class
)
public class AiSessionInput implements Input<AiSession> {
    public static final ViewMetadata<AiSession, AiSessionInput> METADATA = 
        new ViewMetadata<AiSession, AiSessionInput>(
            AiSessionFetcher.$
                .name(),
            AiSessionInput::new
    );

    private static final DtoPropAccessor ID_ACCESSOR = new DtoPropAccessor(
        false,
        new int[] { AiSessionDraft.Producer.SLOT_ID }
    );

    private String id;

    @FixedInputField
    private String name;

    public AiSessionInput() {
    }

    public AiSessionInput(@NotNull AiSession base) {
        this.id = ID_ACCESSOR.get(base);
        this.name = base.name();
    }

    @Nullable
    public String getId() {
        return id;
    }

    public void setId(@Nullable String id) {
        this.id = id;
    }

    /**
     * 会话名称
     */
    @NotNull
    public String getName() {
        if (name == null) {
            throw new IllegalStateException("The property \"name\" is not specified");
        }
        return name;
    }

    public void setName(@NotNull String name) {
        this.name = name;
    }

    @Override
    public AiSession toEntity() {
        return AiSessionDraft.$.produce(__draft -> {
            ID_ACCESSOR.set(__draft, id);
            __draft.setName(name);
        });
    }

    @Override
    public int hashCode() {
        int hash = Objects.hashCode(id);
        hash = hash * 31 + Objects.hashCode(name);
        return hash;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        AiSessionInput other = (AiSessionInput) o;
        if (!Objects.equals(id, other.id)) {
            return false;
        }
        if (!Objects.equals(name, other.name)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("AiSessionInput").append('(');
        builder.append("id=").append(id);
        builder.append(", name=").append(name);
        builder.append(')');
        return builder.toString();
    }

    @JsonPOJOBuilder(
            withPrefix = ""
    )
    public static class Builder {
        private String id;

        private String name;

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder name(String name) {
            this.name = Objects.requireNonNull(name, "The property \"name\" cannot be null");
            return this;
        }

        public AiSessionInput build() {
            AiSessionInput _input = new AiSessionInput();
            _input.setId(id);
            if (name == null) {
                throw Input.unknownNonNullProperty(AiSessionInput.class, "name");
            }
            _input.setName(name);
            return _input;
        }
    }
}
