{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/perl.js"], "sourcesContent": ["\n// it's like \"peek\", but need for look-ahead or look-behind if index < 0\nfunction look(stream, c){\n  return stream.string.charAt(stream.pos+(c||0));\n}\n\n// return a part of prefix of current stream from current position\nfunction prefix(stream, c){\n  if(c){\n    var x=stream.pos-c;\n    return stream.string.substr((x>=0?x:0),c);}\n  else{\n    return stream.string.substr(0,stream.pos-1);\n  }\n}\n\n// return a part of suffix of current stream from current position\nfunction suffix(stream, c){\n  var y=stream.string.length;\n  var x=y-stream.pos+1;\n  return stream.string.substr(stream.pos,(c&&c<y?c:x));\n}\n\n// eating and vomiting a part of stream from current position\nfunction eatSuffix(stream, c){\n  var x=stream.pos+c;\n  var y;\n  if(x<=0)\n    stream.pos=0;\n  else if(x>=(y=stream.string.length-1))\n    stream.pos=y;\n  else\n    stream.pos=x;\n}\n\n// http://perldoc.perl.org\nvar PERL={                                      //   null - magic touch\n  //   1 - keyword\n  //   2 - def\n  //   3 - atom\n  //   4 - operator\n  //   5 - builtin (predefined)\n  //   [x,y] - x=1,2,3; y=must be defined if x{...}\n  //      PERL operators\n  '->':   4,\n  '++':   4,\n  '--':   4,\n  '**':   4,\n  //   ! ~ \\ and unary + and -\n  '=~':   4,\n  '!~':   4,\n  '*':   4,\n  '/':   4,\n  '%':   4,\n  'x':   4,\n  '+':   4,\n  '-':   4,\n  '.':   4,\n  '<<':   4,\n  '>>':   4,\n  //   named unary operators\n  '<':   4,\n  '>':   4,\n  '<=':   4,\n  '>=':   4,\n  'lt':   4,\n  'gt':   4,\n  'le':   4,\n  'ge':   4,\n  '==':   4,\n  '!=':   4,\n  '<=>':   4,\n  'eq':   4,\n  'ne':   4,\n  'cmp':   4,\n  '~~':   4,\n  '&':   4,\n  '|':   4,\n  '^':   4,\n  '&&':   4,\n  '||':   4,\n  '//':   4,\n  '..':   4,\n  '...':   4,\n  '?':   4,\n  ':':   4,\n  '=':   4,\n  '+=':   4,\n  '-=':   4,\n  '*=':   4,  //   etc. ???\n  ',':   4,\n  '=>':   4,\n  '::':   4,\n  //   list operators (rightward)\n  'not':   4,\n  'and':   4,\n  'or':   4,\n  'xor':   4,\n  //      PERL predefined variables (I know, what this is a paranoid idea, but may be needed for people, who learn PERL, and for me as well, ...and may be for you?;)\n  'BEGIN':   [5,1],\n  'END':   [5,1],\n  'PRINT':   [5,1],\n  'PRINTF':   [5,1],\n  'GETC':   [5,1],\n  'READ':   [5,1],\n  'READLINE':   [5,1],\n  'DESTROY':   [5,1],\n  'TIE':   [5,1],\n  'TIEHANDLE':   [5,1],\n  'UNTIE':   [5,1],\n  'STDIN':    5,\n  'STDIN_TOP':    5,\n  'STDOUT':    5,\n  'STDOUT_TOP':    5,\n  'STDERR':    5,\n  'STDERR_TOP':    5,\n  '$ARG':    5,\n  '$_':    5,\n  '@ARG':    5,\n  '@_':    5,\n  '$LIST_SEPARATOR':    5,\n  '$\"':    5,\n  '$PROCESS_ID':    5,\n  '$PID':    5,\n  '$$':    5,\n  '$REAL_GROUP_ID':    5,\n  '$GID':    5,\n  '$(':    5,\n  '$EFFECTIVE_GROUP_ID':    5,\n  '$EGID':    5,\n  '$)':    5,\n  '$PROGRAM_NAME':    5,\n  '$0':    5,\n  '$SUBSCRIPT_SEPARATOR':    5,\n  '$SUBSEP':    5,\n  '$;':    5,\n  '$REAL_USER_ID':    5,\n  '$UID':    5,\n  '$<':    5,\n  '$EFFECTIVE_USER_ID':    5,\n  '$EUID':    5,\n  '$>':    5,\n  '$a':    5,\n  '$b':    5,\n  '$COMPILING':    5,\n  '$^C':    5,\n  '$DEBUGGING':    5,\n  '$^D':    5,\n  '${^ENCODING}':    5,\n  '$ENV':    5,\n  '%ENV':    5,\n  '$SYSTEM_FD_MAX':    5,\n  '$^F':    5,\n  '@F':    5,\n  '${^GLOBAL_PHASE}':    5,\n  '$^H':    5,\n  '%^H':    5,\n  '@INC':    5,\n  '%INC':    5,\n  '$INPLACE_EDIT':    5,\n  '$^I':    5,\n  '$^M':    5,\n  '$OSNAME':    5,\n  '$^O':    5,\n  '${^OPEN}':    5,\n  '$PERLDB':    5,\n  '$^P':    5,\n  '$SIG':    5,\n  '%SIG':    5,\n  '$BASETIME':    5,\n  '$^T':    5,\n  '${^TAINT}':    5,\n  '${^UNICODE}':    5,\n  '${^UTF8CACHE}':    5,\n  '${^UTF8LOCALE}':    5,\n  '$PERL_VERSION':    5,\n  '$^V':    5,\n  '${^WIN32_SLOPPY_STAT}':    5,\n  '$EXECUTABLE_NAME':    5,\n  '$^X':    5,\n  '$1':    5, // - regexp $1, $2...\n  '$MATCH':    5,\n  '$&':    5,\n  '${^MATCH}':    5,\n  '$PREMATCH':    5,\n  '$`':    5,\n  '${^PREMATCH}':    5,\n  '$POSTMATCH':    5,\n  \"$'\":    5,\n  '${^POSTMATCH}':    5,\n  '$LAST_PAREN_MATCH':    5,\n  '$+':    5,\n  '$LAST_SUBMATCH_RESULT':    5,\n  '$^N':    5,\n  '@LAST_MATCH_END':    5,\n  '@+':    5,\n  '%LAST_PAREN_MATCH':    5,\n  '%+':    5,\n  '@LAST_MATCH_START':    5,\n  '@-':    5,\n  '%LAST_MATCH_START':    5,\n  '%-':    5,\n  '$LAST_REGEXP_CODE_RESULT':    5,\n  '$^R':    5,\n  '${^RE_DEBUG_FLAGS}':    5,\n  '${^RE_TRIE_MAXBUF}':    5,\n  '$ARGV':    5,\n  '@ARGV':    5,\n  'ARGV':    5,\n  'ARGVOUT':    5,\n  '$OUTPUT_FIELD_SEPARATOR':    5,\n  '$OFS':    5,\n  '$,':    5,\n  '$INPUT_LINE_NUMBER':    5,\n  '$NR':    5,\n  '$.':    5,\n  '$INPUT_RECORD_SEPARATOR':    5,\n  '$RS':    5,\n  '$/':    5,\n  '$OUTPUT_RECORD_SEPARATOR':    5,\n  '$ORS':    5,\n  '$\\\\':    5,\n  '$OUTPUT_AUTOFLUSH':    5,\n  '$|':    5,\n  '$ACCUMULATOR':    5,\n  '$^A':    5,\n  '$FORMAT_FORMFEED':    5,\n  '$^L':    5,\n  '$FORMAT_PAGE_NUMBER':    5,\n  '$%':    5,\n  '$FORMAT_LINES_LEFT':    5,\n  '$-':    5,\n  '$FORMAT_LINE_BREAK_CHARACTERS':    5,\n  '$:':    5,\n  '$FORMAT_LINES_PER_PAGE':    5,\n  '$=':    5,\n  '$FORMAT_TOP_NAME':    5,\n  '$^':    5,\n  '$FORMAT_NAME':    5,\n  '$~':    5,\n  '${^CHILD_ERROR_NATIVE}':    5,\n  '$EXTENDED_OS_ERROR':    5,\n  '$^E':    5,\n  '$EXCEPTIONS_BEING_CAUGHT':    5,\n  '$^S':    5,\n  '$WARNING':    5,\n  '$^W':    5,\n  '${^WARNING_BITS}':    5,\n  '$OS_ERROR':    5,\n  '$ERRNO':    5,\n  '$!':    5,\n  '%OS_ERROR':    5,\n  '%ERRNO':    5,\n  '%!':    5,\n  '$CHILD_ERROR':    5,\n  '$?':    5,\n  '$EVAL_ERROR':    5,\n  '$@':    5,\n  '$OFMT':    5,\n  '$#':    5,\n  '$*':    5,\n  '$ARRAY_BASE':    5,\n  '$[':    5,\n  '$OLD_PERL_VERSION':    5,\n  '$]':    5,\n  //      PERL blocks\n  'if':[1,1],\n  elsif:[1,1],\n  'else':[1,1],\n  'while':[1,1],\n  unless:[1,1],\n  'for':[1,1],\n  foreach:[1,1],\n  //      PERL functions\n  'abs':1,     // - absolute value function\n  accept:1,     // - accept an incoming socket connect\n  alarm:1,     // - schedule a SIGALRM\n  'atan2':1,     // - arctangent of Y/X in the range -PI to PI\n  bind:1,     // - binds an address to a socket\n  binmode:1,     // - prepare binary files for I/O\n  bless:1,     // - create an object\n  bootstrap:1,     //\n  'break':1,     // - break out of a \"given\" block\n  caller:1,     // - get context of the current subroutine call\n  chdir:1,     // - change your current working directory\n  chmod:1,     // - changes the permissions on a list of files\n  chomp:1,     // - remove a trailing record separator from a string\n  chop:1,     // - remove the last character from a string\n  chown:1,     // - change the ownership on a list of files\n  chr:1,     // - get character this number represents\n  chroot:1,     // - make directory new root for path lookups\n  close:1,     // - close file (or pipe or socket) handle\n  closedir:1,     // - close directory handle\n  connect:1,     // - connect to a remote socket\n  'continue':[1,1], // - optional trailing block in a while or foreach\n  'cos':1,     // - cosine function\n  crypt:1,     // - one-way passwd-style encryption\n  dbmclose:1,     // - breaks binding on a tied dbm file\n  dbmopen:1,     // - create binding on a tied dbm file\n  'default':1,     //\n  defined:1,     // - test whether a value, variable, or function is defined\n  'delete':1,     // - deletes a value from a hash\n  die:1,     // - raise an exception or bail out\n  'do':1,     // - turn a BLOCK into a TERM\n  dump:1,     // - create an immediate core dump\n  each:1,     // - retrieve the next key/value pair from a hash\n  endgrent:1,     // - be done using group file\n  endhostent:1,     // - be done using hosts file\n  endnetent:1,     // - be done using networks file\n  endprotoent:1,     // - be done using protocols file\n  endpwent:1,     // - be done using passwd file\n  endservent:1,     // - be done using services file\n  eof:1,     // - test a filehandle for its end\n  'eval':1,     // - catch exceptions or compile and run code\n  'exec':1,     // - abandon this program to run another\n  exists:1,     // - test whether a hash key is present\n  exit:1,     // - terminate this program\n  'exp':1,     // - raise I to a power\n  fcntl:1,     // - file control system call\n  fileno:1,     // - return file descriptor from filehandle\n  flock:1,     // - lock an entire file with an advisory lock\n  fork:1,     // - create a new process just like this one\n  format:1,     // - declare a picture format with use by the write() function\n  formline:1,     // - internal function used for formats\n  getc:1,     // - get the next character from the filehandle\n  getgrent:1,     // - get next group record\n  getgrgid:1,     // - get group record given group user ID\n  getgrnam:1,     // - get group record given group name\n  gethostbyaddr:1,     // - get host record given its address\n  gethostbyname:1,     // - get host record given name\n  gethostent:1,     // - get next hosts record\n  getlogin:1,     // - return who logged in at this tty\n  getnetbyaddr:1,     // - get network record given its address\n  getnetbyname:1,     // - get networks record given name\n  getnetent:1,     // - get next networks record\n  getpeername:1,     // - find the other end of a socket connection\n  getpgrp:1,     // - get process group\n  getppid:1,     // - get parent process ID\n  getpriority:1,     // - get current nice value\n  getprotobyname:1,     // - get protocol record given name\n  getprotobynumber:1,     // - get protocol record numeric protocol\n  getprotoent:1,     // - get next protocols record\n  getpwent:1,     // - get next passwd record\n  getpwnam:1,     // - get passwd record given user login name\n  getpwuid:1,     // - get passwd record given user ID\n  getservbyname:1,     // - get services record given its name\n  getservbyport:1,     // - get services record given numeric port\n  getservent:1,     // - get next services record\n  getsockname:1,     // - retrieve the sockaddr for a given socket\n  getsockopt:1,     // - get socket options on a given socket\n  given:1,     //\n  glob:1,     // - expand filenames using wildcards\n  gmtime:1,     // - convert UNIX time into record or string using Greenwich time\n  'goto':1,     // - create spaghetti code\n  grep:1,     // - locate elements in a list test true against a given criterion\n  hex:1,     // - convert a string to a hexadecimal number\n  'import':1,     // - patch a module's namespace into your own\n  index:1,     // - find a substring within a string\n  'int':1,     // - get the integer portion of a number\n  ioctl:1,     // - system-dependent device control system call\n  'join':1,     // - join a list into a string using a separator\n  keys:1,     // - retrieve list of indices from a hash\n  kill:1,     // - send a signal to a process or process group\n  last:1,     // - exit a block prematurely\n  lc:1,     // - return lower-case version of a string\n  lcfirst:1,     // - return a string with just the next letter in lower case\n  length:1,     // - return the number of bytes in a string\n  'link':1,     // - create a hard link in the filesystem\n  listen:1,     // - register your socket as a server\n  local: 2,    // - create a temporary value for a global variable (dynamic scoping)\n  localtime:1,     // - convert UNIX time into record or string using local time\n  lock:1,     // - get a thread lock on a variable, subroutine, or method\n  'log':1,     // - retrieve the natural logarithm for a number\n  lstat:1,     // - stat a symbolic link\n  m:null,  // - match a string with a regular expression pattern\n  map:1,     // - apply a change to a list to get back a new list with the changes\n  mkdir:1,     // - create a directory\n  msgctl:1,     // - SysV IPC message control operations\n  msgget:1,     // - get SysV IPC message queue\n  msgrcv:1,     // - receive a SysV IPC message from a message queue\n  msgsnd:1,     // - send a SysV IPC message to a message queue\n  my: 2,    // - declare and assign a local variable (lexical scoping)\n  'new':1,     //\n  next:1,     // - iterate a block prematurely\n  no:1,     // - unimport some module symbols or semantics at compile time\n  oct:1,     // - convert a string to an octal number\n  open:1,     // - open a file, pipe, or descriptor\n  opendir:1,     // - open a directory\n  ord:1,     // - find a character's numeric representation\n  our: 2,    // - declare and assign a package variable (lexical scoping)\n  pack:1,     // - convert a list into a binary representation\n  'package':1,     // - declare a separate global namespace\n  pipe:1,     // - open a pair of connected filehandles\n  pop:1,     // - remove the last element from an array and return it\n  pos:1,     // - find or set the offset for the last/next m//g search\n  print:1,     // - output a list to a filehandle\n  printf:1,     // - output a formatted list to a filehandle\n  prototype:1,     // - get the prototype (if any) of a subroutine\n  push:1,     // - append one or more elements to an array\n  q:null,  // - singly quote a string\n  qq:null,  // - doubly quote a string\n  qr:null,  // - Compile pattern\n  quotemeta:null,  // - quote regular expression magic characters\n  qw:null,  // - quote a list of words\n  qx:null,  // - backquote quote a string\n  rand:1,     // - retrieve the next pseudorandom number\n  read:1,     // - fixed-length buffered input from a filehandle\n  readdir:1,     // - get a directory from a directory handle\n  readline:1,     // - fetch a record from a file\n  readlink:1,     // - determine where a symbolic link is pointing\n  readpipe:1,     // - execute a system command and collect standard output\n  recv:1,     // - receive a message over a Socket\n  redo:1,     // - start this loop iteration over again\n  ref:1,     // - find out the type of thing being referenced\n  rename:1,     // - change a filename\n  require:1,     // - load in external functions from a library at runtime\n  reset:1,     // - clear all variables of a given name\n  'return':1,     // - get out of a function early\n  reverse:1,     // - flip a string or a list\n  rewinddir:1,     // - reset directory handle\n  rindex:1,     // - right-to-left substring search\n  rmdir:1,     // - remove a directory\n  s:null,  // - replace a pattern with a string\n  say:1,     // - print with newline\n  scalar:1,     // - force a scalar context\n  seek:1,     // - reposition file pointer for random-access I/O\n  seekdir:1,     // - reposition directory pointer\n  select:1,     // - reset default output or do I/O multiplexing\n  semctl:1,     // - SysV semaphore control operations\n  semget:1,     // - get set of SysV semaphores\n  semop:1,     // - SysV semaphore operations\n  send:1,     // - send a message over a socket\n  setgrent:1,     // - prepare group file for use\n  sethostent:1,     // - prepare hosts file for use\n  setnetent:1,     // - prepare networks file for use\n  setpgrp:1,     // - set the process group of a process\n  setpriority:1,     // - set a process's nice value\n  setprotoent:1,     // - prepare protocols file for use\n  setpwent:1,     // - prepare passwd file for use\n  setservent:1,     // - prepare services file for use\n  setsockopt:1,     // - set some socket options\n  shift:1,     // - remove the first element of an array, and return it\n  shmctl:1,     // - SysV shared memory operations\n  shmget:1,     // - get SysV shared memory segment identifier\n  shmread:1,     // - read SysV shared memory\n  shmwrite:1,     // - write SysV shared memory\n  shutdown:1,     // - close down just half of a socket connection\n  'sin':1,     // - return the sine of a number\n  sleep:1,     // - block for some number of seconds\n  socket:1,     // - create a socket\n  socketpair:1,     // - create a pair of sockets\n  'sort':1,     // - sort a list of values\n  splice:1,     // - add or remove elements anywhere in an array\n  'split':1,     // - split up a string using a regexp delimiter\n  sprintf:1,     // - formatted print into a string\n  'sqrt':1,     // - square root function\n  srand:1,     // - seed the random number generator\n  stat:1,     // - get a file's status information\n  state:1,     // - declare and assign a state variable (persistent lexical scoping)\n  study:1,     // - optimize input data for repeated searches\n  'sub':1,     // - declare a subroutine, possibly anonymously\n  'substr':1,     // - get or alter a portion of a string\n  symlink:1,     // - create a symbolic link to a file\n  syscall:1,     // - execute an arbitrary system call\n  sysopen:1,     // - open a file, pipe, or descriptor\n  sysread:1,     // - fixed-length unbuffered input from a filehandle\n  sysseek:1,     // - position I/O pointer on handle used with sysread and syswrite\n  system:1,     // - run a separate program\n  syswrite:1,     // - fixed-length unbuffered output to a filehandle\n  tell:1,     // - get current seekpointer on a filehandle\n  telldir:1,     // - get current seekpointer on a directory handle\n  tie:1,     // - bind a variable to an object class\n  tied:1,     // - get a reference to the object underlying a tied variable\n  time:1,     // - return number of seconds since 1970\n  times:1,     // - return elapsed time for self and child processes\n  tr:null,  // - transliterate a string\n  truncate:1,     // - shorten a file\n  uc:1,     // - return upper-case version of a string\n  ucfirst:1,     // - return a string with just the next letter in upper case\n  umask:1,     // - set file creation mode mask\n  undef:1,     // - remove a variable or function definition\n  unlink:1,     // - remove one link to a file\n  unpack:1,     // - convert binary structure into normal perl variables\n  unshift:1,     // - prepend more elements to the beginning of a list\n  untie:1,     // - break a tie binding to a variable\n  use:1,     // - load in a module at compile time\n  utime:1,     // - set a file's last access and modify times\n  values:1,     // - return a list of the values in a hash\n  vec:1,     // - test or set particular bits in a string\n  wait:1,     // - wait for any child process to die\n  waitpid:1,     // - wait for a particular child process to die\n  wantarray:1,     // - get void vs scalar vs list context of current subroutine call\n  warn:1,     // - print debugging info\n  when:1,     //\n  write:1,     // - print a picture record\n  y:null}; // - transliterate a string\n\nvar RXstyle=\"string.special\";\nvar RXmodifiers=/[goseximacplud]/;              // NOTE: \"m\", \"s\", \"y\" and \"tr\" need to correct real modifiers for each regexp type\n\nfunction tokenChain(stream,state,chain,style,tail){     // NOTE: chain.length > 2 is not working now (it's for s[...][...]geos;)\n  state.chain=null;                               //                                                          12   3tail\n  state.style=null;\n  state.tail=null;\n  state.tokenize=function(stream,state){\n    var e=false,c,i=0;\n    while(c=stream.next()){\n      if(c===chain[i]&&!e){\n        if(chain[++i]!==undefined){\n          state.chain=chain[i];\n          state.style=style;\n          state.tail=tail;}\n        else if(tail)\n          stream.eatWhile(tail);\n        state.tokenize=tokenPerl;\n        return style;}\n      e=!e&&c==\"\\\\\";}\n    return style;};\n  return state.tokenize(stream,state);}\n\nfunction tokenSOMETHING(stream,state,string){\n  state.tokenize=function(stream,state){\n    if(stream.string==string)\n      state.tokenize=tokenPerl;\n    stream.skipToEnd();\n    return \"string\";};\n  return state.tokenize(stream,state);}\n\nfunction tokenPerl(stream,state){\n  if(stream.eatSpace())\n    return null;\n  if(state.chain)\n    return tokenChain(stream,state,state.chain,state.style,state.tail);\n  if(stream.match(/^(\\-?((\\d[\\d_]*)?\\.\\d+(e[+-]?\\d+)?|\\d+\\.\\d*)|0x[\\da-fA-F_]+|0b[01_]+|\\d[\\d_]*(e[+-]?\\d+)?)/))\n    return 'number';\n  if(stream.match(/^<<(?=[_a-zA-Z])/)){                  // NOTE: <<SOMETHING\\n...\\nSOMETHING\\n\n    stream.eatWhile(/\\w/);\n    return tokenSOMETHING(stream,state,stream.current().substr(2));}\n  if(stream.sol()&&stream.match(/^\\=item(?!\\w)/)){// NOTE: \\n=item...\\n=cut\\n\n    return tokenSOMETHING(stream,state,'=cut');}\n  var ch=stream.next();\n  if(ch=='\"'||ch==\"'\"){                           // NOTE: ' or \" or <<'SOMETHING'\\n...\\nSOMETHING\\n or <<\"SOMETHING\"\\n...\\nSOMETHING\\n\n    if(prefix(stream, 3)==\"<<\"+ch){\n      var p=stream.pos;\n      stream.eatWhile(/\\w/);\n      var n=stream.current().substr(1);\n      if(n&&stream.eat(ch))\n        return tokenSOMETHING(stream,state,n);\n      stream.pos=p;}\n    return tokenChain(stream,state,[ch],\"string\");}\n  if(ch==\"q\"){\n    var c=look(stream, -2);\n    if(!(c&&/\\w/.test(c))){\n      c=look(stream, 0);\n      if(c==\"x\"){\n        c=look(stream, 1);\n        if(c==\"(\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\")\"],RXstyle,RXmodifiers);}\n        if(c==\"[\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"]\"],RXstyle,RXmodifiers);}\n        if(c==\"{\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"}\"],RXstyle,RXmodifiers);}\n        if(c==\"<\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\">\"],RXstyle,RXmodifiers);}\n        if(/[\\^'\"!~\\/]/.test(c)){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[stream.eat(c)],RXstyle,RXmodifiers);}}\n      else if(c==\"q\"){\n        c=look(stream, 1);\n        if(c==\"(\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\")\"],\"string\");}\n        if(c==\"[\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"]\"],\"string\");}\n        if(c==\"{\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"}\"],\"string\");}\n        if(c==\"<\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\">\"],\"string\");}\n        if(/[\\^'\"!~\\/]/.test(c)){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[stream.eat(c)],\"string\");}}\n      else if(c==\"w\"){\n        c=look(stream, 1);\n        if(c==\"(\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\")\"],\"bracket\");}\n        if(c==\"[\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"]\"],\"bracket\");}\n        if(c==\"{\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"}\"],\"bracket\");}\n        if(c==\"<\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\">\"],\"bracket\");}\n        if(/[\\^'\"!~\\/]/.test(c)){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[stream.eat(c)],\"bracket\");}}\n      else if(c==\"r\"){\n        c=look(stream, 1);\n        if(c==\"(\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\")\"],RXstyle,RXmodifiers);}\n        if(c==\"[\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"]\"],RXstyle,RXmodifiers);}\n        if(c==\"{\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\"}\"],RXstyle,RXmodifiers);}\n        if(c==\"<\"){\n          eatSuffix(stream, 2);\n          return tokenChain(stream,state,[\">\"],RXstyle,RXmodifiers);}\n        if(/[\\^'\"!~\\/]/.test(c)){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[stream.eat(c)],RXstyle,RXmodifiers);}}\n      else if(/[\\^'\"!~\\/(\\[{<]/.test(c)){\n        if(c==\"(\"){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[\")\"],\"string\");}\n        if(c==\"[\"){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[\"]\"],\"string\");}\n        if(c==\"{\"){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[\"}\"],\"string\");}\n        if(c==\"<\"){\n          eatSuffix(stream, 1);\n          return tokenChain(stream,state,[\">\"],\"string\");}\n        if(/[\\^'\"!~\\/]/.test(c)){\n          return tokenChain(stream,state,[stream.eat(c)],\"string\");}}}}\n  if(ch==\"m\"){\n    var c=look(stream, -2);\n    if(!(c&&/\\w/.test(c))){\n      c=stream.eat(/[(\\[{<\\^'\"!~\\/]/);\n      if(c){\n        if(/[\\^'\"!~\\/]/.test(c)){\n          return tokenChain(stream,state,[c],RXstyle,RXmodifiers);}\n        if(c==\"(\"){\n          return tokenChain(stream,state,[\")\"],RXstyle,RXmodifiers);}\n        if(c==\"[\"){\n          return tokenChain(stream,state,[\"]\"],RXstyle,RXmodifiers);}\n        if(c==\"{\"){\n          return tokenChain(stream,state,[\"}\"],RXstyle,RXmodifiers);}\n        if(c==\"<\"){\n          return tokenChain(stream,state,[\">\"],RXstyle,RXmodifiers);}}}}\n  if(ch==\"s\"){\n    var c=/[\\/>\\]})\\w]/.test(look(stream, -2));\n    if(!c){\n      c=stream.eat(/[(\\[{<\\^'\"!~\\/]/);\n      if(c){\n        if(c==\"[\")\n          return tokenChain(stream,state,[\"]\",\"]\"],RXstyle,RXmodifiers);\n        if(c==\"{\")\n          return tokenChain(stream,state,[\"}\",\"}\"],RXstyle,RXmodifiers);\n        if(c==\"<\")\n          return tokenChain(stream,state,[\">\",\">\"],RXstyle,RXmodifiers);\n        if(c==\"(\")\n          return tokenChain(stream,state,[\")\",\")\"],RXstyle,RXmodifiers);\n        return tokenChain(stream,state,[c,c],RXstyle,RXmodifiers);}}}\n  if(ch==\"y\"){\n    var c=/[\\/>\\]})\\w]/.test(look(stream, -2));\n    if(!c){\n      c=stream.eat(/[(\\[{<\\^'\"!~\\/]/);\n      if(c){\n        if(c==\"[\")\n          return tokenChain(stream,state,[\"]\",\"]\"],RXstyle,RXmodifiers);\n        if(c==\"{\")\n          return tokenChain(stream,state,[\"}\",\"}\"],RXstyle,RXmodifiers);\n        if(c==\"<\")\n          return tokenChain(stream,state,[\">\",\">\"],RXstyle,RXmodifiers);\n        if(c==\"(\")\n          return tokenChain(stream,state,[\")\",\")\"],RXstyle,RXmodifiers);\n        return tokenChain(stream,state,[c,c],RXstyle,RXmodifiers);}}}\n  if(ch==\"t\"){\n    var c=/[\\/>\\]})\\w]/.test(look(stream, -2));\n    if(!c){\n      c=stream.eat(\"r\");if(c){\n        c=stream.eat(/[(\\[{<\\^'\"!~\\/]/);\n        if(c){\n          if(c==\"[\")\n            return tokenChain(stream,state,[\"]\",\"]\"],RXstyle,RXmodifiers);\n          if(c==\"{\")\n            return tokenChain(stream,state,[\"}\",\"}\"],RXstyle,RXmodifiers);\n          if(c==\"<\")\n            return tokenChain(stream,state,[\">\",\">\"],RXstyle,RXmodifiers);\n          if(c==\"(\")\n            return tokenChain(stream,state,[\")\",\")\"],RXstyle,RXmodifiers);\n          return tokenChain(stream,state,[c,c],RXstyle,RXmodifiers);}}}}\n  if(ch==\"`\"){\n    return tokenChain(stream,state,[ch],\"builtin\");}\n  if(ch==\"/\"){\n    if(!/~\\s*$/.test(prefix(stream)))\n      return \"operator\";\n    else\n      return tokenChain(stream,state,[ch],RXstyle,RXmodifiers);}\n  if(ch==\"$\"){\n    var p=stream.pos;\n    if(stream.eatWhile(/\\d/)||stream.eat(\"{\")&&stream.eatWhile(/\\d/)&&stream.eat(\"}\"))\n      return \"builtin\";\n    else\n      stream.pos=p;}\n  if(/[$@%]/.test(ch)){\n    var p=stream.pos;\n    if(stream.eat(\"^\")&&stream.eat(/[A-Z]/)||!/[@$%&]/.test(look(stream, -2))&&stream.eat(/[=|\\\\\\-#?@;:&`~\\^!\\[\\]*'\"$+.,\\/<>()]/)){\n      var c=stream.current();\n      if(PERL[c])\n        return \"builtin\";}\n    stream.pos=p;}\n  if(/[$@%&]/.test(ch)){\n    if(stream.eatWhile(/[\\w$]/)||stream.eat(\"{\")&&stream.eatWhile(/[\\w$]/)&&stream.eat(\"}\")){\n      var c=stream.current();\n      if(PERL[c])\n        return \"builtin\";\n      else\n        return \"variable\";}}\n  if(ch==\"#\"){\n    if(look(stream, -2)!=\"$\"){\n      stream.skipToEnd();\n      return \"comment\";}}\n  if(/[:+\\-\\^*$&%@=<>!?|\\/~\\.]/.test(ch)){\n    var p=stream.pos;\n    stream.eatWhile(/[:+\\-\\^*$&%@=<>!?|\\/~\\.]/);\n    if(PERL[stream.current()])\n      return \"operator\";\n    else\n      stream.pos=p;}\n  if(ch==\"_\"){\n    if(stream.pos==1){\n      if(suffix(stream, 6)==\"_END__\"){\n        return tokenChain(stream,state,['\\0'],\"comment\");}\n      else if(suffix(stream, 7)==\"_DATA__\"){\n        return tokenChain(stream,state,['\\0'],\"builtin\");}\n      else if(suffix(stream, 7)==\"_C__\"){\n        return tokenChain(stream,state,['\\0'],\"string\");}}}\n  if(/\\w/.test(ch)){\n    var p=stream.pos;\n    if(look(stream, -2)==\"{\"&&(look(stream, 0)==\"}\"||stream.eatWhile(/\\w/)&&look(stream, 0)==\"}\"))\n      return \"string\";\n    else\n      stream.pos=p;}\n  if(/[A-Z]/.test(ch)){\n    var l=look(stream, -2);\n    var p=stream.pos;\n    stream.eatWhile(/[A-Z_]/);\n    if(/[\\da-z]/.test(look(stream, 0))){\n      stream.pos=p;}\n    else{\n      var c=PERL[stream.current()];\n      if(!c)\n        return \"meta\";\n      if(c[1])\n        c=c[0];\n      if(l!=\":\"){\n        if(c==1)\n          return \"keyword\";\n        else if(c==2)\n          return \"def\";\n        else if(c==3)\n          return \"atom\";\n        else if(c==4)\n          return \"operator\";\n        else if(c==5)\n          return \"builtin\";\n        else\n          return \"meta\";}\n      else\n        return \"meta\";}}\n  if(/[a-zA-Z_]/.test(ch)){\n    var l=look(stream, -2);\n    stream.eatWhile(/\\w/);\n    var c=PERL[stream.current()];\n    if(!c)\n      return \"meta\";\n    if(c[1])\n      c=c[0];\n    if(l!=\":\"){\n      if(c==1)\n        return \"keyword\";\n      else if(c==2)\n        return \"def\";\n      else if(c==3)\n        return \"atom\";\n      else if(c==4)\n        return \"operator\";\n      else if(c==5)\n        return \"builtin\";\n      else\n        return \"meta\";}\n    else\n      return \"meta\";}\n  return null;}\n\nexport const perl = {\n  name: \"perl\",\n\n  startState: function() {\n    return {\n      tokenize: tokenPerl,\n      chain: null,\n      style: null,\n      tail: null\n    };\n  },\n  token: function(stream, state) {\n    return (state.tokenize || tokenPerl)(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"#\"},\n    wordChars: \"$\"\n  }\n};\n"], "mappings": ";;;AAEA,SAAS,KAAK,QAAQ,GAAE;AACtB,SAAO,OAAO,OAAO,OAAO,OAAO,OAAK,KAAG,EAAE;AAC/C;AAGA,SAAS,OAAO,QAAQ,GAAE;AACxB,MAAG,GAAE;AACH,QAAI,IAAE,OAAO,MAAI;AACjB,WAAO,OAAO,OAAO,OAAQ,KAAG,IAAE,IAAE,GAAG,CAAC;AAAA,EAAE,OACxC;AACF,WAAO,OAAO,OAAO,OAAO,GAAE,OAAO,MAAI,CAAC;AAAA,EAC5C;AACF;AAGA,SAAS,OAAO,QAAQ,GAAE;AACxB,MAAI,IAAE,OAAO,OAAO;AACpB,MAAI,IAAE,IAAE,OAAO,MAAI;AACnB,SAAO,OAAO,OAAO,OAAO,OAAO,KAAK,KAAG,IAAE,IAAE,IAAE,CAAE;AACrD;AAGA,SAAS,UAAU,QAAQ,GAAE;AAC3B,MAAI,IAAE,OAAO,MAAI;AACjB,MAAI;AACJ,MAAG,KAAG;AACJ,WAAO,MAAI;AAAA,WACL,MAAI,IAAE,OAAO,OAAO,SAAO;AACjC,WAAO,MAAI;AAAA;AAEX,WAAO,MAAI;AACf;AAGA,IAAI,OAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA;AAAA,EAER,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,MAAQ;AAAA;AAAA,EAER,KAAO;AAAA,EACP,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,KAAO;AAAA,EACP,KAAO;AAAA,EACP,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AAAA;AAAA,EACR,KAAO;AAAA,EACP,MAAQ;AAAA,EACR,MAAQ;AAAA;AAAA,EAER,OAAS;AAAA,EACT,OAAS;AAAA,EACT,MAAQ;AAAA,EACR,OAAS;AAAA;AAAA,EAET,SAAW,CAAC,GAAE,CAAC;AAAA,EACf,OAAS,CAAC,GAAE,CAAC;AAAA,EACb,SAAW,CAAC,GAAE,CAAC;AAAA,EACf,UAAY,CAAC,GAAE,CAAC;AAAA,EAChB,QAAU,CAAC,GAAE,CAAC;AAAA,EACd,QAAU,CAAC,GAAE,CAAC;AAAA,EACd,YAAc,CAAC,GAAE,CAAC;AAAA,EAClB,WAAa,CAAC,GAAE,CAAC;AAAA,EACjB,OAAS,CAAC,GAAE,CAAC;AAAA,EACb,aAAe,CAAC,GAAE,CAAC;AAAA,EACnB,SAAW,CAAC,GAAE,CAAC;AAAA,EACf,SAAY;AAAA,EACZ,aAAgB;AAAA,EAChB,UAAa;AAAA,EACb,cAAiB;AAAA,EACjB,UAAa;AAAA,EACb,cAAiB;AAAA,EACjB,QAAW;AAAA,EACX,MAAS;AAAA,EACT,QAAW;AAAA,EACX,MAAS;AAAA,EACT,mBAAsB;AAAA,EACtB,MAAS;AAAA,EACT,eAAkB;AAAA,EAClB,QAAW;AAAA,EACX,MAAS;AAAA,EACT,kBAAqB;AAAA,EACrB,QAAW;AAAA,EACX,MAAS;AAAA,EACT,uBAA0B;AAAA,EAC1B,SAAY;AAAA,EACZ,MAAS;AAAA,EACT,iBAAoB;AAAA,EACpB,MAAS;AAAA,EACT,wBAA2B;AAAA,EAC3B,WAAc;AAAA,EACd,MAAS;AAAA,EACT,iBAAoB;AAAA,EACpB,QAAW;AAAA,EACX,MAAS;AAAA,EACT,sBAAyB;AAAA,EACzB,SAAY;AAAA,EACZ,MAAS;AAAA,EACT,MAAS;AAAA,EACT,MAAS;AAAA,EACT,cAAiB;AAAA,EACjB,OAAU;AAAA,EACV,cAAiB;AAAA,EACjB,OAAU;AAAA,EACV,gBAAmB;AAAA,EACnB,QAAW;AAAA,EACX,QAAW;AAAA,EACX,kBAAqB;AAAA,EACrB,OAAU;AAAA,EACV,MAAS;AAAA,EACT,oBAAuB;AAAA,EACvB,OAAU;AAAA,EACV,OAAU;AAAA,EACV,QAAW;AAAA,EACX,QAAW;AAAA,EACX,iBAAoB;AAAA,EACpB,OAAU;AAAA,EACV,OAAU;AAAA,EACV,WAAc;AAAA,EACd,OAAU;AAAA,EACV,YAAe;AAAA,EACf,WAAc;AAAA,EACd,OAAU;AAAA,EACV,QAAW;AAAA,EACX,QAAW;AAAA,EACX,aAAgB;AAAA,EAChB,OAAU;AAAA,EACV,aAAgB;AAAA,EAChB,eAAkB;AAAA,EAClB,iBAAoB;AAAA,EACpB,kBAAqB;AAAA,EACrB,iBAAoB;AAAA,EACpB,OAAU;AAAA,EACV,yBAA4B;AAAA,EAC5B,oBAAuB;AAAA,EACvB,OAAU;AAAA,EACV,MAAS;AAAA;AAAA,EACT,UAAa;AAAA,EACb,MAAS;AAAA,EACT,aAAgB;AAAA,EAChB,aAAgB;AAAA,EAChB,MAAS;AAAA,EACT,gBAAmB;AAAA,EACnB,cAAiB;AAAA,EACjB,MAAS;AAAA,EACT,iBAAoB;AAAA,EACpB,qBAAwB;AAAA,EACxB,MAAS;AAAA,EACT,yBAA4B;AAAA,EAC5B,OAAU;AAAA,EACV,mBAAsB;AAAA,EACtB,MAAS;AAAA,EACT,qBAAwB;AAAA,EACxB,MAAS;AAAA,EACT,qBAAwB;AAAA,EACxB,MAAS;AAAA,EACT,qBAAwB;AAAA,EACxB,MAAS;AAAA,EACT,4BAA+B;AAAA,EAC/B,OAAU;AAAA,EACV,sBAAyB;AAAA,EACzB,sBAAyB;AAAA,EACzB,SAAY;AAAA,EACZ,SAAY;AAAA,EACZ,QAAW;AAAA,EACX,WAAc;AAAA,EACd,2BAA8B;AAAA,EAC9B,QAAW;AAAA,EACX,MAAS;AAAA,EACT,sBAAyB;AAAA,EACzB,OAAU;AAAA,EACV,MAAS;AAAA,EACT,2BAA8B;AAAA,EAC9B,OAAU;AAAA,EACV,MAAS;AAAA,EACT,4BAA+B;AAAA,EAC/B,QAAW;AAAA,EACX,OAAU;AAAA,EACV,qBAAwB;AAAA,EACxB,MAAS;AAAA,EACT,gBAAmB;AAAA,EACnB,OAAU;AAAA,EACV,oBAAuB;AAAA,EACvB,OAAU;AAAA,EACV,uBAA0B;AAAA,EAC1B,MAAS;AAAA,EACT,sBAAyB;AAAA,EACzB,MAAS;AAAA,EACT,iCAAoC;AAAA,EACpC,MAAS;AAAA,EACT,0BAA6B;AAAA,EAC7B,MAAS;AAAA,EACT,oBAAuB;AAAA,EACvB,MAAS;AAAA,EACT,gBAAmB;AAAA,EACnB,MAAS;AAAA,EACT,0BAA6B;AAAA,EAC7B,sBAAyB;AAAA,EACzB,OAAU;AAAA,EACV,4BAA+B;AAAA,EAC/B,OAAU;AAAA,EACV,YAAe;AAAA,EACf,OAAU;AAAA,EACV,oBAAuB;AAAA,EACvB,aAAgB;AAAA,EAChB,UAAa;AAAA,EACb,MAAS;AAAA,EACT,aAAgB;AAAA,EAChB,UAAa;AAAA,EACb,MAAS;AAAA,EACT,gBAAmB;AAAA,EACnB,MAAS;AAAA,EACT,eAAkB;AAAA,EAClB,MAAS;AAAA,EACT,SAAY;AAAA,EACZ,MAAS;AAAA,EACT,MAAS;AAAA,EACT,eAAkB;AAAA,EAClB,MAAS;AAAA,EACT,qBAAwB;AAAA,EACxB,MAAS;AAAA;AAAA,EAET,MAAK,CAAC,GAAE,CAAC;AAAA,EACT,OAAM,CAAC,GAAE,CAAC;AAAA,EACV,QAAO,CAAC,GAAE,CAAC;AAAA,EACX,SAAQ,CAAC,GAAE,CAAC;AAAA,EACZ,QAAO,CAAC,GAAE,CAAC;AAAA,EACX,OAAM,CAAC,GAAE,CAAC;AAAA,EACV,SAAQ,CAAC,GAAE,CAAC;AAAA;AAAA,EAEZ,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,SAAQ;AAAA;AAAA,EACR,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,OAAM;AAAA;AAAA,EACN,WAAU;AAAA;AAAA,EACV,SAAQ;AAAA;AAAA,EACR,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,KAAI;AAAA;AAAA,EACJ,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,UAAS;AAAA;AAAA,EACT,SAAQ;AAAA;AAAA,EACR,YAAW,CAAC,GAAE,CAAC;AAAA;AAAA,EACf,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,UAAS;AAAA;AAAA,EACT,SAAQ;AAAA;AAAA,EACR,WAAU;AAAA;AAAA,EACV,SAAQ;AAAA;AAAA,EACR,UAAS;AAAA;AAAA,EACT,KAAI;AAAA;AAAA,EACJ,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,UAAS;AAAA;AAAA,EACT,YAAW;AAAA;AAAA,EACX,WAAU;AAAA;AAAA,EACV,aAAY;AAAA;AAAA,EACZ,UAAS;AAAA;AAAA,EACT,YAAW;AAAA;AAAA,EACX,KAAI;AAAA;AAAA,EACJ,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,QAAO;AAAA;AAAA,EACP,UAAS;AAAA;AAAA,EACT,MAAK;AAAA;AAAA,EACL,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,eAAc;AAAA;AAAA,EACd,eAAc;AAAA;AAAA,EACd,YAAW;AAAA;AAAA,EACX,UAAS;AAAA;AAAA,EACT,cAAa;AAAA;AAAA,EACb,cAAa;AAAA;AAAA,EACb,WAAU;AAAA;AAAA,EACV,aAAY;AAAA;AAAA,EACZ,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,aAAY;AAAA;AAAA,EACZ,gBAAe;AAAA;AAAA,EACf,kBAAiB;AAAA;AAAA,EACjB,aAAY;AAAA;AAAA,EACZ,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,eAAc;AAAA;AAAA,EACd,eAAc;AAAA;AAAA,EACd,YAAW;AAAA;AAAA,EACX,aAAY;AAAA;AAAA,EACZ,YAAW;AAAA;AAAA,EACX,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,MAAK;AAAA;AAAA,EACL,KAAI;AAAA;AAAA,EACJ,UAAS;AAAA;AAAA,EACT,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,IAAG;AAAA;AAAA,EACH,SAAQ;AAAA;AAAA,EACR,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,OAAO;AAAA;AAAA,EACP,WAAU;AAAA;AAAA,EACV,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,GAAE;AAAA;AAAA,EACF,KAAI;AAAA;AAAA,EACJ,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,IAAI;AAAA;AAAA,EACJ,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,IAAG;AAAA;AAAA,EACH,KAAI;AAAA;AAAA,EACJ,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,KAAI;AAAA;AAAA,EACJ,KAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,WAAU;AAAA;AAAA,EACV,MAAK;AAAA;AAAA,EACL,KAAI;AAAA;AAAA,EACJ,KAAI;AAAA;AAAA,EACJ,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,WAAU;AAAA;AAAA,EACV,MAAK;AAAA;AAAA,EACL,GAAE;AAAA;AAAA,EACF,IAAG;AAAA;AAAA,EACH,IAAG;AAAA;AAAA,EACH,WAAU;AAAA;AAAA,EACV,IAAG;AAAA;AAAA,EACH,IAAG;AAAA;AAAA,EACH,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,KAAI;AAAA;AAAA,EACJ,QAAO;AAAA;AAAA,EACP,SAAQ;AAAA;AAAA,EACR,OAAM;AAAA;AAAA,EACN,UAAS;AAAA;AAAA,EACT,SAAQ;AAAA;AAAA,EACR,WAAU;AAAA;AAAA,EACV,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,GAAE;AAAA;AAAA,EACF,KAAI;AAAA;AAAA,EACJ,QAAO;AAAA;AAAA,EACP,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,UAAS;AAAA;AAAA,EACT,YAAW;AAAA;AAAA,EACX,WAAU;AAAA;AAAA,EACV,SAAQ;AAAA;AAAA,EACR,aAAY;AAAA;AAAA,EACZ,aAAY;AAAA;AAAA,EACZ,UAAS;AAAA;AAAA,EACT,YAAW;AAAA;AAAA,EACX,YAAW;AAAA;AAAA,EACX,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,SAAQ;AAAA;AAAA,EACR,UAAS;AAAA;AAAA,EACT,UAAS;AAAA;AAAA,EACT,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,YAAW;AAAA;AAAA,EACX,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,QAAO;AAAA;AAAA,EACP,OAAM;AAAA;AAAA,EACN,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,UAAS;AAAA;AAAA,EACT,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,SAAQ;AAAA;AAAA,EACR,QAAO;AAAA;AAAA,EACP,UAAS;AAAA;AAAA,EACT,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,KAAI;AAAA;AAAA,EACJ,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,IAAG;AAAA;AAAA,EACH,UAAS;AAAA;AAAA,EACT,IAAG;AAAA;AAAA,EACH,SAAQ;AAAA;AAAA,EACR,OAAM;AAAA;AAAA,EACN,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,QAAO;AAAA;AAAA,EACP,SAAQ;AAAA;AAAA,EACR,OAAM;AAAA;AAAA,EACN,KAAI;AAAA;AAAA,EACJ,OAAM;AAAA;AAAA,EACN,QAAO;AAAA;AAAA,EACP,KAAI;AAAA;AAAA,EACJ,MAAK;AAAA;AAAA,EACL,SAAQ;AAAA;AAAA,EACR,WAAU;AAAA;AAAA,EACV,MAAK;AAAA;AAAA,EACL,MAAK;AAAA;AAAA,EACL,OAAM;AAAA;AAAA,EACN,GAAE;AAAI;AAER,IAAI,UAAQ;AACZ,IAAI,cAAY;AAEhB,SAAS,WAAW,QAAO,OAAM,OAAM,OAAM,MAAK;AAChD,QAAM,QAAM;AACZ,QAAM,QAAM;AACZ,QAAM,OAAK;AACX,QAAM,WAAS,SAASA,SAAOC,QAAM;AACnC,QAAI,IAAE,OAAM,GAAE,IAAE;AAChB,WAAM,IAAED,QAAO,KAAK,GAAE;AACpB,UAAG,MAAI,MAAM,CAAC,KAAG,CAAC,GAAE;AAClB,YAAG,MAAM,EAAE,CAAC,MAAI,QAAU;AACxB,UAAAC,OAAM,QAAM,MAAM,CAAC;AACnB,UAAAA,OAAM,QAAM;AACZ,UAAAA,OAAM,OAAK;AAAA,QAAK,WACV;AACN,UAAAD,QAAO,SAAS,IAAI;AACtB,QAAAC,OAAM,WAAS;AACf,eAAO;AAAA,MAAM;AACf,UAAE,CAAC,KAAG,KAAG;AAAA,IAAK;AAChB,WAAO;AAAA,EAAM;AACf,SAAO,MAAM,SAAS,QAAO,KAAK;AAAE;AAEtC,SAAS,eAAe,QAAO,OAAM,QAAO;AAC1C,QAAM,WAAS,SAASD,SAAOC,QAAM;AACnC,QAAGD,QAAO,UAAQ;AAChB,MAAAC,OAAM,WAAS;AACjB,IAAAD,QAAO,UAAU;AACjB,WAAO;AAAA,EAAS;AAClB,SAAO,MAAM,SAAS,QAAO,KAAK;AAAE;AAEtC,SAAS,UAAU,QAAO,OAAM;AAC9B,MAAG,OAAO,SAAS;AACjB,WAAO;AACT,MAAG,MAAM;AACP,WAAO,WAAW,QAAO,OAAM,MAAM,OAAM,MAAM,OAAM,MAAM,IAAI;AACnE,MAAG,OAAO,MAAM,4FAA4F;AAC1G,WAAO;AACT,MAAG,OAAO,MAAM,kBAAkB,GAAE;AAClC,WAAO,SAAS,IAAI;AACpB,WAAO,eAAe,QAAO,OAAM,OAAO,QAAQ,EAAE,OAAO,CAAC,CAAC;AAAA,EAAE;AACjE,MAAG,OAAO,IAAI,KAAG,OAAO,MAAM,eAAe,GAAE;AAC7C,WAAO,eAAe,QAAO,OAAM,MAAM;AAAA,EAAE;AAC7C,MAAI,KAAG,OAAO,KAAK;AACnB,MAAG,MAAI,OAAK,MAAI,KAAI;AAClB,QAAG,OAAO,QAAQ,CAAC,KAAG,OAAK,IAAG;AAC5B,UAAI,IAAE,OAAO;AACb,aAAO,SAAS,IAAI;AACpB,UAAI,IAAE,OAAO,QAAQ,EAAE,OAAO,CAAC;AAC/B,UAAG,KAAG,OAAO,IAAI,EAAE;AACjB,eAAO,eAAe,QAAO,OAAM,CAAC;AACtC,aAAO,MAAI;AAAA,IAAE;AACf,WAAO,WAAW,QAAO,OAAM,CAAC,EAAE,GAAE,QAAQ;AAAA,EAAE;AAChD,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,KAAK,QAAQ,EAAE;AACrB,QAAG,EAAE,KAAG,KAAK,KAAK,CAAC,IAAG;AACpB,UAAE,KAAK,QAAQ,CAAC;AAChB,UAAG,KAAG,KAAI;AACR,YAAE,KAAK,QAAQ,CAAC;AAChB,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,OAAO,IAAI,CAAC,CAAC,GAAE,SAAQ,WAAW;AAAA,QAAE;AAAA,MAAC,WACjE,KAAG,KAAI;AACb,YAAE,KAAK,QAAQ,CAAC;AAChB,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,OAAO,IAAI,CAAC,CAAC,GAAE,QAAQ;AAAA,QAAE;AAAA,MAAC,WACtD,KAAG,KAAI;AACb,YAAE,KAAK,QAAQ,CAAC;AAChB,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAS;AAAA,QAAE;AAClD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAS;AAAA,QAAE;AAClD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAS;AAAA,QAAE;AAClD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAS;AAAA,QAAE;AAClD,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,OAAO,IAAI,CAAC,CAAC,GAAE,SAAS;AAAA,QAAE;AAAA,MAAC,WACvD,KAAG,KAAI;AACb,YAAE,KAAK,QAAQ,CAAC;AAChB,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,OAAO,IAAI,CAAC,CAAC,GAAE,SAAQ,WAAW;AAAA,QAAE;AAAA,MAAC,WACjE,kBAAkB,KAAK,CAAC,GAAE;AAChC,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,KAAG,KAAI;AACR,oBAAU,QAAQ,CAAC;AACnB,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,QAAQ;AAAA,QAAE;AACjD,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,iBAAO,WAAW,QAAO,OAAM,CAAC,OAAO,IAAI,CAAC,CAAC,GAAE,QAAQ;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AACpE,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,KAAK,QAAQ,EAAE;AACrB,QAAG,EAAE,KAAG,KAAK,KAAK,CAAC,IAAG;AACpB,UAAE,OAAO,IAAI,iBAAiB;AAC9B,UAAG,GAAE;AACH,YAAG,aAAa,KAAK,CAAC,GAAE;AACtB,iBAAO,WAAW,QAAO,OAAM,CAAC,CAAC,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC1D,YAAG,KAAG,KAAI;AACR,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAC5D,YAAG,KAAG,KAAI;AACR,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAG,GAAE,SAAQ,WAAW;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AACrE,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,cAAc,KAAK,KAAK,QAAQ,EAAE,CAAC;AACzC,QAAG,CAAC,GAAE;AACJ,UAAE,OAAO,IAAI,iBAAiB;AAC9B,UAAG,GAAE;AACH,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,eAAO,WAAW,QAAO,OAAM,CAAC,GAAE,CAAC,GAAE,SAAQ,WAAW;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAClE,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,cAAc,KAAK,KAAK,QAAQ,EAAE,CAAC;AACzC,QAAG,CAAC,GAAE;AACJ,UAAE,OAAO,IAAI,iBAAiB;AAC9B,UAAG,GAAE;AACH,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,YAAG,KAAG;AACJ,iBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,eAAO,WAAW,QAAO,OAAM,CAAC,GAAE,CAAC,GAAE,SAAQ,WAAW;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAClE,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,cAAc,KAAK,KAAK,QAAQ,EAAE,CAAC;AACzC,QAAG,CAAC,GAAE;AACJ,UAAE,OAAO,IAAI,GAAG;AAAE,UAAG,GAAE;AACrB,YAAE,OAAO,IAAI,iBAAiB;AAC9B,YAAG,GAAE;AACH,cAAG,KAAG;AACJ,mBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,cAAG,KAAG;AACJ,mBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,cAAG,KAAG;AACJ,mBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,cAAG,KAAG;AACJ,mBAAO,WAAW,QAAO,OAAM,CAAC,KAAI,GAAG,GAAE,SAAQ,WAAW;AAC9D,iBAAO,WAAW,QAAO,OAAM,CAAC,GAAE,CAAC,GAAE,SAAQ,WAAW;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AACrE,MAAG,MAAI,KAAI;AACT,WAAO,WAAW,QAAO,OAAM,CAAC,EAAE,GAAE,SAAS;AAAA,EAAE;AACjD,MAAG,MAAI,KAAI;AACT,QAAG,CAAC,QAAQ,KAAK,OAAO,MAAM,CAAC;AAC7B,aAAO;AAAA;AAEP,aAAO,WAAW,QAAO,OAAM,CAAC,EAAE,GAAE,SAAQ,WAAW;AAAA,EAAE;AAC7D,MAAG,MAAI,KAAI;AACT,QAAI,IAAE,OAAO;AACb,QAAG,OAAO,SAAS,IAAI,KAAG,OAAO,IAAI,GAAG,KAAG,OAAO,SAAS,IAAI,KAAG,OAAO,IAAI,GAAG;AAC9E,aAAO;AAAA;AAEP,aAAO,MAAI;AAAA,EAAE;AACjB,MAAG,QAAQ,KAAK,EAAE,GAAE;AAClB,QAAI,IAAE,OAAO;AACb,QAAG,OAAO,IAAI,GAAG,KAAG,OAAO,IAAI,OAAO,KAAG,CAAC,SAAS,KAAK,KAAK,QAAQ,EAAE,CAAC,KAAG,OAAO,IAAI,sCAAsC,GAAE;AAC5H,UAAI,IAAE,OAAO,QAAQ;AACrB,UAAG,KAAK,CAAC;AACP,eAAO;AAAA,IAAU;AACrB,WAAO,MAAI;AAAA,EAAE;AACf,MAAG,SAAS,KAAK,EAAE,GAAE;AACnB,QAAG,OAAO,SAAS,OAAO,KAAG,OAAO,IAAI,GAAG,KAAG,OAAO,SAAS,OAAO,KAAG,OAAO,IAAI,GAAG,GAAE;AACtF,UAAI,IAAE,OAAO,QAAQ;AACrB,UAAG,KAAK,CAAC;AACP,eAAO;AAAA;AAEP,eAAO;AAAA,IAAW;AAAA,EAAC;AACzB,MAAG,MAAI,KAAI;AACT,QAAG,KAAK,QAAQ,EAAE,KAAG,KAAI;AACvB,aAAO,UAAU;AACjB,aAAO;AAAA,IAAU;AAAA,EAAC;AACtB,MAAG,2BAA2B,KAAK,EAAE,GAAE;AACrC,QAAI,IAAE,OAAO;AACb,WAAO,SAAS,0BAA0B;AAC1C,QAAG,KAAK,OAAO,QAAQ,CAAC;AACtB,aAAO;AAAA;AAEP,aAAO,MAAI;AAAA,EAAE;AACjB,MAAG,MAAI,KAAI;AACT,QAAG,OAAO,OAAK,GAAE;AACf,UAAG,OAAO,QAAQ,CAAC,KAAG,UAAS;AAC7B,eAAO,WAAW,QAAO,OAAM,CAAC,IAAI,GAAE,SAAS;AAAA,MAAE,WAC3C,OAAO,QAAQ,CAAC,KAAG,WAAU;AACnC,eAAO,WAAW,QAAO,OAAM,CAAC,IAAI,GAAE,SAAS;AAAA,MAAE,WAC3C,OAAO,QAAQ,CAAC,KAAG,QAAO;AAChC,eAAO,WAAW,QAAO,OAAM,CAAC,IAAI,GAAE,QAAQ;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AACxD,MAAG,KAAK,KAAK,EAAE,GAAE;AACf,QAAI,IAAE,OAAO;AACb,QAAG,KAAK,QAAQ,EAAE,KAAG,QAAM,KAAK,QAAQ,CAAC,KAAG,OAAK,OAAO,SAAS,IAAI,KAAG,KAAK,QAAQ,CAAC,KAAG;AACvF,aAAO;AAAA;AAEP,aAAO,MAAI;AAAA,EAAE;AACjB,MAAG,QAAQ,KAAK,EAAE,GAAE;AAClB,QAAI,IAAE,KAAK,QAAQ,EAAE;AACrB,QAAI,IAAE,OAAO;AACb,WAAO,SAAS,QAAQ;AACxB,QAAG,UAAU,KAAK,KAAK,QAAQ,CAAC,CAAC,GAAE;AACjC,aAAO,MAAI;AAAA,IAAE,OACX;AACF,UAAI,IAAE,KAAK,OAAO,QAAQ,CAAC;AAC3B,UAAG,CAAC;AACF,eAAO;AACT,UAAG,EAAE,CAAC;AACJ,YAAE,EAAE,CAAC;AACP,UAAG,KAAG,KAAI;AACR,YAAG,KAAG;AACJ,iBAAO;AAAA,iBACD,KAAG;AACT,iBAAO;AAAA,iBACD,KAAG;AACT,iBAAO;AAAA,iBACD,KAAG;AACT,iBAAO;AAAA,iBACD,KAAG;AACT,iBAAO;AAAA;AAEP,iBAAO;AAAA,MAAO;AAEhB,eAAO;AAAA,IAAO;AAAA,EAAC;AACrB,MAAG,YAAY,KAAK,EAAE,GAAE;AACtB,QAAI,IAAE,KAAK,QAAQ,EAAE;AACrB,WAAO,SAAS,IAAI;AACpB,QAAI,IAAE,KAAK,OAAO,QAAQ,CAAC;AAC3B,QAAG,CAAC;AACF,aAAO;AACT,QAAG,EAAE,CAAC;AACJ,UAAE,EAAE,CAAC;AACP,QAAG,KAAG,KAAI;AACR,UAAG,KAAG;AACJ,eAAO;AAAA,eACD,KAAG;AACT,eAAO;AAAA,eACD,KAAG;AACT,eAAO;AAAA,eACD,KAAG;AACT,eAAO;AAAA,eACD,KAAG;AACT,eAAO;AAAA;AAEP,eAAO;AAAA,IAAO;AAEhB,aAAO;AAAA,EAAO;AAClB,SAAO;AAAK;AAEP,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,YAAQ,MAAM,YAAY,WAAW,QAAQ,KAAK;AAAA,EACpD;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,IACzB,WAAW;AAAA,EACb;AACF;", "names": ["stream", "state"]}