<template>
  <div class="product-management">
    <div class="page-header">
      <h2>商品列表管理</h2>
    </div>
    
    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <div class="search-box">
        <input 
          v-model="searchQuery.keyword" 
          type="text" 
          placeholder="搜索商品标题或描述" 
          @keyup.enter="handleSearch"
        />
        <button @click="handleSearch">搜索</button>
      </div>
      
      <div class="status-filter">
        <span>状态筛选：</span>
        <select v-model="searchQuery.status" @change="handleSearch">
          <option value="">全部</option>
          <option value="pending_review">待审核</option>
          <option value="available">已上架</option>
          <option value="off_shelf_by_seller">已下架</option>
          <option value="off_shelf_by_admin">管理员下架</option>
          <option value="sold">已售出</option>
          <option value="deleted">已删除</option>
        </select>
      </div>
      
      <div class="batch-actions" v-if="selectedProducts.length > 0">
        <button class="approve-btn" @click="showBatchApproveConfirm">批量上架</button>
        <button class="delete-btn" @click="showBatchDeleteConfirm">批量删除</button>
      </div>
    </div>
    
    <!-- 商品列表表格 -->
    <div class="product-table-container">
      <table class="product-table">
        <thead>
          <tr>
            <th width="30">
              <input 
                type="checkbox" 
                :checked="isAllSelected" 
                @change="toggleSelectAll"
              />
            </th>
            <th width="50">ID</th>
            <th width="80">图片</th>
            <th>标题</th>
            <th width="80">价格</th>
            <th width="80">卖家</th>
            <th width="80">状态</th>
            <th width="140">发布时间</th>
            <th width="160">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="product in products" :key="product.id">
            <td>
              <input 
                type="checkbox" 
                :value="product.id" 
                v-model="selectedProducts"
              />
            </td>
            <td>{{ product.id }}</td>
            <td>
              <img 
                v-if="product.imageUrls && product.imageUrls.length > 0" 
                :src="product.imageUrls[0]" 
                class="product-image" 
                alt="商品图片"
              />
              <span v-else>无图片</span>
            </td>
            <td class="product-title">{{ product.title }}</td>
            <td>¥{{ product.price }}</td>
            <td>{{ product.sellerName }}</td>
            <td>
              <span class="status-badge" :class="getStatusClass(product.status)">
                {{ getStatusText(product.status) }}
              </span>
            </td>
            <td>{{ formatDate(product.postedDate) }}</td>
            <td class="actions">
              <button @click="showProductDetail(product)">查看</button>
              <button @click="showEditForm(product)">编辑</button>
              <button 
                v-if="product.status === 'off_shelf_by_admin'" 
                @click="showApproveConfirm(product)"
              >上架</button>
              <button 
                v-if="product.status === 'available'" 
                @click="showOffShelfConfirm(product)"
              >下架</button>
              <button 
                v-if="product.status !== 'deleted'" 
                class="delete-btn" 
                @click="showDeleteConfirm(product)"
              >删除</button>
            </td>
          </tr>
          <tr v-if="products.length === 0">
            <td colspan="9" class="no-data">暂无商品数据</td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 分页控件 -->
    <div class="pagination">
      <button 
        :disabled="currentPage <= 1" 
        @click="changePage(currentPage - 1)"
      >上一页</button>
      <span>{{ currentPage }} / {{ totalPages }}</span>
      <button 
        :disabled="currentPage >= totalPages" 
        @click="changePage(currentPage + 1)"
      >下一页</button>
      <span class="page-info">共 {{ totalItems }} 条记录</span>
    </div>
    
    <!-- 提示消息 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
      <span class="close-notification" @click="closeNotification">×</span>
    </div>
    
    <!-- 商品详情对话框 -->
    <div v-if="detailVisible" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>商品详情</h3>
          <button class="close-btn" @click="detailVisible = false">×</button>
        </div>
        <div class="modal-body product-detail">
          <div class="product-images" v-if="currentProduct && currentProduct.imageUrls">
            <img 
              v-for="(url, index) in currentProduct.imageUrls" 
              :key="index" 
              :src="url" 
              class="detail-image" 
              alt="商品图片"
            />
          </div>
          <div class="product-info" v-if="currentProduct">
            <div class="info-item">
              <span class="label">ID:</span>
              <span>{{ currentProduct.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">标题:</span>
              <span>{{ currentProduct.title }}</span>
            </div>
            <div class="info-item">
              <span class="label">价格:</span>
              <span>¥{{ currentProduct.price }}</span>
            </div>
            <div class="info-item">
              <span class="label">分类:</span>
              <span>{{ currentProduct.categoryName }}</span>
            </div>
            <div class="info-item">
              <span class="label">状态:</span>
              <span>{{ getStatusText(currentProduct.status) }}</span>
            </div>
            <div class="info-item">
              <span class="label">卖家:</span>
              <span>{{ currentProduct.sellerName }}</span>
            </div>
            <div class="info-item">
              <span class="label">发布时间:</span>
              <span>{{ formatDate(currentProduct.postedDate) }}</span>
            </div>
            <div class="info-item">
              <span class="label">商品成色:</span>
              <span>{{ currentProduct.condition }}</span>
            </div>
            <div class="info-item">
              <span class="label">位置:</span>
              <span>{{ currentProduct.location }}</span>
            </div>
            <div class="info-item">
              <span class="label">配送方式:</span>
              <span>{{ currentProduct.deliveryMethod }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">描述:</span>
              <p class="description">{{ currentProduct.description }}</p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="detailVisible = false">关闭</button>
        </div>
      </div>
    </div>
    
    <!-- 编辑商品对话框 -->
    <div v-if="editVisible" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>编辑商品</h3>
          <button class="close-btn" @click="editVisible = false">×</button>
        </div>
        <div class="modal-body">
          <form class="edit-form" @submit.prevent="handleEditSubmit">
            <div class="form-group">
              <label>标题</label>
              <input type="text" v-model="editForm.title" required />
            </div>
            <div class="form-group">
              <label>价格</label>
              <input type="number" step="0.01" v-model="editForm.price" required />
            </div>
            <div class="form-group">
              <label>分类</label>
              <select v-model="editForm.categoryId" required>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label>商品成色</label>
              <select v-model="editForm.condition" required>
                <option value="NEW">全新</option>
                <option value="LIKE_NEW">几乎全新</option>
                <option value="GOOD">良好</option>
                <option value="FAIR">一般</option>
                <option value="POOR">较差</option>
              </select>
            </div>
            <div class="form-group">
              <label>位置</label>
              <input type="text" v-model="editForm.location" />
            </div>
            <div class="form-group">
              <label>配送方式</label>
              <select v-model="editForm.deliveryMethod">
                <option value="PICKUP">自提</option>
                <option value="DELIVERY">配送</option>
                <option value="BOTH">两者都可</option>
              </select>
            </div>
            <div class="form-group full-width">
              <label>描述</label>
              <textarea v-model="editForm.description" rows="5" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" @click="editVisible = false">取消</button>
          <button type="button" class="primary-btn" @click="handleEditSubmit">保存</button>
        </div>
      </div>
    </div>
    
    <!-- 确认对话框 -->
    <div v-if="confirmVisible" class="modal-overlay">
      <div class="modal-container confirm-dialog">
        <div class="modal-header">
          <h3>{{ confirmTitle }}</h3>
          <button class="close-btn" @click="confirmVisible = false">×</button>
        </div>
        <div class="modal-body">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="modal-footer">
          <button @click="confirmVisible = false">取消</button>
          <button class="primary-btn" @click="handleConfirm">确认</button>
        </div>
      </div>
    </div>
    
    <!-- 下架原因弹窗 -->
    <div v-if="offShelfModalVisible" class="modal-overlay">
      <div class="modal-container confirm-dialog">
        <div class="modal-header">
          <h3>商品下架</h3>
          <button class="close-btn" @click="offShelfModalVisible = false">×</button>
        </div>
        <div class="modal-body">
          <div v-if="offShelfTarget">
            <div class="info-item">
              <span class="label">商品名称:</span>
              <span>{{ offShelfTarget.title }}</span>
            </div>
            <div class="form-group full-width">
              <label>下架原因:</label>
              <textarea 
                v-model="offShelfForm.reason" 
                rows="6" 
                placeholder="请输入下架原因，该原因将发送给商家..."
                class="reason-textarea"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="offShelfModalVisible = false">取消</button>
          <button 
            class="primary-btn" 
            @click="handleOffShelf" 
            :disabled="!offShelfForm.reason || processing"
          >
            {{ processing ? '处理中...' : '确认下架' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { 
  getProducts, 
  getProductDetail, 
  updateProduct, 
  updateProductStatus, 
  deleteProduct,
  batchDeleteProducts,
  batchApproveProducts
} from '@/admin/api/products'
import { getAllCategories } from '@/admin/api/categories'
import { getOrCreateConversation, sendSystemMessage } from '@/admin/api/messages'

// 商品列表数据
const products = ref([])
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))
const selectedProducts = ref([])
const isAllSelected = computed(() => {
  return products.value.length > 0 && selectedProducts.value.length === products.value.length
})

// 搜索和筛选条件
const searchQuery = reactive({
  keyword: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

// 商品详情相关
const detailVisible = ref(false)
const currentProduct = ref(null)

// 编辑商品相关
const editVisible = ref(false)
const editForm = reactive({
  id: null,
  title: '',
  price: '',
  categoryId: '',
  condition: '',
  location: '',
  deliveryMethod: '',
  description: ''
})
const categories = ref([])

// 确认对话框相关
const confirmVisible = ref(false)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmAction = ref(null)
const confirmParam = ref(null)

// 下架原因弹窗相关
const offShelfModalVisible = ref(false)
const offShelfTarget = ref(null)
const offShelfForm = reactive({
  reason: ''
})
const processing = ref(false)

// 提示消息相关
const notification = reactive({
  show: false,
  message: '',
  type: 'success',
  timer: null
})

// 初始化
onMounted(async () => {
  await fetchCategories()
  await fetchProducts()
})

// 获取商品分类
const fetchCategories = async () => {
  try {
    const res = await getAllCategories()
    if (res.code === 200) {
      categories.value = res.data
    }
  } catch (error) {
    console.error('获取商品分类失败:', error)
  }
}

// 获取商品列表
const fetchProducts = async () => {
  try {
    const params = {
      ...searchQuery,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    
    const res = await getProducts(params)
    if (res.code === 200) {
      products.value = res.data.records
      totalItems.value = res.data.total
      // 清空选中状态
      selectedProducts.value = []
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 搜索商品
const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
}

// 切换页码
const changePage = (page) => {
  currentPage.value = page
  fetchProducts()
}

// 全选/取消全选
const toggleSelectAll = (e) => {
  if (e.target.checked) {
    selectedProducts.value = products.value.map(item => item.id)
  } else {
    selectedProducts.value = []
  }
}

// 显示商品详情
const showProductDetail = async (product) => {
  try {
    const res = await getProductDetail(product.id)
    if (res.code === 200) {
      currentProduct.value = res.data
      detailVisible.value = true
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
  }
}

// 显示编辑表单
const showEditForm = (product) => {
  // 复制商品数据到表单
  Object.assign(editForm, {
    id: product.id,
    title: product.title,
    price: product.price,
    categoryId: product.categoryId,
    condition: product.condition,
    location: product.location,
    deliveryMethod: product.deliveryMethod,
    description: product.description
  })
  
  editVisible.value = true
}

// 提交编辑表单
const handleEditSubmit = async () => {
  try {
    const res = await updateProduct(editForm.id, editForm)
    if (res.code === 200) {
      editVisible.value = false
      await fetchProducts() // 刷新列表
      showNotification('商品更新成功')
    } else {
      showNotification(res.message || '商品更新失败', 'error')
    }
  } catch (error) {
    console.error('更新商品失败:', error)
    showNotification('商品更新失败: ' + (error.message || '未知错误'), 'error')
  }
}

// 显示上架确认
const showApproveConfirm = (product) => {
  confirmTitle.value = '确认上架商品'
  confirmMessage.value = `确定要上架商品 "${product.title}" 吗？`
  confirmAction.value = 'approve'
  confirmParam.value = product
  confirmVisible.value = true
}

// 显示下架确认
const showOffShelfConfirm = (product) => {
  offShelfTarget.value = product
  offShelfForm.reason = '' // 清空原因
  offShelfModalVisible.value = true
}

// 显示删除确认
const showDeleteConfirm = (product) => {
  confirmTitle.value = '确认删除商品'
  confirmMessage.value = `确定要删除商品 "${product.title}" 吗？此操作不可恢复！`
  confirmAction.value = 'delete'
  confirmParam.value = product
  confirmVisible.value = true
}

// 显示批量上架确认
const showBatchApproveConfirm = () => {
  confirmTitle.value = '批量上架商品'
  confirmMessage.value = `确定要上架选中的 ${selectedProducts.value.length} 个商品吗？`
  confirmAction.value = 'batchApprove'
  confirmVisible.value = true
}

// 显示批量删除确认
const showBatchDeleteConfirm = () => {
  confirmTitle.value = '批量删除商品'
  confirmMessage.value = `确定要删除选中的 ${selectedProducts.value.length} 个商品吗？此操作不可恢复！`
  confirmAction.value = 'batchDelete'
  confirmVisible.value = true
}

// 处理确认操作
const handleConfirm = async () => {
  try {
    let res
    let successMessage = ''
    
    switch (confirmAction.value) {
      case 'approve':
        res = await updateProductStatus(confirmParam.value.id, 'available')
        successMessage = `商品"${confirmParam.value.title}"上架成功`
        break
      case 'delete':
        res = await deleteProduct(confirmParam.value.id)
        successMessage = `商品"${confirmParam.value.title}"删除成功`
        break
      case 'batchApprove':
        res = await batchApproveProducts(selectedProducts.value)
        successMessage = `批量上架操作完成，成功上架${res.data.successCount}个商品`
        break
      case 'batchDelete':
        res = await batchDeleteProducts(selectedProducts.value)
        successMessage = `批量删除操作完成，成功删除${res.data.successCount}个商品`
        break
    }
    
    if (res && res.code === 200) {
      await fetchProducts() // 刷新列表
      showNotification(successMessage)
    } else {
      showNotification(res.message || '操作失败', 'error')
    }
  } catch (error) {
    console.error('操作失败:', error)
    showNotification('操作失败: ' + (error.message || '未知错误'), 'error')
  } finally {
    confirmVisible.value = false
  }
}

// 处理下架操作
const handleOffShelf = async () => {
  if (!offShelfTarget.value || !offShelfForm.reason || processing.value) return

  processing.value = true
  try {
    const res = await updateProductStatus(offShelfTarget.value.id, 'off_shelf_by_admin')
    if (res.code === 200) {
      // 发送通知消息给卖家
      await sendNotificationToSeller(
        offShelfTarget.value.sellerId,
        `您的商品"${offShelfTarget.value.title}"已被管理员下架，原因：${offShelfForm.reason}`
      )
      
      offShelfModalVisible.value = false
      await fetchProducts() // 刷新列表
      showNotification(`商品"${offShelfTarget.value.title}"下架成功`)
    } else {
      showNotification(res.message || '商品下架失败', 'error')
    }
  } catch (error) {
    console.error('商品下架失败:', error)
    showNotification('商品下架失败: ' + (error.message || '未知错误'), 'error')
  } finally {
    processing.value = false
  }
}

// 发送通知消息给卖家
const sendNotificationToSeller = async (sellerId, content) => {
  try {
    // 创建或获取与卖家的会话
    const conversationRes = await getOrCreateConversation(sellerId)
    if (conversationRes.code === 200) {
      // 发送系统消息
      const messageData = {
        receiverId: sellerId,
        content: content
      }
      
      await sendSystemMessage(messageData)
    }
  } catch (error) {
    console.error('发送通知消息失败:', error)
    // 通知发送失败不影响主流程，只记录错误
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending_review': '待审核',
    'available': '已上架',
    'off_shelf_by_seller': '已下架',
    'off_shelf_by_admin': '管理员下架',
    'sold': '已售出',
    'deleted': '已删除'
  }
  return statusMap[status] || status
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    'pending_review': 'status-pending',
    'available': 'status-available',
    'off_shelf_by_seller': 'status-off',
    'off_shelf_by_admin': 'status-off-admin',
    'sold': 'status-sold',
    'deleted': 'status-deleted'
  }
  return classMap[status] || ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 显示提示消息
const showNotification = (message, type = 'success') => {
  // 如果已有通知，先清除定时器
  if (notification.timer) {
    clearTimeout(notification.timer)
  }
  
  // 设置新通知
  notification.show = true
  notification.message = message
  notification.type = type
  
  // 3秒后自动关闭
  notification.timer = setTimeout(() => {
    notification.show = false
  }, 3000)
}

// 关闭提示消息
const closeNotification = () => {
  notification.show = false
  if (notification.timer) {
    clearTimeout(notification.timer)
  }
}
</script>

<style scoped>
.product-management {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

/* 搜索和筛选区域 */
.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.search-box {
  display: flex;
}

.search-box input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  width: 250px;
}

.search-box button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.status-filter select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.batch-actions {
  margin-left: auto;
}

.batch-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.approve-btn {
  background-color: #52c41a;
  color: white;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

/* 表格样式 */
.product-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
}

.product-table th,
.product-table td {
  padding: 8px 6px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.product-table th {
  background-color: #f7f7f7;
  font-weight: 600;
}

.product-table tbody tr:hover {
  background-color: #f5f5f5;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.product-title {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background-color: #faad14;
  color: white;
}

.status-available {
  background-color: #52c41a;
  color: white;
}

.status-off,
.status-off-admin {
  background-color: #faad14;
  color: white;
}

.status-sold {
  background-color: #1890ff;
  color: white;
}

.status-deleted {
  background-color: #ff4d4f;
  color: white;
}

.actions button {
  padding: 3px 6px;
  margin-right: 3px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.actions button.delete-btn {
  background-color: #ff4d4f;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #999;
}

/* 分页控件 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
}

.pagination button {
  padding: 6px 12px;
  margin: 0 5px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  margin-left: 15px;
  color: #666;
}

/* 模态对话框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.confirm-dialog {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-footer button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
}

.modal-footer button.primary-btn {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

/* 商品详情样式 */
.product-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.product-images {
  flex: 0 0 300px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detail-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  object-fit: cover;
  border-radius: 4px;
}

.product-info {
  flex: 1;
  min-width: 300px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .label {
  font-weight: 600;
  color: #666;
}

.full-width {
  width: 100%;
}

.description {
  white-space: pre-line;
  margin-top: 8px;
}

/* 编辑表单样式 */
.edit-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.form-group {
  flex: 0 0 calc(50% - 15px);
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  flex: 0 0 100%;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 600;
  color: #666;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group textarea {
  resize: vertical;
}

/* 提示消息样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 2000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 250px;
  max-width: 400px;
}

.notification.success {
  background-color: #52c41a;
}

.notification.error {
  background-color: #ff4d4f;
}

.notification.warning {
  background-color: #faad14;
}

.close-notification {
  margin-left: 15px;
  cursor: pointer;
  font-size: 18px;
}

/* 下架原因弹窗样式 */
.reason-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
  min-height: 100px;
}

/* 处理中状态的按钮 */
.primary-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-box input {
    flex: 1;
  }
  
  .status-filter {
    width: 100%;
  }
  
  .batch-actions {
    margin-left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  
  .form-group {
    flex: 0 0 100%;
  }
}
</style> 