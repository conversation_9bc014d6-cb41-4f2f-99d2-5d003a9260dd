package io.github.qifan777.knowledge.user.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import io.github.qifan777.knowledge.user.User;
import io.github.qifan777.knowledge.user.UserDraft;
import io.github.qifan777.knowledge.user.UserFetcher;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.util.Objects;
import org.babyfish.jimmer.Input;
import org.babyfish.jimmer.internal.FixedInputField;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.fetcher.ViewMetadata;
import org.jetbrains.annotations.NotNull;

@GeneratedBy(
        file = "<dive-into-spring-ai>/src/main/dto/User.dto"
)
@JsonDeserialize(
        builder = UserRegisterInput.Builder.class
)
public class UserRegisterInput implements Input<User> {
    public static final ViewMetadata<User, UserRegisterInput> METADATA = 
        new ViewMetadata<User, UserRegisterInput>(
            UserFetcher.$
                .phone()
                .password(),
            UserRegisterInput::new
    );

    @FixedInputField
    private String phone;

    @FixedInputField
    private String password;

    public UserRegisterInput() {
    }

    public UserRegisterInput(@NotNull User base) {
        this.phone = base.phone();
        this.password = base.password();
    }

    /**
     * 手机号
     */
    @NotNull
    public String getPhone() {
        if (phone == null) {
            throw new IllegalStateException("The property \"phone\" is not specified");
        }
        return phone;
    }

    public void setPhone(@NotNull String phone) {
        this.phone = phone;
    }

    /**
     * 密码
     */
    @NotNull
    public String getPassword() {
        if (password == null) {
            throw new IllegalStateException("The property \"password\" is not specified");
        }
        return password;
    }

    public void setPassword(@NotNull String password) {
        this.password = password;
    }

    @Override
    public User toEntity() {
        return UserDraft.$.produce(__draft -> {
            __draft.setPhone(phone);
            __draft.setPassword(password);
        });
    }

    @Override
    public int hashCode() {
        int hash = Objects.hashCode(phone);
        hash = hash * 31 + Objects.hashCode(password);
        return hash;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        UserRegisterInput other = (UserRegisterInput) o;
        if (!Objects.equals(phone, other.phone)) {
            return false;
        }
        if (!Objects.equals(password, other.password)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("UserRegisterInput").append('(');
        builder.append("phone=").append(phone);
        builder.append(", password=").append(password);
        builder.append(')');
        return builder.toString();
    }

    @JsonPOJOBuilder(
            withPrefix = ""
    )
    public static class Builder {
        private String phone;

        private String password;

        public Builder phone(String phone) {
            this.phone = Objects.requireNonNull(phone, "The property \"phone\" cannot be null");
            return this;
        }

        public Builder password(String password) {
            this.password = Objects.requireNonNull(password, "The property \"password\" cannot be null");
            return this;
        }

        public UserRegisterInput build() {
            UserRegisterInput _input = new UserRegisterInput();
            if (phone == null) {
                throw Input.unknownNonNullProperty(UserRegisterInput.class, "phone");
            }
            _input.setPhone(phone);
            if (password == null) {
                throw Input.unknownNonNullProperty(UserRegisterInput.class, "password");
            }
            _input.setPassword(password);
            return _input;
        }
    }
}
