2025-07-29T09:30:01.524+08:00  INFO 24572 --- [shang<PERSON>] [nio-8080-exec-8] c.l.s.config.DatabaseChatMemory          : 📋 从数据库查询到 1 条原始消息
2025-07-29T09:30:01.524+08:00 ERROR 24572 --- [shang<PERSON>] [nio-8080-exec-8] c.l.s.config.DatabaseChatMemory          : ⚠️ 查询结果中包含 1 个null值！
2025-07-29T09:30:01.524+08:00 ERROR 24572 --- [shang<PERSON>] [nio-8080-exec-8] c.l.s.config.DatabaseChatMemory          : ❌ 索引 0 的消息为null
2025-07-29T09:30:01.525+08:00  INFO 24572 --- [shang<PERSON>] [nio-8080-exec-8] c.l.s.config.DatabaseChatMemory          : ✅ 成功加载会话 5f35917d-b25b-4acd-8166-8b3f812ac0a1 的 0 条历史消息用于AI记忆
2025-07-29T09:30:01.525+08:00  INFO 24572 --- [shang<PERSON>] [nio-8080-exec-8] c.l.s.service.impl.AiChatServiceImpl     : 🤖 Agent模式 - 会话 5f35917d-b25b-4acd-8166-8b3f812ac0a1 加载了 0 条历史消息，启用记忆功能
2025-07-29T09:30:01.555+08:00  INFO 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : 🔍 DatabaseChatMemory.get() 被调用 - 会话ID: 5f35917d-b25b-4acd-8166-8b3f812ac0a1, 请求消息数: 10
2025-07-29T09:30:01.555+08:00 DEBUG 24572 --- [shangcheng] [oundedElastic-1] c.l.s.m.A.findRecentMessagesBySessionId  : ==>  Preparing: SELECT * FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = ? AND zhshtp_id IS NOT NULL AND zhshtp_type IS NOT NULL AND zhshtp_text_content IS NOT NULL ORDER BY zhshtp_created_time DESC LIMIT ?
2025-07-29T09:30:01.555+08:00 DEBUG 24572 --- [shangcheng] [oundedElastic-1] c.l.s.m.A.findRecentMessagesBySessionId  : ==> Parameters: 5f35917d-b25b-4acd-8166-8b3f812ac0a1(String), 10(Integer)
2025-07-29T09:30:01.556+08:00 DEBUG 24572 --- [shangcheng] [oundedElastic-1] c.l.s.m.A.findRecentMessagesBySessionId  : <==      Total: 1
2025-07-29T09:30:01.557+08:00  INFO 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : 📋 从数据库查询到 1 条原始消息
2025-07-29T09:30:01.557+08:00 ERROR 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : ⚠️ 查询结果中包含 1 个null值！
2025-07-29T09:30:01.557+08:00 ERROR 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : ❌ 索引 0 的消息为null
2025-07-29T09:30:01.557+08:00  INFO 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : ✅ 成功加载会话 5f35917d-b25b-4acd-8166-8b3f812ac0a1 的 0 条历史消息用于AI记忆
2025-07-29T09:30:01.558+08:00 DEBUG 24572 --- [shangcheng] [oundedElastic-1] c.l.s.config.DatabaseChatMemory          : DatabaseChatMemory.add() 被调用，会话ID: 5f35917d-b25b-4acd-8166-8b3f812ac0a1, 消息数量: 1
2025-07-29T09:30:04.117+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : ==>  Preparing: SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_username = ?
2025-07-29T09:30:04.117+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : ==> Parameters: 123456(String)
2025-07-29T09:30:04.119+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : <==      Total: 1
2025-07-29T09:30:04.124+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : ==>  Preparing: SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_username = ?
2025-07-29T09:30:04.124+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : ==> Parameters: 123456(String)
2025-07-29T09:30:04.124+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.mapper.UserMapper.findByUsername   : <==      Total: 1
2025-07-29T09:30:04.131+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.m.C.findConversationsByUserId      : ==>  Preparing: SELECT * FROM tb_lzhshtp_conversations WHERE lzhshtp_user1_id = ? OR lzhshtp_user2_id = ? ORDER BY lzhshtp_updated_at DESC
2025-07-29T09:30:04.131+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.m.C.findConversationsByUserId      : ==> Parameters: 2(Long), 2(Long)
2025-07-29T09:30:04.131+08:00 DEBUG 24572 --- [shangcheng] [nio-8080-exec-7] c.l.s.m.C.findConversationsByUserId      : <==      Total: 4
2025-07-29T09:30:06.119+08:00  WARN 24572 --- [shangcheng] [ient-2-Worker-0] o.s.a.a.r.SpringAiRetryAutoConfiguration : Retry error. Retry count:1

org.springframework.web.client.RestClientException: Error while extracting response for type [com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletion] and content type [application/json]
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:262) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:819) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:775) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:579) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:533) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:680) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:814) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:774) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:763) ~[spring-web-6.2.8.jar:6.2.8]
	at com.alibaba.cloud.ai.dashscope.api.DashScopeApi.chatCompletionEntity(DashScopeApi.java:1307) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$call$1(DashScopeChatModel.java:173) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357) ~[spring-retry-2.0.12.jar:na]
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230) ~[spring-retry-2.0.12.jar:na]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$call$3(DashScopeChatModel.java:173) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.call(DashScopeChatModel.java:169) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$1.aroundCall(DefaultChatClient.java:675) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundCall$1(DefaultAroundAdvisorChain.java:98) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundCall(DefaultAroundAdvisorChain.java:98) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetChatResponse(DefaultChatClient.java:488) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatResponse$1(DefaultChatClient.java:477) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatResponse(DefaultChatClient.java:477) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetChatResponse(DefaultChatClient.java:461) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:511) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant.apply(ProductAssistant.java:61) ~[classes/:na]
	at com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant.apply(ProductAssistant.java:1) ~[classes/:na]
	at org.springframework.ai.model.function.DefaultFunctionCallbackBuilder$DefaultFunctionInvokingSpec.lambda$build$0(DefaultFunctionCallbackBuilder.java:145) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.apply(FunctionInvokingFunctionCallback.java:51) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.apply(FunctionInvokingFunctionCallback.java:38) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.AbstractFunctionCallback.call(AbstractFunctionCallback.java:108) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.call(FunctionInvokingFunctionCallback.java:38) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.model.AbstractToolCallSupport.executeFunctions(AbstractToolCallSupport.java:222) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.model.AbstractToolCallSupport.handleToolCalls(AbstractToolCallSupport.java:156) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$stream$9(DashScopeChatModel.java:282) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onNext(FluxFlatMap.java:388) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapInner.onNext(FluxSwitchMapNoPrefetch.java:408) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2570) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapInner.onSubscribe(FluxSwitchMapNoPrefetch.java:378) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onSubscribe(FluxMapFuseable.java:96) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapMain.subscribeInner(FluxSwitchMapNoPrefetch.java:219) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapMain.onNext(FluxSwitchMapNoPrefetch.java:164) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.tryEmit(FluxFlatMap.java:547) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onNext(FluxFlatMap.java:988) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2096) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoReduceSeed$ReduceSeedSubscriber.onComplete(MonoReduceSeed.java:163) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.checkTerminated(FluxWindowPredicate.java:768) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.drainRegular(FluxWindowPredicate.java:662) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.drain(FluxWindowPredicate.java:748) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.onComplete(FluxWindowPredicate.java:814) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowPredicateMain.onNext(FluxWindowPredicate.java:243) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxTakeUntil$TakeUntilPredicateSubscriber.onNext(FluxTakeUntil.java:95) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onNext(MonoFlatMapMany.java:251) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMap$WeakScalarSubscription.request(FluxConcatMap.java:480) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2366) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:202) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.onNextNewBuffer(FluxBufferPredicate.java:317) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.tryOnNext(FluxBufferPredicate.java:227) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.onNext(FluxBufferPredicate.java:200) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableConditionalSubscriber.onNext(FluxPeekFuseable.java:503) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onNext(FluxDoFinally.java:113) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatArray$ConcatArraySubscriber.onNext(FluxConcatArray.java:180) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:453) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:724) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:256) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPublish$PublishSubscriber.drain(FluxPublish.java:571) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPublish$PublishSubscriber.onNext(FluxPublish.java:310) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:453) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:724) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:256) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.adapter.JdkFlowAdapter$SubscriberToRS.onNext(JdkFlowAdapter.java:150) ~[reactor-core-3.7.7.jar:3.7.7]
	at java.net.http/jdk.internal.net.http.ResponseSubscribers$PublishingBodySubscriber.onNext(ResponseSubscribers.java:1006) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.ResponseSubscribers$PublishingBodySubscriber.onNext(ResponseSubscribers.java:846) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.schedule(Stream.java:210) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.receiveDataFrame(Stream.java:337) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.incoming(Stream.java:443) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection.processFrame(Http2Connection.java:812) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.frame.FramesDecoder.decode(FramesDecoder.java:155) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$FramesController.processReceivedData(Http2Connection.java:232) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection.asyncReceive(Http2Connection.java:674) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.processQueue(Http2Connection.java:1310) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.runOrSchedule(Http2Connection.java:1328) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.onNext(Http2Connection.java:1354) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.onNext(Http2Connection.java:1288) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$DelegateWrapper.onNext(SSLTube.java:210) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$SSLSubscriberWrapper.onNext(SSLTube.java:492) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$SSLSubscriberWrapper.onNext(SSLTube.java:295) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run1(SubscriberWrapper.java:316) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run(SubscriberWrapper.java:259) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.outgoing(SubscriberWrapper.java:232) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.outgoing(SubscriberWrapper.java:198) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader.processData(SSLFlowDelegate.java:444) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader$ReaderDownstreamPusher.run(SSLFlowDelegate.java:268) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:833) ~[na:na]
Caused by: org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "index" (class com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall), not marked as ignorable
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:408) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:356) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:229) ~[spring-web-6.2.8.jar:6.2.8]
	... 130 common frames omitted
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "index" (class com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall), not marked as ignorable (3 known properties: "function", "type", "id"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 342] (through reference chain: com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletion["output"]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionOutput["choices"]->java.util.ArrayList[0]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionOutput$Choice["message"]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage["tool_calls"]->java.util.ArrayList[0]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall["index"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1153) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2241) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1793) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperties(BeanDeserializerBase.java:1743) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:461) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:361) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:246) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:30) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:361) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:246) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:30) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2125) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1501) ~[jackson-databind-2.17.0.jar:2.17.0]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:397) ~[spring-web-6.2.8.jar:6.2.8]
	... 132 common frames omitted

2025-07-29T09:30:06.125+08:00 ERROR 24572 --- [shangcheng] [ient-2-Worker-0] c.l.s.ai.agent.product.ProductAssistant  : ProductAssistant处理请求失败

org.springframework.web.client.RestClientException: Error while extracting response for type [com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletion] and content type [application/json]
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:262) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:819) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:775) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:579) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:533) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:680) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:814) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:774) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:763) ~[spring-web-6.2.8.jar:6.2.8]
	at com.alibaba.cloud.ai.dashscope.api.DashScopeApi.chatCompletionEntity(DashScopeApi.java:1307) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$call$1(DashScopeChatModel.java:173) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357) ~[spring-retry-2.0.12.jar:na]
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230) ~[spring-retry-2.0.12.jar:na]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$call$3(DashScopeChatModel.java:173) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.call(DashScopeChatModel.java:169) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultChatClientRequestSpec$1.aroundCall(DefaultChatClient.java:675) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextAroundCall$1(DefaultAroundAdvisorChain.java:98) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextAroundCall(DefaultAroundAdvisorChain.java:98) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetChatResponse(DefaultChatClient.java:488) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatResponse$1(DefaultChatClient.java:477) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at io.micrometer.observation.Observation.observe(Observation.java:564) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatResponse(DefaultChatClient.java:477) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetChatResponse(DefaultChatClient.java:461) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:511) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant.apply(ProductAssistant.java:61) ~[classes/:na]
	at com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant.apply(ProductAssistant.java:1) ~[classes/:na]
	at org.springframework.ai.model.function.DefaultFunctionCallbackBuilder$DefaultFunctionInvokingSpec.lambda$build$0(DefaultFunctionCallbackBuilder.java:145) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.apply(FunctionInvokingFunctionCallback.java:51) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.apply(FunctionInvokingFunctionCallback.java:38) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.AbstractFunctionCallback.call(AbstractFunctionCallback.java:108) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.model.function.FunctionInvokingFunctionCallback.call(FunctionInvokingFunctionCallback.java:38) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.model.AbstractToolCallSupport.executeFunctions(AbstractToolCallSupport.java:222) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at org.springframework.ai.chat.model.AbstractToolCallSupport.handleToolCalls(AbstractToolCallSupport.java:156) ~[spring-ai-core-1.0.0-M5.jar:1.0.0-M5]
	at com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel.lambda$stream$9(DashScopeChatModel.java:282) ~[spring-ai-alibaba-core-1.0.0-M5.1.jar:1.0.0-M5.1]
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.onNext(FluxFlatMap.java:388) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapInner.onNext(FluxSwitchMapNoPrefetch.java:408) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2570) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapInner.onSubscribe(FluxSwitchMapNoPrefetch.java:378) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onSubscribe(FluxMapFuseable.java:96) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapMain.subscribeInner(FluxSwitchMapNoPrefetch.java:219) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxSwitchMapNoPrefetch$SwitchMapMain.onNext(FluxSwitchMapNoPrefetch.java:164) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlatMap$FlatMapMain.tryEmit(FluxFlatMap.java:547) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlatMap$FlatMapInner.onNext(FluxFlatMap.java:988) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2096) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoReduceSeed$ReduceSeedSubscriber.onComplete(MonoReduceSeed.java:163) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.checkTerminated(FluxWindowPredicate.java:768) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.drainRegular(FluxWindowPredicate.java:662) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.drain(FluxWindowPredicate.java:748) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowFlux.onComplete(FluxWindowPredicate.java:814) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxWindowPredicate$WindowPredicateMain.onNext(FluxWindowPredicate.java:243) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFilter$FilterSubscriber.onNext(FluxFilter.java:113) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxTakeUntil$TakeUntilPredicateSubscriber.onNext(FluxTakeUntil.java:95) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onNext(MonoFlatMapMany.java:251) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMap$WeakScalarSubscription.request(FluxConcatMap.java:480) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2366) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:202) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.onNextNewBuffer(FluxBufferPredicate.java:317) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.tryOnNext(FluxBufferPredicate.java:227) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxBufferPredicate$BufferPredicateSubscriber.onNext(FluxBufferPredicate.java:200) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableConditionalSubscriber.onNext(FluxPeekFuseable.java:503) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onNext(FluxDoFinally.java:113) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxConcatArray$ConcatArraySubscriber.onNext(FluxConcatArray.java:180) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:453) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:724) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:256) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPublish$PublishSubscriber.drain(FluxPublish.java:571) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxPublish$PublishSubscriber.onNext(FluxPublish.java:310) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:453) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:724) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:256) ~[reactor-core-3.7.7.jar:3.7.7]
	at reactor.adapter.JdkFlowAdapter$SubscriberToRS.onNext(JdkFlowAdapter.java:150) ~[reactor-core-3.7.7.jar:3.7.7]
	at java.net.http/jdk.internal.net.http.ResponseSubscribers$PublishingBodySubscriber.onNext(ResponseSubscribers.java:1006) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.ResponseSubscribers$PublishingBodySubscriber.onNext(ResponseSubscribers.java:846) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.schedule(Stream.java:210) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.receiveDataFrame(Stream.java:337) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Stream.incoming(Stream.java:443) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection.processFrame(Http2Connection.java:812) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.frame.FramesDecoder.decode(FramesDecoder.java:155) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$FramesController.processReceivedData(Http2Connection.java:232) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection.asyncReceive(Http2Connection.java:674) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.processQueue(Http2Connection.java:1310) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.runOrSchedule(Http2Connection.java:1328) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.onNext(Http2Connection.java:1354) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.Http2Connection$Http2TubeSubscriber.onNext(Http2Connection.java:1288) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$DelegateWrapper.onNext(SSLTube.java:210) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$SSLSubscriberWrapper.onNext(SSLTube.java:492) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLTube$SSLSubscriberWrapper.onNext(SSLTube.java:295) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run1(SubscriberWrapper.java:316) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper$DownstreamPusher.run(SubscriberWrapper.java:259) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.outgoing(SubscriberWrapper.java:232) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SubscriberWrapper.outgoing(SubscriberWrapper.java:198) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader.processData(SSLFlowDelegate.java:444) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SSLFlowDelegate$Reader$ReaderDownstreamPusher.run(SSLFlowDelegate.java:268) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149) ~[java.net.http:na]
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230) ~[java.net.http:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:833) ~[na:na]
Caused by: org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unrecognized field "index" (class com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall), not marked as ignorable
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:408) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:356) ~[spring-web-6.2.8.jar:6.2.8]
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:229) ~[spring-web-6.2.8.jar:6.2.8]
	... 130 common frames omitted
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "index" (class com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall), not marked as ignorable (3 known properties: "function", "type", "id"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 342] (through reference chain: com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletion["output"]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionOutput["choices"]->java.util.ArrayList[0]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionOutput$Choice["message"]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage["tool_calls"]->java.util.ArrayList[0]->com.alibaba.cloud.ai.dashscope.api.DashScopeApi$ChatCompletionMessage$ToolCall["index"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1153) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2241) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1793) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperties(BeanDeserializerBase.java:1743) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:461) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:361) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:246) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:30) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:361) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:246) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:30) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.SettableBeanProperty.deserialize(SettableBeanProperty.java:545) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeWithErrorWrapping(BeanDeserializer.java:570) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeUsingPropertyBased(BeanDeserializer.java:440) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeFromObjectUsingNonDefault(BeanDeserializerBase.java:1493) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:348) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2125) ~[jackson-databind-2.17.0.jar:2.17.0]
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1501) ~[jackson-databind-2.17.0.jar:2.17.0]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:397) ~[spring-web-6.2.8.jar:6.2.8]
	... 132 common frames omitted
