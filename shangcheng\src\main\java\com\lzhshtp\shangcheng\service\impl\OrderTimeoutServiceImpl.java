package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;
import com.lzhshtp.shangcheng.mapper.OrderMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.model.Order;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.dto.MessageRequest;
import com.lzhshtp.shangcheng.service.MessageService;
import com.lzhshtp.shangcheng.service.OrderTimeoutService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单超时处理服务实现类
 */
@Service
public class OrderTimeoutServiceImpl implements OrderTimeoutService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderTimeoutServiceImpl.class);
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private MessageService messageService;
    

    
    @Override
    @Transactional
    public String cancelExpiredOrders(OrderTimeoutConfigDTO config) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始执行订单超时检查，超时时间：{}分钟", config.getTimeoutMinutes());

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(config.getTimeoutMinutes());
            
            // 查询超时的未支付订单
            QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("lzhshtp_status", Order.OrderStatus.PENDING_PAYMENT)
                       .lt("lzhshtp_order_date", timeoutThreshold);
            
            List<Order> expiredOrders = orderMapper.selectList(queryWrapper);
            
            if (expiredOrders.isEmpty()) {
                String result = "未发现超时订单";
                logger.info(result);
                return result;
            }
            
            int cancelledCount = 0;
            int failedCount = 0;
            
            // 批量处理超时订单
            for (Order order : expiredOrders) {
                try {
                    // 取消订单
                    order.setStatus(Order.OrderStatus.CANCELLED);
                    orderMapper.updateById(order);
                    
                    // 恢复商品状态为可售
                    Product product = productMapper.selectById(order.getProductId());
                    if (product != null && Product.ProductStatus.SOLD.equals(product.getStatus())) {
                        product.setStatus(Product.ProductStatus.AVAILABLE);
                        productMapper.updateById(product);
                    }
                    
                    // 发送通知
                    if (config.getSendNotification()) {
                        sendCancellationNotifications(order, config);
                    }
                    
                    cancelledCount++;
                    logger.info("订单 {} 已自动取消（超时{}分钟）", order.getOrderId(), config.getTimeoutMinutes());
                    
                } catch (Exception e) {
                    failedCount++;
                    logger.error("取消订单 {} 失败", order.getOrderId(), e);
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            String result = String.format("订单超时检查完成，处理 %d 个订单，成功取消 %d 个，失败 %d 个，耗时 %d ms", 
                expiredOrders.size(), cancelledCount, failedCount, duration);
            
            logger.info(result);
            return result;
            
        } catch (Exception e) {
            logger.error("订单超时检查执行失败", e);
            return "执行失败：" + e.getMessage();
        }
    }
    
    @Override
    public String cancelExpiredOrders() {
        // 使用默认配置
        OrderTimeoutConfigDTO config = createDefaultConfig();
        return cancelExpiredOrders(config);
    }
    
    @Override
    public boolean isOrderExpired(Long orderId) {
        try {
            Order order = orderMapper.selectById(orderId);
            if (order == null || !Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
                return false;
            }

            // 使用默认超时时间30分钟
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);

            return order.getOrderDate().isBefore(timeoutThreshold);

        } catch (Exception e) {
            logger.error("检查订单是否超时失败，订单ID：{}", orderId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean cancelOrderManually(Long orderId, String reason) {
        try {
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                logger.warn("订单不存在：{}", orderId);
                return false;
            }
            
            if (!Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
                logger.warn("订单状态不允许取消，当前状态：{}，订单ID：{}", order.getStatus(), orderId);
                return false;
            }
            
            // 取消订单
            order.setStatus(Order.OrderStatus.CANCELLED);
            orderMapper.updateById(order);
            
            // 恢复商品状态
            Product product = productMapper.selectById(order.getProductId());
            if (product != null && Product.ProductStatus.SOLD.equals(product.getStatus())) {
                product.setStatus(Product.ProductStatus.AVAILABLE);
                productMapper.updateById(product);
            }
            
            // 发送通知
            sendManualCancellationNotification(order, reason);
            
            logger.info("订单 {} 已手动取消，原因：{}", orderId, reason);
            return true;
            
        } catch (Exception e) {
            logger.error("手动取消订单失败，订单ID：{}", orderId, e);
            return false;
        }
    }
    
    /**
     * 发送订单取消通知
     */
    private void sendCancellationNotifications(Order order, OrderTimeoutConfigDTO config) {
        try {
            if (config.getNotificationTargets().contains("buyer")) {
                // 通知买家
                String buyerMessage = String.format(
                    "您的订单 #%d 因超过 %d 分钟未支付已被自动取消。如需购买，请重新下单。",
                    order.getOrderId(), config.getTimeoutMinutes()
                );
                MessageRequest buyerRequest = new MessageRequest();
                buyerRequest.setReceiverId(order.getBuyerId());
                buyerRequest.setContent(buyerMessage);
                messageService.sendSystemMessage(1L, buyerRequest); // 使用系统管理员ID
            }
            
            if (config.getNotificationTargets().contains("seller")) {
                // 通知卖家
                String sellerMessage = String.format(
                    "您的商品订单 #%d 因买家超时未支付已被自动取消，商品已重新上架。",
                    order.getOrderId()
                );
                MessageRequest sellerRequest = new MessageRequest();
                sellerRequest.setReceiverId(order.getSellerId());
                sellerRequest.setContent(sellerMessage);
                messageService.sendSystemMessage(1L, sellerRequest); // 使用系统管理员ID
            }
            
        } catch (Exception e) {
            logger.error("发送订单取消通知失败，订单ID：{}", order.getOrderId(), e);
        }
    }
    
    /**
     * 发送手动取消通知
     */
    private void sendManualCancellationNotification(Order order, String reason) {
        try {
            // 通知买家
            String buyerMessage = String.format(
                "您的订单 #%d 已被取消。取消原因：%s",
                order.getOrderId(), reason
            );
            MessageRequest buyerRequest = new MessageRequest();
            buyerRequest.setReceiverId(order.getBuyerId());
            buyerRequest.setContent(buyerMessage);
            messageService.sendSystemMessage(1L, buyerRequest); // 使用系统管理员ID

            // 通知卖家
            String sellerMessage = String.format(
                "您的商品订单 #%d 已被取消，商品已重新上架。取消原因：%s",
                order.getOrderId(), reason
            );
            MessageRequest sellerRequest = new MessageRequest();
            sellerRequest.setReceiverId(order.getSellerId());
            sellerRequest.setContent(sellerMessage);
            messageService.sendSystemMessage(1L, sellerRequest); // 使用系统管理员ID
            
        } catch (Exception e) {
            logger.error("发送手动取消通知失败，订单ID：{}", order.getOrderId(), e);
        }
    }

    /**
     * 创建默认配置
     */
    private OrderTimeoutConfigDTO createDefaultConfig() {
        OrderTimeoutConfigDTO config = new OrderTimeoutConfigDTO();
        config.setEnabled(true);
        config.setTimeoutMinutes(30);
        config.setCheckInterval(5);
        config.setSendNotification(true);
        config.setNotificationMethods(List.of("message"));
        config.setNotificationTargets(List.of("buyer", "seller"));
        return config;
    }


}
