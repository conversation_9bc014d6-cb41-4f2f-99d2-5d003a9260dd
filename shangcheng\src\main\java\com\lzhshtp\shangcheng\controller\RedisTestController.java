package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis测试控制器
 */
@RestController
@RequestMapping("/api/redis")
public class RedisTestController {

    private final RedisUtils redisUtils;

    @Autowired
    public RedisTestController(RedisUtils redisUtils) {
        this.redisUtils = redisUtils;
    }

    /**
     * 测试字符串操作
     */
    @GetMapping("/string/{key}/{value}")
    public ApiResponse<Map<String, Object>> testString(@PathVariable String key, @PathVariable String value) {
        Map<String, Object> result = new HashMap<>();

        // 设置值
        redisUtils.set(key, value);

        // 获取值
        Object getValue = redisUtils.get(key);

        result.put("operation", "string");
        result.put("key", key);
        result.put("setValue", value);
        result.put("getValue", getValue);

        return ApiResponse.success(result);
    }

    /**
     * 测试带过期时间的字符串操作
     */
    @GetMapping("/string/expire/{key}/{value}/{seconds}")
    public ApiResponse<Map<String, Object>> testStringWithExpire(
            @PathVariable String key,
            @PathVariable String value,
            @PathVariable long seconds) {
        Map<String, Object> result = new HashMap<>();

        // 设置值并设置过期时间
        redisUtils.set(key, value, seconds);

        // 获取值
        Object getValue = redisUtils.get(key);

        // 获取过期时间
        long expire = redisUtils.getExpire(key);

        result.put("operation", "string with expire");
        result.put("key", key);
        result.put("setValue", value);
        result.put("getValue", getValue);
        result.put("expireTime", expire);

        return ApiResponse.success(result);
    }

    /**
     * 测试哈希操作
     */
    @GetMapping("/hash/{key}/{field}/{value}")
    public ApiResponse<Map<String, Object>> testHash(
            @PathVariable String key,
            @PathVariable String field,
            @PathVariable String value) {
        Map<String, Object> result = new HashMap<>();

        // 设置哈希值
        redisUtils.hset(key, field, value);

        // 获取哈希值
        Object getValue = redisUtils.hget(key, field);

        result.put("operation", "hash");
        result.put("key", key);
        result.put("field", field);
        result.put("setValue", value);
        result.put("getValue", getValue);

        return ApiResponse.success(result);
    }

    /**
     * 测试哈希操作 - 使用请求参数
     */
    @GetMapping("/hash-param")
    public ApiResponse<Map<String, Object>> testHashWithParams(
            @RequestParam String key,
            @RequestParam String field,
            @RequestParam String value) {
        Map<String, Object> result = new HashMap<>();

        // 设置哈希值
        redisUtils.hset(key, field, value);

        // 获取哈希值
        Object getValue = redisUtils.hget(key, field);

        result.put("operation", "hash");
        result.put("key", key);
        result.put("field", field);
        result.put("setValue", value);
        result.put("getValue", getValue);

        return ApiResponse.success(result);
    }

    /**
     * 测试哈希操作 - 使用POST方法
     */
    @PostMapping("/hash")
    public ApiResponse<Map<String, Object>> testHashPost(
            @RequestParam String key,
            @RequestParam String field,
            @RequestParam String value) {
        Map<String, Object> result = new HashMap<>();

        // 设置哈希值
        redisUtils.hset(key, field, value);

        // 获取哈希值
        Object getValue = redisUtils.hget(key, field);

        result.put("operation", "hash");
        result.put("key", key);
        result.put("field", field);
        result.put("setValue", value);
        result.put("getValue", getValue);

        return ApiResponse.success(result);
    }
    
    /**
     * 测试哈希操作 - 使用JSON请求体
     */
    @PostMapping("/hash-json")
    public ApiResponse<Map<String, Object>> testHashJson(@RequestBody Map<String, String> params) {
        String key = params.get("key");
        String field = params.get("field");
        String value = params.get("value");

        Map<String, Object> result = new HashMap<>();

        // 设置哈希值
        redisUtils.hset(key, field, value);

        // 获取哈希值
        Object getValue = redisUtils.hget(key, field);

        result.put("operation", "hash");
        result.put("key", key);
        result.put("field", field);
        result.put("setValue", value);
        result.put("getValue", getValue);

        return ApiResponse.success(result);
    }

    /**
     * 测试删除操作
     */
    @DeleteMapping("/{key}")
    public ApiResponse<Map<String, Object>> testDelete(@PathVariable String key) {
        Map<String, Object> result = new HashMap<>();

        // 检查键是否存在
        boolean existsBefore = redisUtils.hasKey(key);

        // 删除键
        redisUtils.del(key);

        // 再次检查键是否存在
        boolean existsAfter = redisUtils.hasKey(key);

        result.put("operation", "delete");
        result.put("key", key);
        result.put("existsBefore", existsBefore);
        result.put("existsAfter", existsAfter);

        return ApiResponse.success(result);
    }
} 