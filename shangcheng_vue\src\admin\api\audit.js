import request from '../utils/request'

/**
 * 自动审核相关API
 */

// 获取自动审核记录列表
export function getAutoAuditRecords(params) {
  return request({
    url: '/admin/audit/auto/records',
    method: 'get',
    params
  })
}

// 获取自动审核记录详情
export function getAutoAuditRecordDetail(recordId) {
  return request({
    url: `/admin/audit/auto/records/${recordId}`,
    method: 'get'
  })
}

// 获取自动审核统计数据
export function getAutoAuditStats(params) {
  return request({
    url: '/admin/audit/auto/stats',
    method: 'get',
    params
  })
}

/**
 * 人工审核相关API
 */

// 获取人工审核任务列表
export function getManualAuditTasks(params) {
  return request({
    url: '/admin/audit/manual/tasks',
    method: 'get',
    params
  })
}

// 获取人工审核统计数据
export function getManualAuditStats() {
  return request({
    url: '/admin/audit/manual/stats',
    method: 'get'
  })
}

// 认领审核任务
export function claimAuditTask(taskId, adminId) {
  return request({
    url: `/admin/audit/manual/tasks/${taskId}/claim`,
    method: 'post',
    data: { adminId }
  })
}

// 获取审核任务详情
export function getAuditTaskDetail(taskId) {
  return request({
    url: `/admin/audit/manual/tasks/${taskId}`,
    method: 'get'
  })
}

// 提交人工审核决策
export function submitManualAuditDecision(taskId, data) {
  return request({
    url: `/admin/audit/manual/tasks/${taskId}/decision`,
    method: 'post',
    data
  })
}

// 获取审核任务的完整材料
export function getAuditTaskMaterials(taskId) {
  return request({
    url: `/admin/audit/manual/tasks/${taskId}/materials`,
    method: 'get'
  })
}

/**
 * 二度复审相关API
 */

// 获取二度复审任务列表
export function getSecondReviewTasks(params) {
  return request({
    url: '/admin/audit/second-review/tasks',
    method: 'get',
    params
  })
}

// 获取二度复审统计数据
export function getSecondReviewStats() {
  return request({
    url: '/admin/audit/second-review/stats',
    method: 'get'
  })
}

// 认领二度复审任务
export function claimSecondReviewTask(taskId, reviewerId) {
  return request({
    url: `/admin/audit/second-review/tasks/${taskId}/claim`,
    method: 'post',
    data: { reviewerId }
  })
}

// 获取二度复审任务详情
export function getSecondReviewTaskDetail(taskId) {
  return request({
    url: `/admin/audit/second-review/tasks/${taskId}`,
    method: 'get'
  })
}

// 提交二度复审决策
export function submitSecondReviewDecision(taskId, data) {
  return request({
    url: `/admin/audit/second-review/tasks/${taskId}/decision`,
    method: 'post',
    data
  })
}

// 获取二度复审任务的所有材料
export function getSecondReviewTaskMaterials(taskId) {
  return request({
    url: `/admin/audit/second-review/tasks/${taskId}/materials`,
    method: 'get'
  })
}

/**
 * 材料补充相关API
 */

// 创建材料请求
export function createMaterialRequest(data) {
  return request({
    url: '/admin/audit/materials/request',
    method: 'post',
    data
  })
}

// 获取材料请求列表
export function getMaterialRequests(params) {
  return request({
    url: '/admin/audit/materials/requests',
    method: 'get',
    params
  })
}

// 获取材料请求详情
export function getMaterialRequestDetail(requestId) {
  return request({
    url: `/admin/audit/materials/requests/${requestId}`,
    method: 'get'
  })
}

// 审核提交的材料
export function reviewSubmittedMaterials(requestId, data) {
  return request({
    url: `/admin/audit/materials/requests/${requestId}/review`,
    method: 'post',
    data
  })
}

/**
 * 审核配置相关API
 */

// 获取审核配置
export function getAuditConfig() {
  return request({
    url: '/admin/audit/config',
    method: 'get'
  })
}

// 更新审核配置
export function updateAuditConfig(data) {
  return request({
    url: '/admin/audit/config',
    method: 'put',
    data
  })
}

// 获取敏感词列表
export function getSensitiveWords(params) {
  return request({
    url: '/admin/audit/sensitive-words',
    method: 'get',
    params
  })
}

// 添加敏感词
export function addSensitiveWord(data) {
  return request({
    url: '/admin/audit/sensitive-words',
    method: 'post',
    data
  })
}

// 更新敏感词
export function updateSensitiveWord(wordId, data) {
  return request({
    url: `/admin/audit/sensitive-words/${wordId}`,
    method: 'put',
    data
  })
}

// 删除敏感词
export function deleteSensitiveWord(wordId) {
  return request({
    url: `/admin/audit/sensitive-words/${wordId}`,
    method: 'delete'
  })
}

// 批量导入敏感词
export function importSensitiveWords(data) {
  return request({
    url: '/admin/audit/sensitive-words/import',
    method: 'post',
    data
  })
}

/**
 * 审核日志相关API
 */

// 获取审核日志
export function getAuditLogs(params) {
  return request({
    url: '/admin/audit/logs',
    method: 'get',
    params
  })
}

// 获取审核员工作统计
export function getAuditorStats(params) {
  return request({
    url: '/admin/audit/auditor-stats',
    method: 'get',
    params
  })
}

// 获取审核效率统计
export function getAuditEfficiencyStats(params) {
  return request({
    url: '/admin/audit/efficiency-stats',
    method: 'get',
    params
  })
}

/**
 * 测试相关API
 */

// 测试阿里云图片审核
export function testAliyunImageAudit(imageUrl) {
  return request({
    url: '/test/image-audit/aliyun/single',
    method: 'post',
    params: { imageUrl }
  })
}

// 测试完整图片审核流程
export function testFullImageAudit(data) {
  return request({
    url: '/test/image-audit/full',
    method: 'post',
    data
  })
}

// 检查阿里云服务状态
export function checkAliyunStatus() {
  return request({
    url: '/test/image-audit/aliyun/status',
    method: 'get'
  })
}
