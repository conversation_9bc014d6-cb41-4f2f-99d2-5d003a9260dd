package com.lzhshtp.shangcheng.config;

import lombok.AllArgsConstructor;
import org.springframework.ai.autoconfigure.vectorstore.redis.RedisVectorStoreAutoConfiguration;
import org.springframework.ai.autoconfigure.vectorstore.redis.RedisVectorStoreProperties;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisConnectionDetails;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPooled;

/**
 * Redis向量数据库配置（完全模仿qifancode实现）
 * 用于RAG（检索增强生成）功能
 */
@Configuration
// 禁用SpringAI提供的RedisStack向量数据库的自动配置，会和Redis的配置冲突
@EnableAutoConfiguration(exclude = {RedisVectorStoreAutoConfiguration.class})
// 读取RedisStack的配置信息
@EnableConfigurationProperties({RedisVectorStoreProperties.class})
@AllArgsConstructor
public class VectorStoreConfig {

    /**
     * 创建RedisStack向量数据库
     *
     * @param embeddingModel 嵌入模型
     * @param properties     redis-stack的配置信息
     * @param redisConnectionDetails redis连接详情
     * @return vectorStore 向量数据库
     */
    @Bean
    public VectorStore vectorStore(EmbeddingModel embeddingModel,
                                   RedisVectorStoreProperties properties,
                                   RedisConnectionDetails redisConnectionDetails) {

        // 添加调试日志
        System.out.println("=== Redis向量数据库配置 ===");
        System.out.println("Index Name: " + properties.getIndex());
        System.out.println("Prefix: " + properties.getPrefix());
        System.out.println("Initialize Schema: " + properties.isInitializeSchema());
        System.out.println("Redis Host: " + redisConnectionDetails.getStandalone().getHost());
        System.out.println("Redis Port: " + redisConnectionDetails.getStandalone().getPort());
        System.out.println("========================");

        // 使用qifancode的配置方式（虽然API被标记为废弃，但仍然可用）
        RedisVectorStore.RedisVectorStoreConfig config = RedisVectorStore.RedisVectorStoreConfig
                .builder()
                .withIndexName(properties.getIndex())
                .withPrefix(properties.getPrefix())
                .build();

        return new RedisVectorStore(
                config,
                embeddingModel,
                new JedisPooled(
                        redisConnectionDetails.getStandalone().getHost(),
                        redisConnectionDetails.getStandalone().getPort(),
                        redisConnectionDetails.getUsername(),
                        redisConnectionDetails.getPassword()
                ),
                properties.isInitializeSchema()
        );
    }
}
