package com.lzhshtp.shangcheng.controller.audit;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.service.audit.AutoAuditRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 自动审核记录管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/audit/auto")
@CrossOrigin(origins = "*")
public class AutoAuditRecordController {

    @Autowired
    private AutoAuditRecordService autoAuditRecordService;

    /**
     * 获取自动审核记录列表
     */
    @GetMapping("/records")
    public ApiResponse<PageResult<AutoAuditRecord>> getAutoAuditRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) String decision,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        try {
            PageResult<AutoAuditRecord> result = autoAuditRecordService.getAutoAuditRecords(
                page, pageSize, productId, decision, startDate, endDate);

            return ApiResponse.success("获取自动审核记录成功", result);

        } catch (Exception e) {
            log.error("获取自动审核记录失败", e);
            return ApiResponse.fail("获取自动审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取自动审核记录详情
     */
    @GetMapping("/records/{recordId}")
    public ApiResponse<AutoAuditRecord> getAutoAuditRecordDetail(@PathVariable Long recordId) {
        try {
            AutoAuditRecord record = autoAuditRecordService.getAutoAuditRecordDetail(recordId);

            if (record == null) {
                return ApiResponse.fail("审核记录不存在");
            }

            return ApiResponse.success("获取审核记录详情成功", record);

        } catch (Exception e) {
            log.error("获取审核记录详情失败，recordId: {}", recordId, e);
            return ApiResponse.fail("获取审核记录详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取自动审核统计数据
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getAutoAuditStats(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        try {
            Map<String, Object> stats = autoAuditRecordService.getAutoAuditStats(startDate, endDate);
            return ApiResponse.success("获取统计数据成功", stats);

        } catch (Exception e) {
            log.error("获取自动审核统计数据失败", e);
            return ApiResponse.fail("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 重新执行自动审核
     */
    @PostMapping("/records/{productId}/reaudit")
    public ApiResponse<AutoAuditRecord> reauditProduct(@PathVariable Long productId) {
        try {
            AutoAuditRecord record = autoAuditRecordService.reauditProduct(productId);
            return ApiResponse.success("重新审核成功", record);

        } catch (Exception e) {
            log.error("重新审核失败，productId: {}", productId, e);
            return ApiResponse.fail("重新审核失败：" + e.getMessage());
        }
    }
}
