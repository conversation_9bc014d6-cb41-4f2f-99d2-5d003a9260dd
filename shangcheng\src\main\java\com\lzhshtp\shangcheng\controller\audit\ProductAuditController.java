package com.lzhshtp.shangcheng.controller.audit;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.service.audit.ProductAuditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商品审核控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/audit")
@Tag(name = "商品审核", description = "商品审核相关接口")
public class ProductAuditController {
    
    @Autowired
    private ProductAuditService productAuditService;
    
    /**
     * 提交商品进行审核
     */
    @PostMapping("/submit/{productId}")
    @Operation(summary = "提交商品审核", description = "商家发布商品后，提交进行审核")
    public ApiResponse<Void> submitProductForAudit(
            @Parameter(description = "商品ID") @PathVariable Long productId) {

        try {
            productAuditService.submitProductForAudit(productId);
            return ApiResponse.successVoid("商品已提交审核");
        } catch (Exception e) {
            log.error("提交商品审核失败", e);
            return ApiResponse.fail("提交审核失败：" + e.getMessage());
        }
    }
    
    /**
     * 重新审核商品
     */
    @PostMapping("/reaudit/{productId}")
    @Operation(summary = "重新审核商品", description = "商家修改商品后重新提交审核")
    public ApiResponse<Void> reauditProduct(
            @Parameter(description = "商品ID") @PathVariable Long productId) {

        try {
            productAuditService.reauditProduct(productId);
            return ApiResponse.successVoid("商品已重新提交审核");
        } catch (Exception e) {
            log.error("重新审核商品失败", e);
            return ApiResponse.fail("重新审核失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取商品审核历史
     */
    @GetMapping("/history/{productId}")
    @Operation(summary = "获取审核历史", description = "查看商品的审核历史记录")
    public ApiResponse<AutoAuditRecord> getAuditHistory(
            @Parameter(description = "商品ID") @PathVariable Long productId) {

        try {
            AutoAuditRecord history = productAuditService.getProductAuditHistory(productId);
            if (history != null) {
                return ApiResponse.success("获取审核历史成功", history);
            } else {
                return ApiResponse.fail("未找到审核历史记录");
            }
        } catch (Exception e) {
            log.error("获取审核历史失败", e);
            return ApiResponse.fail("获取审核历史失败：" + e.getMessage());
        }
    }
}
