package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 用户批量导入DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserImportDTO {
    
    /**
     * 用户名（必填）
     */
    private String username;
    
    /**
     * 密码（必填）
     */
    private String password;
    
    /**
     * 邮箱（必填）
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phoneNumber;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 所在地区
     */
    private String location;
    
    /**
     * 信用分（默认100）
     */
    private Integer creditScore;
    
    /**
     * 是否激活（默认true）
     */
    private Boolean isActive;
    
    /**
     * 用户角色（默认general_user）
     */
    private String role;
    
    /**
     * 行号（用于错误提示）
     */
    private int rowNumber;
}
