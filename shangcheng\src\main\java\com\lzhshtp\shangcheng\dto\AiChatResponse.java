package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI聊天响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiChatResponse {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * AI回复内容
     */
    private String content;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 响应时间
     */
    private LocalDateTime timestamp;

    /**
     * 是否为流式响应的结束标志
     */
    @Builder.Default
    private Boolean isEnd = false;

    /**
     * 错误信息（如果有）
     */
    private String error;

    /**
     * 使用的token数量
     */
    private Integer tokenUsage;

    /**
     * 响应时长（毫秒）
     */
    private Long responseTime;

    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;

    /**
     * Function Call相关信息
     */
    private FunctionCallInfo functionCall;

    /**
     * Function Call信息类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FunctionCallInfo {

        /**
         * 函数名称
         */
        private String functionName;

        /**
         * 函数参数
         */
        private Map<String, Object> arguments;

        /**
         * 函数执行结果
         */
        private String result;

        /**
         * 函数执行状态
         */
        private String status;

        /**
         * 函数执行错误信息
         */
        private String error;
    }

    /**
     * 创建成功响应
     */
    public static AiChatResponse success(String sessionId, String messageId, String content) {
        return AiChatResponse.builder()
                .sessionId(sessionId)
                .messageId(messageId)
                .content(content)
                .messageType("assistant")
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }

    /**
     * 创建流式响应
     */
    public static AiChatResponse stream(String sessionId, String messageId, String content, boolean isEnd) {
        return AiChatResponse.builder()
                .sessionId(sessionId)
                .messageId(messageId)
                .content(content)
                .messageType("assistant")
                .timestamp(LocalDateTime.now())
                .isEnd(isEnd)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static AiChatResponse error(String sessionId, String error) {
        return AiChatResponse.builder()
                .sessionId(sessionId)
                .error(error)
                .timestamp(LocalDateTime.now())
                .isEnd(true)
                .build();
    }
}
