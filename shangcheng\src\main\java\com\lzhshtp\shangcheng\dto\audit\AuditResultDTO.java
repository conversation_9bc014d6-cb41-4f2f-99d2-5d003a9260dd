package com.lzhshtp.shangcheng.dto.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 审核结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditResultDTO {
    
    /**
     * 审核是否通过
     */
    private Boolean passed;
    
    /**
     * 风险等级
     */
    private AuditConstants.RiskLevel riskLevel;
    
    /**
     * 风险评分 (0-100)
     */
    private Integer score;
    
    /**
     * 审核原因
     */
    private String reason;
    
    /**
     * 风险因素列表
     */
    private List<String> riskFactors;
    
    /**
     * 详细信息
     */
    private Map<String, Object> details;
    
    /**
     * 创建通过的审核结果
     */
    public static AuditResultDTO pass(String checkType) {
        return AuditResultDTO.builder()
            .passed(true)
            .riskLevel(AuditConstants.RiskLevel.LOW)
            .score(0)
            .reason("检查通过")
            .build();
    }
    
    /**
     * 创建失败的审核结果
     */
    public static AuditResultDTO fail(AuditConstants.RiskLevel riskLevel, Integer score, String reason) {
        return AuditResultDTO.builder()
            .passed(false)
            .riskLevel(riskLevel)
            .score(score)
            .reason(reason)
            .build();
    }
}

/**
 * 文字审核结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class TextAuditResult {

    /**
     * 基础审核结果
     */
    private AuditResultDTO baseResult;

    /**
     * 违规词汇列表
     */
    private List<String> violatedWords;

    /**
     * 敏感词汇列表
     */
    private List<String> sensitiveWords;

    /**
     * 品牌词汇列表
     */
    private List<String> brandWords;
}

/**
 * 发布行为审核结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class PublishingBehaviorResult {

    /**
     * 基础审核结果
     */
    private AuditResultDTO baseResult;

    /**
     * 今日发布数量
     */
    private Integer todayPostCount;

    /**
     * 本周发布数量
     */
    private Integer weekPostCount;

    /**
     * 重复内容数量
     */
    private Integer duplicateContentCount;
}

/**
 * 信用分审核结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CreditScoreResult {

    /**
     * 基础审核结果
     */
    private AuditResultDTO baseResult;

    /**
     * 用户信用分
     */
    private Integer creditScore;

    /**
     * 信用分阈值
     */
    private Integer threshold;
}

/**
 * 综合审核结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ComprehensiveAuditResult {
    
    /**
     * 总风险评分
     */
    private BigDecimal totalScore;
    
    /**
     * 最终决策
     */
    private AuditConstants.AutoAuditDecision decision;
    
    /**
     * 决策原因
     */
    private String decisionReason;
    
    /**
     * 文字审核结果
     */
    private TextAuditResult textResult;
    
    /**
     * 发布行为审核结果
     */
    private PublishingBehaviorResult behaviorResult;
    
    /**
     * 信用分审核结果
     */
    private CreditScoreResult creditResult;
    
    /**
     * 所有风险因素
     */
    private List<String> allRiskFactors;
}
