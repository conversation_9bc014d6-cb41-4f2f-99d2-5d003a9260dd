<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.SecondReviewTaskMapper">

    <!-- 二度复审任务结果映射 -->
    <resultMap id="SecondReviewTaskResultMap" type="com.lzhshtp.shangcheng.model.SecondReviewTask">
        <id column="lzhshtp_task_id" property="taskId"/>
        <result column="lzhshtp_product_id" property="productId"/>
        <result column="lzhshtp_manual_audit_task_id" property="manualTaskId"/>
        <result column="lzhshtp_senior_admin_id" property="reviewerId"/>
        <result column="lzhshtp_status" property="status"/>
        <result column="lzhshtp_escalation_reason" property="escalationReason"/>
        <result column="lzhshtp_admin_decision" property="finalDecision"/>
        <result column="lzhshtp_admin_comments" property="reviewComments"/>
        <result column="lzhshtp_all_previous_materials" property="allPreviousMaterials"/>
        <result column="lzhshtp_created_time" property="createdTime"/>
        <result column="lzhshtp_assigned_time" property="claimedTime"/>
        <result column="lzhshtp_completed_time" property="completedTime"/>
        <result column="lzhshtp_deadline" property="deadline"/>
    </resultMap>

    <!-- 根据条件查询复审任务 -->
    <select id="findByConditions" parameterType="map" resultMap="SecondReviewTaskResultMap">
        SELECT * FROM tb_lzhshtp_second_review_tasks
        WHERE 1=1
        <if test="status != null and status != ''">
            AND lzhshtp_status = #{status}
        </if>
        <if test="escalationReason != null and escalationReason != ''">
            AND lzhshtp_escalation_reason LIKE CONCAT('%', #{escalationReason}, '%')
        </if>
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="reviewerId != null">
            AND lzhshtp_senior_admin_id = #{reviewerId}
        </if>
        ORDER BY lzhshtp_created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计复审任务数量 -->
    <select id="countByConditions" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_second_review_tasks
        WHERE 1=1
        <if test="status != null and status != ''">
            AND lzhshtp_status = #{status}
        </if>
        <if test="escalationReason != null and escalationReason != ''">
            AND lzhshtp_escalation_reason LIKE CONCAT('%', #{escalationReason}, '%')
        </if>
        <if test="productId != null">
            AND lzhshtp_product_id = #{productId}
        </if>
        <if test="reviewerId != null">
            AND lzhshtp_senior_admin_id = #{reviewerId}
        </if>
    </select>

</mapper>
