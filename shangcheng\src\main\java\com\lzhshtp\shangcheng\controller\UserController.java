package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.utils.OssUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    private final UserService userService;
    private final OssUtil ossUtil;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    public ApiResponse<User> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);

        // 清除敏感信息
        user.setPasswordHash(null);

        return ApiResponse.success(user);
    }

    /**
     * 获取卖家信息
     *
     * @param sellerId 卖家ID
     * @return 卖家信息
     */
    @GetMapping("/seller/{sellerId}")
    public ApiResponse<User> getSellerInfo(@PathVariable Long sellerId) {
        try {
            User seller = userService.findById(sellerId);
            if (seller == null) {
                return ApiResponse.fail("卖家不存在");
            }

            // 清除敏感信息
            seller.setPasswordHash(null);
            seller.setEmail(null);
            seller.setPhoneNumber(null);

            return ApiResponse.success(seller);
        } catch (Exception e) {
            logger.error("获取卖家信息失败", e);
            return ApiResponse.fail("获取卖家信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新当前用户信息
     *
     * @param request 更新请求
     * @return 更新后的用户信息
     */
    @PutMapping("/me")
    public ApiResponse<User> updateCurrentUser(@Valid @RequestBody UserUpdateRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            User updatedUser = userService.updateUserInfo(username, request);

            // 清除敏感信息
            updatedUser.setPasswordHash(null);

            return ApiResponse.success("用户信息更新成功", updatedUser);
        } catch (IllegalArgumentException e) {
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return ApiResponse.fail("更新用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新当前用户头像
     *
     * @param file 头像文件
     * @return 更新后的用户信息
     */
    @PostMapping("/me/avatar")
    public ApiResponse<User> updateAvatar(@RequestParam("file") MultipartFile file) {
        logger.info("开始处理用户头像更新请求");

        if (file.isEmpty()) {
            logger.error("上传失败：文件为空");
            return ApiResponse.fail("上传失败：文件为空");
        }

        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            // 如果用户已有头像，先删除旧头像
            if (user.getAvatarUrl() != null && !user.getAvatarUrl().isEmpty()) {
                try {
                    ossUtil.deleteFile(user.getAvatarUrl());
                    logger.info("已删除旧头像: {}", user.getAvatarUrl());
                } catch (Exception e) {
                    logger.warn("删除旧头像失败: {}", e.getMessage());
                    // 继续上传新头像，不因为删除旧头像失败而中断流程
                }
            }

            // 上传新头像
            String fileUrl = ossUtil.uploadFile(file, "avatars");
            logger.info("新头像上传成功，URL: {}", fileUrl);

            // 更新用户头像URL
            User updatedUser = userService.updateAvatar(username, fileUrl);

            // 清除敏感信息
            updatedUser.setPasswordHash(null);

            return ApiResponse.success("头像更新成功", updatedUser);
        } catch (IOException e) {
            logger.error("头像上传失败", e);
            return ApiResponse.fail("头像上传失败：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("更新头像失败", e);
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("更新头像失败", e);
            return ApiResponse.fail("更新头像失败：" + e.getMessage());
        }
    }

    /**
     * 管理员接口示例
     *
     * @return 消息
     */
    @GetMapping("/admin")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<String> adminOnly() {
        return ApiResponse.success("您拥有管理员权限");
    }

    /**
     * 获取当前管理员信息
     *
     * @return 管理员信息
     */
    @GetMapping("/admin/me")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<User> getCurrentAdminInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);

        // 清除敏感信息
        user.setPasswordHash(null);

        return ApiResponse.success(user);
    }

    /**
     * 获取用户列表（管理员专用）
     *
     * @param keyword 搜索关键词，匹配用户名、ID、手机号或邮箱
     * @param role 用户角色筛选
     * @param isActive 用户状态筛选
     * @param page 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 用户列表分页结果
     */
    @GetMapping("/admin/users")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<PageResult<UserDTO>> getUserList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        try {
            // 构建查询参数
            UserQueryParams params = new UserQueryParams();
            params.setKeyword(keyword);
            params.setRole(role);
            params.setIsActive(isActive);
            params.setPage(page);
            params.setPageSize(pageSize);

            // 查询用户列表
            PageResult<UserDTO> result = userService.getUserList(params);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ApiResponse.fail("获取用户列表失败: " + e.getMessage());
        }
    }




    /**
     * 获取用户详情（管理员专用）
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    @GetMapping("/admin/users/{userId}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<UserDetailDTO> getUserDetail(@PathVariable Long userId) {
        try {
            UserDetailDTO userDetail = userService.getUserDetail(userId);
            return ApiResponse.success(userDetail);
        } catch (IllegalArgumentException e) {
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("获取用户详情失败", e);
            return ApiResponse.fail("获取用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取管理员用户列表（管理员专用）
     *
     * @param keyword 搜索关键词
     * @param isActive 用户状态筛选
     * @param page 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 管理员用户列表分页结果
     */
    @GetMapping("/admin/admins")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<PageResult<UserDTO>> getAdminList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        try {
            // 构建查询参数
            UserQueryParams params = new UserQueryParams();
            params.setKeyword(keyword);
            params.setRole("admin"); // 固定查询管理员
            params.setIsActive(isActive);
            params.setPage(page);
            params.setPageSize(pageSize);

            // 查询管理员列表
            PageResult<UserDTO> result = userService.getAdminList(params);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取管理员列表失败", e);
            return ApiResponse.fail("获取管理员列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态（管理员专用）
     *
     * @param request 用户状态更新请求
     * @return 更新结果
     */
    @PutMapping("/admin/users/status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<User> updateUserStatus(@Valid @RequestBody UserStatusUpdateRequest request) {
        try {
            User updatedUser = userService.updateUserStatus(request);
            // 清除敏感信息
            updatedUser.setPasswordHash(null);
            return ApiResponse.success("用户状态更新成功", updatedUser);
        } catch (IllegalArgumentException e) {
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return ApiResponse.fail("更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新用户状态（管理员专用）
     *
     * @param request 批量用户状态更新请求
     * @return 更新结果
     */
    @PutMapping("/admin/users/batch-status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Map<String, Object>> batchUpdateUserStatus(@Valid @RequestBody BatchUserStatusUpdateRequest request) {
        try {
            int updatedCount = userService.batchUpdateUserStatus(request);
            Map<String, Object> result = new HashMap<>();
            result.put("updatedCount", updatedCount);
            result.put("isActive", request.getIsActive());
            return ApiResponse.success("批量更新用户状态成功", result);
        } catch (Exception e) {
            logger.error("批量更新用户状态失败", e);
            return ApiResponse.fail("批量更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除管理员（管理员专用）
     *
     * @param userId 要删除的管理员用户ID
     * @return 操作结果
     */
    @DeleteMapping("/admins/{userId}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Void> deleteAdmin(@PathVariable Long userId) {
        try {
            // 检查是否是当前登录的管理员
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            User currentUser = userService.findByUsername(currentUsername);

            // 不允许删除自己的账号
            if (currentUser != null && currentUser.getUserId().equals(userId)) {
                return ApiResponse.fail("不能删除自己的管理员账号");
            }

            boolean result = userService.deleteAdmin(userId);
            if (result) {
                return ApiResponse.success("管理员删除成功", null);
            } else {
                return ApiResponse.fail("管理员删除失败");
            }
        } catch (IllegalArgumentException e) {
            logger.error("删除管理员失败: {}", e.getMessage());
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("删除管理员失败", e);
            return ApiResponse.fail("删除管理员失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员信息（管理员专用）
     *
     * @param userId 要编辑的管理员用户ID
     * @param request 管理员更新请求
     * @return 操作结果
     */
    @PutMapping("/admins/{userId}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<User> c(@PathVariable Long userId, @Valid @RequestBody AdminUpdateRequest request) {
        try {
            User updatedUser = userService.updateAdmin(userId, request);

            // 清除敏感信息
            updatedUser.setPasswordHash(null);

            return ApiResponse.success("管理员信息更新成功", updatedUser);
        } catch (IllegalArgumentException e) {
            logger.error("更新管理员信息失败: {}", e.getMessage());
            return ApiResponse.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("更新管理员信息失败", e);
            return ApiResponse.fail("更新管理员信息失败: " + e.getMessage());
        }
    }
}
