package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量更新用户状态请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUserStatusUpdateRequest {
    
    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    private List<Long> userIds;
    
    /**
     * 用户状态（true-启用，false-禁用）
     */
    @NotNull(message = "用户状态不能为空")
    private Boolean isActive;
} 