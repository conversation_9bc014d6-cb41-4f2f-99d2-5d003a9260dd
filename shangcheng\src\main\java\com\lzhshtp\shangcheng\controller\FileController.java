package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.utils.OssUtil;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/files")
public class FileController {
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    private final OssUtil ossUtil;
    
    public FileController(OssUtil ossUtil) {
        this.ossUtil = ossUtil;
    }
    
    @PostMapping("/upload")
    public ApiResponse<?> uploadFile(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "directory", defaultValue = "default") String directory) {
        logger.info("开始处理文件上传请求，文件名: {}, 目录: {}", file.getOriginalFilename(), directory);
        
        if (file.isEmpty()) {
            logger.error("上传失败：文件为空");
            return ApiResponse.fail("上传失败：文件为空");
        }
        
        try {
            logger.info("文件大小: {} bytes", file.getSize());
            String fileUrl = ossUtil.uploadFile(file, directory);
            logger.info("文件上传成功，URL: {}", fileUrl);
            
            Map<String, String> response = new HashMap<>();
            response.put("url", fileUrl);
            return ApiResponse.success("文件上传成功", response);
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return ApiResponse.fail("文件上传失败：" + e.getMessage());
        }
    }
    
    @DeleteMapping
    public ApiResponse<?> deleteFile(@RequestParam("fileUrl") String fileUrl) {
        logger.info("开始处理文件删除请求，文件URL: {}", fileUrl);
        try {
            ossUtil.deleteFile(fileUrl);
            logger.info("文件删除成功");
            return ApiResponse.success("文件删除成功", null);
        } catch (Exception e) {
            logger.error("文件删除失败", e);
            return ApiResponse.fail("文件删除失败：" + e.getMessage());
        }
    }
} 