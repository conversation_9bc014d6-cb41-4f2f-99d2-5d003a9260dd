package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.UserFavoriteDTO;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.UserFavoriteMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.UserFavorite;
import com.lzhshtp.shangcheng.service.UserFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户收藏服务实现类
 */
@Service
public class UserFavoriteServiceImpl implements UserFavoriteService {

    @Autowired
    private UserFavoriteMapper userFavoriteMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ProductMapper productMapper;

    @Override
    @Transactional
    public ApiResponse<String> addFavorite(Long userId, Long productId) {
        // 验证用户是否存在
        if (userMapper.selectById(userId) == null) {
            return ApiResponse.fail("用户不存在");
        }
        
        // 验证商品是否存在
        Product product = productMapper.selectById(productId);
        if (product == null) {
            return ApiResponse.fail("商品不存在");
        }
        
        // 检查商品状态
        if (!Product.ProductStatus.AVAILABLE.equals(product.getStatus())) {
            return ApiResponse.fail("商品已下架或已售出，无法收藏");
        }
        
        // 检查是否已收藏
        if (userFavoriteMapper.checkFavorite(userId, productId) > 0) {
            return ApiResponse.fail("已收藏该商品");
        }
        
        // 添加收藏
        UserFavorite userFavorite = new UserFavorite();
        userFavorite.setLzhshtpUserId(userId);
        userFavorite.setLzhshtpProductId(productId);
        userFavorite.setLzhshtpFavoritedAt(LocalDateTime.now());
        
        int result = userFavoriteMapper.insert(userFavorite);
        if (result > 0) {
            return ApiResponse.success("收藏成功", "收藏成功");
        } else {
            return ApiResponse.fail("收藏失败");
        }
    }

    @Override
    @Transactional
    public ApiResponse<String> removeFavorite(Long favoriteId, Long userId) {
        int result = userFavoriteMapper.delete(favoriteId, userId);
        if (result > 0) {
            return ApiResponse.success("取消收藏成功", "取消收藏成功");
        } else {
            return ApiResponse.fail("取消收藏失败，可能收藏不存在或无权限操作");
        }
    }

    @Override
    @Transactional
    public ApiResponse<String> removeFavoriteByProduct(Long userId, Long productId) {
        int result = userFavoriteMapper.deleteByUserAndProduct(userId, productId);
        if (result > 0) {
            return ApiResponse.success("取消收藏成功", "取消收藏成功");
        } else {
            return ApiResponse.fail("取消收藏失败，可能未收藏该商品");
        }
    }

    @Override
    public ApiResponse<Boolean> isFavorited(Long userId, Long productId) {
        int count = userFavoriteMapper.checkFavorite(userId, productId);
        return ApiResponse.success(count > 0);
    }

    @Override
    public ApiResponse<List<UserFavoriteDTO>> getUserFavorites(Long userId) {
        // 验证用户是否存在
        if (userMapper.selectById(userId) == null) {
            return ApiResponse.fail("用户不存在");
        }
        
        List<UserFavoriteDTO> favorites = userFavoriteMapper.findByUserId(userId);
        return ApiResponse.success(favorites);
    }

    @Override
    public ApiResponse<Integer> getProductFavoriteCount(Long productId) {
        // 验证商品是否存在
        if (productMapper.selectById(productId) == null) {
            return ApiResponse.fail("商品不存在");
        }
        
        int count = userFavoriteMapper.countByProductId(productId);
        return ApiResponse.success(count);
    }
} 