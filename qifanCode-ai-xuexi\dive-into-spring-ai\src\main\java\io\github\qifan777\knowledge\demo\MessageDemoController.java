package io.github.qifan777.knowledge.demo;


import lombok.AllArgsConstructor;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.InMemoryChatMemory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

@RequestMapping("demo/message")
@RestController
@AllArgsConstructor
public class MessageDemoController {
    // AI模型基座，可以切换不同的AI厂商模型
    // 阿里灵积
    private final ChatModel chatModel;
    // 模拟数据库存储会话和消息
    private final ChatMemory chatMemory = new InMemoryChatMemory();

    private final VectorStore vectorStore;
    /**
     * 非流式问答
     *
     * @param prompt 用户提问
     * @return org.springframework.ai.chat.model.ChatResponse
     */
    @GetMapping("chat")
    public String chat(@RequestParam String prompt){
        ChatClient chatClient = ChatClient.create(chatModel);
        return chatClient.prompt()
                //输入提示词
                .user(prompt)
                //call代表非流式回答，结果可以是ChatResponse,也可以是Entity
                .call()
                .content();
    }

    /**
     * 流式问答
     *
     * @param prompt 用户提问
     * @return Flux<ServerSentEvent < String>> 流式响应
     */
    @GetMapping(value = "chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStream(@RequestParam String prompt) {
    return ChatClient.create(chatModel)
            .prompt()
            .user(prompt)
            .stream()
            .content();
    }

    /**
     * 根据会话id，从数据库中查找历史消息，并将消息作为上下文回答。
     *
     * @param prompt    用户的提问
     * @param sessionId 会话id
     * @return SSE流响应
     */
    @GetMapping(value = "chat/stream/history", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStreamWithHistory(@RequestParam String prompt,
                                                               @RequestParam String sessionId){
        MessageChatMemoryAdvisor messageChatMemoryAdvisor = new MessageChatMemoryAdvisor(chatMemory, sessionId, 10);
        return ChatClient.create(chatModel).prompt()
                .user(prompt)
                .advisors(messageChatMemoryAdvisor)
                .stream()
                .content();
    }

    /**
     * 从向量数据库中查找文档，并将查询的文档作为上下文回答。
     *
     * @param prompt 用户的提问
     * @return SSE流响应
     */
    @GetMapping(value = "chat/stream/database", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStreamWithRag(@RequestParam String prompt) {
        // 1. 定义提示词模板，question_answer_context会被替换成向量数据库中查询到的文档。
        String promptWithContext = """
                下面是上下文信息
                ---------------------
                {question_answer_context}
                ---------------------
                给定的上下文和提供的历史信息，而不是事先的知识，回复用户的意见。如果答案不在上下文中，告诉用户你不能回答这个问题。
                """;

        QuestionAnswerAdvisor questionAnswerAdvisor = new QuestionAnswerAdvisor(vectorStore,SearchRequest.builder().build(),promptWithContext);
        return ChatClient.create(chatModel)
                .prompt()
                .user(prompt)
                .advisors(questionAnswerAdvisor)
                .stream()
                .content();
    }

    /**
     * 调用自定义函数回答用户的提问
     *
     * @param prompt       用户的提问
     * @return SSE流式响应
     */
    @GetMapping(value = "chat/stream/function", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStreamWithFunction(@RequestParam String prompt) {
        return ChatClient.create(chatModel)
                .prompt()
                .user(prompt)
                .functions("documentAnalyzerFunction", "documentDeleteFunction")
                .stream()
                .content();
    }
}
