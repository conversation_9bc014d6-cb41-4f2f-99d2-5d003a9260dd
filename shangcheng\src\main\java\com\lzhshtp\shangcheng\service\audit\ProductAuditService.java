package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import com.lzhshtp.shangcheng.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品审核服务 - 主要入口
 */
@Slf4j
@Service
public class ProductAuditService {
    
    @Autowired
    private AutoAuditEngine autoAuditEngine;
    
    @Autowired
    private ManualAuditService manualAuditService;
    
    @Autowired
    private ProductMapper productMapper;
    
    /**
     * 提交商品进行审核
     */
    @Transactional
    public void submitProductForAudit(Long productId) {
        log.info("商品提交审核，商品ID: {}", productId);
        
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new RuntimeException("商品不存在");
        }
        
        // 更新商品状态为待审核
        product.setStatus("pending_review");
        productMapper.updateById(product);
        
        // 执行自动审核
        AutoAuditRecord auditRecord = autoAuditEngine.performAutoAudit(product);
        
        // 根据自动审核结果处理
        handleAutoAuditResult(product, auditRecord);
    }
    
    /**
     * 处理自动审核结果
     */
    private void handleAutoAuditResult(Product product, AutoAuditRecord auditRecord) {
        String decision = auditRecord.getFinalDecision();
        
        switch (decision) {
            case "auto_approve":
                // 自动通过
                product.setStatus("available");
                productMapper.updateById(product);
                log.info("商品自动审核通过，商品ID: {}", product.getId());
                break;
                
            case "auto_reject":
                // 自动拒绝 - 直接删除商品
                Long productId = product.getId();
                String rejectReason = auditRecord.getDecisionReason();

                try {
                    // 删除商品相关的图片文件（如果有OSS配置）
                    deleteProductImages(product);

                    // 从数据库中删除商品
                    productMapper.deleteById(productId);

                    log.info("商品自动审核拒绝，已删除商品，商品ID: {}, 原因: {}", productId, rejectReason);

                    // 可选：发送通知给卖家
                    notifySellerProductRejected(product, rejectReason);

                } catch (Exception e) {
                    log.error("删除被拒绝的商品失败，商品ID: {}, 错误: {}", productId, e.getMessage(), e);
                    // 如果删除失败，降级为下架处理
                    product.setStatus("off_shelf_by_admin");
                    productMapper.updateById(product);
                    log.warn("删除失败，已降级为下架处理，商品ID: {}", productId);
                }
                break;
                
            case "manual_review":
                // 进入人工审核 - 直接传入自动审核记录，避免重新查询
                List<String> reasons = extractAuditReasons(auditRecord);
                manualAuditService.createManualAuditTaskWithRecord(product, auditRecord, reasons);
                log.info("商品进入人工审核，商品ID: {}, 原因: {}", product.getId(), String.join("；", reasons));
                break;
                
            default:
                log.error("未知的审核决策: {}", decision);
                // 默认进入人工审核
                manualAuditService.createManualAuditTaskWithRecord(product, auditRecord, List.of("系统异常，需要人工审核"));
        }
    }
    
    /**
     * 从自动审核记录中提取审核原因
     */
    private List<String> extractAuditReasons(AutoAuditRecord auditRecord) {
        List<String> reasons = new ArrayList<>();
        
        // 添加决策原因
        if (auditRecord.getDecisionReason() != null) {
            reasons.add(auditRecord.getDecisionReason());
        }
        
        // 可以进一步解析各个审核结果的详细原因
        // 这里简化处理
        
        return reasons;
    }
    
    /**
     * 获取待人工审核的任务列表
     */
    public List<ManualAuditTask> getPendingManualAuditTasks() {
        return manualAuditService.getPendingTasks();
    }
    
    /**
     * 管理员开始审核任务
     */
    public boolean startManualAudit(Long taskId, Long adminId) {
        return manualAuditService.startAuditTask(taskId, adminId);
    }
    
    /**
     * 获取管理员正在处理的任务
     */
    public List<ManualAuditTask> getAdminTasks(Long adminId) {
        return manualAuditService.getTasksByAdmin(adminId);
    }
    
    /**
     * 管理员做出审核决策
     */
    public void makeManualAuditDecision(Long taskId, Long adminId, String decision, String comments) {
        manualAuditService.makeAuditDecision(taskId, adminId, decision, comments);
    }
    
    /**
     * 重新审核商品（用于商家修改后重新提交）
     */
    @Transactional
    public void reauditProduct(Long productId) {
        log.info("重新审核商品，商品ID: {}", productId);
        
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new RuntimeException("商品不存在");
        }
        
        // 重置商品状态并重新审核
        submitProductForAudit(productId);
    }
    
    /**
     * 获取商品审核历史
     */
    public AutoAuditRecord getProductAuditHistory(Long productId) {
        // TODO: 可以扩展为获取完整的审核历史
        return autoAuditEngine.getAutoAuditRecord(productId);
    }

    /**
     * 删除商品相关的图片文件
     */
    private void deleteProductImages(Product product) {
        try {
            if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
                String[] imageUrls = product.getImageUrls().split(",");
                for (String imageUrl : imageUrls) {
                    if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                        // 这里可以添加OSS文件删除逻辑
                        // ossUtil.deleteFile(imageUrl.trim());
                        log.debug("准备删除图片文件: {}", imageUrl.trim());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("删除商品图片文件时出错，商品ID: {}, 错误: {}", product.getId(), e.getMessage());
            // 图片删除失败不影响商品删除
        }
    }

    /**
     * 通知卖家商品被拒绝
     */
    private void notifySellerProductRejected(Product product, String rejectReason) {
        try {
            // 这里可以添加消息通知逻辑
            // 例如：发送站内信、邮件、短信等
            log.info("通知卖家商品被拒绝，卖家ID: {}, 商品ID: {}, 原因: {}",
                product.getSellerId(), product.getId(), rejectReason);

            // TODO: 实现具体的通知逻辑
            // messageService.sendProductRejectedNotification(product.getSellerId(), product.getId(), rejectReason);

        } catch (Exception e) {
            log.warn("通知卖家商品被拒绝时出错，商品ID: {}, 错误: {}", product.getId(), e.getMessage());
            // 通知失败不影响商品删除
        }
    }
}
