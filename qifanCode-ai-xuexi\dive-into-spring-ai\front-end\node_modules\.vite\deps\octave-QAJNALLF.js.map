{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/octave.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&|\\\\^~<>!@'\\\\\\\\]\");\nvar singleDelimiters = new RegExp('^[\\\\(\\\\[\\\\{\\\\},:=;\\\\.]');\nvar doubleOperators = new RegExp(\"^((==)|(~=)|(<=)|(>=)|(<<)|(>>)|(\\\\.[\\\\+\\\\-\\\\*/\\\\^\\\\\\\\]))\");\nvar doubleDelimiters = new RegExp(\"^((!=)|(\\\\+=)|(\\\\-=)|(\\\\*=)|(/=)|(&=)|(\\\\|=)|(\\\\^=))\");\nvar tripleDelimiters = new RegExp(\"^((>>=)|(<<=))\");\nvar expressionEnd = new RegExp(\"^[\\\\]\\\\)]\");\nvar identifiers = new RegExp(\"^[_A-Za-z\\xa1-\\uffff][_A-Za-z0-9\\xa1-\\uffff]*\");\n\nvar builtins = wordRegexp([\n  'error', 'eval', 'function', 'abs', 'acos', 'atan', 'asin', 'cos',\n  'cosh', 'exp', 'log', 'prod', 'sum', 'log10', 'max', 'min', 'sign', 'sin', 'sinh',\n  'sqrt', 'tan', 'reshape', 'break', 'zeros', 'default', 'margin', 'round', 'ones',\n  'rand', 'syn', 'ceil', 'floor', 'size', 'clear', 'zeros', 'eye', 'mean', 'std', 'cov',\n  'det', 'eig', 'inv', 'norm', 'rank', 'trace', 'expm', 'logm', 'sqrtm', 'linspace', 'plot',\n  'title', 'xlabel', 'ylabel', 'legend', 'text', 'grid', 'meshgrid', 'mesh', 'num2str',\n  'fft', 'ifft', 'arrayfun', 'cellfun', 'input', 'fliplr', 'flipud', 'ismember'\n]);\n\nvar keywords = wordRegexp([\n  'return', 'case', 'switch', 'else', 'elseif', 'end', 'endif', 'endfunction',\n  'if', 'otherwise', 'do', 'for', 'while', 'try', 'catch', 'classdef', 'properties', 'events',\n  'methods', 'global', 'persistent', 'endfor', 'endwhile', 'printf', 'sprintf', 'disp', 'until',\n  'continue', 'pkg'\n]);\n\n\n// tokenizers\nfunction tokenTranspose(stream, state) {\n  if (!stream.sol() && stream.peek() === '\\'') {\n    stream.next();\n    state.tokenize = tokenBase;\n    return 'operator';\n  }\n  state.tokenize = tokenBase;\n  return tokenBase(stream, state);\n}\n\n\nfunction tokenComment(stream, state) {\n  if (stream.match(/^.*%}/)) {\n    state.tokenize = tokenBase;\n    return 'comment';\n  };\n  stream.skipToEnd();\n  return 'comment';\n}\n\nfunction tokenBase(stream, state) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match('%{')){\n    state.tokenize = tokenComment;\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  if (stream.match(/^[%#]/)){\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+[ij]?/)) {\n      stream.tokenize = tokenBase;\n      return 'number'; };\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n  }\n  if (stream.match(wordRegexp(['nan','NaN','inf','Inf']))) { return 'number'; };\n\n  // Handle Strings\n  var m = stream.match(/^\"(?:[^\"]|\"\")*(\"|$)/) || stream.match(/^'(?:[^']|'')*('|$)/)\n  if (m) { return m[1] ? 'string' : \"error\"; }\n\n  // Handle words\n  if (stream.match(keywords)) { return 'keyword'; } ;\n  if (stream.match(builtins)) { return 'builtin'; } ;\n  if (stream.match(identifiers)) { return 'variable'; } ;\n\n  if (stream.match(singleOperators) || stream.match(doubleOperators)) { return 'operator'; };\n  if (stream.match(singleDelimiters) || stream.match(doubleDelimiters) || stream.match(tripleDelimiters)) { return null; };\n\n  if (stream.match(expressionEnd)) {\n    state.tokenize = tokenTranspose;\n    return null;\n  };\n\n\n  // Handle non-detected items\n  stream.next();\n  return 'error';\n};\n\n\nexport const octave = {\n  name: \"octave\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (style === 'number' || style === 'variable'){\n      state.tokenize = tokenTranspose;\n    }\n    return style;\n  },\n\n  languageData: {\n    commentTokens: {line: \"%\"}\n  }\n};\n\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,IAAI,kBAAkB,IAAI,OAAO,8BAA8B;AAC/D,IAAI,mBAAmB,IAAI,OAAO,wBAAwB;AAC1D,IAAI,kBAAkB,IAAI,OAAO,2DAA2D;AAC5F,IAAI,mBAAmB,IAAI,OAAO,sDAAsD;AACxF,IAAI,mBAAmB,IAAI,OAAO,gBAAgB;AAClD,IAAI,gBAAgB,IAAI,OAAO,WAAW;AAC1C,IAAI,cAAc,IAAI,OAAO,+BAA+C;AAE5E,IAAI,WAAW,WAAW;AAAA,EACxB;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAC5D;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAC3E;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAC1E;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAChF;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAY;AAAA,EACnF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAC3E;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAW;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AACrE,CAAC;AAED,IAAI,WAAW,WAAW;AAAA,EACxB;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAS;AAAA,EAC9D;AAAA,EAAM;AAAA,EAAa;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAA,EAAc;AAAA,EACnF;AAAA,EAAW;AAAA,EAAU;AAAA,EAAc;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EACtF;AAAA,EAAY;AACd,CAAC;AAID,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,MAAM,KAAM;AAC3C,WAAO,KAAK;AACZ,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,SAAO,UAAU,QAAQ,KAAK;AAChC;AAGA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAAC;AACD,SAAO,UAAU;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,OAAO,SAAS;AAAG,WAAO;AAG9B,MAAI,OAAO,MAAM,IAAI,GAAE;AACrB,UAAM,WAAW;AACjB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,OAAO,GAAE;AACxB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AACrC,QAAI,OAAO,MAAM,2BAA2B,GAAG;AAC7C,aAAO,WAAW;AAClB,aAAO;AAAA,IAAU;AAAC;AACpB,QAAI,OAAO,MAAM,sCAAsC,GAAG;AAAE,aAAO;AAAA,IAAU;AAAC;AAC9E,QAAI,OAAO,MAAM,iCAAiC,GAAG;AAAE,aAAO;AAAA,IAAU;AAAC;AAAA,EAC3E;AACA,MAAI,OAAO,MAAM,WAAW,CAAC,OAAM,OAAM,OAAM,KAAK,CAAC,CAAC,GAAG;AAAE,WAAO;AAAA,EAAU;AAAC;AAG7E,MAAI,IAAI,OAAO,MAAM,qBAAqB,KAAK,OAAO,MAAM,qBAAqB;AACjF,MAAI,GAAG;AAAE,WAAO,EAAE,CAAC,IAAI,WAAW;AAAA,EAAS;AAG3C,MAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAW;AAAE;AAClD,MAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAW;AAAE;AAClD,MAAI,OAAO,MAAM,WAAW,GAAG;AAAE,WAAO;AAAA,EAAY;AAAE;AAEtD,MAAI,OAAO,MAAM,eAAe,KAAK,OAAO,MAAM,eAAe,GAAG;AAAE,WAAO;AAAA,EAAY;AAAC;AAC1F,MAAI,OAAO,MAAM,gBAAgB,KAAK,OAAO,MAAM,gBAAgB,KAAK,OAAO,MAAM,gBAAgB,GAAG;AAAE,WAAO;AAAA,EAAM;AAAC;AAExH,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAAC;AAID,SAAO,KAAK;AACZ,SAAO;AACT;AAGO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,YAAY,UAAU,YAAW;AAC7C,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}