package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 论坛分类统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumCategoryStatisticsDTO {
    private Integer categoryId;         // 分类ID
    private String categoryName;        // 分类名称
    private Long postCount;             // 帖子数量
    private Long commentCount;          // 评论数量
    private Long viewCount;             // 浏览量
    private Double percentage;          // 占比
    private Double avgCommentsPerPost;  // 平均每帖评论数
}
