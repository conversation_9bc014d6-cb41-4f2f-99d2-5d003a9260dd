package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_orders")
public class Order {
    
    /**
     * 订单状态枚举
     */
    public static class OrderStatus {
        public static final String PENDING_PAYMENT = "pending_payment";  // 待支付
        public static final String PAID = "paid";                        // 已支付
        public static final String SHIPPED = "shipped";                  // 已发货
        public static final String VERIFYING = "verifying";              // 官方验货中
        public static final String DELIVERED = "delivered";              // 已送达
        public static final String COMPLETED = "completed";              // 已完成
        public static final String CANCELLED = "cancelled";              // 已取消
        public static final String REFUNDED = "refunded";                // 已退款
    }
    
    @TableId(value = "lzhshtp_order_id", type = IdType.AUTO)
    private Long orderId;
    
    @TableField("lzhshtp_product_id")
    private Long productId;
    
    @TableField("lzhshtp_buyer_id")
    private Long buyerId;
    
    @TableField("lzhshtp_seller_id")
    private Long sellerId;
    
    @TableField("lzhshtp_order_date")
    private LocalDateTime orderDate;
    
    @TableField("lzhshtp_total_amount")
    private BigDecimal totalAmount;
    
    @TableField("lzhshtp_status")
    private String status;
    
    @TableField("lzhshtp_shipping_address")
    private String shippingAddress;
    
    @TableField("lzhshtp_payment_method")
    private String paymentMethod;
    
    @TableField("lzhshtp_transaction_id")
    private String transactionId;

    @TableField("lzhshtp_current_location")
    private String currentLocation;

    @TableField("lzhshtp_official_verification")
    private Boolean officialVerification;

    @TableField("lzhshtp_verification_fee")
    private BigDecimal verificationFee;

    @TableField("lzhshtp_verification_payer")
    private String verificationPayer;

    /**
     * 验货费用承担方枚举
     */
    public static class VerificationPayer {
        public static final String BUYER = "buyer";
        public static final String SELLER = "seller";
    }
}