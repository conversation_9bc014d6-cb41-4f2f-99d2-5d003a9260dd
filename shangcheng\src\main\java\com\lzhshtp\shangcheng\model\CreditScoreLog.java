package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 信用分变更记录实体类
 * 对应数据库表：tb_lzhshtp_credit_score_logs
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_credit_score_logs")
public class CreditScoreLog {

    /**
     * 日志记录唯一标识ID
     */
    @TableId(value = "lzhshtp_log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 用户ID
     */
    @TableField("lzhshtp_user_id")
    private Long userId;

    /**
     * 变更类型
     */
    @TableField("lzhshtp_change_type")
    private ChangeType changeType;

    /**
     * 变更原因
     */
    @TableField("lzhshtp_change_reason")
    private String changeReason;

    /**
     * 变更前信用分
     */
    @TableField("lzhshtp_score_before")
    private Integer scoreBefore;

    /**
     * 变更后信用分
     */
    @TableField("lzhshtp_score_after")
    private Integer scoreAfter;

    /**
     * 信用分变化量（正数为加分，负数为减分）
     */
    @TableField("lzhshtp_score_change")
    private Integer scoreChange;

    /**
     * 相关订单ID（如果是评价导致的变更）
     */
    @TableField("lzhshtp_related_order_id")
    private Long relatedOrderId;

    /**
     * 相关评价ID（如果是评价导致的变更）
     */
    @TableField("lzhshtp_related_review_id")
    private Long relatedReviewId;

    /**
     * 操作者ID（系统操作时为NULL）
     */
    @TableField("lzhshtp_operator_id")
    private Long operatorId;

    /**
     * 创建时间
     */
    @TableField(value = "lzhshtp_created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 变更类型枚举
     */
    public enum ChangeType {
        REVIEW("review", "评价导致"),
        PENALTY("penalty", "违规扣分"),
        BONUS("bonus", "奖励加分"),
        SYSTEM("system", "系统调整");

        private final String code;
        private final String description;

        ChangeType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
