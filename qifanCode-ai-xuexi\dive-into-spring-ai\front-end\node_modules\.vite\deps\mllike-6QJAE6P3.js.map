{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/mllike.js"], "sourcesContent": ["function mlLike(parserConfig) {\n  var words = {\n    'as': 'keyword',\n    'do': 'keyword',\n    'else': 'keyword',\n    'end': 'keyword',\n    'exception': 'keyword',\n    'fun': 'keyword',\n    'functor': 'keyword',\n    'if': 'keyword',\n    'in': 'keyword',\n    'include': 'keyword',\n    'let': 'keyword',\n    'of': 'keyword',\n    'open': 'keyword',\n    'rec': 'keyword',\n    'struct': 'keyword',\n    'then': 'keyword',\n    'type': 'keyword',\n    'val': 'keyword',\n    'while': 'keyword',\n    'with': 'keyword'\n  };\n\n  var extraWords = parserConfig.extraWords || {};\n  for (var prop in extraWords) {\n    if (extraWords.hasOwnProperty(prop)) {\n      words[prop] = parserConfig.extraWords[prop];\n    }\n  }\n  var hintWords = [];\n  for (var k in words) { hintWords.push(k); }\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    if (ch === '\"') {\n      state.tokenize = tokenString;\n      return state.tokenize(stream, state);\n    }\n    if (ch === '{') {\n      if (stream.eat('|')) {\n        state.longString = true;\n        state.tokenize = tokenLongString;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '(') {\n      if (stream.match(/^\\*(?!\\))/)) {\n        state.commentLevel++;\n        state.tokenize = tokenComment;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '~' || ch === '?') {\n      stream.eatWhile(/\\w/);\n      return 'variableName.special';\n    }\n    if (ch === '`') {\n      stream.eatWhile(/\\w/);\n      return 'quote';\n    }\n    if (ch === '/' && parserConfig.slashComments && stream.eat('/')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (/\\d/.test(ch)) {\n      if (ch === '0' && stream.eat(/[bB]/)) {\n        stream.eatWhile(/[01]/);\n      } if (ch === '0' && stream.eat(/[xX]/)) {\n        stream.eatWhile(/[0-9a-fA-F]/)\n      } if (ch === '0' && stream.eat(/[oO]/)) {\n        stream.eatWhile(/[0-7]/);\n      } else {\n        stream.eatWhile(/[\\d_]/);\n        if (stream.eat('.')) {\n          stream.eatWhile(/[\\d]/);\n        }\n        if (stream.eat(/[eE]/)) {\n          stream.eatWhile(/[\\d\\-+]/);\n        }\n      }\n      return 'number';\n    }\n    if ( /[+\\-*&%=<>!?|@\\.~:]/.test(ch)) {\n      return 'operator';\n    }\n    if (/[\\w\\xa1-\\uffff]/.test(ch)) {\n      stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n      var cur = stream.current();\n      return words.hasOwnProperty(cur) ? words[cur] : 'variable';\n    }\n    return null\n  }\n\n  function tokenString(stream, state) {\n    var next, end = false, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === '\"' && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    if (end && !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  };\n\n  function tokenComment(stream, state) {\n    var prev, next;\n    while(state.commentLevel > 0 && (next = stream.next()) != null) {\n      if (prev === '(' && next === '*') state.commentLevel++;\n      if (prev === '*' && next === ')') state.commentLevel--;\n      prev = next;\n    }\n    if (state.commentLevel <= 0) {\n      state.tokenize = tokenBase;\n    }\n    return 'comment';\n  }\n\n  function tokenLongString(stream, state) {\n    var prev, next;\n    while (state.longString && (next = stream.next()) != null) {\n      if (prev === '|' && next === '}') state.longString = false;\n      prev = next;\n    }\n    if (!state.longString) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  }\n\n  return {\n    startState: function() {return {tokenize: tokenBase, commentLevel: 0, longString: false};},\n    token: function(stream, state) {\n      if (stream.eatSpace()) return null;\n      return state.tokenize(stream, state);\n    },\n\n    languageData: {\n      autocomplete: hintWords,\n      commentTokens: {\n        line: parserConfig.slashComments ? \"//\" : undefined,\n        block: {open: \"(*\", close: \"*)\"}\n      }\n    }\n  };\n};\n\nexport const oCaml = mlLike({\n  name: \"ocaml\",\n  extraWords: {\n    'and': 'keyword',\n    'assert': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'constraint': 'keyword',\n    'done': 'keyword',\n    'downto': 'keyword',\n    'external': 'keyword',\n    'function': 'keyword',\n    'initializer': 'keyword',\n    'lazy': 'keyword',\n    'match': 'keyword',\n    'method': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'new': 'keyword',\n    'nonrec': 'keyword',\n    'object': 'keyword',\n    'private': 'keyword',\n    'sig': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'value': 'keyword',\n    'virtual': 'keyword',\n    'when': 'keyword',\n\n    // builtins\n    'raise': 'builtin',\n    'failwith': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    // Pervasives builtins\n    'asr': 'builtin',\n    'land': 'builtin',\n    'lor': 'builtin',\n    'lsl': 'builtin',\n    'lsr': 'builtin',\n    'lxor': 'builtin',\n    'mod': 'builtin',\n    'or': 'builtin',\n\n    // More Pervasives\n    'raise_notrace': 'builtin',\n    'trace': 'builtin',\n    'exit': 'builtin',\n    'print_string': 'builtin',\n    'print_endline': 'builtin',\n\n     'int': 'type',\n     'float': 'type',\n     'bool': 'type',\n     'char': 'type',\n     'string': 'type',\n     'unit': 'type',\n\n     // Modules\n     'List': 'builtin'\n  }\n});\n\nexport const fSharp = mlLike({\n  name: \"fsharp\",\n  extraWords: {\n    'abstract': 'keyword',\n    'assert': 'keyword',\n    'base': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'default': 'keyword',\n    'delegate': 'keyword',\n    'do!': 'keyword',\n    'done': 'keyword',\n    'downcast': 'keyword',\n    'downto': 'keyword',\n    'elif': 'keyword',\n    'extern': 'keyword',\n    'finally': 'keyword',\n    'for': 'keyword',\n    'function': 'keyword',\n    'global': 'keyword',\n    'inherit': 'keyword',\n    'inline': 'keyword',\n    'interface': 'keyword',\n    'internal': 'keyword',\n    'lazy': 'keyword',\n    'let!': 'keyword',\n    'match': 'keyword',\n    'member': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'namespace': 'keyword',\n    'new': 'keyword',\n    'null': 'keyword',\n    'override': 'keyword',\n    'private': 'keyword',\n    'public': 'keyword',\n    'return!': 'keyword',\n    'return': 'keyword',\n    'select': 'keyword',\n    'static': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'upcast': 'keyword',\n    'use!': 'keyword',\n    'use': 'keyword',\n    'void': 'keyword',\n    'when': 'keyword',\n    'yield!': 'keyword',\n    'yield': 'keyword',\n\n    // Reserved words\n    'atomic': 'keyword',\n    'break': 'keyword',\n    'checked': 'keyword',\n    'component': 'keyword',\n    'const': 'keyword',\n    'constraint': 'keyword',\n    'constructor': 'keyword',\n    'continue': 'keyword',\n    'eager': 'keyword',\n    'event': 'keyword',\n    'external': 'keyword',\n    'fixed': 'keyword',\n    'method': 'keyword',\n    'mixin': 'keyword',\n    'object': 'keyword',\n    'parallel': 'keyword',\n    'process': 'keyword',\n    'protected': 'keyword',\n    'pure': 'keyword',\n    'sealed': 'keyword',\n    'tailcall': 'keyword',\n    'trait': 'keyword',\n    'virtual': 'keyword',\n    'volatile': 'keyword',\n\n    // builtins\n    'List': 'builtin',\n    'Seq': 'builtin',\n    'Map': 'builtin',\n    'Set': 'builtin',\n    'Option': 'builtin',\n    'int': 'builtin',\n    'string': 'builtin',\n    'not': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    'raise': 'builtin',\n    'failwith': 'builtin'\n  },\n  slashComments: true\n});\n\nexport const sml = mlLike({\n  name: \"sml\",\n  extraWords: {\n    'abstype': 'keyword',\n    'and': 'keyword',\n    'andalso': 'keyword',\n    'case': 'keyword',\n    'datatype': 'keyword',\n    'fn': 'keyword',\n    'handle': 'keyword',\n    'infix': 'keyword',\n    'infixr': 'keyword',\n    'local': 'keyword',\n    'nonfix': 'keyword',\n    'op': 'keyword',\n    'orelse': 'keyword',\n    'raise': 'keyword',\n    'withtype': 'keyword',\n    'eqtype': 'keyword',\n    'sharing': 'keyword',\n    'sig': 'keyword',\n    'signature': 'keyword',\n    'structure': 'keyword',\n    'where': 'keyword',\n    'true': 'keyword',\n    'false': 'keyword',\n\n    // types\n    'int': 'builtin',\n    'real': 'builtin',\n    'string': 'builtin',\n    'char': 'builtin',\n    'bool': 'builtin'\n  },\n  slashComments: true\n});\n"], "mappings": ";;;AAAA,SAAS,OAAO,cAAc;AAC5B,MAAI,QAAQ;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,aAAa;AAAA,IACb,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAEA,MAAI,aAAa,aAAa,cAAc,CAAC;AAC7C,WAAS,QAAQ,YAAY;AAC3B,QAAI,WAAW,eAAe,IAAI,GAAG;AACnC,YAAM,IAAI,IAAI,aAAa,WAAW,IAAI;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,YAAY,CAAC;AACjB,WAAS,KAAK,OAAO;AAAE,cAAU,KAAK,CAAC;AAAA,EAAG;AAE1C,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK;AAErB,QAAI,OAAO,KAAK;AACd,YAAM,WAAW;AACjB,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AACA,QAAI,OAAO,KAAK;AACd,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAM,aAAa;AACnB,cAAM,WAAW;AACjB,eAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,MACrC;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AACd,UAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,cAAM;AACN,cAAM,WAAW;AACjB,eAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,MACrC;AAAA,IACF;AACA,QAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,aAAO,SAAS,IAAI;AACpB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK;AACd,aAAO,SAAS,IAAI;AACpB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,aAAa,iBAAiB,OAAO,IAAI,GAAG,GAAG;AAC/D,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,KAAK,EAAE,GAAG;AACjB,UAAI,OAAO,OAAO,OAAO,IAAI,MAAM,GAAG;AACpC,eAAO,SAAS,MAAM;AAAA,MACxB;AAAE,UAAI,OAAO,OAAO,OAAO,IAAI,MAAM,GAAG;AACtC,eAAO,SAAS,aAAa;AAAA,MAC/B;AAAE,UAAI,OAAO,OAAO,OAAO,IAAI,MAAM,GAAG;AACtC,eAAO,SAAS,OAAO;AAAA,MACzB,OAAO;AACL,eAAO,SAAS,OAAO;AACvB,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,iBAAO,SAAS,MAAM;AAAA,QACxB;AACA,YAAI,OAAO,IAAI,MAAM,GAAG;AACtB,iBAAO,SAAS,SAAS;AAAA,QAC3B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAK,sBAAsB,KAAK,EAAE,GAAG;AACnC,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,KAAK,EAAE,GAAG;AAC9B,aAAO,SAAS,iBAAiB;AACjC,UAAI,MAAM,OAAO,QAAQ;AACzB,aAAO,MAAM,eAAe,GAAG,IAAI,MAAM,GAAG,IAAI;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,QAAQ,OAAO;AAClC,QAAI,MAAM,MAAM,OAAO,UAAU;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,SAAS,OAAO,CAAC,SAAS;AAC5B,cAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,SAAS;AAAA,IACjC;AACA,QAAI,OAAO,CAAC,SAAS;AACnB,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAC;AAED,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,MAAM;AACV,WAAM,MAAM,eAAe,MAAM,OAAO,OAAO,KAAK,MAAM,MAAM;AAC9D,UAAI,SAAS,OAAO,SAAS;AAAK,cAAM;AACxC,UAAI,SAAS,OAAO,SAAS;AAAK,cAAM;AACxC,aAAO;AAAA,IACT;AACA,QAAI,MAAM,gBAAgB,GAAG;AAC3B,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgB,QAAQ,OAAO;AACtC,QAAI,MAAM;AACV,WAAO,MAAM,eAAe,OAAO,OAAO,KAAK,MAAM,MAAM;AACzD,UAAI,SAAS,OAAO,SAAS;AAAK,cAAM,aAAa;AACrD,aAAO;AAAA,IACT;AACA,QAAI,CAAC,MAAM,YAAY;AACrB,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,YAAY,WAAW;AAAC,aAAO,EAAC,UAAU,WAAW,cAAc,GAAG,YAAY,MAAK;AAAA,IAAE;AAAA,IACzF,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,OAAO,SAAS;AAAG,eAAO;AAC9B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AAAA,IAEA,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,QACb,MAAM,aAAa,gBAAgB,OAAO;AAAA,QAC1C,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AAEO,IAAM,QAAQ,OAAO;AAAA,EAC1B,MAAM;AAAA,EACN,YAAY;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ;AAAA;AAAA,IAGR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,SAAS;AAAA;AAAA,IAGT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA;AAAA,IAGN,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IAEhB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA;AAAA,IAGR,QAAQ;AAAA,EACX;AACF,CAAC;AAEM,IAAM,SAAS,OAAO;AAAA,EAC3B,MAAM;AAAA,EACN,YAAY;AAAA,IACV,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA;AAAA,IAGT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA;AAAA,IAGZ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IAET,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,eAAe;AACjB,CAAC;AAEM,IAAM,MAAM,OAAO;AAAA,EACxB,MAAM;AAAA,EACN,YAAY;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA;AAAA,IAGT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAAA,EACA,eAAe;AACjB,CAAC;", "names": []}