package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.OrderCreateRequest;
import com.lzhshtp.shangcheng.dto.OrderDTO;
import com.lzhshtp.shangcheng.dto.OrderCompleteResponse;
import com.lzhshtp.shangcheng.dto.PageResult;

import java.util.Map;

public interface OrderService {
    
    /**
     * 创建订单
     * 
     * @param userId 当前用户ID
     * @param request 订单创建请求
     * @return 创建的订单ID
     */
    Long createOrder(Long userId, OrderCreateRequest request);
    
    /**
     * 获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderDTO getOrderById(Long orderId);
    
    /**
     * 查询买家订单列表
     * 
     * @param buyerId 买家ID
     * @param status 订单状态（可选）
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页订单列表
     */
    PageResult<OrderDTO> getBuyerOrders(Long userId, String status, int page, int pageSize);
    
    /**
     * 查询卖家订单列表
     * 
     * @param sellerId 卖家ID
     * @param status 订单状态（可选）
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页订单列表
     */
    PageResult<OrderDTO> getSellerOrders(Long userId, String status, int page, int pageSize);
    
    /**
     * 管理员查询所有订单
     * 
     * @param keyword 搜索关键词（订单号、买家用户名、卖家用户名、商品名称）
     * @param status 订单状态（可选）
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页订单列表
     */
    PageResult<OrderDTO> getAdminOrders(String keyword, String status, int page, int pageSize);
    
    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    boolean cancelOrder(Long orderId, Long userId);
    
    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 新状态
     * @param userId 当前用户ID（用于权限验证）
     * @return 是否成功
     */
    boolean updateOrderStatus(Long orderId, String status, Long userId);
    
    /**
     * 生成支付宝支付链接
     * 
     * @param orderId 订单ID
     * @param userId 当前用户ID
     * @return 支付链接
     */
    String generateAlipayLink(Long orderId, Long userId);
    
    /**
     * 处理支付宝支付回调
     *
     * @param params 回调参数
     * @return 是否处理成功
     */
    boolean handleAlipayCallback(Map<String, String> params);

    /**
     * 使用余额支付订单
     *
     * @param orderId 订单ID
     * @param userId 当前用户ID
     * @return 是否支付成功
     */
    boolean payWithBalance(Long orderId, Long userId);

    /**
     * 获取订单支付信息
     *
     * @param orderId 订单ID
     * @param userId 当前用户ID
     * @return 支付信息（包含订单金额、用户余额等）
     */
    Map<String, Object> getPaymentInfo(Long orderId, Long userId);

    /**
     * 完成订单（确认收货）
     *
     * @param orderId 订单ID
     * @param userId 当前用户ID
     * @return 完成结果，包含是否需要显示评价弹窗
     */
    OrderCompleteResponse completeOrder(Long orderId, Long userId);

    void shipOrder(Long orderId, Long userId);
}