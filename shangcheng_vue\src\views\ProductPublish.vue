<template>
  <div class="product-publish-page">
    <!-- 顶部导航 -->
    <nav class="top-nav">
      <div class="nav-content">
        <router-link to="/home" class="logo" style="color: #FF0000;">易转</router-link>
        <div class="nav-right">
          <router-link to="/profile" class="user-avatar" title="个人中心">
            <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主要内容区 -->
    <div class="container">
      <div class="publish-form-card">
        <h1 class="page-title">发布您的闲置商品</h1>
        
        <form @submit.prevent="publishProduct" class="publish-form">
          <!-- 商品标题 -->
          <div class="form-group">
            <label for="title">商品标题</label>
            <input 
              type="text" 
              id="title" 
              v-model="productForm.title" 
              placeholder="简洁明了的标题更容易被买家搜索到" 
              class="form-control"
              required
            >
          </div>
          
          <!-- 商品描述 -->
          <div class="form-group">
            <label for="description">商品描述</label>
            <textarea
              id="description"
              v-model="productForm.description"
              placeholder="详细描述商品的使用情况、功能、尺寸、配件等信息"
              class="form-control"
              rows="6"
              required
            ></textarea>
          </div>

          <!-- 官方验货选项 -->
          <div class="form-group verification-section">
            <div class="verification-option">
              <label class="verification-label">
                <input
                  type="checkbox"
                  v-model="productForm.supportOfficialVerification"
                  @change="onVerificationChange"
                >
                <span class="checkmark"></span>
                <span class="label-text">提供官方验货服务</span>
              </label>
              <div class="verification-info" v-if="productForm.supportOfficialVerification">
                <div class="verification-benefits">
                  <p class="benefit-item">✅ 买家享受免费官方验货</p>
                  <p class="benefit-item">🛡️ 提升商品可信度</p>
                  <p class="benefit-item">📈 增加成交概率</p>
                </div>
                <div class="verification-cost">
                  <p class="cost-note">
                    💰 验货费用：¥{{ verificationFee }} (发货时从您的余额扣除)
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 商品图片 -->
          <div class="form-group">
            <label>商品图片</label>
            <div class="image-upload-area">
              <div 
                class="image-upload-box" 
                @click="triggerImageUpload"
                v-if="!productForm.imagePreview"
              >
                <div class="upload-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path><line x1="16" y1="5" x2="22" y2="5"></line><line x1="19" y1="2" x2="19" y2="8"></line><circle cx="9" cy="9" r="2"></circle><path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg>
                </div>
                <p>点击选择图片上传</p>
              </div>
              <div class="image-preview" v-if="productForm.imagePreview">
                <img :src="productForm.imagePreview" alt="商品预览图">
                <button type="button" class="remove-image-btn" @click="removeImage">×</button>
              </div>
              <input 
                type="file" 
                ref="imageInput" 
                @change="handleImageChange" 
                accept="image/*" 
                style="display: none"
              >
            </div>
          </div>
          
          <!-- 商品分类 -->
          <div class="form-group">
            <label for="category">商品分类</label>
            <select 
              id="category" 
              v-model="productForm.categoryId" 
              class="form-control"
              required
            >
              <option value="" disabled selected>请选择分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <!-- 商品价格 -->
          <div class="form-group">
            <label for="price">期望价格</label>
            <div class="price-input-group">
              <span class="currency-symbol">¥</span>
              <input 
                type="number" 
                id="price" 
                v-model="productForm.price" 
                placeholder="输入价格 (元)" 
                class="form-control price-input"
                min="0.01"
                step="0.01"
                required
              >
            </div>
          </div>
          
          <!-- 新旧程度 -->
          <div class="form-group">
            <label for="condition">新旧程度</label>
            <select 
              id="condition" 
              v-model="productForm.condition" 
              class="form-control"
              required
            >
              <option value="" disabled selected>请选择新旧程度</option>
              <option value="全新">全新</option>
              <option value="几乎全新">几乎全新</option>
              <option value="九成新">九成新</option>
              <option value="八成新">八成新</option>
              <option value="七成新">七成新</option>
              <option value="六成新及以下">六成新及以下</option>
            </select>
          </div>
          
          <!-- 所在地 -->
          <div class="form-group">
            <label for="location">所在地</label>
            <input 
              type="text" 
              id="location" 
              v-model="productForm.location" 
              placeholder="例：上海市徐汇区" 
              class="form-control"
              required
            >
          </div>
          
          <!-- 交易方式 -->
          <div class="form-group">
            <label for="deliveryMethod">交易方式</label>
            <select 
              id="deliveryMethod" 
              v-model="productForm.deliveryMethod" 
              class="form-control"
              required
            >
              <option value="" disabled selected>请选择交易方式</option>
              <option value="线下自提">线下自提</option>
              <option value="快递邮寄">快递邮寄</option>
              <option value="同城配送">同城配送</option>
              <option value="均可">均可</option>
            </select>
          </div>
          
          <!-- 提交按钮 -->
          <div class="form-actions">
            <button type="submit" class="publish-btn" :disabled="isLoading">
              {{ isLoading ? '发布中...' : '立即发布' }}
            </button>
          </div>
          
          <!-- 错误信息 -->
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getAllCategories } from '@/api/category';
import { uploadProduct } from '@/api/product';

const router = useRouter();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 商品表单数据
const productForm = reactive({
  title: '',
  description: '',
  price: '',
  categoryId: '',
  condition: '',
  location: '',
  deliveryMethod: '',
  image: null,
  imagePreview: null,
  supportOfficialVerification: false,
  verificationFee: 10.00
});

// 加载状态
const isLoading = ref(false);
const errorMessage = ref('');

// 分类数据
const categories = ref([]);
const imageInput = ref(null);

// 验货相关数据
const verificationFee = ref(10.00); // 默认验货费用

// 触发图片选择
const triggerImageUpload = () => {
  imageInput.value.click();
};

// 验货选择变化处理
const onVerificationChange = () => {
  if (productForm.supportOfficialVerification) {
    productForm.verificationFee = verificationFee.value;
  } else {
    productForm.verificationFee = 0;
  }

  // 调试信息
  console.log('验货选择变化:', {
    supportOfficialVerification: productForm.supportOfficialVerification,
    verificationFee: productForm.verificationFee
  });
};

// 处理图片选择
const handleImageChange = (e) => {
  const file = e.target.files[0];
  if (!file) return;
  
  productForm.image = file;
  productForm.imagePreview = URL.createObjectURL(file);
};

// 移除已选图片
const removeImage = () => {
  productForm.image = null;
  productForm.imagePreview = null;
  imageInput.value.value = null; // 清空文件输入
};

// 发布商品
const publishProduct = async () => {
  try {
    // 表单验证
    if (!productForm.image) {
      alert('请上传商品图片');
      return;
    }
    
    // 设置加载状态
    isLoading.value = true;
    errorMessage.value = '';
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('title', productForm.title);
    formData.append('description', productForm.description);
    formData.append('price', productForm.price);
    formData.append('categoryId', productForm.categoryId);
    formData.append('condition', productForm.condition);
    formData.append('location', productForm.location);
    formData.append('deliveryMethod', productForm.deliveryMethod);
    formData.append('image', productForm.image);

    // 添加验货相关字段
    formData.append('supportOfficialVerification', productForm.supportOfficialVerification);
    formData.append('verificationFee', productForm.verificationFee);

    // 调试信息
    console.log('提交的验货数据:', {
      supportOfficialVerification: productForm.supportOfficialVerification,
      verificationFee: productForm.verificationFee
    });

    // 调用API上传商品
    const response = await uploadProduct(formData);
    
    if (response && response.code === 200) {
      alert('商品发布成功！');
      // 跳转到首页
      router.push('/home');
    } else {
      errorMessage.value = response?.message || '发布失败，请稍后重试';
      alert('发布失败: ' + errorMessage.value);
    }
  } catch (error) {
    console.error('发布商品失败:', error);
    errorMessage.value = '网络错误，请稍后重试';
    alert('发布失败: ' + errorMessage.value);
  } finally {
    isLoading.value = false;
  }
};

// 获取分类数据
const fetchCategories = async () => {
  try {
    const response = await getAllCategories();
    if (response && response.code === 200 && response.data) {
      categories.value = response.data;
    }
  } catch (error) {
    console.error('获取分类数据失败:', error);
    categories.value = [
      { id: 1, name: '电子产品' },
      { id: 2, name: '服装鞋包' },
      { id: 3, name: '图书音像' },
      { id: 4, name: '家居家具' },
      { id: 5, name: '运动户外' },
      { id: 6, name: '美妆个护' },
      { id: 7, name: '其他' }
    ];
  }
};

onMounted(async () => {
  // 获取分类数据
  fetchCategories();
  
  // 检查用户是否登录
  if (!userStore.isLoggedIn) {
    alert('请先登录再发布商品');
    router.push('/login');
    return;
  }
  
  // 用户信息已在路由守卫中预先加载，不需要在这里重复加载
});
</script>

<style scoped>
.product-publish-page {
  font-family: 'Noto Sans SC', sans-serif;
  background-color: #F5F5F5;
  min-height: 100vh;
}

/* 顶部导航 */
.top-nav {
  background: #FFFFFF;
  padding: 18px 0;
  border-bottom: 1px solid #EFEFEF;
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  text-decoration: none;
}

.nav-right {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF0000;
  transition: transform 0.2s ease;
}

.avatar-image:hover {
  transform: scale(1.1);
}

/* 主要内容区 */
.container {
  max-width: 800px;
  margin: 30px auto;
  padding: 0 20px;
}

.publish-form-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 30px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 1px solid #EFEFEF;
  padding-bottom: 20px;
}

/* 表单样式 */
.publish-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.form-control {
  padding: 12px 16px;
  border: 1px solid #DEDEDE;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #FF0000;
  box-shadow: 0 0 0 2px rgba(255,0,0,0.1);
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

/* 图片上传区域 */
.image-upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.image-upload-box {
  width: 150px;
  height: 150px;
  border: 2px dashed #DEDEDE;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #999;
}

.image-upload-box:hover {
  border-color: #FF0000;
  color: #FF0000;
}

.upload-icon {
  margin-bottom: 10px;
}

.image-preview {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0,0,0,0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
}

/* 价格输入组 */
.price-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 16px;
  font-weight: 500;
  color: #333;
}

.price-input {
  padding-left: 30px;
}

/* 提交按钮 */
.form-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.publish-btn {
  padding: 12px 40px;
  background-color: #FF0000;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.publish-btn:hover {
  background-color: #E60000;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255,0,0,0.2);
}

.publish-btn:disabled {
  background-color: #FF9999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  color: #FF0000;
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
}

/* 验货选项样式 */
.verification-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #dee2e6;
  transition: all 0.3s ease;
}

.verification-section:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.verification-option {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.verification-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.verification-label input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.label-text {
  color: #007bff;
  font-weight: bold;
}

.verification-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.verification-benefits {
  margin-bottom: 12px;
}

.benefit-item {
  margin: 8px 0;
  color: #28a745;
  font-size: 14px;
  font-weight: 500;
}

.verification-cost {
  border-top: 1px solid #dee2e6;
  padding-top: 12px;
}

.cost-note {
  color: #fd7e14;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  background: #fff3cd;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .publish-form-card {
    padding: 20px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .form-control {
    padding: 10px 12px;
  }
  
  .publish-btn {
    width: 100%;
  }
}
</style> 