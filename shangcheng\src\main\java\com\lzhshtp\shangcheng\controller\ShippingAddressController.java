package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ShippingAddressDTO;
import com.lzhshtp.shangcheng.dto.ShippingAddressRequest;
import com.lzhshtp.shangcheng.exception.BusinessException;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ShippingAddressService;
import com.lzhshtp.shangcheng.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收货地址控制器
 */
@RestController
@RequestMapping("/api/addresses")
public class ShippingAddressController {
    
    @Autowired
    private ShippingAddressService shippingAddressService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取当前用户的所有收货地址
     *
     * @return 收货地址列表
     */
    @GetMapping
    public ApiResponse<List<ShippingAddressDTO>> getUserAddresses() {
        // 这里应该从当前登录用户中获取用户ID，这里暂时模拟一个用户ID
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.getUserAddresses(userId));
    }

    /**
     * 获取收货地址详情
     *
     * @param addressId 收货地址ID
     * @return 收货地址详情
     */
    @GetMapping("/{addressId}")
    public ApiResponse<ShippingAddressDTO> getAddressById(@PathVariable Long addressId) {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.getAddressById(addressId, userId));
    }

    /**
     * 添加收货地址
     *
     * @param request 收货地址请求
     * @return 新增的收货地址
     */
    @PostMapping
    public ApiResponse<ShippingAddressDTO> addAddress(@Valid @RequestBody ShippingAddressRequest request) {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.addAddress(request, userId));
    }
    
    /**
     * 更新收货地址
     *
     * @param addressId 收货地址ID
     * @param request 收货地址请求
     * @return 更新后的收货地址
     */
    @PutMapping("/{addressId}")
    public ApiResponse<ShippingAddressDTO> updateAddress(@PathVariable Long addressId,
                                                   @Valid @RequestBody ShippingAddressRequest request) {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.updateAddress(addressId, request, userId));
    }

    /**
     * 删除收货地址
     *
     * @param addressId 收货地址ID
     * @return 操作结果
     */
    @DeleteMapping("/{addressId}")
    public ApiResponse<Boolean> deleteAddress(@PathVariable Long addressId) {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.deleteAddress(addressId, userId));
    }

    /**
     * 设置默认收货地址
     *
     * @param addressId 收货地址ID
     * @return 操作结果
     */
    @PutMapping("/{addressId}/default")
    public ApiResponse<Boolean> setDefaultAddress(@PathVariable Long addressId) {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.setDefaultAddress(addressId, userId));
    }

    /**
     * 获取默认收货地址
     *
     * @return 默认收货地址
     */
    @GetMapping("/default")
    public ApiResponse<ShippingAddressDTO> getDefaultAddress() {
        Long userId = getCurrentUserId();
        return ApiResponse.success(shippingAddressService.getDefaultAddress(userId));
    }
    
    /**
     * 获取当前登录用户的ID
     * 
     * @return 当前登录用户ID
     */
    private Long getCurrentUserId() {
        // 从Spring Security上下文中获取当前认证用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || 
            authentication.getName().equals("anonymousUser")) {
            // 未登录或匿名用户，抛出异常
            throw new BusinessException("用户未登录");
        }
        
        // 获取用户名
        String username = authentication.getName();
        // 根据用户名查找用户对象
        User user = userService.findByUsername(username);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        return user.getUserId();
    }
} 