package com.lzhshtp.shangcheng.service.audit;

import com.aliyun.imageaudit20191230.Client;
import com.aliyun.imageaudit20191230.models.ScanImageRequest;
import com.aliyun.imageaudit20191230.models.ScanImageResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云图片审核服务
 */
@Slf4j
@Service
public class AliyunImageAuditService {

    @Autowired
    private Client imageAuditClient;

    /**
     * 审核单张图片 - 使用imageaudit20191230 API
     */
    public AuditResultDTO auditSingleImage(String imageUrl) {
        if (!StringUtils.hasText(imageUrl)) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(30)
                .reason("图片URL为空")
                .build();
        }

        try {
            log.info("开始阿里云图片审核，图片URL: {}", imageUrl);

            // 测试模式：如果是测试图片URL，返回模拟结果
            if (isTestImageUrl(imageUrl)) {
                return handleTestImage(imageUrl);
            }

            // 创建图片任务
            ScanImageRequest.ScanImageRequestTask task = new ScanImageRequest.ScanImageRequestTask()
                    .setImageURL(imageUrl);

            // 创建扫描请求
            ScanImageRequest scanImageRequest = new ScanImageRequest()
                    .setTask(Collections.singletonList(task))
                    .setScene(Arrays.asList("porn", "terrorism", "ad", "live")); // 多种检测场景

            RuntimeOptions runtime = new RuntimeOptions();

            // 调用API
            ScanImageResponse response = imageAuditClient.scanImageWithOptions(scanImageRequest, runtime);

            // 解析结果
            return parseImageScanResult(response, imageUrl);

        } catch (Exception e) {
            log.error("阿里云图片审核异常，图片URL: {}", imageUrl, e);
            return createManualReviewResult("图片审核异常：" + e.getMessage());
        }
    }

    /**
     * 解析图片扫描结果 - 完全按照官方示例的方式
     */
    private AuditResultDTO parseImageScanResult(ScanImageResponse response, String imageUrl) {
        try {
            if (response == null || response.getBody() == null) {
                log.warn("阿里云图片审核响应为空，图片URL: {}", imageUrl);
                return createManualReviewResult("审核响应为空");
            }

            // 完全按照官方示例的方式获取suggestion
            // String suggestion = client.scanImageWithOptions(scanImageRequest, runtime).getBody().getData().results.get(0).getSubResults().get(0).suggestion;
            String suggestion;
            try {
                suggestion = response.getBody().getData().results.get(0).getSubResults().get(0).suggestion;
                log.info("图片审核结果 - 建议: {}, 图片URL: {}", suggestion, imageUrl);
            } catch (Exception e) {
                log.info("图片审核通过，无违规内容，图片URL: {}", imageUrl);
                return AuditResultDTO.pass("图片审核通过");
            }

            // 根据建议返回结果
            if ("block".equals(suggestion)) {
                return AuditResultDTO.builder()
                    .passed(false)
                    .riskLevel(AuditConstants.RiskLevel.HIGH)
                    .score(90)
                    .reason("图片包含违规内容")
                    .details(Map.of("suggestion", suggestion))
                    .build();
            } else if ("review".equals(suggestion)) {
                return AuditResultDTO.builder()
                    .passed(false)
                    .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                    .score(60)
                    .reason("图片内容存疑，需要人工审核")
                    .details(Map.of("suggestion", suggestion))
                    .build();
            } else if ("pass".equals(suggestion)) {
                // 通过审核
                log.info("图片审核通过，图片URL: {}", imageUrl);
                return AuditResultDTO.pass("图片审核通过");
            } else {
                // 未知建议，默认通过
                log.info("图片审核通过（未知建议: {}），图片URL: {}", suggestion, imageUrl);
                return AuditResultDTO.pass("图片审核通过");
            }

        } catch (Exception e) {
            log.error("解析阿里云图片审核结果异常，图片URL: {}", imageUrl, e);
            return createManualReviewResult("解析审核结果异常：" + e.getMessage());
        }
    }

    /**
     * 判断是否为测试图片URL
     */
    private boolean isTestImageUrl(String imageUrl) {
        return imageUrl != null && (
            imageUrl.startsWith("test-") ||
            imageUrl.equals("test-image.jpg") ||
            imageUrl.contains("placeholder.com") ||
            imageUrl.contains("test")
        );
    }

    /**
     * 处理测试图片，返回模拟审核结果
     */
    private AuditResultDTO handleTestImage(String imageUrl) {
        log.info("检测到测试图片URL，返回模拟审核结果: {}", imageUrl);

        if (imageUrl.contains("banned") || imageUrl.contains("违禁")) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(90)
                .reason("测试模式：模拟违禁图片")
                .build();
        } else if (imageUrl.contains("sensitive") || imageUrl.contains("敏感")) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(60)
                .reason("测试模式：模拟敏感图片，需要人工审核")
                .build();
        } else {
            return AuditResultDTO.pass("测试模式：模拟正常图片");
        }
    }

    /**
     * 创建需要人工审核的结果
     */
    private AuditResultDTO createManualReviewResult(String reason) {
        return AuditResultDTO.builder()
            .passed(false)
            .riskLevel(AuditConstants.RiskLevel.MEDIUM)
            .score(50)
            .reason(reason)
            .build();
    }



    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            // 检查客户端是否可用
            return imageAuditClient != null;
        } catch (Exception e) {
            log.error("阿里云图片审核服务不可用", e);
            return false;
        }
    }
}
