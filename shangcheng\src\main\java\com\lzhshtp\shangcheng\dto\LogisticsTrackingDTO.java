package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物流跟踪DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsTrackingDTO {
    
    private Long trackingId;
    private Long orderId;
    private String trackingType; // seller_to_buyer, seller_to_official, official_to_buyer, buyer_to_seller
    private String trackingNumber;
    private String currentStatus;
    private String currentLocation;
    private String logisticsCompany;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
