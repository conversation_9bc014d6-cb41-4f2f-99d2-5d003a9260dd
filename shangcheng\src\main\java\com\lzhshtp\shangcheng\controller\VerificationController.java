package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.VerificationDTO;
import com.lzhshtp.shangcheng.dto.VerificationResultDTO;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.service.VerificationService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 验货控制器
 */
@RestController
@RequestMapping("/api/verifications")
public class VerificationController {
    
    private static final Logger logger = LoggerFactory.getLogger(VerificationController.class);
    
    @Autowired
    private VerificationService verificationService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 查询待验货列表
     */
    @GetMapping("/pending")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<List<VerificationDTO>> getPendingVerifications() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            List<VerificationDTO> verifications = verificationService.getPendingVerifications(user.getUserId());
            return ApiResponse.success("查询成功", verifications);
        } catch (Exception e) {
            logger.error("查询待验货列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 查询所有待验货列表（管理员）
     */
    @GetMapping("/pending/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<List<VerificationDTO>> getAllPendingVerifications() {
        try {
            List<VerificationDTO> verifications = verificationService.getPendingVerifications(null);
            return ApiResponse.success("查询成功", verifications);
        } catch (Exception e) {
            logger.error("查询所有待验货列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 更新验货状态
     */
    @PostMapping("/{verificationId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> updateVerificationStatus(
            @PathVariable Long verificationId, 
            @RequestParam String status) {
        try {
            boolean success = verificationService.updateVerificationStatus(verificationId, status);
            return ApiResponse.success("状态更新成功", success);
        } catch (Exception e) {
            logger.error("更新验货状态失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 提交验货结果
     */
    @PostMapping("/result")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> submitVerificationResult(@Valid @RequestBody VerificationResultDTO result) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            boolean success = verificationService.submitVerificationResult(user.getUserId(), result);
            return ApiResponse.success("验货结果提交成功", success);
        } catch (Exception e) {
            logger.error("提交验货结果失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 验货通过，转发给买家
     */
    @PostMapping("/forward/{verificationId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> forwardToBuyer(@PathVariable Long verificationId) {
        try {
            boolean success = verificationService.forwardToBuyer(verificationId);
            return ApiResponse.success("转发成功", success);
        } catch (Exception e) {
            logger.error("转发给买家失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 根据订单ID查询验货记录
     */
    @GetMapping("/order/{orderId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<VerificationDTO> getVerificationByOrderId(@PathVariable Long orderId) {
        try {
            VerificationDTO verification = verificationService.getVerificationByOrderId(orderId);
            if (verification == null) {
                return ApiResponse.fail("该订单没有验货记录");
            }
            return ApiResponse.success("查询成功", verification);
        } catch (Exception e) {
            logger.error("查询验货记录失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员查询验货记录列表
     */
    @GetMapping("/admin/list")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Object> getAdminVerificationList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String verificationPayer,
            @RequestParam(required = false) String keyword) {
        try {
            List<VerificationDTO> verifications = verificationService.getAdminVerificationList(
                page, pageSize, status, verificationPayer, keyword);

            // 构建分页响应格式
            Map<String, Object> result = new HashMap<>();
            result.put("records", verifications);
            result.put("total", verifications.size()); // 注意：这里应该是总记录数，不是当前页记录数
            result.put("page", page);
            result.put("pageSize", pageSize);

            return ApiResponse.success("查询成功", result);
        } catch (Exception e) {
            logger.error("管理员查询验货记录列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员查询验货统计数据
     */
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Object> getAdminVerificationStatistics() {
        try {
            Object statistics = verificationService.getAdminVerificationStatistics();
            return ApiResponse.success("查询成功", statistics);
        } catch (Exception e) {
            logger.error("管理员查询验货统计数据失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
}
