import request from '@/utils/request'

const BASE_URL = '/feedback'

/**
 * 获取用户反馈列表（管理员视角）
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.feedbackType - 反馈类型
 * @param {string} params.status - 反馈状态
 * @param {string} params.relatedEntityType - 关联实体类型
 * @param {number} params.relatedEntityId - 关联实体ID
 * @returns {Promise} - 返回反馈列表
 */
export function getFeedbackList(params) {
  return request({
    url: BASE_URL,
    method: 'get',
    params
  })
}

/**
 * 获取反馈详情
 * @param {number} feedbackId - 反馈ID
 * @returns {Promise} - 返回反馈详情
 */
export function getFeedbackDetail(feedbackId) {
  return request({
    url: `${BASE_URL}/${feedbackId}`,
    method: 'get'
  })
}

/**
 * 更新反馈状态
 * @param {number} feedbackId - 反馈ID
 * @param {Object} data - 状态更新数据
 * @param {string} data.status - 新状态
 * @param {string} data.adminNotes - 管理员备注
 * @returns {Promise} - 返回更新结果
 */
export function updateFeedbackStatus(feedbackId, data) {
  return request({
    url: `${BASE_URL}/${feedbackId}/status`,
    method: 'put',
    data
  })
}

/**
 * 删除反馈
 * @param {number} feedbackId - 反馈ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteFeedback(feedbackId) {
  return request({
    url: `${BASE_URL}/${feedbackId}`,
    method: 'delete'
  })
}

/**
 * 获取反馈统计信息（按状态和类型分组）
 * @returns {Promise} - 返回统计信息
 */
export function getFeedbackStats() {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'get'
  })
} 