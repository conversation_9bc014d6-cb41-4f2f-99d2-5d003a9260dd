<template>
  <div class="admin-management">
    <div class="page-header">
      <h2>管理员管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="openAddAdminDialog">添加管理员</el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索管理员名称、邮箱"
        class="search-input"
        clearable
        @clear="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>

      <el-select v-model="statusFilter" placeholder="账号状态" clearable @change="handleSearch">
        <el-option label="启用" :value="true" />
        <el-option label="禁用" :value="false" />
      </el-select>
    </div>

<!--    &lt;!&ndash; 批量操作区域 &ndash;&gt;-->
<!--    <div class="batch-actions" v-if="selectedAdmins.length > 0">-->
<!--      <el-button type="warning" @click="confirmBatchAction(false)">批量禁用</el-button>-->
<!--      <el-button type="success" @click="confirmBatchAction(true)">批量启用</el-button>-->
<!--    </div>-->

    <!-- 管理员列表表格 -->
    <el-table
      v-loading="loading"
      :data="adminList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="userId" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" width="180" />
      <el-table-column prop="email" label="邮箱" width="220" />
      <el-table-column prop="lastLoginDate" label="上次登录时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.lastLoginDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="registrationDate" label="注册时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.registrationDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="isActive" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
            {{ scope.row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openEditAdminDialog(scope.row)"
          >
            编辑
          </el-button>
<!--          <el-button -->
<!--            link -->
<!--            :type="scope.row.isActive ? 'danger' : 'success'"-->
<!--            @click="confirmStatusChange(scope.row)"-->
<!--          >-->
<!--            {{ scope.row.isActive ? '禁用' : '启用' }}-->
<!--          </el-button>-->
          <el-button
            link
            type="danger"
            @click="confirmDeleteAdmin(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next, jumper, sizes, total"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑管理员对话框 -->
    <el-dialog
      v-model="adminDialogVisible"
      :title="editMode ? '编辑管理员' : '添加管理员'"
      width="500px"
      @close="resetAdminForm"
    >
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="adminFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="adminForm.username" :disabled="editMode" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editMode || changePassword">
          <el-input v-model="adminForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item v-if="editMode">
          <el-checkbox v-model="changePassword">修改密码</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="adminDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdminForm" :loading="submitting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAdminList,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  updateAdminStatus,
  batchUpdateAdminStatus
} from '../../api/admins.js'
import { useAdminStore } from '../../stores/admin'

// 状态管理
const adminStore = useAdminStore()
const loading = ref(false)
const submitting = ref(false)
const adminList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const statusFilter = ref('')
const selectedAdmins = ref([])

// 添加/编辑管理员相关
const adminDialogVisible = ref(false)
const editMode = ref(false)
const changePassword = ref(false)
const adminFormRef = ref(null)
const adminForm = reactive({
  userId: null,
  username: '',
  email: '',
  password: ''
})

// 表单验证规则
const adminFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ]
}

// 初始加载数据
onMounted(() => {
  loadAdminList()
})

// 加载管理员列表
const loadAdminList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      isActive: statusFilter.value !== '' ? statusFilter.value : null
    }

    const response = await getAdminList(params)
    if (response.data.success) {
      // 修正数据结构匹配
      adminList.value = response.data.data.records || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取管理员列表失败')
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  loadAdminList()
}

// 处理页码变更
const handleCurrentChange = (page) => {
  currentPage.value = page
  loadAdminList()
}

// 处理每页数量变更
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadAdminList()
}

// 表格选择变更
const handleSelectionChange = (selection) => {
  selectedAdmins.value = selection
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未登录'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 打开添加管理员对话框
const openAddAdminDialog = () => {
  editMode.value = false
  changePassword.value = true
  resetAdminForm()
  adminDialogVisible.value = true
}

// 打开编辑管理员对话框
const openEditAdminDialog = (admin) => {
  editMode.value = true
  changePassword.value = false
  adminForm.userId = admin.userId
  adminForm.username = admin.username
  adminForm.email = admin.email
  adminForm.password = ''
  adminDialogVisible.value = true
}

// 重置管理员表单
const resetAdminForm = () => {
  adminForm.userId = null
  adminForm.username = ''
  adminForm.email = ''
  adminForm.password = ''
  if (adminFormRef.value) {
    adminFormRef.value.resetFields()
  }
}

// 提交管理员表单
const submitAdminForm = async () => {
  if (!adminFormRef.value) return

  await adminFormRef.value.validate(async (valid) => {
    if (!valid) return

    submitting.value = true

    try {
      // 构造提交数据
      const submitData = {
        username: adminForm.username,
        email: adminForm.email
      }

      // 只有在添加模式或修改密码模式下才提交密码
      if (!editMode.value || changePassword.value) {
        submitData.password = adminForm.password
      }

      let response
      if (editMode.value) {
        // 编辑管理员
        response = await updateAdmin(adminForm.userId, submitData)
      } else {
        // 添加管理员
        response = await createAdmin(submitData)
      }

      if (response.data.success) {
        ElMessage.success(editMode.value ? '管理员信息更新成功' : '管理员添加成功')
        adminDialogVisible.value = false
        loadAdminList()
      } else {
        ElMessage.error(response.data.message || (editMode.value ? '更新失败' : '添加失败'))
      }
    } catch (error) {
      console.error(editMode.value ? '更新管理员失败:' : '添加管理员失败:', error)
      ElMessage.error(error.response?.data?.message || (editMode.value ? '更新失败' : '添加失败'))
    } finally {
      submitting.value = false
    }
  })
}

// 确认修改管理员状态
const confirmStatusChange = (admin) => {
  // 不能禁用自己
  if (adminStore.adminInfo && adminStore.adminInfo.userId === admin.userId) {
    return ElMessage.warning('不能修改自己的账号状态')
  }

  const action = admin.isActive ? '禁用' : '启用'
  ElMessageBox.confirm(`确认${action}该管理员账号?`, '确认操作', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await updateAdminStatus({
        userId: admin.userId,
        isActive: !admin.isActive
      })

      if (response.data.success) {
        ElMessage.success(`${action}成功`)
        loadAdminList()
      } else {
        ElMessage.error(response.data.message || `${action}失败`)
      }
    } catch (error) {
      console.error(`${action}管理员失败:`, error)
      ElMessage.error(error.response?.data?.message || `${action}失败`)
    }
  }).catch(() => {})
}

// 确认删除管理员
const confirmDeleteAdmin = (admin) => {
  // 不能删除自己
  if (adminStore.adminInfo && adminStore.adminInfo.userId === admin.userId) {
    return ElMessage.warning('不能删除自己的账号')
  }

  ElMessageBox.confirm('删除后将无法恢复，确认删除该管理员?', '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteAdmin(admin.userId)

      if (response.data.success) {
        ElMessage.success('管理员删除成功')
        loadAdminList()
      } else {
        ElMessage.error(response.data.message || '删除失败')
      }
    } catch (error) {
      console.error('删除管理员失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }).catch(() => {})
}

// 确认批量操作
const confirmBatchAction = (isActive) => {
  if (selectedAdmins.value.length === 0) {
    return ElMessage.warning('请先选择管理员')
  }

  // 检查是否包含当前登录账号
  if (adminStore.adminInfo && selectedAdmins.value.some(admin => admin.userId === adminStore.adminInfo.userId)) {
    return ElMessage.warning('不能修改自己的账号状态')
  }

  const action = isActive ? '启用' : '禁用'
  const userIds = selectedAdmins.value.map(admin => admin.userId)

  ElMessageBox.confirm(`确认批量${action}选中的${userIds.length}个管理员账号?`, '确认操作', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await batchUpdateAdminStatus({
        userIds,
        isActive
      })

      if (response.data.success) {
        ElMessage.success(`批量${action}成功，共${response.data.data.updatedCount}个账号`)
        loadAdminList()
      } else {
        ElMessage.error(response.data.message || `批量${action}失败`)
      }
    } catch (error) {
      console.error(`批量${action}失败:`, error)
      ElMessage.error(error.response?.data?.message || `批量${action}失败`)
    }
  }).catch(() => {})
}
</script>

<style scoped>
.admin-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
}

.search-container {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.search-input {
  width: 300px;
}

.batch-actions {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
