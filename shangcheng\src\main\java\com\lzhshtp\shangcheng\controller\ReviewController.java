package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ReviewService;
import com.lzhshtp.shangcheng.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评价管理控制器
 */
@RestController
@RequestMapping("/api/reviews")
public class ReviewController {

    private static final Logger logger = LoggerFactory.getLogger(ReviewController.class);

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private UserService userService;

    /**
     * 提交卖家评价
     */
    @PostMapping("/submit")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<ReviewResponse> submitReview(@Valid @RequestBody ReviewRequest reviewRequest) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            ReviewResponse response = reviewService.submitSellerReview(user.getUserId(), reviewRequest);
            return ApiResponse.success("评价提交成功", response);

        } catch (Exception e) {
            logger.error("提交评价失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 检查订单是否可以评价
     */
    @GetMapping("/can-review/{orderId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> canReviewOrder(@PathVariable Long orderId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            boolean canReview = reviewService.canReviewOrder(orderId, user.getUserId());
            return ApiResponse.success("查询成功", canReview);

        } catch (Exception e) {
            logger.error("检查评价权限失败", e);
            return ApiResponse.fail("检查失败");
        }
    }

    /**
     * 检查订单是否已评价
     */
    @GetMapping("/has-reviewed/{orderId}")
    public ApiResponse<Boolean> hasReviewed(@PathVariable Long orderId) {
        try {
            boolean hasReviewed = reviewService.hasReviewed(orderId);
            return ApiResponse.success("查询成功", hasReviewed);

        } catch (Exception e) {
            logger.error("检查评价状态失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 获取订单评价信息
     */
    @GetMapping("/order/{orderId}")
    public ApiResponse<ReviewResponse> getOrderReview(@PathVariable Long orderId) {
        try {
            ReviewResponse review = reviewService.getOrderReview(orderId);
            if (review == null) {
                return ApiResponse.fail("该订单尚未评价");
            }
            return ApiResponse.success("查询成功", review);

        } catch (Exception e) {
            logger.error("获取订单评价失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 获取卖家信用信息
     */
    @GetMapping("/seller/{sellerId}/credit")
    public ApiResponse<SellerCreditInfo> getSellerCreditInfo(@PathVariable Long sellerId) {
        try {
            SellerCreditInfo creditInfo = reviewService.getSellerCreditInfo(sellerId);
            if (creditInfo == null) {
                return ApiResponse.fail("卖家不存在");
            }
            return ApiResponse.success("查询成功", creditInfo);

        } catch (Exception e) {
            logger.error("获取卖家信用信息失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 获取卖家评价列表
     */
    @GetMapping("/seller/{sellerId}")
    public ApiResponse<List<ReviewResponse>> getSellerReviews(
            @PathVariable Long sellerId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            List<ReviewResponse> reviews = reviewService.getSellerReviews(sellerId, page, pageSize);
            return ApiResponse.success("查询成功", reviews);

        } catch (Exception e) {
            logger.error("获取卖家评价列表失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 获取买家评价历史
     */
    @GetMapping("/buyer/my-reviews")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<ReviewResponse>> getBuyerReviews(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            List<ReviewResponse> reviews = reviewService.getBuyerReviews(user.getUserId(), page, pageSize);
            return ApiResponse.success("查询成功", reviews);

        } catch (Exception e) {
            logger.error("获取买家评价历史失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 申请评价申诉
     */
    @PostMapping("/{reviewId}/appeal")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> appealReview(
            @PathVariable Long reviewId,
            @RequestParam String reason,
            @RequestParam(required = false) String evidence) {
        try {
            boolean success = reviewService.appealReview(reviewId, reason, evidence);
            if (success) {
                return ApiResponse.success("申诉提交成功", true);
            } else {
                return ApiResponse.fail("申诉提交失败");
            }

        } catch (Exception e) {
            logger.error("申请评价申诉失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员处理评价申诉
     */
    @PostMapping("/{reviewId}/handle-appeal")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> handleReviewAppeal(
            @PathVariable Long reviewId,
            @RequestParam boolean approved,
            @RequestParam String adminNote) {
        try {
            boolean success = reviewService.handleReviewAppeal(reviewId, approved, adminNote);
            if (success) {
                return ApiResponse.success("申诉处理成功", true);
            } else {
                return ApiResponse.fail("申诉处理失败");
            }

        } catch (Exception e) {
            logger.error("处理评价申诉失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 获取评价统计信息（管理员用）
     */
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<ReviewStatistics> getReviewStatistics(
            @RequestParam(defaultValue = "30") int days) {
        try {
            ReviewStatistics statistics = reviewService.getReviewStatistics(days);
            return ApiResponse.success("查询成功", statistics);

        } catch (Exception e) {
            logger.error("获取评价统计失败", e);
            return ApiResponse.fail("查询失败");
        }
    }

    /**
     * 检测异常评价行为（管理员用）
     */
    @GetMapping("/admin/detect-abnormal/{sellerId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> detectAbnormalReviews(
            @PathVariable Long sellerId,
            @RequestParam(defaultValue = "7") int days) {
        try {
            boolean hasAbnormal = reviewService.detectAbnormalReviews(sellerId, days);
            return ApiResponse.success("检测完成", hasAbnormal);

        } catch (Exception e) {
            logger.error("检测异常评价失败", e);
            return ApiResponse.fail("检测失败");
        }
    }
}
