package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.AiSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI会话Mapper接口
 */
@Mapper
public interface AiSessionMapper extends BaseMapper<AiSession> {

    /**
     * 根据用户ID查询会话列表，按创建时间倒序
     *
     * @param creatorId 创建者用户ID
     * @return 会话列表
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_session WHERE zhshtp_creator_id = #{creatorId} ORDER BY zhshtp_created_time DESC")
    List<AiSession> findByCreatorIdOrderByCreatedTimeDesc(@Param("creatorId") String creatorId);

    /**
     * 根据用户ID和会话名称查询会话
     *
     * @param creatorId 创建者用户ID
     * @param name      会话名称
     * @return 会话信息
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_session WHERE zhshtp_creator_id = #{creatorId} AND zhshtp_name = #{name}")
    AiSession findByCreatorIdAndName(@Param("creatorId") String creatorId, @Param("name") String name);

    /**
     * 统计用户的会话数量
     *
     * @param creatorId 创建者用户ID
     * @return 会话数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_ai_session WHERE zhshtp_creator_id = #{creatorId}")
    int countByCreatorId(@Param("creatorId") String creatorId);
}
