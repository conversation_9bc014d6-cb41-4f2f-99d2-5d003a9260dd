{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/tiddlywiki.js"], "sourcesContent": ["// Tokenizer\nvar textwords = {};\n\nvar keywords = {\n  \"allTags\": true, \"closeAll\": true, \"list\": true,\n  \"newJournal\": true, \"newTiddler\": true,\n  \"permaview\": true, \"saveChanges\": true,\n  \"search\": true, \"slider\": true, \"tabs\": true,\n  \"tag\": true, \"tagging\": true, \"tags\": true,\n  \"tiddler\": true, \"timeline\": true,\n  \"today\": true, \"version\": true, \"option\": true,\n  \"with\": true, \"filter\": true\n};\n\nvar isSpaceName = /[\\w_\\-]/i,\n    reHR = /^\\-\\-\\-\\-+$/,                                 // <hr>\n    reWikiCommentStart = /^\\/\\*\\*\\*$/,            // /***\n    reWikiCommentStop = /^\\*\\*\\*\\/$/,             // ***/\n    reBlockQuote = /^<<<$/,\n\n    reJsCodeStart = /^\\/\\/\\{\\{\\{$/,                       // //{{{ js block start\n    reJsCodeStop = /^\\/\\/\\}\\}\\}$/,                        // //}}} js stop\n    reXmlCodeStart = /^<!--\\{\\{\\{-->$/,           // xml block start\n    reXmlCodeStop = /^<!--\\}\\}\\}-->$/,            // xml stop\n\n    reCodeBlockStart = /^\\{\\{\\{$/,                        // {{{ TW text div block start\n    reCodeBlockStop = /^\\}\\}\\}$/,                 // }}} TW text stop\n\n    reUntilCodeStop = /.*?\\}\\}\\}/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  var sol = stream.sol(), ch = stream.peek();\n\n  state.block = false;        // indicates the start of a code block.\n\n  // check start of  blocks\n  if (sol && /[<\\/\\*{}\\-]/.test(ch)) {\n    if (stream.match(reCodeBlockStart)) {\n      state.block = true;\n      return chain(stream, state, twTokenCode);\n    }\n    if (stream.match(reBlockQuote))\n      return 'quote';\n    if (stream.match(reWikiCommentStart) || stream.match(reWikiCommentStop))\n      return 'comment';\n    if (stream.match(reJsCodeStart) || stream.match(reJsCodeStop) || stream.match(reXmlCodeStart) || stream.match(reXmlCodeStop))\n      return 'comment';\n    if (stream.match(reHR))\n      return 'contentSeparator';\n  }\n\n  stream.next();\n  if (sol && /[\\/\\*!#;:>|]/.test(ch)) {\n    if (ch == \"!\") { // tw header\n      stream.skipToEnd();\n      return \"header\";\n    }\n    if (ch == \"*\") { // tw list\n      stream.eatWhile('*');\n      return \"comment\";\n    }\n    if (ch == \"#\") { // tw numbered list\n      stream.eatWhile('#');\n      return \"comment\";\n    }\n    if (ch == \";\") { // definition list, term\n      stream.eatWhile(';');\n      return \"comment\";\n    }\n    if (ch == \":\") { // definition list, description\n      stream.eatWhile(':');\n      return \"comment\";\n    }\n    if (ch == \">\") { // single line quote\n      stream.eatWhile(\">\");\n      return \"quote\";\n    }\n    if (ch == '|')\n      return 'header';\n  }\n\n  if (ch == '{' && stream.match('{{'))\n    return chain(stream, state, twTokenCode);\n\n  // rudimentary html:// file:// link matching. TW knows much more ...\n  if (/[hf]/i.test(ch) &&\n      /[ti]/i.test(stream.peek()) &&\n      stream.match(/\\b(ttps?|tp|ile):\\/\\/[\\-A-Z0-9+&@#\\/%?=~_|$!:,.;]*[A-Z0-9+&@#\\/%=~_|$]/i))\n    return \"link\";\n\n  // just a little string indicator, don't want to have the whole string covered\n  if (ch == '\"')\n    return 'string';\n\n  if (ch == '~')    // _no_ CamelCase indicator should be bold\n    return 'brace';\n\n  if (/[\\[\\]]/.test(ch) && stream.match(ch)) // check for [[..]]\n    return 'brace';\n\n  if (ch == \"@\") {    // check for space link. TODO fix @@...@@ highlighting\n    stream.eatWhile(isSpaceName);\n    return \"link\";\n  }\n\n  if (/\\d/.test(ch)) {        // numbers\n    stream.eatWhile(/\\d/);\n    return \"number\";\n  }\n\n  if (ch == \"/\") { // tw invisible comment\n    if (stream.eat(\"%\")) {\n      return chain(stream, state, twTokenComment);\n    } else if (stream.eat(\"/\")) { //\n      return chain(stream, state, twTokenEm);\n    }\n  }\n\n  if (ch == \"_\" && stream.eat(\"_\")) // tw underline\n    return chain(stream, state, twTokenUnderline);\n\n  // strikethrough and mdash handling\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    // if strikethrough looks ugly, change CSS.\n    if (stream.peek() != ' ')\n      return chain(stream, state, twTokenStrike);\n    // mdash\n    if (stream.peek() == ' ')\n      return 'brace';\n  }\n\n  if (ch == \"'\" && stream.eat(\"'\")) // tw bold\n    return chain(stream, state, twTokenStrong);\n\n  if (ch == \"<\" && stream.eat(\"<\")) // tw macro\n    return chain(stream, state, twTokenMacro);\n\n  // core macro handling\n  stream.eatWhile(/[\\w\\$_]/);\n  return textwords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// tw invisible comment\nfunction twTokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"%\");\n  }\n  return \"comment\";\n}\n\n// tw strong / bold\nfunction twTokenStrong(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"'\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"'\");\n  }\n  return \"strong\";\n}\n\n// tw code\nfunction twTokenCode(stream, state) {\n  var sb = state.block;\n\n  if (sb && stream.current()) {\n    return \"comment\";\n  }\n\n  if (!sb && stream.match(reUntilCodeStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  if (sb && stream.sol() && stream.match(reCodeBlockStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  stream.next();\n  return \"comment\";\n}\n\n// tw em / italic\nfunction twTokenEm(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"/\");\n  }\n  return \"emphasis\";\n}\n\n// tw underlined text\nfunction twTokenUnderline(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"_\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"_\");\n  }\n  return \"link\";\n}\n\n// tw strike through text looks ugly\n// change CSS if needed\nfunction twTokenStrike(stream, state) {\n  var maybeEnd = false, ch;\n\n  while (ch = stream.next()) {\n    if (ch == \"-\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"-\");\n  }\n  return \"deleted\";\n}\n\n// macro\nfunction twTokenMacro(stream, state) {\n  if (stream.current() == '<<') {\n    return 'meta';\n  }\n\n  var ch = stream.next();\n  if (!ch) {\n    state.tokenize = tokenBase;\n    return null;\n  }\n  if (ch == \">\") {\n    if (stream.peek() == '>') {\n      stream.next();\n      state.tokenize = tokenBase;\n      return \"meta\";\n    }\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  return keywords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// Interface\nexport const tiddlyWiki = {\n  name: \"tiddlywiki\",\n\n  startState: function () {\n    return {tokenize: tokenBase};\n  },\n\n  token: function (stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  }\n};\n\n"], "mappings": ";;;AACA,IAAI,YAAY,CAAC;AAEjB,IAAI,WAAW;AAAA,EACb,WAAW;AAAA,EAAM,YAAY;AAAA,EAAM,QAAQ;AAAA,EAC3C,cAAc;AAAA,EAAM,cAAc;AAAA,EAClC,aAAa;AAAA,EAAM,eAAe;AAAA,EAClC,UAAU;AAAA,EAAM,UAAU;AAAA,EAAM,QAAQ;AAAA,EACxC,OAAO;AAAA,EAAM,WAAW;AAAA,EAAM,QAAQ;AAAA,EACtC,WAAW;AAAA,EAAM,YAAY;AAAA,EAC7B,SAAS;AAAA,EAAM,WAAW;AAAA,EAAM,UAAU;AAAA,EAC1C,QAAQ;AAAA,EAAM,UAAU;AAC1B;AAEA,IAAI,cAAc;AAAlB,IACI,OAAO;AADX,IAEI,qBAAqB;AAFzB,IAGI,oBAAoB;AAHxB,IAII,eAAe;AAJnB,IAMI,gBAAgB;AANpB,IAOI,eAAe;AAPnB,IAQI,iBAAiB;AARrB,IASI,gBAAgB;AATpB,IAWI,mBAAmB;AAXvB,IAYI,kBAAkB;AAZtB,IAcI,kBAAkB;AAEtB,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,MAAM,OAAO,IAAI,GAAG,KAAK,OAAO,KAAK;AAEzC,QAAM,QAAQ;AAGd,MAAI,OAAO,cAAc,KAAK,EAAE,GAAG;AACjC,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,YAAM,QAAQ;AACd,aAAO,MAAM,QAAQ,OAAO,WAAW;AAAA,IACzC;AACA,QAAI,OAAO,MAAM,YAAY;AAC3B,aAAO;AACT,QAAI,OAAO,MAAM,kBAAkB,KAAK,OAAO,MAAM,iBAAiB;AACpE,aAAO;AACT,QAAI,OAAO,MAAM,aAAa,KAAK,OAAO,MAAM,YAAY,KAAK,OAAO,MAAM,cAAc,KAAK,OAAO,MAAM,aAAa;AACzH,aAAO;AACT,QAAI,OAAO,MAAM,IAAI;AACnB,aAAO;AAAA,EACX;AAEA,SAAO,KAAK;AACZ,MAAI,OAAO,eAAe,KAAK,EAAE,GAAG;AAClC,QAAI,MAAM,KAAK;AACb,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AACR,aAAO;AAAA,EACX;AAEA,MAAI,MAAM,OAAO,OAAO,MAAM,IAAI;AAChC,WAAO,MAAM,QAAQ,OAAO,WAAW;AAGzC,MAAI,QAAQ,KAAK,EAAE,KACf,QAAQ,KAAK,OAAO,KAAK,CAAC,KAC1B,OAAO,MAAM,yEAAyE;AACxF,WAAO;AAGT,MAAI,MAAM;AACR,WAAO;AAET,MAAI,MAAM;AACR,WAAO;AAET,MAAI,SAAS,KAAK,EAAE,KAAK,OAAO,MAAM,EAAE;AACtC,WAAO;AAET,MAAI,MAAM,KAAK;AACb,WAAO,SAAS,WAAW;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,MAAM,QAAQ,OAAO,cAAc;AAAA,IAC5C,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,MAAM,QAAQ,OAAO,SAAS;AAAA,IACvC;AAAA,EACF;AAEA,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG;AAC7B,WAAO,MAAM,QAAQ,OAAO,gBAAgB;AAG9C,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAEhC,QAAI,OAAO,KAAK,KAAK;AACnB,aAAO,MAAM,QAAQ,OAAO,aAAa;AAE3C,QAAI,OAAO,KAAK,KAAK;AACnB,aAAO;AAAA,EACX;AAEA,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG;AAC7B,WAAO,MAAM,QAAQ,OAAO,aAAa;AAE3C,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG;AAC7B,WAAO,MAAM,QAAQ,OAAO,YAAY;AAG1C,SAAO,SAAS,SAAS;AACzB,SAAO,UAAU,qBAAqB,OAAO,QAAQ,CAAC,IAAI,YAAY;AACxE;AAGA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAGA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,OACX;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAGA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,KAAK,MAAM;AAEf,MAAI,MAAM,OAAO,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,OAAO,MAAM,eAAe,GAAG;AACxC,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,IAAI,KAAK,OAAO,MAAM,eAAe,GAAG;AACvD,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,KAAK;AACZ,SAAO;AACT;AAGA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,WAAW,OACX;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAGA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,WAAW,OACX;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAIA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,OAAO;AAEtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAGA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,OAAO,QAAQ,KAAK,MAAM;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,CAAC,IAAI;AACP,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB,aAAO,KAAK;AACZ,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,SAAS,SAAS;AACzB,SAAO,SAAS,qBAAqB,OAAO,QAAQ,CAAC,IAAI,YAAY;AACvE;AAGO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EAEN,YAAY,WAAY;AACtB,WAAO,EAAC,UAAU,UAAS;AAAA,EAC7B;AAAA,EAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,WAAO;AAAA,EACT;AACF;", "names": []}