package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.BatchProductIdsRequest;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductCreateRequest;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.utils.OssUtil;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    private static final Logger logger = LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private ProductService productService;
    
    @Autowired
    private OssUtil ossUtil;
    
    @Autowired
    private UserService userService;
    
    /**
     * 分页查询商品列表
     */
    @GetMapping
    public ApiResponse<PageResult<ProductDTO>> getProducts(ProductQueryParams params) {
        // 获取当前登录用户（如果已登录）
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            if (currentUser != null) {
                // 设置排除当前用户的商品
                params.setExcludeSellerId(currentUser.getUserId());
            }
        }
        
        PageResult<ProductDTO> result = productService.getProductsByCondition(params);
        return ApiResponse.success(result);
    }
    
    /**
     * 获取当前用户发布的商品列表
     */
    @GetMapping("/user")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<PageResult<ProductDTO>> getUserProducts(ProductQueryParams params) {
        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        
        // 设置查询参数
        if (params == null) {
            params = new ProductQueryParams();
        }
        params.setSellerId(user.getUserId());
        
        PageResult<ProductDTO> result = productService.getProductsByCondition(params);
        return ApiResponse.success(result);
    }
    
    /**
     * 搜索商品
     */
    @GetMapping("/search")
    public ApiResponse<PageResult<ProductDTO>> searchProducts(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String condition,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDirection) {
        
        ProductQueryParams params = new ProductQueryParams();
        params.setKeyword(keyword);
        params.setCategoryId(categoryId);
        params.setCondition(condition);
        params.setPage(page);
        params.setPageSize(pageSize);
        params.setSortBy(sortBy);
        params.setSortDirection(sortDirection);
        params.setStatus(Product.ProductStatus.AVAILABLE);  // 只查询可用商品
        
        // 获取当前登录用户（如果已登录）
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            if (currentUser != null) {
                // 设置排除当前用户的商品
                params.setExcludeSellerId(currentUser.getUserId());
            }
        }
        
        PageResult<ProductDTO> result = productService.getProductsByCondition(params);
        return ApiResponse.success(result);
    }
    
    /**
     * 获取商品详情
     */
    @GetMapping("/{id}")
    public ApiResponse<ProductDTO> getProductById(@PathVariable("id") Long id) {
        ProductDTO product = productService.getProductById(id);
        if (product == null) {
            return ApiResponse.fail("商品不存在", 404);
        }
        return ApiResponse.success(product);
    }
    
    /**
     * 创建商品
     * 商品创建后会自动进入审核流程，状态为 PENDING_REVIEW
     */
    @PostMapping
    @PreAuthorize("isAuthenticated() and @userService.canPublishProduct(authentication.name)")
    public ApiResponse<Long> createProduct(@Valid @RequestBody ProductDTO productDTO) {
        Long productId = productService.createProduct(productDTO);
        return ApiResponse.success("商品创建成功，已提交审核", productId);
    }
    
    /**
     * 上传商品（带图片）
     */
    @PostMapping("/upload")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> uploadProduct(@Valid ProductCreateRequest request) {
        
        logger.info("开始处理商品上传请求");
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            // 获取用户ID
            User user = userService.findByUsername(username);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }

            // 检查用户是否可以发布商品
            if (!userService.canPublishProduct(username)) {
                return ApiResponse.fail("您暂时无法发布商品，请联系客服");
            }
            
            // 上传图片到OSS
            String imageUrl = null;
            if (request.getImage() != null && !request.getImage().isEmpty()) {
                try {
                    imageUrl = ossUtil.uploadFile(request.getImage(), "products");
                    logger.info("商品图片上传成功，URL: {}", imageUrl);
                } catch (IOException e) {
                    logger.error("商品图片上传失败", e);
                    return ApiResponse.fail("商品图片上传失败: " + e.getMessage());
                }
            } else {
                return ApiResponse.fail("商品图片不能为空");
            }
            
            // 创建商品DTO
            ProductDTO productDTO = new ProductDTO();
            productDTO.setTitle(request.getTitle());
            productDTO.setDescription(request.getDescription());
            productDTO.setPrice(new BigDecimal(request.getPrice().toString()));
            productDTO.setCategoryId(request.getCategoryId());
            productDTO.setCondition(request.getCondition());
            productDTO.setLocation(request.getLocation());
            productDTO.setDeliveryMethod(request.getDeliveryMethod());
            productDTO.setImageUrls(List.of(imageUrl));
            productDTO.setSellerId(user.getUserId());

            // 设置验货相关字段
            productDTO.setSupportOfficialVerification(request.getSupportOfficialVerification() != null ? request.getSupportOfficialVerification() : false);
            if (request.getVerificationFee() != null) {
                productDTO.setVerificationFee(new BigDecimal(request.getVerificationFee().toString()));
            } else {
                productDTO.setVerificationFee(BigDecimal.ZERO);
            }
            
            // 保存商品
            Long productId = productService.createProduct(productDTO);

            return ApiResponse.success("商品上传成功，已提交审核", productId);
        } catch (Exception e) {
            logger.error("商品上传失败", e);
            return ApiResponse.fail("商品上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新商品
     */
    @PutMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> updateProduct(
            @PathVariable("id") Long id,
            @Valid @RequestBody ProductDTO productDTO) {
        
        boolean success = productService.updateProduct(id, productDTO);
        if (!success) {
            return ApiResponse.fail("商品不存在或无权修改", 404);
        }
        return ApiResponse.success("商品更新成功", null);
    }
    
    /**
     * 删除商品
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> deleteProduct(@PathVariable("id") Long id) {
        boolean success = productService.deleteProduct(id);
        if (!success) {
            return ApiResponse.fail("商品不存在或无权删除", 404);
        }
        return ApiResponse.success("商品删除成功", null);
    }
    
    /**
     * 更新商品状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> updateProductStatus(
            @PathVariable("id") Long id,
            @RequestParam String status) {
        
        boolean success = productService.updateProductStatus(id, status);
        if (!success) {
            return ApiResponse.fail("商品不存在或无权修改", 404);
        }
        return ApiResponse.success("商品状态更新成功", null);
    }
    
    /**
     * 获取推荐商品（随机获取8个可用商品）
     */
    @GetMapping("/recommended")
    public ApiResponse<List<ProductDTO>> getRecommendedProducts(
            @RequestParam(required = false) Long excludeProductId,
            @RequestParam(required = false, defaultValue = "8") Integer limit) {
        
        // 设置查询参数
        ProductQueryParams params = new ProductQueryParams();
        params.setStatus(Product.ProductStatus.AVAILABLE);  // 只查询可用商品
        params.setPage(1);
        params.setPageSize(limit);
        params.setSortBy("random");  // 随机排序
        
        // 获取当前登录用户（如果已登录），排除自己的商品
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            if (currentUser != null) {
                params.setExcludeSellerId(currentUser.getUserId());
            }
        }
        
        // 获取随机商品
        PageResult<ProductDTO> result = productService.getProductsByCondition(params);
        List<ProductDTO> recommendedProducts = result.getRecords();
        
        // 如果指定了排除的商品ID，过滤掉该商品
        if (excludeProductId != null) {
            recommendedProducts = recommendedProducts.stream()
                .filter(product -> !product.getId().equals(excludeProductId))
                .toList();
        }
        
        return ApiResponse.success(recommendedProducts);
    }
    
    /**
     * 获取指定卖家的商品列表
     * 
     * @param sellerId 卖家ID
     * @param page 页码
     * @param pageSize 每页数量
     * @param status 商品状态（可选）
     * @param sortBy 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return 商品列表
     */
    @GetMapping("/seller/{sellerId}")
    public ApiResponse<PageResult<ProductDTO>> getSellerProducts(
            @PathVariable Long sellerId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "12") Integer pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDirection) {
        
        // 检查卖家是否存在
        User seller = userService.findById(sellerId);
        if (seller == null) {
            return ApiResponse.fail("卖家不存在");
        }
        
        // 设置查询参数
        ProductQueryParams params = new ProductQueryParams();
        params.setSellerId(sellerId);
        params.setPage(page);
        params.setPageSize(pageSize);
        
        // 设置排序参数
        if (sortBy != null && !sortBy.isEmpty()) {
            params.setSortBy(sortBy);
            if (sortDirection != null && !sortDirection.isEmpty()) {
                params.setSortDirection(sortDirection);
            }
        }
        
        // 如果指定了状态，则设置状态过滤
        if (status != null && !status.isEmpty()) {
            // 直接设置状态字符串，由service层处理具体逻辑
            params.setStatus(status.toUpperCase());
        } else {
            // 默认只返回可用商品
            params.setStatus("AVAILABLE");
        }
        
        PageResult<ProductDTO> result = productService.getProductsByCondition(params);
        return ApiResponse.success(result);
    }
    
    /**
     * 更新商品图片
     */
    @PutMapping("/{id}/image")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<String> updateProductImage(
            @PathVariable("id") Long id,
            @RequestParam("image") MultipartFile image) {
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            
            // 获取用户ID
            User user = userService.findByUsername(username);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }
            
            // 检查商品是否存在并且属于当前用户
            ProductDTO product = productService.getProductById(id);
            if (product == null) {
                return ApiResponse.fail("商品不存在", 404);
            }
            
            if (!product.getSellerId().equals(user.getUserId())) {
                return ApiResponse.fail("无权修改该商品", 403);
            }
            
            // 上传图片到OSS
            if (image != null && !image.isEmpty()) {
                try {
                    String imageUrl = ossUtil.uploadFile(image, "products");
                    logger.info("商品图片更新成功，URL: {}", imageUrl);
                    
                    // 更新商品图片URL
                    boolean success = productService.updateProductImage(id, imageUrl);
                    if (!success) {
                        // 如果更新失败，删除刚刚上传的新图片
                        try {
                            ossUtil.deleteFile(imageUrl);
                        } catch (Exception e) {
                            logger.error("删除新上传图片失败", e);
                        }
                        return ApiResponse.fail("更新商品图片失败");
                    }
                    
                    return ApiResponse.success("商品图片更新成功", imageUrl);
                } catch (IOException e) {
                    logger.error("商品图片上传失败", e);
                    return ApiResponse.fail("商品图片上传失败: " + e.getMessage());
                }
            } else {
                return ApiResponse.fail("商品图片不能为空");
            }
        } catch (Exception e) {
            logger.error("更新商品图片失败", e);
            return ApiResponse.fail("更新商品图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理员审核商品
     * @param id 商品ID
     * @param approved 是否通过审核
     * @return 审核结果
     */
    @PutMapping("/{id}/review")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Void> reviewProduct(
            @PathVariable("id") Long id,
            @RequestParam Boolean approved) {
        
        // 获取商品信息
        ProductDTO product = productService.getProductById(id);
        if (product == null) {
            return ApiResponse.fail("商品不存在", 404);
        }
        
        // 只有待审核的商品才能进行审核操作
        if (!Product.ProductStatus.PENDING_REVIEW.equals(product.getStatus())) {
            return ApiResponse.fail("只能审核待审核状态的商品", 400);
        }
        
        boolean success;
        if (approved) {
            // 审核通过，将商品状态改为可销售
            success = productService.updateProductStatus(id, Product.ProductStatus.AVAILABLE);
            if (success) {
                return ApiResponse.success("商品审核通过", null);
            } else {
                return ApiResponse.fail("审核操作失败", 500);
            }
        } else {
            // 审核不通过，将商品状态改为管理员下架
            success = productService.updateProductStatus(id, Product.ProductStatus.OFF_SHELF_BY_ADMIN);
            if (success) {
                return ApiResponse.success("商品审核不通过，已下架", null);
            } else {
                return ApiResponse.fail("审核操作失败", 500);
            }
        }
    }

    /**
     * 管理员批量删除商品
     * @param request 包含商品ID列表的请求
     * @return 操作结果和成功删除的商品数量
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Map<String, Object>> batchDeleteProducts(@Valid @RequestBody BatchProductIdsRequest request) {
        logger.info("管理员批量删除商品，商品ID列表: {}", request.getProductIds());
        
        try {
            // 获取当前管理员信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String adminUsername = authentication.getName();
            logger.info("管理员 {} 执行批量删除商品操作", adminUsername);
            
            // 执行批量删除
            Map<String, Object> result = productService.batchDeleteProducts(request.getProductIds());
            
            return ApiResponse.success("批量删除商品操作完成", result);
        } catch (Exception e) {
            logger.error("批量删除商品失败", e);
            return ApiResponse.fail("批量删除商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理员批量上架商品
     * @param request 包含商品ID列表的请求
     * @return 操作结果和成功上架的商品数量
     */
    @PutMapping("/batch/approve")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Map<String, Object>> batchApproveProducts(@Valid @RequestBody BatchProductIdsRequest request) {
        logger.info("管理员批量上架商品，商品ID列表: {}", request.getProductIds());
        
        try {
            // 获取当前管理员信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String adminUsername = authentication.getName();
            logger.info("管理员 {} 执行批量上架商品操作", adminUsername);
            
            // 执行批量上架
            Map<String, Object> result = productService.batchApproveProducts(request.getProductIds());
            
            return ApiResponse.success("批量上架商品操作完成", result);
        } catch (Exception e) {
            logger.error("批量上架商品失败", e);
            return ApiResponse.fail("批量上架商品失败: " + e.getMessage());
        }
    }
} 