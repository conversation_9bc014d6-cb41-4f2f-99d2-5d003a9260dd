import request from '@/utils/request';

// 关注用户
export function followUser(userId) {
  return request({
    url: `/follows/${userId}`,
    method: 'post'
  });
}

// 取消关注用户
export function unfollowUser(userId) {
  return request({
    url: `/follows/${userId}`,
    method: 'delete'
  });
}

// 检查是否已关注用户
export function checkFollowing(userId) {
  return request({
    url: `/follows/check/${userId}`,
    method: 'get'
  });
}

// 获取用户的关注数量
export function getFollowingsCount(userId) {
  return request({
    url: `/follows/followings/count/${userId}`,
    method: 'get'
  });
}

// 获取用户的粉丝数量
export function getFollowersCount(userId) {
  return request({
    url: `/follows/followers/count/${userId}`,
    method: 'get'
  });
}

// 获取用户的关注列表
export function getUserFollowings(page = 1, pageSize = 10) {
  return request({
    url: '/follows/followings',
    method: 'get',
    params: {
      page,
      pageSize
    }
  });
} 