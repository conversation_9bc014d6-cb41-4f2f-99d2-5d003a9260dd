import request from '@/admin/utils/request'

/**
 * 获取订单超时任务配置
 */
export function getOrderTimeoutConfig() {
  return request({
    url: '/admin/tasks/order-timeout/config',
    method: 'get'
  })
}

/**
 * 更新订单超时任务配置
 * @param {Object} config 配置对象
 */
export function updateOrderTimeoutConfig(config) {
  return request({
    url: '/admin/tasks/order-timeout/config',
    method: 'post',
    data: config
  })
}

/**
 * 切换任务状态
 * @param {String} taskName 任务名称
 * @param {Boolean} enabled 是否启用
 */
export function toggleTask(taskName, enabled) {
  return request({
    url: `/admin/tasks/${taskName}/toggle`,
    method: 'post',
    params: { enabled }
  })
}

/**
 * 手动执行任务
 * @param {String} taskName 任务名称
 */
export function executeTask(taskName) {
  return request({
    url: `/admin/tasks/${taskName}/execute`,
    method: 'post'
  })
}

/**
 * 获取所有任务配置
 */
export function getAllTaskConfigs() {
  return request({
    url: '/admin/tasks/configs',
    method: 'get'
  })
}

/**
 * 获取任务配置详情
 * @param {String} taskName 任务名称
 */
export function getTaskConfig(taskName) {
  return request({
    url: `/admin/tasks/${taskName}/config`,
    method: 'get'
  })
}
