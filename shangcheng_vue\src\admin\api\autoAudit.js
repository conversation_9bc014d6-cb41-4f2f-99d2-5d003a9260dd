import request from '@/utils/request'

/**
 * 自动审核记录API
 */
export const autoAuditApi = {
  /**
   * 获取自动审核记录列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.productId - 商品ID
   * @param {string} params.decision - 审核决策
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   */
  getRecords(params = {}) {
    return request.get('/admin/audit/auto/records', { params })
  },

  /**
   * 获取自动审核记录详情
   * @param {number} recordId - 记录ID
   */
  getRecordDetail(recordId) {
    return request.get(`/admin/audit/auto/records/${recordId}`)
  },

  /**
   * 获取自动审核统计数据
   * @param {Object} params - 查询参数
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   */
  getStats(params = {}) {
    return request.get('/admin/audit/auto/stats', { params })
  },

  /**
   * 重新执行自动审核
   * @param {number} productId - 商品ID
   */
  reauditProduct(productId) {
    return request.post(`/admin/audit/auto/records/${productId}/reaudit`)
  }
}

export default autoAuditApi
