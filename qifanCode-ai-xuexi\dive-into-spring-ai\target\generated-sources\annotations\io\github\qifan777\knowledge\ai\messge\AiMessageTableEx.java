package io.github.qifan777.knowledge.ai.messge;

import io.github.qifan777.knowledge.ai.session.AiSessionTableEx;
import io.github.qifan777.knowledge.user.UserTableEx;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = AiMessage.class
)
public class AiMessageTableEx extends AiMessageTable implements TableEx<AiMessage> {
    public static final AiMessageTableEx $ = new AiMessageTableEx(AiMessageTable.$, null);

    public AiMessageTableEx() {
        super();
    }

    public AiMessageTableEx(AbstractTypedTable.DelayedOperation<AiMessage> delayedOperation) {
        super(delayedOperation);
    }

    public AiMessageTableEx(TableImplementor<AiMessage> table) {
        super(table);
    }

    protected AiMessageTableEx(AiMessageTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx editor() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiMessageProps.EDITOR.unwrap()));
        }
        return new UserTableEx(joinOperation(AiMessageProps.EDITOR.unwrap()));
    }

    public UserTableEx editor(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiMessageProps.EDITOR.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(AiMessageProps.EDITOR.unwrap(), joinType));
    }

    public UserTableEx creator() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiMessageProps.CREATOR.unwrap()));
        }
        return new UserTableEx(joinOperation(AiMessageProps.CREATOR.unwrap()));
    }

    public UserTableEx creator(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiMessageProps.CREATOR.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(AiMessageProps.CREATOR.unwrap(), joinType));
    }

    public AiSessionTableEx session() {
        __beforeJoin();
        if (raw != null) {
            return new AiSessionTableEx(raw.joinImplementor(AiMessageProps.SESSION.unwrap()));
        }
        return new AiSessionTableEx(joinOperation(AiMessageProps.SESSION.unwrap()));
    }

    public AiSessionTableEx session(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new AiSessionTableEx(raw.joinImplementor(AiMessageProps.SESSION.unwrap(), joinType));
        }
        return new AiSessionTableEx(joinOperation(AiMessageProps.SESSION.unwrap(), joinType));
    }

    @Override
    public AiMessageTableEx asTableEx() {
        return this;
    }

    @Override
    public AiMessageTableEx __disableJoin(String reason) {
        return new AiMessageTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<AiMessageTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<AiMessageTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
