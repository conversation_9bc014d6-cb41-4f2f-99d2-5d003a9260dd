package io.github.qifan777.knowledge;

import io.github.qifan777.knowledge.ai.messge.AiMessage;
import io.github.qifan777.knowledge.ai.messge.AiMessageDraft;
import io.github.qifan777.knowledge.ai.session.AiSession;
import io.github.qifan777.knowledge.ai.session.AiSessionDraft;
import io.github.qifan777.knowledge.user.User;
import io.github.qifan777.knowledge.user.UserDraft;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Objects {
    static AiMessage createAiMessage(DraftConsumer<AiMessageDraft> block) {
        return AiMessageDraft.$.produce(block);
    }

    static AiMessage createAiMessage(AiMessage base, DraftConsumer<AiMessageDraft> block) {
        return AiMessageDraft.$.produce(base, block);
    }

    static AiSession createAiSession(DraftConsumer<AiSessionDraft> block) {
        return AiSessionDraft.$.produce(block);
    }

    static AiSession createAiSession(AiSession base, DraftConsumer<AiSessionDraft> block) {
        return AiSessionDraft.$.produce(base, block);
    }

    static User createUser(DraftConsumer<UserDraft> block) {
        return UserDraft.$.produce(block);
    }

    static User createUser(User base, DraftConsumer<UserDraft> block) {
        return UserDraft.$.produce(base, block);
    }
}
