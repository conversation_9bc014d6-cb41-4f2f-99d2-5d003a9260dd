import request from '@/utils/request'

/**
 * 管理员查询所有订单
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 搜索关键词（订单号、买家用户名、卖家用户名、商品名称）
 * @param {string} params.status - 订单状态筛选
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} - 返回订单列表数据
 */
export function getOrderList(params) {
  return request({
    url: '/orders/admin',
    method: 'get',
    params
  })
}

/**
 * 获取订单状态列表
 * @returns {Array} - 订单状态列表
 */
export function getOrderStatusOptions() {
  return [
    { value: 'pending_payment', label: '待支付' },
    { value: 'paid', label: '已支付' },
    { value: 'shipped', label: '已发货' },
    { value: 'delivered', label: '已送达' },
    { value: 'completed', label: '已完成' },
    { value: 'cancelled', label: '已取消' },
    { value: 'refunded', label: '已退款' }
  ]
}

/**
 * 获取订单状态对应的标签类型
 * @param {string} status - 订单状态
 * @returns {string} - 标签类型
 */
export function getOrderStatusType(status) {
  const statusMap = {
    'pending_payment': 'warning',
    'paid': 'primary',
    'shipped': 'info',
    'delivered': 'info',
    'completed': 'success',
    'cancelled': 'danger',
    'refunded': 'danger'
  }
  return statusMap[status] || 'default'
}
