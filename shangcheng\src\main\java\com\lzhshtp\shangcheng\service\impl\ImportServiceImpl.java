package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.mapper.CategoryMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Category;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ImportService;
import com.lzhshtp.shangcheng.utils.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量导入服务实现类
 */
@Service
public class ImportServiceImpl implements ImportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    @Transactional
    public ImportResultDTO importUsers(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        ImportResultDTO result = new ImportResultDTO();
        result.setImportType("user");
        result.setStartTime(startTimeStr);
        
        List<String> errorMessages = new ArrayList<>();
        List<Long> successIds = new ArrayList<>();
        
        try {
            // 验证文件格式
            if (!ExcelUtil.isValidExcelFile(file)) {
                errorMessages.add("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
                result.setErrorMessages(errorMessages);
                result.setFailCount(1);
                return result;
            }
            
            // 解析Excel文件
            List<UserImportDTO> userList = ExcelUtil.parseUserExcel(file);
            result.setTotalCount(userList.size());
            
            if (userList.isEmpty()) {
                errorMessages.add("Excel文件中没有有效数据");
                result.setErrorMessages(errorMessages);
                return result;
            }
            
            // 批量导入用户
            for (UserImportDTO userImportDTO : userList) {
                try {
                    // 验证必填字段
                    if (userImportDTO.getUsername() == null || userImportDTO.getUsername().trim().isEmpty()) {
                        errorMessages.add(String.format("第%d行：用户名不能为空", userImportDTO.getRowNumber()));
                        continue;
                    }
                    
                    if (userImportDTO.getPassword() == null || userImportDTO.getPassword().trim().isEmpty()) {
                        errorMessages.add(String.format("第%d行：密码不能为空", userImportDTO.getRowNumber()));
                        continue;
                    }
                    
                    if (userImportDTO.getEmail() == null || userImportDTO.getEmail().trim().isEmpty()) {
                        errorMessages.add(String.format("第%d行：邮箱不能为空", userImportDTO.getRowNumber()));
                        continue;
                    }
                    
                    // 检查用户名是否已存在
                    User existingUser = userMapper.findByUsername(userImportDTO.getUsername().trim());
                    if (existingUser != null) {
                        errorMessages.add(String.format("第%d行：用户名'%s'已存在", 
                            userImportDTO.getRowNumber(), userImportDTO.getUsername()));
                        continue;
                    }
                    
                    // 检查邮箱是否已存在
                    User existingEmailUser = userMapper.findByEmail(userImportDTO.getEmail().trim());
                    if (existingEmailUser != null) {
                        errorMessages.add(String.format("第%d行：邮箱'%s'已存在", 
                            userImportDTO.getRowNumber(), userImportDTO.getEmail()));
                        continue;
                    }
                    
                    // 检查手机号是否已存在（如果提供了手机号）
                    if (userImportDTO.getPhoneNumber() != null && !userImportDTO.getPhoneNumber().trim().isEmpty()) {
                        User existingPhoneUser = userMapper.findByPhoneNumber(userImportDTO.getPhoneNumber().trim());
                        if (existingPhoneUser != null) {
                            errorMessages.add(String.format("第%d行：手机号'%s'已存在", 
                                userImportDTO.getRowNumber(), userImportDTO.getPhoneNumber()));
                            continue;
                        }
                    }
                    
                    // 创建用户对象
                    User user = new User();
                    user.setUsername(userImportDTO.getUsername().trim());
                    user.setPasswordHash(passwordEncoder.encode(userImportDTO.getPassword()));
                    user.setEmail(userImportDTO.getEmail().trim());
                    user.setPhoneNumber(userImportDTO.getPhoneNumber() != null ? userImportDTO.getPhoneNumber().trim() : null);
                    user.setAvatarUrl(userImportDTO.getAvatarUrl());
                    user.setBio(userImportDTO.getBio());
                    user.setLocation(userImportDTO.getLocation());
                    user.setRegistrationDate(LocalDateTime.now());
                    user.setCreditScore(userImportDTO.getCreditScore() != null ? userImportDTO.getCreditScore() : 100);
                    user.setIsActive(userImportDTO.getIsActive() != null ? userImportDTO.getIsActive() : true);
                    
                    // 设置用户角色
                    String role = userImportDTO.getRole();
                    if (role == null || role.trim().isEmpty()) {
                        user.setRole("general_user");
                    } else {
                        // 验证角色是否有效
                        String roleValue = role.trim().toLowerCase();
                        if ("admin".equals(roleValue) || "general_user".equals(roleValue) || "ai_customer_service".equals(roleValue)) {
                            user.setRole(roleValue);
                        } else {
                            user.setRole("general_user");
                        }
                    }
                    
                    // 保存用户
                    userMapper.insert(user);
                    successIds.add(user.getUserId());
                    
                    logger.info("成功导入用户：{}", user.getUsername());
                    
                } catch (Exception e) {
                    logger.error("导入第{}行用户失败", userImportDTO.getRowNumber(), e);
                    errorMessages.add(String.format("第%d行：导入失败 - %s", 
                        userImportDTO.getRowNumber(), e.getMessage()));
                }
            }
            
        } catch (IOException e) {
            logger.error("解析Excel文件失败", e);
            errorMessages.add("解析Excel文件失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("批量导入用户失败", e);
            errorMessages.add("批量导入失败：" + e.getMessage());
        }
        
        // 设置结果
        result.setSuccessCount(successIds.size());
        result.setFailCount(result.getTotalCount() - result.getSuccessCount());
        result.setErrorMessages(errorMessages);
        result.setSuccessIds(successIds);
        
        long endTime = System.currentTimeMillis();
        result.setEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setDuration(endTime - startTime);
        
        logger.info("用户批量导入完成，总数：{}，成功：{}，失败：{}",
            result.getTotalCount(), result.getSuccessCount(), result.getFailCount());

        return result;
    }

    @Override
    @Transactional
    public ImportResultDTO importCategories(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        ImportResultDTO result = new ImportResultDTO();
        result.setImportType("category");
        result.setStartTime(startTimeStr);

        List<String> errorMessages = new ArrayList<>();
        List<Long> successIds = new ArrayList<>();

        try {
            // 验证文件格式
            if (!ExcelUtil.isValidExcelFile(file)) {
                errorMessages.add("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
                result.setErrorMessages(errorMessages);
                result.setFailCount(1);
                return result;
            }

            // 解析Excel文件
            List<CategoryImportDTO> categoryList = ExcelUtil.parseCategoryExcel(file);
            result.setTotalCount(categoryList.size());

            if (categoryList.isEmpty()) {
                errorMessages.add("Excel文件中没有有效数据");
                result.setErrorMessages(errorMessages);
                return result;
            }

            // 用于存储已创建的分类名称和ID的映射，便于处理父子关系
            Map<String, Integer> categoryNameToIdMap = new HashMap<>();

            // 先获取数据库中已存在的分类
            List<Category> existingCategories = categoryMapper.selectList(null);
            for (Category category : existingCategories) {
                categoryNameToIdMap.put(category.getName(), category.getId());
            }

            // 分两轮处理：第一轮处理顶级分类，第二轮处理子分类
            List<CategoryImportDTO> topLevelCategories = new ArrayList<>();
            List<CategoryImportDTO> subCategories = new ArrayList<>();

            for (CategoryImportDTO categoryImportDTO : categoryList) {
                if (categoryImportDTO.getParentCategoryName() == null ||
                    categoryImportDTO.getParentCategoryName().trim().isEmpty()) {
                    topLevelCategories.add(categoryImportDTO);
                } else {
                    subCategories.add(categoryImportDTO);
                }
            }

            // 第一轮：处理顶级分类
            for (CategoryImportDTO categoryImportDTO : topLevelCategories) {
                try {
                    if (processCategory(categoryImportDTO, null, categoryNameToIdMap, errorMessages, successIds)) {
                        // 成功创建，更新映射
                        Category newCategory = categoryMapper.findByName(categoryImportDTO.getName().trim());
                        if (newCategory != null) {
                            categoryNameToIdMap.put(newCategory.getName(), newCategory.getId());
                        }
                    }
                } catch (Exception e) {
                    logger.error("导入第{}行分类失败", categoryImportDTO.getRowNumber(), e);
                    errorMessages.add(String.format("第%d行：导入失败 - %s",
                        categoryImportDTO.getRowNumber(), e.getMessage()));
                }
            }

            // 第二轮：处理子分类
            for (CategoryImportDTO categoryImportDTO : subCategories) {
                try {
                    String parentName = categoryImportDTO.getParentCategoryName().trim();
                    Integer parentId = categoryNameToIdMap.get(parentName);

                    if (parentId == null) {
                        errorMessages.add(String.format("第%d行：父级分类'%s'不存在",
                            categoryImportDTO.getRowNumber(), parentName));
                        continue;
                    }

                    if (processCategory(categoryImportDTO, parentId, categoryNameToIdMap, errorMessages, successIds)) {
                        // 成功创建，更新映射
                        Category newCategory = categoryMapper.findByName(categoryImportDTO.getName().trim());
                        if (newCategory != null) {
                            categoryNameToIdMap.put(newCategory.getName(), newCategory.getId());
                        }
                    }
                } catch (Exception e) {
                    logger.error("导入第{}行分类失败", categoryImportDTO.getRowNumber(), e);
                    errorMessages.add(String.format("第%d行：导入失败 - %s",
                        categoryImportDTO.getRowNumber(), e.getMessage()));
                }
            }

        } catch (IOException e) {
            logger.error("解析Excel文件失败", e);
            errorMessages.add("解析Excel文件失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("批量导入分类失败", e);
            errorMessages.add("批量导入失败：" + e.getMessage());
        }

        // 设置结果
        result.setSuccessCount(successIds.size());
        result.setFailCount(result.getTotalCount() - result.getSuccessCount());
        result.setErrorMessages(errorMessages);
        result.setSuccessIds(successIds);

        long endTime = System.currentTimeMillis();
        result.setEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setDuration(endTime - startTime);

        logger.info("分类批量导入完成，总数：{}，成功：{}，失败：{}",
            result.getTotalCount(), result.getSuccessCount(), result.getFailCount());

        return result;
    }

    /**
     * 处理单个分类的导入
     */
    private boolean processCategory(CategoryImportDTO categoryImportDTO, Integer parentId,
                                  Map<String, Integer> categoryNameToIdMap,
                                  List<String> errorMessages, List<Long> successIds) {
        // 验证必填字段
        if (categoryImportDTO.getName() == null || categoryImportDTO.getName().trim().isEmpty()) {
            errorMessages.add(String.format("第%d行：分类名称不能为空", categoryImportDTO.getRowNumber()));
            return false;
        }

        // 检查分类名称是否已存在
        if (categoryNameToIdMap.containsKey(categoryImportDTO.getName().trim())) {
            errorMessages.add(String.format("第%d行：分类名称'%s'已存在",
                categoryImportDTO.getRowNumber(), categoryImportDTO.getName()));
            return false;
        }

        // 创建分类对象
        Category category = new Category();
        category.setName(categoryImportDTO.getName().trim());
        category.setDescription(categoryImportDTO.getDescription());
        category.setParentId(parentId);

        // 保存分类
        categoryMapper.insert(category);
        successIds.add(category.getId().longValue());

        logger.info("成功导入分类：{}", category.getName());
        return true;
    }

    @Override
    public byte[] generateUserTemplate() {
        try {
            return ExcelUtil.generateUserTemplate();
        } catch (IOException e) {
            logger.error("生成用户导入模板失败", e);
            throw new RuntimeException("生成用户导入模板失败", e);
        }
    }

    @Override
    public byte[] generateCategoryTemplate() {
        try {
            return ExcelUtil.generateCategoryTemplate();
        } catch (IOException e) {
            logger.error("生成分类导入模板失败", e);
            throw new RuntimeException("生成分类导入模板失败", e);
        }
    }
}
