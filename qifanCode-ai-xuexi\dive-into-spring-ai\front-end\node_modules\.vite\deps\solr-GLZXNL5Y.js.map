{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/solr.js"], "sourcesContent": ["var isStringChar = /[^\\s\\|\\!\\+\\-\\*\\?\\~\\^\\&\\:\\(\\)\\[\\]\\{\\}\\\"\\\\]/;\nvar isOperatorChar = /[\\|\\!\\+\\-\\*\\?\\~\\^\\&]/;\nvar isOperatorString = /^(OR|AND|NOT|TO)$/;\n\nfunction isNumber(word) {\n  return parseFloat(word).toString() === word;\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) break;\n      escaped = !escaped && next == \"\\\\\";\n    }\n\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenOperator(operator) {\n  return function(stream, state) {\n    if (operator == \"|\")\n      stream.eat(/\\|/);\n    else if (operator == \"&\")\n      stream.eat(/\\&/);\n\n    state.tokenize = tokenBase;\n    return \"operator\";\n  };\n}\n\nfunction tokenWord(ch) {\n  return function(stream, state) {\n    var word = ch;\n    while ((ch = stream.peek()) && ch.match(isStringChar) != null) {\n      word += stream.next();\n    }\n\n    state.tokenize = tokenBase;\n    if (isOperatorString.test(word))\n      return \"operator\";\n    else if (isNumber(word))\n      return \"number\";\n    else if (stream.peek() == \":\")\n      return \"propertyName\";\n    else\n      return \"string\";\n  };\n}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"')\n    state.tokenize = tokenString(ch);\n  else if (isOperatorChar.test(ch))\n    state.tokenize = tokenOperator(ch);\n  else if (isStringChar.test(ch))\n    state.tokenize = tokenWord(ch);\n\n  return (state.tokenize != tokenBase) ? state.tokenize(stream, state) : null;\n}\n\nexport const solr = {\n  name: \"solr\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AAEvB,SAAS,SAAS,MAAM;AACtB,SAAO,WAAW,IAAI,EAAE,SAAS,MAAM;AACzC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC;AAAS;AAC/B,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AAEA,QAAI,CAAC;AAAS,YAAM,WAAW;AAC/B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,UAAU;AAC/B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,YAAY;AACd,aAAO,IAAI,IAAI;AAAA,aACR,YAAY;AACnB,aAAO,IAAI,IAAI;AAEjB,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,UAAU,IAAI;AACrB,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO;AACX,YAAQ,KAAK,OAAO,KAAK,MAAM,GAAG,MAAM,YAAY,KAAK,MAAM;AAC7D,cAAQ,OAAO,KAAK;AAAA,IACtB;AAEA,UAAM,WAAW;AACjB,QAAI,iBAAiB,KAAK,IAAI;AAC5B,aAAO;AAAA,aACA,SAAS,IAAI;AACpB,aAAO;AAAA,aACA,OAAO,KAAK,KAAK;AACxB,aAAO;AAAA;AAEP,aAAO;AAAA,EACX;AACF;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM;AACR,UAAM,WAAW,YAAY,EAAE;AAAA,WACxB,eAAe,KAAK,EAAE;AAC7B,UAAM,WAAW,cAAc,EAAE;AAAA,WAC1B,aAAa,KAAK,EAAE;AAC3B,UAAM,WAAW,UAAU,EAAE;AAE/B,SAAQ,MAAM,YAAY,YAAa,MAAM,SAAS,QAAQ,KAAK,IAAI;AACzE;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACF;", "names": []}