package com.lzhshtp.shangcheng.repository.search;

import com.lzhshtp.shangcheng.document.ProductDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品搜索Repository
 * 基于ElasticSearch的商品搜索功能
 */
@Repository
public interface ProductSearchRepository extends ElasticsearchRepository<ProductDocument, Long> {

    /**
     * 根据标题搜索商品
     */
    Page<ProductDocument> findByTitleContaining(String title, Pageable pageable);

    /**
     * 根据标题或描述搜索商品
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title^2\", \"description\"]}}], \"filter\": [{\"term\": {\"status\": 0}}]}}")
    Page<ProductDocument> findByTitleOrDescriptionContaining(String keyword, Pageable pageable);

    /**
     * 根据分类搜索商品
     */
    Page<ProductDocument> findByCategoryIdAndStatus(Integer categoryId, Integer status, Pageable pageable);

    /**
     * 根据价格范围搜索商品
     */
    Page<ProductDocument> findByPriceBetweenAndStatus(BigDecimal minPrice, BigDecimal maxPrice, Integer status, Pageable pageable);

    /**
     * 根据位置搜索商品
     */
    Page<ProductDocument> findByLocationAndStatus(String location, Integer status, Pageable pageable);

    /**
     * 根据卖家搜索商品
     */
    Page<ProductDocument> findBySellerIdAndStatus(Long sellerId, Integer status, Pageable pageable);

    /**
     * 复合搜索 - 关键词 + 分类 + 价格范围
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"title^3\", \"description^1\", \"categoryName^2\"]}}], \"filter\": [{\"term\": {\"status\": 0}}, {\"term\": {\"categoryId\": \"?1\"}}, {\"range\": {\"price\": {\"gte\": \"?2\", \"lte\": \"?3\"}}}]}}")
    Page<ProductDocument> findByKeywordAndCategoryAndPriceRange(String keyword, Integer categoryId, BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);

    /**
     * 获取热门商品（按浏览量排序）
     */
    Page<ProductDocument> findByStatusOrderByViewCountDesc(Integer status, Pageable pageable);

    /**
     * 获取最新商品
     */
    Page<ProductDocument> findByStatusOrderByPostedDateDesc(Integer status, Pageable pageable);

    /**
     * 搜索建议 - 根据标题前缀
     */
    @Query("{\"bool\": {\"must\": [{\"prefix\": {\"title\": \"?0\"}}], \"filter\": [{\"term\": {\"status\": 0}}]}}")
    List<ProductDocument> findSuggestionsByTitlePrefix(String prefix);

    /**
     * 相似商品推荐
     */
    @Query("{\"more_like_this\": {\"fields\": [\"title\", \"description\", \"categoryName\"], \"like\": [{\"_index\": \"products\", \"_id\": \"?0\"}], \"min_term_freq\": 1, \"max_query_terms\": 12}}")
    List<ProductDocument> findSimilarProducts(Long productId);
}
