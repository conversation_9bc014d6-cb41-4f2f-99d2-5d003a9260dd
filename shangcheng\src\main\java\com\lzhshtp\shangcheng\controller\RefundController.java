package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.RefundRequestDTO;
import com.lzhshtp.shangcheng.dto.RefundResponseDTO;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.RefundService;
import com.lzhshtp.shangcheng.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 退款控制器
 */
@RestController
@RequestMapping("/api/refunds")
public class RefundController {
    
    private static final Logger logger = LoggerFactory.getLogger(RefundController.class);
    
    @Autowired
    private RefundService refundService;

    @Autowired
    private UserService userService;
    
    /**
     * 创建退款申请
     */
    @PostMapping("/create")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> createRefund(@Valid @RequestBody RefundRequestDTO request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            Long refundId = refundService.createRefundRequest(user.getUserId(), request);
            return ApiResponse.success("退款申请创建成功", refundId);
        } catch (Exception e) {
            logger.error("创建退款申请失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 商家处理退款申请
     */
    @PostMapping("/seller/response")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> sellerResponse(@Valid @RequestBody RefundResponseDTO response) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            boolean success = refundService.handleSellerResponse(user.getUserId(), response);
            return ApiResponse.success("处理成功", success);
        } catch (Exception e) {
            logger.error("商家处理退款申请失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
    
    /**
     * 申请管理员介入（支持多文件上传）
     */
    @PostMapping("/admin/intervention")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> requestAdminIntervention(
            @RequestParam Long refundId,
            @RequestParam(required = false) String evidence,
            @RequestParam(value = "files", required = false) MultipartFile[] files) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            boolean success = refundService.requestAdminIntervention(user.getUserId(), refundId, evidence, files);
            return ApiResponse.success("申请管理员介入成功", success);
        } catch (Exception e) {
            logger.error("申请管理员介入失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员处理退款申请
     */
    @PostMapping("/admin/response")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> adminResponse(@Valid @RequestBody RefundResponseDTO response) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            boolean success = refundService.handleAdminResponse(user.getUserId(), response);
            return ApiResponse.success("处理成功", success);
        } catch (Exception e) {
            logger.error("管理员处理退款申请失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 处理强制退款任务
     */
    @PostMapping("/forced/{forcedRefundId}/process")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> processForcedRefund(@PathVariable Long forcedRefundId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            boolean success = refundService.processForcedRefund(user.getUserId(), forcedRefundId);
            return ApiResponse.success("强制退款任务处理成功", success);
        } catch (Exception e) {
            logger.error("处理强制退款任务失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 查询退款申请列表
     */
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<RefundRequestDTO>> getRefundList(@RequestParam String type) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            List<RefundRequestDTO> refunds = refundService.getRefundRequests(user.getUserId(), type);
            return ApiResponse.success("查询成功", refunds);
        } catch (Exception e) {
            logger.error("查询退款申请列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 查询强制退款任务列表
     */
    @GetMapping("/forced/tasks")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<RefundRequestDTO>> getForcedRefundTasks() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            List<RefundRequestDTO> tasks = refundService.getForcedRefundTasks(user.getUserId());
            return ApiResponse.success("查询成功", tasks);
        } catch (Exception e) {
            logger.error("查询强制退款任务失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员查询退款申请列表
     */
    @GetMapping("/admin/list")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<List<RefundRequestDTO>> getAdminRefundList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        try {
            List<RefundRequestDTO> refunds = refundService.getAdminRefundList(page, pageSize, status, keyword);
            return ApiResponse.success("查询成功", refunds);
        } catch (Exception e) {
            logger.error("管理员查询退款申请列表失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }

    /**
     * 管理员查询退款统计数据
     */
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Object> getAdminRefundStatistics() {
        try {
            Object statistics = refundService.getAdminRefundStatistics();
            return ApiResponse.success("查询成功", statistics);
        } catch (Exception e) {
            logger.error("管理员查询退款统计数据失败", e);
            return ApiResponse.fail(e.getMessage());
        }
    }
}
