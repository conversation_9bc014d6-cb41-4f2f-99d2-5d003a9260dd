package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 订单完成响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCompleteResponse {

    /**
     * 订单是否完成成功
     */
    private Boolean success;

    /**
     * 是否需要显示评价弹窗
     */
    private Boolean showReviewDialog;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 卖家ID
     */
    private Long sellerId;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品标题
     */
    private String productTitle;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 订单金额
     */
    private java.math.BigDecimal totalAmount;

    /**
     * 消息提示
     */
    private String message;

    /**
     * 创建成功响应（需要评价）
     */
    public static OrderCompleteResponse successWithReview(Long orderId, Long sellerId, String sellerName,
                                                         Long productId, String productTitle, String productImage,
                                                         java.math.BigDecimal totalAmount) {
        return OrderCompleteResponse.builder()
                .success(true)
                .showReviewDialog(true)
                .orderId(orderId)
                .sellerId(sellerId)
                .sellerName(sellerName)
                .productId(productId)
                .productTitle(productTitle)
                .productImage(productImage)
                .totalAmount(totalAmount)
                .message("订单完成成功，请为本次购物体验评价")
                .build();
    }

    /**
     * 创建成功响应（无需评价）
     */
    public static OrderCompleteResponse successWithoutReview(String message) {
        return OrderCompleteResponse.builder()
                .success(true)
                .showReviewDialog(false)
                .message(message)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static OrderCompleteResponse failure(String message) {
        return OrderCompleteResponse.builder()
                .success(false)
                .showReviewDialog(false)
                .message(message)
                .build();
    }
}
