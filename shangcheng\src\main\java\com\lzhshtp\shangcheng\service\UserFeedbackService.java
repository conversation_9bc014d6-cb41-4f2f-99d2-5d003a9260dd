package com.lzhshtp.shangcheng.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lzhshtp.shangcheng.dto.FeedbackStatusUpdateRequest;
import com.lzhshtp.shangcheng.dto.UserFeedbackDTO;
import com.lzhshtp.shangcheng.dto.UserFeedbackQueryParams;
import com.lzhshtp.shangcheng.dto.UserFeedbackRequest;
import com.lzhshtp.shangcheng.model.UserFeedback;

/**
 * 用户反馈/举报服务接口
 */
public interface UserFeedbackService extends IService<UserFeedback> {
    
    /**
     * 提交反馈/举报
     *
     * @param request 反馈请求
     * @param userId 当前用户ID
     * @return 反馈ID
     */
    Long submitFeedback(UserFeedbackRequest request, Long userId);
    
    /**
     * 分页查询反馈列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<UserFeedbackDTO> getFeedbackList(UserFeedbackQueryParams queryParams);
    
    /**
     * 获取反馈详情
     *
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    UserFeedbackDTO getFeedbackDetail(Long feedbackId);
    
    /**
     * 更新反馈状态（管理员功能）
     *
     * @param feedbackId 反馈ID
     * @param request 状态更新请求
     * @param adminId 管理员ID
     * @return 是否成功
     */
    boolean updateFeedbackStatus(Long feedbackId, FeedbackStatusUpdateRequest request, Long adminId);
    
    /**
     * 删除反馈
     *
     * @param feedbackId 反馈ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    boolean deleteFeedback(Long feedbackId, Long userId);
} 