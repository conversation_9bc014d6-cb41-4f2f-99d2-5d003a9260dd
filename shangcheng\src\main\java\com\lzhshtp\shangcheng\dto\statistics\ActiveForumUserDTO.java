package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活跃论坛用户DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActiveForumUserDTO {
    private Long userId;                // 用户ID
    private String username;            // 用户名
    private String avatarUrl;           // 头像
    private Long postCount;             // 发帖数量
    private Long commentCount;          // 评论数量
    private Long totalViews;            // 帖子总浏览量
    private Double avgViewsPerPost;     // 平均每帖浏览量
}
