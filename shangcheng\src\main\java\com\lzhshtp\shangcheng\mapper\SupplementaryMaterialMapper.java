package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.SupplementaryMaterial;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 补充材料Mapper
 */
@Mapper
public interface SupplementaryMaterialMapper extends BaseMapper<SupplementaryMaterial> {
    
    /**
     * 根据材料请求ID查询补充材料
     */
    @Select("SELECT " +
            "lzhshtp_material_id AS materialId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_seller_id AS sellerId, " +
            "lzhshtp_material_type AS materialType, " +
            "lzhshtp_material_urls AS materialUrls, " +
            "lzhshtp_description AS description, " +
            "lzhshtp_submit_time AS submitTime, " +
            "lzhshtp_admin_reviewed AS adminReviewed, " +
            "lzhshtp_admin_comments AS adminComments " +
            "FROM tb_lzhshtp_supplementary_materials " +
            "WHERE lzhshtp_request_id = #{requestId} " +
            "ORDER BY lzhshtp_submit_time DESC")
    List<SupplementaryMaterial> findByRequestId(@Param("requestId") Long requestId);
    
    /**
     * 根据商品ID查询补充材料
     */
    @Select("SELECT " +
            "lzhshtp_material_id AS materialId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_seller_id AS sellerId, " +
            "lzhshtp_material_type AS materialType, " +
            "lzhshtp_material_urls AS materialUrls, " +
            "lzhshtp_description AS description, " +
            "lzhshtp_submit_time AS submitTime, " +
            "lzhshtp_admin_reviewed AS adminReviewed, " +
            "lzhshtp_admin_comments AS adminComments " +
            "FROM tb_lzhshtp_supplementary_materials " +
            "WHERE lzhshtp_product_id = #{productId} " +
            "ORDER BY lzhshtp_submit_time DESC")
    List<SupplementaryMaterial> findByProductId(@Param("productId") Long productId);
    
    /**
     * 根据卖家ID查询补充材料
     */
    @Select("SELECT " +
            "lzhshtp_material_id AS materialId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_seller_id AS sellerId, " +
            "lzhshtp_material_type AS materialType, " +
            "lzhshtp_material_urls AS materialUrls, " +
            "lzhshtp_description AS description, " +
            "lzhshtp_submit_time AS submitTime, " +
            "lzhshtp_admin_reviewed AS adminReviewed, " +
            "lzhshtp_admin_comments AS adminComments " +
            "FROM tb_lzhshtp_supplementary_materials " +
            "WHERE lzhshtp_seller_id = #{sellerId} " +
            "ORDER BY lzhshtp_submit_time DESC")
    List<SupplementaryMaterial> findBySellerId(@Param("sellerId") Long sellerId);

    /**
     * 查询未审核的补充材料
     */
    @Select("SELECT " +
            "lzhshtp_material_id AS materialId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_seller_id AS sellerId, " +
            "lzhshtp_material_type AS materialType, " +
            "lzhshtp_material_urls AS materialUrls, " +
            "lzhshtp_description AS description, " +
            "lzhshtp_submit_time AS submitTime, " +
            "lzhshtp_admin_reviewed AS adminReviewed, " +
            "lzhshtp_admin_comments AS adminComments " +
            "FROM tb_lzhshtp_supplementary_materials " +
            "WHERE lzhshtp_admin_reviewed = FALSE " +
            "ORDER BY lzhshtp_submit_time ASC")
    List<SupplementaryMaterial> findUnreviewedMaterials();
    
    /**
     * 更新管理员审核状态
     */
    @Update("UPDATE tb_lzhshtp_supplementary_materials " +
            "SET lzhshtp_admin_reviewed = #{reviewed}, lzhshtp_admin_comments = #{comments} " +
            "WHERE lzhshtp_material_id = #{materialId}")
    int updateReviewStatus(@Param("materialId") Long materialId, 
                          @Param("reviewed") Boolean reviewed,
                          @Param("comments") String comments);
}
