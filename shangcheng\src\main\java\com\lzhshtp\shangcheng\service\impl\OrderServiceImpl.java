package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.OrderCreateRequest;
import com.lzhshtp.shangcheng.dto.OrderDTO;
import com.lzhshtp.shangcheng.dto.OrderCompleteResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.mapper.OrderMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.ShippingAddressMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Order;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.ShippingAddress;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.OrderService;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.VerificationService;
import com.lzhshtp.shangcheng.utils.AlipayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ProductService productService;

    @Autowired
    private ShippingAddressMapper addressMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private OrderAsyncService orderAsyncService;

    @Override
    @Transactional
    public Long createOrder(Long userId, OrderCreateRequest request) {
        logger.info("创建订单，用户ID: {}, 请求: {}", userId, request);

        // 1. 获取商品信息
        ProductDTO productDTO = productService.getProductById(request.getProductId());
        if (productDTO == null) {
            logger.error("商品不存在，ID: {}", request.getProductId());
            throw new RuntimeException("商品不存在");
        }

        // 2. 检查商品状态
        if (!Product.ProductStatus.AVAILABLE.equals(productDTO.getStatus())) {
            logger.error("商品状态不可购买，状态: {}", productDTO.getStatus());
            throw new RuntimeException("商品已下架或已售出");
        }

        // 3. 检查是否是自己的商品
        if (productDTO.getSellerId().equals(userId)) {
            logger.error("不能购买自己的商品");
            throw new RuntimeException("不能购买自己的商品");
        }

        // 4. 获取收货地址
        ShippingAddress address = addressMapper.findById(request.getAddressId());
        if (address == null || !address.getUserId().equals(userId)) {
            logger.error("收货地址不存在或不属于当前用户");
            throw new RuntimeException("收货地址不存在");
        }

        // 5. 处理验货逻辑
        boolean needVerification = false;
        String verificationPayer = null;
        BigDecimal verificationFee = BigDecimal.ZERO;

        // 检查是否需要验货
        logger.info("验货逻辑检查 - 请求验货: {}, 商品支持验货: {}",
            request.getOfficialVerification(), productDTO.getSupportOfficialVerification());

        if (request.getOfficialVerification() != null && request.getOfficialVerification()) {
            needVerification = true;
            verificationPayer = request.getVerificationPayer() != null ? request.getVerificationPayer() : "buyer";
            verificationFee = request.getVerificationFee() != null ? request.getVerificationFee() : BigDecimal.valueOf(10.00);
            logger.info("买家选择验货 - 付费方: {}, 费用: {}", verificationPayer, verificationFee);
        } else if (productDTO.getSupportOfficialVerification() != null && productDTO.getSupportOfficialVerification()) {
            needVerification = true;
            verificationPayer = "seller";
            verificationFee = productDTO.getVerificationFee() != null ? productDTO.getVerificationFee() : BigDecimal.valueOf(10.00);
            logger.info("卖家提供验货 - 付费方: {}, 费用: {}", verificationPayer, verificationFee);
        }

        logger.info("最终验货设置 - 需要验货: {}, 付费方: {}, 费用: {}", needVerification, verificationPayer, verificationFee);

        // 6. 验证金额
        BigDecimal totalAmount = productDTO.getPrice();
        if (needVerification && "buyer".equals(verificationPayer)) {
            totalAmount = totalAmount.add(verificationFee);
        }

        if (request.getTotalAmount() != null && request.getTotalAmount().compareTo(totalAmount) != 0) {
            logger.warn("前端计算的总金额与后端不一致，前端: {}, 后端: {}", request.getTotalAmount(), totalAmount);
        }

        // 6. 创建订单
        Order order = new Order();
        order.setProductId(request.getProductId());
        order.setBuyerId(userId);
        order.setSellerId(productDTO.getSellerId());
        order.setOrderDate(LocalDateTime.now());
        order.setTotalAmount(totalAmount);
        order.setStatus(Order.OrderStatus.PENDING_PAYMENT);
        order.setPaymentMethod(request.getPaymentMethod());

        // 设置验货相关字段
        order.setOfficialVerification(needVerification);
        order.setVerificationFee(verificationFee);
        // 确保verificationPayer不为null，即使不需要验货也设置默认值
        order.setVerificationPayer(verificationPayer != null ? verificationPayer : "buyer");

        // 格式化收货地址信息
        String shippingAddressStr = String.format("%s,%s,%s%s%s%s",
                address.getRecipientName(),
                address.getPhoneNumber(),
                address.getProvince(),
                address.getCity(),
                address.getDistrict() != null ? address.getDistrict() : "",
                address.getStreetAddress());
        order.setShippingAddress(shippingAddressStr);

        // 保存订单
        orderMapper.insert(order);
        logger.info("订单创建成功，ID: {}", order.getOrderId());

        // 验证保存后的验货字段
        logger.info("保存后的订单验货信息 - 验货: {}, 付费方: {}, 费用: {}",
            order.getOfficialVerification(), order.getVerificationPayer(), order.getVerificationFee());

        // 注意：验货记录将在发货时创建，而不是订单创建时

        return order.getOrderId();
    }

    @Override
    public OrderDTO getOrderById(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return null;
        }
        return convertToDTO(order);
    }

    @Override
    public PageResult<OrderDTO> getBuyerOrders(Long buyerId, String status, int page, int pageSize) {
        // 创建分页对象
        Page<Order> pageObj = new Page<>(page, pageSize);
        IPage<Order> orderPage;

        // 根据状态查询
        if (status != null && !status.isEmpty()) {
            orderPage = orderMapper.selectBuyerOrdersByStatus(pageObj, buyerId, status);
        } else {
            orderPage = orderMapper.selectBuyerOrders(pageObj, buyerId);
        }

        // 转换为DTO
        List<OrderDTO> orderDTOs = convertToDTOs(orderPage.getRecords());

        return new PageResult<>(orderDTOs, orderPage.getTotal(), page, pageSize);
    }

    @Override
    public PageResult<OrderDTO> getSellerOrders(Long sellerId, String status, int page, int pageSize) {
        // 创建分页对象
        Page<Order> pageObj = new Page<>(page, pageSize);
        IPage<Order> orderPage;

        // 根据状态查询
        if (status != null && !status.isEmpty()) {
            orderPage = orderMapper.selectSellerOrdersByStatus(pageObj, sellerId, status);
        } else {
            orderPage = orderMapper.selectSellerOrders(pageObj, sellerId);
        }

        // 转换为DTO
        List<OrderDTO> orderDTOs = convertToDTOs(orderPage.getRecords());

        return new PageResult<>(orderDTOs, orderPage.getTotal(), page, pageSize);
    }

    @Override
    public PageResult<OrderDTO> getAdminOrders(String keyword, String status, int page, int pageSize) {
        logger.info("管理员查询订单，关键词: {}, 状态: {}, 页码: {}, 每页数量: {}", keyword, status, page, pageSize);

        // 创建分页对象
        Page<Order> pageObj = new Page<>(page, pageSize);

        // 调用Mapper查询
        IPage<Order> orderPage = orderMapper.selectAdminOrders(pageObj, keyword, status);

        // 转换为DTO
        List<OrderDTO> orderDTOs = convertToDTOs(orderPage.getRecords());

        return new PageResult<>(orderDTOs, orderPage.getTotal(), page, pageSize);
    }

    @Override
    @Transactional
    public boolean cancelOrder(Long orderId, Long userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            logger.error("订单不存在，ID: {}", orderId);
            return false;
        }

        // 只有买家可以取消订单
        if (!order.getBuyerId().equals(userId)) {
            logger.error("无权取消订单，订单ID: {}, 用户ID: {}", orderId, userId);
            return false;
        }

        // 只有待支付状态的订单可以取消
        if (!Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
            logger.error("订单状态不允许取消，状态: {}", order.getStatus());
            return false;
        }

        // 更新订单状态
        order.setStatus(Order.OrderStatus.CANCELLED);
        return orderMapper.updateById(order) > 0;
    }

    @Override
    @Transactional
    public boolean updateOrderStatus(Long orderId, String status, Long userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            logger.error("订单不存在，ID: {}", orderId);
            return false;
        }

        // 权限检查
        boolean isBuyer = order.getBuyerId().equals(userId);
        boolean isSeller = order.getSellerId().equals(userId);

        if (!isBuyer && !isSeller) {
            logger.error("无权更新订单状态，订单ID: {}, 用户ID: {}", orderId, userId);
            return false;
        }

        // 状态转换检查
        if (!isValidStatusTransition(order.getStatus(), status, isBuyer, isSeller)) {
            logger.error("无效的状态转换，当前状态: {}, 目标状态: {}", order.getStatus(), status);
            return false;
        }

        // 更新订单状态
        order.setStatus(status);

        // 如果订单完成，更新商品状态为已售出
        if (Order.OrderStatus.COMPLETED.equals(status)) {
            Product product = productMapper.selectById(order.getProductId());
            if (product != null) {
                product.setStatus(Product.ProductStatus.SOLD);
                productMapper.updateById(product);
            }
        }

        return orderMapper.updateById(order) > 0;
    }

    @Override
    @Transactional
    public OrderCompleteResponse completeOrder(Long orderId, Long userId) {
        try {
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                logger.error("订单不存在，ID: {}", orderId);
                return OrderCompleteResponse.failure("订单不存在");
            }

            // 只有买家可以完成订单
            if (!order.getBuyerId().equals(userId)) {
                logger.error("无权完成订单，订单ID: {}, 用户ID: {}", orderId, userId);
                return OrderCompleteResponse.failure("无权操作此订单");
            }

            // 只有已送达的订单可以完成
            if (!Order.OrderStatus.DELIVERED.equals(order.getStatus())) {
                logger.error("订单状态不允许完成，当前状态: {}", order.getStatus());
                return OrderCompleteResponse.failure("订单状态不允许确认收货");
            }

            // 更新订单状态为已完成
            order.setStatus(Order.OrderStatus.COMPLETED);

            // 更新商品状态为已售出
            Product product = productMapper.selectById(order.getProductId());
            if (product != null) {
                product.setStatus(Product.ProductStatus.SOLD);
                productMapper.updateById(product);
            }

            boolean updateSuccess = orderMapper.updateById(order) > 0;
            if (!updateSuccess) {
                return OrderCompleteResponse.failure("确认收货失败");
            }

            // 获取卖家信息
            User seller = userMapper.selectById(order.getSellerId());
            String sellerName = seller != null ? seller.getUsername() : "未知卖家";

            // 获取商品信息
            String productTitle = product != null ? product.getTitle() : "未知商品";
            String productImage = product != null ? product.getImageUrls() : null;

            // 检查是否已经评价过
            // 这里需要注入ReviewService，但为了避免循环依赖，我们先简单处理
            // TODO: 后续可以通过事件机制来处理

            logger.info("订单确认收货成功，订单ID: {}", orderId);

            // 返回成功响应，包含评价信息
            return OrderCompleteResponse.successWithReview(
                orderId,
                order.getSellerId(),
                sellerName,
                order.getProductId(),
                productTitle,
                productImage,
                order.getTotalAmount()
            );

        } catch (Exception e) {
            logger.error("确认收货失败，订单ID: {}", orderId, e);
            return OrderCompleteResponse.failure("确认收货失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void shipOrder(Long orderId, Long userId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            logger.error("订单不存在，ID: {}", orderId);
            throw new RuntimeException("订单不存在");
        }

        // 只有卖家可以发货
        if (!order.getSellerId().equals(userId)) {
            logger.error("无权发货，订单ID: {}, 用户ID: {}", orderId, userId);
            throw new RuntimeException("无权执行此操作");
        }

        // 只有已支付的订单可以发货
        if (!Order.OrderStatus.PAID.equals(order.getStatus())) {
            logger.error("订单状态不允许发货，当前状态: {}", order.getStatus());
            throw new RuntimeException("订单状态不正确，无法发货");
        }

        // 检查是否需要验货，如果需要则先扣除验货费
        if (needsVerification(order)) {
            // 需要验货：根据付费方扣除验货费
            try {
                if ("seller".equals(order.getVerificationPayer())) {
                    // 卖家付费：从卖家余额扣除
                    deductVerificationFeeFromSeller(order);
                    logger.info("已扣除卖家验货费，订单ID: {}", orderId);
                } else {
                    // 买家付费：验货费已包含在订单总金额中，无需额外扣除
                    logger.info("买家付费验货，验货费已包含在订单金额中，订单ID: {}", orderId);
                }
            } catch (Exception e) {
                logger.error("扣除验货费失败，无法发货，订单ID: {}", orderId, e);
                throw new RuntimeException("验货费扣除失败，发货失败：" + e.getMessage());
            }
        }

        // 更新订单状态为已发货（验货费检查通过后才发货）
        order.setStatus(Order.OrderStatus.SHIPPED);
        orderMapper.updateById(order);

        // 根据是否需要验货进行后续处理
        if (needsVerification(order)) {
            // 需要验货：创建验货记录，然后异步任务模拟商品送达验货中心
            try {
                // 创建验货记录（付费方和费用信息从订单中获取）
                verificationService.createVerificationRecord(orderId);
                logger.info("验货记录创建成功，订单ID: {}", orderId);

                // 异步任务，模拟商品送达验货中心
                orderAsyncService.scheduleVerificationArrival(orderId);
                logger.info("订单 {} 需要验货，将送达验货中心", orderId);
            } catch (Exception e) {
                logger.error("创建验货记录失败，订单ID: {}", orderId, e);
                // 验货记录创建失败，改为直接送达
                orderAsyncService.scheduleDelivery(orderId);
                logger.info("验货记录创建失败，订单 {} 改为直接送达买家", orderId);
            }
        } else {
            // 不需要验货：异步任务，10秒后自动更新为已送达
            orderAsyncService.scheduleDelivery(orderId);
            logger.info("订单 {} 不需要验货，将直接送达买家", orderId);
        }
    }

    /**
     * 检查订单是否需要验货
     */
    private boolean needsVerification(Order order) {
        try {
            // 获取商品信息
            Product product = productMapper.selectById(order.getProductId());
            if (product == null) {
                logger.warn("商品不存在，订单ID: {}, 商品ID: {}", order.getOrderId(), order.getProductId());
                return false;
            }

            // 检查商品是否支持官方验货
            Boolean supportVerification = product.getSupportOfficialVerification();
            if (supportVerification == true) {
                return true;
            }

            // 检查买家是否选择了验货服务
            Boolean buyerChooseVerification = order.getOfficialVerification();
            return buyerChooseVerification != null && buyerChooseVerification;

        } catch (Exception e) {
            logger.error("检查验货需求失败，订单ID: {}", order.getOrderId(), e);
            return false;
        }
    }

    /**
     * 扣除卖家验货费
     */
    private void deductVerificationFeeFromSeller(Order order) {
        try {
            // 获取商品信息
            Product product = productMapper.selectById(order.getProductId());
            if (product == null) {
                logger.error("商品不存在，订单ID: {}", order.getOrderId());
                throw new RuntimeException("商品不存在");
            }

            // 获取验货费用
            BigDecimal verificationFee = product.getVerificationFee();
            if (verificationFee == null || verificationFee.compareTo(BigDecimal.ZERO) <= 0) {
                logger.info("商品无验货费用，订单ID: {}", order.getOrderId());
                return;
            }

            // 获取卖家信息
            User seller = userMapper.selectById(order.getSellerId());
            if (seller == null) {
                logger.error("卖家不存在，订单ID: {}", order.getOrderId());
                throw new RuntimeException("卖家不存在");
            }

            // 检查卖家余额是否足够
            BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
            if (sellerBalance.compareTo(verificationFee) < 0) {
                logger.error("卖家余额不足，无法扣除验货费，订单ID: {}, 余额: {}, 验货费: {}",
                    order.getOrderId(), sellerBalance, verificationFee);
                throw new RuntimeException("卖家余额不足，无法扣除验货费");
            }

            // 扣除卖家验货费
            BigDecimal newBalance = sellerBalance.subtract(verificationFee);
            seller.setBalance(newBalance);
            userMapper.updateById(seller);

            logger.info("已扣除卖家验货费，订单ID: {}, 验货费: {}, 卖家余额: {} -> {}",
                order.getOrderId(), verificationFee, sellerBalance, newBalance);

        } catch (Exception e) {
            logger.error("扣除卖家验货费失败，订单ID: {}", order.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 获取验货费用
     */
    private BigDecimal getVerificationFee(Order order) {
        try {
            // 获取商品信息
            Product product = productMapper.selectById(order.getProductId());
            if (product != null && product.getVerificationFee() != null) {
                return product.getVerificationFee();
            }
            // 默认验货费用
            return new BigDecimal("10.00");
        } catch (Exception e) {
            logger.error("获取验货费用失败，订单ID: {}", order.getOrderId(), e);
            return new BigDecimal("10.00");
        }
    }

    @Override
    @Transactional
    public String generateAlipayLink(Long orderId, Long userId) {
        // 获取订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            logger.error("订单不存在，ID: {}", orderId);
            throw new RuntimeException("订单不存在");
        }

        // 检查是否是买家
        if (!order.getBuyerId().equals(userId)) {
            logger.error("非买家无法支付订单，订单ID: {}, 用户ID: {}", orderId, userId);
            throw new RuntimeException("无权支付此订单");
        }

        // 检查订单状态
        if (!Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
            logger.error("订单状态不允许支付，状态: {}", order.getStatus());
            throw new RuntimeException("订单状态不允许支付");
        }

        // 获取商品信息
        Product product = productMapper.selectById(order.getProductId());
        if (product == null) {
            logger.error("商品不存在，ID: {}", order.getProductId());
            throw new RuntimeException("商品不存在");
        }

        // 转换为DTO
        OrderDTO orderDTO = convertToDTO(order);
        orderDTO.setProductTitle(product.getTitle());

        // 生成支付链接，现在不再需要传递 returnUrl
        return alipayUtil.generatePayLink(orderDTO);
    }

    @Override
    @Transactional
    public boolean handleAlipayCallback(Map<String, String> params) {
        logger.info("收到支付宝回调: {}", params);

        // 验证签名
        if (!alipayUtil.verifyCallback(params)) {
            logger.error("支付宝回调签名验证失败");
            return false;
        }

        // 获取交易状态
        String tradeStatus = params.get("trade_status");

        // 在同步回调中，trade_status 可能为 null。
        // 只有在 trade_status 明确存在且不为 TRADE_SUCCESS 时，我们才认为交易失败。
        if (tradeStatus != null && !"TRADE_SUCCESS".equals(tradeStatus)) {
            logger.info("交易未成功，状态: {}", tradeStatus);
            return true; // 返回true表示接收到通知，但业务上不处理
        }

        // 获取订单号
        String outTradeNo = params.get("out_trade_no");
        Long orderId;
        try {
            orderId = Long.parseLong(outTradeNo);
        } catch (NumberFormatException e) {
            logger.error("订单号格式错误: {}", outTradeNo);
            return false;
        }

        // 获取交易号
        String tradeNo = params.get("trade_no");

        // 更新订单状态
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            logger.error("订单不存在，ID: {}", orderId);
            return false;
        }

        // 检查订单状态
        if (!Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
            logger.warn("订单已处理，状态: {}", order.getStatus());
            return true;
        }

        // 更新订单状态和交易号
        order.setStatus(Order.OrderStatus.PAID);
        order.setTransactionId(tradeNo);

        boolean updated = orderMapper.updateById(order) > 0;
        if (updated) {
            logger.info("订单支付成功，订单ID: {}, 交易号: {}", orderId, tradeNo);
        } else {
            logger.error("更新订单状态失败，订单ID: {}", orderId);
        }

        return updated;
    }

    /**
     * 检查状态转换是否有效
     */
    private boolean isValidStatusTransition(String currentStatus, String targetStatus, boolean isBuyer, boolean isSeller) {
        switch (currentStatus) {
            case Order.OrderStatus.PENDING_PAYMENT:
                // 待支付 -> 已取消（买家）
                return isBuyer && Order.OrderStatus.CANCELLED.equals(targetStatus);
            case Order.OrderStatus.PAID:
                // 已支付 -> 已发货（卖家）
                return isSeller && Order.OrderStatus.SHIPPED.equals(targetStatus);
            case Order.OrderStatus.SHIPPED:
                // 已发货 -> 已送达（买家）
                return isBuyer && Order.OrderStatus.DELIVERED.equals(targetStatus);
            case Order.OrderStatus.DELIVERED:
                // 已送达 -> 已完成（买家）
                return isBuyer && Order.OrderStatus.COMPLETED.equals(targetStatus);
            default:
                return false;
        }
    }

    /**
     * 将Order实体集合转换为OrderDTO集合，优化N+1查询
     */
    private List<OrderDTO> convertToDTOs(List<Order> orders) {
        if (orders == null || orders.isEmpty()) {
            return new ArrayList<>();
        }

        // 提取所有需要的ID
        List<Long> productIds = orders.stream().map(Order::getProductId).distinct().collect(Collectors.toList());
        List<Long> buyerIds = orders.stream().map(Order::getBuyerId).distinct().collect(Collectors.toList());
        List<Long> sellerIds = orders.stream().map(Order::getSellerId).distinct().collect(Collectors.toList());

        // 一次性查询所有关联数据
        Map<Long, Product> productMap = productMapper.selectBatchIds(productIds).stream()
                .filter(p -> p != null && p.getId() != null)
                .collect(Collectors.toMap(Product::getId, product -> product));
        Map<Long, User> buyerMap = userMapper.selectBatchIds(buyerIds).stream()
                .filter(u -> u != null && u.getUserId() != null)
                .collect(Collectors.toMap(User::getUserId, user -> user));
        Map<Long, User> sellerMap = userMapper.selectBatchIds(sellerIds).stream()
                .filter(u -> u != null && u.getUserId() != null)
                .collect(Collectors.toMap(User::getUserId, user -> user));

        // 组装DTO
        return orders.stream().map(order -> {
            OrderDTO dto = new OrderDTO();
            BeanUtils.copyProperties(order, dto);

            // 填充商品信息
            Product product = productMap.get(order.getProductId());
            if (product != null) {
                dto.setProductTitle(product.getTitle());
                if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
                    String[] imageUrls = product.getImageUrls().split(",");
                    if (imageUrls.length > 0) {
                        dto.setProductImage(imageUrls[0]);
                    }
                }
            }

            // 填充买家信息
            User buyer = buyerMap.get(order.getBuyerId());
            if (buyer != null) {
                dto.setBuyerUsername(buyer.getUsername());
            }

            // 填充卖家信息
            User seller = sellerMap.get(order.getSellerId());
            if (seller != null) {
                dto.setSellerUsername(seller.getUsername());
            }

            dto.setStatusText(getStatusText(order.getStatus()));

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 将Order实体转换为OrderDTO
     */
    private OrderDTO convertToDTO(Order order) {
        if (order == null) {
            return null;
        }

        OrderDTO dto = new OrderDTO();
        BeanUtils.copyProperties(order, dto);

        // 获取商品信息
        Product product = productMapper.selectById(order.getProductId());
        if (product != null) {
            dto.setProductTitle(product.getTitle());
            if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
                String[] imageUrls = product.getImageUrls().split(",");
                if (imageUrls.length > 0) {
                    dto.setProductImage(imageUrls[0]);
                }
            }
        }

        // 获取买家信息
        User buyer = userMapper.selectById(order.getBuyerId());
        if (buyer != null) {
            dto.setBuyerUsername(buyer.getUsername());
        }

        // 获取卖家信息
        User seller = userMapper.selectById(order.getSellerId());
        if (seller != null) {
            dto.setSellerUsername(seller.getUsername());
        }

        // 设置状态文本
        dto.setStatusText(getStatusText(order.getStatus()));

        return dto;
    }

    /**
     * 获取状态的中文描述
     */
    private String getStatusText(String status) {
        switch (status) {
            case Order.OrderStatus.PENDING_PAYMENT:
                return "待支付";
            case Order.OrderStatus.PAID:
                return "已支付";
            case Order.OrderStatus.SHIPPED:
                return "已发货";
            case Order.OrderStatus.DELIVERED:
                return "已送达";
            case Order.OrderStatus.COMPLETED:
                return "已完成";
            case Order.OrderStatus.CANCELLED:
                return "已取消";
            case Order.OrderStatus.REFUNDED:
                return "已退款";
            default:
                return status;
        }
    }

    /**
     * 使用余额支付订单
     */
    @Override
    @Transactional
    public boolean payWithBalance(Long orderId, Long userId) {
        try {
            // 查询订单
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                logger.error("订单不存在，订单ID：{}", orderId);
                return false;
            }

            // 验证订单归属
            if (!order.getBuyerId().equals(userId)) {
                logger.error("用户{}无权支付订单{}", userId, orderId);
                return false;
            }

            // 验证订单状态
            if (!Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus())) {
                logger.error("订单{}状态不是待支付，当前状态：{}", orderId, order.getStatus());
                return false;
            }

            // 查询用户余额
            User user = userMapper.selectById(userId);
            if (user == null) {
                logger.error("用户不存在，用户ID：{}", userId);
                return false;
            }

            BigDecimal userBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
            BigDecimal orderAmount = order.getTotalAmount();

            // 检查余额是否足够
            if (userBalance.compareTo(orderAmount) < 0) {
                logger.error("用户{}余额不足，余额：{}，订单金额：{}", userId, userBalance, orderAmount);
                return false;
            }

            // 查询卖家信息
            Long sellerId = order.getSellerId();
            User seller = userMapper.selectById(sellerId);
            if (seller == null) {
                logger.error("卖家不存在，卖家ID：{}", sellerId);
                return false;
            }

            // 扣除买家余额
            BigDecimal newBuyerBalance = userBalance.subtract(orderAmount);
            user.setBalance(newBuyerBalance);
            int updateBuyerResult = userMapper.updateById(user);

            if (updateBuyerResult <= 0) {
                logger.error("更新买家余额失败，买家ID：{}", userId);
                return false;
            }

            // 计算卖家应收金额（扣除验货费，验货费归平台）
            BigDecimal sellerReceiveAmount = orderAmount;
            if (order.getOfficialVerification() != null && order.getOfficialVerification() &&
                "buyer".equals(order.getVerificationPayer())) {
                // 买家付费验货：从订单金额中扣除验货费
                sellerReceiveAmount = orderAmount.subtract(order.getVerificationFee());
                logger.info("买家付费验货，卖家实收：{}，验货费：{}（归平台）",
                    sellerReceiveAmount, order.getVerificationFee());
            }

            // 增加卖家余额（只收商品金额）
            BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
            BigDecimal newSellerBalance = sellerBalance.add(sellerReceiveAmount);
            seller.setBalance(newSellerBalance);
            int updateSellerResult = userMapper.updateById(seller);

            if (updateSellerResult <= 0) {
                logger.error("更新卖家余额失败，卖家ID：{}", sellerId);
                return false;
            }

            // 更新订单状态为已支付
            order.setStatus(Order.OrderStatus.PAID);
            int updateOrderResult = orderMapper.updateById(order);

            if (updateOrderResult <= 0) {
                logger.error("更新订单状态失败，订单ID：{}", orderId);
                // 这里应该回滚用户余额，但由于使用了@Transactional，会自动回滚
                return false;
            }

            logger.info("用户{}使用余额支付订单{}成功，买家支付：{}，卖家实收：{}，买家余额：{} -> {}，卖家余额：{} -> {}",
                    userId, orderId, orderAmount, sellerReceiveAmount, userBalance, newBuyerBalance, sellerBalance, newSellerBalance);

            return true;
        } catch (Exception e) {
            logger.error("余额支付订单失败，订单ID：{}，用户ID：{}", orderId, userId, e);
            return false;
        }
    }

    /**
     * 获取订单支付信息
     */
    @Override
    public Map<String, Object> getPaymentInfo(Long orderId, Long userId) {
        try {
            // 查询订单
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                throw new IllegalArgumentException("订单不存在");
            }

            // 验证订单归属
            if (!order.getBuyerId().equals(userId)) {
                throw new IllegalArgumentException("无权查看此订单");
            }

            // 查询用户余额
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }

            BigDecimal userBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
            BigDecimal orderAmount = order.getTotalAmount();

            // 构建支付信息
            Map<String, Object> paymentInfo = new java.util.HashMap<>();
            paymentInfo.put("orderId", orderId);
            paymentInfo.put("orderAmount", orderAmount);
            paymentInfo.put("userBalance", userBalance);
            paymentInfo.put("balanceEnough", userBalance.compareTo(orderAmount) >= 0);
            paymentInfo.put("orderStatus", order.getStatus());
            paymentInfo.put("canPay", Order.OrderStatus.PENDING_PAYMENT.equals(order.getStatus()));

            return paymentInfo;
        } catch (Exception e) {
            logger.error("获取订单支付信息失败，订单ID：{}，用户ID：{}", orderId, userId, e);
            throw e;
        }
    }
}
