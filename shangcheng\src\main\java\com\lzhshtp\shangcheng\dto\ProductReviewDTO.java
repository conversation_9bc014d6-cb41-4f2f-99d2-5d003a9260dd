package com.lzhshtp.shangcheng.dto;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品评价响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductReviewDTO {
    
    /**
     * 评价ID
     */
    private Long reviewId;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 评价者ID
     */
    private Long reviewerId;
    
    /**
     * 评价者用户名
     */
    private String reviewerName;
    
    /**
     * 评价者头像
     */
    private String reviewerAvatar;
    
    /**
     * 评分（1-5星）
     */
    private Integer rating;
    
    /**
     * 评价内容
     */
    private String comment;
    
    /**
     * 评价时间
     */
    private LocalDateTime reviewDate;
} 