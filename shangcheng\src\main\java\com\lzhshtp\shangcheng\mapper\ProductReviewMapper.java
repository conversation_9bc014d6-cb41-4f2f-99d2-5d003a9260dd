package com.lzhshtp.shangcheng.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.lzhshtp.shangcheng.model.ProductReview;

/**
 * 商品评价Mapper接口
 */
@Mapper
public interface ProductReviewMapper {
    
    /**
     * 添加商品评价
     * 
     * @param review 评价信息
     * @return 影响的行数
     */
    int insert(ProductReview review);
    
    /**
     * 根据商品ID查询评价列表
     * 
     * @param productId 商品ID
     * @return 评价列表
     */
    List<ProductReview> selectByProductId(@Param("productId") Long productId);
    
    /**
     * 根据评价ID查询评价
     * 
     * @param reviewId 评价ID
     * @return 评价信息
     */
    ProductReview selectById(@Param("reviewId") Long reviewId);
    
    /**
     * 根据用户ID查询该用户发布的评价
     * 
     * @param reviewerId 评价者ID
     * @return 评价列表
     */
    List<ProductReview> selectByReviewerId(@Param("reviewerId") Long reviewerId);
    
    /**
     * 删除评价
     * 
     * @param reviewId 评价ID
     * @return 影响的行数
     */
    int deleteById(@Param("reviewId") Long reviewId);
    
    /**
     * 更新评价
     * 
     * @param review 评价信息
     * @return 影响的行数
     */
    int update(ProductReview review);
    
    /**
     * 获取商品的评价总数
     * 
     * @param productId 商品ID
     * @return 评价总数
     */
    int countByProductId(@Param("productId") Long productId);
    
    /**
     * 获取商品的平均评分
     * 
     * @param productId 商品ID
     * @return 平均评分
     */
    Double getAverageRatingByProductId(@Param("productId") Long productId);
} 