import request from '@/utils/request'

/**
 * 获取帖子列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求结果
 */
export function getPostList(params) {
  return request({
    url: '/forum/posts',
    method: 'get',
    params
  })
}

/**
 * 获取帖子详情
 * @param {number} postId 帖子ID
 * @returns {Promise} 请求结果
 */
export function getPostDetail(postId) {
  return request({
    url: `/forum/posts/${postId}`,
    method: 'get'
  })
}

/**
 * 创建帖子
 * @param {Object} data 帖子数据
 * @returns {Promise} 请求结果
 */
export function createPost(data) {
  return request({
    url: '/forum/posts',
    method: 'post',
    data
  })
}

/**
 * 更新帖子
 * @param {number} postId 帖子ID
 * @param {Object} data 帖子数据
 * @returns {Promise} 请求结果
 */
export function updatePost(postId, data) {
  return request({
    url: `/forum/posts/${postId}`,
    method: 'put',
    data
  })
}

/**
 * 删除帖子
 * @param {number} postId 帖子ID
 * @returns {Promise} 请求结果
 */
export function deletePost(postId) {
  return request({
    url: `/forum/posts/${postId}`,
    method: 'delete'
  })
}

/**
 * 获取帖子评论列表
 * @param {number} postId 帖子ID
 * @returns {Promise} 请求结果
 */
export function getPostComments(postId) {
  return request({
    url: `/forum/posts/${postId}/comments`,
    method: 'get'
  })
}

/**
 * 创建评论
 * @param {Object} data 评论数据
 * @returns {Promise} 请求结果
 */
export function createComment(data) {
  return request({
    url: '/forum/posts/comments',
    method: 'post',
    data
  })
}

/**
 * 删除评论
 * @param {number} commentId 评论ID
 * @returns {Promise} 请求结果
 */
export function deleteComment(commentId) {
  return request({
    url: `/forum/posts/comments/${commentId}`,
    method: 'delete'
  })
}

/**
 * 设置帖子置顶状态
 * @param {number} postId 帖子ID
 * @param {boolean} isPinned 是否置顶
 * @returns {Promise} 请求结果
 */
export function setPinned(postId, isPinned) {
  return request({
    url: `/forum/posts/${postId}/pinned`,
    method: 'put',
    params: { isPinned }
  })
}

/**
 * 设置帖子状态
 * @param {number} postId 帖子ID
 * @param {number} status 状态
 * @returns {Promise} 请求结果
 */
export function setStatus(postId, status) {
  return request({
    url: `/forum/posts/${postId}/status`,
    method: 'put',
    params: { status }
  })
}
