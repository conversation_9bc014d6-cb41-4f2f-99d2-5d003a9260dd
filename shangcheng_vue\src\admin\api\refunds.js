import request from '../utils/request'

/**
 * 管理员退款管理API
 */

// 获取待处理的退款申请列表
export const getPendingRefunds = () => {
  return request({
    url: '/refunds/list',
    method: 'get',
    params: { type: 'admin' }
  })
}

// 获取所有退款申请列表（带分页）
export const getAllRefunds = (params) => {
  return request({
    url: '/refunds/admin/list',
    method: 'get',
    params
  })
}

// 管理员处理退款申请
export const handleAdminRefund = (data) => {
  return request({
    url: '/refunds/admin/response',
    method: 'post',
    data
  })
}

// 获取强制退款任务列表
export const getForcedRefundTasks = () => {
  return request({
    url: '/refunds/forced/tasks',
    method: 'get'
  })
}

// 获取退款申请详情
export const getRefundDetail = (refundId) => {
  return request({
    url: `/refunds/${refundId}`,
    method: 'get'
  })
}

// 获取退款统计数据
export const getRefundStatistics = () => {
  return request({
    url: '/refunds/admin/statistics',
    method: 'get'
  })
}

// 批量处理退款申请
export const batchHandleRefunds = (data) => {
  return request({
    url: '/refunds/admin/batch',
    method: 'post',
    data
  })
}

// 导出退款数据
export const exportRefundData = (params) => {
  return request({
    url: '/refunds/admin/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
