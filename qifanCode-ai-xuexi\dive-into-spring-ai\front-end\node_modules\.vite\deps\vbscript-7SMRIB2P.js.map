{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/vbscript.js"], "sourcesContent": ["function mkVBScript(parserConf) {\n    var ERRORCLASS = 'error';\n\n    function wordRegexp(words) {\n        return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n    }\n\n    var singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&\\\\\\\\\\\\^<>=]\");\n    var doubleOperators = new RegExp(\"^((<>)|(<=)|(>=))\");\n    var singleDelimiters = new RegExp('^[\\\\.,]');\n    var brackets = new RegExp('^[\\\\(\\\\)]');\n    var identifiers = new RegExp(\"^[A-Za-z][_A-Za-z0-9]*\");\n\n    var openingKeywords = ['class','sub','select','while','if','function', 'property', 'with', 'for'];\n    var middleKeywords = ['else','elseif','case'];\n    var endKeywords = ['next','loop','wend'];\n\n    var wordOperators = wordRegexp(['and', 'or', 'not', 'xor', 'is', 'mod', 'eqv', 'imp']);\n    var commonkeywords = ['dim', 'redim', 'then',  'until', 'randomize',\n                          'byval','byref','new','property', 'exit', 'in',\n                          'const','private', 'public',\n                          'get','set','let', 'stop', 'on error resume next', 'on error goto 0', 'option explicit', 'call', 'me'];\n\n    //This list was from: http://msdn.microsoft.com/en-us/library/f8tbc79x(v=vs.84).aspx\n    var atomWords = ['true', 'false', 'nothing', 'empty', 'null'];\n    //This list was from: http://msdn.microsoft.com/en-us/library/3ca8tfek(v=vs.84).aspx\n    var builtinFuncsWords = ['abs', 'array', 'asc', 'atn', 'cbool', 'cbyte', 'ccur', 'cdate', 'cdbl', 'chr', 'cint', 'clng', 'cos', 'csng', 'cstr', 'date', 'dateadd', 'datediff', 'datepart',\n                        'dateserial', 'datevalue', 'day', 'escape', 'eval', 'execute', 'exp', 'filter', 'formatcurrency', 'formatdatetime', 'formatnumber', 'formatpercent', 'getlocale', 'getobject',\n                        'getref', 'hex', 'hour', 'inputbox', 'instr', 'instrrev', 'int', 'fix', 'isarray', 'isdate', 'isempty', 'isnull', 'isnumeric', 'isobject', 'join', 'lbound', 'lcase', 'left',\n                        'len', 'loadpicture', 'log', 'ltrim', 'rtrim', 'trim', 'maths', 'mid', 'minute', 'month', 'monthname', 'msgbox', 'now', 'oct', 'replace', 'rgb', 'right', 'rnd', 'round',\n                        'scriptengine', 'scriptenginebuildversion', 'scriptenginemajorversion', 'scriptengineminorversion', 'second', 'setlocale', 'sgn', 'sin', 'space', 'split', 'sqr', 'strcomp',\n                        'string', 'strreverse', 'tan', 'time', 'timer', 'timeserial', 'timevalue', 'typename', 'ubound', 'ucase', 'unescape', 'vartype', 'weekday', 'weekdayname', 'year'];\n\n    //This list was from: http://msdn.microsoft.com/en-us/library/ydz4cfk3(v=vs.84).aspx\n    var builtinConsts = ['vbBlack', 'vbRed', 'vbGreen', 'vbYellow', 'vbBlue', 'vbMagenta', 'vbCyan', 'vbWhite', 'vbBinaryCompare', 'vbTextCompare',\n                         'vbSunday', 'vbMonday', 'vbTuesday', 'vbWednesday', 'vbThursday', 'vbFriday', 'vbSaturday', 'vbUseSystemDayOfWeek', 'vbFirstJan1', 'vbFirstFourDays', 'vbFirstFullWeek',\n                         'vbGeneralDate', 'vbLongDate', 'vbShortDate', 'vbLongTime', 'vbShortTime', 'vbObjectError',\n                         'vbOKOnly', 'vbOKCancel', 'vbAbortRetryIgnore', 'vbYesNoCancel', 'vbYesNo', 'vbRetryCancel', 'vbCritical', 'vbQuestion', 'vbExclamation', 'vbInformation', 'vbDefaultButton1', 'vbDefaultButton2',\n                         'vbDefaultButton3', 'vbDefaultButton4', 'vbApplicationModal', 'vbSystemModal', 'vbOK', 'vbCancel', 'vbAbort', 'vbRetry', 'vbIgnore', 'vbYes', 'vbNo',\n                         'vbCr', 'VbCrLf', 'vbFormFeed', 'vbLf', 'vbNewLine', 'vbNullChar', 'vbNullString', 'vbTab', 'vbVerticalTab', 'vbUseDefault', 'vbTrue', 'vbFalse',\n                         'vbEmpty', 'vbNull', 'vbInteger', 'vbLong', 'vbSingle', 'vbDouble', 'vbCurrency', 'vbDate', 'vbString', 'vbObject', 'vbError', 'vbBoolean', 'vbVariant', 'vbDataObject', 'vbDecimal', 'vbByte', 'vbArray'];\n    //This list was from: http://msdn.microsoft.com/en-us/library/hkc375ea(v=vs.84).aspx\n    var builtinObjsWords = ['WScript', 'err', 'debug', 'RegExp'];\n    var knownProperties = ['description', 'firstindex', 'global', 'helpcontext', 'helpfile', 'ignorecase', 'length', 'number', 'pattern', 'source', 'value', 'count'];\n    var knownMethods = ['clear', 'execute', 'raise', 'replace', 'test', 'write', 'writeline', 'close', 'open', 'state', 'eof', 'update', 'addnew', 'end', 'createobject', 'quit'];\n\n    var aspBuiltinObjsWords = ['server', 'response', 'request', 'session', 'application'];\n    var aspKnownProperties = ['buffer', 'cachecontrol', 'charset', 'contenttype', 'expires', 'expiresabsolute', 'isclientconnected', 'pics', 'status', //response\n                              'clientcertificate', 'cookies', 'form', 'querystring', 'servervariables', 'totalbytes', //request\n                              'contents', 'staticobjects', //application\n                              'codepage', 'lcid', 'sessionid', 'timeout', //session\n                              'scripttimeout']; //server\n    var aspKnownMethods = ['addheader', 'appendtolog', 'binarywrite', 'end', 'flush', 'redirect', //response\n                           'binaryread', //request\n                           'remove', 'removeall', 'lock', 'unlock', //application\n                           'abandon', //session\n                           'getlasterror', 'htmlencode', 'mappath', 'transfer', 'urlencode']; //server\n\n    var knownWords = knownMethods.concat(knownProperties);\n\n    builtinObjsWords = builtinObjsWords.concat(builtinConsts);\n\n    if (parserConf.isASP){\n        builtinObjsWords = builtinObjsWords.concat(aspBuiltinObjsWords);\n        knownWords = knownWords.concat(aspKnownMethods, aspKnownProperties);\n    };\n\n    var keywords = wordRegexp(commonkeywords);\n    var atoms = wordRegexp(atomWords);\n    var builtinFuncs = wordRegexp(builtinFuncsWords);\n    var builtinObjs = wordRegexp(builtinObjsWords);\n    var known = wordRegexp(knownWords);\n    var stringPrefixes = '\"';\n\n    var opening = wordRegexp(openingKeywords);\n    var middle = wordRegexp(middleKeywords);\n    var closing = wordRegexp(endKeywords);\n    var doubleClosing = wordRegexp(['end']);\n    var doOpening = wordRegexp(['do']);\n    var noIndentWords = wordRegexp(['on error resume next', 'exit']);\n    var comment = wordRegexp(['rem']);\n\n\n    function indent(_stream, state) {\n      state.currentIndent++;\n    }\n\n    function dedent(_stream, state) {\n      state.currentIndent--;\n    }\n    // tokenizers\n    function tokenBase(stream, state) {\n        if (stream.eatSpace()) {\n            return null\n            //return null;\n        }\n\n        var ch = stream.peek();\n\n        // Handle Comments\n        if (ch === \"'\") {\n            stream.skipToEnd();\n            return 'comment';\n        }\n        if (stream.match(comment)){\n            stream.skipToEnd();\n            return 'comment';\n        }\n\n\n        // Handle Number Literals\n        if (stream.match(/^((&H)|(&O))?[0-9\\.]/i, false) && !stream.match(/^((&H)|(&O))?[0-9\\.]+[a-z_]/i, false)) {\n            var floatLiteral = false;\n            // Floats\n            if (stream.match(/^\\d*\\.\\d+/i)) { floatLiteral = true; }\n            else if (stream.match(/^\\d+\\.\\d*/)) { floatLiteral = true; }\n            else if (stream.match(/^\\.\\d+/)) { floatLiteral = true; }\n\n            if (floatLiteral) {\n                // Float literals may be \"imaginary\"\n                stream.eat(/J/i);\n                return 'number';\n            }\n            // Integers\n            var intLiteral = false;\n            // Hex\n            if (stream.match(/^&H[0-9a-f]+/i)) { intLiteral = true; }\n            // Octal\n            else if (stream.match(/^&O[0-7]+/i)) { intLiteral = true; }\n            // Decimal\n            else if (stream.match(/^[1-9]\\d*F?/)) {\n                // Decimal literals may be \"imaginary\"\n                stream.eat(/J/i);\n                // TODO - Can you have imaginary longs?\n                intLiteral = true;\n            }\n            // Zero by itself with no other piece of number.\n            else if (stream.match(/^0(?![\\dx])/i)) { intLiteral = true; }\n            if (intLiteral) {\n                // Integer literals may be \"long\"\n                stream.eat(/L/i);\n                return 'number';\n            }\n        }\n\n        // Handle Strings\n        if (stream.match(stringPrefixes)) {\n            state.tokenize = tokenStringFactory(stream.current());\n            return state.tokenize(stream, state);\n        }\n\n        // Handle operators and Delimiters\n        if (stream.match(doubleOperators)\n            || stream.match(singleOperators)\n            || stream.match(wordOperators)) {\n            return 'operator';\n        }\n        if (stream.match(singleDelimiters)) {\n            return null;\n        }\n\n        if (stream.match(brackets)) {\n            return \"bracket\";\n        }\n\n        if (stream.match(noIndentWords)) {\n            state.doInCurrentLine = true;\n\n            return 'keyword';\n        }\n\n        if (stream.match(doOpening)) {\n            indent(stream,state);\n            state.doInCurrentLine = true;\n\n            return 'keyword';\n        }\n        if (stream.match(opening)) {\n            if (! state.doInCurrentLine)\n              indent(stream,state);\n            else\n              state.doInCurrentLine = false;\n\n            return 'keyword';\n        }\n        if (stream.match(middle)) {\n            return 'keyword';\n        }\n\n\n        if (stream.match(doubleClosing)) {\n            dedent(stream,state);\n            dedent(stream,state);\n\n            return 'keyword';\n        }\n        if (stream.match(closing)) {\n            if (! state.doInCurrentLine)\n              dedent(stream,state);\n            else\n              state.doInCurrentLine = false;\n\n            return 'keyword';\n        }\n\n        if (stream.match(keywords)) {\n            return 'keyword';\n        }\n\n        if (stream.match(atoms)) {\n            return 'atom';\n        }\n\n        if (stream.match(known)) {\n            return 'variableName.special';\n        }\n\n        if (stream.match(builtinFuncs)) {\n            return 'builtin';\n        }\n\n        if (stream.match(builtinObjs)){\n            return 'builtin';\n        }\n\n        if (stream.match(identifiers)) {\n            return 'variable';\n        }\n\n        // Handle non-detected items\n        stream.next();\n        return ERRORCLASS;\n    }\n\n    function tokenStringFactory(delimiter) {\n        var singleline = delimiter.length == 1;\n        var OUTCLASS = 'string';\n\n        return function(stream, state) {\n            while (!stream.eol()) {\n                stream.eatWhile(/[^'\"]/);\n                if (stream.match(delimiter)) {\n                    state.tokenize = tokenBase;\n                    return OUTCLASS;\n                } else {\n                    stream.eat(/['\"]/);\n                }\n            }\n            if (singleline) {\n              state.tokenize = tokenBase;\n            }\n            return OUTCLASS;\n        };\n    }\n\n\n    function tokenLexer(stream, state) {\n        var style = state.tokenize(stream, state);\n        var current = stream.current();\n\n        // Handle '.' connected identifiers\n        if (current === '.') {\n            style = state.tokenize(stream, state);\n\n            current = stream.current();\n            if (style && (style.substr(0, 8) === 'variable' || style==='builtin' || style==='keyword')){//|| knownWords.indexOf(current.substring(1)) > -1) {\n                if (style === 'builtin' || style === 'keyword') style='variable';\n                if (knownWords.indexOf(current.substr(1)) > -1) style='keyword';\n\n                return style;\n            } else {\n                return ERRORCLASS;\n            }\n        }\n\n        return style;\n    }\n\n    return {\n        name: \"vbscript\",\n        startState: function() {\n            return {\n              tokenize: tokenBase,\n              lastToken: null,\n              currentIndent: 0,\n              nextLineIndent: 0,\n              doInCurrentLine: false,\n              ignoreKeyword: false\n\n\n          };\n        },\n\n        token: function(stream, state) {\n            if (stream.sol()) {\n              state.currentIndent += state.nextLineIndent;\n              state.nextLineIndent = 0;\n              state.doInCurrentLine = 0;\n            }\n            var style = tokenLexer(stream, state);\n\n            state.lastToken = {style:style, content: stream.current()};\n\n            if (style===null) style=null;\n\n            return style;\n        },\n\n        indent: function(state, textAfter, cx) {\n            var trueText = textAfter.replace(/^\\s+|\\s+$/g, '') ;\n            if (trueText.match(closing) || trueText.match(doubleClosing) || trueText.match(middle)) return cx.unit*(state.currentIndent-1);\n            if(state.currentIndent < 0) return 0;\n            return state.currentIndent * cx.unit\n        }\n\n    };\n};\n\nexport const vbScript = mkVBScript({})\nexport const vbScriptASP = mkVBScript({isASP: true})\n"], "mappings": ";;;AAAA,SAAS,WAAW,YAAY;AAC5B,MAAI,aAAa;AAEjB,WAAS,WAAW,OAAO;AACvB,WAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG;AAAA,EAC9D;AAEA,MAAI,kBAAkB,IAAI,OAAO,0BAA0B;AAC3D,MAAI,kBAAkB,IAAI,OAAO,mBAAmB;AACpD,MAAI,mBAAmB,IAAI,OAAO,SAAS;AAC3C,MAAI,WAAW,IAAI,OAAO,WAAW;AACrC,MAAI,cAAc,IAAI,OAAO,wBAAwB;AAErD,MAAI,kBAAkB,CAAC,SAAQ,OAAM,UAAS,SAAQ,MAAK,YAAY,YAAY,QAAQ,KAAK;AAChG,MAAI,iBAAiB,CAAC,QAAO,UAAS,MAAM;AAC5C,MAAI,cAAc,CAAC,QAAO,QAAO,MAAM;AAEvC,MAAI,gBAAgB,WAAW,CAAC,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,OAAO,KAAK,CAAC;AACrF,MAAI,iBAAiB;AAAA,IAAC;AAAA,IAAO;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAClC;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAM;AAAA,IAAY;AAAA,IAAQ;AAAA,IAC1C;AAAA,IAAQ;AAAA,IAAW;AAAA,IACnB;AAAA,IAAM;AAAA,IAAM;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAwB;AAAA,IAAmB;AAAA,IAAmB;AAAA,IAAQ;AAAA,EAAI;AAG3H,MAAI,YAAY,CAAC,QAAQ,SAAS,WAAW,SAAS,MAAM;AAE5D,MAAI,oBAAoB;AAAA,IAAC;AAAA,IAAO;AAAA,IAAS;AAAA,IAAO;AAAA,IAAO;AAAA,IAAS;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAY;AAAA,IAC3J;AAAA,IAAc;AAAA,IAAa;AAAA,IAAO;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAO;AAAA,IAAU;AAAA,IAAkB;AAAA,IAAkB;AAAA,IAAgB;AAAA,IAAiB;AAAA,IAAa;AAAA,IAClK;AAAA,IAAU;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAS;AAAA,IAAY;AAAA,IAAO;AAAA,IAAO;AAAA,IAAW;AAAA,IAAU;AAAA,IAAW;AAAA,IAAU;AAAA,IAAa;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAS;AAAA,IACtK;AAAA,IAAO;AAAA,IAAe;AAAA,IAAO;AAAA,IAAS;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAO;AAAA,IAAU;AAAA,IAAS;AAAA,IAAa;AAAA,IAAU;AAAA,IAAO;AAAA,IAAO;AAAA,IAAW;AAAA,IAAO;AAAA,IAAS;AAAA,IAAO;AAAA,IACjK;AAAA,IAAgB;AAAA,IAA4B;AAAA,IAA4B;AAAA,IAA4B;AAAA,IAAU;AAAA,IAAa;AAAA,IAAO;AAAA,IAAO;AAAA,IAAS;AAAA,IAAS;AAAA,IAAO;AAAA,IAClK;AAAA,IAAU;AAAA,IAAc;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAc;AAAA,IAAa;AAAA,IAAY;AAAA,IAAU;AAAA,IAAS;AAAA,IAAY;AAAA,IAAW;AAAA,IAAW;AAAA,IAAe;AAAA,EAAM;AAGrL,MAAI,gBAAgB;AAAA,IAAC;AAAA,IAAW;AAAA,IAAS;AAAA,IAAW;AAAA,IAAY;AAAA,IAAU;AAAA,IAAa;AAAA,IAAU;AAAA,IAAW;AAAA,IAAmB;AAAA,IAC1G;AAAA,IAAY;AAAA,IAAY;AAAA,IAAa;AAAA,IAAe;AAAA,IAAc;AAAA,IAAY;AAAA,IAAc;AAAA,IAAwB;AAAA,IAAe;AAAA,IAAmB;AAAA,IACtJ;AAAA,IAAiB;AAAA,IAAc;AAAA,IAAe;AAAA,IAAc;AAAA,IAAe;AAAA,IAC3E;AAAA,IAAY;AAAA,IAAc;AAAA,IAAsB;AAAA,IAAiB;AAAA,IAAW;AAAA,IAAiB;AAAA,IAAc;AAAA,IAAc;AAAA,IAAiB;AAAA,IAAiB;AAAA,IAAoB;AAAA,IAC/K;AAAA,IAAoB;AAAA,IAAoB;AAAA,IAAsB;AAAA,IAAiB;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAW;AAAA,IAAW;AAAA,IAAY;AAAA,IAAS;AAAA,IAC9I;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAc;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAc;AAAA,IAAgB;AAAA,IAAS;AAAA,IAAiB;AAAA,IAAgB;AAAA,IAAU;AAAA,IACvI;AAAA,IAAW;AAAA,IAAU;AAAA,IAAa;AAAA,IAAU;AAAA,IAAY;AAAA,IAAY;AAAA,IAAc;AAAA,IAAU;AAAA,IAAY;AAAA,IAAY;AAAA,IAAW;AAAA,IAAa;AAAA,IAAa;AAAA,IAAgB;AAAA,IAAa;AAAA,IAAU;AAAA,EAAS;AAE9N,MAAI,mBAAmB,CAAC,WAAW,OAAO,SAAS,QAAQ;AAC3D,MAAI,kBAAkB,CAAC,eAAe,cAAc,UAAU,eAAe,YAAY,cAAc,UAAU,UAAU,WAAW,UAAU,SAAS,OAAO;AAChK,MAAI,eAAe,CAAC,SAAS,WAAW,SAAS,WAAW,QAAQ,SAAS,aAAa,SAAS,QAAQ,SAAS,OAAO,UAAU,UAAU,OAAO,gBAAgB,MAAM;AAE5K,MAAI,sBAAsB,CAAC,UAAU,YAAY,WAAW,WAAW,aAAa;AACpF,MAAI,qBAAqB;AAAA,IAAC;AAAA,IAAU;AAAA,IAAgB;AAAA,IAAW;AAAA,IAAe;AAAA,IAAW;AAAA,IAAmB;AAAA,IAAqB;AAAA,IAAQ;AAAA;AAAA,IAC/G;AAAA,IAAqB;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAe;AAAA,IAAmB;AAAA;AAAA,IAC1E;AAAA,IAAY;AAAA;AAAA,IACZ;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAa;AAAA;AAAA,IACjC;AAAA,EAAe;AACzC,MAAI,kBAAkB;AAAA,IAAC;AAAA,IAAa;AAAA,IAAe;AAAA,IAAe;AAAA,IAAO;AAAA,IAAS;AAAA;AAAA,IAC3D;AAAA;AAAA,IACA;AAAA,IAAU;AAAA,IAAa;AAAA,IAAQ;AAAA;AAAA,IAC/B;AAAA;AAAA,IACA;AAAA,IAAgB;AAAA,IAAc;AAAA,IAAW;AAAA,IAAY;AAAA,EAAW;AAEvF,MAAI,aAAa,aAAa,OAAO,eAAe;AAEpD,qBAAmB,iBAAiB,OAAO,aAAa;AAExD,MAAI,WAAW,OAAM;AACjB,uBAAmB,iBAAiB,OAAO,mBAAmB;AAC9D,iBAAa,WAAW,OAAO,iBAAiB,kBAAkB;AAAA,EACtE;AAAC;AAED,MAAI,WAAW,WAAW,cAAc;AACxC,MAAI,QAAQ,WAAW,SAAS;AAChC,MAAI,eAAe,WAAW,iBAAiB;AAC/C,MAAI,cAAc,WAAW,gBAAgB;AAC7C,MAAI,QAAQ,WAAW,UAAU;AACjC,MAAI,iBAAiB;AAErB,MAAI,UAAU,WAAW,eAAe;AACxC,MAAI,SAAS,WAAW,cAAc;AACtC,MAAI,UAAU,WAAW,WAAW;AACpC,MAAI,gBAAgB,WAAW,CAAC,KAAK,CAAC;AACtC,MAAI,YAAY,WAAW,CAAC,IAAI,CAAC;AACjC,MAAI,gBAAgB,WAAW,CAAC,wBAAwB,MAAM,CAAC;AAC/D,MAAI,UAAU,WAAW,CAAC,KAAK,CAAC;AAGhC,WAAS,OAAO,SAAS,OAAO;AAC9B,UAAM;AAAA,EACR;AAEA,WAAS,OAAO,SAAS,OAAO;AAC9B,UAAM;AAAA,EACR;AAEA,WAAS,UAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,SAAS,GAAG;AACnB,aAAO;AAAA,IAEX;AAEA,QAAI,KAAK,OAAO,KAAK;AAGrB,QAAI,OAAO,KAAK;AACZ,aAAO,UAAU;AACjB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,MAAM,OAAO,GAAE;AACtB,aAAO,UAAU;AACjB,aAAO;AAAA,IACX;AAIA,QAAI,OAAO,MAAM,yBAAyB,KAAK,KAAK,CAAC,OAAO,MAAM,gCAAgC,KAAK,GAAG;AACtG,UAAI,eAAe;AAEnB,UAAI,OAAO,MAAM,YAAY,GAAG;AAAE,uBAAe;AAAA,MAAM,WAC9C,OAAO,MAAM,WAAW,GAAG;AAAE,uBAAe;AAAA,MAAM,WAClD,OAAO,MAAM,QAAQ,GAAG;AAAE,uBAAe;AAAA,MAAM;AAExD,UAAI,cAAc;AAEd,eAAO,IAAI,IAAI;AACf,eAAO;AAAA,MACX;AAEA,UAAI,aAAa;AAEjB,UAAI,OAAO,MAAM,eAAe,GAAG;AAAE,qBAAa;AAAA,MAAM,WAE/C,OAAO,MAAM,YAAY,GAAG;AAAE,qBAAa;AAAA,MAAM,WAEjD,OAAO,MAAM,aAAa,GAAG;AAElC,eAAO,IAAI,IAAI;AAEf,qBAAa;AAAA,MACjB,WAES,OAAO,MAAM,cAAc,GAAG;AAAE,qBAAa;AAAA,MAAM;AAC5D,UAAI,YAAY;AAEZ,eAAO,IAAI,IAAI;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AAGA,QAAI,OAAO,MAAM,cAAc,GAAG;AAC9B,YAAM,WAAW,mBAAmB,OAAO,QAAQ,CAAC;AACpD,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACvC;AAGA,QAAI,OAAO,MAAM,eAAe,KACzB,OAAO,MAAM,eAAe,KAC5B,OAAO,MAAM,aAAa,GAAG;AAChC,aAAO;AAAA,IACX;AACA,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAChC,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,QAAQ,GAAG;AACxB,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,aAAa,GAAG;AAC7B,YAAM,kBAAkB;AAExB,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,SAAS,GAAG;AACzB,aAAO,QAAO,KAAK;AACnB,YAAM,kBAAkB;AAExB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,MAAM,OAAO,GAAG;AACvB,UAAI,CAAE,MAAM;AACV,eAAO,QAAO,KAAK;AAAA;AAEnB,cAAM,kBAAkB;AAE1B,aAAO;AAAA,IACX;AACA,QAAI,OAAO,MAAM,MAAM,GAAG;AACtB,aAAO;AAAA,IACX;AAGA,QAAI,OAAO,MAAM,aAAa,GAAG;AAC7B,aAAO,QAAO,KAAK;AACnB,aAAO,QAAO,KAAK;AAEnB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,MAAM,OAAO,GAAG;AACvB,UAAI,CAAE,MAAM;AACV,eAAO,QAAO,KAAK;AAAA;AAEnB,cAAM,kBAAkB;AAE1B,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,QAAQ,GAAG;AACxB,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,KAAK,GAAG;AACrB,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,KAAK,GAAG;AACrB,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,YAAY,GAAG;AAC5B,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,WAAW,GAAE;AAC1B,aAAO;AAAA,IACX;AAEA,QAAI,OAAO,MAAM,WAAW,GAAG;AAC3B,aAAO;AAAA,IACX;AAGA,WAAO,KAAK;AACZ,WAAO;AAAA,EACX;AAEA,WAAS,mBAAmB,WAAW;AACnC,QAAI,aAAa,UAAU,UAAU;AACrC,QAAI,WAAW;AAEf,WAAO,SAAS,QAAQ,OAAO;AAC3B,aAAO,CAAC,OAAO,IAAI,GAAG;AAClB,eAAO,SAAS,OAAO;AACvB,YAAI,OAAO,MAAM,SAAS,GAAG;AACzB,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,IAAI,MAAM;AAAA,QACrB;AAAA,MACJ;AACA,UAAI,YAAY;AACd,cAAM,WAAW;AAAA,MACnB;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAGA,WAAS,WAAW,QAAQ,OAAO;AAC/B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,OAAO,QAAQ;AAG7B,QAAI,YAAY,KAAK;AACjB,cAAQ,MAAM,SAAS,QAAQ,KAAK;AAEpC,gBAAU,OAAO,QAAQ;AACzB,UAAI,UAAU,MAAM,OAAO,GAAG,CAAC,MAAM,cAAc,UAAQ,aAAa,UAAQ,YAAW;AACvF,YAAI,UAAU,aAAa,UAAU;AAAW,kBAAM;AACtD,YAAI,WAAW,QAAQ,QAAQ,OAAO,CAAC,CAAC,IAAI;AAAI,kBAAM;AAEtD,eAAO;AAAA,MACX,OAAO;AACH,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAEA,SAAO;AAAA,IACH,MAAM;AAAA,IACN,YAAY,WAAW;AACnB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,QACX,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,eAAe;AAAA,MAGnB;AAAA,IACF;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC3B,UAAI,OAAO,IAAI,GAAG;AAChB,cAAM,iBAAiB,MAAM;AAC7B,cAAM,iBAAiB;AACvB,cAAM,kBAAkB;AAAA,MAC1B;AACA,UAAI,QAAQ,WAAW,QAAQ,KAAK;AAEpC,YAAM,YAAY,EAAC,OAAa,SAAS,OAAO,QAAQ,EAAC;AAEzD,UAAI,UAAQ;AAAM,gBAAM;AAExB,aAAO;AAAA,IACX;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACnC,UAAI,WAAW,UAAU,QAAQ,cAAc,EAAE;AACjD,UAAI,SAAS,MAAM,OAAO,KAAK,SAAS,MAAM,aAAa,KAAK,SAAS,MAAM,MAAM;AAAG,eAAO,GAAG,QAAM,MAAM,gBAAc;AAC5H,UAAG,MAAM,gBAAgB;AAAG,eAAO;AACnC,aAAO,MAAM,gBAAgB,GAAG;AAAA,IACpC;AAAA,EAEJ;AACJ;AAEO,IAAM,WAAW,WAAW,CAAC,CAAC;AAC9B,IAAM,cAAc,WAAW,EAAC,OAAO,KAAI,CAAC;", "names": []}