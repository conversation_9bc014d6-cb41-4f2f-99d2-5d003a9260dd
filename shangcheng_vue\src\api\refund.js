import request from '@/utils/request';

/**
 * 创建退款申请
 * @param {Object} data 退款申请数据
 * @returns {Promise} 请求Promise
 */
export function createRefund(data) {
  return request({
    url: '/refunds/create',
    method: 'post',
    data
  });
}

/**
 * 商家处理退款申请
 * @param {Object} data 处理数据
 * @returns {Promise} 请求Promise
 */
export function handleSellerRefund(data) {
  return request({
    url: '/refunds/seller/response',
    method: 'post',
    data
  });
}

/**
 * 申请管理员介入
 * @param {Number} refundId 退款申请ID
 * @param {String} evidence 补充证据
 * @returns {Promise} 请求Promise
 */
export function requestAdminIntervention(refundId, evidence) {
  return request({
    url: '/refunds/admin/intervention',
    method: 'post',
    params: { refundId, evidence }
  });
}

/**
 * 申请管理员介入（支持多文件上传）
 * @param {FormData} formData 包含文件和数据的FormData
 * @returns {Promise} 请求Promise
 */
export function requestAdminInterventionWithFiles(formData) {
  return request({
    url: '/refunds/admin/intervention',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 管理员处理退款申请
 * @param {Object} data 处理数据
 * @returns {Promise} 请求Promise
 */
export function handleAdminRefund(data) {
  return request({
    url: '/refunds/admin/response',
    method: 'post',
    data
  });
}

/**
 * 处理强制退款任务
 * @param {Number} forcedRefundId 强制退款任务ID
 * @returns {Promise} 请求Promise
 */
export function processForcedRefund(forcedRefundId) {
  return request({
    url: `/refunds/forced/${forcedRefundId}/process`,
    method: 'post'
  });
}

/**
 * 查询退款申请列表
 * @param {String} type 用户类型：buyer/seller/admin
 * @returns {Promise} 请求Promise
 */
export function getRefundList(type) {
  return request({
    url: '/refunds/list',
    method: 'get',
    params: { type }
  });
}

/**
 * 查询强制退款任务列表
 * @returns {Promise} 请求Promise
 */
export function getForcedRefundTasks() {
  return request({
    url: '/refunds/forced/tasks',
    method: 'get'
  });
}

/**
 * 管理员查询退款申请列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getAdminRefundList(params = {}) {
  return request({
    url: '/refunds/admin/list',
    method: 'get',
    params: {
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      status: params.status || '',
      keyword: params.keyword || ''
    }
  });
}

/**
 * 管理员查询退款统计数据
 * @returns {Promise} 请求Promise
 */
export function getAdminRefundStatistics() {
  return request({
    url: '/refunds/admin/statistics',
    method: 'get'
  });
}
