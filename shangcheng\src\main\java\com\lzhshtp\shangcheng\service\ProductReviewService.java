package com.lzhshtp.shangcheng.service;

import java.util.List;

import com.lzhshtp.shangcheng.dto.ProductReviewDTO;
import com.lzhshtp.shangcheng.dto.ProductReviewRequest;

/**
 * 商品评价Service接口
 */
public interface ProductReviewService {
    
    /**
     * 添加商品评价
     * 
     * @param userId 当前用户ID
     * @param request 评价请求
     * @return 评价DTO
     */
    ProductReviewDTO addReview(Long userId, ProductReviewRequest request);
    
    /**
     * 根据商品ID获取评价列表
     * 
     * @param productId 商品ID
     * @return 评价列表
     */
    List<ProductReviewDTO> getReviewsByProductId(Long productId);
    
    /**
     * 根据评价ID获取评价
     * 
     * @param reviewId 评价ID
     * @return 评价DTO
     */
    ProductReviewDTO getReviewById(Long reviewId);
    
    /**
     * 根据用户ID获取该用户发布的评价
     * 
     * @param userId 用户ID
     * @return 评价列表
     */
    List<ProductReviewDTO> getReviewsByUserId(Long userId);
    
    /**
     * 删除评价
     * 
     * @param userId 当前用户ID
     * @param reviewId 评价ID
     * @return 是否删除成功
     */
    boolean deleteReview(Long userId, Long reviewId);
    
    /**
     * 更新评价
     * 
     * @param userId 当前用户ID
     * @param reviewId 评价ID
     * @param request 评价请求
     * @return 更新后的评价DTO
     */
    ProductReviewDTO updateReview(Long userId, Long reviewId, ProductReviewRequest request);
    
    /**
     * 获取商品的评价总数
     * 
     * @param productId 商品ID
     * @return 评价总数
     */
    int getReviewCount(Long productId);
    
    /**
     * 获取商品的平均评分
     * 
     * @param productId 商品ID
     * @return 平均评分
     */
    Double getAverageRating(Long productId);
} 