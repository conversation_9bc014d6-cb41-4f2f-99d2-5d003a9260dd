<template>
  <div class="dashboard-home">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">数据统计</h2>
      <div class="header-actions">
        <el-button @click="refreshAllData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 概览统计卡片 -->
    <div class="overview-cards" v-if="overviewData">
      <div class="stat-card">
        <div class="card-icon user-icon">👥</div>
        <div class="card-content">
          <div class="card-title">总用户数</div>
          <div class="card-value">{{ formatNumber(overviewData.totalUsers) }}</div>
          <div class="card-trend" :class="getTrendClass(overviewData.userGrowthRate)">
            <span class="trend-icon">{{ getTrendIcon(overviewData.userGrowthRate) }}</span>
            <span>{{ formatPercent(overviewData.userGrowthRate) }}</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="card-icon product-icon">📦</div>
        <div class="card-content">
          <div class="card-title">总商品数</div>
          <div class="card-value">{{ formatNumber(overviewData.totalProducts) }}</div>
          <div class="card-trend" :class="getTrendClass(overviewData.productGrowthRate)">
            <span class="trend-icon">{{ getTrendIcon(overviewData.productGrowthRate) }}</span>
            <span>{{ formatPercent(overviewData.productGrowthRate) }}</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="card-icon order-icon">📝</div>
        <div class="card-content">
          <div class="card-title">总订单数</div>
          <div class="card-value">{{ formatNumber(overviewData.totalOrders) }}</div>
          <div class="card-trend" :class="getTrendClass(overviewData.orderGrowthRate)">
            <span class="trend-icon">{{ getTrendIcon(overviewData.orderGrowthRate) }}</span>
            <span>{{ formatPercent(overviewData.orderGrowthRate) }}</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="card-icon revenue-icon">💰</div>
        <div class="card-content">
          <div class="card-title">总交易额</div>
          <div class="card-value">¥{{ formatNumber(overviewData.totalRevenue) }}</div>
          <div class="card-trend" :class="getTrendClass(overviewData.revenueGrowthRate)">
            <span class="trend-icon">{{ getTrendIcon(overviewData.revenueGrowthRate) }}</span>
            <span>{{ formatPercent(overviewData.revenueGrowthRate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 第一行图表 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户注册趋势</h3>
            <el-select v-model="userChartDays" @change="fetchUserStatistics" size="small">
              <el-option label="最近7天" :value="7"></el-option>
              <el-option label="最近30天" :value="30"></el-option>
              <el-option label="最近90天" :value="90"></el-option>
            </el-select>
          </div>
          <div class="chart-content">
            <div ref="userTrendChart" class="chart"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>商品发布趋势</h3>
            <el-select v-model="productChartDays" @change="fetchProductStatistics" size="small">
              <el-option label="最近7天" :value="7"></el-option>
              <el-option label="最近30天" :value="30"></el-option>
              <el-option label="最近90天" :value="90"></el-option>
            </el-select>
          </div>
          <div class="chart-content">
            <div ref="productTrendChart" class="chart"></div>
          </div>
        </div>
      </div>

      <!-- 第二行图表 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单量趋势</h3>
            <el-select v-model="orderChartDays" @change="fetchOrderStatistics" size="small">
              <el-option label="最近7天" :value="7"></el-option>
              <el-option label="最近30天" :value="30"></el-option>
              <el-option label="最近90天" :value="90"></el-option>
            </el-select>
          </div>
          <div class="chart-content">
            <div ref="orderTrendChart" class="chart"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>商品分类分布</h3>
          </div>
          <div class="chart-content">
            <div ref="categoryChart" class="chart"></div>
          </div>
        </div>
      </div>

      <!-- 第三行图表 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户地域分布</h3>
          </div>
          <div class="chart-content">
            <div ref="locationChart" class="chart"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>热门商品排行（按订单量）</h3>
          </div>
          <div class="chart-content">
            <div ref="hotProductChart" class="chart"></div>
          </div>
        </div>
      </div>

      <!-- 第四行图表 - 论坛统计 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>论坛活跃度趋势</h3>
            <el-select v-model="forumChartDays" @change="fetchForumStatistics" size="small">
              <el-option label="最近7天" :value="7"></el-option>
              <el-option label="最近30天" :value="30"></el-option>
              <el-option label="最近90天" :value="90"></el-option>
            </el-select>
          </div>
          <div class="chart-content">
            <div ref="forumActivityChart" class="chart"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>论坛分类热度</h3>
          </div>
          <div class="chart-content">
            <div ref="forumCategoryChart" class="chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时数据面板 -->
    <div class="realtime-panel" v-if="realtimeData">
      <div class="panel-header">
        <h3>实时数据</h3>
        <span class="update-time">最后更新: {{ realtimeUpdateTime }}</span>
      </div>
      <div class="realtime-stats">
        <div class="realtime-item">
          <span class="label">今日新增用户:</span>
          <span class="value">{{ realtimeData.todayNewUsers }}</span>
        </div>
        <div class="realtime-item">
          <span class="label">今日新增商品:</span>
          <span class="value">{{ realtimeData.todayNewProducts }}</span>
        </div>
        <div class="realtime-item">
          <span class="label">今日订单数:</span>
          <span class="value">{{ realtimeData.todayOrders }}</span>
        </div>
        <div class="realtime-item">
          <span class="label">今日交易额:</span>
          <span class="value">¥{{ formatNumber(realtimeData.todayRevenue) }}</span>
        </div>
        <div class="realtime-item" v-if="forumStatistics">
          <span class="label">论坛总帖子:</span>
          <span class="value">{{ formatNumber(forumStatistics.totalPosts) }}</span>
        </div>
        <div class="realtime-item" v-if="forumStatistics">
          <span class="label">论坛总评论:</span>
          <span class="value">{{ formatNumber(forumStatistics.totalComments) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import {
  getOverviewStatistics,
  getUserStatistics,
  getProductStatistics,
  getOrderStatistics,
  getForumStatistics,
  getHotProducts,
  getUserLocationStatistics,
  getRealtimeStatistics
} from '@/admin/api/statistics'

// 响应式数据
const loading = ref(false)
const overviewData = ref(null)
const userStatistics = ref(null)
const productStatistics = ref(null)
const orderStatistics = ref(null)
const forumStatistics = ref(null)
const hotProducts = ref([])
const locationStatistics = ref([])
const realtimeData = ref(null)
const realtimeUpdateTime = ref('')

// 图表选项
const userChartDays = ref(30)
const productChartDays = ref(30)
const orderChartDays = ref(30)
const forumChartDays = ref(30)

// 图表实例
const userTrendChart = ref(null)
const productTrendChart = ref(null)
const orderTrendChart = ref(null)
const categoryChart = ref(null)
const locationChart = ref(null)
const hotProductChart = ref(null)
const forumActivityChart = ref(null)
const forumCategoryChart = ref(null)

let userChartInstance = null
let productChartInstance = null
let orderChartInstance = null
let categoryChartInstance = null
let locationChartInstance = null
let hotProductChartInstance = null
let forumActivityChartInstance = null
let forumCategoryChartInstance = null
let realtimeTimer = null

// 生命周期
onMounted(async () => {
  await initDashboard()
  startRealtimeUpdate()
})

onUnmounted(() => {
  // 销毁图表实例
  destroyCharts()
  // 清除定时器
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
  }
})

// 初始化Dashboard
const initDashboard = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchOverviewStatistics(),
      fetchUserStatistics(),
      fetchProductStatistics(),
      fetchOrderStatistics(),
      fetchForumStatistics(),
      fetchHotProducts(),
      fetchLocationStatistics()
    ])

    // 等待DOM更新后初始化图表
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('初始化Dashboard失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 获取概览统计数据
const fetchOverviewStatistics = async () => {
  try {
    const res = await getOverviewStatistics()
    if (res.code === 200) {
      overviewData.value = res.data
    }
  } catch (error) {
    console.error('获取概览统计失败:', error)
  }
}

// 获取用户统计数据
const fetchUserStatistics = async () => {
  try {
    const res = await getUserStatistics(userChartDays.value)
    if (res.code === 200) {
      userStatistics.value = res.data
      updateUserChart()
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

// 获取商品统计数据
const fetchProductStatistics = async () => {
  try {
    const res = await getProductStatistics(productChartDays.value)
    if (res.code === 200) {
      productStatistics.value = res.data
      updateProductChart()
      updateCategoryChart()
    }
  } catch (error) {
    console.error('获取商品统计失败:', error)
  }
}

// 获取订单统计数据
const fetchOrderStatistics = async () => {
  try {
    const res = await getOrderStatistics(orderChartDays.value)
    if (res.code === 200) {
      orderStatistics.value = res.data
      updateOrderChart()
    }
  } catch (error) {
    console.error('获取订单统计失败:', error)
  }
}

// 获取论坛统计数据
const fetchForumStatistics = async () => {
  try {
    const res = await getForumStatistics(forumChartDays.value)
    if (res.code === 200) {
      forumStatistics.value = res.data
      updateForumActivityChart()
      updateForumCategoryChart()
    }
  } catch (error) {
    console.error('获取论坛统计失败:', error)
  }
}

// 获取热门商品（固定按订单量）
const fetchHotProducts = async () => {
  try {
    const res = await getHotProducts(10, 'orders')
    if (res.code === 200) {
      hotProducts.value = res.data
      updateHotProductChart()
    }
  } catch (error) {
    console.error('获取热门商品失败:', error)
  }
}

// 获取地域统计
const fetchLocationStatistics = async () => {
  try {
    const res = await getUserLocationStatistics()
    if (res.code === 200) {
      locationStatistics.value = res.data
      updateLocationChart()
    }
  } catch (error) {
    console.error('获取地域统计失败:', error)
  }
}

// 获取实时数据
const fetchRealtimeStatistics = async () => {
  try {
    const res = await getRealtimeStatistics()
    if (res.code === 200) {
      realtimeData.value = res.data
      realtimeUpdateTime.value = new Date().toLocaleTimeString()
    }
  } catch (error) {
    console.error('获取实时数据失败:', error)
  }
}

// 初始化所有图表
const initCharts = () => {
  initUserChart()
  initProductChart()
  initOrderChart()
  initCategoryChart()
  initLocationChart()
  initHotProductChart()
  initForumActivityChart()
  initForumCategoryChart()
}

// 初始化用户趋势图表
const initUserChart = () => {
  if (userChartInstance) {
    userChartInstance.dispose()
  }
  userChartInstance = echarts.init(userTrendChart.value)
  updateUserChart()
}

// 更新用户趋势图表
const updateUserChart = () => {
  if (!userChartInstance || !userStatistics.value) return

  const option = {
    title: {
      text: '用户注册趋势',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['新增用户', '累计用户']
    },
    xAxis: {
      type: 'category',
      data: userStatistics.value.registrationDates || []
    },
    yAxis: [
      {
        type: 'value',
        name: '新增用户',
        position: 'left'
      },
      {
        type: 'value',
        name: '累计用户',
        position: 'right'
      }
    ],
    series: [
      {
        name: '新增用户',
        type: 'line',
        data: userStatistics.value.registrationCounts || [],
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '累计用户',
        type: 'line',
        yAxisIndex: 1,
        data: userStatistics.value.cumulativeUsers || [],
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }

  userChartInstance.setOption(option)
}

// 初始化商品趋势图表
const initProductChart = () => {
  if (productChartInstance) {
    productChartInstance.dispose()
  }
  productChartInstance = echarts.init(productTrendChart.value)
  updateProductChart()
}

// 更新商品趋势图表
const updateProductChart = () => {
  if (!productChartInstance || !productStatistics.value) return

  const option = {
    title: {
      text: '商品发布趋势',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: productStatistics.value.publishDates || []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增商品',
        type: 'bar',
        data: productStatistics.value.publishCounts || [],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }

  productChartInstance.setOption(option)
}

// 初始化订单趋势图表
const initOrderChart = () => {
  if (orderChartInstance) {
    orderChartInstance.dispose()
  }
  orderChartInstance = echarts.init(orderTrendChart.value)
  updateOrderChart()
}

// 更新订单趋势图表
const updateOrderChart = () => {
  if (!orderChartInstance || !orderStatistics.value) return

  const option = {
    title: {
      text: '订单量趋势',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: orderStatistics.value.orderDates || []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '订单数量',
        type: 'line',
        data: orderStatistics.value.orderCounts || [],
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }

  orderChartInstance.setOption(option)
}

// 初始化分类分布图表
const initCategoryChart = () => {
  if (categoryChartInstance) {
    categoryChartInstance.dispose()
  }
  categoryChartInstance = echarts.init(categoryChart.value)
  updateCategoryChart()
}

// 更新分类分布图表
const updateCategoryChart = () => {
  if (!categoryChartInstance || !productStatistics.value) return

  const categoryData = productStatistics.value.categoryDistribution || []
  const data = categoryData.map(item => ({
    name: item.categoryName,
    value: item.productCount
  }))

  const option = {
    title: {
      text: '商品分类分布',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '商品分类',
        type: 'pie',
        radius: ['40%', '70%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  categoryChartInstance.setOption(option)
}

// 初始化地域分布图表
const initLocationChart = () => {
  if (locationChartInstance) {
    locationChartInstance.dispose()
  }
  locationChartInstance = echarts.init(locationChart.value)
  updateLocationChart()
}

// 更新地域分布图表
const updateLocationChart = () => {
  if (!locationChartInstance || !locationStatistics.value) return

  const data = locationStatistics.value.map(item => ({
    name: item.location,
    value: item.userCount
  }))

  const option = {
    title: {
      text: '用户地域分布',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户数量',
        type: 'pie',
        radius: '60%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  locationChartInstance.setOption(option)
}

// 初始化热门商品图表
const initHotProductChart = () => {
  if (hotProductChartInstance) {
    hotProductChartInstance.dispose()
  }
  hotProductChartInstance = echarts.init(hotProductChart.value)
  updateHotProductChart()
}

// 更新热门商品图表
const updateHotProductChart = () => {
  if (!hotProductChartInstance || !hotProducts.value) return

  const data = hotProducts.value.map(item => item.title)
  const values = hotProducts.value.map(item => item.value)

  const option = {
    title: {
      text: '热门商品排行（按订单量）',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data
    },
    series: [
      {
        name: '订单量',
        type: 'bar',
        data: values,
        itemStyle: { color: '#909399' }
      }
    ]
  }

  hotProductChartInstance.setOption(option)
}

// 初始化论坛活跃度图表
const initForumActivityChart = () => {
  if (forumActivityChartInstance) {
    forumActivityChartInstance.dispose()
  }
  forumActivityChartInstance = echarts.init(forumActivityChart.value)
  updateForumActivityChart()
}

// 更新论坛活跃度图表
const updateForumActivityChart = () => {
  if (!forumActivityChartInstance || !forumStatistics.value) return

  const option = {
    title: {
      text: '论坛活跃度趋势',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['帖子数', '评论数', '浏览量']
    },
    xAxis: {
      type: 'category',
      data: forumStatistics.value.activityDates || []
    },
    yAxis: [
      {
        type: 'value',
        name: '帖子/评论',
        position: 'left'
      },
      {
        type: 'value',
        name: '浏览量',
        position: 'right'
      }
    ],
    series: [
      {
        name: '帖子数',
        type: 'line',
        data: forumStatistics.value.postCounts || [],
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '评论数',
        type: 'line',
        data: forumStatistics.value.commentCounts || [],
        smooth: true,
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '浏览量',
        type: 'line',
        yAxisIndex: 1,
        data: forumStatistics.value.viewCounts || [],
        smooth: true,
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }

  forumActivityChartInstance.setOption(option)
}

// 初始化论坛分类图表
const initForumCategoryChart = () => {
  if (forumCategoryChartInstance) {
    forumCategoryChartInstance.dispose()
  }
  forumCategoryChartInstance = echarts.init(forumCategoryChart.value)
  updateForumCategoryChart()
}

// 更新论坛分类图表
const updateForumCategoryChart = () => {
  if (!forumCategoryChartInstance || !forumStatistics.value) return

  const categoryData = forumStatistics.value.categoryStatistics || []
  const data = categoryData.map(item => ({
    name: item.categoryName,
    value: item.postCount
  }))

  const option = {
    title: {
      text: '论坛分类热度',
      textStyle: { fontSize: 14, color: '#333' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '帖子数量',
        type: 'pie',
        radius: ['30%', '70%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {c}'
        }
      }
    ]
  }

  forumCategoryChartInstance.setOption(option)
}

// 销毁所有图表
const destroyCharts = () => {
  if (userChartInstance) {
    userChartInstance.dispose()
    userChartInstance = null
  }
  if (productChartInstance) {
    productChartInstance.dispose()
    productChartInstance = null
  }
  if (orderChartInstance) {
    orderChartInstance.dispose()
    orderChartInstance = null
  }
  if (categoryChartInstance) {
    categoryChartInstance.dispose()
    categoryChartInstance = null
  }
  if (locationChartInstance) {
    locationChartInstance.dispose()
    locationChartInstance = null
  }
  if (hotProductChartInstance) {
    hotProductChartInstance.dispose()
    hotProductChartInstance = null
  }
  if (forumActivityChartInstance) {
    forumActivityChartInstance.dispose()
    forumActivityChartInstance = null
  }
  if (forumCategoryChartInstance) {
    forumCategoryChartInstance.dispose()
    forumCategoryChartInstance = null
  }
}

// 开始实时数据更新
const startRealtimeUpdate = () => {
  fetchRealtimeStatistics()
  realtimeTimer = setInterval(() => {
    fetchRealtimeStatistics()
  }, 30000) // 每30秒更新一次
}

// 刷新所有数据
const refreshAllData = async () => {
  await initDashboard()
  ElMessage.success('数据刷新成功')
}

// 导出报表
const exportReport = () => {
  ElMessage.info('报表导出功能开发中...')
}

// 工具方法
const formatNumber = (num) => {
  if (!num) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const formatPercent = (rate) => {
  if (!rate) return '0%'
  return `${rate > 0 ? '+' : ''}${rate.toFixed(1)}%`
}

const getTrendClass = (rate) => {
  if (!rate) return 'neutral'
  return rate > 0 ? 'positive' : 'negative'
}

const getTrendIcon = (rate) => {
  if (!rate) return '→'
  return rate > 0 ? '↗' : '↘'
}
</script>

<style scoped>
.dashboard-home {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 概览统计卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.card-trend.positive {
  color: #67c23a;
}

.card-trend.negative {
  color: #f56c6c;
}

.card-trend.neutral {
  color: #909399;
}

.trend-icon {
  margin-right: 4px;
}

/* 图表容器 */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chart-content {
  padding: 20px;
}

.chart {
  width: 100%;
  height: 300px;
}

/* 实时数据面板 */
.realtime-panel {
  margin-top: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.update-time {
  font-size: 12px;
  opacity: 0.9;
}

.realtime-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
}

.realtime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.realtime-item .label {
  font-size: 14px;
  color: #666;
}

.realtime-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-home {
    padding: 12px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .realtime-stats {
    grid-template-columns: 1fr;
  }
}
</style>
