package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Agent测试控制器
 * 用于测试各种Agent功能
 */
@Slf4j
@RestController
@RequestMapping("/api/agent/test")
@RequiredArgsConstructor
public class AgentTestController {

    private final ProductAssistant productAssistant;
    private final ProductService productService;
    private final CategoryService categoryService;

    /**
     * 测试商品助手Agent
     */
    @GetMapping("/product")
    public ApiResponse<String> testProductAssistant(@RequestParam String query) {
        try {
            ProductAssistant.Request request = new ProductAssistant.Request(query);
            String response = productAssistant.apply(request);
            return ApiResponse.success("Agent响应成功", response);
        } catch (Exception e) {
            log.error("测试商品助手Agent失败", e);
            return ApiResponse.fail("Agent测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试商品搜索功能
     */
    @GetMapping("/product/search")
    public ApiResponse<String> testProductSearch(
            @RequestParam String keyword,
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String minPrice,
            @RequestParam(required = false) String maxPrice
    ) {
        try {
            String response = productAssistant.searchProducts(
                keyword, 
                categoryId, 
                minPrice != null ? new java.math.BigDecimal(minPrice) : null,
                maxPrice != null ? new java.math.BigDecimal(maxPrice) : null,
                null, null, 1, 5
            );
            return ApiResponse.success("搜索成功", response);
        } catch (Exception e) {
            log.error("测试商品搜索失败", e);
            return ApiResponse.fail("搜索测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试商品详情功能
     */
    @GetMapping("/product/{productId}")
    public ApiResponse<String> testProductDetail(@PathVariable Long productId) {
        try {
            String response = productAssistant.getProductDetail(productId);
            return ApiResponse.success("获取详情成功", response);
        } catch (Exception e) {
            log.error("测试商品详情失败", e);
            return ApiResponse.fail("详情测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试商品比较功能
     */
    @GetMapping("/product/compare")
    public ApiResponse<String> testProductCompare(@RequestParam String productIds) {
        try {
            String response = productAssistant.compareProducts(productIds);
            return ApiResponse.success("比较成功", response);
        } catch (Exception e) {
            log.error("测试商品比较失败", e);
            return ApiResponse.fail("比较测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试商品推荐功能
     */
    @GetMapping("/product/recommend/{productId}")
    public ApiResponse<String> testProductRecommend(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "5") Integer limit
    ) {
        try {
            String response = productAssistant.recommendSimilarProducts(productId, limit);
            return ApiResponse.success("推荐成功", response);
        } catch (Exception e) {
            log.error("测试商品推荐失败", e);
            return ApiResponse.fail("推荐测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试分类列表功能
     */
    @GetMapping("/categories")
    public ApiResponse<String> testCategoryList() {
        try {
            String response = productAssistant.getCategoryList();
            return ApiResponse.success("获取分类成功", response);
        } catch (Exception e) {
            log.error("测试分类列表失败", e);
            return ApiResponse.fail("分类测试失败：" + e.getMessage());
        }
    }


}
