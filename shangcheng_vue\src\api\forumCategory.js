import request from '@/utils/request'

/**
 * 获取所有论坛分类
 * @returns {Promise} Promise对象
 */
export function getAllForumCategories() {
  return request({
    url: '/forum/categories',
    method: 'get'
  })
}

/**
 * 根据ID获取论坛分类
 * @param {Number} id 分类ID
 * @returns {Promise} Promise对象
 */
export function getForumCategoryById(id) {
  return request({
    url: `/forum/categories/${id}`,
    method: 'get'
  })
}

/**
 * 创建论坛分类
 * @param {Object} data 分类数据
 * @returns {Promise} Promise对象
 */
export function createForumCategory(data) {
  return request({
    url: '/forum/categories',
    method: 'post',
    data
  })
}

/**
 * 更新论坛分类
 * @param {Number} id 分类ID
 * @param {Object} data 分类数据
 * @returns {Promise} Promise对象
 */
export function updateForumCategory(id, data) {
  return request({
    url: `/forum/categories/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除论坛分类
 * @param {Number} id 分类ID
 * @returns {Promise} Promise对象
 */
export function deleteForumCategory(id) {
  return request({
    url: `/forum/categories/${id}`,
    method: 'delete'
  })
}
