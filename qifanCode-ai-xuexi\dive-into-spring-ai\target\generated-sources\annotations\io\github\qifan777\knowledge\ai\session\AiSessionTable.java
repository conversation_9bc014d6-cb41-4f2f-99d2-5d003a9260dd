package io.github.qifan777.knowledge.ai.session;

import io.github.qifan777.knowledge.ai.messge.AiMessageTableEx;
import io.github.qifan777.knowledge.user.UserTable;
import java.lang.Deprecated;
import java.lang.Override;
import java.lang.String;
import java.time.LocalDateTime;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = AiSession.class
)
public class AiSessionTable extends AbstractTypedTable<AiSession> implements AiSessionProps {
    public static final AiSessionTable $ = new AiSessionTable();

    public AiSessionTable() {
        super(AiSession.class);
    }

    public AiSessionTable(AbstractTypedTable.DelayedOperation<AiSession> delayedOperation) {
        super(AiSession.class, delayedOperation);
    }

    public AiSessionTable(TableImplementor<AiSession> table) {
        super(table);
    }

    protected AiSessionTable(AiSessionTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Str id() {
        return __get(AiSessionProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<LocalDateTime> createdTime() {
        return __get(AiSessionProps.CREATED_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<LocalDateTime> editedTime() {
        return __get(AiSessionProps.EDITED_TIME.unwrap());
    }

    @Override
    public UserTable editor() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiSessionProps.EDITOR.unwrap()));
        }
        return new UserTable(joinOperation(AiSessionProps.EDITOR.unwrap()));
    }

    @Override
    public UserTable editor(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiSessionProps.EDITOR.unwrap(), joinType));
        }
        return new UserTable(joinOperation(AiSessionProps.EDITOR.unwrap(), joinType));
    }

    @Override
    public PropExpression.Str editorId() {
        return __getAssociatedId(AiSessionProps.EDITOR.unwrap());
    }

    @Override
    public UserTable creator() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiSessionProps.CREATOR.unwrap()));
        }
        return new UserTable(joinOperation(AiSessionProps.CREATOR.unwrap()));
    }

    @Override
    public UserTable creator(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiSessionProps.CREATOR.unwrap(), joinType));
        }
        return new UserTable(joinOperation(AiSessionProps.CREATOR.unwrap(), joinType));
    }

    @Override
    public PropExpression.Str creatorId() {
        return __getAssociatedId(AiSessionProps.CREATOR.unwrap());
    }

    @Override
    public PropExpression.Str name() {
        return __get(AiSessionProps.NAME.unwrap());
    }

    @Override
    public Predicate messages(Function<AiMessageTableEx, Predicate> block) {
        return exists(AiSessionProps.MESSAGES.unwrap(), block);
    }

    @Override
    public AiSessionTableEx asTableEx() {
        return new AiSessionTableEx(this, null);
    }

    @Override
    public AiSessionTable __disableJoin(String reason) {
        return new AiSessionTable(this, reason);
    }

    @GeneratedBy(
            type = AiSession.class
    )
    public static class Remote extends AbstractTypedTable<AiSession> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(AiSession.class, delayedOperation);
        }

        public Remote(TableImplementor<AiSession> table) {
            super(table);
        }

        public PropExpression.Str id() {
            return __get(AiSessionProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<AiSession> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
