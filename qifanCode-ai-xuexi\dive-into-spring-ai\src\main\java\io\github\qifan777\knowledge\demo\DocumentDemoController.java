package io.github.qifan777.knowledge.demo;




import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStreamReader;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("demo/document")
@RestController
@AllArgsConstructor
@Slf4j
public class DocumentDemoController {

    // 阿里嵌入模型
    private final EmbeddingModel embeddingModel;
    // 向量数据库
    private final VectorStore vectorStore;

    @PostMapping("embedding")
    public float[] embedding(@RequestParam String text) {
        return embeddingModel.embed(text);
    }

    @SneakyThrows
    @PostMapping("etl/reader/multipart")
    public String readFromMultipart(@RequestParam MultipartFile file) {
       Resource resource = new InputStreamResource(file.getInputStream());
       TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(resource);
       return tikaDocumentReader.read().get(0).getContent();
    }


    @SneakyThrows
    @PostMapping("etl/reader/local-file")
    public String readFromLocalFile (@RequestParam String file) {
        Resource resource = new FileSystemResource(file);
        TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(resource);
        return tikaDocumentReader.read().get(0).getContent();
    }


    @SneakyThrows
    @PostMapping("etl/treasfrom/split")
    public List<String> split(@RequestParam MultipartFile file) {
        Resource resource = new InputStreamResource(file.getInputStream());
        TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(resource);
        List<Document> read = tikaDocumentReader.read();
        List<Document> split = new TokenTextSplitter().split(read);
        return split.stream().map(Document::toString).toList();
    }

    @SneakyThrows
    @PostMapping("etl/write/vector")
    public void writeVector(@RequestParam MultipartFile file) {
        Resource resource = new InputStreamResource(file.getInputStream());
        TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(resource);
        List<Document> read = tikaDocumentReader.read();
        List<Document> split = new TokenTextSplitter().split(read);
        vectorStore.add(split);
    }

    @PostMapping("query")
    public List<Document> query(@RequestParam String text) {
        return vectorStore.similaritySearch(text);
    }
}
