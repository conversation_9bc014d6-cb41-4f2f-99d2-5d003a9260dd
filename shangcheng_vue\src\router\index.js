import { createRouter, createWebHistory } from 'vue-router'
import Login from '@/views/Login.vue'
import Register from '@/views/Register.vue'
import { useUserStore } from '@/stores/user'
import { useAdminStore } from '@/admin/stores/admin'
import { adminRoutes, adminRouteGuard } from '@/admin'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  // 路由守卫，需要登录才能访问的路由
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'UserProfile',
    component: () => import('@/views/UserProfile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/publish',
    name: 'ProductPublish',
    component: () => import('@/views/ProductPublish.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/ProductDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'ProductSearch',
    component: () => import('@/views/ProductSearch.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/seller/:id',
    name: 'SellerProfile',
    component: () => import('@/views/SellerProfile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/order/create/:productId',
    name: 'OrderCreate',
    component: () => import('@/views/OrderCreate.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/order/success',
    name: 'OrderSuccess',
    component: () => import('@/views/OrderSuccess.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/forum',
    name: 'Forum',
    component: () => import('@/views/Forum.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/forum/post/:id',
    name: 'ForumPostDetail',
    component: () => import('@/views/ForumPostDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/orders',
    name: 'OrderManagement',
    component: () => import('@/views/OrderManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/refunds',
    name: 'RefundManagement',
    component: () => import('@/views/RefundManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/refund/forced-tasks',
    name: 'ForcedRefundTasks',
    component: () => import('@/views/ForcedRefundTasks.vue'),
    meta: { requiresAuth: true }
  }
]

// 合并普通路由和管理员路由
const allRoutes = [...routes, ...adminRoutes]

const router = createRouter({
  history: createWebHistory(),
  routes: allRoutes
})

// 用于跟踪是否已经加载过用户信息
let userInfoLoaded = false;

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 检查是否是管理员路由
  if (to.path.startsWith('/admin')) {
    // 使用管理员路由守卫处理
    const adminStore = useAdminStore()
    adminRouteGuard(to, from, next, adminStore)
    return
  }

  // 检查路由是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查用户是否已登录
    const token = localStorage.getItem('token')
    if (!token) {
      // 未登录则跳转到登录页
      console.log('路由守卫: 未检测到token，跳转到登录页')
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原本要访问的路径
      })
    } else {
      // 已登录，获取用户信息
      const userStore = useUserStore()

      // 如果已经有用户信息且角色是普通用户，直接放行
      if (userStore.userInfo && userStore.userInfo.role === 'general_user') {
        console.log('路由守卫: 已有用户信息，直接放行', userStore.userInfo)
        next()
        return
      }

      // 没有用户信息，尝试获取
      if (!userInfoLoaded) {
        try {
          await userStore.fetchUserInfo()
          userInfoLoaded = true
          console.log('路由守卫: 用户信息预加载完成', userStore.userInfo)
        } catch (error) {
          console.error('路由守卫: 预加载用户信息失败', error)
          // 获取用户信息失败，可能是token无效，重定向到登录页
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
      }

      // 检查用户角色是否为普通用户
      if (userStore.userInfo && userStore.userInfo.role === 'general_user') {
        console.log('路由守卫: 普通用户验证通过，允许访问')
        next() // 是普通用户则放行
      } else {
        // 不是普通用户，提示无权限并重定向到登录页
        console.error('路由守卫: 用户角色验证失败', userStore.userInfo)
        alert('您没有权限访问此页面，请使用普通用户账号登录')
        userStore.logout() // 清除当前用户信息
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
      }
    }
  } else {
    next() // 不需要认证的路由直接放行
  }
})

export default router
