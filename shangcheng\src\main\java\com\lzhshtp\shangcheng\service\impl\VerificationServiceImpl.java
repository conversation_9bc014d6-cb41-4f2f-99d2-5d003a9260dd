package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.VerificationDTO;
import com.lzhshtp.shangcheng.dto.VerificationResultDTO;
import com.lzhshtp.shangcheng.mapper.*;
import com.lzhshtp.shangcheng.model.*;
import com.lzhshtp.shangcheng.service.VerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * 验货服务实现类
 */
@Service
public class VerificationServiceImpl implements VerificationService {

    private static final Logger logger = LoggerFactory.getLogger(VerificationServiceImpl.class);

    @Autowired
    private VerificationRecordMapper verificationRecordMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ProductMapper productMapper;

    /**
     * 创建验货记录
     */
    @Override
    public Long createVerificationRecord(Long orderId) {
        try {
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                throw new IllegalArgumentException("订单不存在");
            }

            // 检查是否已有验货记录
            VerificationRecord existing = verificationRecordMapper.selectByOrderId(orderId);
            if (existing != null) {
                return existing.getVerificationId();
            }

            // 创建验货记录
            VerificationRecord record = VerificationRecord.builder()
                    .orderId(orderId)
                    .verifierId(1L) // 默认验货员，实际应该分配
                    .verificationStatus(VerificationRecord.VerificationStatus.VERIFYING)
                    .createdTime(LocalDateTime.now())
                    .build();

            verificationRecordMapper.insert(record);

            logger.info("创建验货记录成功，订单ID：{}", orderId);
            return record.getVerificationId();
        } catch (Exception e) {
            logger.error("创建验货记录失败", e);
            throw e;
        }
    }

    // 移除了带参数的createVerificationRecord方法，统一使用无参数版本

    /**
     * 更新验货状态
     */
    @Override
    public boolean updateVerificationStatus(Long verificationId, String status) {
        try {
            VerificationRecord record = verificationRecordMapper.selectById(verificationId);
            if (record == null) {
                throw new IllegalArgumentException("验货记录不存在");
            }

            record.setVerificationStatus(status);

            if (VerificationRecord.VerificationStatus.VERIFYING.equals(status)) {
                record.setReceivedTime(LocalDateTime.now());
            }

            verificationRecordMapper.updateById(record);

            logger.info("更新验货状态成功，验货ID：{}，状态：{}", verificationId, status);
            return true;
        } catch (Exception e) {
            logger.error("更新验货状态失败", e);
            throw e;
        }
    }

    /**
     * 提交验货结果
     */
    @Override
    @Transactional
    public boolean submitVerificationResult(Long verifierId, VerificationResultDTO result) {
        try {
            VerificationRecord record = verificationRecordMapper.selectById(result.getVerificationId());
            if (record == null) {
                throw new IllegalArgumentException("验货记录不存在");
            }

            // 验证权限（暂时允许任何管理员处理验货记录）
            // 更新验货员ID为当前处理的管理员
            record.setVerifierId(verifierId);

            // 更新验货记录
            record.setVerificationResult(result.getResult());
            record.setVerificationImages(String.join(",", result.getImageUrls() != null ? result.getImageUrls() : new ArrayList<>()));
            record.setVerifiedTime(LocalDateTime.now());

            if (result.getPassed()) {
                record.setVerificationStatus(VerificationRecord.VerificationStatus.PASSED);
                verificationRecordMapper.updateById(record);

                // 验货通过，转发给买家
                return forwardToBuyer(result.getVerificationId());
            } else {
                record.setVerificationStatus(VerificationRecord.VerificationStatus.FAILED);
                verificationRecordMapper.updateById(record);

                // 验货失败，处理退款
                return handleVerificationFailure(result.getVerificationId());
            }
        } catch (Exception e) {
            logger.error("提交验货结果失败", e);
            throw e;
        }
    }

    /**
     * 验货通过，转发给买家
     */
    @Override
    public boolean forwardToBuyer(Long verificationId) {
        try {
            VerificationRecord record = verificationRecordMapper.selectById(verificationId);
            if (record == null) {
                throw new IllegalArgumentException("验货记录不存在");
            }

            // 更新验货状态
            record.setVerificationStatus(VerificationRecord.VerificationStatus.FORWARDED);
            record.setForwardedTime(LocalDateTime.now());
            verificationRecordMapper.updateById(record);

            // 更新订单状态为已送达（验货通过后直接送达买家）
            Order order = orderMapper.selectById(record.getOrderId());
            order.setStatus(Order.OrderStatus.DELIVERED);
            order.setCurrentLocation("已从官方验货中心发出，正在配送中");
            orderMapper.updateById(order);

            logger.info("验货通过，已转发给买家，订单ID：{}", record.getOrderId());
            return true;
        } catch (Exception e) {
            logger.error("转发给买家失败", e);
            throw e;
        }
    }

    /**
     * 验货失败，处理退款
     */
    @Override
    @Transactional
    public boolean handleVerificationFailure(Long verificationId) {
        try {
            VerificationRecord record = verificationRecordMapper.selectById(verificationId);
            if (record == null) {
                throw new IllegalArgumentException("验货记录不存在");
            }

            Order order = orderMapper.selectById(record.getOrderId());

            // 查询买家和卖家
            User buyer = userMapper.selectById(order.getBuyerId());
            User seller = userMapper.selectById(order.getSellerId());

            // 计算退款金额（只退商品金额，验货费已在发货时从卖家扣除）
            BigDecimal productAmount = order.getTotalAmount().subtract(order.getVerificationFee());
            BigDecimal refundAmount = productAmount;

            // 验货失败的资金处理：
            // 1. 买家收到商品金额退款
            // 2. 卖家承担商品金额损失（因为商品有问题）
            // 3. 验货费处理根据付费方决定

            // 无论谁付费验货，卖家都需要承担商品退款（因为商品有问题）
            BigDecimal sellerBalance = seller.getBalance() != null ? seller.getBalance() : BigDecimal.ZERO;
            BigDecimal newSellerBalance = sellerBalance.subtract(refundAmount);
            seller.setBalance(newSellerBalance);
            userMapper.updateById(seller);

            if ("seller".equals(order.getVerificationPayer())) {
                logger.info("卖家付费验货失败，卖家承担商品退款：{}，验货费已在发货时扣除", refundAmount);
            } else {
                logger.info("买家付费验货失败，卖家承担商品退款：{}，验货费不退还", refundAmount);
            }

            // 增加买家余额（买家收到商品退款）
            BigDecimal buyerBalance = buyer.getBalance() != null ? buyer.getBalance() : BigDecimal.ZERO;
            BigDecimal newBuyerBalance = buyerBalance.add(refundAmount);
            buyer.setBalance(newBuyerBalance);
            userMapper.updateById(buyer);

            // 更新订单状态为已取消（验货失败）
            order.setStatus(Order.OrderStatus.CANCELLED);
            order.setCurrentLocation("验货失败，订单已取消");
            orderMapper.updateById(order);

            logger.info("验货失败，订单已取消，订单ID：{}，买家退款：{}，卖家扣款：{}，验货费已在发货时处理",
                record.getOrderId(), refundAmount, refundAmount);
            return true;
        } catch (Exception e) {
            logger.error("处理验货失败退款失败", e);
            throw e;
        }
    }

    /**
     * 查询待验货列表
     */
    @Override
    public List<VerificationDTO> getPendingVerifications(Long verifierId) {
        List<VerificationRecord> records;

        if (verifierId != null) {
            records = verificationRecordMapper.selectPendingByVerifierId(verifierId);
        } else {
            records = verificationRecordMapper.selectAllPending();
        }

        return convertToDTO(records);
    }

    /**
     * 根据订单ID查询验货记录
     */
    @Override
    public VerificationDTO getVerificationByOrderId(Long orderId) {
        VerificationRecord record = verificationRecordMapper.selectByOrderId(orderId);
        if (record == null) {
            return null;
        }

        return convertToDTO(record);
    }

    /**
     * 转换为DTO
     */
    private List<VerificationDTO> convertToDTO(List<VerificationRecord> records) {
        List<VerificationDTO> result = new ArrayList<>();

        for (VerificationRecord record : records) {
            result.add(convertToDTO(record));
        }

        return result;
    }

    private VerificationDTO convertToDTO(VerificationRecord record) {
        // 查询关联信息
        Order order = orderMapper.selectById(record.getOrderId());
        Product product = productMapper.selectById(order.getProductId());
        User buyer = userMapper.selectById(order.getBuyerId());
        User seller = userMapper.selectById(order.getSellerId());
        User verifier = userMapper.selectById(record.getVerifierId());

        return VerificationDTO.builder()
                .verificationId(record.getVerificationId())
                .orderId(record.getOrderId())
                .verifierId(record.getVerifierId())
                .verificationStatus(record.getVerificationStatus())
                .verificationResult(record.getVerificationResult())
                .verificationImages(record.getVerificationImages() != null ?
                        List.of(record.getVerificationImages().split(",")) : new ArrayList<>())
                .receivedTime(record.getReceivedTime())
                .verifiedTime(record.getVerifiedTime())
                .forwardedTime(record.getForwardedTime())
                .createdTime(record.getCreatedTime())
                .verifierName(verifier != null ? verifier.getUsername() : "未分配")
                .productTitle(product.getTitle())
                .buyerName(buyer.getUsername())
                .sellerName(seller.getUsername())
                .build();
    }

    /**
     * 管理员查询验货记录列表
     */
    @Override
    public List<VerificationDTO> getAdminVerificationList(int page, int pageSize, String status, String verificationPayer, String keyword) {
        try {
            // 构建查询条件
            List<VerificationRecord> records;

            if (status != null && !status.trim().isEmpty()) {
                // 按状态筛选
                records = verificationRecordMapper.selectList(
                    new QueryWrapper<VerificationRecord>()
                        .eq("lzhshtp_verification_status", status)
                        .orderByDesc("lzhshtp_created_time")
                );
            } else {
                // 查询所有
                records = verificationRecordMapper.selectList(
                    new QueryWrapper<VerificationRecord>()
                        .orderByDesc("lzhshtp_created_time")
                );
            }

            // 如果有付费方筛选，需要从订单中获取验货付费方信息
            if (verificationPayer != null && !verificationPayer.trim().isEmpty()) {
                records = records.stream()
                    .filter(record -> {
                        try {
                            Order order = orderMapper.selectById(record.getOrderId());
                            if (order != null) {
                                String orderVerificationPayer = order.getOfficialVerification() ? "buyer" : "seller";
                                return verificationPayer.equals(orderVerificationPayer);
                            }
                            return false;
                        } catch (Exception e) {
                            logger.error("获取订单验货付费方信息失败，订单ID: {}", record.getOrderId(), e);
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
            }

            // 如果有关键词搜索，进一步过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                // 这里可以根据需要添加关键词搜索逻辑
                // 比如搜索商品标题、买家名称等
            }

            return convertToDTO(records);
        } catch (Exception e) {
            logger.error("管理员查询验货记录列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 管理员查询验货统计数据
     */
    @Override
    public Object getAdminVerificationStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总验货记录数
            long totalCount = verificationRecordMapper.selectCount(null);
            statistics.put("totalCount", totalCount);

            // 待验货数
            long waitingCount = verificationRecordMapper.selectCount(
                new QueryWrapper<VerificationRecord>()
                    .eq("lzhshtp_verification_status", "waiting_goods")
            );
            statistics.put("waitingCount", waitingCount);

            // 验货中数
            long verifyingCount = verificationRecordMapper.selectCount(
                new QueryWrapper<VerificationRecord>()
                    .eq("lzhshtp_verification_status", "verifying")
            );
            statistics.put("verifyingCount", verifyingCount);

            // 验货通过数
            long passedCount = verificationRecordMapper.selectCount(
                new QueryWrapper<VerificationRecord>()
                    .eq("lzhshtp_verification_status", "passed")
            );
            statistics.put("passedCount", passedCount);

            // 验货失败数
            long failedCount = verificationRecordMapper.selectCount(
                new QueryWrapper<VerificationRecord>()
                    .eq("lzhshtp_verification_status", "failed")
            );
            statistics.put("failedCount", failedCount);

            // 注意：付费方信息需要从订单中获取，暂时不在统计中显示
            // 可以通过关联查询订单表来获取付费方统计，但为了简化暂时移除
            statistics.put("buyerPayCount", 0);
            statistics.put("sellerPayCount", 0);

            return statistics;
        } catch (Exception e) {
            logger.error("管理员查询验货统计数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 更新验货记录状态为已收货（商品送达验货中心）
     */
    @Override
    @Transactional
    public boolean updateVerificationStatusToReceived(Long orderId) {
        try {
            logger.info("开始更新验货记录状态，订单ID: {}", orderId);

            // 根据订单ID查找验货记录
            QueryWrapper<VerificationRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("lzhshtp_order_id", orderId);
            VerificationRecord record = verificationRecordMapper.selectOne(queryWrapper);

            if (record == null) {
                logger.error("未找到订单 {} 的验货记录", orderId);
                return false;
            }

            logger.info("找到验货记录: ID={}, 当前状态={}", record.getVerificationId(), record.getVerificationStatus());

            // 检查当前状态（验货记录刚创建时应该是waiting_goods）
            if (!"waiting_goods".equals(record.getVerificationStatus())) {
                logger.warn("验货记录 {} 状态不是等待收货，当前状态：{}，继续更新",
                    record.getVerificationId(), record.getVerificationStatus());
                // 继续更新状态，因为可能是重复调用
            }

            // 更新状态为验货中，并设置收货时间
            record.setVerificationStatus("verifying");
            record.setReceivedTime(LocalDateTime.now());

            int result = verificationRecordMapper.updateById(record);
            logger.info("验货记录更新结果: {}, 记录ID: {}", result, record.getVerificationId());

            if (result > 0) {
                logger.info("验货记录 {} 状态已更新为验货中", record.getVerificationId());
                return true;
            } else {
                logger.error("更新验货记录 {} 状态失败", record.getVerificationId());
                return false;
            }

        } catch (Exception e) {
            logger.error("更新验货记录状态失败，订单ID: {}", orderId, e);
            return false;
        }
    }
}
