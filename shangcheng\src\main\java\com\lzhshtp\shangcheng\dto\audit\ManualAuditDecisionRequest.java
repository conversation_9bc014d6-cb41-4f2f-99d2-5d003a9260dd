package com.lzhshtp.shangcheng.dto.audit;

import lombok.Data;

/**
 * 人工审核决策请求
 */
@Data
public class ManualAuditDecisionRequest {
    
    /**
     * 管理员ID
     */
    private Long adminId;
    
    /**
     * 审核决策
     * approved: 通过
     * rejected: 拒绝
     * request_materials: 要求补充材料
     * escalate_to_second_review: 升级到二度复审
     */
    private String decision;
    
    /**
     * 审核意见
     */
    private String comments;
    
    /**
     * 要求补充的材料列表（当decision为request_materials时使用）
     */
    private String requiredMaterials;
}
