spring:
  application:
    name: knowledge-base
  profiles:
    active: dev,private
  data:
    redis:
      database: 0
      timeout: 10s
      password: 123456
      repositories:
        enabled: false
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0

  datasource:
    url: ******************************************
    username: root
    password: 1234
    driver-class-name: com.mysql.cj.jdbc.Driver
  ai:
    # 阿里灵积
    dash-scope:
      api-key: sk-38560676cdd14b8f89ff34405e044645
      enabled: true
      chat:
        model: qwen-max
      embedding:
        model: text-embedding-v2
    vectorstore:
      redis:
        initialize-schema: true
        index-name: default
        prefix: default



server:
  port: 9902

sa-token:
  # token名称 (同时也是cookie名称)
  token-name: token
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 自动续签
  auto-renew: true


oss:
  provider: ali_yun
  # 阿里云oss配置
  ali-yun:
    access-key-id: xxx
    access-key-secret: xxx
    endpoint: xxx
    bucket-name: xxx
jimmer:
  dialect: org.babyfish.jimmer.sql.dialect.MySqlDialect
  show-sql: true
  pretty-sql: true
  client:
    ts:
      path: /ts.zip
    openapi:
      path: /openapi
      ui-path: /openapi-ui
