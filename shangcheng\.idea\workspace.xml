<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1d80e941-3ec1-481a-9c05-e60914caa126" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../shangcheng_vue/src/admin/views/verifications/VerificationManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../shangcheng_vue/src/admin/views/verifications/VerificationManagement.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.1-bin\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="19" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zLT3wGtRNf5t5tfI5Fg7NlEGMI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.shangcheng [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.shangcheng [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.shangcheng [validate].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ShangchengApplication (1).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ShangchengApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.ShangchengApplication (1)">
    <configuration name="ShangchengApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="shangcheng" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lzhshtp.shangcheng.ShangchengApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.lzhshtp.shangcheng.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ShangchengApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="shangcheng" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.shangcheng.ShangchengApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ShangchengApplication (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1d80e941-3ec1-481a-9c05-e60914caa126" name="Changes" comment="" />
      <created>1751509157264</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751509157264</updated>
      <workItem from="1751509158400" duration="229000" />
      <workItem from="1751510019968" duration="15818000" />
      <workItem from="1751561566542" duration="16612000" />
      <workItem from="1751723610527" duration="17000" />
      <workItem from="1751729529796" duration="29570000" />
      <workItem from="1751816502786" duration="15788000" />
      <workItem from="1751908376962" duration="18922000" />
      <workItem from="1752024637912" duration="13642000" />
      <workItem from="1752078110772" duration="8791000" />
      <workItem from="1752117803348" duration="3009000" />
      <workItem from="1752170191932" duration="3433000" />
      <workItem from="1752203555102" duration="5944000" />
      <workItem from="1752350023622" duration="6965000" />
      <workItem from="1752398512914" duration="9025000" />
      <workItem from="1752433972616" duration="10907000" />
      <workItem from="1752455148942" duration="16295000" />
      <workItem from="1752517548816" duration="5601000" />
      <workItem from="1752542764417" duration="8273000" />
      <workItem from="1752561303485" duration="8839000" />
      <workItem from="1752606293494" duration="4053000" />
      <workItem from="1752629137975" duration="7058000" />
      <workItem from="1752669742105" duration="1783000" />
      <workItem from="1752672915778" duration="12000" />
      <workItem from="1752678896059" duration="6264000" />
      <workItem from="1752688487958" duration="13747000" />
      <workItem from="1752725276470" duration="21331000" />
      <workItem from="1752771030764" duration="2627000" />
      <workItem from="1752774303922" duration="1497000" />
      <workItem from="1752775853306" duration="19533000" />
      <workItem from="1753019838028" duration="9194000" />
      <workItem from="1753053121263" duration="436000" />
      <workItem from="1753054256948" duration="15985000" />
      <workItem from="1753098587700" duration="1790000" />
      <workItem from="1753266340887" duration="1384000" />
      <workItem from="1753356365403" duration="5505000" />
      <workItem from="1753362856202" duration="660000" />
      <workItem from="1753364013339" duration="13448000" />
      <workItem from="1753401867878" duration="637000" />
      <workItem from="1753404156266" duration="1526000" />
      <workItem from="1753406810862" duration="20351000" />
      <workItem from="1753457612352" duration="20766000" />
      <workItem from="1753545519467" duration="4801000" />
      <workItem from="1753552150241" duration="9127000" />
      <workItem from="1753629680751" duration="17041000" />
      <workItem from="1753673851891" duration="29111000" />
      <workItem from="1753727499426" duration="5132000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/lzhshtp/shangcheng/service/impl/ProductReviewServiceImpl.java</url>
          <line>57</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/lzhshtp/shangcheng/service/AdminStatisticsService.java</url>
          <line>73</line>
          <properties class="com.lzhshtp.shangcheng.service.AdminStatisticsService" method="getRealtimeStatistics">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="allSensitiveWords" />
        <watch expression="allSensitiveWords" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
</project>