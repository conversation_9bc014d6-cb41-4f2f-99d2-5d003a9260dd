package com.lzhshtp.shangcheng.controller.user;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import com.lzhshtp.shangcheng.model.SupplementaryMaterial;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.service.audit.MaterialRequestService;
import com.lzhshtp.shangcheng.service.audit.SupplementaryMaterialService;
import com.lzhshtp.shangcheng.utils.OssUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户材料管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserMaterialController {

    @Autowired
    private MaterialRequestService materialRequestService;

    @Autowired
    private SupplementaryMaterialService supplementaryMaterialService;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private UserService userService;

    /**
     * 获取当前用户的材料请求列表
     */
    @GetMapping("/material-requests")
    public ApiResponse<List<MaterialRequest>> getUserMaterialRequests(Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            log.info("获取用户材料请求，用户ID: {}", user.getUserId());

            List<MaterialRequest> requests = materialRequestService.getMaterialRequestsByUser(user.getUserId());

            return ApiResponse.success("获取材料请求成功", requests);
        } catch (Exception e) {
            log.error("获取用户材料请求失败", e);
            return ApiResponse.fail("获取材料请求失败：" + e.getMessage());
        }
    }

    /**
     * 获取材料请求详情
     */
    @GetMapping("/material-requests/{requestId}")
    public ApiResponse<MaterialRequest> getMaterialRequestDetail(
            @PathVariable Long requestId,
            Authentication authentication) {
        try {

            String username = authentication.getName();
            User user = userService.findByUsername(username);

            Long userId = user.getUserId();
            log.info("获取材料请求详情，请求ID: {}, 用户ID: {}", requestId, userId);

            MaterialRequest request = materialRequestService.getMaterialRequestDetail(requestId, userId);

            if (request == null) {
                return ApiResponse.fail("材料请求不存在或无权访问");
            }

            return ApiResponse.success("获取材料请求详情成功", request);
        } catch (Exception e) {
            log.error("获取材料请求详情失败，请求ID: {}", requestId, e);
            return ApiResponse.fail("获取材料请求详情失败：" + e.getMessage());
        }
    }

    /**
     * 提交补充材料
     */
    @PostMapping("/supplementary-materials")
    public ApiResponse<SupplementaryMaterial> submitSupplementaryMaterial(
            @RequestParam Long requestId,
            @RequestParam String materialType,
            @RequestParam String description,
            @RequestParam("files") MultipartFile[] files,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            Long userId = user.getUserId();
            log.info("提交补充材料，请求ID: {}, 用户ID: {}, 材料类型: {}", requestId, userId, materialType);

            // 验证材料请求是否属于当前用户
            MaterialRequest request = materialRequestService.getMaterialRequestDetail(requestId, userId);
            if (request == null) {
                return ApiResponse.fail("材料请求不存在或无权访问");
            }

            // 上传文件
            List<String> fileUrls = new ArrayList<>();
            if (files != null) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        String fileUrl = ossUtil.uploadFile(file, "materials");
                        fileUrls.add(fileUrl);
                    }
                }
            }

            // 将文件URL列表转换为JSON字符串
            String materialUrlsJson;
            try {
                materialUrlsJson = objectMapper.writeValueAsString(fileUrls);
            } catch (Exception e) {
                log.error("转换文件URL为JSON失败", e);
                return ApiResponse.fail("文件URL格式转换失败");
            }

            // 创建补充材料
            SupplementaryMaterial material = supplementaryMaterialService.createSupplementaryMaterial(
                request.getProductId(),
                requestId,
                userId,
                materialType,
                materialUrlsJson,
                description
            );

            // 更新材料请求状态为已提交
            materialRequestService.updateMaterialRequestStatus(requestId, "submitted");

            return ApiResponse.success("提交材料成功", material);
        } catch (Exception e) {
            log.error("提交补充材料失败，请求ID: {}", requestId, e);
            return ApiResponse.fail("提交材料失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户提交的补充材料
     */
    @GetMapping("/supplementary-materials")
    public ApiResponse<List<SupplementaryMaterial>> getUserSupplementaryMaterials(
            @RequestParam(required = false) Long requestId,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);

            Long userId = user.getUserId();
            log.info("获取用户补充材料，用户ID: {}, 请求ID: {}", userId, requestId);

            List<SupplementaryMaterial> materials;
            if (requestId != null) {
                materials = supplementaryMaterialService.getMaterialsByRequestId(requestId);
                // 过滤出属于当前用户的材料
                materials = materials.stream()
                    .filter(material -> userId.equals(material.getSellerId()))
                    .toList();
            } else {
                materials = supplementaryMaterialService.getMaterialsBySellerId(userId);
            }

            return ApiResponse.success("获取补充材料成功", materials);
        } catch (Exception e) {
            log.error("获取用户补充材料失败", e);
            return ApiResponse.fail("获取补充材料失败：" + e.getMessage());
        }
    }

    /**
     * 删除补充材料
     */
    @DeleteMapping("/supplementary-materials/{materialId}")
    public ApiResponse<Void> deleteSupplementaryMaterial(
            @PathVariable Long materialId,
            Authentication authentication) {
        try {

            String username = authentication.getName();
            User user = userService.findByUsername(username);

            Long userId = user.getUserId();
            log.info("删除补充材料，材料ID: {}, 用户ID: {}", materialId, userId);

            // 验证材料是否属于当前用户
            SupplementaryMaterial material = supplementaryMaterialService.getMaterialById(materialId);
            if (material == null || !userId.equals(material.getSellerId())) {
                return ApiResponse.fail("材料不存在或无权删除");
            }

            supplementaryMaterialService.deleteMaterial(materialId);

            return ApiResponse.successVoid("删除材料成功");
        } catch (Exception e) {
            log.error("删除补充材料失败，材料ID: {}", materialId, e);
            return ApiResponse.fail("删除材料失败：" + e.getMessage());
        }
    }
}
