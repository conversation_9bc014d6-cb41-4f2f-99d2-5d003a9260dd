<template>
  <div class="home-page-full">
    <!-- 顶部导航 -->
    <nav class="top-nav">
        <div class="container nav-content">
            <router-link to="/home" class="logo">易转</router-link>
            <div class="search-area">
                <div class="search-bar">
                    <input 
                      v-model="searchKeyword" 
                      type="text" 
                      class="search-input" 
                      placeholder="搜索想要的商品..."
                      @keyup.enter="goToSearch"
                    >
                    <button class="search-button" @click="goToSearch">搜索</button>
                </div>
                <div class="search-tags">
                    <span class="search-tags-label">热门：</span>
                    <a v-for="keyword in hotKeywords" :key="keyword" href="#" @click.prevent="quickSearchLink(keyword)">{{ keyword }}</a>
                </div>
            </div>
            <div class="nav-right">
                <router-link to="/profile" class="user-avatar" title="个人中心">
                    <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
                </router-link>
            </div>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container">
        <div class="main-content-card reveal">
            <!-- 频道分区 -->
            <section class="channels-section">
                <div class="large-channel-card">
                    <div>
                        <h2>闲置抄底好物</h2>
                        <p>超绝性价比, 省到底</p>
                    </div>
                    <a href="#" class="cta-button">去看看 ></a>
                </div>
                <div class="small-channels-grid">

                    <div v-for="card in promoCards" :key="card.title" :class="['channel-card', card.bgColorClass]">
                        <div><h3>{{ card.title }}</h3><p>{{ card.subtitle }}</p></div>
                        <div class="products">
                            <div class="product-item"><img :src="card.img1" alt=""><span class="price">¥{{ card.price1 }}</span></div>
                            <div class="product-item"><img :src="card.img2" alt=""><span class="price">¥{{ card.price2 }}</span></div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <div class="main-content-card reveal">
          <!-- 兴趣标签 -->
          <section class="interest-tags">
              <a href="#" class="interest-tag active" @click="switchCategory(0, $event)">猜你喜欢</a>
              <a v-for="category in categories" :key="category.id" href="#" class="interest-tag" @click="switchCategory(category.id, $event)">{{ category.name }}</a>
          </section>

          <!-- 筛选条件 -->
          <section class="filter-section">
            <div class="filter-row">
              <!-- 新旧程度筛选 -->
              <div class="filter-group">
                <span class="filter-label">新旧程度：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !filters.condition }]"
                    @click="toggleFilter('condition', null)">
                    不限
                  </button>
                  <button
                    v-for="condition in conditionOptions"
                    :key="condition.value"
                    :class="['filter-btn', { active: filters.condition === condition.value }]"
                    @click="toggleFilter('condition', condition.value)">
                    {{ condition.label }}
                  </button>
                </div>
              </div>

              <!-- 交易方式筛选 -->
              <div class="filter-group">
                <span class="filter-label">交易方式：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !filters.deliveryMethod }]"
                    @click="toggleFilter('deliveryMethod', null)">
                    不限
                  </button>
                  <button
                    v-for="method in deliveryMethodOptions"
                    :key="method.value"
                    :class="['filter-btn', { active: filters.deliveryMethod === method.value }]"
                    @click="toggleFilter('deliveryMethod', method.value)">
                    {{ method.label }}
                  </button>
                </div>
              </div>

              <!-- 区域筛选 -->
              <div class="filter-group">
                <span class="filter-label">区域：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !filters.location }]"
                    @click="toggleFilter('location', null)">
                    不限
                  </button>
                  <button
                    :class="['filter-btn', { active: filters.location === 'same_city' }]"
                    @click="toggleFilter('location', 'same_city')">
                    同城
                  </button>
                  <el-select
                    v-model="filters.location"
                    placeholder="选择城市"
                    clearable
                    @change="onLocationChange"
                    style="width: 120px; margin-left: 8px;">
                    <el-option
                      v-for="city in cityOptions"
                      :key="city.value"
                      :label="city.label"
                      :value="city.value">
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 当前筛选条件显示 -->
            <div v-if="hasActiveFilters" class="active-filters">
              <span class="active-filters-label">当前筛选：</span>
              <el-tag
                v-if="filters.condition"
                closable
                @close="toggleFilter('condition', null)"
                type="primary">
                {{ getConditionLabel(filters.condition) }}
              </el-tag>
              <el-tag
                v-if="filters.deliveryMethod"
                closable
                @close="toggleFilter('deliveryMethod', null)"
                type="success">
                {{ getDeliveryMethodLabel(filters.deliveryMethod) }}
              </el-tag>
              <el-tag
                v-if="filters.location"
                closable
                @close="toggleFilter('location', null)"
                type="warning">
                {{ getLocationLabel(filters.location) }}
              </el-tag>
              <el-button
                size="small"
                type="danger"
                link
                @click="clearAllFilters">
                清空筛选
              </el-button>
            </div>
          </section>

          <!-- 加载指示器 -->
          <div v-if="loading" class="loading-indicator">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>

          <!-- 商品瀑布流 -->
          <section v-else class="products-feed">
              <div class="product-card" v-for="product in products" :key="product.id" @click="goToProductDetail(product.id)">
                  <img :src="product.image" :alt="product.name">
                  <div class="product-info">
                      <h3 class="product-title">{{ product.name }}</h3>
                      <div class="product-meta">
                          <span class="product-price"><span>¥</span>{{ product.price }}</span>
                          <div class="product-seller">
                              <img :src="product.sellerAvatar" :alt="product.sellerName">
                              <span>{{ product.sellerName }}</span>
                          </div>
                      </div>
                  </div>
              </div>
              
              <!-- 空状态 -->
              <div v-if="products.length === 0" class="empty-state">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
                <p>暂无商品数据</p>
              </div>
              
          </section>
                        <!-- 加载更多按钮 -->
                        <div v-if="products.length > 0" class="load-more-container">
                <div v-if="loading" class="loading-indicator-inline">
                  <div class="spinner-small"></div>
                  <span>加载中...</span>
                </div>
                <button v-else-if="hasMoreProducts" @click="loadMoreProducts" class="load-more-btn">
                  加载更多商品
                </button>
                <div v-else class="no-more-products">
                  已经到底啦，没有更多商品了~
                </div>
              </div>
        </div>
    </main>

    <!-- 悬浮操作按钮-->
    <FloatingActionButtons />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getAllCategories } from '@/api/category';
import { getProducts } from '@/api/product';
import { useUserStore } from '@/stores/user'
import { getHotSearchKeywords } from '@/api/search';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import FloatingActionButtons from '@/components/FloatingActionButtons.vue';

// 路由实例
const router = useRouter();

// 用户状态管理
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 确保用户信息已加载
const ensureUserInfo = async () => {
  if (!userStore.userInfo) {
    console.log('Home页面: 用户信息未加载，尝试获取');
    await userStore.fetchUserInfo();
  } else {
    console.log('Home页面: 用户信息已存在', userStore.userInfo);
  }
};

// 快速链接数据
const quickLinks = ref(['穿戴甲', '电动车', '打印机', '手机挂绳', '休闲裤', '洞洞鞋']);

// 推广卡片数据
const promoCards = ref([
  { title: '衣橱捡漏', subtitle: '时尚美衣低价淘', price1: '169', price2: '199', img1: 'https://via.placeholder.com/100x100', img2: 'https://via.placeholder.com/100x100', bgColorClass: 'bg-red-light' },
  { title: '手机数码', subtitle: '热门装备省心入', price1: '108', price2: '999', img1: 'https://via.placeholder.com/100x100', img2: 'https://via.placeholder.com/100x100', bgColorClass: 'bg-blue-light' },
  { title: '二次元', subtitle: '热门新品随手入', price1: '60', price2: '5', img1: 'https://via.placeholder.com/100x100', img2: 'https://via.placeholder.com/100x100', bgColorClass: 'bg-green-light' },
  { title: '省钱卡券', subtitle: '吃喝玩乐放心购', price1: '3.99', price2: '4000', img1: 'https://via.placeholder.com/100x100', img2: 'https://via.placeholder.com/100x100', bgColorClass: 'bg-yellow-light' },
]);

// 分类数据
const categories = ref([]);
// 当前选中的分类ID (0表示"猜你喜欢"，即全部商品)
const currentCategoryId = ref(0);

// 筛选条件
const filters = ref({
  condition: null,        // 新旧程度
  deliveryMethod: null,   // 交易方式
  location: null          // 区域
});

// 筛选选项数据
const conditionOptions = ref([
  { value: '全新', label: '全新' },
  { value: '九成新', label: '九成新' },
  { value: '八成新', label: '八成新' },
  { value: '七成新', label: '七成新' },
  { value: '六成新及以下', label: '六成新及以下' }
]);

const deliveryMethodOptions = ref([
  { value: '同城配送', label: '同城配送' },
  { value: '快递邮寄', label: '快递邮寄' },
  { value: '线下自提', label: '线下自提' }
]);

const cityOptions = ref([
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '杭州', label: '杭州' },
  { value: '南京', label: '南京' },
  { value: '武汉', label: '武汉' },
  { value: '成都', label: '成都' },
  { value: '西安', label: '西安' },
  { value: '重庆', label: '重庆' }
]);

// 商品列表数据
const products = ref([]);
// 加载状态
const loading = ref(false);
// 分页参数
const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreProducts = ref(true);

// 搜索关键词
const searchKeyword = ref('');

// 热门搜索词
const hotKeywords = ref(['数码产品', '服装配饰', '家居用品', '运动户外']);

// 计算属性：是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return filters.value.condition || filters.value.deliveryMethod || filters.value.location;
});

// 获取分类数据
const fetchCategories = async () => {
  try {
    const response = await getAllCategories();
    if (response && response.code === 200 && response.data) {
      // 只显示顶级分类（parentId为空或0的分类）
      categories.value = response.data.filter(item => !item.parentId || item.parentId === 0);
    }
  } catch (error) {
    console.error('获取分类数据失败:', error);
  }
};

// 获取热门搜索词
const fetchHotKeywords = async () => {
  try {
    const response = await getHotSearchKeywords(8); // 获取8个热门搜索词
    if (response && response.code === 200 && response.data) {
      hotKeywords.value = response.data;
    }
  } catch (error) {
    console.warn('获取热门搜索词失败，使用默认值:', error);
    // 保持默认值
  }
};

// 获取商品数据
const fetchProducts = async (categoryId = 0, isLoadMore = false) => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: 'AVAILABLE' // 只获取可用商品
    };
    
    // 如果选择了特定分类，添加分类ID过滤
    if (categoryId > 0) {
      params.categoryId = categoryId;
    }

    // 添加筛选条件
    if (filters.value.condition) {
      params.condition = filters.value.condition;
    }

    if (filters.value.deliveryMethod) {
      params.deliveryMethod = filters.value.deliveryMethod;
    }

    if (filters.value.location) {
      if (filters.value.location === 'same_city') {
        // 同城筛选 - 使用用户的location字段
        if (userInfo.value && userInfo.value.location) {
          params.location = userInfo.value.location;
          console.log('设置同城筛选，用户所在地:', params.location);
        } else {
          console.warn('用户未设置所在地，无法进行同城筛选');
          // 提示用户设置所在地
          ElMessage.warning('请先在个人中心设置您的所在地，才能使用同城筛选功能');
          // 重置同城筛选状态
          filters.value.location = null;
          return;
        }
      } else {
        params.location = filters.value.location;
        console.log('设置地区筛选:', params.location);
      }
    }

    const response = await getProducts(params);
    if (response && response.code === 200 && response.data) {
      // 转换返回的商品数据以适应前端显示格式
      if (response.data.records && response.data.records.length > 0) {
        const newProducts = response.data.records.map(item => ({
          id: item.id,
          name: item.title,
          price: item.price,
          image: item.imageUrls && item.imageUrls.length > 0 ? item.imageUrls[0] : 'https://via.placeholder.com/300x300',
          sellerName: item.sellerName || '未知卖家',
          sellerAvatar: item.sellerAvatar || defaultAvatar.value
        }));
        
        // 如果是加载更多，则追加到现有列表，否则替换
        if (isLoadMore) {
          products.value = [...products.value, ...newProducts];
        } else {
          products.value = newProducts;
        }
        
        // 判断是否还有更多数据
        hasMoreProducts.value = response.data.records.length === pageSize.value && 
                              currentPage.value < response.data.pages;
      } else if (!isLoadMore) {
        // 空列表情况（仅在非加载更多时清空）
        products.value = [];
        hasMoreProducts.value = false;
      } else {
        // 加载更多但没有数据，说明没有更多了
        hasMoreProducts.value = false;
      }
    } else {
      // 响应格式不正确或状态码非200
      if (!isLoadMore) {
        products.value = [];
      }
      hasMoreProducts.value = false;
      console.warn('获取商品数据格式不正确或无数据');
    }
  } catch (error) {
    console.error('获取商品数据失败:', error);
    if (!isLoadMore) {
      products.value = [];
    }
    hasMoreProducts.value = false;
  } finally {
    loading.value = false;
  }
};

// 切换分类
const switchCategory = (categoryId, event) => {
  // 阻止默认行为
  event.preventDefault();

  // 更新当前分类ID
  currentCategoryId.value = categoryId;

  // 重置分页参数
  currentPage.value = 1;
  hasMoreProducts.value = true;

  // 获取对应分类的商品
  fetchProducts(categoryId, false);

  // 更新激活状态
  document.querySelectorAll('.interest-tag').forEach(tag => {
    tag.classList.remove('active');
  });
  event.target.classList.add('active');
};

// 筛选相关方法
const toggleFilter = (filterType, value) => {
  filters.value[filterType] = filters.value[filterType] === value ? null : value;

  // 重置分页并重新获取商品
  currentPage.value = 1;
  hasMoreProducts.value = true;
  products.value = [];

  fetchProducts(currentCategoryId.value, false);
};

const onLocationChange = (value) => {
  filters.value.location = value;

  // 重置分页并重新获取商品
  currentPage.value = 1;
  hasMoreProducts.value = true;
  products.value = [];

  fetchProducts(currentCategoryId.value, false);
};

const clearAllFilters = () => {
  filters.value = {
    condition: null,
    deliveryMethod: null,
    location: null
  };

  // 重置分页并重新获取商品
  currentPage.value = 1;
  hasMoreProducts.value = true;
  products.value = [];

  fetchProducts(currentCategoryId.value, false);
};

// 获取标签显示文本的方法
const getConditionLabel = (value) => {
  const option = conditionOptions.value.find(opt => opt.value === value);
  return option ? option.label : value;
};

const getDeliveryMethodLabel = (value) => {
  const option = deliveryMethodOptions.value.find(opt => opt.value === value);
  return option ? option.label : value;
};

const getLocationLabel = (value) => {
  if (value === 'same_city') return '同城';
  // 现在value和label都是中文，直接返回即可
  return value;
};

// 添加goToProductDetail方法
const goToProductDetail = (productId) => {
  router.push(`/product/${productId}`);
};

// 加载更多商品
const loadMoreProducts = () => {
  if (loading.value || !hasMoreProducts.value) return;
  
  // 增加页码
  currentPage.value++;
  
  // 获取更多商品
  fetchProducts(currentCategoryId.value, true);
};

// 搜索跳转方法
const goToSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchKeyword.value.trim() }
    });
  }
};

// 快速链接搜索
const quickSearchLink = (keyword) => {
  searchKeyword.value = keyword;
  goToSearch();
};

// 跳转到AI助手页面
const goToAgentDemo = () => {
  router.push('/agent-demo');
};

// 检测滚动到底部
const checkScrollBottom = () => {
  // 检查是否滚动到接近底部
  const scrollPosition = window.scrollY + window.innerHeight;
  const pageHeight = document.documentElement.scrollHeight;
  
  // 当滚动到距离底部200px时加载更多
  if (pageHeight - scrollPosition < 200 && hasMoreProducts.value && !loading.value) {
    loadMoreProducts();
  }
};

onMounted(async () => {
  // 确保用户信息已加载
  await ensureUserInfo();

  // 获取数据
  fetchCategories();
  fetchProducts(currentCategoryId.value, false);
  fetchHotKeywords(); // 获取热门搜索词

  // 动画效果
  const revealElements = document.querySelectorAll('.reveal');
  const revealObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });

  revealElements.forEach(elem => {
    revealObserver.observe(elem);
  });

  // 获取分类数据
  fetchCategories();
  
  // 获取商品数据（默认获取全部）
  fetchProducts();

  // 添加滚动监听
  window.addEventListener('scroll', checkScrollBottom);
});

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', checkScrollBottom);
});
</script>

<style scoped>
:global(body) {
  font-family: 'Noto Sans SC', sans-serif;
  background-color: #F5F5F5;
  color: #333;
}

.home-page-full {
  width: 100%;
  --primary-color: #FF4D4F;
  --secondary-color: #FF7875;
  --text-color-dark: #333;
  --text-color-light: #666;
  --bg-color: #F5F5F5;
  --white: #FFFFFF;
  --border-color: #EFEFEF;
}

.container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 顶部导航 */
.top-nav {
  background: var(--white);
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.search-area {
  flex-grow: 1;
  margin: 0 32px;
  max-width: 600px;
}

.search-bar {
  display: flex;
  border: 2px solid var(--primary-color);
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: none;
  padding: 10px 20px;
  width: 100%;
  font-size: 16px;
}
.search-input:focus {
  outline: none;
}

.search-button {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}

.search-tags {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 14px;
}

.search-tags a {
  color: var(--text-color-light);
  text-decoration: none;
}
.search-tags a:hover {
  color: var(--primary-color);
}

.search-tags-label {
  color: var(--text-color-light);
  margin-right: 8px;
  font-size: 13px;
}

.nav-right {
  display: flex;
  align-items: center;
}

.nav-right a {
  color: var(--text-color-dark);
  text-decoration: none;
  font-size: 16px;
  transition: color 0.2s ease;
  margin-left: 20px;
}

.nav-right a:hover {
  color: var(--primary-color);
}

.nav-right .nav-icon-link {
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-assistant-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-assistant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  color: white !important;
}

/* 主要内容 */
main {
}

/* 频道分区 */
.channels-section {
  display: flex;
  gap: 20px;
}

.large-channel-card {
  flex: 2;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 12px;
  padding: 24px;
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.large-channel-card h2 { font-size: 28px; }
.large-channel-card p { font-size: 16px; opacity: 0.9; }
.large-channel-card .cta-button {
  background: rgba(255,255,255,0.9);
  color: var(--primary-color);
  padding: 10px 16px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  align-self: flex-start;
  margin-top: 16px;
  font-size: 16px;
}

.small-channels-grid {
  flex: 3;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.channel-card {
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.channel-card h3 { font-size: 18px; }
.channel-card p { font-size: 14px; color: var(--text-color-light); }
.channel-card .products { display: flex; align-items: flex-end; gap: 8px; margin-top: 8px; }
.channel-card .product-item img { width: 100%; height: auto; border-radius: 8px; }
.channel-card .product-item .price { font-size: 15px; font-weight: 500; color: var(--text-color-dark); }
.channel-card .product-item { display:flex; flex-direction:column; gap: 4px;}

/* AI助手卡片样式 */
.ai-assistant-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ai-assistant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.ai-assistant-card h3 {
  color: white;
  font-size: 18px;
  margin-bottom: 8px;
}

.ai-assistant-card p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.ai-features {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.ai-feature {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  backdrop-filter: blur(10px);
}

.bg-red-light { background-color: #FFF1F0; }
.bg-blue-light { background-color: #FFF7F7; }
.bg-green-light { background-color: #FFF1F0; }
.bg-yellow-light { background-color: #FFF7F7; }

.main-content-card {
  background: var(--white);
  padding: 24px;
  border-radius: 16px;
  margin-top: 20px;
}

/* 兴趣标签 */
.interest-tags {
  margin-bottom: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.interest-tag {
  background: #f5f5f5;
  padding: 12px 22px;
  border-radius: 22px;
  font-size: 16px;
  text-decoration: none;
  color: var(--text-color-dark);
  transition: all 0.2s ease;
  font-weight: 500;
}
.interest-tag:hover:not(.active) {
  background-color: #e0e0e0;
}

.interest-tag.active {
    background-color: #ffde03;
    color: var(--text-color-dark);
    font-weight: 700;
}

/* 筛选条件样式 */
.filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
}

.filter-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 16px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.filter-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.active-filters {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.active-filters-label {
  font-size: 13px;
  color: #666;
  margin-right: 8px;
}

/* 瀑布流商品 */
.products-feed {
  column-count: 5;
  column-gap: 16px;
}

.product-card {
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  break-inside: avoid;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid var(--border-color);
  cursor: pointer;
}
.product-card:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.product-card img {
  width: 100%;
  display: block;
}

.product-info {
  padding: 12px;
}

.product-title {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: var(--primary-color);
}
.product-price span { font-size: 14px; }

.product-seller {
  display: flex;
  align-items: center;
  gap: 6px;
}
.product-seller img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
.product-seller span {
  font-size: 14px;
  color: var(--text-color-light);
}

/* 动画 */
.reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}
.reveal.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: var(--text-color-light);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载更多区域 */
.load-more-container {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  margin: 20px 0;
  width: 100%;
  clear: both;
}

.load-more-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.loading-indicator-inline {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-color-light);
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

.no-more-products {
  color: var(--text-color-light);
  font-size: 14px;
  padding: 10px;
  text-align: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: var(--text-color-light);
  text-align: center;
}

.empty-state svg {
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
}

/* 导航右侧用户区域 */
.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: transform 0.2s ease;
}

.avatar-image:hover {
  transform: scale(1.1);
}

/* 筛选条件响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    padding: 16px;
  }

  .filter-row {
    gap: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-label {
    min-width: auto;
    font-size: 13px;
  }

  .filter-options {
    width: 100%;
  }

  .filter-btn {
    font-size: 12px;
    padding: 5px 10px;
  }

  .active-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .active-filters-label {
    font-size: 12px;
  }
}
</style>
 