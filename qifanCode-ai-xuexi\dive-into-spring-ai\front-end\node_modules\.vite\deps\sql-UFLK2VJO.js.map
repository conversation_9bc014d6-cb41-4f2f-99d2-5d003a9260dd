{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/sql.js"], "sourcesContent": ["export function sql(parserConfig) {\n  var client         = parserConfig.client || {},\n      atoms          = parserConfig.atoms || {\"false\": true, \"true\": true, \"null\": true},\n      builtin        = parserConfig.builtin || set(defaultBuiltin),\n      keywords       = parserConfig.keywords || set(sqlKeywords),\n      operatorChars  = parserConfig.operatorChars || /^[*+\\-%<>!=&|~^\\/]/,\n      support        = parserConfig.support || {},\n      hooks          = parserConfig.hooks || {},\n      dateSQL        = parserConfig.dateSQL || {\"date\" : true, \"time\" : true, \"timestamp\" : true},\n      backslashStringEscapes = parserConfig.backslashStringEscapes !== false,\n      brackets       = parserConfig.brackets || /^[\\{}\\(\\)\\[\\]]/,\n      punctuation    = parserConfig.punctuation || /^[;.,:]/\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    // call hooks from the mime type\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n\n    if (support.hexNumber &&\n      ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/))\n      || (ch == \"x\" || ch == \"X\") && stream.match(/^'[0-9a-fA-F]*'/))) {\n      // hex\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/hexadecimal-literals.html\n      return \"number\";\n    } else if (support.binaryNumber &&\n      (((ch == \"b\" || ch == \"B\") && stream.match(/^'[01]+'/))\n      || (ch == \"0\" && stream.match(/^b[01]*/)))) {\n      // bitstring\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/bit-field-literals.html\n      return \"number\";\n    } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {\n      // numbers\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/number-literals.html\n      stream.match(/^[0-9]*(\\.[0-9]+)?([eE][-+]?[0-9]+)?/);\n      support.decimallessFloat && stream.match(/^\\.(?!\\.)/);\n      return \"number\";\n    } else if (ch == \"?\" && (stream.eatSpace() || stream.eol() || stream.eat(\";\"))) {\n      // placeholders\n      return \"macroName\";\n    } else if (ch == \"'\" || (ch == '\"' && support.doubleQuote)) {\n      // strings\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      state.tokenize = tokenLiteral(ch);\n      return state.tokenize(stream, state);\n    } else if ((((support.nCharCast && (ch == \"n\" || ch == \"N\"))\n        || (support.charsetCast && ch == \"_\" && stream.match(/[a-z][a-z0-9]*/i)))\n        && (stream.peek() == \"'\" || stream.peek() == '\"'))) {\n      // charset casting: _utf8'str', N'str', n'str'\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      return \"keyword\";\n    } else if (support.escapeConstant && (ch == \"e\" || ch == \"E\")\n        && (stream.peek() == \"'\" || (stream.peek() == '\"' && support.doubleQuote))) {\n      // escape constant: E'str', e'str'\n      // ref: https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE\n      state.tokenize = function(stream, state) {\n        return (state.tokenize = tokenLiteral(stream.next(), true))(stream, state);\n      }\n      return \"keyword\";\n    } else if (support.commentSlashSlash && ch == \"/\" && stream.eat(\"/\")) {\n      // 1-line comment\n      stream.skipToEnd();\n      return \"comment\";\n    } else if ((support.commentHash && ch == \"#\")\n        || (ch == \"-\" && stream.eat(\"-\") && (!support.commentSpaceRequired || stream.eat(\" \")))) {\n      // 1-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"/\" && stream.eat(\"*\")) {\n      // multi-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      state.tokenize = tokenComment(1);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\") {\n      // .1 for 0.1\n      if (support.zerolessFloat && stream.match(/^(?:\\d+(?:e[+-]?\\d+)?)/i))\n        return \"number\";\n      if (stream.match(/^\\.+/))\n        return null\n      // .table_name (ODBC)\n      // // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n      if (support.ODBCdotTable && stream.match(/^[\\w\\d_$#]+/))\n        return \"type\";\n    } else if (operatorChars.test(ch)) {\n      // operators\n      stream.eatWhile(operatorChars);\n      return \"operator\";\n    } else if (brackets.test(ch)) {\n      // brackets\n      return \"bracket\";\n    } else if (punctuation.test(ch)) {\n      // punctuation\n      stream.eatWhile(punctuation);\n      return \"punctuation\";\n    } else if (ch == '{' &&\n        (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*\"[^\"]*\"( )*}/))) {\n      // dates (weird ODBC syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      return \"number\";\n    } else {\n      stream.eatWhile(/^[_\\w\\d]/);\n      var word = stream.current().toLowerCase();\n      // dates (standard SQL syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+\"[^\"]*\"/)))\n        return \"number\";\n      if (atoms.hasOwnProperty(word)) return \"atom\";\n      if (builtin.hasOwnProperty(word)) return \"type\";\n      if (keywords.hasOwnProperty(word)) return \"keyword\";\n      if (client.hasOwnProperty(word)) return \"builtin\";\n      return null;\n    }\n  }\n\n  // 'string', with char specified in quote escaped by '\\'\n  function tokenLiteral(quote, backslashEscapes) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        escaped = (backslashStringEscapes || backslashEscapes) && !escaped && ch == \"\\\\\";\n      }\n      return \"string\";\n    };\n  }\n  function tokenComment(depth) {\n    return function(stream, state) {\n      var m = stream.match(/^.*?(\\/\\*|\\*\\/)/)\n      if (!m) stream.skipToEnd()\n      else if (m[1] == \"/*\") state.tokenize = tokenComment(depth + 1)\n      else if (depth > 1) state.tokenize = tokenComment(depth - 1)\n      else state.tokenize = tokenBase\n      return \"comment\"\n    }\n  }\n\n  function pushContext(stream, state, type) {\n    state.context = {\n      prev: state.context,\n      indent: stream.indentation(),\n      col: stream.column(),\n      type: type\n    };\n  }\n\n  function popContext(state) {\n    state.indent = state.context.indent;\n    state.context = state.context.prev;\n  }\n\n  return {\n    name: \"sql\",\n\n    startState: function() {\n      return {tokenize: tokenBase, context: null};\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (state.context && state.context.align == null)\n          state.context.align = false;\n      }\n      if (state.tokenize == tokenBase && stream.eatSpace()) return null;\n\n      var style = state.tokenize(stream, state);\n      if (style == \"comment\") return style;\n\n      if (state.context && state.context.align == null)\n        state.context.align = true;\n\n      var tok = stream.current();\n      if (tok == \"(\")\n        pushContext(stream, state, \")\");\n      else if (tok == \"[\")\n        pushContext(stream, state, \"]\");\n      else if (state.context && state.context.type == tok)\n        popContext(state);\n      return style;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context;\n      if (!cx) return null;\n      var closing = textAfter.charAt(0) == cx.type;\n      if (cx.align) return cx.col + (closing ? 0 : 1);\n      else return cx.indent + (closing ? 0 : iCx.unit);\n    },\n\n    languageData: {\n      commentTokens: {\n        line: support.commentSlashSlash ? \"//\" : support.commentHash ? \"#\" : \"--\",\n        block: {open: \"/*\", close: \"*/\"}\n      },\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n    }\n  };\n};\n\n// `identifier`\nfunction hookIdentifier(stream) {\n  // MySQL/MariaDB identifiers\n  // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"`\" && !stream.eat(\"`\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// \"identifier\"\nfunction hookIdentifierDoublequote(stream) {\n  // Standard SQL /SQLite identifiers\n  // ref: http://web.archive.org/web/20160813185132/http://savage.net.au/SQL/sql-99.bnf.html#delimited%20identifier\n  // ref: http://sqlite.org/lang_keywords.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"\\\"\" && !stream.eat(\"\\\"\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// variable token\nfunction hookVar(stream) {\n  // variables\n  // @@prefix.varName @varName\n  // varName can be quoted with ` or ' or \"\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/user-variables.html\n  if (stream.eat(\"@\")) {\n    stream.match('session.');\n    stream.match('local.');\n    stream.match('global.');\n  }\n\n  if (stream.eat(\"'\")) {\n    stream.match(/^.*'/);\n    return \"string.special\";\n  } else if (stream.eat('\"')) {\n    stream.match(/^.*\"/);\n    return \"string.special\";\n  } else if (stream.eat(\"`\")) {\n    stream.match(/^.*`/);\n    return \"string.special\";\n  } else if (stream.match(/^[0-9a-zA-Z$\\.\\_]+/)) {\n    return \"string.special\";\n  }\n  return null;\n};\n\n// short client keyword token\nfunction hookClient(stream) {\n  // \\N means NULL\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/null-values.html\n  if (stream.eat(\"N\")) {\n    return \"atom\";\n  }\n  // \\g, etc\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/mysql-commands.html\n  return stream.match(/^[a-zA-Z.#!?]/) ? \"string.special\" : null;\n}\n\n// these keywords are used by all SQL dialects (however, a mode can still overwrite it)\nvar sqlKeywords = \"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit \";\n\n// turn a space-separated list into an array\nfunction set(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar defaultBuiltin = \"bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric\"\n\n// A generic SQL Mode. It's not a standard, it just try to support what is generally supported\nexport const standardSQL = sql({\n  keywords: set(sqlKeywords + \"begin\"),\n  builtin: set(defaultBuiltin),\n  atoms: set(\"false true null unknown\"),\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nexport const msSQL = sql({\n  client: set(\"$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\"),\n  keywords: set(sqlKeywords + \"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with\"),\n  builtin: set(\"bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table \"),\n  atoms: set(\"is not null like and or in left right between inner outer join all any some cross unpivot pivot exists\"),\n  operatorChars: /^[*+\\-%<>!=^\\&|\\/]/,\n  brackets: /^[\\{}\\(\\)]/,\n  punctuation: /^[;.,:/]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date datetimeoffset datetime2 smalldatetime datetime time\"),\n  hooks: {\n    \"@\":   hookVar\n  }\n});\n\nexport const mySQL = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\nexport const mariaDB = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\n// provided by the phpLiteAdmin project - phpliteadmin.org\nexport const sqlite = sql({\n  // commands of the official SQLite client, ref: https://www.sqlite.org/cli.html#dotcmd\n  client: set(\"auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\"),\n  // ref: http://sqlite.org/lang_keywords.html\n  keywords: set(sqlKeywords + \"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without\"),\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real\"),\n  // ref: http://sqlite.org/syntax/literal-value.html\n  atoms: set(\"null current_date current_time current_timestamp\"),\n  // ref: http://sqlite.org/lang_expr.html#binaryops\n  operatorChars: /^[*+\\-%<>!=&|/~]/,\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  dateSQL: set(\"date time timestamp datetime\"),\n  support: set(\"decimallessFloat zerolessFloat\"),\n  identifierQuote: \"\\\"\",  //ref: http://sqlite.org/lang_keywords.html\n  hooks: {\n    // bind-parameters ref:http://sqlite.org/lang_expr.html#varparam\n    \"@\":   hookVar,\n    \":\":   hookVar,\n    \"?\":   hookVar,\n    \"$\":   hookVar,\n    // The preferred way to escape Identifiers is using double quotes, ref: http://sqlite.org/lang_keywords.html\n    \"\\\"\":   hookIdentifierDoublequote,\n    // there is also support for backticks, ref: http://sqlite.org/lang_keywords.html\n    \"`\":   hookIdentifier\n  }\n});\n\n// the query language used by Apache Cassandra is called CQL, but this mime type\n// is called Cassandra to avoid confusion with Contextual Query Language\nexport const cassandra = sql({\n  client: { },\n  keywords: set(\"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime\"),\n  builtin: set(\"ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint\"),\n  atoms: set(\"false true infinity NaN\"),\n  operatorChars: /^[<>=]/,\n  dateSQL: { },\n  support: set(\"commentSlashSlash decimallessFloat\"),\n  hooks: { }\n});\n\n// this is based on Peter Raganitsch's 'plsql' mode\nexport const plSQL = sql({\n  client:     set(\"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap\"),\n  keywords:   set(\"abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\"),\n  builtin:    set(\"abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml\"),\n  operatorChars: /^[*\\/+\\-%<>!=~]/,\n  dateSQL:    set(\"date time timestamp\"),\n  support:    set(\"doubleQuote nCharCast zerolessFloat binaryNumber hexNumber\")\n});\n\n// Created to support specific hive keywords\nexport const hive = sql({\n  keywords: set(\"select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year\"),\n  builtin: set(\"bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=]/,\n  dateSQL: set(\"date timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nexport const pgSQL = sql({\n  client: set(\"source\"),\n  // For PostgreSQL - https://www.postgresql.org/docs/11/sql-keywords-appendix.html\n  // For pl/pgsql lang - https://github.com/postgres/postgres/blob/REL_11_2/src/pl/plpgsql/src/pl_scanner.c\n  keywords: set(sqlKeywords + \"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone\"),\n  // https://www.postgresql.org/docs/11/datatype.html\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*\\/+\\-%<>!=&|^\\/#@?~]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant\")\n});\n\n// Google's SQL-like query language, GQL\nexport const gql = sql({\n  keywords: set(\"ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where\"),\n  atoms: set(\"false true\"),\n  builtin: set(\"blob datetime first key __key__ string integer double boolean null\"),\n  operatorChars: /^[*+\\-%<>!=]/\n});\n\n// Greenplum\nexport const gpSQL = sql({\n  client: set(\"source\"),\n  //https://github.com/greenplum-db/gpdb/blob/master/src/include/parser/kwlist.h\n  keywords: set(\"abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone\"),\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast\")\n});\n\n// Spark SQL\nexport const sparkSQL = sql({\n  keywords: set(\"add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with\"),\n  builtin: set(\"tinyint smallint int bigint boolean float double string binary timestamp decimal array map struct uniontype delimited serde sequencefile textfile rcfile inputformat outputformat\"),\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*\\/+\\-%<>!=~&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote zerolessFloat\")\n});\n\n// Esper\nexport const esper = sql({\n  client: set(\"source\"),\n  // http://www.espertech.com/esper/release-5.5.0/esper-reference/html/appendix_keywords.html\n  keywords: set(\"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window\"),\n  builtin: {},\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"time\"),\n  support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber\")\n});\n\n/*\n  How options are used by SQL Mode\n  =================================================\n\n  keywords:\n    A list of keywords you want to be highlighted.\n  builtin:\n    A list of builtin types you want to be highlighted (if you want types to be of class \"builtin\" instead of \"keyword\").\n  operatorChars:\n    All characters that must be handled as operators.\n  client:\n    Commands parsed and executed by the client (not the server).\n  support:\n    A list of supported syntaxes which are not common, but are supported by more than 1 DBMS.\n    * ODBCdotTable: .tableName\n    * zerolessFloat: .1\n    * doubleQuote\n    * nCharCast: N'string'\n    * charsetCast: _utf8'string'\n    * commentHash: use # char for comments\n    * commentSlashSlash: use // for comments\n    * commentSpaceRequired: require a space after -- for comments\n  atoms:\n    Keywords that must be highlighted as atoms,. Some DBMS's support more atoms than others:\n    UNKNOWN, INFINITY, UNDERFLOW, NaN...\n  dateSQL:\n    Used for date/time SQL standard syntax, because not all DBMS's support same temporal types.\n*/\n"], "mappings": ";;;AAAO,SAAS,IAAI,cAAc;AAChC,MAAI,SAAiB,aAAa,UAAU,CAAC,GACzC,QAAiB,aAAa,SAAS,EAAC,SAAS,MAAM,QAAQ,MAAM,QAAQ,KAAI,GACjF,UAAiB,aAAa,WAAW,IAAI,cAAc,GAC3D,WAAiB,aAAa,YAAY,IAAI,WAAW,GACzD,gBAAiB,aAAa,iBAAiB,sBAC/C,UAAiB,aAAa,WAAW,CAAC,GAC1C,QAAiB,aAAa,SAAS,CAAC,GACxC,UAAiB,aAAa,WAAW,EAAC,QAAS,MAAM,QAAS,MAAM,aAAc,KAAI,GAC1F,yBAAyB,aAAa,2BAA2B,OACjE,WAAiB,aAAa,YAAY,kBAC1C,cAAiB,aAAa,eAAe;AAEjD,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK;AAGrB,QAAI,MAAM,EAAE,GAAG;AACb,UAAI,SAAS,MAAM,EAAE,EAAE,QAAQ,KAAK;AACpC,UAAI,WAAW;AAAO,eAAO;AAAA,IAC/B;AAEA,QAAI,QAAQ,cACR,MAAM,OAAO,OAAO,MAAM,mBAAmB,MAC3C,MAAM,OAAO,MAAM,QAAQ,OAAO,MAAM,iBAAiB,IAAI;AAGjE,aAAO;AAAA,IACT,WAAW,QAAQ,kBACd,MAAM,OAAO,MAAM,QAAQ,OAAO,MAAM,UAAU,KACjD,MAAM,OAAO,OAAO,MAAM,SAAS,IAAK;AAG5C,aAAO;AAAA,IACT,WAAW,GAAG,WAAW,CAAC,IAAI,MAAM,GAAG,WAAW,CAAC,IAAI,IAAI;AAGzD,aAAO,MAAM,sCAAsC;AACnD,cAAQ,oBAAoB,OAAO,MAAM,WAAW;AACpD,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,OAAO,SAAS,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,GAAG,IAAI;AAE9E,aAAO;AAAA,IACT,WAAW,MAAM,OAAQ,MAAM,OAAO,QAAQ,aAAc;AAG1D,YAAM,WAAW,aAAa,EAAE;AAChC,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,YAAc,QAAQ,cAAc,MAAM,OAAO,MAAM,QAC/C,QAAQ,eAAe,MAAM,OAAO,OAAO,MAAM,iBAAiB,OAClE,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,MAAO;AAGtD,aAAO;AAAA,IACT,WAAW,QAAQ,mBAAmB,MAAM,OAAO,MAAM,SACjD,OAAO,KAAK,KAAK,OAAQ,OAAO,KAAK,KAAK,OAAO,QAAQ,cAAe;AAG9E,YAAM,WAAW,SAASA,SAAQC,QAAO;AACvC,gBAAQA,OAAM,WAAW,aAAaD,QAAO,KAAK,GAAG,IAAI,GAAGA,SAAQC,MAAK;AAAA,MAC3E;AACA,aAAO;AAAA,IACT,WAAW,QAAQ,qBAAqB,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAEpE,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAY,QAAQ,eAAe,MAAM,OACjC,MAAM,OAAO,OAAO,IAAI,GAAG,MAAM,CAAC,QAAQ,wBAAwB,OAAO,IAAI,GAAG,IAAK;AAG3F,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAGvC,YAAM,WAAW,aAAa,CAAC;AAC/B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,WAAW,MAAM,KAAK;AAEpB,UAAI,QAAQ,iBAAiB,OAAO,MAAM,yBAAyB;AACjE,eAAO;AACT,UAAI,OAAO,MAAM,MAAM;AACrB,eAAO;AAGT,UAAI,QAAQ,gBAAgB,OAAO,MAAM,aAAa;AACpD,eAAO;AAAA,IACX,WAAW,cAAc,KAAK,EAAE,GAAG;AAEjC,aAAO,SAAS,aAAa;AAC7B,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,EAAE,GAAG;AAE5B,aAAO;AAAA,IACT,WAAW,YAAY,KAAK,EAAE,GAAG;AAE/B,aAAO,SAAS,WAAW;AAC3B,aAAO;AAAA,IACT,WAAW,MAAM,QACZ,OAAO,MAAM,sCAAsC,KAAK,OAAO,MAAM,sCAAsC,IAAI;AAGlH,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS,UAAU;AAC1B,UAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AAGxC,UAAI,QAAQ,eAAe,IAAI,MAAM,OAAO,MAAM,cAAc,KAAK,OAAO,MAAM,cAAc;AAC9F,eAAO;AACT,UAAI,MAAM,eAAe,IAAI;AAAG,eAAO;AACvC,UAAI,QAAQ,eAAe,IAAI;AAAG,eAAO;AACzC,UAAI,SAAS,eAAe,IAAI;AAAG,eAAO;AAC1C,UAAI,OAAO,eAAe,IAAI;AAAG,eAAO;AACxC,aAAO;AAAA,IACT;AAAA,EACF;AAGA,WAAS,aAAa,OAAO,kBAAkB;AAC7C,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,UAAU,OAAO;AACrB,cAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,YAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,gBAAM,WAAW;AACjB;AAAA,QACF;AACA,mBAAW,0BAA0B,qBAAqB,CAAC,WAAW,MAAM;AAAA,MAC9E;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,aAAa,OAAO;AAC3B,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,IAAI,OAAO,MAAM,iBAAiB;AACtC,UAAI,CAAC;AAAG,eAAO,UAAU;AAAA,eAChB,EAAE,CAAC,KAAK;AAAM,cAAM,WAAW,aAAa,QAAQ,CAAC;AAAA,eACrD,QAAQ;AAAG,cAAM,WAAW,aAAa,QAAQ,CAAC;AAAA;AACtD,cAAM,WAAW;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,YAAY,QAAQ,OAAO,MAAM;AACxC,UAAM,UAAU;AAAA,MACd,MAAM,MAAM;AAAA,MACZ,QAAQ,OAAO,YAAY;AAAA,MAC3B,KAAK,OAAO,OAAO;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,UAAU,MAAM,QAAQ;AAAA,EAChC;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IAEN,YAAY,WAAW;AACrB,aAAO,EAAC,UAAU,WAAW,SAAS,KAAI;AAAA,IAC5C;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,OAAO,IAAI,GAAG;AAChB,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS;AAC1C,gBAAM,QAAQ,QAAQ;AAAA,MAC1B;AACA,UAAI,MAAM,YAAY,aAAa,OAAO,SAAS;AAAG,eAAO;AAE7D,UAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,UAAI,SAAS;AAAW,eAAO;AAE/B,UAAI,MAAM,WAAW,MAAM,QAAQ,SAAS;AAC1C,cAAM,QAAQ,QAAQ;AAExB,UAAI,MAAM,OAAO,QAAQ;AACzB,UAAI,OAAO;AACT,oBAAY,QAAQ,OAAO,GAAG;AAAA,eACvB,OAAO;AACd,oBAAY,QAAQ,OAAO,GAAG;AAAA,eACvB,MAAM,WAAW,MAAM,QAAQ,QAAQ;AAC9C,mBAAW,KAAK;AAClB,aAAO;AAAA,IACT;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,KAAK;AACtC,UAAI,KAAK,MAAM;AACf,UAAI,CAAC;AAAI,eAAO;AAChB,UAAI,UAAU,UAAU,OAAO,CAAC,KAAK,GAAG;AACxC,UAAI,GAAG;AAAO,eAAO,GAAG,OAAO,UAAU,IAAI;AAAA;AACxC,eAAO,GAAG,UAAU,UAAU,IAAI,IAAI;AAAA,IAC7C;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe;AAAA,QACb,MAAM,QAAQ,oBAAoB,OAAO,QAAQ,cAAc,MAAM;AAAA,QACrE,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI;AAAA,MACjC;AAAA,MACA,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,IAC1D;AAAA,EACF;AACF;AAGA,SAAS,eAAe,QAAQ;AAG9B,MAAI;AACJ,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;AAAG,aAAO;AAAA,EAC5C;AACA,SAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AACzC,SAAO,OAAO,SAAS,IAAI,IAAI,mBAAmB;AACpD;AAGA,SAAS,0BAA0B,QAAQ;AAIzC,MAAI;AACJ,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,MAAM,OAAQ,CAAC,OAAO,IAAI,GAAI;AAAG,aAAO;AAAA,EAC9C;AACA,SAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AACzC,SAAO,OAAO,SAAS,IAAI,IAAI,mBAAmB;AACpD;AAGA,SAAS,QAAQ,QAAQ;AAKvB,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,WAAO,MAAM,UAAU;AACvB,WAAO,MAAM,QAAQ;AACrB,WAAO,MAAM,SAAS;AAAA,EACxB;AAEA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,WAAO,MAAM,MAAM;AACnB,WAAO;AAAA,EACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,WAAO,MAAM,MAAM;AACnB,WAAO;AAAA,EACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,WAAO,MAAM,MAAM;AACnB,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,oBAAoB,GAAG;AAC7C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,SAAS,WAAW,QAAQ;AAG1B,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,WAAO;AAAA,EACT;AAGA,SAAO,OAAO,MAAM,eAAe,IAAI,mBAAmB;AAC5D;AAGA,IAAI,cAAc;AAGlB,SAAS,IAAI,KAAK;AAChB,MAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAAG,QAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,iBAAiB;AAGd,IAAM,cAAc,IAAI;AAAA,EAC7B,UAAU,IAAI,cAAc,OAAO;AAAA,EACnC,SAAS,IAAI,cAAc;AAAA,EAC3B,OAAO,IAAI,yBAAyB;AAAA,EACpC,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,iDAAiD;AAChE,CAAC;AAEM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAQ,IAAI,uVAAuV;AAAA,EACnW,UAAU,IAAI,cAAc,qSAAqS;AAAA,EACjU,SAAS,IAAI,gNAAgN;AAAA,EAC7N,OAAO,IAAI,wGAAwG;AAAA,EACnH,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,wBAAwB;AAAA,EACxB,SAAS,IAAI,2DAA2D;AAAA,EACxE,OAAO;AAAA,IACL,KAAO;AAAA,EACT;AACF,CAAC;AAEM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAQ,IAAI,6HAA6H;AAAA,EACzI,UAAU,IAAI,cAAc,o9FAAo9F;AAAA,EACh/F,SAAS,IAAI,wTAAwT;AAAA,EACrU,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,uIAAuI;AAAA,EACpJ,OAAO;AAAA,IACL,KAAO;AAAA,IACP,KAAO;AAAA,IACP,MAAO;AAAA,EACT;AACF,CAAC;AAEM,IAAM,UAAU,IAAI;AAAA,EACzB,QAAQ,IAAI,6HAA6H;AAAA,EACzI,UAAU,IAAI,cAAc,2gGAA2gG;AAAA,EACviG,SAAS,IAAI,wTAAwT;AAAA,EACrU,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,uIAAuI;AAAA,EACpJ,OAAO;AAAA,IACL,KAAO;AAAA,IACP,KAAO;AAAA,IACP,MAAO;AAAA,EACT;AACF,CAAC;AAGM,IAAM,SAAS,IAAI;AAAA;AAAA,EAExB,QAAQ,IAAI,sWAAsW;AAAA;AAAA,EAElX,UAAU,IAAI,cAAc,6pBAA6pB;AAAA;AAAA,EAEzrB,SAAS,IAAI,oQAAoQ;AAAA;AAAA,EAEjR,OAAO,IAAI,kDAAkD;AAAA;AAAA,EAE7D,eAAe;AAAA;AAAA,EAEf,SAAS,IAAI,8BAA8B;AAAA,EAC3C,SAAS,IAAI,gCAAgC;AAAA,EAC7C,iBAAiB;AAAA;AAAA,EACjB,OAAO;AAAA;AAAA,IAEL,KAAO;AAAA,IACP,KAAO;AAAA,IACP,KAAO;AAAA,IACP,KAAO;AAAA;AAAA,IAEP,KAAQ;AAAA;AAAA,IAER,KAAO;AAAA,EACT;AACF,CAAC;AAIM,IAAM,YAAY,IAAI;AAAA,EAC3B,QAAQ,CAAE;AAAA,EACV,UAAU,IAAI,2hBAA2hB;AAAA,EACziB,SAAS,IAAI,0IAA0I;AAAA,EACvJ,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,SAAS,CAAE;AAAA,EACX,SAAS,IAAI,oCAAoC;AAAA,EACjD,OAAO,CAAE;AACX,CAAC;AAGM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAY,IAAI,gqBAAgqB;AAAA,EAChrB,UAAY,IAAI,itDAAitD;AAAA,EACjuD,SAAY,IAAI,i9BAAi9B;AAAA,EACj+B,eAAe;AAAA,EACf,SAAY,IAAI,qBAAqB;AAAA,EACrC,SAAY,IAAI,4DAA4D;AAC9E,CAAC;AAGM,IAAM,OAAO,IAAI;AAAA,EACtB,UAAU,IAAI,k1DAAk1D;AAAA,EACh2D,SAAS,IAAI,wKAAwK;AAAA,EACrL,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,SAAS,IAAI,gBAAgB;AAAA,EAC7B,SAAS,IAAI,iDAAiD;AAChE,CAAC;AAEM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAQ,IAAI,QAAQ;AAAA;AAAA;AAAA,EAGpB,UAAU,IAAI,cAAc,m3NAAm3N;AAAA;AAAA,EAE/4N,SAAS,IAAI,4ZAA4Z;AAAA,EACza,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,wBAAwB;AAAA,EACxB,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,yGAAyG;AACxH,CAAC;AAGM,IAAM,MAAM,IAAI;AAAA,EACrB,UAAU,IAAI,wHAAwH;AAAA,EACtI,OAAO,IAAI,YAAY;AAAA,EACvB,SAAS,IAAI,oEAAoE;AAAA,EACjF,eAAe;AACjB,CAAC;AAGM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAQ,IAAI,QAAQ;AAAA;AAAA,EAEpB,UAAU,IAAI,y7GAAy7G;AAAA,EACv8G,SAAS,IAAI,kaAAka;AAAA,EAC/a,OAAO,IAAI,yBAAyB;AAAA,EACpC,eAAe;AAAA,EACf,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,0FAA0F;AACzG,CAAC;AAGM,IAAM,WAAW,IAAI;AAAA,EAC1B,UAAU,IAAI,i+CAAi+C;AAAA,EAC/+C,SAAS,IAAI,mLAAmL;AAAA,EAChM,OAAO,IAAI,iBAAiB;AAAA,EAC5B,eAAe;AAAA,EACf,SAAS,IAAI,qBAAqB;AAAA,EAClC,SAAS,IAAI,wCAAwC;AACvD,CAAC;AAGM,IAAM,QAAQ,IAAI;AAAA,EACvB,QAAQ,IAAI,QAAQ;AAAA;AAAA,EAEpB,UAAU,IAAI,60BAA60B;AAAA,EAC31B,SAAS,CAAC;AAAA,EACV,OAAO,IAAI,iBAAiB;AAAA,EAC5B,eAAe;AAAA,EACf,SAAS,IAAI,MAAM;AAAA,EACnB,SAAS,IAAI,uDAAuD;AACtE,CAAC;", "names": ["stream", "state"]}