package com.lzhshtp.shangcheng.controller.audit;


import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import com.lzhshtp.shangcheng.service.audit.MaterialRequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 材料请求控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/audit/material-requests")
public class MaterialRequestController {

    @Autowired
    private MaterialRequestService materialRequestService;

    /**
     * 获取商品的材料请求列表
     */
    @GetMapping("/product/{productId}")
    public ApiResponse<List<MaterialRequest>> getMaterialRequestsByProduct(@PathVariable Long productId) {
        try {
            log.info("获取商品材料请求，商品ID: {}", productId);

            List<MaterialRequest> requests = materialRequestService.getMaterialRequestsByProduct(productId);

            return ApiResponse.success("获取材料请求成功", requests);
        } catch (Exception e) {
            log.error("获取商品材料请求失败，商品ID: {}", productId, e);
            return ApiResponse.fail("获取材料请求失败：" + e.getMessage());
        }
    }

    /**
     * 获取待处理的材料请求
     */
    @GetMapping("/pending")
    public ApiResponse<List<MaterialRequest>> getPendingMaterialRequests() {
        try {
            log.info("获取待处理的材料请求");

            List<MaterialRequest> requests = materialRequestService.getPendingMaterialRequests();

            return ApiResponse.success("获取待处理材料请求成功", requests);
        } catch (Exception e) {
            log.error("获取待处理材料请求失败", e);
            return ApiResponse.fail("获取待处理材料请求失败：" + e.getMessage());
        }
    }

    /**
     * 更新材料请求状态
     */
    @PutMapping("/{requestId}/status")
    public ApiResponse<Void> updateMaterialRequestStatus(
            @PathVariable Long requestId,
            @RequestParam String status) {
        try {
            log.info("更新材料请求状态，请求ID: {}, 状态: {}", requestId, status);

            materialRequestService.updateMaterialRequestStatus(requestId, status);

            return ApiResponse.successVoid("更新材料请求状态成功");
        } catch (Exception e) {
            log.error("更新材料请求状态失败，请求ID: {}", requestId, e);
            return ApiResponse.fail("更新材料请求状态失败：" + e.getMessage());
        }
    }

    /**
     * 标记卖家已通知
     */
    @PutMapping("/{requestId}/notify")
    public ApiResponse<Void> markSellerNotified(@PathVariable Long requestId) {
        try {
            log.info("标记卖家已通知，请求ID: {}", requestId);

            materialRequestService.markSellerNotified(requestId);

            return ApiResponse.successVoid("标记通知状态成功");
        } catch (Exception e) {
            log.error("标记通知状态失败，请求ID: {}", requestId, e);
            return ApiResponse.fail("标记通知状态失败：" + e.getMessage());
        }
    }
}
