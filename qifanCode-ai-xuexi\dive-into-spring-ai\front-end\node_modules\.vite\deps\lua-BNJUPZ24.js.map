{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/lua.js"], "sourcesContent": ["function prefixRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")\", \"i\");\n}\nfunction wordRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\n\n// long list of standard functions from lua manual\nvar builtins = wordRE([\n  \"_G\",\"_VERSION\",\"assert\",\"collectgarbage\",\"dofile\",\"error\",\"getfenv\",\"getmetatable\",\"ipairs\",\"load\",\n  \"loadfile\",\"loadstring\",\"module\",\"next\",\"pairs\",\"pcall\",\"print\",\"rawequal\",\"rawget\",\"rawset\",\"require\",\n  \"select\",\"setfenv\",\"setmetatable\",\"tonumber\",\"tostring\",\"type\",\"unpack\",\"xpcall\",\n\n  \"coroutine.create\",\"coroutine.resume\",\"coroutine.running\",\"coroutine.status\",\"coroutine.wrap\",\"coroutine.yield\",\n\n  \"debug.debug\",\"debug.getfenv\",\"debug.gethook\",\"debug.getinfo\",\"debug.getlocal\",\"debug.getmetatable\",\n  \"debug.getregistry\",\"debug.getupvalue\",\"debug.setfenv\",\"debug.sethook\",\"debug.setlocal\",\"debug.setmetatable\",\n  \"debug.setupvalue\",\"debug.traceback\",\n\n  \"close\",\"flush\",\"lines\",\"read\",\"seek\",\"setvbuf\",\"write\",\n\n  \"io.close\",\"io.flush\",\"io.input\",\"io.lines\",\"io.open\",\"io.output\",\"io.popen\",\"io.read\",\"io.stderr\",\"io.stdin\",\n  \"io.stdout\",\"io.tmpfile\",\"io.type\",\"io.write\",\n\n  \"math.abs\",\"math.acos\",\"math.asin\",\"math.atan\",\"math.atan2\",\"math.ceil\",\"math.cos\",\"math.cosh\",\"math.deg\",\n  \"math.exp\",\"math.floor\",\"math.fmod\",\"math.frexp\",\"math.huge\",\"math.ldexp\",\"math.log\",\"math.log10\",\"math.max\",\n  \"math.min\",\"math.modf\",\"math.pi\",\"math.pow\",\"math.rad\",\"math.random\",\"math.randomseed\",\"math.sin\",\"math.sinh\",\n  \"math.sqrt\",\"math.tan\",\"math.tanh\",\n\n  \"os.clock\",\"os.date\",\"os.difftime\",\"os.execute\",\"os.exit\",\"os.getenv\",\"os.remove\",\"os.rename\",\"os.setlocale\",\n  \"os.time\",\"os.tmpname\",\n\n  \"package.cpath\",\"package.loaded\",\"package.loaders\",\"package.loadlib\",\"package.path\",\"package.preload\",\n  \"package.seeall\",\n\n  \"string.byte\",\"string.char\",\"string.dump\",\"string.find\",\"string.format\",\"string.gmatch\",\"string.gsub\",\n  \"string.len\",\"string.lower\",\"string.match\",\"string.rep\",\"string.reverse\",\"string.sub\",\"string.upper\",\n\n  \"table.concat\",\"table.insert\",\"table.maxn\",\"table.remove\",\"table.sort\"\n]);\nvar keywords = wordRE([\"and\",\"break\",\"elseif\",\"false\",\"nil\",\"not\",\"or\",\"return\",\n                       \"true\",\"function\", \"end\", \"if\", \"then\", \"else\", \"do\",\n                       \"while\", \"repeat\", \"until\", \"for\", \"in\", \"local\" ]);\n\nvar indentTokens = wordRE([\"function\", \"if\",\"repeat\",\"do\", \"\\\\(\", \"{\"]);\nvar dedentTokens = wordRE([\"end\", \"until\", \"\\\\)\", \"}\"]);\nvar dedentPartial = prefixRE([\"end\", \"until\", \"\\\\)\", \"}\", \"else\", \"elseif\"]);\n\nfunction readBracket(stream) {\n  var level = 0;\n  while (stream.eat(\"=\")) ++level;\n  stream.eat(\"[\");\n  return level;\n}\n\nfunction normal(stream, state) {\n  var ch = stream.next();\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    if (stream.eat(\"[\") && stream.eat(\"[\"))\n      return (state.cur = bracketed(readBracket(stream), \"comment\"))(stream, state);\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (ch == \"\\\"\" || ch == \"'\")\n    return (state.cur = string(ch))(stream, state);\n  if (ch == \"[\" && /[\\[=]/.test(stream.peek()))\n    return (state.cur = bracketed(readBracket(stream), \"string\"))(stream, state);\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w.%]/);\n    return \"number\";\n  }\n  if (/[\\w_]/.test(ch)) {\n    stream.eatWhile(/[\\w\\\\\\-_.]/);\n    return \"variable\";\n  }\n  return null;\n}\n\nfunction bracketed(level, style) {\n  return function(stream, state) {\n    var curlev = null, ch;\n    while ((ch = stream.next()) != null) {\n      if (curlev == null) {if (ch == \"]\") curlev = 0;}\n      else if (ch == \"=\") ++curlev;\n      else if (ch == \"]\" && curlev == level) { state.cur = normal; break; }\n      else curlev = null;\n    }\n    return style;\n  };\n}\n\nfunction string(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.cur = normal;\n    return \"string\";\n  };\n}\n\nexport const lua = {\n  name: \"lua\",\n\n  startState: function() {\n    return {basecol: 0, indentDepth: 0, cur: normal};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.cur(stream, state);\n    var word = stream.current();\n    if (style == \"variable\") {\n      if (keywords.test(word)) style = \"keyword\";\n      else if (builtins.test(word)) style = \"builtin\";\n    }\n    if ((style != \"comment\") && (style != \"string\")){\n      if (indentTokens.test(word)) ++state.indentDepth;\n      else if (dedentTokens.test(word)) --state.indentDepth;\n    }\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var closing = dedentPartial.test(textAfter);\n    return state.basecol + cx.unit * (state.indentDepth - (closing ? 1 : 0));\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(?:end|until|else|\\)|\\})$/,\n    commentTokens: {line: \"--\", block: {open: \"--[[\", close: \"]]--\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,SAAS,OAAO;AACvB,SAAO,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI,KAAK,GAAG;AACvD;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;AACxD;AAGA,IAAI,WAAW,OAAO;AAAA,EACpB;AAAA,EAAK;AAAA,EAAW;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAe;AAAA,EAAS;AAAA,EAC7F;AAAA,EAAW;AAAA,EAAa;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAC7F;AAAA,EAAS;AAAA,EAAU;AAAA,EAAe;AAAA,EAAW;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAExE;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAmB;AAAA,EAAiB;AAAA,EAE9F;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAC/E;AAAA,EAAoB;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAiB;AAAA,EACxF;AAAA,EAAmB;AAAA,EAEnB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAEhD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAAY;AAAA,EACnG;AAAA,EAAY;AAAA,EAAa;AAAA,EAAU;AAAA,EAEnC;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAW;AAAA,EAAY;AAAA,EAC/F;AAAA,EAAW;AAAA,EAAa;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAa;AAAA,EAAW;AAAA,EAAa;AAAA,EAClG;AAAA,EAAW;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAW;AAAA,EAClG;AAAA,EAAY;AAAA,EAAW;AAAA,EAEvB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAc;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAC9F;AAAA,EAAU;AAAA,EAEV;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAe;AAAA,EACpF;AAAA,EAEA;AAAA,EAAc;AAAA,EAAc;AAAA,EAAc;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAgB;AAAA,EACxF;AAAA,EAAa;AAAA,EAAe;AAAA,EAAe;AAAA,EAAa;AAAA,EAAiB;AAAA,EAAa;AAAA,EAEtF;AAAA,EAAe;AAAA,EAAe;AAAA,EAAa;AAAA,EAAe;AAC5D,CAAC;AACD,IAAI,WAAW,OAAO;AAAA,EAAC;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAChD;AAAA,EAAO;AAAA,EAAY;AAAA,EAAO;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAChD;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAQ,CAAC;AAEzE,IAAI,eAAe,OAAO,CAAC,YAAY,MAAK,UAAS,MAAM,OAAO,GAAG,CAAC;AACtE,IAAI,eAAe,OAAO,CAAC,OAAO,SAAS,OAAO,GAAG,CAAC;AACtD,IAAI,gBAAgB,SAAS,CAAC,OAAO,SAAS,OAAO,KAAK,QAAQ,QAAQ,CAAC;AAE3E,SAAS,YAAY,QAAQ;AAC3B,MAAI,QAAQ;AACZ,SAAO,OAAO,IAAI,GAAG;AAAG,MAAE;AAC1B,SAAO,IAAI,GAAG;AACd,SAAO;AACT;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,QAAI,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG;AACnC,cAAQ,MAAM,MAAM,UAAU,YAAY,MAAM,GAAG,SAAS,GAAG,QAAQ,KAAK;AAC9E,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,OAAQ,MAAM;AACtB,YAAQ,MAAM,MAAM,OAAO,EAAE,GAAG,QAAQ,KAAK;AAC/C,MAAI,MAAM,OAAO,QAAQ,KAAK,OAAO,KAAK,CAAC;AACzC,YAAQ,MAAM,MAAM,UAAU,YAAY,MAAM,GAAG,QAAQ,GAAG,QAAQ,KAAK;AAC7E,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,WAAO,SAAS,YAAY;AAC5B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,UAAU,OAAO,OAAO;AAC/B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,SAAS,MAAM;AACnB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,UAAU,MAAM;AAAC,YAAI,MAAM;AAAK,mBAAS;AAAA,MAAE,WACtC,MAAM;AAAK,UAAE;AAAA,eACb,MAAM,OAAO,UAAU,OAAO;AAAE,cAAM,MAAM;AAAQ;AAAA,MAAO;AAC/D,iBAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,SAAS,CAAC;AAAS;AAC7B,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,QAAI,CAAC;AAAS,YAAM,MAAM;AAC1B,WAAO;AAAA,EACT;AACF;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO,EAAC,SAAS,GAAG,aAAa,GAAG,KAAK,OAAM;AAAA,EACjD;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,IAAI,QAAQ,KAAK;AACnC,QAAI,OAAO,OAAO,QAAQ;AAC1B,QAAI,SAAS,YAAY;AACvB,UAAI,SAAS,KAAK,IAAI;AAAG,gBAAQ;AAAA,eACxB,SAAS,KAAK,IAAI;AAAG,gBAAQ;AAAA,IACxC;AACA,QAAK,SAAS,aAAe,SAAS,UAAU;AAC9C,UAAI,aAAa,KAAK,IAAI;AAAG,UAAE,MAAM;AAAA,eAC5B,aAAa,KAAK,IAAI;AAAG,UAAE,MAAM;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,UAAU,cAAc,KAAK,SAAS;AAC1C,WAAO,MAAM,UAAU,GAAG,QAAQ,MAAM,eAAe,UAAU,IAAI;AAAA,EACvE;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,QAAQ,OAAO,OAAM,EAAC;AAAA,EAClE;AACF;", "names": []}