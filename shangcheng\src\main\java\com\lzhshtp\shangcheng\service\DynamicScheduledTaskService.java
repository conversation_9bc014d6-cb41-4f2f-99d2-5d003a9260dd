package com.lzhshtp.shangcheng.service;

import org.springframework.scheduling.support.CronTrigger;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 动态定时任务管理服务
 */
@Service
public class DynamicScheduledTaskService {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicScheduledTaskService.class);
    
    @Autowired
    private TaskScheduler taskScheduler;
    
    /**
     * 存储正在运行的定时任务
     */
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    
    /**
     * 调度任务
     * 
     * @param taskName 任务名称
     * @param cronExpression Cron表达式
     * @param task 任务执行逻辑
     */
    public void scheduleTask(String taskName, String cronExpression, Runnable task) {
        try {
            // 先取消现有任务
            cancelTask(taskName);
            
            // 创建新任务
            ScheduledFuture<?> future = taskScheduler.schedule(task, new CronTrigger(cronExpression));
            scheduledTasks.put(taskName, future);
            
            logger.info("定时任务 {} 已调度，Cron表达式：{}", taskName, cronExpression);
        } catch (Exception e) {
            logger.error("调度任务 {} 失败：{}", taskName, e.getMessage(), e);
            throw new RuntimeException("调度任务失败：" + e.getMessage());
        }
    }
    
    /**
     * 重新调度任务
     * 
     * @param taskName 任务名称
     * @param cronExpression 新的Cron表达式
     * @param task 任务执行逻辑
     */
    public void rescheduleTask(String taskName, String cronExpression, Runnable task) {
        logger.info("重新调度任务：{}", taskName);
        scheduleTask(taskName, cronExpression, task);
    }
    
    /**
     * 取消任务
     * 
     * @param taskName 任务名称
     */
    public void cancelTask(String taskName) {
        ScheduledFuture<?> future = scheduledTasks.get(taskName);
        if (future != null) {
            future.cancel(false);
            scheduledTasks.remove(taskName);
            logger.info("定时任务 {} 已取消", taskName);
        }
    }
    
    /**
     * 检查任务是否正在运行
     * 
     * @param taskName 任务名称
     * @return 是否正在运行
     */
    public boolean isTaskRunning(String taskName) {
        ScheduledFuture<?> future = scheduledTasks.get(taskName);
        return future != null && !future.isCancelled() && !future.isDone();
    }
    
    /**
     * 获取所有正在运行的任务名称
     * 
     * @return 任务名称集合
     */
    public java.util.Set<String> getRunningTaskNames() {
        return scheduledTasks.keySet();
    }
    
    /**
     * 停止所有任务
     */
    public void stopAllTasks() {
        logger.info("停止所有定时任务");
        scheduledTasks.forEach((taskName, future) -> {
            future.cancel(false);
            logger.info("任务 {} 已停止", taskName);
        });
        scheduledTasks.clear();
    }
}
