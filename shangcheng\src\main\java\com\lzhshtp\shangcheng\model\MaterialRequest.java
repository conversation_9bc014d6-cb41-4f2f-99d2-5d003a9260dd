package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 材料补充请求实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_material_requests")
public class MaterialRequest {
    
    @TableId(value = "lzhshtp_request_id", type = IdType.AUTO)
    private Long requestId;
    
    @TableField("lzhshtp_product_id")
    private Long productId;
    
    @TableField("lzhshtp_requester_type")
    private String requesterType;  // manual_audit, second_review
    
    @TableField("lzhshtp_requester_task_id")
    private Long requesterTaskId;
    
    @TableField("lzhshtp_admin_id")
    private Long adminId;
    
    @TableField("lzhshtp_required_materials")
    private String requiredMaterials;  // JSON格式
    
    @TableField("lzhshtp_request_reason")
    private String requestReason;
    
    @TableField("lzhshtp_status")
    private String status;  // waiting, submitted, approved, rejected
    
    @TableField("lzhshtp_request_time")
    private LocalDateTime requestTime;
    
    @TableField("lzhshtp_deadline")
    private LocalDateTime deadline;
    
    @TableField("lzhshtp_seller_notified")
    private Boolean sellerNotified;

    // 以下字段用于前端显示，不存储在数据库中
    @TableField(exist = false)
    private String productTitle;

    @TableField(exist = false)
    private String sellerNickname;

    @TableField(exist = false)
    private String adminNickname;
}
