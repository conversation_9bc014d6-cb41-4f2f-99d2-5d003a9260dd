package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 论坛帖子查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumPostQueryRequest {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    
    /**
     * 分类ID
     */
    private Integer categoryId;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 排序字段
     */
    private String orderBy = "posted_at";
    
    /**
     * 排序方式 asc/desc
     */
    private String orderDirection = "desc";
    
    /**
     * 是否只看置顶
     */
    private Boolean onlyPinned;
    
    /**
     * 是否启用置顶功能（置顶帖子优先显示）
     */
    private Boolean enablePinned;
} 