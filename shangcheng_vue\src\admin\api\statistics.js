import request from '@/utils/request'

/**
 * 获取平台概览统计数据
 */
export function getOverviewStatistics() {
  return request({
    url: '/admin/statistics/overview',
    method: 'get'
  })
}

/**
 * 获取用户统计数据
 * @param {number} days 统计天数
 */
export function getUserStatistics(days = 30) {
  return request({
    url: '/admin/statistics/users',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取商品统计数据
 * @param {number} days 统计天数
 */
export function getProductStatistics(days = 30) {
  return request({
    url: '/admin/statistics/products',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取订单统计数据
 * @param {number} days 统计天数
 */
export function getOrderStatistics(days = 30) {
  return request({
    url: '/admin/statistics/orders',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取论坛统计数据
 * @param {number} days 统计天数
 */
export function getForumStatistics(days = 30) {
  return request({
    url: '/admin/statistics/forum',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取热门商品排行
 * @param {number} limit 排行数量
 * @param {string} type 排行类型：views、favorites、orders
 */
export function getHotProducts(limit = 10, type = 'views') {
  return request({
    url: '/admin/statistics/hot-products',
    method: 'get',
    params: { limit, type }
  })
}

/**
 * 获取用户地域分布统计
 */
export function getUserLocationStatistics() {
  return request({
    url: '/admin/statistics/user-locations',
    method: 'get'
  })
}

/**
 * 获取实时统计数据
 */
export function getRealtimeStatistics() {
  return request({
    url: '/admin/statistics/realtime',
    method: 'get'
  })
}

/**
 * 获取自定义时间范围的统计数据
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @param {string} type 统计类型
 */
export function getCustomStatistics(startDate, endDate, type) {
  return request({
    url: '/admin/statistics/custom',
    method: 'get',
    params: { startDate, endDate, type }
  })
}
