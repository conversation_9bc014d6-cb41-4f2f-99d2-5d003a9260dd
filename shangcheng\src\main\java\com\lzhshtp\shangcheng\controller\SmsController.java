package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.enums.SmsType;
import com.lzhshtp.shangcheng.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 短信验证码控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/sms")
public class SmsController {
    
    @Autowired
    private SmsService smsService;
    
    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    /**
     * 发送验证码
     */
    @PostMapping("/send")
    public ApiResponse<Map<String, Object>> sendVerificationCode(
            @RequestParam String phone,
            @RequestParam(defaultValue = "login") String type) {
        
        try {
            log.info("收到发送验证码请求，手机号：{}，类型：{}", phone, type);
            
            // 1. 验证手机号格式
            if (!isValidPhone(phone)) {
                return ApiResponse.fail("手机号格式不正确");
            }
            
            // 2. 验证类型
            if (!isValidType(type)) {
                return ApiResponse.fail("验证码类型不正确");
            }
            
            // 3. 检查发送限制
            if (!smsService.canSendCode(phone)) {
                long remainingTime = smsService.getRemainingWaitTime(phone);
                return ApiResponse.fail("发送过于频繁，请" + remainingTime + "秒后再试");
            }
            
            // 4. 发送验证码
            boolean success = smsService.sendVerificationCode(phone, type);
            
            if (success) {
                Map<String, Object> data = new HashMap<>();
                data.put("message", "验证码发送成功");
                data.put("waitTime", 60);
                return ApiResponse.success(data);
            } else {
                return ApiResponse.fail("验证码发送失败，请稍后重试");
            }
            
        } catch (Exception e) {
            log.error("发送验证码异常", e);
            return ApiResponse.fail("系统异常，请稍后重试");
        }
    }
    
    /**
     * 验证验证码
     */
    @PostMapping("/verify")
    public ApiResponse<String> verifyCode(
            @RequestParam String phone,
            @RequestParam String code,
            @RequestParam(defaultValue = "login") String type) {
        
        try {
            log.info("收到验证验证码请求，手机号：{}，类型：{}", phone, type);
            
            // 1. 验证手机号格式
            if (!isValidPhone(phone)) {
                return ApiResponse.fail("手机号格式不正确");
            }
            
            // 2. 验证验证码格式
            if (!isValidCode(code)) {
                return ApiResponse.fail("验证码格式不正确");
            }
            
            // 3. 验证类型
            if (!isValidType(type)) {
                return ApiResponse.fail("验证码类型不正确");
            }
            
            // 4. 验证验证码
            boolean success = smsService.verifyCode(phone, code, type);
            
            if (success) {
                return ApiResponse.success("验证码验证成功");
            } else {
                return ApiResponse.fail("验证码错误或已过期");
            }
            
        } catch (Exception e) {
            log.error("验证验证码异常", e);
            return ApiResponse.fail("系统异常，请稍后重试");
        }
    }
    
    /**
     * 检查是否可以发送验证码
     */
    @GetMapping("/check")
    public ApiResponse<Map<String, Object>> checkSendStatus(@RequestParam String phone) {
        try {
            if (!isValidPhone(phone)) {
                return ApiResponse.fail("手机号格式不正确");
            }
            
            boolean canSend = smsService.canSendCode(phone);
            long remainingTime = smsService.getRemainingWaitTime(phone);
            
            Map<String, Object> data = new HashMap<>();
            data.put("canSend", canSend);
            data.put("remainingTime", remainingTime);
            
            return ApiResponse.success(data);
            
        } catch (Exception e) {
            log.error("检查发送状态异常", e);
            return ApiResponse.fail("系统异常，请稍后重试");
        }
    }
    
    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * 验证验证码格式
     */
    private boolean isValidCode(String code) {
        return code != null && code.matches("\\d{6}");
    }
    
    /**
     * 验证类型
     */
    private boolean isValidType(String type) {
        return SmsType.isValid(type);
    }
}
