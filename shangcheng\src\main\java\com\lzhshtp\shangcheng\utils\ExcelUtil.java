package com.lzhshtp.shangcheng.utils;

import com.lzhshtp.shangcheng.dto.CategoryImportDTO;
import com.lzhshtp.shangcheng.dto.UserImportDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel工具类
 */
public class ExcelUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);
    
    /**
     * 解析用户Excel文件
     * 
     * @param file Excel文件
     * @return 用户导入DTO列表
     * @throws IOException IO异常
     */
    public static List<UserImportDTO> parseUserExcel(MultipartFile file) throws IOException {
        List<UserImportDTO> userList = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行，从第二行开始读取
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                UserImportDTO user = new UserImportDTO();
                user.setRowNumber(i + 1); // Excel行号从1开始
                
                try {
                    // 读取各列数据
                    user.setUsername(getCellStringValue(row.getCell(0)));
                    user.setPassword(getCellStringValue(row.getCell(1)));
                    user.setEmail(getCellStringValue(row.getCell(2)));
                    user.setPhoneNumber(getCellStringValue(row.getCell(3)));
                    user.setAvatarUrl(getCellStringValue(row.getCell(4)));
                    user.setBio(getCellStringValue(row.getCell(5)));
                    user.setLocation(getCellStringValue(row.getCell(6)));
                    
                    // 信用分，默认100
                    String creditScoreStr = getCellStringValue(row.getCell(7));
                    user.setCreditScore(creditScoreStr != null && !creditScoreStr.trim().isEmpty() 
                        ? Integer.parseInt(creditScoreStr.trim()) : 100);
                    
                    // 是否激活，默认true
                    String isActiveStr = getCellStringValue(row.getCell(8));
                    user.setIsActive(isActiveStr == null || isActiveStr.trim().isEmpty() 
                        || "true".equalsIgnoreCase(isActiveStr.trim()) || "1".equals(isActiveStr.trim()));
                    
                    // 用户角色，默认general_user
                    String role = getCellStringValue(row.getCell(9));
                    user.setRole(role != null && !role.trim().isEmpty() ? role.trim() : "general_user");
                    
                    userList.add(user);
                } catch (Exception e) {
                    logger.warn("解析用户Excel第{}行数据失败: {}", i + 1, e.getMessage());
                    // 继续处理下一行
                }
            }
        }
        
        return userList;
    }
    
    /**
     * 解析分类Excel文件
     * 
     * @param file Excel文件
     * @return 分类导入DTO列表
     * @throws IOException IO异常
     */
    public static List<CategoryImportDTO> parseCategoryExcel(MultipartFile file) throws IOException {
        List<CategoryImportDTO> categoryList = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行，从第二行开始读取
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                CategoryImportDTO category = new CategoryImportDTO();
                category.setRowNumber(i + 1); // Excel行号从1开始
                
                try {
                    // 读取各列数据
                    category.setName(getCellStringValue(row.getCell(0)));
                    category.setDescription(getCellStringValue(row.getCell(1)));
                    category.setParentCategoryName(getCellStringValue(row.getCell(2)));
                    
                    // 排序权重
                    String sortOrderStr = getCellStringValue(row.getCell(3));
                    if (sortOrderStr != null && !sortOrderStr.trim().isEmpty()) {
                        category.setSortOrder(Integer.parseInt(sortOrderStr.trim()));
                    }
                    
                    categoryList.add(category);
                } catch (Exception e) {
                    logger.warn("解析分类Excel第{}行数据失败: {}", i + 1, e.getMessage());
                    // 继续处理下一行
                }
            }
        }
        
        return categoryList;
    }
    
    /**
     * 生成用户导入模板
     * 
     * @return Excel文件字节数组
     * @throws IOException IO异常
     */
    public static byte[] generateUserTemplate() throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("用户导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"用户名", "密码", "邮箱", "手机号", "头像URL", "个人简介", "所在地区", "信用分", "是否激活", "用户角色"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建示例数据行
            Row exampleRow = sheet.createRow(1);
            String[] examples = {"zhangsan", "123456", "<EMAIL>", "13800138001", "", "热爱生活", "北京市", "100", "true", "general_user"};
            
            for (int i = 0; i < examples.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(examples[i]);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 生成分类导入模板
     * 
     * @return Excel文件字节数组
     * @throws IOException IO异常
     */
    public static byte[] generateCategoryTemplate() throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("分类导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"分类名称", "分类描述", "父级分类名称", "排序权重"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建示例数据行
            String[][] examples = {
                {"电子产品", "各类电子设备和数码产品", "", "1"},
                {"手机通讯", "手机、平板等通讯设备", "电子产品", "1"},
                {"电脑办公", "笔记本、台式机、办公设备", "电子产品", "2"},
                {"服装鞋帽", "男装、女装、鞋子、帽子等", "", "2"}
            };
            
            for (int i = 0; i < examples.length; i++) {
                Row exampleRow = sheet.createRow(i + 1);
                for (int j = 0; j < examples[i].length; j++) {
                    Cell cell = exampleRow.createCell(j);
                    cell.setCellValue(examples[i][j]);
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 获取单元格字符串值
     * 
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 验证Excel文件格式
     * 
     * @param file 文件
     * @return 是否为有效的Excel文件
     */
    public static boolean isValidExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String fileName = file.getOriginalFilename();
        return fileName != null && (fileName.endsWith(".xlsx") || fileName.endsWith(".xls"));
    }
}
