package com.lzhshtp.shangcheng.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ForumPostRequest;
import com.lzhshtp.shangcheng.model.ForumPost;

/**
 * 论坛帖子服务接口
 */
public interface ForumPostService extends IService<ForumPost> {

    /**
     * 创建帖子
     * 
     * @param request 帖子请求
     * @param userId 用户ID
     * @return 帖子ID
     */
    Long createPost(ForumPostRequest request, Long userId);
    
    /**
     * 更新帖子
     * 
     * @param postId 帖子ID
     * @param request 帖子请求
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updatePost(Long postId, ForumPostRequest request, Long userId);
    
    /**
     * 删除帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deletePost(Long postId, Long userId);
    
    /**
     * 获取帖子详情
     * 
     * @param postId 帖子ID
     * @return 帖子详情
     */
    ForumPostDTO getPostDetail(Long postId);
    
    /**
     * 分页查询帖子列表
     * 
     * @param queryRequest 查询请求
     * @return 分页结果
     */
    IPage<ForumPostDTO> getPostList(ForumPostQueryRequest queryRequest);
    
    /**
     * 设置帖子置顶状态
     * 
     * @param postId 帖子ID
     * @param isPinned 是否置顶
     * @return 是否成功
     */
    boolean setPinned(Long postId, boolean isPinned);
    
    /**
     * 设置帖子状态
     * 
     * @param postId 帖子ID
     * @param status 状态
     * @return 是否成功
     */
    boolean setStatus(Long postId, Integer status);
    
    /**
     * 增加帖子浏览量
     * 
     * @param postId 帖子ID
     * @return 是否成功
     */
    boolean increaseViewCount(Long postId);
} 