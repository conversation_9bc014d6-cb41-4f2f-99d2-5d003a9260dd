package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 官方验货记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_verification_records")
public class VerificationRecord {
    
    @TableId(value = "lzhshtp_verification_id", type = IdType.AUTO)
    private Long verificationId;
    
    @TableField("lzhshtp_order_id")
    private Long orderId;
    
    @TableField("lzhshtp_verifier_id")
    private Long verifierId;
    
    @TableField("lzhshtp_verification_status")
    private String verificationStatus;
    
    @TableField("lzhshtp_verification_result")
    private String verificationResult;
    
    @TableField("lzhshtp_verification_images")
    private String verificationImages;
    
    @TableField("lzhshtp_received_time")
    private LocalDateTime receivedTime;
    
    @TableField("lzhshtp_verified_time")
    private LocalDateTime verifiedTime;
    
    @TableField("lzhshtp_forwarded_time")
    private LocalDateTime forwardedTime;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;

    // 注意：验货付费方和费用信息可以从关联的订单中获取，不需要在验货记录中重复存储

    /**
     * 验货状态枚举
     */
    public static class VerificationStatus {
        public static final String WAITING_GOODS = "waiting_goods";
        public static final String VERIFYING = "verifying";
        public static final String PASSED = "passed";
        public static final String FAILED = "failed";
        public static final String FORWARDED = "forwarded";
    }
}
