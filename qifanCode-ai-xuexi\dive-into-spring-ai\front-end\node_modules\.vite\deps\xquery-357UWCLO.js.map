{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/xquery.js"], "sourcesContent": ["// The keywords object is set to the result of this self executing\n// function. Each keyword is a property of the keywords object whose\n// value is {type: atype, style: astyle}\nvar keywords = function(){\n  // convenience functions used to build keywords object\n  function kw(type) {return {type: type, style: \"keyword\"};}\n  var operator = kw(\"operator\")\n  , atom = {type: \"atom\", style: \"atom\"}\n  , punctuation = {type: \"punctuation\", style: null}\n  , qualifier = {type: \"axis_specifier\", style: \"qualifier\"};\n\n  // kwObj is what is return from this function at the end\n  var kwObj = {\n    ',': punctuation\n  };\n\n  // a list of 'basic' keywords. For each add a property to kwObj with the value of\n  // {type: basic[i], style: \"keyword\"} e.g. 'after' --> {type: \"after\", style: \"keyword\"}\n  var basic = ['after', 'all', 'allowing', 'ancestor', 'ancestor-or-self', 'any', 'array', 'as',\n               'ascending', 'at', 'attribute', 'base-uri', 'before', 'boundary-space', 'by', 'case', 'cast',\n               'castable', 'catch', 'child', 'collation', 'comment', 'construction', 'contains', 'content',\n               'context', 'copy', 'copy-namespaces', 'count', 'decimal-format', 'declare', 'default', 'delete',\n               'descendant', 'descendant-or-self', 'descending', 'diacritics', 'different', 'distance',\n               'document', 'document-node', 'element', 'else', 'empty', 'empty-sequence', 'encoding', 'end',\n               'entire', 'every', 'exactly', 'except', 'external', 'first', 'following', 'following-sibling',\n               'for', 'from', 'ftand', 'ftnot', 'ft-option', 'ftor', 'function', 'fuzzy', 'greatest', 'group',\n               'if', 'import', 'in', 'inherit', 'insensitive', 'insert', 'instance', 'intersect', 'into',\n               'invoke', 'is', 'item', 'language', 'last', 'lax', 'least', 'let', 'levels', 'lowercase', 'map',\n               'modify', 'module', 'most', 'namespace', 'next', 'no', 'node', 'nodes', 'no-inherit',\n               'no-preserve', 'not', 'occurs', 'of', 'only', 'option', 'order', 'ordered', 'ordering',\n               'paragraph', 'paragraphs', 'parent', 'phrase', 'preceding', 'preceding-sibling', 'preserve',\n               'previous', 'processing-instruction', 'relationship', 'rename', 'replace', 'return',\n               'revalidation', 'same', 'satisfies', 'schema', 'schema-attribute', 'schema-element', 'score',\n               'self', 'sensitive', 'sentence', 'sentences', 'sequence', 'skip', 'sliding', 'some', 'stable',\n               'start', 'stemming', 'stop', 'strict', 'strip', 'switch', 'text', 'then', 'thesaurus', 'times',\n               'to', 'transform', 'treat', 'try', 'tumbling', 'type', 'typeswitch', 'union', 'unordered',\n               'update', 'updating', 'uppercase', 'using', 'validate', 'value', 'variable', 'version',\n               'weight', 'when', 'where', 'wildcards', 'window', 'with', 'without', 'word', 'words', 'xquery'];\n  for(var i=0, l=basic.length; i < l; i++) { kwObj[basic[i]] = kw(basic[i]);};\n\n  // a list of types. For each add a property to kwObj with the value of\n  // {type: \"atom\", style: \"atom\"}\n  var types = ['xs:anyAtomicType', 'xs:anySimpleType', 'xs:anyType', 'xs:anyURI',\n               'xs:base64Binary', 'xs:boolean', 'xs:byte', 'xs:date', 'xs:dateTime', 'xs:dateTimeStamp',\n               'xs:dayTimeDuration', 'xs:decimal', 'xs:double', 'xs:duration', 'xs:ENTITIES', 'xs:ENTITY',\n               'xs:float', 'xs:gDay', 'xs:gMonth', 'xs:gMonthDay', 'xs:gYear', 'xs:gYearMonth', 'xs:hexBinary',\n               'xs:ID', 'xs:IDREF', 'xs:IDREFS', 'xs:int', 'xs:integer', 'xs:item', 'xs:java', 'xs:language',\n               'xs:long', 'xs:Name', 'xs:NCName', 'xs:negativeInteger', 'xs:NMTOKEN', 'xs:NMTOKENS',\n               'xs:nonNegativeInteger', 'xs:nonPositiveInteger', 'xs:normalizedString', 'xs:NOTATION',\n               'xs:numeric', 'xs:positiveInteger', 'xs:precisionDecimal', 'xs:QName', 'xs:short', 'xs:string',\n               'xs:time', 'xs:token', 'xs:unsignedByte', 'xs:unsignedInt', 'xs:unsignedLong',\n               'xs:unsignedShort', 'xs:untyped', 'xs:untypedAtomic', 'xs:yearMonthDuration'];\n  for(var i=0, l=types.length; i < l; i++) { kwObj[types[i]] = atom;};\n\n  // each operator will add a property to kwObj with value of {type: \"operator\", style: \"keyword\"}\n  var operators = ['eq', 'ne', 'lt', 'le', 'gt', 'ge', ':=', '=', '>', '>=', '<', '<=', '.', '|', '?', 'and', 'or', 'div', 'idiv', 'mod', '*', '/', '+', '-'];\n  for(var i=0, l=operators.length; i < l; i++) { kwObj[operators[i]] = operator;};\n\n  // each axis_specifiers will add a property to kwObj with value of {type: \"axis_specifier\", style: \"qualifier\"}\n  var axis_specifiers = [\"self::\", \"attribute::\", \"child::\", \"descendant::\", \"descendant-or-self::\", \"parent::\",\n                         \"ancestor::\", \"ancestor-or-self::\", \"following::\", \"preceding::\", \"following-sibling::\", \"preceding-sibling::\"];\n  for(var i=0, l=axis_specifiers.length; i < l; i++) { kwObj[axis_specifiers[i]] = qualifier; };\n\n  return kwObj;\n}();\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\n// the primary mode tokenizer\nfunction tokenBase(stream, state) {\n  var ch = stream.next(),\n      mightBeFunction = false,\n      isEQName = isEQNameAhead(stream);\n\n  // an XML tag (if not in some sub, chained tokenizer)\n  if (ch == \"<\") {\n    if(stream.match(\"!--\", true))\n      return chain(stream, state, tokenXMLComment);\n\n    if(stream.match(\"![CDATA\", false)) {\n      state.tokenize = tokenCDATA;\n      return \"tag\";\n    }\n\n    if(stream.match(\"?\", false)) {\n      return chain(stream, state, tokenPreProcessing);\n    }\n\n    var isclose = stream.eat(\"/\");\n    stream.eatSpace();\n    var tagName = \"\", c;\n    while ((c = stream.eat(/[^\\s\\u00a0=<>\\\"\\'\\/?]/))) tagName += c;\n\n    return chain(stream, state, tokenTag(tagName, isclose));\n  }\n  // start code block\n  else if(ch == \"{\") {\n    pushStateStack(state, { type: \"codeblock\"});\n    return null;\n  }\n  // end code block\n  else if(ch == \"}\") {\n    popStateStack(state);\n    return null;\n  }\n  // if we're in an XML block\n  else if(isInXmlBlock(state)) {\n    if(ch == \">\")\n      return \"tag\";\n    else if(ch == \"/\" && stream.eat(\">\")) {\n      popStateStack(state);\n      return \"tag\";\n    }\n    else\n      return \"variable\";\n  }\n  // if a number\n  else if (/\\d/.test(ch)) {\n    stream.match(/^\\d*(?:\\.\\d*)?(?:E[+\\-]?\\d+)?/);\n    return \"atom\";\n  }\n  // comment start\n  else if (ch === \"(\" && stream.eat(\":\")) {\n    pushStateStack(state, { type: \"comment\"});\n    return chain(stream, state, tokenComment);\n  }\n  // quoted string\n  else if (!isEQName && (ch === '\"' || ch === \"'\"))\n    return chain(stream, state, tokenString(ch));\n  // variable\n  else if(ch === \"$\") {\n    return chain(stream, state, tokenVariable);\n  }\n  // assignment\n  else if(ch ===\":\" && stream.eat(\"=\")) {\n    return \"keyword\";\n  }\n  // open paren\n  else if(ch === \"(\") {\n    pushStateStack(state, { type: \"paren\"});\n    return null;\n  }\n  // close paren\n  else if(ch === \")\") {\n    popStateStack(state);\n    return null;\n  }\n  // open paren\n  else if(ch === \"[\") {\n    pushStateStack(state, { type: \"bracket\"});\n    return null;\n  }\n  // close paren\n  else if(ch === \"]\") {\n    popStateStack(state);\n    return null;\n  }\n  else {\n    var known = keywords.propertyIsEnumerable(ch) && keywords[ch];\n\n    // if there's a EQName ahead, consume the rest of the string portion, it's likely a function\n    if(isEQName && ch === '\\\"') while(stream.next() !== '\"'){}\n    if(isEQName && ch === '\\'') while(stream.next() !== '\\''){}\n\n    // gobble up a word if the character is not known\n    if(!known) stream.eatWhile(/[\\w\\$_-]/);\n\n    // gobble a colon in the case that is a lib func type call fn:doc\n    var foundColon = stream.eat(\":\");\n\n    // if there's not a second colon, gobble another word. Otherwise, it's probably an axis specifier\n    // which should get matched as a keyword\n    if(!stream.eat(\":\") && foundColon) {\n      stream.eatWhile(/[\\w\\$_-]/);\n    }\n    // if the next non whitespace character is an open paren, this is probably a function (if not a keyword of other sort)\n    if(stream.match(/^[ \\t]*\\(/, false)) {\n      mightBeFunction = true;\n    }\n    // is the word a keyword?\n    var word = stream.current();\n    known = keywords.propertyIsEnumerable(word) && keywords[word];\n\n    // if we think it's a function call but not yet known,\n    // set style to variable for now for lack of something better\n    if(mightBeFunction && !known) known = {type: \"function_call\", style: \"def\"};\n\n    // if the previous word was element, attribute, axis specifier, this word should be the name of that\n    if(isInXmlConstructor(state)) {\n      popStateStack(state);\n      return \"variable\";\n    }\n    // as previously checked, if the word is element,attribute, axis specifier, call it an \"xmlconstructor\" and\n    // push the stack so we know to look for it on the next word\n    if(word == \"element\" || word == \"attribute\" || known.type == \"axis_specifier\") pushStateStack(state, {type: \"xmlconstructor\"});\n\n    // if the word is known, return the details of that else just call this a generic 'word'\n    return known ? known.style : \"variable\";\n  }\n}\n\n// handle comments, including nested\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, maybeNested = false, nestedCount = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \")\" && maybeEnd) {\n      if(nestedCount > 0)\n        nestedCount--;\n      else {\n        popStateStack(state);\n        break;\n      }\n    }\n    else if(ch == \":\" && maybeNested) {\n      nestedCount++;\n    }\n    maybeEnd = (ch == \":\");\n    maybeNested = (ch == \"(\");\n  }\n\n  return \"comment\";\n}\n\n// tokenizer for string literals\n// optionally pass a tokenizer function to set state.tokenize back to when finished\nfunction tokenString(quote, f) {\n  return function(stream, state) {\n    var ch;\n\n    if(isInString(state) && stream.current() == quote) {\n      popStateStack(state);\n      if(f) state.tokenize = f;\n      return \"string\";\n    }\n\n    pushStateStack(state, { type: \"string\", name: quote, tokenize: tokenString(quote, f) });\n\n    // if we're in a string and in an XML block, allow an embedded code block\n    if(stream.match(\"{\", false) && isInXmlAttributeBlock(state)) {\n      state.tokenize = tokenBase;\n      return \"string\";\n    }\n\n\n    while (ch = stream.next()) {\n      if (ch ==  quote) {\n        popStateStack(state);\n        if(f) state.tokenize = f;\n        break;\n      }\n      else {\n        // if we're in a string and in an XML block, allow an embedded code block in an attribute\n        if(stream.match(\"{\", false) && isInXmlAttributeBlock(state)) {\n          state.tokenize = tokenBase;\n          return \"string\";\n        }\n\n      }\n    }\n\n    return \"string\";\n  };\n}\n\n// tokenizer for variables\nfunction tokenVariable(stream, state) {\n  var isVariableChar = /[\\w\\$_-]/;\n\n  // a variable may start with a quoted EQName so if the next character is quote, consume to the next quote\n  if(stream.eat(\"\\\"\")) {\n    while(stream.next() !== '\\\"'){};\n    stream.eat(\":\");\n  } else {\n    stream.eatWhile(isVariableChar);\n    if(!stream.match(\":=\", false)) stream.eat(\":\");\n  }\n  stream.eatWhile(isVariableChar);\n  state.tokenize = tokenBase;\n  return \"variable\";\n}\n\n// tokenizer for XML tags\nfunction tokenTag(name, isclose) {\n  return function(stream, state) {\n    stream.eatSpace();\n    if(isclose && stream.eat(\">\")) {\n      popStateStack(state);\n      state.tokenize = tokenBase;\n      return \"tag\";\n    }\n    // self closing tag without attributes?\n    if(!stream.eat(\"/\"))\n      pushStateStack(state, { type: \"tag\", name: name, tokenize: tokenBase});\n    if(!stream.eat(\">\")) {\n      state.tokenize = tokenAttribute;\n      return \"tag\";\n    }\n    else {\n      state.tokenize = tokenBase;\n    }\n    return \"tag\";\n  };\n}\n\n// tokenizer for XML attributes\nfunction tokenAttribute(stream, state) {\n  var ch = stream.next();\n\n  if(ch == \"/\" && stream.eat(\">\")) {\n    if(isInXmlAttributeBlock(state)) popStateStack(state);\n    if(isInXmlBlock(state)) popStateStack(state);\n    return \"tag\";\n  }\n  if(ch == \">\") {\n    if(isInXmlAttributeBlock(state)) popStateStack(state);\n    return \"tag\";\n  }\n  if(ch == \"=\")\n    return null;\n  // quoted string\n  if (ch == '\"' || ch == \"'\")\n    return chain(stream, state, tokenString(ch, tokenAttribute));\n\n  if(!isInXmlAttributeBlock(state))\n    pushStateStack(state, { type: \"attribute\", tokenize: tokenAttribute});\n\n  stream.eat(/[a-zA-Z_:]/);\n  stream.eatWhile(/[-a-zA-Z0-9_:.]/);\n  stream.eatSpace();\n\n  // the case where the attribute has not value and the tag was closed\n  if(stream.match(\">\", false) || stream.match(\"/\", false)) {\n    popStateStack(state);\n    state.tokenize = tokenBase;\n  }\n\n  return \"attribute\";\n}\n\n// handle comments, including nested\nfunction tokenXMLComment(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"-\" && stream.match(\"->\", true)) {\n      state.tokenize = tokenBase;\n      return \"comment\";\n    }\n  }\n}\n\n\n// handle CDATA\nfunction tokenCDATA(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"]\" && stream.match(\"]\", true)) {\n      state.tokenize = tokenBase;\n      return \"comment\";\n    }\n  }\n}\n\n// handle preprocessing instructions\nfunction tokenPreProcessing(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"?\" && stream.match(\">\", true)) {\n      state.tokenize = tokenBase;\n      return \"processingInstruction\";\n    }\n  }\n}\n\n\n// functions to test the current context of the state\nfunction isInXmlBlock(state) { return isIn(state, \"tag\"); }\nfunction isInXmlAttributeBlock(state) { return isIn(state, \"attribute\"); }\nfunction isInXmlConstructor(state) { return isIn(state, \"xmlconstructor\"); }\nfunction isInString(state) { return isIn(state, \"string\"); }\n\nfunction isEQNameAhead(stream) {\n  // assume we've already eaten a quote (\")\n  if(stream.current() === '\"')\n    return stream.match(/^[^\\\"]+\\\"\\:/, false);\n  else if(stream.current() === '\\'')\n    return stream.match(/^[^\\\"]+\\'\\:/, false);\n  else\n    return false;\n}\n\nfunction isIn(state, type) {\n  return (state.stack.length && state.stack[state.stack.length - 1].type == type);\n}\n\nfunction pushStateStack(state, newState) {\n  state.stack.push(newState);\n}\n\nfunction popStateStack(state) {\n  state.stack.pop();\n  var reinstateTokenize = state.stack.length && state.stack[state.stack.length-1].tokenize;\n  state.tokenize = reinstateTokenize || tokenBase;\n}\n\n// the interface for the mode API\nexport const xQuery = {\n  name: \"xquery\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      cc: [],\n      stack: []\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n\n  languageData: {\n    commentTokens: {block: {open: \"(:\", close: \":)\"}}\n  }\n};\n"], "mappings": ";;;AAGA,IAAI,WAAW,WAAU;AAEvB,WAAS,GAAG,MAAM;AAAC,WAAO,EAAC,MAAY,OAAO,UAAS;AAAA,EAAE;AACzD,MAAI,WAAW,GAAG,UAAU,GAC1B,OAAO,EAAC,MAAM,QAAQ,OAAO,OAAM,GACnC,cAAc,EAAC,MAAM,eAAe,OAAO,KAAI,GAC/C,YAAY,EAAC,MAAM,kBAAkB,OAAO,YAAW;AAGzD,MAAI,QAAQ;AAAA,IACV,KAAK;AAAA,EACP;AAIA,MAAI,QAAQ;AAAA,IAAC;AAAA,IAAS;AAAA,IAAO;AAAA,IAAY;AAAA,IAAY;AAAA,IAAoB;AAAA,IAAO;AAAA,IAAS;AAAA,IAC5E;AAAA,IAAa;AAAA,IAAM;AAAA,IAAa;AAAA,IAAY;AAAA,IAAU;AAAA,IAAkB;AAAA,IAAM;AAAA,IAAQ;AAAA,IACtF;AAAA,IAAY;AAAA,IAAS;AAAA,IAAS;AAAA,IAAa;AAAA,IAAW;AAAA,IAAgB;AAAA,IAAY;AAAA,IAClF;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAmB;AAAA,IAAS;AAAA,IAAkB;AAAA,IAAW;AAAA,IAAW;AAAA,IACvF;AAAA,IAAc;AAAA,IAAsB;AAAA,IAAc;AAAA,IAAc;AAAA,IAAa;AAAA,IAC7E;AAAA,IAAY;AAAA,IAAiB;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAkB;AAAA,IAAY;AAAA,IACvF;AAAA,IAAU;AAAA,IAAS;AAAA,IAAW;AAAA,IAAU;AAAA,IAAY;AAAA,IAAS;AAAA,IAAa;AAAA,IAC1E;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAS;AAAA,IAAa;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAS;AAAA,IAAY;AAAA,IACvF;AAAA,IAAM;AAAA,IAAU;AAAA,IAAM;AAAA,IAAW;AAAA,IAAe;AAAA,IAAU;AAAA,IAAY;AAAA,IAAa;AAAA,IACnF;AAAA,IAAU;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAO;AAAA,IAAS;AAAA,IAAO;AAAA,IAAU;AAAA,IAAa;AAAA,IAC1F;AAAA,IAAU;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAQ;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAS;AAAA,IACxE;AAAA,IAAe;AAAA,IAAO;AAAA,IAAU;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAS;AAAA,IAAW;AAAA,IAC5E;AAAA,IAAa;AAAA,IAAc;AAAA,IAAU;AAAA,IAAU;AAAA,IAAa;AAAA,IAAqB;AAAA,IACjF;AAAA,IAAY;AAAA,IAA0B;AAAA,IAAgB;AAAA,IAAU;AAAA,IAAW;AAAA,IAC3E;AAAA,IAAgB;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAU;AAAA,IAAoB;AAAA,IAAkB;AAAA,IACrF;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAY;AAAA,IAAa;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAQ;AAAA,IACrF;AAAA,IAAS;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAS;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAa;AAAA,IACvF;AAAA,IAAM;AAAA,IAAa;AAAA,IAAS;AAAA,IAAO;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAc;AAAA,IAAS;AAAA,IAC9E;AAAA,IAAU;AAAA,IAAY;AAAA,IAAa;AAAA,IAAS;AAAA,IAAY;AAAA,IAAS;AAAA,IAAY;AAAA,IAC7E;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAa;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAS;AAAA,EAAQ;AAC3G,WAAQ,IAAE,GAAG,IAAE,MAAM,QAAQ,IAAI,GAAG,KAAK;AAAE,UAAM,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;AAAA,EAAE;AAAC;AAI3E,MAAI,QAAQ;AAAA,IAAC;AAAA,IAAoB;AAAA,IAAoB;AAAA,IAAc;AAAA,IACtD;AAAA,IAAmB;AAAA,IAAc;AAAA,IAAW;AAAA,IAAW;AAAA,IAAe;AAAA,IACtE;AAAA,IAAsB;AAAA,IAAc;AAAA,IAAa;AAAA,IAAe;AAAA,IAAe;AAAA,IAC/E;AAAA,IAAY;AAAA,IAAW;AAAA,IAAa;AAAA,IAAgB;AAAA,IAAY;AAAA,IAAiB;AAAA,IACjF;AAAA,IAAS;AAAA,IAAY;AAAA,IAAa;AAAA,IAAU;AAAA,IAAc;AAAA,IAAW;AAAA,IAAW;AAAA,IAChF;AAAA,IAAW;AAAA,IAAW;AAAA,IAAa;AAAA,IAAsB;AAAA,IAAc;AAAA,IACvE;AAAA,IAAyB;AAAA,IAAyB;AAAA,IAAuB;AAAA,IACzE;AAAA,IAAc;AAAA,IAAsB;AAAA,IAAuB;AAAA,IAAY;AAAA,IAAY;AAAA,IACnF;AAAA,IAAW;AAAA,IAAY;AAAA,IAAmB;AAAA,IAAkB;AAAA,IAC5D;AAAA,IAAoB;AAAA,IAAc;AAAA,IAAoB;AAAA,EAAsB;AACzF,WAAQ,IAAE,GAAG,IAAE,MAAM,QAAQ,IAAI,GAAG,KAAK;AAAE,UAAM,MAAM,CAAC,CAAC,IAAI;AAAA,EAAK;AAAC;AAGnE,MAAI,YAAY,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,MAAM,OAAO,QAAQ,OAAO,KAAK,KAAK,KAAK,GAAG;AAC1J,WAAQ,IAAE,GAAG,IAAE,UAAU,QAAQ,IAAI,GAAG,KAAK;AAAE,UAAM,UAAU,CAAC,CAAC,IAAI;AAAA,EAAS;AAAC;AAG/E,MAAI,kBAAkB;AAAA,IAAC;AAAA,IAAU;AAAA,IAAe;AAAA,IAAW;AAAA,IAAgB;AAAA,IAAwB;AAAA,IAC5E;AAAA,IAAc;AAAA,IAAsB;AAAA,IAAe;AAAA,IAAe;AAAA,IAAuB;AAAA,EAAqB;AACrI,WAAQ,IAAE,GAAG,IAAE,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAAE,UAAM,gBAAgB,CAAC,CAAC,IAAI;AAAA,EAAW;AAAC;AAE7F,SAAO;AACT,EAAE;AAEF,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AAGA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK,GACjB,kBAAkB,OAClB,WAAW,cAAc,MAAM;AAGnC,MAAI,MAAM,KAAK;AACb,QAAG,OAAO,MAAM,OAAO,IAAI;AACzB,aAAO,MAAM,QAAQ,OAAO,eAAe;AAE7C,QAAG,OAAO,MAAM,WAAW,KAAK,GAAG;AACjC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAEA,QAAG,OAAO,MAAM,KAAK,KAAK,GAAG;AAC3B,aAAO,MAAM,QAAQ,OAAO,kBAAkB;AAAA,IAChD;AAEA,QAAI,UAAU,OAAO,IAAI,GAAG;AAC5B,WAAO,SAAS;AAChB,QAAI,UAAU,IAAI;AAClB,WAAQ,IAAI,OAAO,IAAI,uBAAuB;AAAI,iBAAW;AAE7D,WAAO,MAAM,QAAQ,OAAO,SAAS,SAAS,OAAO,CAAC;AAAA,EACxD,WAEQ,MAAM,KAAK;AACjB,mBAAe,OAAO,EAAE,MAAM,YAAW,CAAC;AAC1C,WAAO;AAAA,EACT,WAEQ,MAAM,KAAK;AACjB,kBAAc,KAAK;AACnB,WAAO;AAAA,EACT,WAEQ,aAAa,KAAK,GAAG;AAC3B,QAAG,MAAM;AACP,aAAO;AAAA,aACD,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACpC,oBAAc,KAAK;AACnB,aAAO;AAAA,IACT;AAEE,aAAO;AAAA,EACX,WAES,KAAK,KAAK,EAAE,GAAG;AACtB,WAAO,MAAM,+BAA+B;AAC5C,WAAO;AAAA,EACT,WAES,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACtC,mBAAe,OAAO,EAAE,MAAM,UAAS,CAAC;AACxC,WAAO,MAAM,QAAQ,OAAO,YAAY;AAAA,EAC1C,WAES,CAAC,aAAa,OAAO,OAAO,OAAO;AAC1C,WAAO,MAAM,QAAQ,OAAO,YAAY,EAAE,CAAC;AAAA,WAErC,OAAO,KAAK;AAClB,WAAO,MAAM,QAAQ,OAAO,aAAa;AAAA,EAC3C,WAEQ,OAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACpC,WAAO;AAAA,EACT,WAEQ,OAAO,KAAK;AAClB,mBAAe,OAAO,EAAE,MAAM,QAAO,CAAC;AACtC,WAAO;AAAA,EACT,WAEQ,OAAO,KAAK;AAClB,kBAAc,KAAK;AACnB,WAAO;AAAA,EACT,WAEQ,OAAO,KAAK;AAClB,mBAAe,OAAO,EAAE,MAAM,UAAS,CAAC;AACxC,WAAO;AAAA,EACT,WAEQ,OAAO,KAAK;AAClB,kBAAc,KAAK;AACnB,WAAO;AAAA,EACT,OACK;AACH,QAAI,QAAQ,SAAS,qBAAqB,EAAE,KAAK,SAAS,EAAE;AAG5D,QAAG,YAAY,OAAO;AAAM,aAAM,OAAO,KAAK,MAAM,KAAI;AAAA,MAAC;AACzD,QAAG,YAAY,OAAO;AAAM,aAAM,OAAO,KAAK,MAAM,KAAK;AAAA,MAAC;AAG1D,QAAG,CAAC;AAAO,aAAO,SAAS,UAAU;AAGrC,QAAI,aAAa,OAAO,IAAI,GAAG;AAI/B,QAAG,CAAC,OAAO,IAAI,GAAG,KAAK,YAAY;AACjC,aAAO,SAAS,UAAU;AAAA,IAC5B;AAEA,QAAG,OAAO,MAAM,aAAa,KAAK,GAAG;AACnC,wBAAkB;AAAA,IACpB;AAEA,QAAI,OAAO,OAAO,QAAQ;AAC1B,YAAQ,SAAS,qBAAqB,IAAI,KAAK,SAAS,IAAI;AAI5D,QAAG,mBAAmB,CAAC;AAAO,cAAQ,EAAC,MAAM,iBAAiB,OAAO,MAAK;AAG1E,QAAG,mBAAmB,KAAK,GAAG;AAC5B,oBAAc,KAAK;AACnB,aAAO;AAAA,IACT;AAGA,QAAG,QAAQ,aAAa,QAAQ,eAAe,MAAM,QAAQ;AAAkB,qBAAe,OAAO,EAAC,MAAM,iBAAgB,CAAC;AAG7H,WAAO,QAAQ,MAAM,QAAQ;AAAA,EAC/B;AACF;AAGA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO,cAAc,OAAO,cAAc,GAAG;AAC5D,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,UAAG,cAAc;AACf;AAAA,WACG;AACH,sBAAc,KAAK;AACnB;AAAA,MACF;AAAA,IACF,WACQ,MAAM,OAAO,aAAa;AAChC;AAAA,IACF;AACA,eAAY,MAAM;AAClB,kBAAe,MAAM;AAAA,EACvB;AAEA,SAAO;AACT;AAIA,SAAS,YAAY,OAAO,GAAG;AAC7B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI;AAEJ,QAAG,WAAW,KAAK,KAAK,OAAO,QAAQ,KAAK,OAAO;AACjD,oBAAc,KAAK;AACnB,UAAG;AAAG,cAAM,WAAW;AACvB,aAAO;AAAA,IACT;AAEA,mBAAe,OAAO,EAAE,MAAM,UAAU,MAAM,OAAO,UAAU,YAAY,OAAO,CAAC,EAAE,CAAC;AAGtF,QAAG,OAAO,MAAM,KAAK,KAAK,KAAK,sBAAsB,KAAK,GAAG;AAC3D,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAGA,WAAO,KAAK,OAAO,KAAK,GAAG;AACzB,UAAI,MAAO,OAAO;AAChB,sBAAc,KAAK;AACnB,YAAG;AAAG,gBAAM,WAAW;AACvB;AAAA,MACF,OACK;AAEH,YAAG,OAAO,MAAM,KAAK,KAAK,KAAK,sBAAsB,KAAK,GAAG;AAC3D,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACT;AAAA,MAEF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,iBAAiB;AAGrB,MAAG,OAAO,IAAI,GAAI,GAAG;AACnB,WAAM,OAAO,KAAK,MAAM,KAAK;AAAA,IAAC;AAAC;AAC/B,WAAO,IAAI,GAAG;AAAA,EAChB,OAAO;AACL,WAAO,SAAS,cAAc;AAC9B,QAAG,CAAC,OAAO,MAAM,MAAM,KAAK;AAAG,aAAO,IAAI,GAAG;AAAA,EAC/C;AACA,SAAO,SAAS,cAAc;AAC9B,QAAM,WAAW;AACjB,SAAO;AACT;AAGA,SAAS,SAAS,MAAM,SAAS;AAC/B,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,SAAS;AAChB,QAAG,WAAW,OAAO,IAAI,GAAG,GAAG;AAC7B,oBAAc,KAAK;AACnB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAEA,QAAG,CAAC,OAAO,IAAI,GAAG;AAChB,qBAAe,OAAO,EAAE,MAAM,OAAO,MAAY,UAAU,UAAS,CAAC;AACvE,QAAG,CAAC,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,OACK;AACH,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,KAAK,OAAO,KAAK;AAErB,MAAG,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAC/B,QAAG,sBAAsB,KAAK;AAAG,oBAAc,KAAK;AACpD,QAAG,aAAa,KAAK;AAAG,oBAAc,KAAK;AAC3C,WAAO;AAAA,EACT;AACA,MAAG,MAAM,KAAK;AACZ,QAAG,sBAAsB,KAAK;AAAG,oBAAc,KAAK;AACpD,WAAO;AAAA,EACT;AACA,MAAG,MAAM;AACP,WAAO;AAET,MAAI,MAAM,OAAO,MAAM;AACrB,WAAO,MAAM,QAAQ,OAAO,YAAY,IAAI,cAAc,CAAC;AAE7D,MAAG,CAAC,sBAAsB,KAAK;AAC7B,mBAAe,OAAO,EAAE,MAAM,aAAa,UAAU,eAAc,CAAC;AAEtE,SAAO,IAAI,YAAY;AACvB,SAAO,SAAS,iBAAiB;AACjC,SAAO,SAAS;AAGhB,MAAG,OAAO,MAAM,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,GAAG;AACvD,kBAAc,KAAK;AACnB,UAAM,WAAW;AAAA,EACnB;AAEA,SAAO;AACT;AAGA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,OAAO,MAAM,MAAM,IAAI,GAAG;AACzC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAIA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,OAAO,MAAM,KAAK,IAAI,GAAG;AACxC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAGA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,OAAO,MAAM,KAAK,IAAI,GAAG;AACxC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAIA,SAAS,aAAa,OAAO;AAAE,SAAO,KAAK,OAAO,KAAK;AAAG;AAC1D,SAAS,sBAAsB,OAAO;AAAE,SAAO,KAAK,OAAO,WAAW;AAAG;AACzE,SAAS,mBAAmB,OAAO;AAAE,SAAO,KAAK,OAAO,gBAAgB;AAAG;AAC3E,SAAS,WAAW,OAAO;AAAE,SAAO,KAAK,OAAO,QAAQ;AAAG;AAE3D,SAAS,cAAc,QAAQ;AAE7B,MAAG,OAAO,QAAQ,MAAM;AACtB,WAAO,OAAO,MAAM,eAAe,KAAK;AAAA,WAClC,OAAO,QAAQ,MAAM;AAC3B,WAAO,OAAO,MAAM,eAAe,KAAK;AAAA;AAExC,WAAO;AACX;AAEA,SAAS,KAAK,OAAO,MAAM;AACzB,SAAQ,MAAM,MAAM,UAAU,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAC5E;AAEA,SAAS,eAAe,OAAO,UAAU;AACvC,QAAM,MAAM,KAAK,QAAQ;AAC3B;AAEA,SAAS,cAAc,OAAO;AAC5B,QAAM,MAAM,IAAI;AAChB,MAAI,oBAAoB,MAAM,MAAM,UAAU,MAAM,MAAM,MAAM,MAAM,SAAO,CAAC,EAAE;AAChF,QAAM,WAAW,qBAAqB;AACxC;AAGO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,IAAI,CAAC;AAAA,MACL,OAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAClD;AACF;", "names": []}