package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 平台概览统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OverviewStatisticsDTO {
    
    // 总体统计
    private Long totalUsers;              // 总用户数
    private Long activeUsers;             // 活跃用户数（最近30天登录）
    private Long totalProducts;           // 总商品数
    private Long availableProducts;       // 可用商品数
    private Long totalOrders;             // 总订单数
    private Long completedOrders;         // 已完成订单数
    private BigDecimal totalRevenue;      // 总交易额
    private Long totalForumPosts;         // 总论坛帖子数
    private Long totalForumComments;      // 总论坛评论数
    
    // 今日统计
    private Long todayNewUsers;           // 今日新增用户
    private Long todayNewProducts;        // 今日新增商品
    private Long todayOrders;             // 今日订单数
    private BigDecimal todayRevenue;      // 今日交易额
    private Long todayForumPosts;         // 今日新增帖子
    private Long todayForumComments;      // 今日新增评论
    
    // 昨日统计（用于对比）
    private Long yesterdayNewUsers;       // 昨日新增用户
    private Long yesterdayNewProducts;    // 昨日新增商品
    private Long yesterdayOrders;         // 昨日订单数
    private BigDecimal yesterdayRevenue;  // 昨日交易额
    
    // 增长率（百分比）
    private Double userGrowthRate;        // 用户增长率
    private Double productGrowthRate;     // 商品增长率
    private Double orderGrowthRate;       // 订单增长率
    private Double revenueGrowthRate;     // 交易额增长率
    
    // 平台健康度指标
    private Double orderCompletionRate;   // 订单完成率
    private Double userActiveRate;        // 用户活跃率
    private Double productAvailableRate;  // 商品可用率
    private BigDecimal avgOrderAmount;    // 平均订单金额
    private Double avgUserProducts;       // 平均每用户商品数
    
    // 最近7天趋势数据（用于小图表）
    private String[] last7Days;          // 最近7天日期
    private Long[] last7DaysUsers;       // 最近7天新增用户
    private Long[] last7DaysProducts;    // 最近7天新增商品
    private Long[] last7DaysOrders;      // 最近7天订单数
    private BigDecimal[] last7DaysRevenue; // 最近7天交易额
}
