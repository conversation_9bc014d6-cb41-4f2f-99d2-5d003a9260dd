package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.config.AlipayConfig;
import com.lzhshtp.shangcheng.dto.RechargeRequest;
import com.lzhshtp.shangcheng.dto.RechargeResponse;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.RechargeService;
import com.lzhshtp.shangcheng.utils.AlipayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 充值服务实现类
 */
@Service
public class RechargeServiceImpl implements RechargeService {
    
    private static final Logger logger = LoggerFactory.getLogger(RechargeServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private AlipayConfig alipayConfig;
    
    /**
     * 创建充值订单
     */
    @Override
    public RechargeResponse createRecharge(Long userId, RechargeRequest request) {
        // 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }
        
        // 生成充值订单号
        String rechargeOrderNo = generateRechargeOrderNo(userId);
        
        // 生成支付宝支付链接（无论同步还是异步模式都需要支付页面）
        String paymentForm = alipayUtil.generateRechargePayLink(rechargeOrderNo, request.getAmount(), userId);
        if (paymentForm == null) {
            throw new RuntimeException("生成支付链接失败");
        }

        logger.info("用户{}发起充值，金额：{}，订单号：{}", userId, request.getAmount(), rechargeOrderNo);

        return RechargeResponse.builder()
                .rechargeOrderNo(rechargeOrderNo)
                .amount(request.getAmount())
                .paymentForm(paymentForm)
                .status("pending")
                .build();
    }
    
    /**
     * 处理支付宝支付回调
     */
    @Override
    @Transactional
    public boolean handleAlipayCallback(Map<String, String> params) {
        try {
            // 同步模式：直接处理成功，跳过签名验证
            if (alipayConfig.isSyncMode()) {
                String outTradeNo = params.get("out_trade_no");
                String totalAmount = params.get("total_amount");

                if (outTradeNo != null && totalAmount != null) {
                    logger.info("同步模式：直接处理充值成功，订单号：{}，金额：{}", outTradeNo, totalAmount);
                    return processRechargeSuccess(outTradeNo, new BigDecimal(totalAmount));
                }
                return false;
            }

            // 异步模式：完整的签名验证流程
            if (!alipayUtil.verifyCallback(params)) {
                logger.error("支付宝回调签名验证失败");
                return false;
            }

            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String totalAmount = params.get("total_amount");

            // 只处理支付成功的回调
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                return processRechargeSuccess(outTradeNo, new BigDecimal(totalAmount));
            }

            return true;
        } catch (Exception e) {
            logger.error("处理支付宝回调异常", e);
            return false;
        }
    }
    
    /**
     * 处理支付宝支付返回
     */
    @Override
    public boolean handleAlipayReturn(Map<String, String> params) {
        try {
            // 同步模式：直接返回成功，并处理充值
            if (alipayConfig.isSyncMode()) {
                String outTradeNo = params.get("out_trade_no");
                String totalAmount = params.get("total_amount");

                if (outTradeNo != null && totalAmount != null) {
                    logger.info("同步模式：支付返回直接处理成功，订单号：{}，金额：{}", outTradeNo, totalAmount);
                    // 在同步返回时也处理充值成功（防止异步回调失败）
                    processRechargeSuccess(outTradeNo, new BigDecimal(totalAmount));
                    return true;
                }
                return false;
            }

            // 异步模式：验证签名
            if (!alipayUtil.verifyCallback(params)) {
                logger.error("支付宝返回签名验证失败");
                return false;
            }

            String tradeStatus = params.get("trade_status");
            return "TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus);
        } catch (Exception e) {
            logger.error("处理支付宝返回异常", e);
            return false;
        }
    }
    
    /**
     * 处理充值成功
     */
    private boolean processRechargeSuccess(String rechargeOrderNo, BigDecimal amount) {
        try {
            // 从订单号中提取用户ID
            Long userId = extractUserIdFromOrderNo(rechargeOrderNo);

            // 防重复处理：检查是否已经处理过这个订单
            // 这里可以通过Redis或数据库记录已处理的订单，简单起见先用日志检查
            logger.info("开始处理充值成功，订单号：{}，用户ID：{}，金额：{}", rechargeOrderNo, userId, amount);

            // 查询用户（重新查询确保获取最新数据）
            User user = userMapper.selectById(userId);
            if (user == null) {
                logger.error("充值成功但用户不存在，订单号：{}", rechargeOrderNo);
                return false;
            }

            logger.info("查询到用户当前余额：{}，准备增加金额：{}", user.getBalance(), amount);

            // 增加用户余额
            BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
            BigDecimal newBalance = currentBalance.add(amount);
            user.setBalance(newBalance);

            // 更新用户余额
            int updateResult = userMapper.updateById(user);

            if (updateResult > 0) {
                logger.info("用户{}充值成功，金额：{}，余额：{} -> {}，订单号：{}",
                        userId, amount, currentBalance, newBalance, rechargeOrderNo);
                return true;
            } else {
                logger.error("更新用户余额失败，订单号：{}", rechargeOrderNo);
                return false;
            }
        } catch (Exception e) {
            logger.error("处理充值成功异常，订单号：{}", rechargeOrderNo, e);
            return false;
        }
    }
    
    /**
     * 生成充值订单号
     */
    private String generateRechargeOrderNo(Long userId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return "RC" + userId + timestamp;
    }
    
    /**
     * 从订单号中提取用户ID
     */
    private Long extractUserIdFromOrderNo(String orderNo) {
        // 订单号格式：RC{userId}{timestamp}
        if (orderNo.startsWith("RC")) {
            String userIdPart = orderNo.substring(2, orderNo.length() - 14); // 去掉RC前缀和14位时间戳
            return Long.parseLong(userIdPart);
        }
        throw new IllegalArgumentException("无效的充值订单号：" + orderNo);
    }
}
