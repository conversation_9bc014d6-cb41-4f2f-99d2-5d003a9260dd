<template>
  <div class="sync-es-container">
    <div class="page-header">
      <h2>ElasticSearch数据同步</h2>
      <p class="page-description">将数据库中的商品和论坛帖子数据同步到ElasticSearch搜索引擎</p>
    </div>

    <div class="sync-cards">
      <!-- 商品同步卡片 -->
      <div class="sync-card">
        <div class="card-header">
          <h3>📦 商品数据同步</h3>
          <div class="status-badge" :class="productSyncStatus">
            {{ getStatusText(productSyncStatus) }}
          </div>
        </div>
        
        <div class="card-content">
          <p class="card-description">
            将数据库中的所有商品数据同步到ElasticSearch，包括商品标题、描述、价格、分类等信息，用于商品搜索功能。
          </p>
          
          <div class="sync-info" v-if="productSyncResult">
            <div class="info-item">
              <span class="label">同步时间：</span>
              <span class="value">{{ productSyncResult.time }}</span>
            </div>
            <div class="info-item">
              <span class="label">同步结果：</span>
              <span class="value">{{ productSyncResult.message }}</span>
            </div>
          </div>
        </div>
        
        <div class="card-actions">
          <button 
            class="sync-btn primary" 
            @click="syncProducts"
            :disabled="productSyncStatus === 'syncing'"
          >
            <span v-if="productSyncStatus === 'syncing'" class="loading-icon">⏳</span>
            <span v-else class="sync-icon">🔄</span>
            {{ productSyncStatus === 'syncing' ? '同步中...' : '同步商品数据' }}
          </button>
        </div>
      </div>

      <!-- 论坛帖子同步卡片 -->
      <div class="sync-card">
        <div class="card-header">
          <h3>💬 论坛帖子同步</h3>
          <div class="status-badge" :class="forumSyncStatus">
            {{ getStatusText(forumSyncStatus) }}
          </div>
        </div>
        
        <div class="card-content">
          <p class="card-description">
            将数据库中的所有论坛帖子数据同步到ElasticSearch，包括帖子标题、内容、作者等信息，用于论坛搜索功能。
          </p>
          
          <div class="sync-info" v-if="forumSyncResult">
            <div class="info-item">
              <span class="label">同步时间：</span>
              <span class="value">{{ forumSyncResult.time }}</span>
            </div>
            <div class="info-item">
              <span class="label">同步结果：</span>
              <span class="value">{{ forumSyncResult.message }}</span>
            </div>
          </div>
        </div>
        
        <div class="card-actions">
          <button 
            class="sync-btn primary" 
            @click="syncForumPosts"
            :disabled="forumSyncStatus === 'syncing'"
          >
            <span v-if="forumSyncStatus === 'syncing'" class="loading-icon">⏳</span>
            <span v-else class="sync-icon">🔄</span>
            {{ forumSyncStatus === 'syncing' ? '同步中...' : '同步帖子数据' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 批量同步区域 -->
    <div class="batch-sync-section">
      <div class="section-header">
        <h3>🚀 批量操作</h3>
        <p>一键同步所有数据，适用于初始化或数据重建场景</p>
      </div>
      
      <div class="batch-actions">
        <button 
          class="sync-btn batch" 
          @click="syncAllData"
          :disabled="productSyncStatus === 'syncing' || forumSyncStatus === 'syncing'"
        >
          <span v-if="productSyncStatus === 'syncing' || forumSyncStatus === 'syncing'" class="loading-icon">⏳</span>
          <span v-else class="sync-icon">⚡</span>
          {{ (productSyncStatus === 'syncing' || forumSyncStatus === 'syncing') ? '同步中...' : '同步所有数据' }}
        </button>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="help-section">
      <div class="section-header">
        <h3>📖 使用说明</h3>
      </div>
      
      <div class="help-content">
        <div class="help-item">
          <h4>🎯 什么时候需要同步？</h4>
          <ul>
            <li>首次部署系统时</li>
            <li>ElasticSearch数据丢失或损坏时</li>
            <li>数据库结构发生变化时</li>
            <li>搜索功能异常时</li>
          </ul>
        </div>
        
        <div class="help-item">
          <h4>⚠️ 注意事项</h4>
          <ul>
            <li>同步过程可能需要较长时间，请耐心等待</li>
            <li>同步期间搜索功能可能受到影响</li>
            <li>建议在系统访问量较低时进行同步</li>
            <li>同步完成后建议测试搜索功能</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { syncProductToES, syncForumPostToES } from '../api/import'

// 同步状态
const productSyncStatus = ref('idle') // idle, syncing, success, error
const forumSyncStatus = ref('idle')

// 同步结果
const productSyncResult = ref(null)
const forumSyncResult = ref(null)

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    idle: '待同步',
    syncing: '同步中',
    success: '同步成功',
    error: '同步失败'
  }
  return statusMap[status] || '未知状态'
}

// 同步商品数据
const syncProducts = async () => {
  productSyncStatus.value = 'syncing'
  productSyncResult.value = null
  
  try {
    const response = await syncProductToES()
    
    if (response.success) {
      productSyncStatus.value = 'success'
      productSyncResult.value = {
        time: new Date().toLocaleString(),
        message: response.message || '商品数据同步成功'
      }
      ElMessage.success('商品数据同步成功！')
    } else {
      throw new Error(response.message || '同步失败')
    }
  } catch (error) {
    productSyncStatus.value = 'error'
    productSyncResult.value = {
      time: new Date().toLocaleString(),
      message: error.message || '同步失败'
    }
    ElMessage.error('商品数据同步失败：' + error.message)
  }
}

// 同步论坛帖子数据
const syncForumPosts = async () => {
  forumSyncStatus.value = 'syncing'
  forumSyncResult.value = null
  
  try {
    const response = await syncForumPostToES()
    
    if (response.success) {
      forumSyncStatus.value = 'success'
      forumSyncResult.value = {
        time: new Date().toLocaleString(),
        message: response.message || '论坛帖子数据同步成功'
      }
      ElMessage.success('论坛帖子数据同步成功！')
    } else {
      throw new Error(response.message || '同步失败')
    }
  } catch (error) {
    forumSyncStatus.value = 'error'
    forumSyncResult.value = {
      time: new Date().toLocaleString(),
      message: error.message || '同步失败'
    }
    ElMessage.error('论坛帖子数据同步失败：' + error.message)
  }
}

// 同步所有数据
const syncAllData = async () => {
  ElMessage.info('开始同步所有数据...')
  
  // 先同步商品
  await syncProducts()
  
  // 等待一秒后同步论坛帖子
  setTimeout(async () => {
    await syncForumPosts()
    
    // 检查是否都成功
    if (productSyncStatus.value === 'success' && forumSyncStatus.value === 'success') {
      ElMessage.success('所有数据同步完成！')
    }
  }, 1000)
}
</script>

<style scoped>
.sync-es-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h2 {
  font-size: 28px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.sync-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.sync-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
}

.sync-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  color: #1f2937;
  margin: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.idle {
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.syncing {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-badge.success {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}

.card-content {
  padding: 16px 24px;
}

.card-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.sync-info {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.info-item .value {
  color: #6b7280;
}

.card-actions {
  padding: 16px 24px 24px;
}

.sync-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sync-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sync-btn.primary {
  background: #3b82f6;
  color: white;
}

.sync-btn.primary:hover:not(:disabled) {
  background: #2563eb;
}

.sync-btn.batch {
  background: #10b981;
  color: white;
  font-size: 16px;
  padding: 16px 32px;
}

.sync-btn.batch:hover:not(:disabled) {
  background: #059669;
}

.batch-sync-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 20px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.section-header p {
  color: #6b7280;
  margin: 0;
}

.batch-actions {
  text-align: center;
}

.help-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.help-item h4 {
  font-size: 16px;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.help-item ul {
  margin: 0;
  padding-left: 20px;
}

.help-item li {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
