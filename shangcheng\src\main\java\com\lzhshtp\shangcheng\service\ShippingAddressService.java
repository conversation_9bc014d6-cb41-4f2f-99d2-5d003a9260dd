package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.ShippingAddressDTO;
import com.lzhshtp.shangcheng.dto.ShippingAddressRequest;

import java.util.List;

/**
 * 收货地址服务接口
 */
public interface ShippingAddressService {
    
    /**
     * 获取用户的所有收货地址
     * 
     * @param userId 用户ID
     * @return 收货地址列表
     */
    List<ShippingAddressDTO> getUserAddresses(Long userId);
    
    /**
     * 获取收货地址详情
     * 
     * @param addressId 收货地址ID
     * @param userId 用户ID（用于权限验证）
     * @return 收货地址详情
     */
    ShippingAddressDTO getAddressById(Long addressId, Long userId);
    
    /**
     * 添加收货地址
     * 
     * @param request 收货地址请求
     * @param userId 用户ID
     * @return 新增的收货地址
     */
    ShippingAddressDTO addAddress(ShippingAddressRequest request, Long userId);
    
    /**
     * 更新收货地址
     * 
     * @param addressId 收货地址ID
     * @param request 收货地址请求
     * @param userId 用户ID（用于权限验证）
     * @return 更新后的收货地址
     */
    ShippingAddressDTO updateAddress(Long addressId, ShippingAddressRequest request, Long userId);
    
    /**
     * 删除收货地址
     * 
     * @param addressId 收货地址ID
     * @param userId 用户ID（用于权限验证）
     * @return 是否删除成功
     */
    boolean deleteAddress(Long addressId, Long userId);
    
    /**
     * 设置默认收货地址
     * 
     * @param addressId 收货地址ID
     * @param userId 用户ID（用于权限验证）
     * @return 是否设置成功
     */
    boolean setDefaultAddress(Long addressId, Long userId);
    
    /**
     * 获取用户的默认收货地址
     * 
     * @param userId 用户ID
     * @return 默认收货地址，如果没有则返回null
     */
    ShippingAddressDTO getDefaultAddress(Long userId);
} 