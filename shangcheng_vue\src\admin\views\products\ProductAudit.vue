<template>
  <div class="product-audit-management">
    <div class="page-header">
      <h2 class="page-title">商品审核管理</h2>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="stats">
      <div class="stat-card">
        <div class="stat-value">{{ stats.pendingCount || 0 }}</div>
        <div class="stat-label">待审核</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.availableCount || 0 }}</div>
        <div class="stat-label">已上架</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.rejectedCount || 0 }}</div>
        <div class="stat-label">已拒绝</div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-container">
      <div class="filter-item">
        <label>关键词:</label>
        <input
          type="text"
          v-model="queryParams.keyword"
          placeholder="输入关键词搜索"
          @keyup.enter="fetchProductList"
        />
      </div>

      <div class="filter-item">
        <label>状态:</label>
        <select v-model="queryParams.status">
          <option value="">全部</option>
          <option value="pending_review">待审核</option>
          <option value="available">已上架</option>
          <option value="off_shelf_by_admin">已拒绝</option>
        </select>
      </div>

      <button class="search-btn" @click="fetchProductList">搜索</button>
      <button class="reset-btn" @click="resetFilters">重置</button>
    </div>

    <!-- 商品列表 -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>图片</th>
            <th>商品名称</th>
            <th>价格</th>
            <th>卖家</th>
            <th>状态</th>
            <th>发布时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in productList" :key="item.id">
            <td>{{ item.id }}</td>
            <td>
              <div class="product-img">
                <img :src="item.imageUrls && item.imageUrls.length > 0 ? item.imageUrls[0] : ''" alt="商品图片" />
              </div>
            </td>
            <td>{{ item.title }}</td>
            <td>¥{{ item.price }}</td>
            <td>{{ item.sellerName }}</td>
            <td>
              <span class="status-badge" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </span>
            </td>
            <td>{{ formatDateTime(item.postedDate) }}</td>
            <td>
              <button 
                class="action-btn approve-btn" 
                @click="approveProduct(item)"
                v-if="item.status === 'pending_review'"
              >
                <span class="btn-icon">✅</span> 通过
              </button>
              <button 
                class="action-btn reject-btn" 
                @click="showRejectDialog(item)"
                v-if="item.status === 'pending_review'"
              >
                <span class="btn-icon">❌</span> 拒绝
              </button>
              <button class="action-btn view-btn" @click="viewProduct(item)">
                <span class="btn-icon">👁️</span> 查看
              </button>
            </td>
          </tr>
          <tr v-if="productList.length === 0 && !loading">
            <td colspan="8" class="no-data">暂无数据</td>
          </tr>
          <tr v-if="loading">
            <td colspan="8" class="loading-row">
              <div class="loading-spinner"></div>
              加载中...
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" v-if="total > 0">
      <div class="pagination-info">
        共 {{ total }} 条数据，当前 {{ currentPage }}/{{ totalPages }} 页
      </div>
      <div class="pagination-btns">
        <button
          :disabled="currentPage <= 1 || loading"
          @click="changePage(currentPage - 1)"
        >上一页</button>
        <button
          :disabled="currentPage >= totalPages || loading"
          @click="changePage(currentPage + 1)"
        >下一页</button>
      </div>
    </div>

    <!-- 商品详情弹窗 -->
    <div class="modal" v-if="showDetailModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>商品详情</h2>
          <button class="close-btn" @click="showDetailModal = false">&times;</button>
        </div>
        <div class="modal-body" v-if="currentProduct">
          <div class="product-detail">
            <div class="product-images">
              <img 
                v-for="(img, index) in currentProduct.imageUrls" 
                :key="index" 
                :src="img" 
                alt="商品图片" 
                class="detail-image"
              />
            </div>
            <div class="detail-info">
              <h3>{{ currentProduct.title }}</h3>
              <div class="detail-item">
                <span class="detail-label">价格:</span>
                <span class="detail-value price">¥{{ currentProduct.price }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">卖家:</span>
                <span class="detail-value">{{ currentProduct.sellerName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">分类:</span>
                <span class="detail-value">{{ currentProduct.categoryName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">商品状况:</span>
                <span class="detail-value">{{ currentProduct.condition }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">位置:</span>
                <span class="detail-value">{{ currentProduct.location }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">发布时间:</span>
                <span class="detail-value">{{ formatDateTime(currentProduct.postedDate) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态:</span>
                <span class="detail-value status-badge" :class="getStatusClass(currentProduct.status)">
                  {{ getStatusText(currentProduct.status) }}
                </span>
              </div>
              <div class="detail-item description">
                <span class="detail-label">商品描述:</span>
                <div class="detail-content">{{ currentProduct.description }}</div>
              </div>
            </div>
          </div>

          <!-- 审核操作按钮 -->
          <div class="action-buttons" v-if="currentProduct.status === 'pending_review'">
            <button class="approve-btn full-width" @click="approveProduct(currentProduct)">
              <span class="btn-icon">✅</span> 通过审核
            </button>
            <button class="reject-btn full-width" @click="showRejectDialog(currentProduct)">
              <span class="btn-icon">❌</span> 拒绝审核
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 拒绝理由弹窗 -->
    <div class="modal" v-if="showRejectModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>拒绝商品审核</h2>
          <button class="close-btn" @click="showRejectModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="rejectTarget">
            <div class="detail-item">
              <span class="detail-label">商品名称:</span>
              <span class="detail-value">{{ rejectTarget.title }}</span>
            </div>
            <div class="form-item">
              <label>拒绝理由:</label>
              <textarea 
                v-model="rejectForm.reason" 
                rows="6" 
                placeholder="请输入拒绝理由，该理由将发送给商家..."
                class="reject-textarea"
              ></textarea>
            </div>
            <div class="form-btns">
              <button 
                class="save-btn" 
                @click="rejectProduct" 
                :disabled="!rejectForm.reason || processing"
              >
                {{ processing ? '处理中...' : '确认拒绝' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { getProducts, getProductDetail, reviewProduct } from '@/admin/api/products'
import { getOrCreateConversation, sendSystemMessage } from '@/admin/api/messages'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: 'pending_review', // 默认查看待审核的商品
  keyword: ''
})

// 数据状态
const productList = ref([])
const total = ref(0)
const currentPage = ref(1)
const totalPages = computed(() => Math.ceil(total.value / queryParams.pageSize))
const stats = ref(null)
const loading = ref(false) // 添加加载状态

// 弹窗状态
const showDetailModal = ref(false)
const showRejectModal = ref(false)
const currentProduct = ref(null)
const rejectTarget = ref(null)
const rejectForm = reactive({
  reason: ''
})
const processing = ref(false)

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true // 开始加载
  try {
    console.log('请求参数:', queryParams);
    const res = await getProducts(queryParams)
    console.log('返回结果:', res);
    if (res.code === 200 && res.data) {
      productList.value = res.data.records || []
      total.value = res.data.total || 0
      
      // 确保当前页码与返回的页码一致
      currentPage.value = res.data.page || queryParams.pageNum
      
      // 打印一下实际数据，便于调试
      console.log(`获取到${productList.value.length}条商品数据，当前第${currentPage.value}页，总共${total.value}条`)
      
      fetchProductStats() // 更新统计信息
    } else {
      console.error('获取商品列表失败:', res.message)
      alert(`获取商品列表失败: ${res.message || '服务器错误'}`)
    }
  } catch (error) {
    console.error('获取商品列表出错:', error)
    alert('获取商品列表出错，请重试')
  } finally {
    loading.value = false // 结束加载
  }
}

// 获取商品统计信息
const fetchProductStats = async () => {
  try {
    // 分别获取各状态下的商品数量
    const pendingRes = await getProducts({
      pageNum: 1,
      pageSize: 1,
      status: 'pending_review'
    })
    
    const availableRes = await getProducts({
      pageNum: 1,
      pageSize: 1,
      status: 'available'
    })
    
    const rejectedRes = await getProducts({
      pageNum: 1,
      pageSize: 1,
      status: 'off_shelf_by_admin'
    })
    
    stats.value = {
      pendingCount: pendingRes.data?.total || 0,
      availableCount: availableRes.data?.total || 0,
      rejectedCount: rejectedRes.data?.total || 0
    }
  } catch (error) {
    console.error('获取商品统计信息出错:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  queryParams.keyword = ''
  queryParams.status = 'pending_review'
  queryParams.pageNum = 1
  fetchProductList()
}

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return
  
  // 修改请求参数中的页码
  queryParams.pageNum = page
  
  // 请求新数据前清空当前列表，避免旧数据影响视觉
  productList.value = []
  
  // 发起请求
  fetchProductList()
}

// 查看商品详情
const viewProduct = async (product) => {
  try {
    const res = await getProductDetail(product.id)
    if (res.code === 200 && res.data) {
      currentProduct.value = res.data
      showDetailModal.value = true
    } else {
      console.error('获取商品详情失败:', res.message)
    }
  } catch (error) {
    console.error('获取商品详情出错:', error)
  }
}

// 通过商品审核
const approveProduct = async (product) => {
  if (processing.value) return
  
  processing.value = true
  try {
    const res = await reviewProduct(product.id, true)
    if (res.code === 200) {
      // 发送通知消息给卖家
      await sendNotificationToSeller(
        product.sellerId,
        `您的商品"${product.title}"已通过审核，现已上架到平台。`
      )
      
      alert('审核通过成功')
      
      // 关闭可能打开的弹窗
      showDetailModal.value = false
      
      // 刷新列表
      fetchProductList()
    } else {
      alert('审核操作失败: ' + res.message)
    }
  } catch (error) {
    console.error('审核操作出错:', error)
    alert('审核操作出错，请重试')
  } finally {
    processing.value = false
  }
}

// 显示拒绝审核对话框
const showRejectDialog = (product) => {
  rejectTarget.value = product
  rejectForm.reason = ''
  showRejectModal.value = true
}

// 拒绝商品审核
const rejectProduct = async () => {
  if (!rejectTarget.value || !rejectForm.reason || processing.value) return
  
  processing.value = true
  try {
    const res = await reviewProduct(rejectTarget.value.id, false)
    if (res.code === 200) {
      // 发送带有拒绝理由的通知消息给卖家
      await sendNotificationToSeller(
        rejectTarget.value.sellerId,
        `您的商品"${rejectTarget.value.title}"未通过审核，原因：${rejectForm.reason}`
      )
      
      alert('审核拒绝成功')
      
      // 关闭弹窗
      showRejectModal.value = false
      showDetailModal.value = false
      
      // 刷新列表
      fetchProductList()
    } else {
      alert('审核操作失败: ' + res.message)
    }
  } catch (error) {
    console.error('审核操作出错:', error)
    alert('审核操作出错，请重试')
  } finally {
    processing.value = false
  }
}

// 发送通知消息给卖家
const sendNotificationToSeller = async (sellerId, content) => {
  try {
    // 创建或获取与卖家的会话
    const conversationRes = await getOrCreateConversation(sellerId)
    if (conversationRes.code === 200) {
      // 发送系统消息
      const messageData = {
        receiverId: sellerId,
        content: content
      }
      
      await sendSystemMessage(messageData)
    }
  } catch (error) {
    console.error('发送通知消息失败:', error)
    throw error
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'pending_review': return 'status-pending'
    case 'available': return 'status-available'
    case 'off_shelf_by_admin': return 'status-rejected'
    case 'off_shelf_by_seller': return 'status-off-shelf'
    case 'sold': return 'status-sold'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending_review': '待审核',
    'available': '已上架',
    'off_shelf_by_admin': '已拒绝',
    'off_shelf_by_seller': '卖家下架',
    'sold': '已售出'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProductList()
})
</script>

<style scoped>
.product-audit-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  color: #333;
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 筛选区域样式 */
.filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  margin-right: 10px;
  font-size: 14px;
  color: #666;
}

.filter-item input, .filter-item select {
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-width: 120px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn {
  background-color: #1890ff;
  color: white;
}

.reset-btn {
  background-color: #f0f0f0;
  color: #666;
}

/* 表格样式 */
.table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.data-table th {
  background-color: #fafafa;
  font-weight: 500;
  color: #333;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

.loading-row {
  text-align: center;
  padding: 30px 0;
  color: #999;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商品图片样式 */
.product-img {
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 4px;
}

.product-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 状态标签样式 */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-available {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

.status-off-shelf {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-sold {
  background-color: #f0f0f0;
  color: #999;
}

/* 操作按钮样式 */
.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;
  display: inline-flex;
  align-items: center;
}

.btn-icon {
  margin-right: 4px;
  font-size: 14px;
}

.approve-btn {
  background-color: #52c41a;
  color: white;
}

.reject-btn {
  background-color: #ff4d4f;
  color: white;
}

.view-btn {
  background-color: #1890ff;
  color: white;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination-btns button {
  padding: 6px 12px;
  margin-left: 8px;
  border: 1px solid #d9d9d9;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
}

.pagination-btns button:disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 700px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

/* 商品详情样式 */
.product-detail {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.product-images {
  flex: 0 0 250px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-image {
  width: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.detail-info {
  flex: 1;
}

.detail-info h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.detail-value {
  color: #333;
}

.detail-value.price {
  font-size: 18px;
  color: #ff4d4f;
  font-weight: bold;
}

.detail-content {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-line;
}

.description {
  margin-top: 15px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.full-width {
  flex: 1;
  padding: 10px;
  font-size: 14px;
}

/* 表单样式 */
.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #666;
}

.reject-textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 100px;
  resize: vertical;
}

.form-btns {
  display: flex;
  justify-content: flex-end;
}

.save-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}
</style> 