import request from '@/admin/utils/request'

/**
 * 批量导入用户
 * @param {File} file Excel文件
 * @returns {Promise}
 */
export function importUsers(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/admin/import/users',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 60秒超时，因为批量导入可能需要较长时间
  })
}

/**
 * 批量导入分类
 * @param {File} file Excel文件
 * @returns {Promise}
 */
export function importCategories(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/admin/import/categories',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 60秒超时
  })
}

/**
 * 下载用户导入模板
 * @returns {Promise}
 */
export function downloadUserTemplate() {
  return request({
    url: '/admin/import/templates/users',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 下载分类导入模板
 * @returns {Promise}
 */
export function downloadCategoryTemplate() {
  return request({
    url: '/admin/import/templates/categories',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 同步商品到ElasticSearch
 * @param {number} productId 商品ID，可选，不传则同步所有商品
 * @returns {Promise}
 */
export function syncProductToES(productId) {
  const params = productId ? { productId } : {}

  return request({
    url: '/search/admin/sync-product',
    method: 'post',
    params,
    timeout: 120000 // 2分钟超时，因为同步可能需要较长时间
  })
}

/**
 * 同步论坛帖子到ElasticSearch
 * @param {number} postId 帖子ID，可选，不传则同步所有帖子
 * @returns {Promise}
 */
export function syncForumPostToES(postId) {
  const params = postId ? { postId } : {}

  return request({
    url: '/search/admin/sync-forum-post',
    method: 'post',
    params,
    timeout: 120000 // 2分钟超时
  })
}
