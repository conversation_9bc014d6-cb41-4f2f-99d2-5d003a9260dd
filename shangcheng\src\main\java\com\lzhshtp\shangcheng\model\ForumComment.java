package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 论坛评论实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_forum_comments")
public class ForumComment {
    
    /**
     * 评论ID
     */
    @TableId(value = "lzhshtp_comment_id", type = IdType.AUTO)
    private Long commentId;
    
    /**
     * 所属帖子ID
     */
    @TableField("lzhshtp_post_id")
    private Long postId;
    
    /**
     * 评论作者ID
     */
    @TableField("lzhshtp_author_id")
    private Long authorId;
    
    /**
     * 评论内容
     */
    @TableField("lzhshtp_content")
    private String content;
    
    /**
     * 评论时间
     */
    @TableField("lzhshtp_commented_at")
    private LocalDateTime commentedAt;
    
    /**
     * 父评论ID（回复某条评论）
     */
    @TableField("lzhshtp_parent_comment_id")
    private Long parentCommentId;
} 