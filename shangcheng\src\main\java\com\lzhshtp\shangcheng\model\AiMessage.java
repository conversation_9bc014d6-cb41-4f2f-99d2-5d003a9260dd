package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI消息实体类
 * 对应数据库表: tb_lzhshtp_ai_message
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_lzhshtp_ai_message", autoResultMap = true)
public class AiMessage {

    /**
     * AI消息唯一标识ID(UUID格式)
     */
    @TableId(value = "zhshtp_id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 消息创建时间
     */
    @TableField("zhshtp_created_time")
    private LocalDateTime createdTime;

    /**
     * 消息最后编辑时间
     */
    @TableField("zhshtp_edited_time")
    private LocalDateTime editedTime;

    /**
     * 消息创建者用户ID，关联商城用户表
     */
    @TableField("zhshtp_creator_id")
    private String creatorId;

    /**
     * 消息最后编辑者用户ID
     */
    @TableField("zhshtp_editor_id")
    private String editorId;

    /**
     * 消息类型：user/assistant/system/function_call/function_result
     */
    @TableField("zhshtp_type")
    private String type;

    /**
     * 消息文本内容
     */
    @TableField("zhshtp_text_content")
    private String textContent;

    /**
     * 媒体内容JSON格式，包含图片链接、语音链接等多媒体信息
     */
    @TableField(value = "zhshtp_medias", typeHandler = JacksonTypeHandler.class)
    private List<MediaContent> medias;

    /**
     * 所属AI会话ID，关联tb_lzhshtp_ai_session表
     */
    @TableField("zhshtp_ai_session_id")
    private String aiSessionId;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        USER("user", "用户消息"),
        ASSISTANT("assistant", "AI助手回复"),
        SYSTEM("system", "系统消息"),
        FUNCTION_CALL("function_call", "函数调用"),
        FUNCTION_RESULT("function_result", "函数结果");

        private final String code;
        private final String description;

        MessageType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static MessageType fromCode(String code) {
            for (MessageType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return USER;
        }
    }

    /**
     * 媒体内容类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MediaContent {
        /**
         * 媒体类型：image/audio/video/file
         */
        private String type;

        /**
         * 媒体URL
         */
        private String url;

        /**
         * 媒体文件名
         */
        private String filename;

        /**
         * 媒体文件大小(字节)
         */
        private Long size;

        /**
         * 媒体MIME类型
         */
        private String mimeType;

        /**
         * 额外属性
         */
        private Map<String, Object> properties;
    }
}
