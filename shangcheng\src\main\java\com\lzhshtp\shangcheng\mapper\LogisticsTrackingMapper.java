package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.LogisticsTracking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 物流跟踪Mapper接口
 */
@Mapper
public interface LogisticsTrackingMapper extends BaseMapper<LogisticsTracking> {
    
    /**
     * 根据订单ID查询物流跟踪记录
     */
    @Select("SELECT * FROM tb_lzhshtp_logistics_tracking WHERE lzhshtp_order_id = #{orderId} ORDER BY lzhshtp_created_time DESC")
    List<LogisticsTracking> selectByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据订单ID和物流类型查询最新的物流记录
     */
    @Select("SELECT * FROM tb_lzhshtp_logistics_tracking WHERE lzhshtp_order_id = #{orderId} AND lzhshtp_tracking_type = #{trackingType} ORDER BY lzhshtp_updated_time DESC LIMIT 1")
    LogisticsTracking selectLatestByOrderIdAndType(@Param("orderId") Long orderId, @Param("trackingType") String trackingType);
}
