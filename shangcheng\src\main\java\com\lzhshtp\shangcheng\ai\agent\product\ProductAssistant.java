package com.lzhshtp.shangcheng.ai.agent.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.lzhshtp.shangcheng.ai.agent.AbstractAgent;
import com.lzhshtp.shangcheng.ai.agent.Agent;
import com.lzhshtp.shangcheng.dto.CategoryDTO;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.service.CategoryService;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.SearchService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Description;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品助手Agent
 * 提供商品搜索、推荐、比较等智能服务
 */
@Agent
@Description("智能商品助手，帮助用户搜索商品、获取商品信息、比较商品、推荐相似商品等")
@AllArgsConstructor
@Slf4j
public class ProductAssistant extends AbstractAgent implements Function<ProductAssistant.Request, String> {

    private final ChatModel chatModel;
    private final ProductService productService;
    private final SearchService searchService;
    private final CategoryService categoryService;

    private static final String SYSTEM_PROMPT = """
            你是一个专业的商品助手，专门帮助用户在二手商品交易平台上找到合适的商品。
            你的主要职责包括：
            1. 根据用户需求搜索和推荐商品
            2. 提供商品详细信息和比较分析
            3. 解答商品相关问题
            4. 提供购买建议和注意事项
            
            请用友好、专业的语气回答用户问题，并尽可能提供有用的信息。
            """;

    @Override
    public String apply(Request request) {
        try {
            return ChatClient.create(chatModel)
                    .prompt()
                    .system(SYSTEM_PROMPT)
                    .user(request.query())
                    .functions("productSearchFunction", "productDetailFunction", "productCompareFunction",
                              "productRecommendFunction", "categoryListFunction")
                    .call()
                    .content();
        } catch (Exception e) {
            log.error("ProductAssistant处理请求失败", e);
            return "抱歉，我在处理您的请求时遇到了问题，请稍后再试。";
        }
    }

    public record Request(
            @JsonProperty(required = true)
            @JsonPropertyDescription("用户关于商品的问题或需求")
            String query
    ) {}



    /**
     * 商品搜索功能
     */
    @Description("根据关键词、分类、价格范围等条件搜索商品")
    public String searchProducts(
            @JsonPropertyDescription("搜索关键词") String keyword,
            @JsonPropertyDescription("分类ID，可选") Integer categoryId,
            @JsonPropertyDescription("最低价格，可选") BigDecimal minPrice,
            @JsonPropertyDescription("最高价格，可选") BigDecimal maxPrice,
            @JsonPropertyDescription("商品新旧程度，可选") String condition,
            @JsonPropertyDescription("地区，可选") String location,
            @JsonPropertyDescription("页码，默认1") Integer page,
            @JsonPropertyDescription("每页数量，默认10") Integer pageSize
    ) {
        try {
            ProductQueryParams params = new ProductQueryParams();
            params.setKeyword(keyword);
            params.setCategoryId(categoryId);
            params.setMinPrice(minPrice);
            params.setMaxPrice(maxPrice);
            params.setCondition(condition);
            params.setLocation(location);
            params.setPage(page != null ? page : 1);
            params.setPageSize(pageSize != null ? pageSize : 10);
            params.setStatus("available"); // 只搜索可用商品

            PageResult<ProductDTO> result = searchService.searchProducts(params);

            if (result.getRecords().isEmpty()) {
                return "抱歉，没有找到符合条件的商品。建议您：\n" +
                       "1. 尝试使用更宽泛的关键词\n" +
                       "2. 调整价格范围\n" +
                       "3. 选择其他分类或地区";
            }

            StringBuilder response = new StringBuilder();
            response.append(String.format("找到 %d 个相关商品（第%d页，共%d页）：\n\n",
                    result.getTotal(), result.getPage(), result.getTotalPages()));

            for (ProductDTO product : result.getRecords()) {
                response.append(formatProductInfo(product)).append("\n");
            }

            return response.toString();
        } catch (Exception e) {
            log.error("搜索商品失败", e);
            return "搜索商品时出现错误，请稍后再试。";
        }
    }

    /**
     * 获取商品详情
     */
    @Description("根据商品ID获取详细信息")
    public String getProductDetail(
            @JsonPropertyDescription("商品ID") Long productId
    ) {
        try {
            ProductDTO product = productService.getProductById(productId);
            if (product == null) {
                return "抱歉，找不到ID为 " + productId + " 的商品。";
            }

            return formatDetailedProductInfo(product);
        } catch (Exception e) {
            log.error("获取商品详情失败", e);
            return "获取商品详情时出现错误，请稍后再试。";
        }
    }

    /**
     * 比较多个商品
     */
    @Description("比较多个商品的特性和价格")
    public String compareProducts(
            @JsonPropertyDescription("要比较的商品ID列表，用逗号分隔") String productIds
    ) {
        try {
            String[] ids = productIds.split(",");
            if (ids.length < 2) {
                return "请提供至少2个商品ID进行比较。";
            }
            if (ids.length > 5) {
                return "最多只能同时比较5个商品。";
            }

            StringBuilder comparison = new StringBuilder();
            comparison.append("商品比较结果：\n\n");

            for (String idStr : ids) {
                try {
                    Long id = Long.parseLong(idStr.trim());
                    ProductDTO product = productService.getProductById(id);
                    if (product != null) {
                        comparison.append(formatComparisonInfo(product)).append("\n");
                    } else {
                        comparison.append("商品ID ").append(id).append(" 不存在\n\n");
                    }
                } catch (NumberFormatException e) {
                    comparison.append("无效的商品ID: ").append(idStr).append("\n\n");
                }
            }

            return comparison.toString();
        } catch (Exception e) {
            log.error("比较商品失败", e);
            return "比较商品时出现错误，请稍后再试。";
        }
    }

    /**
     * 推荐相似商品
     */
    @Description("根据商品ID推荐相似商品")
    public String recommendSimilarProducts(
            @JsonPropertyDescription("参考商品ID") Long productId,
            @JsonPropertyDescription("推荐数量，默认5") Integer limit
    ) {
        try {
            List<ProductDTO> similarProducts = searchService.getSimilarProducts(productId);

            if (similarProducts.isEmpty()) {
                return "抱歉，暂时没有找到相似的商品推荐。";
            }

            int actualLimit = limit != null ? Math.min(limit, 10) : 5;
            List<ProductDTO> limitedProducts = similarProducts.stream()
                    .limit(actualLimit)
                    .collect(Collectors.toList());

            StringBuilder response = new StringBuilder();
            response.append("为您推荐以下相似商品：\n\n");

            for (ProductDTO product : limitedProducts) {
                response.append(formatProductInfo(product)).append("\n");
            }

            return response.toString();
        } catch (Exception e) {
            log.error("推荐相似商品失败", e);
            return "推荐商品时出现错误，请稍后再试。";
        }
    }

    /**
     * 获取分类列表
     */
    @Description("获取商品分类列表")
    public String getCategoryList() {
        try {
            List<CategoryDTO> categories = categoryService.getAllCategories();

            StringBuilder response = new StringBuilder();
            response.append("商品分类列表：\n\n");

            for (CategoryDTO category : categories) {
                response.append(formatCategoryInfo(category, 0)).append("\n");
            }

            return response.toString();
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return "获取分类列表时出现错误，请稍后再试。";
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 格式化商品基本信息
     */
    private String formatProductInfo(ProductDTO product) {
        return String.format("📦 %s\n💰 价格：¥%.2f\n📍 地区：%s\n🏷️ 分类：%s\n📅 发布时间：%s\n🔗 商品ID：%d\n",
                product.getTitle(),
                product.getPrice(),
                product.getLocation() != null ? product.getLocation() : "未知",
                product.getCategoryName() != null ? product.getCategoryName() : "未分类",
                product.getPostedDate() != null ? product.getPostedDate().toString() : "未知",
                product.getId());
    }

    /**
     * 格式化商品详细信息
     */
    private String formatDetailedProductInfo(ProductDTO product) {
        StringBuilder info = new StringBuilder();
        info.append("📦 商品详情\n");
        info.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
        info.append(String.format("🏷️ 标题：%s\n", product.getTitle()));
        info.append(String.format("💰 价格：¥%.2f\n", product.getPrice()));
        info.append(String.format("📝 描述：%s\n", product.getDescription() != null ? product.getDescription() : "暂无描述"));
        info.append(String.format("🏷️ 分类：%s\n", product.getCategoryName() != null ? product.getCategoryName() : "未分类"));
        info.append(String.format("🔧 新旧程度：%s\n", product.getCondition() != null ? product.getCondition() : "未知"));
        info.append(String.format("📍 地区：%s\n", product.getLocation() != null ? product.getLocation() : "未知"));
        info.append(String.format("🚚 配送方式：%s\n", product.getDeliveryMethod() != null ? product.getDeliveryMethod() : "未知"));
        info.append(String.format("👤 卖家：%s\n", product.getSellerName() != null ? product.getSellerName() : "未知"));
        info.append(String.format("📅 发布时间：%s\n", product.getPostedDate() != null ? product.getPostedDate().toString() : "未知"));
        info.append(String.format("🔗 商品ID：%d\n", product.getId()));

        if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
            info.append(String.format("🖼️ 图片数量：%d张\n", product.getImageUrls().size()));
        }

        return info.toString();
    }

    /**
     * 格式化商品比较信息
     */
    private String formatComparisonInfo(ProductDTO product) {
        return String.format("📦 %s | 💰 ¥%.2f | 🔧 %s | 📍 %s | 🔗 ID:%d\n",
                product.getTitle(),
                product.getPrice(),
                product.getCondition() != null ? product.getCondition() : "未知",
                product.getLocation() != null ? product.getLocation() : "未知",
                product.getId());
    }

    /**
     * 格式化分类信息（递归处理子分类）
     */
    private String formatCategoryInfo(CategoryDTO category, int level) {
        StringBuilder info = new StringBuilder();
        String indent = "  ".repeat(level);
        info.append(String.format("%s🏷️ %s (ID:%d)\n", indent, category.getName(), category.getId()));

        if (category.getDescription() != null && !category.getDescription().isEmpty()) {
            info.append(String.format("%s   📝 %s\n", indent, category.getDescription()));
        }

        if (category.getChildren() != null && !category.getChildren().isEmpty()) {
            for (CategoryDTO child : category.getChildren()) {
                info.append(formatCategoryInfo(child, level + 1));
            }
        }

        return info.toString();
    }


}
