package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户登录请求DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginRequest {
    
    @NotBlank(message = "用户名不能为空")
    private String username; // 用户名
    
    @NotBlank(message = "密码不能为空")
    private String password;
} 