package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 补充材料实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_supplementary_materials")
public class SupplementaryMaterial {
    
    @TableId(value = "lzhshtp_material_id", type = IdType.AUTO)
    private Long materialId;
    
    @TableField("lzhshtp_product_id")
    private Long productId;
    
    @TableField("lzhshtp_request_id")
    private Long requestId;
    
    @TableField("lzhshtp_seller_id")
    private Long sellerId;
    
    @TableField("lzhshtp_material_type")
    private String materialType;
    
    @TableField("lzhshtp_material_urls")
    private String materialUrls;  // JSON格式的文件URL列表
    
    @TableField("lzhshtp_description")
    private String description;
    
    @TableField("lzhshtp_submit_time")
    private LocalDateTime submitTime;
    
    @TableField("lzhshtp_admin_reviewed")
    private Boolean adminReviewed;
    
    @TableField("lzhshtp_admin_comments")
    private String adminComments;

    // 以下字段用于前端显示，不存储在数据库中
    @TableField(exist = false)
    private String sellerNickname;
}
