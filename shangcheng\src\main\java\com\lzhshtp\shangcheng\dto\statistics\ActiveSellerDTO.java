package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 活跃卖家DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActiveSellerDTO {
    private Long userId;                // 用户ID
    private String username;            // 用户名
    private String avatarUrl;           // 头像
    private Long orderCount;            // 订单数量
    private BigDecimal totalAmount;     // 总销售金额
    private BigDecimal avgAmount;       // 平均订单金额
    private Double completionRate;      // 订单完成率
}
