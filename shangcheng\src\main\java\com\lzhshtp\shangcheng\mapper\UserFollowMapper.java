package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lzhshtp.shangcheng.model.UserFollow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户关注Mapper接口
 */
@Mapper
public interface UserFollowMapper extends BaseMapper<UserFollow> {
    
    /**
     * 检查用户是否已关注某人
     * 
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    UserFollow selectByFollowerIdAndFollowingId(@Param("followerId") Long followerId, @Param("followingId") Long followingId);
    
    /**
     * 分页查询用户的关注列表
     * 
     * @param page 分页参数
     * @param followerId 关注者ID
     * @return 关注列表
     */
    IPage<UserFollow> selectFollowingsByFollowerId(Page<UserFollow> page, @Param("followerId") Long followerId);
    
    /**
     * 分页查询用户的粉丝列表
     * 
     * @param page 分页参数
     * @param followingId 被关注者ID
     * @return 粉丝列表
     */
    IPage<UserFollow> selectFollowersByFollowingId(Page<UserFollow> page, @Param("followingId") Long followingId);
    
    /**
     * 获取用户的关注数量
     * 
     * @param followerId 关注者ID
     * @return 关注数量
     */
    Integer countFollowingsByFollowerId(@Param("followerId") Long followerId);
    
    /**
     * 获取用户的粉丝数量
     * 
     * @param followingId 被关注者ID
     * @return 粉丝数量
     */
    Integer countFollowersByFollowingId(@Param("followingId") Long followingId);
} 