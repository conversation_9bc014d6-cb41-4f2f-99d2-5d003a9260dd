package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCreateRequest {
    
    @NotNull(message = "商品ID不能为空")
    private Long productId;
    
    @NotNull(message = "收货地址ID不能为空")
    private Long addressId;
    
    @NotNull(message = "支付方式不能为空")
    private String paymentMethod;
    
    // 订单总金额（前端计算，后端验证）
    private BigDecimal totalAmount;

    // 备注信息（可选）
    private String remarks;

    // 验货相关字段
    private Boolean officialVerification;
    private String verificationPayer;
    private BigDecimal verificationFee;
} 