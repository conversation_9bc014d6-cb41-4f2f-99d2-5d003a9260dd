{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/asciiarmor.js"], "sourcesContent": ["function errorIfNotEmpty(stream) {\n  var nonWS = stream.match(/^\\s*\\S/);\n  stream.skipToEnd();\n  return nonWS ? \"error\" : null;\n}\n\nexport const asciiArmor = {\n  name: \"asciiarmor\",\n  token: function(stream, state) {\n    var m;\n    if (state.state == \"top\") {\n      if (stream.sol() && (m = stream.match(/^-----BEGIN (.*)?-----\\s*$/))) {\n        state.state = \"headers\";\n        state.type = m[1];\n        return \"tag\";\n      }\n      return errorIfNotEmpty(stream);\n    } else if (state.state == \"headers\") {\n      if (stream.sol() && stream.match(/^\\w+:/)) {\n        state.state = \"header\";\n        return \"atom\";\n      } else {\n        var result = errorIfNotEmpty(stream);\n        if (result) state.state = \"body\";\n        return result;\n      }\n    } else if (state.state == \"header\") {\n      stream.skipToEnd();\n      state.state = \"headers\";\n      return \"string\";\n    } else if (state.state == \"body\") {\n      if (stream.sol() && (m = stream.match(/^-----END (.*)?-----\\s*$/))) {\n        if (m[1] != state.type) return \"error\";\n        state.state = \"end\";\n        return \"tag\";\n      } else {\n        if (stream.eatWhile(/[A-Za-z0-9+\\/=]/)) {\n          return null;\n        } else {\n          stream.next();\n          return \"error\";\n        }\n      }\n    } else if (state.state == \"end\") {\n      return errorIfNotEmpty(stream);\n    }\n  },\n  blankLine: function(state) {\n    if (state.state == \"headers\") state.state = \"body\";\n  },\n  startState: function() {\n    return {state: \"top\", type: null};\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,QAAQ,OAAO,MAAM,QAAQ;AACjC,SAAO,UAAU;AACjB,SAAO,QAAQ,UAAU;AAC3B;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI;AACJ,QAAI,MAAM,SAAS,OAAO;AACxB,UAAI,OAAO,IAAI,MAAM,IAAI,OAAO,MAAM,4BAA4B,IAAI;AACpE,cAAM,QAAQ;AACd,cAAM,OAAO,EAAE,CAAC;AAChB,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,MAAM;AAAA,IAC/B,WAAW,MAAM,SAAS,WAAW;AACnC,UAAI,OAAO,IAAI,KAAK,OAAO,MAAM,OAAO,GAAG;AACzC,cAAM,QAAQ;AACd,eAAO;AAAA,MACT,OAAO;AACL,YAAI,SAAS,gBAAgB,MAAM;AACnC,YAAI;AAAQ,gBAAM,QAAQ;AAC1B,eAAO;AAAA,MACT;AAAA,IACF,WAAW,MAAM,SAAS,UAAU;AAClC,aAAO,UAAU;AACjB,YAAM,QAAQ;AACd,aAAO;AAAA,IACT,WAAW,MAAM,SAAS,QAAQ;AAChC,UAAI,OAAO,IAAI,MAAM,IAAI,OAAO,MAAM,0BAA0B,IAAI;AAClE,YAAI,EAAE,CAAC,KAAK,MAAM;AAAM,iBAAO;AAC/B,cAAM,QAAQ;AACd,eAAO;AAAA,MACT,OAAO;AACL,YAAI,OAAO,SAAS,iBAAiB,GAAG;AACtC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,WAAW,MAAM,SAAS,OAAO;AAC/B,aAAO,gBAAgB,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,WAAW,SAAS,OAAO;AACzB,QAAI,MAAM,SAAS;AAAW,YAAM,QAAQ;AAAA,EAC9C;AAAA,EACA,YAAY,WAAW;AACrB,WAAO,EAAC,OAAO,OAAO,MAAM,KAAI;AAAA,EAClC;AACF;", "names": []}