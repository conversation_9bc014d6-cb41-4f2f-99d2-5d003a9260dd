{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/toml.js"], "sourcesContent": ["export const toml = {\n  name: \"toml\",\n  startState: function () {\n    return {\n      inString: false,\n      stringType: \"\",\n      lhs: true,\n      inArray: 0\n    };\n  },\n  token: function (stream, state) {\n    //check for state changes\n    if (!state.inString && ((stream.peek() == '\"') || (stream.peek() == \"'\"))) {\n      state.stringType = stream.peek();\n      stream.next(); // Skip quote\n      state.inString = true; // Update state\n    }\n    if (stream.sol() && state.inArray === 0) {\n      state.lhs = true;\n    }\n    //return state\n    if (state.inString) {\n      while (state.inString && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.inString = false; // Clear flag\n        } else if (stream.peek() === '\\\\') {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n    } else if (state.inArray && stream.peek() === ']') {\n      stream.next();\n      state.inArray--;\n      return 'bracket';\n    } else if (state.lhs && stream.peek() === '[' && stream.skipTo(']')) {\n      stream.next();//skip closing ]\n      // array of objects has an extra open & close []\n      if (stream.peek() === ']') stream.next();\n      return \"atom\";\n    } else if (stream.peek() === \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.eatSpace()) {\n      return null;\n    } else if (state.lhs && stream.eatWhile(function (c) { return c != '=' && c != ' '; })) {\n      return \"property\";\n    } else if (state.lhs && stream.peek() === \"=\") {\n      stream.next();\n      state.lhs = false;\n      return null;\n    } else if (!state.lhs && stream.match(/^\\d\\d\\d\\d[\\d\\-\\:\\.T]*Z/)) {\n      return 'atom'; //date\n    } else if (!state.lhs && (stream.match('true') || stream.match('false'))) {\n      return 'atom';\n    } else if (!state.lhs && stream.peek() === '[') {\n      state.inArray++;\n      stream.next();\n      return 'bracket';\n    } else if (!state.lhs && stream.match(/^\\-?\\d+(?:\\.\\d+)?/)) {\n      return 'number';\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  },\n  languageData: {\n    commentTokens: { line: '#' },\n  },\n};\n"], "mappings": ";;;AAAO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,SAAU,QAAQ,OAAO;AAE9B,QAAI,CAAC,MAAM,aAAc,OAAO,KAAK,KAAK,OAAS,OAAO,KAAK,KAAK,MAAO;AACzE,YAAM,aAAa,OAAO,KAAK;AAC/B,aAAO,KAAK;AACZ,YAAM,WAAW;AAAA,IACnB;AACA,QAAI,OAAO,IAAI,KAAK,MAAM,YAAY,GAAG;AACvC,YAAM,MAAM;AAAA,IACd;AAEA,QAAI,MAAM,UAAU;AAClB,aAAO,MAAM,YAAY,CAAC,OAAO,IAAI,GAAG;AACtC,YAAI,OAAO,KAAK,MAAM,MAAM,YAAY;AACtC,iBAAO,KAAK;AACZ,gBAAM,WAAW;AAAA,QACnB,WAAW,OAAO,KAAK,MAAM,MAAM;AACjC,iBAAO,KAAK;AACZ,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,MAAM,cAAc;AAAA,QAC7B;AAAA,MACF;AACA,aAAO,MAAM,MAAM,aAAa;AAAA,IAClC,WAAW,MAAM,WAAW,OAAO,KAAK,MAAM,KAAK;AACjD,aAAO,KAAK;AACZ,YAAM;AACN,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,OAAO,KAAK,MAAM,OAAO,OAAO,OAAO,GAAG,GAAG;AACnE,aAAO,KAAK;AAEZ,UAAI,OAAO,KAAK,MAAM;AAAK,eAAO,KAAK;AACvC,aAAO;AAAA,IACT,WAAW,OAAO,KAAK,MAAM,KAAK;AAChC,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAW,OAAO,SAAS,GAAG;AAC5B,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,OAAO,SAAS,SAAU,GAAG;AAAE,aAAO,KAAK,OAAO,KAAK;AAAA,IAAK,CAAC,GAAG;AACtF,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK;AAC7C,aAAO,KAAK;AACZ,YAAM,MAAM;AACZ,aAAO;AAAA,IACT,WAAW,CAAC,MAAM,OAAO,OAAO,MAAM,wBAAwB,GAAG;AAC/D,aAAO;AAAA,IACT,WAAW,CAAC,MAAM,QAAQ,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,OAAO,IAAI;AACxE,aAAO;AAAA,IACT,WAAW,CAAC,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK;AAC9C,YAAM;AACN,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAW,CAAC,MAAM,OAAO,OAAO,MAAM,mBAAmB,GAAG;AAC1D,aAAO;AAAA,IACT,WAAW,CAAC,OAAO,SAAS,GAAG;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAE,MAAM,IAAI;AAAA,EAC7B;AACF;", "names": []}