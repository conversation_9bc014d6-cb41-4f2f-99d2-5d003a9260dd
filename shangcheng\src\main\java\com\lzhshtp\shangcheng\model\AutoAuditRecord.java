package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 自动审核记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_auto_audit_records")
public class AutoAuditRecord {
    
    @TableId(value = "lzhshtp_record_id", type = IdType.AUTO)
    private Long recordId;
    
    @TableField("lzhshtp_product_id")
    private Long productId;
    
    @TableField("lzhshtp_text_audit_result")
    private String textAuditResult;  // JSON格式
    
    @TableField("lzhshtp_image_audit_result")
    private String imageAuditResult;  // JSON格式
    
    @TableField("lzhshtp_credit_audit_result")
    private String creditAuditResult;  // JSON格式
    
    @TableField("lzhshtp_price_audit_result")
    private String priceAuditResult;  // JSON格式
    
    @TableField("lzhshtp_publishing_behavior_result")
    private String publishingBehaviorResult;  // JSON格式
    
    @TableField("lzhshtp_final_decision")
    private String finalDecision;  // auto_approve, manual_review, auto_reject
    
    @TableField("lzhshtp_decision_reason")
    private String decisionReason;
    
    @TableField("lzhshtp_risk_score")
    private BigDecimal riskScore;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;

    // 以下字段用于前端显示，不存储在数据库中
    @TableField(exist = false)
    private String productTitle;

    @TableField(exist = false)
    private BigDecimal productPrice;

    @TableField(exist = false)
    private String productDescription;

    @TableField(exist = false)
    private String productImages;
}
