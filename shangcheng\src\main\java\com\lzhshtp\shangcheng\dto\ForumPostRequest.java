package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 论坛帖子请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumPostRequest {
    
    /**
     * 帖子标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(min = 2, max = 100, message = "标题长度应在2-100字符之间")
    private String title;
    
    /**
     * 帖子内容
     */
    @NotBlank(message = "内容不能为空")
    @Size(min = 5, max = 10000, message = "内容长度应在5-10000字符之间")
    private String content;
    
    /**
     * 所属分类ID
     */
    @NotNull(message = "分类不能为空")
    private Integer forumCategoryId;
} 