//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package org.springframework.ai.chat.messages;

import org.springframework.ai.model.Content;

public interface Message extends Content {
    MessageType getMessageType();
}

和

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.springframework.ai.chat.messages;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;
import org.springframework.util.StreamUtils;

public abstract class AbstractMessage implements Message {
    public static final String MESSAGE_TYPE = "messageType";
    protected final MessageType messageType;
    protected final String textContent;
    protected final Map<String, Object> metadata;

    protected AbstractMessage(MessageType messageType, String textContent, Map<String, Object> metadata) {
        Assert.notNull(messageType, "Message type must not be null");
        if (messageType == MessageType.SYSTEM || messageType == MessageType.USER) {
            Assert.notNull(textContent, "Content must not be null for SYSTEM or USER messages");
        }

        this.messageType = messageType;
        this.textContent = textContent;
        this.metadata = new HashMap(metadata);
        this.metadata.put("messageType", messageType);
    }

    protected AbstractMessage(MessageType messageType, Resource resource, Map<String, Object> metadata) {
        Assert.notNull(resource, "Resource must not be null");

        try (InputStream inputStream = resource.getInputStream()) {
            this.textContent = StreamUtils.copyToString(inputStream, Charset.defaultCharset());
        } catch (IOException ex) {
            throw new RuntimeException("Failed to read resource", ex);
        }

        this.messageType = messageType;
        this.metadata = new HashMap(metadata);
        this.metadata.put("messageType", messageType);
    }

    public String getText() {
        return this.textContent;
    }

    public String getContent() {
        return this.textContent;
    }

    public Map<String, Object> getMetadata() {
        return this.metadata;
    }

    public MessageType getMessageType() {
        return this.messageType;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (!(o instanceof AbstractMessage)) {
            return false;
        } else {
            AbstractMessage that = (AbstractMessage)o;
            return this.messageType == that.messageType && Objects.equals(this.textContent, that.textContent) && Objects.equals(this.metadata, that.metadata);
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.messageType, this.textContent, this.metadata});
    }
}

和

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.springframework.ai.chat.messages;

import java.util.Map;
import java.util.Objects;
import org.springframework.core.io.Resource;

public class SystemMessage extends AbstractMessage {
    public SystemMessage(String textContent) {
        super(MessageType.SYSTEM, textContent, Map.of());
    }

    public SystemMessage(Resource resource) {
        super(MessageType.SYSTEM, resource, Map.of());
    }

    public String getText() {
        return this.textContent;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o instanceof SystemMessage) {
            SystemMessage that = (SystemMessage)o;
            return !super.equals(o) ? false : Objects.equals(this.textContent, that.textContent);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{super.hashCode(), this.textContent});
    }

    public String toString() {
        String var10000 = this.textContent;
        return "SystemMessage{textContent='" + var10000 + "', messageType=" + String.valueOf(this.messageType) + ", metadata=" + String.valueOf(this.metadata) + "}";
    }
}


和

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.springframework.ai.chat.messages;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.ai.model.Media;
import org.springframework.ai.model.MediaContent;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;

public class UserMessage extends AbstractMessage implements MediaContent {
    protected final List<Media> media;

    public UserMessage(String textContent) {
        this(MessageType.USER, textContent, new ArrayList(), Map.of());
    }

    public UserMessage(Resource resource) {
        super(MessageType.USER, resource, Map.of());
        this.media = new ArrayList();
    }

    public UserMessage(String textContent, List<Media> media) {
        this(MessageType.USER, textContent, media, Map.of());
    }

    public UserMessage(String textContent, Media... media) {
        this(textContent, Arrays.asList(media));
    }

    public UserMessage(String textContent, Collection<Media> mediaList, Map<String, Object> metadata) {
        this(MessageType.USER, textContent, mediaList, metadata);
    }

    public UserMessage(MessageType messageType, String textContent, Collection<Media> media, Map<String, Object> metadata) {
        super(messageType, textContent, metadata);
        Assert.notNull(media, "media data must not be null");
        this.media = new ArrayList(media);
    }

    public String toString() {
        String var10000 = this.getText();
        return "UserMessage{content='" + var10000 + "', properties=" + String.valueOf(this.metadata) + ", messageType=" + String.valueOf(this.messageType) + "}";
    }

    public List<Media> getMedia() {
        return this.media;
    }

    public String getText() {
        return this.textContent;
    }
}
