import request from '@/utils/request'

/**
 * 创建充值订单
 * @param {Object} data - 充值数据
 * @param {number} data.amount - 充值金额
 * @param {string} data.paymentMethod - 支付方式
 * @returns {Promise} 充值响应
 */
export const createRecharge = (data) => {
  return request({
    url: '/recharge/create',
    method: 'post',
    data
  })
}

/**
 * 查询充值状态
 * @param {string} rechargeOrderNo - 充值订单号
 * @returns {Promise} 充值状态
 */
export const getRechargeStatus = (rechargeOrderNo) => {
  return request({
    url: `/recharge/status/${rechargeOrderNo}`,
    method: 'get'
  })
}
