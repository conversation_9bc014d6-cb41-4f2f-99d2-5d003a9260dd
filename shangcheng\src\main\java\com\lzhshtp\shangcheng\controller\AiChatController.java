package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.AiChatRequest;
import com.lzhshtp.shangcheng.dto.AiChatResponse;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.AiMessage;
import com.lzhshtp.shangcheng.model.AiSession;
import com.lzhshtp.shangcheng.service.AiChatService;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import jakarta.validation.Valid;
import java.time.Duration;
import java.util.List;

/**
 * AI聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai")
@RequiredArgsConstructor
public class AiChatController {

    private final AiChatService aiChatService;
    private final UserService userService;

    /**
     * 流式AI聊天对话
     *
     * @param request 聊天请求
     * @return SSE流式响应
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PreAuthorize("isAuthenticated()")
    public Flux<ServerSentEvent<AiChatResponse>> chatStream(@Valid @RequestBody AiChatRequest request) {
        try {
            Long userId = getCurrentUserId();

            return aiChatService.chatStream(request, userId)
                .map(response -> ServerSentEvent.<AiChatResponse>builder()
                    .data(response)
                    .build())
                .onErrorResume(error -> {
                    log.error("AI流式聊天异常", error);
                    AiChatResponse errorResponse = AiChatResponse.error(null, "AI服务异常：" + error.getMessage());
                    return Flux.just(ServerSentEvent.<AiChatResponse>builder()
                        .data(errorResponse)
                        .build());
                })
                .delayElements(Duration.ofMillis(50)); // 添加小延迟，避免前端处理过快

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            AiChatResponse errorResponse = AiChatResponse.error(null, "用户认证失败");
            return Flux.just(ServerSentEvent.<AiChatResponse>builder()
                .data(errorResponse)
                .build());
        }
    }

    /**
     * 非流式AI聊天对话
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    @PostMapping("/chat")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<AiChatResponse> chat(@Valid @RequestBody AiChatRequest request) {
        try {
            Long userId = getCurrentUserId();
            AiChatResponse response = aiChatService.chat(request, userId);

            if (response.getError() != null) {
                return ApiResponse.fail(response.getError());
            }

            return ApiResponse.success("AI回复成功", response);

        } catch (Exception e) {
            log.error("AI聊天异常", e);
            return ApiResponse.fail("AI服务异常：" + e.getMessage());
        }
    }

    /**
     * 创建新的AI会话
     *
     * @param sessionName 会话名称
     * @return 会话信息
     */
    @PostMapping("/sessions")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<AiSession> createSession(@RequestParam(required = false) String sessionName) {
        try {
            Long userId = getCurrentUserId();
            AiSession session = aiChatService.createSession(sessionName, userId);
            return ApiResponse.success("会话创建成功", session);

        } catch (Exception e) {
            log.error("创建AI会话异常", e);
            return ApiResponse.fail("创建会话失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的AI会话列表
     *
     * @return 会话列表
     */
    @GetMapping("/sessions")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<AiSession>> getUserSessions() {
        try {
            Long userId = getCurrentUserId();
            List<AiSession> sessions = aiChatService.getUserSessions(userId);
            return ApiResponse.success("获取会话列表成功", sessions);

        } catch (Exception e) {
            log.error("获取AI会话列表异常", e);
            return ApiResponse.fail("获取会话列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取AI会话详情（包含消息列表）
     *
     * @param sessionId 会话ID
     * @return 会话详情
     */
    @GetMapping("/sessions/{sessionId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<AiSession> getSessionDetail(@PathVariable String sessionId) {
        try {
            Long userId = getCurrentUserId();
            AiSession session = aiChatService.getSessionDetail(sessionId, userId);

            if (session == null) {
                return ApiResponse.fail("会话不存在或无权访问", 404);
            }

            return ApiResponse.success("获取会话详情成功", session);

        } catch (Exception e) {
            log.error("获取AI会话详情异常", e);
            return ApiResponse.fail("获取会话详情失败：" + e.getMessage());
        }
    }

    /**
     * 删除AI会话
     *
     * @param sessionId 会话ID
     * @return 删除结果
     */
    @DeleteMapping("/sessions/{sessionId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> deleteSession(@PathVariable String sessionId) {
        try {
            Long userId = getCurrentUserId();
            boolean success = aiChatService.deleteSession(sessionId, userId);

            if (success) {
                return ApiResponse.successVoid("会话删除成功");
            } else {
                return ApiResponse.fail("会话不存在或无权删除", 404);
            }

        } catch (Exception e) {
            log.error("删除AI会话异常", e);
            return ApiResponse.fail("删除会话失败：" + e.getMessage());
        }
    }

    /**
     * 清空AI会话历史消息
     *
     * @param sessionId 会话ID
     * @return 清空结果
     */
    @DeleteMapping("/sessions/{sessionId}/messages")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> clearSessionHistory(@PathVariable String sessionId) {
        try {
            Long userId = getCurrentUserId();
            boolean success = aiChatService.clearSessionHistory(sessionId, userId);

            if (success) {
                return ApiResponse.successVoid("会话历史清空成功");
            } else {
                return ApiResponse.fail("会话不存在或无权操作", 404);
            }

        } catch (Exception e) {
            log.error("清空AI会话历史异常", e);
            return ApiResponse.fail("清空会话历史失败：" + e.getMessage());
        }
    }

    /**
     * 获取AI会话的消息列表
     *
     * @param sessionId 会话ID
     * @param limit     消息数量限制
     * @return 消息列表
     */
    @GetMapping("/sessions/{sessionId}/messages")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<AiMessage>> getSessionMessages(
            @PathVariable String sessionId,
            @RequestParam(required = false) Integer limit) {
        try {
            Long userId = getCurrentUserId();
            List<AiMessage> messages = aiChatService.getSessionMessages(sessionId, userId, limit);
            return ApiResponse.success("获取消息列表成功", messages);

        } catch (Exception e) {
            log.error("获取AI会话消息异常", e);
            return ApiResponse.fail("获取消息列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("用户未登录");
        }

        String username = authentication.getName();
        if ("anonymousUser".equals(username)) {
            throw new RuntimeException("用户未登录");
        }

        User user = userService.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return user.getUserId();
    }
}
