<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="visible"
    @update:model-value="handleDialogClose"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="import-dialog-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="下载模板" description="下载Excel导入模板"></el-step>
        <el-step title="上传文件" description="选择填写好的Excel文件"></el-step>
        <el-step title="导入结果" description="查看导入结果"></el-step>
      </el-steps>

      <!-- 步骤1: 下载模板 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="template-download">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请先下载Excel模板文件</p>
              <p>2. 按照模板格式填写数据</p>
              <p>3. 必填字段不能为空</p>
              <p>4. 请确保数据格式正确</p>
            </template>
          </el-alert>
          
          <div class="download-section">
            <el-button 
              type="primary" 
              size="large"
              :loading="downloadLoading"
              @click="handleDownloadTemplate"
            >
              <el-icon><Download /></el-icon>
              下载{{ importType === 'user' ? '用户' : '分类' }}导入模板
            </el-button>
          </div>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :accept="'.xlsx,.xls'"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 .xlsx/.xls 文件，且不超过10MB
            </div>
          </template>
        </el-upload>

        <div class="file-info" v-if="selectedFile">
          <p><strong>选择的文件：</strong>{{ selectedFile.name }}</p>
          <p><strong>文件大小：</strong>{{ formatFileSize(selectedFile.size) }}</p>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="import-result">
          <el-result
            :icon="importResult.successCount > 0 ? 'success' : 'error'"
            :title="importResult.successCount > 0 ? '导入完成' : '导入失败'"
          >
            <template #sub-title>
              <div class="result-summary">
                <p>总处理数量：{{ importResult.totalCount }}</p>
                <p>成功导入：<span class="success-count">{{ importResult.successCount }}</span></p>
                <p>失败数量：<span class="fail-count">{{ importResult.failCount }}</span></p>
                <p>耗时：{{ importResult.duration }}ms</p>
              </div>
            </template>
          </el-result>

          <!-- 错误信息 -->
          <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0" class="error-messages">
            <el-collapse>
              <el-collapse-item title="查看错误详情" name="errors">
                <div class="error-list">
                  <div 
                    v-for="(error, index) in importResult.errorMessages" 
                    :key="index"
                    class="error-item"
                  >
                    {{ error }}
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          v-if="currentStep === 0" 
          type="primary" 
          @click="nextStep"
          :disabled="!templateDownloaded"
        >
          下一步
        </el-button>
        <el-button 
          v-if="currentStep === 1" 
          @click="prevStep"
        >
          上一步
        </el-button>
        <el-button 
          v-if="currentStep === 1" 
          type="primary" 
          @click="handleImport"
          :disabled="!selectedFile"
          :loading="importLoading"
        >
          开始导入
        </el-button>
        <el-button 
          v-if="currentStep === 2" 
          type="primary" 
          @click="handleClose"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { 
  downloadUserTemplate, 
  downloadCategoryTemplate,
  importUsers,
  importCategories
} from '@/admin/api/import'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  importType: {
    type: String,
    required: true,
    validator: (value) => ['user', 'category'].includes(value)
  }
})

const emit = defineEmits(['update:visible', 'import-success'])

// 响应式数据
const currentStep = ref(0)
const downloadLoading = ref(false)
const importLoading = ref(false)
const templateDownloaded = ref(false)
const selectedFile = ref(null)
const fileList = ref([])
const uploadRef = ref(null)
const importResult = ref({})

// 计算属性
const dialogTitle = computed(() => {
  return `批量导入${props.importType === 'user' ? '用户' : '分类'}`
})

// 监听visible变化，重置状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetDialog()
  }
})

// 重置对话框状态
const resetDialog = () => {
  currentStep.value = 0
  templateDownloaded.value = false
  selectedFile.value = null
  fileList.value = []
  importResult.value = {}
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  downloadLoading.value = true
  try {
    const downloadFunc = props.importType === 'user' ? downloadUserTemplate : downloadCategoryTemplate
    const response = await downloadFunc()
    
    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.importType === 'user' ? '用户' : '分类'}导入模板.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    templateDownloaded.value = true
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloadLoading.value = false
  }
}

// 文件选择
const handleFileChange = (file) => {
  selectedFile.value = file.raw
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 执行导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importLoading.value = true
  try {
    const importFunc = props.importType === 'user' ? importUsers : importCategories
    const response = await importFunc(selectedFile.value)
    
    if (response.success) {
      importResult.value = response.data
      currentStep.value = 2
      
      // 通知父组件导入成功
      emit('import-success', response.data)
    } else {
      ElMessage.error(response.message || '导入失败')
      if (response.data) {
        importResult.value = response.data
        currentStep.value = 2
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败：' + (error.message || '未知错误'))
  } finally {
    importLoading.value = false
  }
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 处理对话框关闭（来自el-dialog的model-value更新）
const handleDialogClose = (value) => {
  if (!value) {
    emit('update:visible', false)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.import-dialog-content {
  padding: 20px 0;
}

.step-content {
  margin-top: 30px;
  min-height: 200px;
}

.template-download {
  text-align: center;
}

.download-section {
  margin-top: 20px;
}

.upload-demo {
  margin-top: 20px;
}

.file-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.import-result {
  text-align: center;
}

.result-summary {
  text-align: left;
  margin: 20px 0;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
}

.fail-count {
  color: #f56c6c;
  font-weight: bold;
}

.error-messages {
  margin-top: 20px;
  text-align: left;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item {
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
