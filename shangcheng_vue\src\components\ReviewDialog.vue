<template>
  <el-dialog
    v-model="visible"
    title="订单完成 - 评价卖家"
    width="500px"
    :before-close="handleClose"
    class="review-dialog">
    
    <div class="review-form">
      <!-- 订单信息 -->
      <div class="order-info">
        <img :src="orderInfo.productImage || defaultImage" class="product-image" />
        <div class="product-details">
          <h3>{{ orderInfo.productTitle }}</h3>
          <p>卖家：{{ orderInfo.sellerName }}</p>
          <p>订单金额：¥{{ formatPrice(orderInfo.totalAmount) }}</p>
        </div>
      </div>
      
      <!-- 星级评价 -->
      <div class="rating-section">
        <h4>请为这次购物体验打分：</h4>
        <el-rate 
          v-model="reviewForm.rating" 
          :max="5" 
          show-text 
          :texts="ratingTexts"
          class="rating-stars" />
        <div class="rating-desc">
          {{ getRatingDescription(reviewForm.rating) }}
        </div>
      </div>
      
      <!-- 评价标签 -->
      <div class="tags-section">
        <h4>选择评价标签（可选）：</h4>
        <div class="tag-buttons">
          <el-button 
            v-for="tag in availableTags" 
            :key="tag"
            :type="selectedTags.includes(tag) ? 'primary' : ''"
            size="small"
            @click="toggleTag(tag)">
            {{ tag }}
          </el-button>
        </div>
      </div>
      
      <!-- 文字评价 -->
      <div class="content-section">
        <h4>详细评价（可选）：</h4>
        <el-input 
          v-model="reviewForm.content"
          type="textarea"
          :rows="3"
          placeholder="分享您的购物体验，帮助其他买家..."
          maxlength="500"
          show-word-limit />
      </div>
      
      <!-- 匿名选项 -->
      <div class="anonymous-section">
        <el-checkbox v-model="reviewForm.anonymous">匿名评价</el-checkbox>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="skipReview">跳过评价</el-button>
        <el-button type="primary" @click="submitReview" :loading="submitting">提交评价</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { submitSellerReview } from '@/api/review'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'skip'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const reviewForm = ref({
  rating: 5,
  content: '',
  anonymous: false
})

const selectedTags = ref([])
const submitting = ref(false)

// 默认图片
const defaultImage = 'https://via.placeholder.com/80x80'

// 评分文本
const ratingTexts = ['非常不满意', '不满意', '一般', '满意', '非常满意']

// 可选标签
const availableTags = ref([
  '发货快', '包装好', '质量好', '服务态度好', '物流快',
  '商品描述准确', '性价比高', '推荐购买', '会再次购买'
])

// 方法
const formatPrice = (price) => {
  return price ? price.toLocaleString() : '0'
}

const getRatingDescription = (rating) => {
  if (!rating) return ''
  return ratingTexts[rating - 1] || ''
}

const toggleTag = (tag) => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
}

const handleClose = () => {
  visible.value = false
}

const skipReview = () => {
  visible.value = false
  emit('skip')
}

const submitReview = async () => {
  if (!reviewForm.value.rating) {
    ElMessage.warning('请选择评分')
    return
  }

  submitting.value = true
  
  try {
    const reviewData = {
      orderId: props.orderInfo.orderId,
      rating: reviewForm.value.rating,
      reviewContent: reviewForm.value.content,
      reviewTags: selectedTags.value,
      isAnonymous: reviewForm.value.anonymous
    }

    const response = await submitSellerReview(reviewData)
    
    if (response && response.code === 200) {
      ElMessage.success('评价提交成功')
      visible.value = false
      emit('success', response.data)
      resetForm()
    } else {
      ElMessage.error(response?.message || '评价提交失败')
    }
  } catch (error) {
    console.error('提交评价失败:', error)
    ElMessage.error('评价提交失败，请稍后再试')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  reviewForm.value = {
    rating: 5,
    content: '',
    anonymous: false
  }
  selectedTags.value = []
}

// 监听弹窗打开，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.review-dialog {
  border-radius: 8px;
}

.review-form {
  padding: 10px 0;
}

.order-info {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.product-details {
  flex: 1;
}

.product-details h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.product-details p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.rating-section {
  margin-bottom: 25px;
}

.rating-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.rating-stars {
  margin-bottom: 10px;
}

.rating-desc {
  color: #666;
  font-size: 14px;
}

.tags-section {
  margin-bottom: 25px;
}

.tags-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.tag-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.content-section {
  margin-bottom: 20px;
}

.content-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.anonymous-section {
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .order-info {
    flex-direction: column;
    text-align: center;
  }
  
  .tag-buttons {
    justify-content: center;
  }
}
</style>
