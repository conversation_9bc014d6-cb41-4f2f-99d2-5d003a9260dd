package com.lzhshtp.shangcheng.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.DateFormat;

import java.time.LocalDateTime;

/**
 * 论坛帖子搜索文档
 * 用于ElasticSearch全文检索
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "forum_posts")
public class ForumPostDocument {

    @Id
    private Long postId;

    /**
     * 帖子标题 - 主要搜索字段，权重最高
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;

    /**
     * 帖子内容 - 主要搜索字段
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String content;

    /**
     * 作者ID - 用于过滤
     */
    @Field(type = FieldType.Long)
    private Long authorId;

    /**
     * 作者用户名 - 用于搜索和显示
     */
    @Field(type = FieldType.Keyword)
    private String authorUsername;

    /**
     * 分类ID - 用于分类过滤
     */
    @Field(type = FieldType.Integer)
    private Integer categoryId;

    /**
     * 分类名称 - 用于搜索和显示
     */
    @Field(type = FieldType.Keyword)
    private String categoryName;

    /**
     * 发布时间 - 用于排序
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime postedAt;

    /**
     * 浏览次数 - 用于热度排序
     */
    @Field(type = FieldType.Integer)
    private Integer viewsCount;

    /**
     * 是否置顶 - 用于排序
     */
    @Field(type = FieldType.Boolean)
    private Boolean isPinned;

    /**
     * 帖子状态 - 用于过滤
     */
    @Field(type = FieldType.Integer)
    private Integer status;

    /**
     * 评论数量 - 用于热度排序
     */
    @Field(type = FieldType.Integer)
    private Integer commentCount;

    /**
     * 点赞数量 - 用于热度排序
     */
    @Field(type = FieldType.Integer)
    private Integer likeCount;

    /**
     * 帖子标签 - 用于标签搜索
     */
    @Field(type = FieldType.Keyword)
    private String[] tags;

    /**
     * 搜索权重分数 - 综合评分
     */
    @Field(type = FieldType.Double)
    private Double searchScore;

    /**
     * 创建时间 - 用于索引管理
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime createdAt;

    /**
     * 更新时间 - 用于增量同步
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime updatedAt;
}
