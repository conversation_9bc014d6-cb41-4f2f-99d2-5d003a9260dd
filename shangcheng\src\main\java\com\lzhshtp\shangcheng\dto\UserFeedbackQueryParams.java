package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户反馈/举报查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackQueryParams {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    
    /**
     * 提交反馈的用户ID
     */
    private Long reporterId;
    
    /**
     * 反馈类型: bug_report(问题反馈), suggestion(建议), complaint(投诉), abuse_report(举报)
     */
    private String feedbackType;
    
    /**
     * 处理状态: pending(待处理), in_progress(处理中), resolved(已解决), rejected(已拒绝)
     */
    private String status;
    
    /**
     * 如果是举报，则指明举报对象类型（例如 'product', 'user', 'post'）
     */
    private String relatedEntityType;
    
    /**
     * 关联的实体ID（例如：举报商品则为product_id）
     */
    private Long relatedEntityId;
    
    /**
     * 关键词搜索（可搜索反馈内容、用户名等）
     */
    private String keyword;
} 