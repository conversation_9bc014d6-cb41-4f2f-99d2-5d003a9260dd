package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lzhshtp.shangcheng.model.UserFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户反馈/举报 Mapper 接口
 */
public interface UserFeedbackMapper extends BaseMapper<UserFeedback> {

    /**
     * 分页查询用户反馈列表
     *
     * @param page 分页参数
     * @param reporterId 提交反馈的用户ID，可为null
     * @param feedbackType 反馈类型，可为null
     * @param status 处理状态，可为null
     * @param relatedEntityType 关联实体类型，可为null
     * @param relatedEntityId 关联实体ID，可为null
     * @param keyword 关键词搜索，可为null
     * @return 分页结果
     */
    IPage<UserFeedback> selectFeedbackList(
            Page<UserFeedback> page,
            @Param("reporterId") Long reporterId,
            @Param("feedbackType") String feedbackType,
            @Param("status") String status,
            @Param("relatedEntityType") String relatedEntityType,
            @Param("relatedEntityId") Long relatedEntityId,
            @Param("keyword") String keyword
    );
    
    /**
     * 更新反馈状态
     *
     * @param feedbackId 反馈ID
     * @param status 新状态
     * @param adminId 处理的管理员ID
     * @param adminNotes 管理员备注
     * @return 影响行数
     */
    int updateFeedbackStatus(
            @Param("feedbackId") Long feedbackId,
            @Param("status") String status,
            @Param("adminId") Long adminId,
            @Param("adminNotes") String adminNotes
    );
} 