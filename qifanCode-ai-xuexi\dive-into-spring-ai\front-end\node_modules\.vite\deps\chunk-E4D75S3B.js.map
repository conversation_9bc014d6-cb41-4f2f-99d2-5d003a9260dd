{"version": 3, "sources": ["../../@codemirror/autocomplete/dist/index.js"], "sourcesContent": ["import { Annotation, StateEffect, EditorSelection, codePointAt, codePointSize, fromCodePoint, Facet, combineConfig, StateField, Prec, Text, Transaction, MapMode, RangeValue, RangeSet, CharCategory } from '@codemirror/state';\nimport { Direction, logException, showTooltip, EditorView, ViewPlugin, getTooltip, Decoration, WidgetType, keymap } from '@codemirror/view';\nimport { syntaxTree, indentUnit } from '@codemirror/language';\n\n/**\nAn instance of this is passed to completion source functions.\n*/\nclass CompletionContext {\n    /**\n    Create a new completion context. (Mostly useful for testing\n    completion sources—in the editor, the extension will create\n    these for you.)\n    */\n    constructor(\n    /**\n    The editor state that the completion happens in.\n    */\n    state, \n    /**\n    The position at which the completion is happening.\n    */\n    pos, \n    /**\n    Indicates whether completion was activated explicitly, or\n    implicitly by typing. The usual way to respond to this is to\n    only return completions when either there is part of a\n    completable entity before the cursor, or `explicit` is true.\n    */\n    explicit) {\n        this.state = state;\n        this.pos = pos;\n        this.explicit = explicit;\n        /**\n        @internal\n        */\n        this.abortListeners = [];\n    }\n    /**\n    Get the extent, content, and (if there is a token) type of the\n    token before `this.pos`.\n    */\n    tokenBefore(types) {\n        let token = syntaxTree(this.state).resolveInner(this.pos, -1);\n        while (token && types.indexOf(token.name) < 0)\n            token = token.parent;\n        return token ? { from: token.from, to: this.pos,\n            text: this.state.sliceDoc(token.from, this.pos),\n            type: token.type } : null;\n    }\n    /**\n    Get the match of the given expression directly before the\n    cursor.\n    */\n    matchBefore(expr) {\n        let line = this.state.doc.lineAt(this.pos);\n        let start = Math.max(line.from, this.pos - 250);\n        let str = line.text.slice(start - line.from, this.pos - line.from);\n        let found = str.search(ensureAnchor(expr, false));\n        return found < 0 ? null : { from: start + found, to: this.pos, text: str.slice(found) };\n    }\n    /**\n    Yields true when the query has been aborted. Can be useful in\n    asynchronous queries to avoid doing work that will be ignored.\n    */\n    get aborted() { return this.abortListeners == null; }\n    /**\n    Allows you to register abort handlers, which will be called when\n    the query is\n    [aborted](https://codemirror.net/6/docs/ref/#autocomplete.CompletionContext.aborted).\n    */\n    addEventListener(type, listener) {\n        if (type == \"abort\" && this.abortListeners)\n            this.abortListeners.push(listener);\n    }\n}\nfunction toSet(chars) {\n    let flat = Object.keys(chars).join(\"\");\n    let words = /\\w/.test(flat);\n    if (words)\n        flat = flat.replace(/\\w/g, \"\");\n    return `[${words ? \"\\\\w\" : \"\"}${flat.replace(/[^\\w\\s]/g, \"\\\\$&\")}]`;\n}\nfunction prefixMatch(options) {\n    let first = Object.create(null), rest = Object.create(null);\n    for (let { label } of options) {\n        first[label[0]] = true;\n        for (let i = 1; i < label.length; i++)\n            rest[label[i]] = true;\n    }\n    let source = toSet(first) + toSet(rest) + \"*$\";\n    return [new RegExp(\"^\" + source), new RegExp(source)];\n}\n/**\nGiven a a fixed array of options, return an autocompleter that\ncompletes them.\n*/\nfunction completeFromList(list) {\n    let options = list.map(o => typeof o == \"string\" ? { label: o } : o);\n    let [validFor, match] = options.every(o => /^\\w+$/.test(o.label)) ? [/\\w*$/, /\\w+$/] : prefixMatch(options);\n    return (context) => {\n        let token = context.matchBefore(match);\n        return token || context.explicit ? { from: token ? token.from : context.pos, options, validFor } : null;\n    };\n}\n/**\nWrap the given completion source so that it will only fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifIn(nodes, source) {\n    return (context) => {\n        for (let pos = syntaxTree(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return source(context);\n            if (pos.type.isTop)\n                break;\n        }\n        return null;\n    };\n}\n/**\nWrap the given completion source so that it will not fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifNotIn(nodes, source) {\n    return (context) => {\n        for (let pos = syntaxTree(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return null;\n            if (pos.type.isTop)\n                break;\n        }\n        return source(context);\n    };\n}\nclass Option {\n    constructor(completion, source, match, score) {\n        this.completion = completion;\n        this.source = source;\n        this.match = match;\n        this.score = score;\n    }\n}\nfunction cur(state) { return state.selection.main.from; }\n// Make sure the given regexp has a $ at its end and, if `start` is\n// true, a ^ at its start.\nfunction ensureAnchor(expr, start) {\n    var _a;\n    let { source } = expr;\n    let addStart = start && source[0] != \"^\", addEnd = source[source.length - 1] != \"$\";\n    if (!addStart && !addEnd)\n        return expr;\n    return new RegExp(`${addStart ? \"^\" : \"\"}(?:${source})${addEnd ? \"$\" : \"\"}`, (_a = expr.flags) !== null && _a !== void 0 ? _a : (expr.ignoreCase ? \"i\" : \"\"));\n}\n/**\nThis annotation is added to transactions that are produced by\npicking a completion.\n*/\nconst pickedCompletion = /*@__PURE__*/Annotation.define();\n/**\nHelper function that returns a transaction spec which inserts a\ncompletion's text in the main selection range, and any other\nselection range that has the same text in front of it.\n*/\nfunction insertCompletionText(state, text, from, to) {\n    let { main } = state.selection, fromOff = from - main.from, toOff = to - main.from;\n    return Object.assign(Object.assign({}, state.changeByRange(range => {\n        if (range != main && from != to &&\n            state.sliceDoc(range.from + fromOff, range.from + toOff) != state.sliceDoc(from, to))\n            return { range };\n        return {\n            changes: { from: range.from + fromOff, to: to == main.from ? range.to : range.from + toOff, insert: text },\n            range: EditorSelection.cursor(range.from + fromOff + text.length)\n        };\n    })), { scrollIntoView: true, userEvent: \"input.complete\" });\n}\nconst SourceCache = /*@__PURE__*/new WeakMap();\nfunction asSource(source) {\n    if (!Array.isArray(source))\n        return source;\n    let known = SourceCache.get(source);\n    if (!known)\n        SourceCache.set(source, known = completeFromList(source));\n    return known;\n}\nconst startCompletionEffect = /*@__PURE__*/StateEffect.define();\nconst closeCompletionEffect = /*@__PURE__*/StateEffect.define();\n\n// A pattern matcher for fuzzy completion matching. Create an instance\n// once for a pattern, and then use that to match any number of\n// completions.\nclass FuzzyMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.chars = [];\n        this.folded = [];\n        // Buffers reused by calls to `match` to track matched character\n        // positions.\n        this.any = [];\n        this.precise = [];\n        this.byWord = [];\n        this.score = 0;\n        this.matched = [];\n        for (let p = 0; p < pattern.length;) {\n            let char = codePointAt(pattern, p), size = codePointSize(char);\n            this.chars.push(char);\n            let part = pattern.slice(p, p + size), upper = part.toUpperCase();\n            this.folded.push(codePointAt(upper == part ? part.toLowerCase() : upper, 0));\n            p += size;\n        }\n        this.astral = pattern.length != this.chars.length;\n    }\n    ret(score, matched) {\n        this.score = score;\n        this.matched = matched;\n        return this;\n    }\n    // Matches a given word (completion) against the pattern (input).\n    // Will return a boolean indicating whether there was a match and,\n    // on success, set `this.score` to the score, `this.matched` to an\n    // array of `from, to` pairs indicating the matched parts of `word`.\n    //\n    // The score is a number that is more negative the worse the match\n    // is. See `Penalty` above.\n    match(word) {\n        if (this.pattern.length == 0)\n            return this.ret(-100 /* Penalty.NotFull */, []);\n        if (word.length < this.pattern.length)\n            return null;\n        let { chars, folded, any, precise, byWord } = this;\n        // For single-character queries, only match when they occur right\n        // at the start\n        if (chars.length == 1) {\n            let first = codePointAt(word, 0), firstSize = codePointSize(first);\n            let score = firstSize == word.length ? 0 : -100 /* Penalty.NotFull */;\n            if (first == chars[0]) ;\n            else if (first == folded[0])\n                score += -200 /* Penalty.CaseFold */;\n            else\n                return null;\n            return this.ret(score, [0, firstSize]);\n        }\n        let direct = word.indexOf(this.pattern);\n        if (direct == 0)\n            return this.ret(word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */, [0, this.pattern.length]);\n        let len = chars.length, anyTo = 0;\n        if (direct < 0) {\n            for (let i = 0, e = Math.min(word.length, 200); i < e && anyTo < len;) {\n                let next = codePointAt(word, i);\n                if (next == chars[anyTo] || next == folded[anyTo])\n                    any[anyTo++] = i;\n                i += codePointSize(next);\n            }\n            // No match, exit immediately\n            if (anyTo < len)\n                return null;\n        }\n        // This tracks the extent of the precise (non-folded, not\n        // necessarily adjacent) match\n        let preciseTo = 0;\n        // Tracks whether there is a match that hits only characters that\n        // appear to be starting words. `byWordFolded` is set to true when\n        // a case folded character is encountered in such a match\n        let byWordTo = 0, byWordFolded = false;\n        // If we've found a partial adjacent match, these track its state\n        let adjacentTo = 0, adjacentStart = -1, adjacentEnd = -1;\n        let hasLower = /[a-z]/.test(word), wordAdjacent = true;\n        // Go over the option's text, scanning for the various kinds of matches\n        for (let i = 0, e = Math.min(word.length, 200), prevType = 0 /* Tp.NonWord */; i < e && byWordTo < len;) {\n            let next = codePointAt(word, i);\n            if (direct < 0) {\n                if (preciseTo < len && next == chars[preciseTo])\n                    precise[preciseTo++] = i;\n                if (adjacentTo < len) {\n                    if (next == chars[adjacentTo] || next == folded[adjacentTo]) {\n                        if (adjacentTo == 0)\n                            adjacentStart = i;\n                        adjacentEnd = i + 1;\n                        adjacentTo++;\n                    }\n                    else {\n                        adjacentTo = 0;\n                    }\n                }\n            }\n            let ch, type = next < 0xff\n                ? (next >= 48 && next <= 57 || next >= 97 && next <= 122 ? 2 /* Tp.Lower */ : next >= 65 && next <= 90 ? 1 /* Tp.Upper */ : 0 /* Tp.NonWord */)\n                : ((ch = fromCodePoint(next)) != ch.toLowerCase() ? 1 /* Tp.Upper */ : ch != ch.toUpperCase() ? 2 /* Tp.Lower */ : 0 /* Tp.NonWord */);\n            if (!i || type == 1 /* Tp.Upper */ && hasLower || prevType == 0 /* Tp.NonWord */ && type != 0 /* Tp.NonWord */) {\n                if (chars[byWordTo] == next || (folded[byWordTo] == next && (byWordFolded = true)))\n                    byWord[byWordTo++] = i;\n                else if (byWord.length)\n                    wordAdjacent = false;\n            }\n            prevType = type;\n            i += codePointSize(next);\n        }\n        if (byWordTo == len && byWord[0] == 0 && wordAdjacent)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0), byWord, word);\n        if (adjacentTo == len && adjacentStart == 0)\n            return this.ret(-200 /* Penalty.CaseFold */ - word.length + (adjacentEnd == word.length ? 0 : -100 /* Penalty.NotFull */), [0, adjacentEnd]);\n        if (direct > -1)\n            return this.ret(-700 /* Penalty.NotStart */ - word.length, [direct, direct + this.pattern.length]);\n        if (adjacentTo == len)\n            return this.ret(-200 /* Penalty.CaseFold */ + -700 /* Penalty.NotStart */ - word.length, [adjacentStart, adjacentEnd]);\n        if (byWordTo == len)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0) + -700 /* Penalty.NotStart */ +\n                (wordAdjacent ? 0 : -1100 /* Penalty.Gap */), byWord, word);\n        return chars.length == 2 ? null\n            : this.result((any[0] ? -700 /* Penalty.NotStart */ : 0) + -200 /* Penalty.CaseFold */ + -1100 /* Penalty.Gap */, any, word);\n    }\n    result(score, positions, word) {\n        let result = [], i = 0;\n        for (let pos of positions) {\n            let to = pos + (this.astral ? codePointSize(codePointAt(word, pos)) : 1);\n            if (i && result[i - 1] == pos)\n                result[i - 1] = to;\n            else {\n                result[i++] = pos;\n                result[i++] = to;\n            }\n        }\n        return this.ret(score - word.length, result);\n    }\n}\nclass StrictMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.matched = [];\n        this.score = 0;\n        this.folded = pattern.toLowerCase();\n    }\n    match(word) {\n        if (word.length < this.pattern.length)\n            return null;\n        let start = word.slice(0, this.pattern.length);\n        let match = start == this.pattern ? 0 : start.toLowerCase() == this.folded ? -200 /* Penalty.CaseFold */ : null;\n        if (match == null)\n            return null;\n        this.matched = [0, start.length];\n        this.score = match + (word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */);\n        return this;\n    }\n}\n\nconst completionConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            activateOnTyping: true,\n            activateOnCompletion: () => false,\n            activateOnTypingDelay: 100,\n            selectOnOpen: true,\n            override: null,\n            closeOnBlur: true,\n            maxRenderedOptions: 100,\n            defaultKeymap: true,\n            tooltipClass: () => \"\",\n            optionClass: () => \"\",\n            aboveCursor: false,\n            icons: true,\n            addToOptions: [],\n            positionInfo: defaultPositionInfo,\n            filterStrict: false,\n            compareCompletions: (a, b) => a.label.localeCompare(b.label),\n            interactionDelay: 75,\n            updateSyncTime: 100\n        }, {\n            defaultKeymap: (a, b) => a && b,\n            closeOnBlur: (a, b) => a && b,\n            icons: (a, b) => a && b,\n            tooltipClass: (a, b) => c => joinClass(a(c), b(c)),\n            optionClass: (a, b) => c => joinClass(a(c), b(c)),\n            addToOptions: (a, b) => a.concat(b),\n            filterStrict: (a, b) => a || b,\n        });\n    }\n});\nfunction joinClass(a, b) {\n    return a ? b ? a + \" \" + b : a : b;\n}\nfunction defaultPositionInfo(view, list, option, info, space, tooltip) {\n    let rtl = view.textDirection == Direction.RTL, left = rtl, narrow = false;\n    let side = \"top\", offset, maxWidth;\n    let spaceLeft = list.left - space.left, spaceRight = space.right - list.right;\n    let infoWidth = info.right - info.left, infoHeight = info.bottom - info.top;\n    if (left && spaceLeft < Math.min(infoWidth, spaceRight))\n        left = false;\n    else if (!left && spaceRight < Math.min(infoWidth, spaceLeft))\n        left = true;\n    if (infoWidth <= (left ? spaceLeft : spaceRight)) {\n        offset = Math.max(space.top, Math.min(option.top, space.bottom - infoHeight)) - list.top;\n        maxWidth = Math.min(400 /* Info.Width */, left ? spaceLeft : spaceRight);\n    }\n    else {\n        narrow = true;\n        maxWidth = Math.min(400 /* Info.Width */, (rtl ? list.right : space.right - list.left) - 30 /* Info.Margin */);\n        let spaceBelow = space.bottom - list.bottom;\n        if (spaceBelow >= infoHeight || spaceBelow > list.top) { // Below the completion\n            offset = option.bottom - list.top;\n        }\n        else { // Above it\n            side = \"bottom\";\n            offset = list.bottom - option.top;\n        }\n    }\n    let scaleY = (list.bottom - list.top) / tooltip.offsetHeight;\n    let scaleX = (list.right - list.left) / tooltip.offsetWidth;\n    return {\n        style: `${side}: ${offset / scaleY}px; max-width: ${maxWidth / scaleX}px`,\n        class: \"cm-completionInfo-\" + (narrow ? (rtl ? \"left-narrow\" : \"right-narrow\") : left ? \"left\" : \"right\")\n    };\n}\n\nfunction optionContent(config) {\n    let content = config.addToOptions.slice();\n    if (config.icons)\n        content.push({\n            render(completion) {\n                let icon = document.createElement(\"div\");\n                icon.classList.add(\"cm-completionIcon\");\n                if (completion.type)\n                    icon.classList.add(...completion.type.split(/\\s+/g).map(cls => \"cm-completionIcon-\" + cls));\n                icon.setAttribute(\"aria-hidden\", \"true\");\n                return icon;\n            },\n            position: 20\n        });\n    content.push({\n        render(completion, _s, _v, match) {\n            let labelElt = document.createElement(\"span\");\n            labelElt.className = \"cm-completionLabel\";\n            let label = completion.displayLabel || completion.label, off = 0;\n            for (let j = 0; j < match.length;) {\n                let from = match[j++], to = match[j++];\n                if (from > off)\n                    labelElt.appendChild(document.createTextNode(label.slice(off, from)));\n                let span = labelElt.appendChild(document.createElement(\"span\"));\n                span.appendChild(document.createTextNode(label.slice(from, to)));\n                span.className = \"cm-completionMatchedText\";\n                off = to;\n            }\n            if (off < label.length)\n                labelElt.appendChild(document.createTextNode(label.slice(off)));\n            return labelElt;\n        },\n        position: 50\n    }, {\n        render(completion) {\n            if (!completion.detail)\n                return null;\n            let detailElt = document.createElement(\"span\");\n            detailElt.className = \"cm-completionDetail\";\n            detailElt.textContent = completion.detail;\n            return detailElt;\n        },\n        position: 80\n    });\n    return content.sort((a, b) => a.position - b.position).map(a => a.render);\n}\nfunction rangeAroundSelected(total, selected, max) {\n    if (total <= max)\n        return { from: 0, to: total };\n    if (selected < 0)\n        selected = 0;\n    if (selected <= (total >> 1)) {\n        let off = Math.floor(selected / max);\n        return { from: off * max, to: (off + 1) * max };\n    }\n    let off = Math.floor((total - selected) / max);\n    return { from: total - (off + 1) * max, to: total - off * max };\n}\nclass CompletionTooltip {\n    constructor(view, stateField, applyCompletion) {\n        this.view = view;\n        this.stateField = stateField;\n        this.applyCompletion = applyCompletion;\n        this.info = null;\n        this.infoDestroy = null;\n        this.placeInfoReq = {\n            read: () => this.measureInfo(),\n            write: (pos) => this.placeInfo(pos),\n            key: this\n        };\n        this.space = null;\n        this.currentClass = \"\";\n        let cState = view.state.field(stateField);\n        let { options, selected } = cState.open;\n        let config = view.state.facet(completionConfig);\n        this.optionContent = optionContent(config);\n        this.optionClass = config.optionClass;\n        this.tooltipClass = config.tooltipClass;\n        this.range = rangeAroundSelected(options.length, selected, config.maxRenderedOptions);\n        this.dom = document.createElement(\"div\");\n        this.dom.className = \"cm-tooltip-autocomplete\";\n        this.updateTooltipClass(view.state);\n        this.dom.addEventListener(\"mousedown\", (e) => {\n            let { options } = view.state.field(stateField).open;\n            for (let dom = e.target, match; dom && dom != this.dom; dom = dom.parentNode) {\n                if (dom.nodeName == \"LI\" && (match = /-(\\d+)$/.exec(dom.id)) && +match[1] < options.length) {\n                    this.applyCompletion(view, options[+match[1]]);\n                    e.preventDefault();\n                    return;\n                }\n            }\n        });\n        this.dom.addEventListener(\"focusout\", (e) => {\n            let state = view.state.field(this.stateField, false);\n            if (state && state.tooltip && view.state.facet(completionConfig).closeOnBlur &&\n                e.relatedTarget != view.contentDOM)\n                view.dispatch({ effects: closeCompletionEffect.of(null) });\n        });\n        this.showOptions(options, cState.id);\n    }\n    mount() { this.updateSel(); }\n    showOptions(options, id) {\n        if (this.list)\n            this.list.remove();\n        this.list = this.dom.appendChild(this.createListBox(options, id, this.range));\n        this.list.addEventListener(\"scroll\", () => {\n            if (this.info)\n                this.view.requestMeasure(this.placeInfoReq);\n        });\n    }\n    update(update) {\n        var _a;\n        let cState = update.state.field(this.stateField);\n        let prevState = update.startState.field(this.stateField);\n        this.updateTooltipClass(update.state);\n        if (cState != prevState) {\n            let { options, selected, disabled } = cState.open;\n            if (!prevState.open || prevState.open.options != options) {\n                this.range = rangeAroundSelected(options.length, selected, update.state.facet(completionConfig).maxRenderedOptions);\n                this.showOptions(options, cState.id);\n            }\n            this.updateSel();\n            if (disabled != ((_a = prevState.open) === null || _a === void 0 ? void 0 : _a.disabled))\n                this.dom.classList.toggle(\"cm-tooltip-autocomplete-disabled\", !!disabled);\n        }\n    }\n    updateTooltipClass(state) {\n        let cls = this.tooltipClass(state);\n        if (cls != this.currentClass) {\n            for (let c of this.currentClass.split(\" \"))\n                if (c)\n                    this.dom.classList.remove(c);\n            for (let c of cls.split(\" \"))\n                if (c)\n                    this.dom.classList.add(c);\n            this.currentClass = cls;\n        }\n    }\n    positioned(space) {\n        this.space = space;\n        if (this.info)\n            this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSel() {\n        let cState = this.view.state.field(this.stateField), open = cState.open;\n        if (open.selected > -1 && open.selected < this.range.from || open.selected >= this.range.to) {\n            this.range = rangeAroundSelected(open.options.length, open.selected, this.view.state.facet(completionConfig).maxRenderedOptions);\n            this.showOptions(open.options, cState.id);\n        }\n        if (this.updateSelectedOption(open.selected)) {\n            this.destroyInfo();\n            let { completion } = open.options[open.selected];\n            let { info } = completion;\n            if (!info)\n                return;\n            let infoResult = typeof info === \"string\" ? document.createTextNode(info) : info(completion);\n            if (!infoResult)\n                return;\n            if (\"then\" in infoResult) {\n                infoResult.then(obj => {\n                    if (obj && this.view.state.field(this.stateField, false) == cState)\n                        this.addInfoPane(obj, completion);\n                }).catch(e => logException(this.view.state, e, \"completion info\"));\n            }\n            else {\n                this.addInfoPane(infoResult, completion);\n            }\n        }\n    }\n    addInfoPane(content, completion) {\n        this.destroyInfo();\n        let wrap = this.info = document.createElement(\"div\");\n        wrap.className = \"cm-tooltip cm-completionInfo\";\n        if (content.nodeType != null) {\n            wrap.appendChild(content);\n            this.infoDestroy = null;\n        }\n        else {\n            let { dom, destroy } = content;\n            wrap.appendChild(dom);\n            this.infoDestroy = destroy || null;\n        }\n        this.dom.appendChild(wrap);\n        this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSelectedOption(selected) {\n        let set = null;\n        for (let opt = this.list.firstChild, i = this.range.from; opt; opt = opt.nextSibling, i++) {\n            if (opt.nodeName != \"LI\" || !opt.id) {\n                i--; // A section header\n            }\n            else if (i == selected) {\n                if (!opt.hasAttribute(\"aria-selected\")) {\n                    opt.setAttribute(\"aria-selected\", \"true\");\n                    set = opt;\n                }\n            }\n            else {\n                if (opt.hasAttribute(\"aria-selected\"))\n                    opt.removeAttribute(\"aria-selected\");\n            }\n        }\n        if (set)\n            scrollIntoView(this.list, set);\n        return set;\n    }\n    measureInfo() {\n        let sel = this.dom.querySelector(\"[aria-selected]\");\n        if (!sel || !this.info)\n            return null;\n        let listRect = this.dom.getBoundingClientRect();\n        let infoRect = this.info.getBoundingClientRect();\n        let selRect = sel.getBoundingClientRect();\n        let space = this.space;\n        if (!space) {\n            let win = this.dom.ownerDocument.defaultView || window;\n            space = { left: 0, top: 0, right: win.innerWidth, bottom: win.innerHeight };\n        }\n        if (selRect.top > Math.min(space.bottom, listRect.bottom) - 10 ||\n            selRect.bottom < Math.max(space.top, listRect.top) + 10)\n            return null;\n        return this.view.state.facet(completionConfig).positionInfo(this.view, listRect, selRect, infoRect, space, this.dom);\n    }\n    placeInfo(pos) {\n        if (this.info) {\n            if (pos) {\n                if (pos.style)\n                    this.info.style.cssText = pos.style;\n                this.info.className = \"cm-tooltip cm-completionInfo \" + (pos.class || \"\");\n            }\n            else {\n                this.info.style.cssText = \"top: -1e6px\";\n            }\n        }\n    }\n    createListBox(options, id, range) {\n        const ul = document.createElement(\"ul\");\n        ul.id = id;\n        ul.setAttribute(\"role\", \"listbox\");\n        ul.setAttribute(\"aria-expanded\", \"true\");\n        ul.setAttribute(\"aria-label\", this.view.state.phrase(\"Completions\"));\n        let curSection = null;\n        for (let i = range.from; i < range.to; i++) {\n            let { completion, match } = options[i], { section } = completion;\n            if (section) {\n                let name = typeof section == \"string\" ? section : section.name;\n                if (name != curSection && (i > range.from || range.from == 0)) {\n                    curSection = name;\n                    if (typeof section != \"string\" && section.header) {\n                        ul.appendChild(section.header(section));\n                    }\n                    else {\n                        let header = ul.appendChild(document.createElement(\"completion-section\"));\n                        header.textContent = name;\n                    }\n                }\n            }\n            const li = ul.appendChild(document.createElement(\"li\"));\n            li.id = id + \"-\" + i;\n            li.setAttribute(\"role\", \"option\");\n            let cls = this.optionClass(completion);\n            if (cls)\n                li.className = cls;\n            for (let source of this.optionContent) {\n                let node = source(completion, this.view.state, this.view, match);\n                if (node)\n                    li.appendChild(node);\n            }\n        }\n        if (range.from)\n            ul.classList.add(\"cm-completionListIncompleteTop\");\n        if (range.to < options.length)\n            ul.classList.add(\"cm-completionListIncompleteBottom\");\n        return ul;\n    }\n    destroyInfo() {\n        if (this.info) {\n            if (this.infoDestroy)\n                this.infoDestroy();\n            this.info.remove();\n            this.info = null;\n        }\n    }\n    destroy() {\n        this.destroyInfo();\n    }\n}\nfunction completionTooltip(stateField, applyCompletion) {\n    return (view) => new CompletionTooltip(view, stateField, applyCompletion);\n}\nfunction scrollIntoView(container, element) {\n    let parent = container.getBoundingClientRect();\n    let self = element.getBoundingClientRect();\n    let scaleY = parent.height / container.offsetHeight;\n    if (self.top < parent.top)\n        container.scrollTop -= (parent.top - self.top) / scaleY;\n    else if (self.bottom > parent.bottom)\n        container.scrollTop += (self.bottom - parent.bottom) / scaleY;\n}\n\n// Used to pick a preferred option when two options with the same\n// label occur in the result.\nfunction score(option) {\n    return (option.boost || 0) * 100 + (option.apply ? 10 : 0) + (option.info ? 5 : 0) +\n        (option.type ? 1 : 0);\n}\nfunction sortOptions(active, state) {\n    let options = [];\n    let sections = null;\n    let addOption = (option) => {\n        options.push(option);\n        let { section } = option.completion;\n        if (section) {\n            if (!sections)\n                sections = [];\n            let name = typeof section == \"string\" ? section : section.name;\n            if (!sections.some(s => s.name == name))\n                sections.push(typeof section == \"string\" ? { name } : section);\n        }\n    };\n    let conf = state.facet(completionConfig);\n    for (let a of active)\n        if (a.hasResult()) {\n            let getMatch = a.result.getMatch;\n            if (a.result.filter === false) {\n                for (let option of a.result.options) {\n                    addOption(new Option(option, a.source, getMatch ? getMatch(option) : [], 1e9 - options.length));\n                }\n            }\n            else {\n                let pattern = state.sliceDoc(a.from, a.to), match;\n                let matcher = conf.filterStrict ? new StrictMatcher(pattern) : new FuzzyMatcher(pattern);\n                for (let option of a.result.options)\n                    if (match = matcher.match(option.label)) {\n                        let matched = !option.displayLabel ? match.matched : getMatch ? getMatch(option, match.matched) : [];\n                        addOption(new Option(option, a.source, matched, match.score + (option.boost || 0)));\n                    }\n            }\n        }\n    if (sections) {\n        let sectionOrder = Object.create(null), pos = 0;\n        let cmp = (a, b) => { var _a, _b; return ((_a = a.rank) !== null && _a !== void 0 ? _a : 1e9) - ((_b = b.rank) !== null && _b !== void 0 ? _b : 1e9) || (a.name < b.name ? -1 : 1); };\n        for (let s of sections.sort(cmp)) {\n            pos -= 1e5;\n            sectionOrder[s.name] = pos;\n        }\n        for (let option of options) {\n            let { section } = option.completion;\n            if (section)\n                option.score += sectionOrder[typeof section == \"string\" ? section : section.name];\n        }\n    }\n    let result = [], prev = null;\n    let compare = conf.compareCompletions;\n    for (let opt of options.sort((a, b) => (b.score - a.score) || compare(a.completion, b.completion))) {\n        let cur = opt.completion;\n        if (!prev || prev.label != cur.label || prev.detail != cur.detail ||\n            (prev.type != null && cur.type != null && prev.type != cur.type) ||\n            prev.apply != cur.apply || prev.boost != cur.boost)\n            result.push(opt);\n        else if (score(opt.completion) > score(prev))\n            result[result.length - 1] = opt;\n        prev = opt.completion;\n    }\n    return result;\n}\nclass CompletionDialog {\n    constructor(options, attrs, tooltip, timestamp, selected, disabled) {\n        this.options = options;\n        this.attrs = attrs;\n        this.tooltip = tooltip;\n        this.timestamp = timestamp;\n        this.selected = selected;\n        this.disabled = disabled;\n    }\n    setSelected(selected, id) {\n        return selected == this.selected || selected >= this.options.length ? this\n            : new CompletionDialog(this.options, makeAttrs(id, selected), this.tooltip, this.timestamp, selected, this.disabled);\n    }\n    static build(active, state, id, prev, conf) {\n        let options = sortOptions(active, state);\n        if (!options.length) {\n            return prev && active.some(a => a.state == 1 /* State.Pending */) ?\n                new CompletionDialog(prev.options, prev.attrs, prev.tooltip, prev.timestamp, prev.selected, true) : null;\n        }\n        let selected = state.facet(completionConfig).selectOnOpen ? 0 : -1;\n        if (prev && prev.selected != selected && prev.selected != -1) {\n            let selectedValue = prev.options[prev.selected].completion;\n            for (let i = 0; i < options.length; i++)\n                if (options[i].completion == selectedValue) {\n                    selected = i;\n                    break;\n                }\n        }\n        return new CompletionDialog(options, makeAttrs(id, selected), {\n            pos: active.reduce((a, b) => b.hasResult() ? Math.min(a, b.from) : a, 1e8),\n            create: createTooltip,\n            above: conf.aboveCursor,\n        }, prev ? prev.timestamp : Date.now(), selected, false);\n    }\n    map(changes) {\n        return new CompletionDialog(this.options, this.attrs, Object.assign(Object.assign({}, this.tooltip), { pos: changes.mapPos(this.tooltip.pos) }), this.timestamp, this.selected, this.disabled);\n    }\n}\nclass CompletionState {\n    constructor(active, id, open) {\n        this.active = active;\n        this.id = id;\n        this.open = open;\n    }\n    static start() {\n        return new CompletionState(none, \"cm-ac-\" + Math.floor(Math.random() * 2e6).toString(36), null);\n    }\n    update(tr) {\n        let { state } = tr, conf = state.facet(completionConfig);\n        let sources = conf.override ||\n            state.languageDataAt(\"autocomplete\", cur(state)).map(asSource);\n        let active = sources.map(source => {\n            let value = this.active.find(s => s.source == source) ||\n                new ActiveSource(source, this.active.some(a => a.state != 0 /* State.Inactive */) ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n            return value.update(tr, conf);\n        });\n        if (active.length == this.active.length && active.every((a, i) => a == this.active[i]))\n            active = this.active;\n        let open = this.open;\n        if (open && tr.docChanged)\n            open = open.map(tr.changes);\n        if (tr.selection || active.some(a => a.hasResult() && tr.changes.touchesRange(a.from, a.to)) ||\n            !sameResults(active, this.active))\n            open = CompletionDialog.build(active, state, this.id, open, conf);\n        else if (open && open.disabled && !active.some(a => a.state == 1 /* State.Pending */))\n            open = null;\n        if (!open && active.every(a => a.state != 1 /* State.Pending */) && active.some(a => a.hasResult()))\n            active = active.map(a => a.hasResult() ? new ActiveSource(a.source, 0 /* State.Inactive */) : a);\n        for (let effect of tr.effects)\n            if (effect.is(setSelectedEffect))\n                open = open && open.setSelected(effect.value, this.id);\n        return active == this.active && open == this.open ? this : new CompletionState(active, this.id, open);\n    }\n    get tooltip() { return this.open ? this.open.tooltip : null; }\n    get attrs() { return this.open ? this.open.attrs : baseAttrs; }\n}\nfunction sameResults(a, b) {\n    if (a == b)\n        return true;\n    for (let iA = 0, iB = 0;;) {\n        while (iA < a.length && !a[iA].hasResult)\n            iA++;\n        while (iB < b.length && !b[iB].hasResult)\n            iB++;\n        let endA = iA == a.length, endB = iB == b.length;\n        if (endA || endB)\n            return endA == endB;\n        if (a[iA++].result != b[iB++].result)\n            return false;\n    }\n}\nconst baseAttrs = {\n    \"aria-autocomplete\": \"list\"\n};\nfunction makeAttrs(id, selected) {\n    let result = {\n        \"aria-autocomplete\": \"list\",\n        \"aria-haspopup\": \"listbox\",\n        \"aria-controls\": id\n    };\n    if (selected > -1)\n        result[\"aria-activedescendant\"] = id + \"-\" + selected;\n    return result;\n}\nconst none = [];\nfunction getUserEvent(tr, conf) {\n    if (tr.isUserEvent(\"input.complete\")) {\n        let completion = tr.annotation(pickedCompletion);\n        if (completion && conf.activateOnCompletion(completion))\n            return \"input\";\n    }\n    return tr.isUserEvent(\"input.type\") ? \"input\" : tr.isUserEvent(\"delete.backward\") ? \"delete\" : null;\n}\nclass ActiveSource {\n    constructor(source, state, explicitPos = -1) {\n        this.source = source;\n        this.state = state;\n        this.explicitPos = explicitPos;\n    }\n    hasResult() { return false; }\n    update(tr, conf) {\n        let event = getUserEvent(tr, conf), value = this;\n        if (event)\n            value = value.handleUserEvent(tr, event, conf);\n        else if (tr.docChanged)\n            value = value.handleChange(tr);\n        else if (tr.selection && value.state != 0 /* State.Inactive */)\n            value = new ActiveSource(value.source, 0 /* State.Inactive */);\n        for (let effect of tr.effects) {\n            if (effect.is(startCompletionEffect))\n                value = new ActiveSource(value.source, 1 /* State.Pending */, effect.value ? cur(tr.state) : -1);\n            else if (effect.is(closeCompletionEffect))\n                value = new ActiveSource(value.source, 0 /* State.Inactive */);\n            else if (effect.is(setActiveEffect))\n                for (let active of effect.value)\n                    if (active.source == value.source)\n                        value = active;\n        }\n        return value;\n    }\n    handleUserEvent(tr, type, conf) {\n        return type == \"delete\" || !conf.activateOnTyping ? this.map(tr.changes) : new ActiveSource(this.source, 1 /* State.Pending */);\n    }\n    handleChange(tr) {\n        return tr.changes.touchesRange(cur(tr.startState)) ? new ActiveSource(this.source, 0 /* State.Inactive */) : this.map(tr.changes);\n    }\n    map(changes) {\n        return changes.empty || this.explicitPos < 0 ? this : new ActiveSource(this.source, this.state, changes.mapPos(this.explicitPos));\n    }\n}\nclass ActiveResult extends ActiveSource {\n    constructor(source, explicitPos, result, from, to) {\n        super(source, 2 /* State.Result */, explicitPos);\n        this.result = result;\n        this.from = from;\n        this.to = to;\n    }\n    hasResult() { return true; }\n    handleUserEvent(tr, type, conf) {\n        var _a;\n        let result = this.result;\n        if (result.map && !tr.changes.empty)\n            result = result.map(result, tr.changes);\n        let from = tr.changes.mapPos(this.from), to = tr.changes.mapPos(this.to, 1);\n        let pos = cur(tr.state);\n        if ((this.explicitPos < 0 ? pos <= from : pos < this.from) ||\n            pos > to || !result ||\n            type == \"delete\" && cur(tr.startState) == this.from)\n            return new ActiveSource(this.source, type == \"input\" && conf.activateOnTyping ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n        let explicitPos = this.explicitPos < 0 ? -1 : tr.changes.mapPos(this.explicitPos);\n        if (checkValid(result.validFor, tr.state, from, to))\n            return new ActiveResult(this.source, explicitPos, result, from, to);\n        if (result.update &&\n            (result = result.update(result, from, to, new CompletionContext(tr.state, pos, explicitPos >= 0))))\n            return new ActiveResult(this.source, explicitPos, result, result.from, (_a = result.to) !== null && _a !== void 0 ? _a : cur(tr.state));\n        return new ActiveSource(this.source, 1 /* State.Pending */, explicitPos);\n    }\n    handleChange(tr) {\n        return tr.changes.touchesRange(this.from, this.to) ? new ActiveSource(this.source, 0 /* State.Inactive */) : this.map(tr.changes);\n    }\n    map(mapping) {\n        if (mapping.empty)\n            return this;\n        let result = this.result.map ? this.result.map(this.result, mapping) : this.result;\n        if (!result)\n            return new ActiveSource(this.source, 0 /* State.Inactive */);\n        return new ActiveResult(this.source, this.explicitPos < 0 ? -1 : mapping.mapPos(this.explicitPos), this.result, mapping.mapPos(this.from), mapping.mapPos(this.to, 1));\n    }\n}\nfunction checkValid(validFor, state, from, to) {\n    if (!validFor)\n        return false;\n    let text = state.sliceDoc(from, to);\n    return typeof validFor == \"function\" ? validFor(text, from, to, state) : ensureAnchor(validFor, true).test(text);\n}\nconst setActiveEffect = /*@__PURE__*/StateEffect.define({\n    map(sources, mapping) { return sources.map(s => s.map(mapping)); }\n});\nconst setSelectedEffect = /*@__PURE__*/StateEffect.define();\nconst completionState = /*@__PURE__*/StateField.define({\n    create() { return CompletionState.start(); },\n    update(value, tr) { return value.update(tr); },\n    provide: f => [\n        showTooltip.from(f, val => val.tooltip),\n        EditorView.contentAttributes.from(f, state => state.attrs)\n    ]\n});\nfunction applyCompletion(view, option) {\n    const apply = option.completion.apply || option.completion.label;\n    let result = view.state.field(completionState).active.find(a => a.source == option.source);\n    if (!(result instanceof ActiveResult))\n        return false;\n    if (typeof apply == \"string\")\n        view.dispatch(Object.assign(Object.assign({}, insertCompletionText(view.state, apply, result.from, result.to)), { annotations: pickedCompletion.of(option.completion) }));\n    else\n        apply(view, option.completion, result.from, result.to);\n    return true;\n}\nconst createTooltip = /*@__PURE__*/completionTooltip(completionState, applyCompletion);\n\n/**\nReturns a command that moves the completion selection forward or\nbackward by the given amount.\n*/\nfunction moveCompletionSelection(forward, by = \"option\") {\n    return (view) => {\n        let cState = view.state.field(completionState, false);\n        if (!cState || !cState.open || cState.open.disabled ||\n            Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n            return false;\n        let step = 1, tooltip;\n        if (by == \"page\" && (tooltip = getTooltip(view, cState.open.tooltip)))\n            step = Math.max(2, Math.floor(tooltip.dom.offsetHeight /\n                tooltip.dom.querySelector(\"li\").offsetHeight) - 1);\n        let { length } = cState.open.options;\n        let selected = cState.open.selected > -1 ? cState.open.selected + step * (forward ? 1 : -1) : forward ? 0 : length - 1;\n        if (selected < 0)\n            selected = by == \"page\" ? 0 : length - 1;\n        else if (selected >= length)\n            selected = by == \"page\" ? length - 1 : 0;\n        view.dispatch({ effects: setSelectedEffect.of(selected) });\n        return true;\n    };\n}\n/**\nAccept the current completion.\n*/\nconst acceptCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (view.state.readOnly || !cState || !cState.open || cState.open.selected < 0 || cState.open.disabled ||\n        Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n        return false;\n    return applyCompletion(view, cState.open.options[cState.open.selected]);\n};\n/**\nExplicitly start autocompletion.\n*/\nconst startCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState)\n        return false;\n    view.dispatch({ effects: startCompletionEffect.of(true) });\n    return true;\n};\n/**\nClose the currently active completion.\n*/\nconst closeCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState || !cState.active.some(a => a.state != 0 /* State.Inactive */))\n        return false;\n    view.dispatch({ effects: closeCompletionEffect.of(null) });\n    return true;\n};\nclass RunningQuery {\n    constructor(active, context) {\n        this.active = active;\n        this.context = context;\n        this.time = Date.now();\n        this.updates = [];\n        // Note that 'undefined' means 'not done yet', whereas 'null' means\n        // 'query returned null'.\n        this.done = undefined;\n    }\n}\nconst MaxUpdateCount = 50, MinAbortTime = 1000;\nconst completionPlugin = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.debounceUpdate = -1;\n        this.running = [];\n        this.debounceAccept = -1;\n        this.pendingStart = false;\n        this.composing = 0 /* CompositionState.None */;\n        for (let active of view.state.field(completionState).active)\n            if (active.state == 1 /* State.Pending */)\n                this.startQuery(active);\n    }\n    update(update) {\n        let cState = update.state.field(completionState);\n        let conf = update.state.facet(completionConfig);\n        if (!update.selectionSet && !update.docChanged && update.startState.field(completionState) == cState)\n            return;\n        let doesReset = update.transactions.some(tr => {\n            return (tr.selection || tr.docChanged) && !getUserEvent(tr, conf);\n        });\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (doesReset ||\n                query.updates.length + update.transactions.length > MaxUpdateCount && Date.now() - query.time > MinAbortTime) {\n                for (let handler of query.context.abortListeners) {\n                    try {\n                        handler();\n                    }\n                    catch (e) {\n                        logException(this.view.state, e);\n                    }\n                }\n                query.context.abortListeners = null;\n                this.running.splice(i--, 1);\n            }\n            else {\n                query.updates.push(...update.transactions);\n            }\n        }\n        if (this.debounceUpdate > -1)\n            clearTimeout(this.debounceUpdate);\n        if (update.transactions.some(tr => tr.effects.some(e => e.is(startCompletionEffect))))\n            this.pendingStart = true;\n        let delay = this.pendingStart ? 50 : conf.activateOnTypingDelay;\n        this.debounceUpdate = cState.active.some(a => a.state == 1 /* State.Pending */ && !this.running.some(q => q.active.source == a.source))\n            ? setTimeout(() => this.startUpdate(), delay) : -1;\n        if (this.composing != 0 /* CompositionState.None */)\n            for (let tr of update.transactions) {\n                if (getUserEvent(tr, conf) == \"input\")\n                    this.composing = 2 /* CompositionState.Changed */;\n                else if (this.composing == 2 /* CompositionState.Changed */ && tr.selection)\n                    this.composing = 3 /* CompositionState.ChangedAndMoved */;\n            }\n    }\n    startUpdate() {\n        this.debounceUpdate = -1;\n        this.pendingStart = false;\n        let { state } = this.view, cState = state.field(completionState);\n        for (let active of cState.active) {\n            if (active.state == 1 /* State.Pending */ && !this.running.some(r => r.active.source == active.source))\n                this.startQuery(active);\n        }\n    }\n    startQuery(active) {\n        let { state } = this.view, pos = cur(state);\n        let context = new CompletionContext(state, pos, active.explicitPos == pos);\n        let pending = new RunningQuery(active, context);\n        this.running.push(pending);\n        Promise.resolve(active.source(context)).then(result => {\n            if (!pending.context.aborted) {\n                pending.done = result || null;\n                this.scheduleAccept();\n            }\n        }, err => {\n            this.view.dispatch({ effects: closeCompletionEffect.of(null) });\n            logException(this.view.state, err);\n        });\n    }\n    scheduleAccept() {\n        if (this.running.every(q => q.done !== undefined))\n            this.accept();\n        else if (this.debounceAccept < 0)\n            this.debounceAccept = setTimeout(() => this.accept(), this.view.state.facet(completionConfig).updateSyncTime);\n    }\n    // For each finished query in this.running, try to create a result\n    // or, if appropriate, restart the query.\n    accept() {\n        var _a;\n        if (this.debounceAccept > -1)\n            clearTimeout(this.debounceAccept);\n        this.debounceAccept = -1;\n        let updated = [];\n        let conf = this.view.state.facet(completionConfig);\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (query.done === undefined)\n                continue;\n            this.running.splice(i--, 1);\n            if (query.done) {\n                let active = new ActiveResult(query.active.source, query.active.explicitPos, query.done, query.done.from, (_a = query.done.to) !== null && _a !== void 0 ? _a : cur(query.updates.length ? query.updates[0].startState : this.view.state));\n                // Replay the transactions that happened since the start of\n                // the request and see if that preserves the result\n                for (let tr of query.updates)\n                    active = active.update(tr, conf);\n                if (active.hasResult()) {\n                    updated.push(active);\n                    continue;\n                }\n            }\n            let current = this.view.state.field(completionState).active.find(a => a.source == query.active.source);\n            if (current && current.state == 1 /* State.Pending */) {\n                if (query.done == null) {\n                    // Explicitly failed. Should clear the pending status if it\n                    // hasn't been re-set in the meantime.\n                    let active = new ActiveSource(query.active.source, 0 /* State.Inactive */);\n                    for (let tr of query.updates)\n                        active = active.update(tr, conf);\n                    if (active.state != 1 /* State.Pending */)\n                        updated.push(active);\n                }\n                else {\n                    // Cleared by subsequent transactions. Restart.\n                    this.startQuery(current);\n                }\n            }\n        }\n        if (updated.length)\n            this.view.dispatch({ effects: setActiveEffect.of(updated) });\n    }\n}, {\n    eventHandlers: {\n        blur(event) {\n            let state = this.view.state.field(completionState, false);\n            if (state && state.tooltip && this.view.state.facet(completionConfig).closeOnBlur) {\n                let dialog = state.open && getTooltip(this.view, state.open.tooltip);\n                if (!dialog || !dialog.dom.contains(event.relatedTarget))\n                    setTimeout(() => this.view.dispatch({ effects: closeCompletionEffect.of(null) }), 10);\n            }\n        },\n        compositionstart() {\n            this.composing = 1 /* CompositionState.Started */;\n        },\n        compositionend() {\n            if (this.composing == 3 /* CompositionState.ChangedAndMoved */) {\n                // Safari fires compositionend events synchronously, possibly\n                // from inside an update, so dispatch asynchronously to avoid reentrancy\n                setTimeout(() => this.view.dispatch({ effects: startCompletionEffect.of(false) }), 20);\n            }\n            this.composing = 0 /* CompositionState.None */;\n        }\n    }\n});\nconst windows = typeof navigator == \"object\" && /*@__PURE__*//Win/.test(navigator.platform);\nconst commitCharacters = /*@__PURE__*/Prec.highest(/*@__PURE__*/EditorView.domEventHandlers({\n    keydown(event, view) {\n        let field = view.state.field(completionState, false);\n        if (!field || !field.open || field.open.disabled || field.open.selected < 0 ||\n            event.key.length > 1 || event.ctrlKey && !(windows && event.altKey) || event.metaKey)\n            return false;\n        let option = field.open.options[field.open.selected];\n        let result = field.active.find(a => a.source == option.source);\n        let commitChars = option.completion.commitCharacters || result.result.commitCharacters;\n        if (commitChars && commitChars.indexOf(event.key) > -1)\n            applyCompletion(view, option);\n        return false;\n    }\n}));\n\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-tooltip.cm-tooltip-autocomplete\": {\n        \"& > ul\": {\n            fontFamily: \"monospace\",\n            whiteSpace: \"nowrap\",\n            overflow: \"hidden auto\",\n            maxWidth_fallback: \"700px\",\n            maxWidth: \"min(700px, 95vw)\",\n            minWidth: \"250px\",\n            maxHeight: \"10em\",\n            height: \"100%\",\n            listStyle: \"none\",\n            margin: 0,\n            padding: 0,\n            \"& > li, & > completion-section\": {\n                padding: \"1px 3px\",\n                lineHeight: 1.2\n            },\n            \"& > li\": {\n                overflowX: \"hidden\",\n                textOverflow: \"ellipsis\",\n                cursor: \"pointer\"\n            },\n            \"& > completion-section\": {\n                display: \"list-item\",\n                borderBottom: \"1px solid silver\",\n                paddingLeft: \"0.5em\",\n                opacity: 0.7\n            }\n        }\n    },\n    \"&light .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#17c\",\n        color: \"white\",\n    },\n    \"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#777\",\n    },\n    \"&dark .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#347\",\n        color: \"white\",\n    },\n    \"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#444\",\n    },\n    \".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after\": {\n        content: '\"···\"',\n        opacity: 0.5,\n        display: \"block\",\n        textAlign: \"center\"\n    },\n    \".cm-tooltip.cm-completionInfo\": {\n        position: \"absolute\",\n        padding: \"3px 9px\",\n        width: \"max-content\",\n        maxWidth: `${400 /* Info.Width */}px`,\n        boxSizing: \"border-box\"\n    },\n    \".cm-completionInfo.cm-completionInfo-left\": { right: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-right\": { left: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-left-narrow\": { right: `${30 /* Info.Margin */}px` },\n    \".cm-completionInfo.cm-completionInfo-right-narrow\": { left: `${30 /* Info.Margin */}px` },\n    \"&light .cm-snippetField\": { backgroundColor: \"#00000022\" },\n    \"&dark .cm-snippetField\": { backgroundColor: \"#ffffff22\" },\n    \".cm-snippetFieldPosition\": {\n        verticalAlign: \"text-top\",\n        width: 0,\n        height: \"1.15em\",\n        display: \"inline-block\",\n        margin: \"0 -0.7px -.7em\",\n        borderLeft: \"1.4px dotted #888\"\n    },\n    \".cm-completionMatchedText\": {\n        textDecoration: \"underline\"\n    },\n    \".cm-completionDetail\": {\n        marginLeft: \"0.5em\",\n        fontStyle: \"italic\"\n    },\n    \".cm-completionIcon\": {\n        fontSize: \"90%\",\n        width: \".8em\",\n        display: \"inline-block\",\n        textAlign: \"center\",\n        paddingRight: \".6em\",\n        opacity: \"0.6\",\n        boxSizing: \"content-box\"\n    },\n    \".cm-completionIcon-function, .cm-completionIcon-method\": {\n        \"&:after\": { content: \"'ƒ'\" }\n    },\n    \".cm-completionIcon-class\": {\n        \"&:after\": { content: \"'○'\" }\n    },\n    \".cm-completionIcon-interface\": {\n        \"&:after\": { content: \"'◌'\" }\n    },\n    \".cm-completionIcon-variable\": {\n        \"&:after\": { content: \"'𝑥'\" }\n    },\n    \".cm-completionIcon-constant\": {\n        \"&:after\": { content: \"'𝐶'\" }\n    },\n    \".cm-completionIcon-type\": {\n        \"&:after\": { content: \"'𝑡'\" }\n    },\n    \".cm-completionIcon-enum\": {\n        \"&:after\": { content: \"'∪'\" }\n    },\n    \".cm-completionIcon-property\": {\n        \"&:after\": { content: \"'□'\" }\n    },\n    \".cm-completionIcon-keyword\": {\n        \"&:after\": { content: \"'🔑\\uFE0E'\" } // Disable emoji rendering\n    },\n    \".cm-completionIcon-namespace\": {\n        \"&:after\": { content: \"'▢'\" }\n    },\n    \".cm-completionIcon-text\": {\n        \"&:after\": { content: \"'abc'\", fontSize: \"50%\", verticalAlign: \"middle\" }\n    }\n});\n\nclass FieldPos {\n    constructor(field, line, from, to) {\n        this.field = field;\n        this.line = line;\n        this.from = from;\n        this.to = to;\n    }\n}\nclass FieldRange {\n    constructor(field, from, to) {\n        this.field = field;\n        this.from = from;\n        this.to = to;\n    }\n    map(changes) {\n        let from = changes.mapPos(this.from, -1, MapMode.TrackDel);\n        let to = changes.mapPos(this.to, 1, MapMode.TrackDel);\n        return from == null || to == null ? null : new FieldRange(this.field, from, to);\n    }\n}\nclass Snippet {\n    constructor(lines, fieldPositions) {\n        this.lines = lines;\n        this.fieldPositions = fieldPositions;\n    }\n    instantiate(state, pos) {\n        let text = [], lineStart = [pos];\n        let lineObj = state.doc.lineAt(pos), baseIndent = /^\\s*/.exec(lineObj.text)[0];\n        for (let line of this.lines) {\n            if (text.length) {\n                let indent = baseIndent, tabs = /^\\t*/.exec(line)[0].length;\n                for (let i = 0; i < tabs; i++)\n                    indent += state.facet(indentUnit);\n                lineStart.push(pos + indent.length - tabs);\n                line = indent + line.slice(tabs);\n            }\n            text.push(line);\n            pos += line.length + 1;\n        }\n        let ranges = this.fieldPositions.map(pos => new FieldRange(pos.field, lineStart[pos.line] + pos.from, lineStart[pos.line] + pos.to));\n        return { text, ranges };\n    }\n    static parse(template) {\n        let fields = [];\n        let lines = [], positions = [], m;\n        for (let line of template.split(/\\r\\n?|\\n/)) {\n            while (m = /[#$]\\{(?:(\\d+)(?::([^}]*))?|([^}]*))\\}/.exec(line)) {\n                let seq = m[1] ? +m[1] : null, name = m[2] || m[3] || \"\", found = -1;\n                for (let i = 0; i < fields.length; i++) {\n                    if (seq != null ? fields[i].seq == seq : name ? fields[i].name == name : false)\n                        found = i;\n                }\n                if (found < 0) {\n                    let i = 0;\n                    while (i < fields.length && (seq == null || (fields[i].seq != null && fields[i].seq < seq)))\n                        i++;\n                    fields.splice(i, 0, { seq, name });\n                    found = i;\n                    for (let pos of positions)\n                        if (pos.field >= found)\n                            pos.field++;\n                }\n                positions.push(new FieldPos(found, lines.length, m.index, m.index + name.length));\n                line = line.slice(0, m.index) + name + line.slice(m.index + m[0].length);\n            }\n            for (let esc; esc = /\\\\([{}])/.exec(line);) {\n                line = line.slice(0, esc.index) + esc[1] + line.slice(esc.index + esc[0].length);\n                for (let pos of positions)\n                    if (pos.line == lines.length && pos.from > esc.index) {\n                        pos.from--;\n                        pos.to--;\n                    }\n            }\n            lines.push(line);\n        }\n        return new Snippet(lines, positions);\n    }\n}\nlet fieldMarker = /*@__PURE__*/Decoration.widget({ widget: /*@__PURE__*/new class extends WidgetType {\n        toDOM() {\n            let span = document.createElement(\"span\");\n            span.className = \"cm-snippetFieldPosition\";\n            return span;\n        }\n        ignoreEvent() { return false; }\n    } });\nlet fieldRange = /*@__PURE__*/Decoration.mark({ class: \"cm-snippetField\" });\nclass ActiveSnippet {\n    constructor(ranges, active) {\n        this.ranges = ranges;\n        this.active = active;\n        this.deco = Decoration.set(ranges.map(r => (r.from == r.to ? fieldMarker : fieldRange).range(r.from, r.to)));\n    }\n    map(changes) {\n        let ranges = [];\n        for (let r of this.ranges) {\n            let mapped = r.map(changes);\n            if (!mapped)\n                return null;\n            ranges.push(mapped);\n        }\n        return new ActiveSnippet(ranges, this.active);\n    }\n    selectionInsideField(sel) {\n        return sel.ranges.every(range => this.ranges.some(r => r.field == this.active && r.from <= range.from && r.to >= range.to));\n    }\n}\nconst setActive = /*@__PURE__*/StateEffect.define({\n    map(value, changes) { return value && value.map(changes); }\n});\nconst moveToField = /*@__PURE__*/StateEffect.define();\nconst snippetState = /*@__PURE__*/StateField.define({\n    create() { return null; },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setActive))\n                return effect.value;\n            if (effect.is(moveToField) && value)\n                return new ActiveSnippet(value.ranges, effect.value);\n        }\n        if (value && tr.docChanged)\n            value = value.map(tr.changes);\n        if (value && tr.selection && !value.selectionInsideField(tr.selection))\n            value = null;\n        return value;\n    },\n    provide: f => EditorView.decorations.from(f, val => val ? val.deco : Decoration.none)\n});\nfunction fieldSelection(ranges, field) {\n    return EditorSelection.create(ranges.filter(r => r.field == field).map(r => EditorSelection.range(r.from, r.to)));\n}\n/**\nConvert a snippet template to a function that can\n[apply](https://codemirror.net/6/docs/ref/#autocomplete.Completion.apply) it. Snippets are written\nusing syntax like this:\n\n    \"for (let ${index} = 0; ${index} < ${end}; ${index}++) {\\n\\t${}\\n}\"\n\nEach `${}` placeholder (you may also use `#{}`) indicates a field\nthat the user can fill in. Its name, if any, will be the default\ncontent for the field.\n\nWhen the snippet is activated by calling the returned function,\nthe code is inserted at the given position. Newlines in the\ntemplate are indented by the indentation of the start line, plus\none [indent unit](https://codemirror.net/6/docs/ref/#language.indentUnit) per tab character after\nthe newline.\n\nOn activation, (all instances of) the first field are selected.\nThe user can move between fields with Tab and Shift-Tab as long as\nthe fields are active. Moving to the last field or moving the\ncursor out of the current field deactivates the fields.\n\nThe order of fields defaults to textual order, but you can add\nnumbers to placeholders (`${1}` or `${1:defaultText}`) to provide\na custom order.\n\nTo include a literal `{` or `}` in your template, put a backslash\nin front of it. This will be removed and the brace will not be\ninterpreted as indicating a placeholder.\n*/\nfunction snippet(template) {\n    let snippet = Snippet.parse(template);\n    return (editor, completion, from, to) => {\n        let { text, ranges } = snippet.instantiate(editor.state, from);\n        let spec = {\n            changes: { from, to, insert: Text.of(text) },\n            scrollIntoView: true,\n            annotations: completion ? [pickedCompletion.of(completion), Transaction.userEvent.of(\"input.complete\")] : undefined\n        };\n        if (ranges.length)\n            spec.selection = fieldSelection(ranges, 0);\n        if (ranges.some(r => r.field > 0)) {\n            let active = new ActiveSnippet(ranges, 0);\n            let effects = spec.effects = [setActive.of(active)];\n            if (editor.state.field(snippetState, false) === undefined)\n                effects.push(StateEffect.appendConfig.of([snippetState, addSnippetKeymap, snippetPointerHandler, baseTheme]));\n        }\n        editor.dispatch(editor.state.update(spec));\n    };\n}\nfunction moveField(dir) {\n    return ({ state, dispatch }) => {\n        let active = state.field(snippetState, false);\n        if (!active || dir < 0 && active.active == 0)\n            return false;\n        let next = active.active + dir, last = dir > 0 && !active.ranges.some(r => r.field == next + dir);\n        dispatch(state.update({\n            selection: fieldSelection(active.ranges, next),\n            effects: setActive.of(last ? null : new ActiveSnippet(active.ranges, next)),\n            scrollIntoView: true\n        }));\n        return true;\n    };\n}\n/**\nA command that clears the active snippet, if any.\n*/\nconst clearSnippet = ({ state, dispatch }) => {\n    let active = state.field(snippetState, false);\n    if (!active)\n        return false;\n    dispatch(state.update({ effects: setActive.of(null) }));\n    return true;\n};\n/**\nMove to the next snippet field, if available.\n*/\nconst nextSnippetField = /*@__PURE__*/moveField(1);\n/**\nMove to the previous snippet field, if available.\n*/\nconst prevSnippetField = /*@__PURE__*/moveField(-1);\n/**\nCheck if there is an active snippet with a next field for\n`nextSnippetField` to move to.\n*/\nfunction hasNextSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.ranges.some(r => r.field == active.active + 1));\n}\n/**\nReturns true if there is an active snippet and a previous field\nfor `prevSnippetField` to move to.\n*/\nfunction hasPrevSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.active > 0);\n}\nconst defaultSnippetKeymap = [\n    { key: \"Tab\", run: nextSnippetField, shift: prevSnippetField },\n    { key: \"Escape\", run: clearSnippet }\n];\n/**\nA facet that can be used to configure the key bindings used by\nsnippets. The default binds Tab to\n[`nextSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.nextSnippetField), Shift-Tab to\n[`prevSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.prevSnippetField), and Escape\nto [`clearSnippet`](https://codemirror.net/6/docs/ref/#autocomplete.clearSnippet).\n*/\nconst snippetKeymap = /*@__PURE__*/Facet.define({\n    combine(maps) { return maps.length ? maps[0] : defaultSnippetKeymap; }\n});\nconst addSnippetKeymap = /*@__PURE__*/Prec.highest(/*@__PURE__*/keymap.compute([snippetKeymap], state => state.facet(snippetKeymap)));\n/**\nCreate a completion from a snippet. Returns an object with the\nproperties from `completion`, plus an `apply` function that\napplies the snippet.\n*/\nfunction snippetCompletion(template, completion) {\n    return Object.assign(Object.assign({}, completion), { apply: snippet(template) });\n}\nconst snippetPointerHandler = /*@__PURE__*/EditorView.domEventHandlers({\n    mousedown(event, view) {\n        let active = view.state.field(snippetState, false), pos;\n        if (!active || (pos = view.posAtCoords({ x: event.clientX, y: event.clientY })) == null)\n            return false;\n        let match = active.ranges.find(r => r.from <= pos && r.to >= pos);\n        if (!match || match.field == active.active)\n            return false;\n        view.dispatch({\n            selection: fieldSelection(active.ranges, match.field),\n            effects: setActive.of(active.ranges.some(r => r.field > match.field)\n                ? new ActiveSnippet(active.ranges, match.field) : null),\n            scrollIntoView: true\n        });\n        return true;\n    }\n});\n\nfunction wordRE(wordChars) {\n    let escaped = wordChars.replace(/[\\]\\-\\\\]/g, \"\\\\$&\");\n    try {\n        return new RegExp(`[\\\\p{Alphabetic}\\\\p{Number}_${escaped}]+`, \"ug\");\n    }\n    catch (_a) {\n        return new RegExp(`[\\w${escaped}]`, \"g\");\n    }\n}\nfunction mapRE(re, f) {\n    return new RegExp(f(re.source), re.unicode ? \"u\" : \"\");\n}\nconst wordCaches = /*@__PURE__*/Object.create(null);\nfunction wordCache(wordChars) {\n    return wordCaches[wordChars] || (wordCaches[wordChars] = new WeakMap);\n}\nfunction storeWords(doc, wordRE, result, seen, ignoreAt) {\n    for (let lines = doc.iterLines(), pos = 0; !lines.next().done;) {\n        let { value } = lines, m;\n        wordRE.lastIndex = 0;\n        while (m = wordRE.exec(value)) {\n            if (!seen[m[0]] && pos + m.index != ignoreAt) {\n                result.push({ type: \"text\", label: m[0] });\n                seen[m[0]] = true;\n                if (result.length >= 2000 /* C.MaxList */)\n                    return;\n            }\n        }\n        pos += value.length + 1;\n    }\n}\nfunction collectWords(doc, cache, wordRE, to, ignoreAt) {\n    let big = doc.length >= 1000 /* C.MinCacheLen */;\n    let cached = big && cache.get(doc);\n    if (cached)\n        return cached;\n    let result = [], seen = Object.create(null);\n    if (doc.children) {\n        let pos = 0;\n        for (let ch of doc.children) {\n            if (ch.length >= 1000 /* C.MinCacheLen */) {\n                for (let c of collectWords(ch, cache, wordRE, to - pos, ignoreAt - pos)) {\n                    if (!seen[c.label]) {\n                        seen[c.label] = true;\n                        result.push(c);\n                    }\n                }\n            }\n            else {\n                storeWords(ch, wordRE, result, seen, ignoreAt - pos);\n            }\n            pos += ch.length + 1;\n        }\n    }\n    else {\n        storeWords(doc, wordRE, result, seen, ignoreAt);\n    }\n    if (big && result.length < 2000 /* C.MaxList */)\n        cache.set(doc, result);\n    return result;\n}\n/**\nA completion source that will scan the document for words (using a\n[character categorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer)), and\nreturn those as completions.\n*/\nconst completeAnyWord = context => {\n    let wordChars = context.state.languageDataAt(\"wordChars\", context.pos).join(\"\");\n    let re = wordRE(wordChars);\n    let token = context.matchBefore(mapRE(re, s => s + \"$\"));\n    if (!token && !context.explicit)\n        return null;\n    let from = token ? token.from : context.pos;\n    let options = collectWords(context.state.doc, wordCache(wordChars), re, 50000 /* C.Range */, from);\n    return { from, options, validFor: mapRE(re, s => \"^\" + s) };\n};\n\nconst defaults = {\n    brackets: [\"(\", \"[\", \"{\", \"'\", '\"'],\n    before: \")]}:;>\",\n    stringPrefixes: []\n};\nconst closeBracketEffect = /*@__PURE__*/StateEffect.define({\n    map(value, mapping) {\n        let mapped = mapping.mapPos(value, -1, MapMode.TrackAfter);\n        return mapped == null ? undefined : mapped;\n    }\n});\nconst closedBracket = /*@__PURE__*/new class extends RangeValue {\n};\nclosedBracket.startSide = 1;\nclosedBracket.endSide = -1;\nconst bracketState = /*@__PURE__*/StateField.define({\n    create() { return RangeSet.empty; },\n    update(value, tr) {\n        value = value.map(tr.changes);\n        if (tr.selection) {\n            let line = tr.state.doc.lineAt(tr.selection.main.head);\n            value = value.update({ filter: from => from >= line.from && from <= line.to });\n        }\n        for (let effect of tr.effects)\n            if (effect.is(closeBracketEffect))\n                value = value.update({ add: [closedBracket.range(effect.value, effect.value + 1)] });\n        return value;\n    }\n});\n/**\nExtension to enable bracket-closing behavior. When a closeable\nbracket is typed, its closing bracket is immediately inserted\nafter the cursor. When closing a bracket directly in front of a\nclosing bracket inserted by the extension, the cursor moves over\nthat bracket.\n*/\nfunction closeBrackets() {\n    return [inputHandler, bracketState];\n}\nconst definedClosing = \"()[]{}<>\";\nfunction closing(ch) {\n    for (let i = 0; i < definedClosing.length; i += 2)\n        if (definedClosing.charCodeAt(i) == ch)\n            return definedClosing.charAt(i + 1);\n    return fromCodePoint(ch < 128 ? ch : ch + 1);\n}\nfunction config(state, pos) {\n    return state.languageDataAt(\"closeBrackets\", pos)[0] || defaults;\n}\nconst android = typeof navigator == \"object\" && /*@__PURE__*//Android\\b/.test(navigator.userAgent);\nconst inputHandler = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, insert) => {\n    if ((android ? view.composing : view.compositionStarted) || view.state.readOnly)\n        return false;\n    let sel = view.state.selection.main;\n    if (insert.length > 2 || insert.length == 2 && codePointSize(codePointAt(insert, 0)) == 1 ||\n        from != sel.from || to != sel.to)\n        return false;\n    let tr = insertBracket(view.state, insert);\n    if (!tr)\n        return false;\n    view.dispatch(tr);\n    return true;\n});\n/**\nCommand that implements deleting a pair of matching brackets when\nthe cursor is between them.\n*/\nconst deleteBracketPair = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty) {\n            let before = prevChar(state.doc, range.head);\n            for (let token of tokens) {\n                if (token == before && nextChar(state.doc, range.head) == closing(codePointAt(token, 0)))\n                    return { changes: { from: range.head - token.length, to: range.head + token.length },\n                        range: EditorSelection.cursor(range.head - token.length) };\n            }\n        }\n        return { range: dont = range };\n    });\n    if (!dont)\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete.backward\" }));\n    return !dont;\n};\n/**\nClose-brackets related key bindings. Binds Backspace to\n[`deleteBracketPair`](https://codemirror.net/6/docs/ref/#autocomplete.deleteBracketPair).\n*/\nconst closeBracketsKeymap = [\n    { key: \"Backspace\", run: deleteBracketPair }\n];\n/**\nImplements the extension's behavior on text insertion. If the\ngiven string counts as a bracket in the language around the\nselection, and replacing the selection with it requires custom\nbehavior (inserting a closing version or skipping past a\npreviously-closed bracket), this function returns a transaction\nrepresenting that custom behavior. (You only need this if you want\nto programmatically insert brackets—the\n[`closeBrackets`](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets) extension will\ntake care of running this for user input.)\n*/\nfunction insertBracket(state, bracket) {\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    for (let tok of tokens) {\n        let closed = closing(codePointAt(tok, 0));\n        if (bracket == tok)\n            return closed == tok ? handleSame(state, tok, tokens.indexOf(tok + tok + tok) > -1, conf)\n                : handleOpen(state, tok, closed, conf.before || defaults.before);\n        if (bracket == closed && closedBracketAt(state, state.selection.main.from))\n            return handleClose(state, tok, closed);\n    }\n    return null;\n}\nfunction closedBracketAt(state, pos) {\n    let found = false;\n    state.field(bracketState).between(0, state.doc.length, from => {\n        if (from == pos)\n            found = true;\n    });\n    return found;\n}\nfunction nextChar(doc, pos) {\n    let next = doc.sliceString(pos, pos + 2);\n    return next.slice(0, codePointSize(codePointAt(next, 0)));\n}\nfunction prevChar(doc, pos) {\n    let prev = doc.sliceString(pos - 2, pos);\n    return codePointSize(codePointAt(prev, 0)) == prev.length ? prev : prev.slice(1);\n}\nfunction handleOpen(state, open, close, closeBefore) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: open, from: range.from }, { insert: close, from: range.to }],\n                effects: closeBracketEffect.of(range.to + open.length),\n                range: EditorSelection.range(range.anchor + open.length, range.head + open.length) };\n        let next = nextChar(state.doc, range.head);\n        if (!next || /\\s/.test(next) || closeBefore.indexOf(next) > -1)\n            return { changes: { insert: open + close, from: range.head },\n                effects: closeBracketEffect.of(range.head + open.length),\n                range: EditorSelection.cursor(range.head + open.length) };\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction handleClose(state, _open, close) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty && nextChar(state.doc, range.head) == close)\n            return { changes: { from: range.head, to: range.head + close.length, insert: close },\n                range: EditorSelection.cursor(range.head + close.length) };\n        return dont = { range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\n// Handles cases where the open and close token are the same, and\n// possibly triple quotes (as in `\"\"\"abc\"\"\"`-style quoting).\nfunction handleSame(state, token, allowTriple, config) {\n    let stringPrefixes = config.stringPrefixes || defaults.stringPrefixes;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: token, from: range.from }, { insert: token, from: range.to }],\n                effects: closeBracketEffect.of(range.to + token.length),\n                range: EditorSelection.range(range.anchor + token.length, range.head + token.length) };\n        let pos = range.head, next = nextChar(state.doc, pos), start;\n        if (next == token) {\n            if (nodeStart(state, pos)) {\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: EditorSelection.cursor(pos + token.length) };\n            }\n            else if (closedBracketAt(state, pos)) {\n                let isTriple = allowTriple && state.sliceDoc(pos, pos + token.length * 3) == token + token + token;\n                let content = isTriple ? token + token + token : token;\n                return { changes: { from: pos, to: pos + content.length, insert: content },\n                    range: EditorSelection.cursor(pos + content.length) };\n            }\n        }\n        else if (allowTriple && state.sliceDoc(pos - 2 * token.length, pos) == token + token &&\n            (start = canStartStringAt(state, pos - 2 * token.length, stringPrefixes)) > -1 &&\n            nodeStart(state, start)) {\n            return { changes: { insert: token + token + token + token, from: pos },\n                effects: closeBracketEffect.of(pos + token.length),\n                range: EditorSelection.cursor(pos + token.length) };\n        }\n        else if (state.charCategorizer(pos)(next) != CharCategory.Word) {\n            if (canStartStringAt(state, pos, stringPrefixes) > -1 && !probablyInString(state, pos, token, stringPrefixes))\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: EditorSelection.cursor(pos + token.length) };\n        }\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction nodeStart(state, pos) {\n    let tree = syntaxTree(state).resolveInner(pos + 1);\n    return tree.parent && tree.from == pos;\n}\nfunction probablyInString(state, pos, quoteToken, prefixes) {\n    let node = syntaxTree(state).resolveInner(pos, -1);\n    let maxPrefix = prefixes.reduce((m, p) => Math.max(m, p.length), 0);\n    for (let i = 0; i < 5; i++) {\n        let start = state.sliceDoc(node.from, Math.min(node.to, node.from + quoteToken.length + maxPrefix));\n        let quotePos = start.indexOf(quoteToken);\n        if (!quotePos || quotePos > -1 && prefixes.indexOf(start.slice(0, quotePos)) > -1) {\n            let first = node.firstChild;\n            while (first && first.from == node.from && first.to - first.from > quoteToken.length + quotePos) {\n                if (state.sliceDoc(first.to - quoteToken.length, first.to) == quoteToken)\n                    return false;\n                first = first.firstChild;\n            }\n            return true;\n        }\n        let parent = node.to == pos && node.parent;\n        if (!parent)\n            break;\n        node = parent;\n    }\n    return false;\n}\nfunction canStartStringAt(state, pos, prefixes) {\n    let charCat = state.charCategorizer(pos);\n    if (charCat(state.sliceDoc(pos - 1, pos)) != CharCategory.Word)\n        return pos;\n    for (let prefix of prefixes) {\n        let start = pos - prefix.length;\n        if (state.sliceDoc(start, pos) == prefix && charCat(state.sliceDoc(start - 1, start)) != CharCategory.Word)\n            return start;\n    }\n    return -1;\n}\n\n/**\nReturns an extension that enables autocompletion.\n*/\nfunction autocompletion(config = {}) {\n    return [\n        commitCharacters,\n        completionState,\n        completionConfig.of(config),\n        completionPlugin,\n        completionKeymapExt,\n        baseTheme\n    ];\n}\n/**\nBasic keybindings for autocompletion.\n\n - Ctrl-Space: [`startCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.startCompletion)\n - Escape: [`closeCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.closeCompletion)\n - ArrowDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true)`\n - ArrowUp: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(false)`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - Enter: [`acceptCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.acceptCompletion)\n*/\nconst completionKeymap = [\n    { key: \"Ctrl-Space\", run: startCompletion },\n    { key: \"Escape\", run: closeCompletion },\n    { key: \"ArrowDown\", run: /*@__PURE__*/moveCompletionSelection(true) },\n    { key: \"ArrowUp\", run: /*@__PURE__*/moveCompletionSelection(false) },\n    { key: \"PageDown\", run: /*@__PURE__*/moveCompletionSelection(true, \"page\") },\n    { key: \"PageUp\", run: /*@__PURE__*/moveCompletionSelection(false, \"page\") },\n    { key: \"Enter\", run: acceptCompletion }\n];\nconst completionKeymapExt = /*@__PURE__*/Prec.highest(/*@__PURE__*/keymap.computeN([completionConfig], state => state.facet(completionConfig).defaultKeymap ? [completionKeymap] : []));\n/**\nGet the current completion status. When completions are available,\nthis will return `\"active\"`. When completions are pending (in the\nprocess of being queried), this returns `\"pending\"`. Otherwise, it\nreturns `null`.\n*/\nfunction completionStatus(state) {\n    let cState = state.field(completionState, false);\n    return cState && cState.active.some(a => a.state == 1 /* State.Pending */) ? \"pending\"\n        : cState && cState.active.some(a => a.state != 0 /* State.Inactive */) ? \"active\" : null;\n}\nconst completionArrayCache = /*@__PURE__*/new WeakMap;\n/**\nReturns the available completions as an array.\n*/\nfunction currentCompletions(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    if (!open || open.disabled)\n        return [];\n    let completions = completionArrayCache.get(open.options);\n    if (!completions)\n        completionArrayCache.set(open.options, completions = open.options.map(o => o.completion));\n    return completions;\n}\n/**\nReturn the currently selected completion, if any.\n*/\nfunction selectedCompletion(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.options[open.selected].completion : null;\n}\n/**\nReturns the currently selected position in the active completion\nlist, or null if no completions are active.\n*/\nfunction selectedCompletionIndex(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.selected : null;\n}\n/**\nCreate an effect that can be attached to a transaction to change\nthe currently selected completion.\n*/\nfunction setSelectedCompletion(index) {\n    return setSelectedEffect.of(index);\n}\n\nexport { CompletionContext, acceptCompletion, autocompletion, clearSnippet, closeBrackets, closeBracketsKeymap, closeCompletion, completeAnyWord, completeFromList, completionKeymap, completionStatus, currentCompletions, deleteBracketPair, hasNextSnippetField, hasPrevSnippetField, ifIn, ifNotIn, insertBracket, insertCompletionText, moveCompletionSelection, nextSnippetField, pickedCompletion, prevSnippetField, selectedCompletion, selectedCompletionIndex, setSelectedCompletion, snippet, snippetCompletion, snippetKeymap, startCompletion };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,oBAAN,MAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,YAIA,OAIA,KAOA,UAAU;AACN,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAIhB,SAAK,iBAAiB,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACf,QAAI,QAAQ,WAAW,KAAK,KAAK,EAAE,aAAa,KAAK,KAAK,EAAE;AAC5D,WAAO,SAAS,MAAM,QAAQ,MAAM,IAAI,IAAI;AACxC,cAAQ,MAAM;AAClB,WAAO,QAAQ;AAAA,MAAE,MAAM,MAAM;AAAA,MAAM,IAAI,KAAK;AAAA,MACxC,MAAM,KAAK,MAAM,SAAS,MAAM,MAAM,KAAK,GAAG;AAAA,MAC9C,MAAM,MAAM;AAAA,IAAK,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM;AACd,QAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG;AACzC,QAAI,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAC9C,QAAI,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACjE,QAAI,QAAQ,IAAI,OAAO,aAAa,MAAM,KAAK,CAAC;AAChD,WAAO,QAAQ,IAAI,OAAO,EAAE,MAAM,QAAQ,OAAO,IAAI,KAAK,KAAK,MAAM,IAAI,MAAM,KAAK,EAAE;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AAAE,WAAO,KAAK,kBAAkB;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpD,iBAAiB,MAAM,UAAU;AAC7B,QAAI,QAAQ,WAAW,KAAK;AACxB,WAAK,eAAe,KAAK,QAAQ;AAAA,EACzC;AACJ;AACA,SAAS,MAAM,OAAO;AAClB,MAAI,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,EAAE;AACrC,MAAI,QAAQ,KAAK,KAAK,IAAI;AAC1B,MAAI;AACA,WAAO,KAAK,QAAQ,OAAO,EAAE;AACjC,SAAO,IAAI,QAAQ,QAAQ,EAAE,GAAG,KAAK,QAAQ,YAAY,MAAM,CAAC;AACpE;AACA,SAAS,YAAY,SAAS;AAC1B,MAAI,QAAQ,uBAAO,OAAO,IAAI,GAAG,OAAO,uBAAO,OAAO,IAAI;AAC1D,WAAS,EAAE,MAAM,KAAK,SAAS;AAC3B,UAAM,MAAM,CAAC,CAAC,IAAI;AAClB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,WAAK,MAAM,CAAC,CAAC,IAAI;AAAA,EACzB;AACA,MAAI,SAAS,MAAM,KAAK,IAAI,MAAM,IAAI,IAAI;AAC1C,SAAO,CAAC,IAAI,OAAO,MAAM,MAAM,GAAG,IAAI,OAAO,MAAM,CAAC;AACxD;AAKA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,UAAU,KAAK,IAAI,OAAK,OAAO,KAAK,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC;AACnE,MAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,MAAM,OAAK,QAAQ,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,MAAM,IAAI,YAAY,OAAO;AAC1G,SAAO,CAAC,YAAY;AAChB,QAAI,QAAQ,QAAQ,YAAY,KAAK;AACrC,WAAO,SAAS,QAAQ,WAAW,EAAE,MAAM,QAAQ,MAAM,OAAO,QAAQ,KAAK,SAAS,SAAS,IAAI;AAAA,EACvG;AACJ;AAoBA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,SAAO,CAAC,YAAY;AAChB,aAAS,MAAM,WAAW,QAAQ,KAAK,EAAE,aAAa,QAAQ,KAAK,EAAE,GAAG,KAAK,MAAM,IAAI,QAAQ;AAC3F,UAAI,MAAM,QAAQ,IAAI,IAAI,IAAI;AAC1B,eAAO;AACX,UAAI,IAAI,KAAK;AACT;AAAA,IACR;AACA,WAAO,OAAO,OAAO;AAAA,EACzB;AACJ;AACA,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,YAAY,QAAQ,OAAOA,QAAO;AAC1C,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQA;AAAA,EACjB;AACJ;AACA,SAAS,IAAI,OAAO;AAAE,SAAO,MAAM,UAAU,KAAK;AAAM;AAGxD,SAAS,aAAa,MAAM,OAAO;AAC/B,MAAI;AACJ,MAAI,EAAE,OAAO,IAAI;AACjB,MAAI,WAAW,SAAS,OAAO,CAAC,KAAK,KAAK,SAAS,OAAO,OAAO,SAAS,CAAC,KAAK;AAChF,MAAI,CAAC,YAAY,CAAC;AACd,WAAO;AACX,SAAO,IAAI,OAAO,GAAG,WAAW,MAAM,EAAE,MAAM,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAM,KAAK,aAAa,MAAM,EAAG;AAChK;AAKA,IAAM,mBAAgC,WAAW,OAAO;AAMxD,SAAS,qBAAqB,OAAO,MAAM,MAAM,IAAI;AACjD,MAAI,EAAE,KAAK,IAAI,MAAM,WAAW,UAAU,OAAO,KAAK,MAAM,QAAQ,KAAK,KAAK;AAC9E,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,cAAc,WAAS;AAChE,QAAI,SAAS,QAAQ,QAAQ,MACzB,MAAM,SAAS,MAAM,OAAO,SAAS,MAAM,OAAO,KAAK,KAAK,MAAM,SAAS,MAAM,EAAE;AACnF,aAAO,EAAE,MAAM;AACnB,WAAO;AAAA,MACH,SAAS,EAAE,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,KAAK,OAAO,MAAM,KAAK,MAAM,OAAO,OAAO,QAAQ,KAAK;AAAA,MACzG,OAAO,gBAAgB,OAAO,MAAM,OAAO,UAAU,KAAK,MAAM;AAAA,IACpE;AAAA,EACJ,CAAC,CAAC,GAAG,EAAE,gBAAgB,MAAM,WAAW,iBAAiB,CAAC;AAC9D;AACA,IAAM,cAA2B,oBAAI,QAAQ;AAC7C,SAAS,SAAS,QAAQ;AACtB,MAAI,CAAC,MAAM,QAAQ,MAAM;AACrB,WAAO;AACX,MAAI,QAAQ,YAAY,IAAI,MAAM;AAClC,MAAI,CAAC;AACD,gBAAY,IAAI,QAAQ,QAAQ,iBAAiB,MAAM,CAAC;AAC5D,SAAO;AACX;AACA,IAAM,wBAAqC,YAAY,OAAO;AAC9D,IAAM,wBAAqC,YAAY,OAAO;AAK9D,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,SAAS;AACjB,SAAK,UAAU;AACf,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS,CAAC;AAGf,SAAK,MAAM,CAAC;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,QAAQ;AACb,SAAK,UAAU,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,UAAS;AACjC,UAAI,OAAO,YAAY,SAAS,CAAC,GAAG,OAAO,cAAc,IAAI;AAC7D,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAQ,MAAM,GAAG,IAAI,IAAI,GAAG,QAAQ,KAAK,YAAY;AAChE,WAAK,OAAO,KAAK,YAAY,SAAS,OAAO,KAAK,YAAY,IAAI,OAAO,CAAC,CAAC;AAC3E,WAAK;AAAA,IACT;AACA,SAAK,SAAS,QAAQ,UAAU,KAAK,MAAM;AAAA,EAC/C;AAAA,EACA,IAAIA,QAAO,SAAS;AAChB,SAAK,QAAQA;AACb,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM;AACR,QAAI,KAAK,QAAQ,UAAU;AACvB,aAAO,KAAK,IAAI,MAA4B,CAAC,CAAC;AAClD,QAAI,KAAK,SAAS,KAAK,QAAQ;AAC3B,aAAO;AACX,QAAI,EAAE,OAAO,QAAQ,KAAK,SAAS,OAAO,IAAI;AAG9C,QAAI,MAAM,UAAU,GAAG;AACnB,UAAI,QAAQ,YAAY,MAAM,CAAC,GAAG,YAAY,cAAc,KAAK;AACjE,UAAIA,SAAQ,aAAa,KAAK,SAAS,IAAI;AAC3C,UAAI,SAAS,MAAM,CAAC;AAAG;AAAA,eACd,SAAS,OAAO,CAAC;AACtB,QAAAA,UAAS;AAAA;AAET,eAAO;AACX,aAAO,KAAK,IAAIA,QAAO,CAAC,GAAG,SAAS,CAAC;AAAA,IACzC;AACA,QAAI,SAAS,KAAK,QAAQ,KAAK,OAAO;AACtC,QAAI,UAAU;AACV,aAAO,KAAK,IAAI,KAAK,UAAU,KAAK,QAAQ,SAAS,IAAI,MAA4B,CAAC,GAAG,KAAK,QAAQ,MAAM,CAAC;AACjH,QAAI,MAAM,MAAM,QAAQ,QAAQ;AAChC,QAAI,SAAS,GAAG;AACZ,eAAS,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,QAAQ,GAAG,GAAG,IAAI,KAAK,QAAQ,OAAM;AACnE,YAAI,OAAO,YAAY,MAAM,CAAC;AAC9B,YAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,OAAO,KAAK;AAC5C,cAAI,OAAO,IAAI;AACnB,aAAK,cAAc,IAAI;AAAA,MAC3B;AAEA,UAAI,QAAQ;AACR,eAAO;AAAA,IACf;AAGA,QAAI,YAAY;AAIhB,QAAI,WAAW,GAAG,eAAe;AAEjC,QAAI,aAAa,GAAG,gBAAgB,IAAI,cAAc;AACtD,QAAI,WAAW,QAAQ,KAAK,IAAI,GAAG,eAAe;AAElD,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,QAAQ,GAAG,GAAG,WAAW,GAAoB,IAAI,KAAK,WAAW,OAAM;AACrG,UAAI,OAAO,YAAY,MAAM,CAAC;AAC9B,UAAI,SAAS,GAAG;AACZ,YAAI,YAAY,OAAO,QAAQ,MAAM,SAAS;AAC1C,kBAAQ,WAAW,IAAI;AAC3B,YAAI,aAAa,KAAK;AAClB,cAAI,QAAQ,MAAM,UAAU,KAAK,QAAQ,OAAO,UAAU,GAAG;AACzD,gBAAI,cAAc;AACd,8BAAgB;AACpB,0BAAc,IAAI;AAClB;AAAA,UACJ,OACK;AACD,yBAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,IAAI,OAAO,OAAO,MACf,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,IAAmB,QAAQ,MAAM,QAAQ,KAAK,IAAmB,KACxH,KAAK,cAAc,IAAI,MAAM,GAAG,YAAY,IAAI,IAAmB,MAAM,GAAG,YAAY,IAAI,IAAmB;AACvH,UAAI,CAAC,KAAK,QAAQ,KAAoB,YAAY,YAAY,KAAsB,QAAQ,GAAoB;AAC5G,YAAI,MAAM,QAAQ,KAAK,QAAS,OAAO,QAAQ,KAAK,SAAS,eAAe;AACxE,iBAAO,UAAU,IAAI;AAAA,iBAChB,OAAO;AACZ,yBAAe;AAAA,MACvB;AACA,iBAAW;AACX,WAAK,cAAc,IAAI;AAAA,IAC3B;AACA,QAAI,YAAY,OAAO,OAAO,CAAC,KAAK,KAAK;AACrC,aAAO,KAAK,OAAO,QAA6B,eAAe,OAA8B,IAAI,QAAQ,IAAI;AACjH,QAAI,cAAc,OAAO,iBAAiB;AACtC,aAAO,KAAK,IAAI,OAA8B,KAAK,UAAU,eAAe,KAAK,SAAS,IAAI,OAA6B,CAAC,GAAG,WAAW,CAAC;AAC/I,QAAI,SAAS;AACT,aAAO,KAAK,IAAI,OAA8B,KAAK,QAAQ,CAAC,QAAQ,SAAS,KAAK,QAAQ,MAAM,CAAC;AACrG,QAAI,cAAc;AACd,aAAO,KAAK,IAAI,OAA8B,OAA8B,KAAK,QAAQ,CAAC,eAAe,WAAW,CAAC;AACzH,QAAI,YAAY;AACZ,aAAO,KAAK,OAAO,QAA6B,eAAe,OAA8B,KAAK,QAC7F,eAAe,IAAI,QAA0B,QAAQ,IAAI;AAClE,WAAO,MAAM,UAAU,IAAI,OACrB,KAAK,QAAQ,IAAI,CAAC,IAAI,OAA8B,KAAK,OAA8B,OAAyB,KAAK,IAAI;AAAA,EACnI;AAAA,EACA,OAAOA,QAAO,WAAW,MAAM;AAC3B,QAAI,SAAS,CAAC,GAAG,IAAI;AACrB,aAAS,OAAO,WAAW;AACvB,UAAI,KAAK,OAAO,KAAK,SAAS,cAAc,YAAY,MAAM,GAAG,CAAC,IAAI;AACtE,UAAI,KAAK,OAAO,IAAI,CAAC,KAAK;AACtB,eAAO,IAAI,CAAC,IAAI;AAAA,WACf;AACD,eAAO,GAAG,IAAI;AACd,eAAO,GAAG,IAAI;AAAA,MAClB;AAAA,IACJ;AACA,WAAO,KAAK,IAAIA,SAAQ,KAAK,QAAQ,MAAM;AAAA,EAC/C;AACJ;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,SAAS;AACjB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS,QAAQ,YAAY;AAAA,EACtC;AAAA,EACA,MAAM,MAAM;AACR,QAAI,KAAK,SAAS,KAAK,QAAQ;AAC3B,aAAO;AACX,QAAI,QAAQ,KAAK,MAAM,GAAG,KAAK,QAAQ,MAAM;AAC7C,QAAI,QAAQ,SAAS,KAAK,UAAU,IAAI,MAAM,YAAY,KAAK,KAAK,SAAS,OAA8B;AAC3G,QAAI,SAAS;AACT,aAAO;AACX,SAAK,UAAU,CAAC,GAAG,MAAM,MAAM;AAC/B,SAAK,QAAQ,SAAS,KAAK,UAAU,KAAK,QAAQ,SAAS,IAAI;AAC/D,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,mBAAgC,MAAM,OAAO;AAAA,EAC/C,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,kBAAkB;AAAA,MAClB,sBAAsB,MAAM;AAAA,MAC5B,uBAAuB;AAAA,MACvB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,cAAc,MAAM;AAAA,MACpB,aAAa,MAAM;AAAA,MACnB,aAAa;AAAA,MACb,OAAO;AAAA,MACP,cAAc,CAAC;AAAA,MACf,cAAc;AAAA,MACd,cAAc;AAAA,MACd,oBAAoB,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK;AAAA,MAC3D,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IACpB,GAAG;AAAA,MACC,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,MAC9B,aAAa,CAAC,GAAG,MAAM,KAAK;AAAA,MAC5B,OAAO,CAAC,GAAG,MAAM,KAAK;AAAA,MACtB,cAAc,CAAC,GAAG,MAAM,OAAK,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MACjD,aAAa,CAAC,GAAG,MAAM,OAAK,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MAChD,cAAc,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC;AAAA,MAClC,cAAc,CAAC,GAAG,MAAM,KAAK;AAAA,IACjC,CAAC;AAAA,EACL;AACJ,CAAC;AACD,SAAS,UAAU,GAAG,GAAG;AACrB,SAAO,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AACrC;AACA,SAAS,oBAAoB,MAAM,MAAM,QAAQ,MAAM,OAAO,SAAS;AACnE,MAAI,MAAM,KAAK,iBAAiB,UAAU,KAAK,OAAO,KAAK,SAAS;AACpE,MAAI,OAAO,OAAO,QAAQ;AAC1B,MAAI,YAAY,KAAK,OAAO,MAAM,MAAM,aAAa,MAAM,QAAQ,KAAK;AACxE,MAAI,YAAY,KAAK,QAAQ,KAAK,MAAM,aAAa,KAAK,SAAS,KAAK;AACxE,MAAI,QAAQ,YAAY,KAAK,IAAI,WAAW,UAAU;AAClD,WAAO;AAAA,WACF,CAAC,QAAQ,aAAa,KAAK,IAAI,WAAW,SAAS;AACxD,WAAO;AACX,MAAI,cAAc,OAAO,YAAY,aAAa;AAC9C,aAAS,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,OAAO,KAAK,MAAM,SAAS,UAAU,CAAC,IAAI,KAAK;AACrF,eAAW,KAAK,IAAI,KAAsB,OAAO,YAAY,UAAU;AAAA,EAC3E,OACK;AACD,aAAS;AACT,eAAW,KAAK;AAAA,MAAI;AAAA,OAAuB,MAAM,KAAK,QAAQ,MAAM,QAAQ,KAAK,QAAQ;AAAA;AAAA,IAAoB;AAC7G,QAAI,aAAa,MAAM,SAAS,KAAK;AACrC,QAAI,cAAc,cAAc,aAAa,KAAK,KAAK;AACnD,eAAS,OAAO,SAAS,KAAK;AAAA,IAClC,OACK;AACD,aAAO;AACP,eAAS,KAAK,SAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACA,MAAI,UAAU,KAAK,SAAS,KAAK,OAAO,QAAQ;AAChD,MAAI,UAAU,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AAChD,SAAO;AAAA,IACH,OAAO,GAAG,IAAI,KAAK,SAAS,MAAM,kBAAkB,WAAW,MAAM;AAAA,IACrE,OAAO,wBAAwB,SAAU,MAAM,gBAAgB,iBAAkB,OAAO,SAAS;AAAA,EACrG;AACJ;AAEA,SAAS,cAAcC,SAAQ;AAC3B,MAAI,UAAUA,QAAO,aAAa,MAAM;AACxC,MAAIA,QAAO;AACP,YAAQ,KAAK;AAAA,MACT,OAAO,YAAY;AACf,YAAI,OAAO,SAAS,cAAc,KAAK;AACvC,aAAK,UAAU,IAAI,mBAAmB;AACtC,YAAI,WAAW;AACX,eAAK,UAAU,IAAI,GAAG,WAAW,KAAK,MAAM,MAAM,EAAE,IAAI,SAAO,uBAAuB,GAAG,CAAC;AAC9F,aAAK,aAAa,eAAe,MAAM;AACvC,eAAO;AAAA,MACX;AAAA,MACA,UAAU;AAAA,IACd,CAAC;AACL,UAAQ,KAAK;AAAA,IACT,OAAO,YAAY,IAAI,IAAI,OAAO;AAC9B,UAAI,WAAW,SAAS,cAAc,MAAM;AAC5C,eAAS,YAAY;AACrB,UAAI,QAAQ,WAAW,gBAAgB,WAAW,OAAO,MAAM;AAC/D,eAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AAC/B,YAAI,OAAO,MAAM,GAAG,GAAG,KAAK,MAAM,GAAG;AACrC,YAAI,OAAO;AACP,mBAAS,YAAY,SAAS,eAAe,MAAM,MAAM,KAAK,IAAI,CAAC,CAAC;AACxE,YAAI,OAAO,SAAS,YAAY,SAAS,cAAc,MAAM,CAAC;AAC9D,aAAK,YAAY,SAAS,eAAe,MAAM,MAAM,MAAM,EAAE,CAAC,CAAC;AAC/D,aAAK,YAAY;AACjB,cAAM;AAAA,MACV;AACA,UAAI,MAAM,MAAM;AACZ,iBAAS,YAAY,SAAS,eAAe,MAAM,MAAM,GAAG,CAAC,CAAC;AAClE,aAAO;AAAA,IACX;AAAA,IACA,UAAU;AAAA,EACd,GAAG;AAAA,IACC,OAAO,YAAY;AACf,UAAI,CAAC,WAAW;AACZ,eAAO;AACX,UAAI,YAAY,SAAS,cAAc,MAAM;AAC7C,gBAAU,YAAY;AACtB,gBAAU,cAAc,WAAW;AACnC,aAAO;AAAA,IACX;AAAA,IACA,UAAU;AAAA,EACd,CAAC;AACD,SAAO,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,OAAK,EAAE,MAAM;AAC5E;AACA,SAAS,oBAAoB,OAAO,UAAU,KAAK;AAC/C,MAAI,SAAS;AACT,WAAO,EAAE,MAAM,GAAG,IAAI,MAAM;AAChC,MAAI,WAAW;AACX,eAAW;AACf,MAAI,YAAa,SAAS,GAAI;AAC1B,QAAIC,OAAM,KAAK,MAAM,WAAW,GAAG;AACnC,WAAO,EAAE,MAAMA,OAAM,KAAK,KAAKA,OAAM,KAAK,IAAI;AAAA,EAClD;AACA,MAAI,MAAM,KAAK,OAAO,QAAQ,YAAY,GAAG;AAC7C,SAAO,EAAE,MAAM,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,MAAM,IAAI;AAClE;AACA,IAAM,oBAAN,MAAwB;AAAA,EACpB,YAAY,MAAM,YAAYC,kBAAiB;AAC3C,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,kBAAkBA;AACvB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,eAAe;AAAA,MAChB,MAAM,MAAM,KAAK,YAAY;AAAA,MAC7B,OAAO,CAAC,QAAQ,KAAK,UAAU,GAAG;AAAA,MAClC,KAAK;AAAA,IACT;AACA,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,QAAI,SAAS,KAAK,MAAM,MAAM,UAAU;AACxC,QAAI,EAAE,SAAS,SAAS,IAAI,OAAO;AACnC,QAAIF,UAAS,KAAK,MAAM,MAAM,gBAAgB;AAC9C,SAAK,gBAAgB,cAAcA,OAAM;AACzC,SAAK,cAAcA,QAAO;AAC1B,SAAK,eAAeA,QAAO;AAC3B,SAAK,QAAQ,oBAAoB,QAAQ,QAAQ,UAAUA,QAAO,kBAAkB;AACpF,SAAK,MAAM,SAAS,cAAc,KAAK;AACvC,SAAK,IAAI,YAAY;AACrB,SAAK,mBAAmB,KAAK,KAAK;AAClC,SAAK,IAAI,iBAAiB,aAAa,CAAC,MAAM;AAC1C,UAAI,EAAE,SAAAG,SAAQ,IAAI,KAAK,MAAM,MAAM,UAAU,EAAE;AAC/C,eAAS,MAAM,EAAE,QAAQ,OAAO,OAAO,OAAO,KAAK,KAAK,MAAM,IAAI,YAAY;AAC1E,YAAI,IAAI,YAAY,SAAS,QAAQ,UAAU,KAAK,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAIA,SAAQ,QAAQ;AACxF,eAAK,gBAAgB,MAAMA,SAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7C,YAAE,eAAe;AACjB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,IAAI,iBAAiB,YAAY,CAAC,MAAM;AACzC,UAAI,QAAQ,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK;AACnD,UAAI,SAAS,MAAM,WAAW,KAAK,MAAM,MAAM,gBAAgB,EAAE,eAC7D,EAAE,iBAAiB,KAAK;AACxB,aAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,IAAI,EAAE,CAAC;AAAA,IACjE,CAAC;AACD,SAAK,YAAY,SAAS,OAAO,EAAE;AAAA,EACvC;AAAA,EACA,QAAQ;AAAE,SAAK,UAAU;AAAA,EAAG;AAAA,EAC5B,YAAY,SAAS,IAAI;AACrB,QAAI,KAAK;AACL,WAAK,KAAK,OAAO;AACrB,SAAK,OAAO,KAAK,IAAI,YAAY,KAAK,cAAc,SAAS,IAAI,KAAK,KAAK,CAAC;AAC5E,SAAK,KAAK,iBAAiB,UAAU,MAAM;AACvC,UAAI,KAAK;AACL,aAAK,KAAK,eAAe,KAAK,YAAY;AAAA,IAClD,CAAC;AAAA,EACL;AAAA,EACA,OAAO,QAAQ;AACX,QAAI;AACJ,QAAI,SAAS,OAAO,MAAM,MAAM,KAAK,UAAU;AAC/C,QAAI,YAAY,OAAO,WAAW,MAAM,KAAK,UAAU;AACvD,SAAK,mBAAmB,OAAO,KAAK;AACpC,QAAI,UAAU,WAAW;AACrB,UAAI,EAAE,SAAS,UAAU,SAAS,IAAI,OAAO;AAC7C,UAAI,CAAC,UAAU,QAAQ,UAAU,KAAK,WAAW,SAAS;AACtD,aAAK,QAAQ,oBAAoB,QAAQ,QAAQ,UAAU,OAAO,MAAM,MAAM,gBAAgB,EAAE,kBAAkB;AAClH,aAAK,YAAY,SAAS,OAAO,EAAE;AAAA,MACvC;AACA,WAAK,UAAU;AACf,UAAI,cAAc,KAAK,UAAU,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC3E,aAAK,IAAI,UAAU,OAAO,oCAAoC,CAAC,CAAC,QAAQ;AAAA,IAChF;AAAA,EACJ;AAAA,EACA,mBAAmB,OAAO;AACtB,QAAI,MAAM,KAAK,aAAa,KAAK;AACjC,QAAI,OAAO,KAAK,cAAc;AAC1B,eAAS,KAAK,KAAK,aAAa,MAAM,GAAG;AACrC,YAAI;AACA,eAAK,IAAI,UAAU,OAAO,CAAC;AACnC,eAAS,KAAK,IAAI,MAAM,GAAG;AACvB,YAAI;AACA,eAAK,IAAI,UAAU,IAAI,CAAC;AAChC,WAAK,eAAe;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,WAAW,OAAO;AACd,SAAK,QAAQ;AACb,QAAI,KAAK;AACL,WAAK,KAAK,eAAe,KAAK,YAAY;AAAA,EAClD;AAAA,EACA,YAAY;AACR,QAAI,SAAS,KAAK,KAAK,MAAM,MAAM,KAAK,UAAU,GAAG,OAAO,OAAO;AACnE,QAAI,KAAK,WAAW,MAAM,KAAK,WAAW,KAAK,MAAM,QAAQ,KAAK,YAAY,KAAK,MAAM,IAAI;AACzF,WAAK,QAAQ,oBAAoB,KAAK,QAAQ,QAAQ,KAAK,UAAU,KAAK,KAAK,MAAM,MAAM,gBAAgB,EAAE,kBAAkB;AAC/H,WAAK,YAAY,KAAK,SAAS,OAAO,EAAE;AAAA,IAC5C;AACA,QAAI,KAAK,qBAAqB,KAAK,QAAQ,GAAG;AAC1C,WAAK,YAAY;AACjB,UAAI,EAAE,WAAW,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAC/C,UAAI,EAAE,KAAK,IAAI;AACf,UAAI,CAAC;AACD;AACJ,UAAI,aAAa,OAAO,SAAS,WAAW,SAAS,eAAe,IAAI,IAAI,KAAK,UAAU;AAC3F,UAAI,CAAC;AACD;AACJ,UAAI,UAAU,YAAY;AACtB,mBAAW,KAAK,SAAO;AACnB,cAAI,OAAO,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK;AACxD,iBAAK,YAAY,KAAK,UAAU;AAAA,QACxC,CAAC,EAAE,MAAM,OAAK,aAAa,KAAK,KAAK,OAAO,GAAG,iBAAiB,CAAC;AAAA,MACrE,OACK;AACD,aAAK,YAAY,YAAY,UAAU;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,SAAS,YAAY;AAC7B,SAAK,YAAY;AACjB,QAAI,OAAO,KAAK,OAAO,SAAS,cAAc,KAAK;AACnD,SAAK,YAAY;AACjB,QAAI,QAAQ,YAAY,MAAM;AAC1B,WAAK,YAAY,OAAO;AACxB,WAAK,cAAc;AAAA,IACvB,OACK;AACD,UAAI,EAAE,KAAK,QAAQ,IAAI;AACvB,WAAK,YAAY,GAAG;AACpB,WAAK,cAAc,WAAW;AAAA,IAClC;AACA,SAAK,IAAI,YAAY,IAAI;AACzB,SAAK,KAAK,eAAe,KAAK,YAAY;AAAA,EAC9C;AAAA,EACA,qBAAqB,UAAU;AAC3B,QAAI,MAAM;AACV,aAAS,MAAM,KAAK,KAAK,YAAY,IAAI,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI,aAAa,KAAK;AACvF,UAAI,IAAI,YAAY,QAAQ,CAAC,IAAI,IAAI;AACjC;AAAA,MACJ,WACS,KAAK,UAAU;AACpB,YAAI,CAAC,IAAI,aAAa,eAAe,GAAG;AACpC,cAAI,aAAa,iBAAiB,MAAM;AACxC,gBAAM;AAAA,QACV;AAAA,MACJ,OACK;AACD,YAAI,IAAI,aAAa,eAAe;AAChC,cAAI,gBAAgB,eAAe;AAAA,MAC3C;AAAA,IACJ;AACA,QAAI;AACA,qBAAe,KAAK,MAAM,GAAG;AACjC,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AACV,QAAI,MAAM,KAAK,IAAI,cAAc,iBAAiB;AAClD,QAAI,CAAC,OAAO,CAAC,KAAK;AACd,aAAO;AACX,QAAI,WAAW,KAAK,IAAI,sBAAsB;AAC9C,QAAI,WAAW,KAAK,KAAK,sBAAsB;AAC/C,QAAI,UAAU,IAAI,sBAAsB;AACxC,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,OAAO;AACR,UAAI,MAAM,KAAK,IAAI,cAAc,eAAe;AAChD,cAAQ,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,IAAI,YAAY,QAAQ,IAAI,YAAY;AAAA,IAC9E;AACA,QAAI,QAAQ,MAAM,KAAK,IAAI,MAAM,QAAQ,SAAS,MAAM,IAAI,MACxD,QAAQ,SAAS,KAAK,IAAI,MAAM,KAAK,SAAS,GAAG,IAAI;AACrD,aAAO;AACX,WAAO,KAAK,KAAK,MAAM,MAAM,gBAAgB,EAAE,aAAa,KAAK,MAAM,UAAU,SAAS,UAAU,OAAO,KAAK,GAAG;AAAA,EACvH;AAAA,EACA,UAAU,KAAK;AACX,QAAI,KAAK,MAAM;AACX,UAAI,KAAK;AACL,YAAI,IAAI;AACJ,eAAK,KAAK,MAAM,UAAU,IAAI;AAClC,aAAK,KAAK,YAAY,mCAAmC,IAAI,SAAS;AAAA,MAC1E,OACK;AACD,aAAK,KAAK,MAAM,UAAU;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,SAAS,IAAI,OAAO;AAC9B,UAAM,KAAK,SAAS,cAAc,IAAI;AACtC,OAAG,KAAK;AACR,OAAG,aAAa,QAAQ,SAAS;AACjC,OAAG,aAAa,iBAAiB,MAAM;AACvC,OAAG,aAAa,cAAc,KAAK,KAAK,MAAM,OAAO,aAAa,CAAC;AACnE,QAAI,aAAa;AACjB,aAAS,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK;AACxC,UAAI,EAAE,YAAY,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE,QAAQ,IAAI;AACtD,UAAI,SAAS;AACT,YAAI,OAAO,OAAO,WAAW,WAAW,UAAU,QAAQ;AAC1D,YAAI,QAAQ,eAAe,IAAI,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAC3D,uBAAa;AACb,cAAI,OAAO,WAAW,YAAY,QAAQ,QAAQ;AAC9C,eAAG,YAAY,QAAQ,OAAO,OAAO,CAAC;AAAA,UAC1C,OACK;AACD,gBAAI,SAAS,GAAG,YAAY,SAAS,cAAc,oBAAoB,CAAC;AACxE,mBAAO,cAAc;AAAA,UACzB;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,KAAK,GAAG,YAAY,SAAS,cAAc,IAAI,CAAC;AACtD,SAAG,KAAK,KAAK,MAAM;AACnB,SAAG,aAAa,QAAQ,QAAQ;AAChC,UAAI,MAAM,KAAK,YAAY,UAAU;AACrC,UAAI;AACA,WAAG,YAAY;AACnB,eAAS,UAAU,KAAK,eAAe;AACnC,YAAI,OAAO,OAAO,YAAY,KAAK,KAAK,OAAO,KAAK,MAAM,KAAK;AAC/D,YAAI;AACA,aAAG,YAAY,IAAI;AAAA,MAC3B;AAAA,IACJ;AACA,QAAI,MAAM;AACN,SAAG,UAAU,IAAI,gCAAgC;AACrD,QAAI,MAAM,KAAK,QAAQ;AACnB,SAAG,UAAU,IAAI,mCAAmC;AACxD,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AACV,QAAI,KAAK,MAAM;AACX,UAAI,KAAK;AACL,aAAK,YAAY;AACrB,WAAK,KAAK,OAAO;AACjB,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AAAA,EACrB;AACJ;AACA,SAAS,kBAAkB,YAAYD,kBAAiB;AACpD,SAAO,CAAC,SAAS,IAAI,kBAAkB,MAAM,YAAYA,gBAAe;AAC5E;AACA,SAAS,eAAe,WAAW,SAAS;AACxC,MAAI,SAAS,UAAU,sBAAsB;AAC7C,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,OAAO,SAAS,UAAU;AACvC,MAAI,KAAK,MAAM,OAAO;AAClB,cAAU,cAAc,OAAO,MAAM,KAAK,OAAO;AAAA,WAC5C,KAAK,SAAS,OAAO;AAC1B,cAAU,cAAc,KAAK,SAAS,OAAO,UAAU;AAC/D;AAIA,SAAS,MAAM,QAAQ;AACnB,UAAQ,OAAO,SAAS,KAAK,OAAO,OAAO,QAAQ,KAAK,MAAM,OAAO,OAAO,IAAI,MAC3E,OAAO,OAAO,IAAI;AAC3B;AACA,SAAS,YAAY,QAAQ,OAAO;AAChC,MAAI,UAAU,CAAC;AACf,MAAI,WAAW;AACf,MAAI,YAAY,CAAC,WAAW;AACxB,YAAQ,KAAK,MAAM;AACnB,QAAI,EAAE,QAAQ,IAAI,OAAO;AACzB,QAAI,SAAS;AACT,UAAI,CAAC;AACD,mBAAW,CAAC;AAChB,UAAI,OAAO,OAAO,WAAW,WAAW,UAAU,QAAQ;AAC1D,UAAI,CAAC,SAAS,KAAK,OAAK,EAAE,QAAQ,IAAI;AAClC,iBAAS,KAAK,OAAO,WAAW,WAAW,EAAE,KAAK,IAAI,OAAO;AAAA,IACrE;AAAA,EACJ;AACA,MAAI,OAAO,MAAM,MAAM,gBAAgB;AACvC,WAAS,KAAK;AACV,QAAI,EAAE,UAAU,GAAG;AACf,UAAI,WAAW,EAAE,OAAO;AACxB,UAAI,EAAE,OAAO,WAAW,OAAO;AAC3B,iBAAS,UAAU,EAAE,OAAO,SAAS;AACjC,oBAAU,IAAI,OAAO,QAAQ,EAAE,QAAQ,WAAW,SAAS,MAAM,IAAI,CAAC,GAAG,MAAM,QAAQ,MAAM,CAAC;AAAA,QAClG;AAAA,MACJ,OACK;AACD,YAAI,UAAU,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,GAAG;AAC5C,YAAI,UAAU,KAAK,eAAe,IAAI,cAAc,OAAO,IAAI,IAAI,aAAa,OAAO;AACvF,iBAAS,UAAU,EAAE,OAAO;AACxB,cAAI,QAAQ,QAAQ,MAAM,OAAO,KAAK,GAAG;AACrC,gBAAI,UAAU,CAAC,OAAO,eAAe,MAAM,UAAU,WAAW,SAAS,QAAQ,MAAM,OAAO,IAAI,CAAC;AACnG,sBAAU,IAAI,OAAO,QAAQ,EAAE,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS,EAAE,CAAC;AAAA,UACtF;AAAA,MACR;AAAA,IACJ;AACJ,MAAI,UAAU;AACV,QAAI,eAAe,uBAAO,OAAO,IAAI,GAAG,MAAM;AAC9C,QAAI,MAAM,CAAC,GAAG,MAAM;AAAE,UAAI,IAAI;AAAI,eAAS,KAAK,EAAE,UAAU,QAAQ,OAAO,SAAS,KAAK,SAAS,KAAK,EAAE,UAAU,QAAQ,OAAO,SAAS,KAAK,SAAS,EAAE,OAAO,EAAE,OAAO,KAAK;AAAA,IAAI;AACpL,aAAS,KAAK,SAAS,KAAK,GAAG,GAAG;AAC9B,aAAO;AACP,mBAAa,EAAE,IAAI,IAAI;AAAA,IAC3B;AACA,aAAS,UAAU,SAAS;AACxB,UAAI,EAAE,QAAQ,IAAI,OAAO;AACzB,UAAI;AACA,eAAO,SAAS,aAAa,OAAO,WAAW,WAAW,UAAU,QAAQ,IAAI;AAAA,IACxF;AAAA,EACJ;AACA,MAAI,SAAS,CAAC,GAAG,OAAO;AACxB,MAAI,UAAU,KAAK;AACnB,WAAS,OAAO,QAAQ,KAAK,CAAC,GAAG,MAAO,EAAE,QAAQ,EAAE,SAAU,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,GAAG;AAChG,QAAIE,OAAM,IAAI;AACd,QAAI,CAAC,QAAQ,KAAK,SAASA,KAAI,SAAS,KAAK,UAAUA,KAAI,UACtD,KAAK,QAAQ,QAAQA,KAAI,QAAQ,QAAQ,KAAK,QAAQA,KAAI,QAC3D,KAAK,SAASA,KAAI,SAAS,KAAK,SAASA,KAAI;AAC7C,aAAO,KAAK,GAAG;AAAA,aACV,MAAM,IAAI,UAAU,IAAI,MAAM,IAAI;AACvC,aAAO,OAAO,SAAS,CAAC,IAAI;AAChC,WAAO,IAAI;AAAA,EACf;AACA,SAAO;AACX;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACnB,YAAY,SAAS,OAAO,SAAS,WAAW,UAAU,UAAU;AAChE,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,YAAY,UAAU,IAAI;AACtB,WAAO,YAAY,KAAK,YAAY,YAAY,KAAK,QAAQ,SAAS,OAChE,IAAI,kBAAiB,KAAK,SAAS,UAAU,IAAI,QAAQ,GAAG,KAAK,SAAS,KAAK,WAAW,UAAU,KAAK,QAAQ;AAAA,EAC3H;AAAA,EACA,OAAO,MAAM,QAAQ,OAAO,IAAI,MAAM,MAAM;AACxC,QAAI,UAAU,YAAY,QAAQ,KAAK;AACvC,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO,QAAQ,OAAO;AAAA,QAAK,OAAK,EAAE,SAAS;AAAA;AAAA,MAAqB,IAC5D,IAAI,kBAAiB,KAAK,SAAS,KAAK,OAAO,KAAK,SAAS,KAAK,WAAW,KAAK,UAAU,IAAI,IAAI;AAAA,IAC5G;AACA,QAAI,WAAW,MAAM,MAAM,gBAAgB,EAAE,eAAe,IAAI;AAChE,QAAI,QAAQ,KAAK,YAAY,YAAY,KAAK,YAAY,IAAI;AAC1D,UAAI,gBAAgB,KAAK,QAAQ,KAAK,QAAQ,EAAE;AAChD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,YAAI,QAAQ,CAAC,EAAE,cAAc,eAAe;AACxC,qBAAW;AACX;AAAA,QACJ;AAAA,IACR;AACA,WAAO,IAAI,kBAAiB,SAAS,UAAU,IAAI,QAAQ,GAAG;AAAA,MAC1D,KAAK,OAAO,OAAO,CAAC,GAAG,MAAM,EAAE,UAAU,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,GAAG;AAAA,MACzE,QAAQ;AAAA,MACR,OAAO,KAAK;AAAA,IAChB,GAAG,OAAO,KAAK,YAAY,KAAK,IAAI,GAAG,UAAU,KAAK;AAAA,EAC1D;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,kBAAiB,KAAK,SAAS,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,EAAE,KAAK,QAAQ,OAAO,KAAK,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,WAAW,KAAK,UAAU,KAAK,QAAQ;AAAA,EACjM;AACJ;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAClB,YAAY,QAAQ,IAAI,MAAM;AAC1B,SAAK,SAAS;AACd,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,QAAQ;AACX,WAAO,IAAI,iBAAgB,MAAM,WAAW,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,EAAE,SAAS,EAAE,GAAG,IAAI;AAAA,EAClG;AAAA,EACA,OAAO,IAAI;AACP,QAAI,EAAE,MAAM,IAAI,IAAI,OAAO,MAAM,MAAM,gBAAgB;AACvD,QAAI,UAAU,KAAK,YACf,MAAM,eAAe,gBAAgB,IAAI,KAAK,CAAC,EAAE,IAAI,QAAQ;AACjE,QAAI,SAAS,QAAQ,IAAI,YAAU;AAC/B,UAAI,QAAQ,KAAK,OAAO,KAAK,OAAK,EAAE,UAAU,MAAM,KAChD,IAAI;AAAA,QAAa;AAAA,QAAQ,KAAK,OAAO;AAAA,UAAK,OAAK,EAAE,SAAS;AAAA;AAAA,QAAsB,IAAI,IAAwB;AAAA;AAAA,MAAsB;AACtI,aAAO,MAAM,OAAO,IAAI,IAAI;AAAA,IAChC,CAAC;AACD,QAAI,OAAO,UAAU,KAAK,OAAO,UAAU,OAAO,MAAM,CAAC,GAAG,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC;AACjF,eAAS,KAAK;AAClB,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,GAAG;AACX,aAAO,KAAK,IAAI,GAAG,OAAO;AAC9B,QAAI,GAAG,aAAa,OAAO,KAAK,OAAK,EAAE,UAAU,KAAK,GAAG,QAAQ,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC,KACvF,CAAC,YAAY,QAAQ,KAAK,MAAM;AAChC,aAAO,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,aAC3D,QAAQ,KAAK,YAAY,CAAC,OAAO;AAAA,MAAK,OAAK,EAAE,SAAS;AAAA;AAAA,IAAqB;AAChF,aAAO;AACX,QAAI,CAAC,QAAQ,OAAO;AAAA,MAAM,OAAK,EAAE,SAAS;AAAA;AAAA,IAAqB,KAAK,OAAO,KAAK,OAAK,EAAE,UAAU,CAAC;AAC9F,eAAS,OAAO,IAAI,OAAK,EAAE,UAAU,IAAI,IAAI;AAAA,QAAa,EAAE;AAAA,QAAQ;AAAA;AAAA,MAAsB,IAAI,CAAC;AACnG,aAAS,UAAU,GAAG;AAClB,UAAI,OAAO,GAAG,iBAAiB;AAC3B,eAAO,QAAQ,KAAK,YAAY,OAAO,OAAO,KAAK,EAAE;AAC7D,WAAO,UAAU,KAAK,UAAU,QAAQ,KAAK,OAAO,OAAO,IAAI,iBAAgB,QAAQ,KAAK,IAAI,IAAI;AAAA,EACxG;AAAA,EACA,IAAI,UAAU;AAAE,WAAO,KAAK,OAAO,KAAK,KAAK,UAAU;AAAA,EAAM;AAAA,EAC7D,IAAI,QAAQ;AAAE,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,EAAW;AAClE;AACA,SAAS,YAAY,GAAG,GAAG;AACvB,MAAI,KAAK;AACL,WAAO;AACX,WAAS,KAAK,GAAG,KAAK,OAAK;AACvB,WAAO,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE;AAC3B;AACJ,WAAO,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE;AAC3B;AACJ,QAAI,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,EAAE;AAC1C,QAAI,QAAQ;AACR,aAAO,QAAQ;AACnB,QAAI,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;AAC1B,aAAO;AAAA,EACf;AACJ;AACA,IAAM,YAAY;AAAA,EACd,qBAAqB;AACzB;AACA,SAAS,UAAU,IAAI,UAAU;AAC7B,MAAI,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACrB;AACA,MAAI,WAAW;AACX,WAAO,uBAAuB,IAAI,KAAK,MAAM;AACjD,SAAO;AACX;AACA,IAAM,OAAO,CAAC;AACd,SAAS,aAAa,IAAI,MAAM;AAC5B,MAAI,GAAG,YAAY,gBAAgB,GAAG;AAClC,QAAI,aAAa,GAAG,WAAW,gBAAgB;AAC/C,QAAI,cAAc,KAAK,qBAAqB,UAAU;AAClD,aAAO;AAAA,EACf;AACA,SAAO,GAAG,YAAY,YAAY,IAAI,UAAU,GAAG,YAAY,iBAAiB,IAAI,WAAW;AACnG;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAAQ,OAAO,cAAc,IAAI;AACzC,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,YAAY;AAAE,WAAO;AAAA,EAAO;AAAA,EAC5B,OAAO,IAAI,MAAM;AACb,QAAI,QAAQ,aAAa,IAAI,IAAI,GAAG,QAAQ;AAC5C,QAAI;AACA,cAAQ,MAAM,gBAAgB,IAAI,OAAO,IAAI;AAAA,aACxC,GAAG;AACR,cAAQ,MAAM,aAAa,EAAE;AAAA,aACxB,GAAG,aAAa,MAAM,SAAS;AACpC,cAAQ,IAAI;AAAA,QAAa,MAAM;AAAA,QAAQ;AAAA;AAAA,MAAsB;AACjE,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,qBAAqB;AAC/B,gBAAQ,IAAI,cAAa,MAAM,QAAQ,GAAuB,OAAO,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;AAAA,eAC1F,OAAO,GAAG,qBAAqB;AACpC,gBAAQ,IAAI;AAAA,UAAa,MAAM;AAAA,UAAQ;AAAA;AAAA,QAAsB;AAAA,eACxD,OAAO,GAAG,eAAe;AAC9B,iBAAS,UAAU,OAAO;AACtB,cAAI,OAAO,UAAU,MAAM;AACvB,oBAAQ;AAAA;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,IAAI,MAAM,MAAM;AAC5B,WAAO,QAAQ,YAAY,CAAC,KAAK,mBAAmB,KAAK,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MAAa,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAqB;AAAA,EAClI;AAAA,EACA,aAAa,IAAI;AACb,WAAO,GAAG,QAAQ,aAAa,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI;AAAA,MAAa,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAsB,IAAI,KAAK,IAAI,GAAG,OAAO;AAAA,EACpI;AAAA,EACA,IAAI,SAAS;AACT,WAAO,QAAQ,SAAS,KAAK,cAAc,IAAI,OAAO,IAAI,cAAa,KAAK,QAAQ,KAAK,OAAO,QAAQ,OAAO,KAAK,WAAW,CAAC;AAAA,EACpI;AACJ;AACA,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACpC,YAAY,QAAQ,aAAa,QAAQ,MAAM,IAAI;AAC/C,UAAM,QAAQ,GAAsB,WAAW;AAC/C,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AAAE,WAAO;AAAA,EAAM;AAAA,EAC3B,gBAAgB,IAAI,MAAM,MAAM;AAC5B,QAAI;AACJ,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,OAAO,CAAC,GAAG,QAAQ;AAC1B,eAAS,OAAO,IAAI,QAAQ,GAAG,OAAO;AAC1C,QAAI,OAAO,GAAG,QAAQ,OAAO,KAAK,IAAI,GAAG,KAAK,GAAG,QAAQ,OAAO,KAAK,IAAI,CAAC;AAC1E,QAAI,MAAM,IAAI,GAAG,KAAK;AACtB,SAAK,KAAK,cAAc,IAAI,OAAO,OAAO,MAAM,KAAK,SACjD,MAAM,MAAM,CAAC,UACb,QAAQ,YAAY,IAAI,GAAG,UAAU,KAAK,KAAK;AAC/C,aAAO,IAAI;AAAA,QAAa,KAAK;AAAA,QAAQ,QAAQ,WAAW,KAAK,mBAAmB,IAAwB;AAAA;AAAA,MAAsB;AAClI,QAAI,cAAc,KAAK,cAAc,IAAI,KAAK,GAAG,QAAQ,OAAO,KAAK,WAAW;AAChF,QAAI,WAAW,OAAO,UAAU,GAAG,OAAO,MAAM,EAAE;AAC9C,aAAO,IAAI,cAAa,KAAK,QAAQ,aAAa,QAAQ,MAAM,EAAE;AACtE,QAAI,OAAO,WACN,SAAS,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI,kBAAkB,GAAG,OAAO,KAAK,eAAe,CAAC,CAAC;AAChG,aAAO,IAAI,cAAa,KAAK,QAAQ,aAAa,QAAQ,OAAO,OAAO,KAAK,OAAO,QAAQ,QAAQ,OAAO,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC;AAC1I,WAAO,IAAI,aAAa,KAAK,QAAQ,GAAuB,WAAW;AAAA,EAC3E;AAAA,EACA,aAAa,IAAI;AACb,WAAO,GAAG,QAAQ,aAAa,KAAK,MAAM,KAAK,EAAE,IAAI,IAAI;AAAA,MAAa,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAsB,IAAI,KAAK,IAAI,GAAG,OAAO;AAAA,EACpI;AAAA,EACA,IAAI,SAAS;AACT,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,SAAS,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO,IAAI,KAAK;AAC5E,QAAI,CAAC;AACD,aAAO,IAAI;AAAA,QAAa,KAAK;AAAA,QAAQ;AAAA;AAAA,MAAsB;AAC/D,WAAO,IAAI,cAAa,KAAK,QAAQ,KAAK,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,WAAW,GAAG,KAAK,QAAQ,QAAQ,OAAO,KAAK,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,CAAC,CAAC;AAAA,EACzK;AACJ;AACA,SAAS,WAAW,UAAU,OAAO,MAAM,IAAI;AAC3C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,MAAM,SAAS,MAAM,EAAE;AAClC,SAAO,OAAO,YAAY,aAAa,SAAS,MAAM,MAAM,IAAI,KAAK,IAAI,aAAa,UAAU,IAAI,EAAE,KAAK,IAAI;AACnH;AACA,IAAM,kBAA+B,YAAY,OAAO;AAAA,EACpD,IAAI,SAAS,SAAS;AAAE,WAAO,QAAQ,IAAI,OAAK,EAAE,IAAI,OAAO,CAAC;AAAA,EAAG;AACrE,CAAC;AACD,IAAM,oBAAiC,YAAY,OAAO;AAC1D,IAAM,kBAA+B,WAAW,OAAO;AAAA,EACnD,SAAS;AAAE,WAAO,gBAAgB,MAAM;AAAA,EAAG;AAAA,EAC3C,OAAO,OAAO,IAAI;AAAE,WAAO,MAAM,OAAO,EAAE;AAAA,EAAG;AAAA,EAC7C,SAAS,OAAK;AAAA,IACV,YAAY,KAAK,GAAG,SAAO,IAAI,OAAO;AAAA,IACtC,WAAW,kBAAkB,KAAK,GAAG,WAAS,MAAM,KAAK;AAAA,EAC7D;AACJ,CAAC;AACD,SAAS,gBAAgB,MAAM,QAAQ;AACnC,QAAM,QAAQ,OAAO,WAAW,SAAS,OAAO,WAAW;AAC3D,MAAI,SAAS,KAAK,MAAM,MAAM,eAAe,EAAE,OAAO,KAAK,OAAK,EAAE,UAAU,OAAO,MAAM;AACzF,MAAI,EAAE,kBAAkB;AACpB,WAAO;AACX,MAAI,OAAO,SAAS;AAChB,SAAK,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,qBAAqB,KAAK,OAAO,OAAO,OAAO,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,aAAa,iBAAiB,GAAG,OAAO,UAAU,EAAE,CAAC,CAAC;AAAA;AAExK,UAAM,MAAM,OAAO,YAAY,OAAO,MAAM,OAAO,EAAE;AACzD,SAAO;AACX;AACA,IAAM,gBAA6B,kBAAkB,iBAAiB,eAAe;AAMrF,SAAS,wBAAwB,SAAS,KAAK,UAAU;AACrD,SAAO,CAAC,SAAS;AACb,QAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACpD,QAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,OAAO,KAAK,YACvC,KAAK,IAAI,IAAI,OAAO,KAAK,YAAY,KAAK,MAAM,MAAM,gBAAgB,EAAE;AACxE,aAAO;AACX,QAAI,OAAO,GAAG;AACd,QAAI,MAAM,WAAW,UAAU,WAAW,MAAM,OAAO,KAAK,OAAO;AAC/D,aAAO,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,eACtC,QAAQ,IAAI,cAAc,IAAI,EAAE,YAAY,IAAI,CAAC;AACzD,QAAI,EAAE,OAAO,IAAI,OAAO,KAAK;AAC7B,QAAI,WAAW,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW,QAAQ,UAAU,IAAI,MAAM,UAAU,IAAI,SAAS;AACrH,QAAI,WAAW;AACX,iBAAW,MAAM,SAAS,IAAI,SAAS;AAAA,aAClC,YAAY;AACjB,iBAAW,MAAM,SAAS,SAAS,IAAI;AAC3C,SAAK,SAAS,EAAE,SAAS,kBAAkB,GAAG,QAAQ,EAAE,CAAC;AACzD,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,mBAAmB,CAAC,SAAS;AAC/B,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACpD,MAAI,KAAK,MAAM,YAAY,CAAC,UAAU,CAAC,OAAO,QAAQ,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,YAC1F,KAAK,IAAI,IAAI,OAAO,KAAK,YAAY,KAAK,MAAM,MAAM,gBAAgB,EAAE;AACxE,WAAO;AACX,SAAO,gBAAgB,MAAM,OAAO,KAAK,QAAQ,OAAO,KAAK,QAAQ,CAAC;AAC1E;AAIA,IAAM,kBAAkB,CAAC,SAAS;AAC9B,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACpD,MAAI,CAAC;AACD,WAAO;AACX,OAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,IAAI,EAAE,CAAC;AACzD,SAAO;AACX;AAIA,IAAM,kBAAkB,CAAC,SAAS;AAC9B,MAAI,SAAS,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACpD,MAAI,CAAC,UAAU,CAAC,OAAO,OAAO;AAAA,IAAK,OAAK,EAAE,SAAS;AAAA;AAAA,EAAsB;AACrE,WAAO;AACX,OAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,IAAI,EAAE,CAAC;AACzD,SAAO;AACX;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,QAAQ,SAAS;AACzB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,UAAU,CAAC;AAGhB,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,iBAAiB;AAAvB,IAA2B,eAAe;AAC1C,IAAM,mBAAgC,WAAW,UAAU,MAAM;AAAA,EAC7D,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,UAAU,CAAC;AAChB,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,aAAS,UAAU,KAAK,MAAM,MAAM,eAAe,EAAE;AACjD,UAAI,OAAO,SAAS;AAChB,aAAK,WAAW,MAAM;AAAA,EAClC;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,SAAS,OAAO,MAAM,MAAM,eAAe;AAC/C,QAAI,OAAO,OAAO,MAAM,MAAM,gBAAgB;AAC9C,QAAI,CAAC,OAAO,gBAAgB,CAAC,OAAO,cAAc,OAAO,WAAW,MAAM,eAAe,KAAK;AAC1F;AACJ,QAAI,YAAY,OAAO,aAAa,KAAK,QAAM;AAC3C,cAAQ,GAAG,aAAa,GAAG,eAAe,CAAC,aAAa,IAAI,IAAI;AAAA,IACpE,CAAC;AACD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,aACA,MAAM,QAAQ,SAAS,OAAO,aAAa,SAAS,kBAAkB,KAAK,IAAI,IAAI,MAAM,OAAO,cAAc;AAC9G,iBAAS,WAAW,MAAM,QAAQ,gBAAgB;AAC9C,cAAI;AACA,oBAAQ;AAAA,UACZ,SACO,GAAG;AACN,yBAAa,KAAK,KAAK,OAAO,CAAC;AAAA,UACnC;AAAA,QACJ;AACA,cAAM,QAAQ,iBAAiB;AAC/B,aAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,MAC9B,OACK;AACD,cAAM,QAAQ,KAAK,GAAG,OAAO,YAAY;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,KAAK,iBAAiB;AACtB,mBAAa,KAAK,cAAc;AACpC,QAAI,OAAO,aAAa,KAAK,QAAM,GAAG,QAAQ,KAAK,OAAK,EAAE,GAAG,qBAAqB,CAAC,CAAC;AAChF,WAAK,eAAe;AACxB,QAAI,QAAQ,KAAK,eAAe,KAAK,KAAK;AAC1C,SAAK,iBAAiB,OAAO,OAAO,KAAK,OAAK,EAAE,SAAS,KAAyB,CAAC,KAAK,QAAQ,KAAK,OAAK,EAAE,OAAO,UAAU,EAAE,MAAM,CAAC,IAChI,WAAW,MAAM,KAAK,YAAY,GAAG,KAAK,IAAI;AACpD,QAAI,KAAK,aAAa;AAClB,eAAS,MAAM,OAAO,cAAc;AAChC,YAAI,aAAa,IAAI,IAAI,KAAK;AAC1B,eAAK,YAAY;AAAA,iBACZ,KAAK,aAAa,KAAoC,GAAG;AAC9D,eAAK,YAAY;AAAA,MACzB;AAAA,EACR;AAAA,EACA,cAAc;AACV,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,QAAI,EAAE,MAAM,IAAI,KAAK,MAAM,SAAS,MAAM,MAAM,eAAe;AAC/D,aAAS,UAAU,OAAO,QAAQ;AAC9B,UAAI,OAAO,SAAS,KAAyB,CAAC,KAAK,QAAQ,KAAK,OAAK,EAAE,OAAO,UAAU,OAAO,MAAM;AACjG,aAAK,WAAW,MAAM;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,WAAW,QAAQ;AACf,QAAI,EAAE,MAAM,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK;AAC1C,QAAI,UAAU,IAAI,kBAAkB,OAAO,KAAK,OAAO,eAAe,GAAG;AACzE,QAAI,UAAU,IAAI,aAAa,QAAQ,OAAO;AAC9C,SAAK,QAAQ,KAAK,OAAO;AACzB,YAAQ,QAAQ,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,YAAU;AACnD,UAAI,CAAC,QAAQ,QAAQ,SAAS;AAC1B,gBAAQ,OAAO,UAAU;AACzB,aAAK,eAAe;AAAA,MACxB;AAAA,IACJ,GAAG,SAAO;AACN,WAAK,KAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,IAAI,EAAE,CAAC;AAC9D,mBAAa,KAAK,KAAK,OAAO,GAAG;AAAA,IACrC,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,QAAQ,MAAM,OAAK,EAAE,SAAS,MAAS;AAC5C,WAAK,OAAO;AAAA,aACP,KAAK,iBAAiB;AAC3B,WAAK,iBAAiB,WAAW,MAAM,KAAK,OAAO,GAAG,KAAK,KAAK,MAAM,MAAM,gBAAgB,EAAE,cAAc;AAAA,EACpH;AAAA;AAAA;AAAA,EAGA,SAAS;AACL,QAAI;AACJ,QAAI,KAAK,iBAAiB;AACtB,mBAAa,KAAK,cAAc;AACpC,SAAK,iBAAiB;AACtB,QAAI,UAAU,CAAC;AACf,QAAI,OAAO,KAAK,KAAK,MAAM,MAAM,gBAAgB;AACjD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,UAAI,MAAM,SAAS;AACf;AACJ,WAAK,QAAQ,OAAO,KAAK,CAAC;AAC1B,UAAI,MAAM,MAAM;AACZ,YAAI,SAAS,IAAI,aAAa,MAAM,OAAO,QAAQ,MAAM,OAAO,aAAa,MAAM,MAAM,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ,QAAQ,OAAO,SAAS,KAAK,IAAI,MAAM,QAAQ,SAAS,MAAM,QAAQ,CAAC,EAAE,aAAa,KAAK,KAAK,KAAK,CAAC;AAGzO,iBAAS,MAAM,MAAM;AACjB,mBAAS,OAAO,OAAO,IAAI,IAAI;AACnC,YAAI,OAAO,UAAU,GAAG;AACpB,kBAAQ,KAAK,MAAM;AACnB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,KAAK,MAAM,MAAM,eAAe,EAAE,OAAO,KAAK,OAAK,EAAE,UAAU,MAAM,OAAO,MAAM;AACrG,UAAI,WAAW,QAAQ,SAAS,GAAuB;AACnD,YAAI,MAAM,QAAQ,MAAM;AAGpB,cAAI,SAAS,IAAI;AAAA,YAAa,MAAM,OAAO;AAAA,YAAQ;AAAA;AAAA,UAAsB;AACzE,mBAAS,MAAM,MAAM;AACjB,qBAAS,OAAO,OAAO,IAAI,IAAI;AACnC,cAAI,OAAO,SAAS;AAChB,oBAAQ,KAAK,MAAM;AAAA,QAC3B,OACK;AAED,eAAK,WAAW,OAAO;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ;AACR,WAAK,KAAK,SAAS,EAAE,SAAS,gBAAgB,GAAG,OAAO,EAAE,CAAC;AAAA,EACnE;AACJ,GAAG;AAAA,EACC,eAAe;AAAA,IACX,KAAK,OAAO;AACR,UAAI,QAAQ,KAAK,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACxD,UAAI,SAAS,MAAM,WAAW,KAAK,KAAK,MAAM,MAAM,gBAAgB,EAAE,aAAa;AAC/E,YAAI,SAAS,MAAM,QAAQ,WAAW,KAAK,MAAM,MAAM,KAAK,OAAO;AACnE,YAAI,CAAC,UAAU,CAAC,OAAO,IAAI,SAAS,MAAM,aAAa;AACnD,qBAAW,MAAM,KAAK,KAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE;AAAA,MAC5F;AAAA,IACJ;AAAA,IACA,mBAAmB;AACf,WAAK,YAAY;AAAA,IACrB;AAAA,IACA,iBAAiB;AACb,UAAI,KAAK,aAAa,GAA0C;AAG5D,mBAAW,MAAM,KAAK,KAAK,SAAS,EAAE,SAAS,sBAAsB,GAAG,KAAK,EAAE,CAAC,GAAG,EAAE;AAAA,MACzF;AACA,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AACJ,CAAC;AACD,IAAM,UAAU,OAAO,aAAa,YAAyB,MAAM,KAAK,UAAU,QAAQ;AAC1F,IAAM,mBAAgC,KAAK,QAAqB,WAAW,iBAAiB;AAAA,EACxF,QAAQ,OAAO,MAAM;AACjB,QAAI,QAAQ,KAAK,MAAM,MAAM,iBAAiB,KAAK;AACnD,QAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,MAAM,KAAK,YAAY,MAAM,KAAK,WAAW,KACtE,MAAM,IAAI,SAAS,KAAK,MAAM,WAAW,EAAE,WAAW,MAAM,WAAW,MAAM;AAC7E,aAAO;AACX,QAAI,SAAS,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ;AACnD,QAAI,SAAS,MAAM,OAAO,KAAK,OAAK,EAAE,UAAU,OAAO,MAAM;AAC7D,QAAI,cAAc,OAAO,WAAW,oBAAoB,OAAO,OAAO;AACtE,QAAI,eAAe,YAAY,QAAQ,MAAM,GAAG,IAAI;AAChD,sBAAgB,MAAM,MAAM;AAChC,WAAO;AAAA,EACX;AACJ,CAAC,CAAC;AAEF,IAAM,YAAyB,WAAW,UAAU;AAAA,EAChD,uCAAuC;AAAA,IACnC,UAAU;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,kCAAkC;AAAA,QAC9B,SAAS;AAAA,QACT,YAAY;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,QACN,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ;AAAA,MACZ;AAAA,MACA,0BAA0B;AAAA,QACtB,SAAS;AAAA,QACT,cAAc;AAAA,QACd,aAAa;AAAA,QACb,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,wDAAwD;AAAA,IACpD,YAAY;AAAA,IACZ,OAAO;AAAA,EACX;AAAA,EACA,iEAAiE;AAAA,IAC7D,YAAY;AAAA,EAChB;AAAA,EACA,uDAAuD;AAAA,IACnD,YAAY;AAAA,IACZ,OAAO;AAAA,EACX;AAAA,EACA,gEAAgE;AAAA,IAC5D,YAAY;AAAA,EAChB;AAAA,EACA,oFAAoF;AAAA,IAChF,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAAA,EACA,iCAAiC;AAAA,IAC7B,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,GAAG,GAAoB;AAAA,IACjC,WAAW;AAAA,EACf;AAAA,EACA,6CAA6C,EAAE,OAAO,OAAO;AAAA,EAC7D,8CAA8C,EAAE,MAAM,OAAO;AAAA,EAC7D,oDAAoD,EAAE,OAAO,GAAG,EAAoB,KAAK;AAAA,EACzF,qDAAqD,EAAE,MAAM,GAAG,EAAoB,KAAK;AAAA,EACzF,2BAA2B,EAAE,iBAAiB,YAAY;AAAA,EAC1D,0BAA0B,EAAE,iBAAiB,YAAY;AAAA,EACzD,4BAA4B;AAAA,IACxB,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,EAChB;AAAA,EACA,6BAA6B;AAAA,IACzB,gBAAgB;AAAA,EACpB;AAAA,EACA,wBAAwB;AAAA,IACpB,YAAY;AAAA,IACZ,WAAW;AAAA,EACf;AAAA,EACA,sBAAsB;AAAA,IAClB,UAAU;AAAA,IACV,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAAA,EACA,0DAA0D;AAAA,IACtD,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,4BAA4B;AAAA,IACxB,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,IAC5B,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,+BAA+B;AAAA,IAC3B,WAAW,EAAE,SAAS,OAAO;AAAA,EACjC;AAAA,EACA,+BAA+B;AAAA,IAC3B,WAAW,EAAE,SAAS,OAAO;AAAA,EACjC;AAAA,EACA,2BAA2B;AAAA,IACvB,WAAW,EAAE,SAAS,OAAO;AAAA,EACjC;AAAA,EACA,2BAA2B;AAAA,IACvB,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,+BAA+B;AAAA,IAC3B,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,8BAA8B;AAAA,IAC1B,WAAW,EAAE,SAAS,QAAa;AAAA;AAAA,EACvC;AAAA,EACA,gCAAgC;AAAA,IAC5B,WAAW,EAAE,SAAS,MAAM;AAAA,EAChC;AAAA,EACA,2BAA2B;AAAA,IACvB,WAAW,EAAE,SAAS,SAAS,UAAU,OAAO,eAAe,SAAS;AAAA,EAC5E;AACJ,CAAC;AAED,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,OAAO,MAAM,MAAM,IAAI;AAC/B,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACd;AACJ;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACb,YAAY,OAAO,MAAM,IAAI;AACzB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,QAAQ;AACzD,QAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,GAAG,QAAQ,QAAQ;AACpD,WAAO,QAAQ,QAAQ,MAAM,OAAO,OAAO,IAAI,YAAW,KAAK,OAAO,MAAM,EAAE;AAAA,EAClF;AACJ;AACA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACV,YAAY,OAAO,gBAAgB;AAC/B,SAAK,QAAQ;AACb,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,YAAY,OAAO,KAAK;AACpB,QAAI,OAAO,CAAC,GAAG,YAAY,CAAC,GAAG;AAC/B,QAAI,UAAU,MAAM,IAAI,OAAO,GAAG,GAAG,aAAa,OAAO,KAAK,QAAQ,IAAI,EAAE,CAAC;AAC7E,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,KAAK,QAAQ;AACb,YAAI,SAAS,YAAY,OAAO,OAAO,KAAK,IAAI,EAAE,CAAC,EAAE;AACrD,iBAAS,IAAI,GAAG,IAAI,MAAM;AACtB,oBAAU,MAAM,MAAM,UAAU;AACpC,kBAAU,KAAK,MAAM,OAAO,SAAS,IAAI;AACzC,eAAO,SAAS,KAAK,MAAM,IAAI;AAAA,MACnC;AACA,WAAK,KAAK,IAAI;AACd,aAAO,KAAK,SAAS;AAAA,IACzB;AACA,QAAI,SAAS,KAAK,eAAe,IAAI,CAAAC,SAAO,IAAI,WAAWA,KAAI,OAAO,UAAUA,KAAI,IAAI,IAAIA,KAAI,MAAM,UAAUA,KAAI,IAAI,IAAIA,KAAI,EAAE,CAAC;AACnI,WAAO,EAAE,MAAM,OAAO;AAAA,EAC1B;AAAA,EACA,OAAO,MAAM,UAAU;AACnB,QAAI,SAAS,CAAC;AACd,QAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,GAAG;AAChC,aAAS,QAAQ,SAAS,MAAM,UAAU,GAAG;AACzC,aAAO,IAAI,yCAAyC,KAAK,IAAI,GAAG;AAC5D,YAAI,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,QAAQ;AAClE,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,cAAI,OAAO,OAAO,OAAO,CAAC,EAAE,OAAO,MAAM,OAAO,OAAO,CAAC,EAAE,QAAQ,OAAO;AACrE,oBAAQ;AAAA,QAChB;AACA,YAAI,QAAQ,GAAG;AACX,cAAI,IAAI;AACR,iBAAO,IAAI,OAAO,WAAW,OAAO,QAAS,OAAO,CAAC,EAAE,OAAO,QAAQ,OAAO,CAAC,EAAE,MAAM;AAClF;AACJ,iBAAO,OAAO,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC;AACjC,kBAAQ;AACR,mBAAS,OAAO;AACZ,gBAAI,IAAI,SAAS;AACb,kBAAI;AAAA,QAChB;AACA,kBAAU,KAAK,IAAI,SAAS,OAAO,MAAM,QAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK,MAAM,CAAC;AAChF,eAAO,KAAK,MAAM,GAAG,EAAE,KAAK,IAAI,OAAO,KAAK,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM;AAAA,MAC3E;AACA,eAAS,KAAK,MAAM,WAAW,KAAK,IAAI,KAAI;AACxC,eAAO,KAAK,MAAM,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,QAAQ,IAAI,CAAC,EAAE,MAAM;AAC/E,iBAAS,OAAO;AACZ,cAAI,IAAI,QAAQ,MAAM,UAAU,IAAI,OAAO,IAAI,OAAO;AAClD,gBAAI;AACJ,gBAAI;AAAA,UACR;AAAA,MACR;AACA,YAAM,KAAK,IAAI;AAAA,IACnB;AACA,WAAO,IAAI,SAAQ,OAAO,SAAS;AAAA,EACvC;AACJ;AACA,IAAI,cAA2B,WAAW,OAAO,EAAE,QAAqB,IAAI,cAAc,WAAW;AAAA,EAC7F,QAAQ;AACJ,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AAAE,WAAO;AAAA,EAAO;AAClC,IAAE,CAAC;AACP,IAAI,aAA0B,WAAW,KAAK,EAAE,OAAO,kBAAkB,CAAC;AAC1E,IAAM,gBAAN,MAAM,eAAc;AAAA,EAChB,YAAY,QAAQ,QAAQ;AACxB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,OAAO,WAAW,IAAI,OAAO,IAAI,QAAM,EAAE,QAAQ,EAAE,KAAK,cAAc,YAAY,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AAAA,EAC/G;AAAA,EACA,IAAI,SAAS;AACT,QAAI,SAAS,CAAC;AACd,aAAS,KAAK,KAAK,QAAQ;AACvB,UAAI,SAAS,EAAE,IAAI,OAAO;AAC1B,UAAI,CAAC;AACD,eAAO;AACX,aAAO,KAAK,MAAM;AAAA,IACtB;AACA,WAAO,IAAI,eAAc,QAAQ,KAAK,MAAM;AAAA,EAChD;AAAA,EACA,qBAAqB,KAAK;AACtB,WAAO,IAAI,OAAO,MAAM,WAAS,KAAK,OAAO,KAAK,OAAK,EAAE,SAAS,KAAK,UAAU,EAAE,QAAQ,MAAM,QAAQ,EAAE,MAAM,MAAM,EAAE,CAAC;AAAA,EAC9H;AACJ;AACA,IAAM,YAAyB,YAAY,OAAO;AAAA,EAC9C,IAAI,OAAO,SAAS;AAAE,WAAO,SAAS,MAAM,IAAI,OAAO;AAAA,EAAG;AAC9D,CAAC;AACD,IAAM,cAA2B,YAAY,OAAO;AACpD,IAAM,eAA4B,WAAW,OAAO;AAAA,EAChD,SAAS;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,OAAO,OAAO,IAAI;AACd,aAAS,UAAU,GAAG,SAAS;AAC3B,UAAI,OAAO,GAAG,SAAS;AACnB,eAAO,OAAO;AAClB,UAAI,OAAO,GAAG,WAAW,KAAK;AAC1B,eAAO,IAAI,cAAc,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC3D;AACA,QAAI,SAAS,GAAG;AACZ,cAAQ,MAAM,IAAI,GAAG,OAAO;AAChC,QAAI,SAAS,GAAG,aAAa,CAAC,MAAM,qBAAqB,GAAG,SAAS;AACjE,cAAQ;AACZ,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,WAAW,YAAY,KAAK,GAAG,SAAO,MAAM,IAAI,OAAO,WAAW,IAAI;AACxF,CAAC;AACD,SAAS,eAAe,QAAQ,OAAO;AACnC,SAAO,gBAAgB,OAAO,OAAO,OAAO,OAAK,EAAE,SAAS,KAAK,EAAE,IAAI,OAAK,gBAAgB,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;AACpH;AA+BA,SAAS,QAAQ,UAAU;AACvB,MAAIC,WAAU,QAAQ,MAAM,QAAQ;AACpC,SAAO,CAAC,QAAQ,YAAY,MAAM,OAAO;AACrC,QAAI,EAAE,MAAM,OAAO,IAAIA,SAAQ,YAAY,OAAO,OAAO,IAAI;AAC7D,QAAI,OAAO;AAAA,MACP,SAAS,EAAE,MAAM,IAAI,QAAQ,KAAK,GAAG,IAAI,EAAE;AAAA,MAC3C,gBAAgB;AAAA,MAChB,aAAa,aAAa,CAAC,iBAAiB,GAAG,UAAU,GAAG,YAAY,UAAU,GAAG,gBAAgB,CAAC,IAAI;AAAA,IAC9G;AACA,QAAI,OAAO;AACP,WAAK,YAAY,eAAe,QAAQ,CAAC;AAC7C,QAAI,OAAO,KAAK,OAAK,EAAE,QAAQ,CAAC,GAAG;AAC/B,UAAI,SAAS,IAAI,cAAc,QAAQ,CAAC;AACxC,UAAI,UAAU,KAAK,UAAU,CAAC,UAAU,GAAG,MAAM,CAAC;AAClD,UAAI,OAAO,MAAM,MAAM,cAAc,KAAK,MAAM;AAC5C,gBAAQ,KAAK,YAAY,aAAa,GAAG,CAAC,cAAc,kBAAkB,uBAAuB,SAAS,CAAC,CAAC;AAAA,IACpH;AACA,WAAO,SAAS,OAAO,MAAM,OAAO,IAAI,CAAC;AAAA,EAC7C;AACJ;AACA,SAAS,UAAU,KAAK;AACpB,SAAO,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5B,QAAI,SAAS,MAAM,MAAM,cAAc,KAAK;AAC5C,QAAI,CAAC,UAAU,MAAM,KAAK,OAAO,UAAU;AACvC,aAAO;AACX,QAAI,OAAO,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK,CAAC,OAAO,OAAO,KAAK,OAAK,EAAE,SAAS,OAAO,GAAG;AAChG,aAAS,MAAM,OAAO;AAAA,MAClB,WAAW,eAAe,OAAO,QAAQ,IAAI;AAAA,MAC7C,SAAS,UAAU,GAAG,OAAO,OAAO,IAAI,cAAc,OAAO,QAAQ,IAAI,CAAC;AAAA,MAC1E,gBAAgB;AAAA,IACpB,CAAC,CAAC;AACF,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,eAAe,CAAC,EAAE,OAAO,SAAS,MAAM;AAC1C,MAAI,SAAS,MAAM,MAAM,cAAc,KAAK;AAC5C,MAAI,CAAC;AACD,WAAO;AACX,WAAS,MAAM,OAAO,EAAE,SAAS,UAAU,GAAG,IAAI,EAAE,CAAC,CAAC;AACtD,SAAO;AACX;AAIA,IAAM,mBAAgC,UAAU,CAAC;AAIjD,IAAM,mBAAgC,UAAU,EAAE;AAiBlD,IAAM,uBAAuB;AAAA,EACzB,EAAE,KAAK,OAAO,KAAK,kBAAkB,OAAO,iBAAiB;AAAA,EAC7D,EAAE,KAAK,UAAU,KAAK,aAAa;AACvC;AAQA,IAAM,gBAA6B,MAAM,OAAO;AAAA,EAC5C,QAAQ,MAAM;AAAE,WAAO,KAAK,SAAS,KAAK,CAAC,IAAI;AAAA,EAAsB;AACzE,CAAC;AACD,IAAM,mBAAgC,KAAK,QAAqB,OAAO,QAAQ,CAAC,aAAa,GAAG,WAAS,MAAM,MAAM,aAAa,CAAC,CAAC;AAMpI,SAAS,kBAAkB,UAAU,YAAY;AAC7C,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AACpF;AACA,IAAM,wBAAqC,WAAW,iBAAiB;AAAA,EACnE,UAAU,OAAO,MAAM;AACnB,QAAI,SAAS,KAAK,MAAM,MAAM,cAAc,KAAK,GAAG;AACpD,QAAI,CAAC,WAAW,MAAM,KAAK,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,MAAM;AAC/E,aAAO;AACX,QAAI,QAAQ,OAAO,OAAO,KAAK,OAAK,EAAE,QAAQ,OAAO,EAAE,MAAM,GAAG;AAChE,QAAI,CAAC,SAAS,MAAM,SAAS,OAAO;AAChC,aAAO;AACX,SAAK,SAAS;AAAA,MACV,WAAW,eAAe,OAAO,QAAQ,MAAM,KAAK;AAAA,MACpD,SAAS,UAAU,GAAG,OAAO,OAAO,KAAK,OAAK,EAAE,QAAQ,MAAM,KAAK,IAC7D,IAAI,cAAc,OAAO,QAAQ,MAAM,KAAK,IAAI,IAAI;AAAA,MAC1D,gBAAgB;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AA+ED,IAAM,WAAW;AAAA,EACb,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAClC,QAAQ;AAAA,EACR,gBAAgB,CAAC;AACrB;AACA,IAAM,qBAAkC,YAAY,OAAO;AAAA,EACvD,IAAI,OAAO,SAAS;AAChB,QAAI,SAAS,QAAQ,OAAO,OAAO,IAAI,QAAQ,UAAU;AACzD,WAAO,UAAU,OAAO,SAAY;AAAA,EACxC;AACJ,CAAC;AACD,IAAM,gBAA6B,IAAI,cAAc,WAAW;AAChE;AACA,cAAc,YAAY;AAC1B,cAAc,UAAU;AACxB,IAAM,eAA4B,WAAW,OAAO;AAAA,EAChD,SAAS;AAAE,WAAO,SAAS;AAAA,EAAO;AAAA,EAClC,OAAO,OAAO,IAAI;AACd,YAAQ,MAAM,IAAI,GAAG,OAAO;AAC5B,QAAI,GAAG,WAAW;AACd,UAAI,OAAO,GAAG,MAAM,IAAI,OAAO,GAAG,UAAU,KAAK,IAAI;AACrD,cAAQ,MAAM,OAAO,EAAE,QAAQ,UAAQ,QAAQ,KAAK,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAAA,IACjF;AACA,aAAS,UAAU,GAAG;AAClB,UAAI,OAAO,GAAG,kBAAkB;AAC5B,gBAAQ,MAAM,OAAO,EAAE,KAAK,CAAC,cAAc,MAAM,OAAO,OAAO,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;AAC3F,WAAO;AAAA,EACX;AACJ,CAAC;AAQD,SAAS,gBAAgB;AACrB,SAAO,CAAC,cAAc,YAAY;AACtC;AACA,IAAM,iBAAiB;AACvB,SAAS,QAAQ,IAAI;AACjB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,QAAI,eAAe,WAAW,CAAC,KAAK;AAChC,aAAO,eAAe,OAAO,IAAI,CAAC;AAC1C,SAAO,cAAc,KAAK,MAAM,KAAK,KAAK,CAAC;AAC/C;AACA,SAAS,OAAO,OAAO,KAAK;AACxB,SAAO,MAAM,eAAe,iBAAiB,GAAG,EAAE,CAAC,KAAK;AAC5D;AACA,IAAM,UAAU,OAAO,aAAa,YAAyB,YAAY,KAAK,UAAU,SAAS;AACjG,IAAM,eAA4B,WAAW,aAAa,GAAG,CAAC,MAAM,MAAM,IAAI,WAAW;AACrF,OAAK,UAAU,KAAK,YAAY,KAAK,uBAAuB,KAAK,MAAM;AACnE,WAAO;AACX,MAAI,MAAM,KAAK,MAAM,UAAU;AAC/B,MAAI,OAAO,SAAS,KAAK,OAAO,UAAU,KAAK,cAAc,YAAY,QAAQ,CAAC,CAAC,KAAK,KACpF,QAAQ,IAAI,QAAQ,MAAM,IAAI;AAC9B,WAAO;AACX,MAAI,KAAK,cAAc,KAAK,OAAO,MAAM;AACzC,MAAI,CAAC;AACD,WAAO;AACX,OAAK,SAAS,EAAE;AAChB,SAAO;AACX,CAAC;AAKD,IAAM,oBAAoB,CAAC,EAAE,OAAO,SAAS,MAAM;AAC/C,MAAI,MAAM;AACN,WAAO;AACX,MAAI,OAAO,OAAO,OAAO,MAAM,UAAU,KAAK,IAAI;AAClD,MAAI,SAAS,KAAK,YAAY,SAAS;AACvC,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,MAAM,OAAO;AACb,UAAI,SAAS,SAAS,MAAM,KAAK,MAAM,IAAI;AAC3C,eAAS,SAAS,QAAQ;AACtB,YAAI,SAAS,UAAU,SAAS,MAAM,KAAK,MAAM,IAAI,KAAK,QAAQ,YAAY,OAAO,CAAC,CAAC;AACnF,iBAAO;AAAA,YAAE,SAAS,EAAE,MAAM,MAAM,OAAO,MAAM,QAAQ,IAAI,MAAM,OAAO,MAAM,OAAO;AAAA,YAC/E,OAAO,gBAAgB,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,UAAE;AAAA,MACrE;AAAA,IACJ;AACA,WAAO,EAAE,OAAO,OAAO,MAAM;AAAA,EACjC,CAAC;AACD,MAAI,CAAC;AACD,aAAS,MAAM,OAAO,SAAS,EAAE,gBAAgB,MAAM,WAAW,kBAAkB,CAAC,CAAC;AAC1F,SAAO,CAAC;AACZ;AAKA,IAAM,sBAAsB;AAAA,EACxB,EAAE,KAAK,aAAa,KAAK,kBAAkB;AAC/C;AAYA,SAAS,cAAc,OAAO,SAAS;AACnC,MAAI,OAAO,OAAO,OAAO,MAAM,UAAU,KAAK,IAAI;AAClD,MAAI,SAAS,KAAK,YAAY,SAAS;AACvC,WAAS,OAAO,QAAQ;AACpB,QAAI,SAAS,QAAQ,YAAY,KAAK,CAAC,CAAC;AACxC,QAAI,WAAW;AACX,aAAO,UAAU,MAAM,WAAW,OAAO,KAAK,OAAO,QAAQ,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,IAClF,WAAW,OAAO,KAAK,QAAQ,KAAK,UAAU,SAAS,MAAM;AACvE,QAAI,WAAW,UAAU,gBAAgB,OAAO,MAAM,UAAU,KAAK,IAAI;AACrE,aAAO,YAAY,OAAO,KAAK,MAAM;AAAA,EAC7C;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,OAAO,KAAK;AACjC,MAAI,QAAQ;AACZ,QAAM,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,IAAI,QAAQ,UAAQ;AAC3D,QAAI,QAAQ;AACR,cAAQ;AAAA,EAChB,CAAC;AACD,SAAO;AACX;AACA,SAAS,SAAS,KAAK,KAAK;AACxB,MAAI,OAAO,IAAI,YAAY,KAAK,MAAM,CAAC;AACvC,SAAO,KAAK,MAAM,GAAG,cAAc,YAAY,MAAM,CAAC,CAAC,CAAC;AAC5D;AACA,SAAS,SAAS,KAAK,KAAK;AACxB,MAAI,OAAO,IAAI,YAAY,MAAM,GAAG,GAAG;AACvC,SAAO,cAAc,YAAY,MAAM,CAAC,CAAC,KAAK,KAAK,SAAS,OAAO,KAAK,MAAM,CAAC;AACnF;AACA,SAAS,WAAW,OAAO,MAAM,OAAO,aAAa;AACjD,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,QAAE,SAAS,CAAC,EAAE,QAAQ,MAAM,MAAM,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,MAAM,MAAM,GAAG,CAAC;AAAA,QACpF,SAAS,mBAAmB,GAAG,MAAM,KAAK,KAAK,MAAM;AAAA,QACrD,OAAO,gBAAgB,MAAM,MAAM,SAAS,KAAK,QAAQ,MAAM,OAAO,KAAK,MAAM;AAAA,MAAE;AAC3F,QAAI,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI;AACzC,QAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI;AACxD,aAAO;AAAA,QAAE,SAAS,EAAE,QAAQ,OAAO,OAAO,MAAM,MAAM,KAAK;AAAA,QACvD,SAAS,mBAAmB,GAAG,MAAM,OAAO,KAAK,MAAM;AAAA,QACvD,OAAO,gBAAgB,OAAO,MAAM,OAAO,KAAK,MAAM;AAAA,MAAE;AAChE,WAAO,EAAE,OAAO,OAAO,MAAM;AAAA,EACjC,CAAC;AACD,SAAO,OAAO,OAAO,MAAM,OAAO,SAAS;AAAA,IACvC,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACf,CAAC;AACL;AACA,SAAS,YAAY,OAAO,OAAO,OAAO;AACtC,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,MAAM,SAAS,SAAS,MAAM,KAAK,MAAM,IAAI,KAAK;AAClD,aAAO;AAAA,QAAE,SAAS,EAAE,MAAM,MAAM,MAAM,IAAI,MAAM,OAAO,MAAM,QAAQ,QAAQ,MAAM;AAAA,QAC/E,OAAO,gBAAgB,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,MAAE;AACjE,WAAO,OAAO,EAAE,MAAM;AAAA,EAC1B,CAAC;AACD,SAAO,OAAO,OAAO,MAAM,OAAO,SAAS;AAAA,IACvC,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACf,CAAC;AACL;AAGA,SAAS,WAAW,OAAO,OAAO,aAAaC,SAAQ;AACnD,MAAI,iBAAiBA,QAAO,kBAAkB,SAAS;AACvD,MAAI,OAAO,MAAM,UAAU,MAAM,cAAc,WAAS;AACpD,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,QAAE,SAAS,CAAC,EAAE,QAAQ,OAAO,MAAM,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,MAAM,MAAM,GAAG,CAAC;AAAA,QACrF,SAAS,mBAAmB,GAAG,MAAM,KAAK,MAAM,MAAM;AAAA,QACtD,OAAO,gBAAgB,MAAM,MAAM,SAAS,MAAM,QAAQ,MAAM,OAAO,MAAM,MAAM;AAAA,MAAE;AAC7F,QAAI,MAAM,MAAM,MAAM,OAAO,SAAS,MAAM,KAAK,GAAG,GAAG;AACvD,QAAI,QAAQ,OAAO;AACf,UAAI,UAAU,OAAO,GAAG,GAAG;AACvB,eAAO;AAAA,UAAE,SAAS,EAAE,QAAQ,QAAQ,OAAO,MAAM,IAAI;AAAA,UACjD,SAAS,mBAAmB,GAAG,MAAM,MAAM,MAAM;AAAA,UACjD,OAAO,gBAAgB,OAAO,MAAM,MAAM,MAAM;AAAA,QAAE;AAAA,MAC1D,WACS,gBAAgB,OAAO,GAAG,GAAG;AAClC,YAAI,WAAW,eAAe,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,KAAK,QAAQ,QAAQ;AAC7F,YAAI,UAAU,WAAW,QAAQ,QAAQ,QAAQ;AACjD,eAAO;AAAA,UAAE,SAAS,EAAE,MAAM,KAAK,IAAI,MAAM,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,UACrE,OAAO,gBAAgB,OAAO,MAAM,QAAQ,MAAM;AAAA,QAAE;AAAA,MAC5D;AAAA,IACJ,WACS,eAAe,MAAM,SAAS,MAAM,IAAI,MAAM,QAAQ,GAAG,KAAK,QAAQ,UAC1E,QAAQ,iBAAiB,OAAO,MAAM,IAAI,MAAM,QAAQ,cAAc,KAAK,MAC5E,UAAU,OAAO,KAAK,GAAG;AACzB,aAAO;AAAA,QAAE,SAAS,EAAE,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,MAAM,IAAI;AAAA,QACjE,SAAS,mBAAmB,GAAG,MAAM,MAAM,MAAM;AAAA,QACjD,OAAO,gBAAgB,OAAO,MAAM,MAAM,MAAM;AAAA,MAAE;AAAA,IAC1D,WACS,MAAM,gBAAgB,GAAG,EAAE,IAAI,KAAK,aAAa,MAAM;AAC5D,UAAI,iBAAiB,OAAO,KAAK,cAAc,IAAI,MAAM,CAAC,iBAAiB,OAAO,KAAK,OAAO,cAAc;AACxG,eAAO;AAAA,UAAE,SAAS,EAAE,QAAQ,QAAQ,OAAO,MAAM,IAAI;AAAA,UACjD,SAAS,mBAAmB,GAAG,MAAM,MAAM,MAAM;AAAA,UACjD,OAAO,gBAAgB,OAAO,MAAM,MAAM,MAAM;AAAA,QAAE;AAAA,IAC9D;AACA,WAAO,EAAE,OAAO,OAAO,MAAM;AAAA,EACjC,CAAC;AACD,SAAO,OAAO,OAAO,MAAM,OAAO,SAAS;AAAA,IACvC,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACf,CAAC;AACL;AACA,SAAS,UAAU,OAAO,KAAK;AAC3B,MAAI,OAAO,WAAW,KAAK,EAAE,aAAa,MAAM,CAAC;AACjD,SAAO,KAAK,UAAU,KAAK,QAAQ;AACvC;AACA,SAAS,iBAAiB,OAAO,KAAK,YAAY,UAAU;AACxD,MAAI,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE;AACjD,MAAI,YAAY,SAAS,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC;AAClE,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,QAAQ,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,WAAW,SAAS,SAAS,CAAC;AAClG,QAAI,WAAW,MAAM,QAAQ,UAAU;AACvC,QAAI,CAAC,YAAY,WAAW,MAAM,SAAS,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,IAAI;AAC/E,UAAI,QAAQ,KAAK;AACjB,aAAO,SAAS,MAAM,QAAQ,KAAK,QAAQ,MAAM,KAAK,MAAM,OAAO,WAAW,SAAS,UAAU;AAC7F,YAAI,MAAM,SAAS,MAAM,KAAK,WAAW,QAAQ,MAAM,EAAE,KAAK;AAC1D,iBAAO;AACX,gBAAQ,MAAM;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AACA,QAAI,SAAS,KAAK,MAAM,OAAO,KAAK;AACpC,QAAI,CAAC;AACD;AACJ,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,OAAO,KAAK,UAAU;AAC5C,MAAI,UAAU,MAAM,gBAAgB,GAAG;AACvC,MAAI,QAAQ,MAAM,SAAS,MAAM,GAAG,GAAG,CAAC,KAAK,aAAa;AACtD,WAAO;AACX,WAAS,UAAU,UAAU;AACzB,QAAI,QAAQ,MAAM,OAAO;AACzB,QAAI,MAAM,SAAS,OAAO,GAAG,KAAK,UAAU,QAAQ,MAAM,SAAS,QAAQ,GAAG,KAAK,CAAC,KAAK,aAAa;AAClG,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAKA,SAAS,eAAeA,UAAS,CAAC,GAAG;AACjC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,iBAAiB,GAAGA,OAAM;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAYA,IAAM,mBAAmB;AAAA,EACrB,EAAE,KAAK,cAAc,KAAK,gBAAgB;AAAA,EAC1C,EAAE,KAAK,UAAU,KAAK,gBAAgB;AAAA,EACtC,EAAE,KAAK,aAAa,KAAkB,wBAAwB,IAAI,EAAE;AAAA,EACpE,EAAE,KAAK,WAAW,KAAkB,wBAAwB,KAAK,EAAE;AAAA,EACnE,EAAE,KAAK,YAAY,KAAkB,wBAAwB,MAAM,MAAM,EAAE;AAAA,EAC3E,EAAE,KAAK,UAAU,KAAkB,wBAAwB,OAAO,MAAM,EAAE;AAAA,EAC1E,EAAE,KAAK,SAAS,KAAK,iBAAiB;AAC1C;AACA,IAAM,sBAAmC,KAAK,QAAqB,OAAO,SAAS,CAAC,gBAAgB,GAAG,WAAS,MAAM,MAAM,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC;", "names": ["score", "config", "off", "applyCompletion", "options", "cur", "pos", "snippet", "config"]}