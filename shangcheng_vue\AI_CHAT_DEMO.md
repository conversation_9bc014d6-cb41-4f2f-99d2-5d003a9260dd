# 🤖 AI智能客服功能演示

## ✨ 功能特点

### 🎯 **主要功能**
- **智能对话**：基于阿里云通义千问大模型，提供智能客服服务
- **流式响应**：实时显示AI回复，提升用户体验
- **会话记忆**：支持上下文对话，AI能记住之前的对话内容
- **美观界面**：仿微信聊天界面，用户体验友好
- **全局可用**：在任何页面都可以通过右侧悬浮按钮打开

### 🚀 **技术特性**
- **Vue 3 + Composition API**：现代化前端框架
- **Server-Sent Events (SSE)**：流式数据传输
- **响应式设计**：支持桌面端和移动端
- **优雅动画**：流畅的弹窗动画效果

## 📱 **使用方法**

### **1. 打开AI客服**
- 在任何页面右侧找到悬浮操作按钮
- 点击"客服"按钮（机器人图标）
- 弹窗将从右侧滑入

### **2. 开始对话**
- 在输入框中输入您的问题
- 按回车键或点击发送
- AI将实时回复您的问题

### **3. 功能操作**
- **表情支持**：点击笑脸图标添加表情
- **关闭对话**：点击右上角X按钮
- **滚动查看**：自动滚动到最新消息

## 🎨 **界面展示**

```
┌─────────────────────────────────────┐
│ 🤖 智能客服                    ✕    │
├─────────────────────────────────────┤
│                                     │
│ 🤖 您好！👋 我是您的专属客服，      │
│    有什么可以帮您？                 │
│                                     │
│                     您好，我想咨询 📱│
│                     一下退货流程。   │
│                                     │
│ 🤖 好的，请您提供一下您的订单号，    │
│    我为您查询一下。                 │
│                                     │
│                                     │
├─────────────────────────────────────┤
│ ➕  输入信息...              😊    │
└─────────────────────────────────────┘
```

## 🔧 **技术实现**

### **前端组件**
- `AiChatDialog.vue` - 主聊天弹窗组件
- `FloatingActionButtons.vue` - 悬浮操作按钮
- `ai.js` - AI API接口封装

### **后端API**
- `POST /api/ai/sessions` - 创建AI会话
- `POST /api/ai/chat` - 发送消息（非流式）
- `POST /api/ai/chat/stream` - 发送消息（流式）
- `GET /api/ai/sessions` - 获取会话列表
- `DELETE /api/ai/sessions/{id}` - 删除会话

### **核心特性**
```javascript
// 流式消息发送
sendAiMessageStream(data, onMessage, onError, onComplete)

// 会话管理
createAiSession(sessionName)
getAiSessions()
deleteAiSession(sessionId)
```

## 🎯 **使用场景**

### **客户服务**
- 商品咨询
- 订单查询
- 退换货指导
- 使用帮助

### **智能助手**
- 商品推荐
- 购物指导
- 问题解答
- 操作指引

## 🚀 **快速开始**

### **1. 启动后端服务**
```bash
cd shangcheng
mvn spring-boot:run
```

### **2. 启动前端服务**
```bash
cd shangcheng_vue
npm run dev
```

### **3. 访问应用**
- 打开浏览器访问 `http://localhost:3000`
- 在任何页面点击右侧"客服"按钮
- 开始与AI对话

## 💡 **提示**

### **最佳实践**
- 问题描述要清晰具体
- 可以进行多轮对话
- 支持中文和英文交流
- AI会记住对话上下文

### **注意事项**
- 需要用户登录才能使用完整功能
- 网络连接需要稳定
- 首次回复可能需要几秒钟

## 🎉 **开始体验**

现在您可以：
1. 访问首页或论坛页面
2. 点击右侧悬浮的"客服"按钮
3. 开始与AI智能客服对话
4. 体验流畅的聊天体验

**祝您使用愉快！** 🎊
