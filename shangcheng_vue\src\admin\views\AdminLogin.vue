<template>
  <div class="admin-login-container">
    <div class="login-box">
      <h2>管理员登录</h2>
      <div class="form-group">
        <label for="username">用户名</label>
        <input 
          type="text" 
          id="username" 
          v-model="loginForm.username" 
          placeholder="请输入管理员用户名"
        >
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input 
          type="password" 
          id="password" 
          v-model="loginForm.password" 
          placeholder="请输入密码"
        >
      </div>
      <div class="error-message" v-if="errorMessage">{{ errorMessage }}</div>
      <button 
        class="login-btn" 
        @click="handleLogin" 
        :disabled="isLoading"
      >
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { adminLogin } from '../api/auth'
import { useAdminStore } from '../stores/admin'

const router = useRouter()
const adminStore = useAdminStore()

const loginForm = reactive({
  username: '',
  password: ''
})

const errorMessage = ref('')
const isLoading = ref(false)

// 在组件加载时清除所有token
onMounted(() => {
  // 清除所有token，确保登录状态干净
  localStorage.removeItem('token');
  localStorage.removeItem('adminToken');
});

const handleLogin = async () => {
  // 表单验证
  if (!loginForm.username || !loginForm.password) {
    errorMessage.value = '用户名和密码不能为空'
    return
  }
  
  try {
    isLoading.value = true
    errorMessage.value = ''
    
    const res = await adminLogin(loginForm)
    console.log('管理员登录响应:', res)
    
    if (res.code === 200 && res.data) {
      // 检查用户角色
      if (res.data.role !== 'admin') {
        errorMessage.value = '非管理员账号，无法登录'
        return
      }
      
      // 保存token
      adminStore.setToken(res.data.token)
      
      // 保存管理员信息到store和localStorage
      adminStore.adminInfo = res.data
      localStorage.setItem('adminInfo', JSON.stringify(res.data))
      console.log('管理员信息已保存到localStorage')
      
      // 确保获取完整的管理员信息
      try {
        console.log('尝试获取完整的管理员信息')
        await adminStore.fetchAdminInfo()
      } catch (fetchError) {
        console.warn('获取完整管理员信息失败，但将继续使用登录返回的基本信息:', fetchError)
      }
      
      // 跳转到管理员首页
      router.push('/admin/dashboard')
    } else {
      errorMessage.value = res.message || '登录失败'
    }
  } catch (error) {
    console.error('登录错误:', error)
    errorMessage.value = error.response?.data?.message || '登录失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.admin-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-box {
  width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.login-btn {
  width: 100%;
  padding: 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover {
  background-color: #40a9ff;
}

.login-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #f5222d;
  margin-bottom: 15px;
}
</style> 