package io.github.qifan777.knowledge.ai.session;

import io.github.qifan777.knowledge.ai.messge.AiMessageTableEx;
import io.github.qifan777.knowledge.user.UserTableEx;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = AiSession.class
)
public class AiSessionTableEx extends AiSessionTable implements TableEx<AiSession> {
    public static final AiSessionTableEx $ = new AiSessionTableEx(AiSessionTable.$, null);

    public AiSessionTableEx() {
        super();
    }

    public AiSessionTableEx(AbstractTypedTable.DelayedOperation<AiSession> delayedOperation) {
        super(delayedOperation);
    }

    public AiSessionTableEx(TableImplementor<AiSession> table) {
        super(table);
    }

    protected AiSessionTableEx(AiSessionTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx editor() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiSessionProps.EDITOR.unwrap()));
        }
        return new UserTableEx(joinOperation(AiSessionProps.EDITOR.unwrap()));
    }

    public UserTableEx editor(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiSessionProps.EDITOR.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(AiSessionProps.EDITOR.unwrap(), joinType));
    }

    public UserTableEx creator() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiSessionProps.CREATOR.unwrap()));
        }
        return new UserTableEx(joinOperation(AiSessionProps.CREATOR.unwrap()));
    }

    public UserTableEx creator(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(AiSessionProps.CREATOR.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(AiSessionProps.CREATOR.unwrap(), joinType));
    }

    public AiMessageTableEx messages() {
        __beforeJoin();
        if (raw != null) {
            return new AiMessageTableEx(raw.joinImplementor(AiSessionProps.MESSAGES.unwrap()));
        }
        return new AiMessageTableEx(joinOperation(AiSessionProps.MESSAGES.unwrap()));
    }

    public AiMessageTableEx messages(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new AiMessageTableEx(raw.joinImplementor(AiSessionProps.MESSAGES.unwrap(), joinType));
        }
        return new AiMessageTableEx(joinOperation(AiSessionProps.MESSAGES.unwrap(), joinType));
    }

    @Override
    public Predicate messages(Function<AiMessageTableEx, Predicate> block) {
        return exists(AiSessionProps.MESSAGES.unwrap(), block);
    }

    @Override
    public AiSessionTableEx asTableEx() {
        return this;
    }

    @Override
    public AiSessionTableEx __disableJoin(String reason) {
        return new AiSessionTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<AiSessionTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<AiSessionTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
