package io.github.qifan777.knowledge.demo;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import jdk.jfr.Description;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.util.function.Function;

@Component
@Description("删除文件函数")
public class DocumentDeleteFunction implements Function<DocumentDeleteFunction.Request,DocumentDeleteFunction.Response> {

    @Override
    public Response apply(Request request) {
        try {
            Files.delete(Paths.get(request.path()));
            return new Response(true, null);
        } catch (NoSuchFileException e) {
            return new Response(false, "文件不存在");
        } catch (AccessDeniedException e) {
            return new Response(false, "权限不足");
        } catch (IOException e) {
            return new Response(false, "删除失败: " + e.getMessage());
        }
    }

    @JsonClassDescription("删除文件的请求参数")
    public record Request(@JsonProperty(required = true)
                              @JsonPropertyDescription("需要删除的文件路径")
                              String path) {}

    public record Response(@JsonPropertyDescription("删除操作结果") boolean success,@JsonPropertyDescription("错误详细") String error) {}
}


