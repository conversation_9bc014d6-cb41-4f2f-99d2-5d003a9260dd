<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布帖子 - 二手交易平台论坛</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #e53935;
            --dark-red: #c62828;
            --light-grey: #f0f2f5;
            --medium-grey: #e8e8e8;
            --dark-grey: #333;
            --text-color-medium: #555;
            --text-color-light: #777;
            --white: #ffffff;
            --border-color: #eee;
            --shadow-light: rgba(0,0,0,0.05);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--light-grey);
            color: var(--dark-grey);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 900px;
            margin: 30px auto;
            padding: 0 16px;
        }

        /* Top Nav - Reused similar to publish-product.html */
        .top-nav {
            background: var(--white);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .top-nav .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }
        .top-nav .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-red);
            text-decoration: none;
        }
        .top-nav .back-link {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            margin-left: 20px;
            transition: color 0.2s ease;
        }
        .top-nav .back-link:hover { color: var(--primary-red); }

        .create-post-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
        }

        .create-post-card h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-grey);
            font-size: 15px;
        }

        .form-group input[type="text"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-red);
            box-shadow: 0 0 0 3px rgba(229, 57, 53, 0.1);
        }

        .form-group textarea {
            min-height: 200px;
            resize: vertical;
        }

        /* 图片上传 (Optional for forum posts, but good to have) */
        .image-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
            margin-bottom: 20px;
        }
        .image-upload-area:hover { border-color: var(--primary-red); }
        .image-upload-area input[type="file"] { display: none; }
        .image-upload-area p { color: var(--text-color-light); font-size: 14px; margin-top: 10px; }
        .image-upload-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
            justify-content: center;
        }
        .image-upload-preview img {
            width: 100px; height: 100px; object-fit: cover; border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .form-actions { text-align: center; margin-top: 30px; }
        .submit-button {
            background-color: var(--primary-red);
            color: var(--white);
            padding: 14px 40px;
            border: none;
            border-radius: 28px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        .submit-button:hover { background-color: var(--dark-red); transform: translateY(-2px); }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container { margin: 20px auto; padding: 0 10px; }
            .create-post-card { padding: 20px; }
            .create-post-card h1 { font-size: 24px; margin-bottom: 20px; }
        }
    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="nav-content">
            <a href="forum.html" class="logo">二手交易论坛</a>
            <a href="forum.html" class="back-link">返回论坛首页</a>
        </div>
    </nav>

    <div class="container">
        <div class="create-post-card">
            <h1>发布新帖子</h1>
            <form>
                <div class="form-group">
                    <label for="post-title">帖子标题</label>
                    <input type="text" id="post-title" name="post-title" placeholder="请输入帖子标题" required>
                </div>

                <div class="form-group">
                    <label for="post-content">帖子内容</label>
                    <textarea id="post-content" name="post-content" placeholder="详细描述您要分享或咨询的内容" required></textarea>
                </div>

                <div class="form-group">
                    <label>图片/附件 (可选)</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <input type="file" id="imageUploadInput" accept="image/*" multiple>
                        <p>点击或拖拽图片到此处上传</p>
                        <div class="image-upload-preview" id="imagePreview"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="category">选择分类</label>
                    <select id="category" name="category" required>
                        <option value="">请选择分类</option>
                        <option value="digital">数码产品</option>
                        <option value="fashion">服饰箱包</option>
                        <option value="home">家居生活</option>
                        <option value="books">图书文具</option>
                        <option value="qa">求助问答</option>
                        <option value="share">经验分享</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="submit" class="submit-button">立即发布</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const imageUploadArea = document.getElementById('imageUploadArea');
            const imageUploadInput = document.getElementById('imageUploadInput');
            const imagePreview = document.getElementById('imagePreview');

            if (imageUploadArea) { // Check if element exists (optional for forum posts)
                imageUploadArea.addEventListener('click', () => {
                    imageUploadInput.click();
                });

                imageUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    imageUploadArea.style.borderColor = 'var(--primary-red)';
                });

                imageUploadArea.addEventListener('dragleave', () => {
                    imageUploadArea.style.borderColor = 'var(--border-color)';
                });

                imageUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    imageUploadArea.style.borderColor = 'var(--border-color)';
                    handleFiles(e.dataTransfer.files);
                });

                imageUploadInput.addEventListener('change', (e) => {
                    handleFiles(e.target.files);
                });
            }

            function handleFiles(files) {
                imagePreview.innerHTML = ''; // Clear existing previews
                Array.from(files).slice(0, 5).forEach(file => { // Limit to 5 images for posts
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            imagePreview.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
</body>
</html> 