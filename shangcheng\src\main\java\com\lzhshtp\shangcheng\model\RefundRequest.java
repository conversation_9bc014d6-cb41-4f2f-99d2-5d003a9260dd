package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款申请实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_refund_requests")
public class RefundRequest {
    
    @TableId(value = "lzhshtp_refund_id", type = IdType.AUTO)
    private Long refundId;
    
    @TableField("lzhshtp_order_id")
    private Long orderId;
    
    @TableField("lzhshtp_buyer_id")
    private Long buyerId;
    
    @TableField("lzhshtp_seller_id")
    private Long sellerId;
    
    @TableField("lzhshtp_refund_amount")
    private BigDecimal refundAmount;
    
    @TableField("lzhshtp_refund_reason")
    private String refundReason;
    
    @TableField("lzhshtp_refund_type")
    private String refundType;
    
    @TableField("lzhshtp_status")
    private String status;
    
    @TableField("lzhshtp_seller_response")
    private String sellerResponse;
    
    @TableField("lzhshtp_admin_response")
    private String adminResponse;
    
    @TableField("lzhshtp_evidence_urls")
    private String evidenceUrls;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    @TableField("lzhshtp_updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 退款类型枚举
     */
    public static class RefundType {
        public static final String REFUND_ONLY = "refund_only";
        public static final String RETURN_REFUND = "return_refund";
    }
    
    /**
     * 退款状态枚举
     */
    public static class RefundStatus {
        public static final String PENDING_SELLER = "pending_seller";
        public static final String SELLER_APPROVED = "seller_approved";
        public static final String SELLER_REJECTED = "seller_rejected";
        public static final String PENDING_ADMIN = "pending_admin";
        public static final String ADMIN_APPROVED = "admin_approved";
        public static final String ADMIN_REJECTED = "admin_rejected";
        public static final String COMPLETED = "completed";
        public static final String CANCELLED = "cancelled";
    }
}
