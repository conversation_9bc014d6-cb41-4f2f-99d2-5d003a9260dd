package com.lzhshtp.shangcheng.dto.audit;

import lombok.Data;

/**
 * 二度复审决策请求
 */
@Data
public class SecondReviewDecisionRequest {
    
    /**
     * 复审员ID
     */
    private Long reviewerId;
    
    /**
     * 复审决策
     * approved: 最终通过
     * rejected: 最终拒绝
     * request_materials: 要求补充材料
     */
    private String decision;
    
    /**
     * 复审意见
     */
    private String comments;
    
    /**
     * 风险评估
     * low: 低风险
     * medium: 中风险
     * high: 高风险
     */
    private String riskLevel;
    
    /**
     * 要求补充的材料列表（当decision为request_materials时使用）
     */
    private String requiredMaterials;
}
