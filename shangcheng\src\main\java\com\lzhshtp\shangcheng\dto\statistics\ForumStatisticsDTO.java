package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 论坛统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumStatisticsDTO {
    
    // 论坛活跃度趋势
    private List<String> activityDates;        // 活跃度统计日期
    private List<Long> postCounts;             // 每日帖子数量
    private List<Long> commentCounts;          // 每日评论数量
    private List<Long> viewCounts;             // 每日浏览量
    
    // 论坛分类统计
    private List<ForumCategoryStatisticsDTO> categoryStatistics; // 分类统计
    
    // 用户参与度统计
    private Long totalPosts;                   // 总帖子数
    private Long totalComments;                // 总评论数
    private Long totalViews;                   // 总浏览量
    private Long activeUserCount;              // 活跃用户数（发过帖或评论）
    private Double avgPostsPerUser;            // 平均每用户帖子数
    private Double avgCommentsPerUser;         // 平均每用户评论数

    // 热门帖子排行
    private List<HotPostDTO> hotPostsByViews;      // 按浏览量排行
    private List<HotPostDTO> hotPostsByComments;   // 按评论数排行

    // 活跃用户排行
    private List<ActiveForumUserDTO> activeUserList; // 活跃用户排行
    
    // 帖子状态分布
    private Map<String, Long> postStatusDistribution; // 帖子状态分布
    
    // 论坛参与度分析
    private Double userParticipationRate;      // 用户参与率（发帖用户/总用户）
    private Double avgCommentsPerPost;         // 平均每帖评论数
    private Double avgViewsPerPost;            // 平均每帖浏览量
    
    // 时间分布分析
    private Map<Integer, Long> hourlyPostDistribution;    // 24小时发帖分布
    private Map<Integer, Long> hourlyCommentDistribution; // 24小时评论分布
}


