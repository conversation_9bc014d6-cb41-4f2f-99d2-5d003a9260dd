<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.UserFollowMapper">
    
    <!-- 检查用户是否已关注某人 -->
    <select id="selectByFollowerIdAndFollowingId" resultType="com.lzhshtp.shangcheng.model.UserFollow">
        SELECT 
            lzhshtp_follow_id as followId,
            lzhshtp_follower_id as followerId,
            lzhshtp_following_id as followingId,
            lzhshtp_followed_at as followedAt
        FROM tb_lzhshtp_user_follows
        WHERE lzhshtp_follower_id = #{followerId} AND lzhshtp_following_id = #{followingId}
        LIMIT 1
    </select>
    
    <!-- 分页查询用户的关注列表 -->
    <select id="selectFollowingsByFollowerId" resultType="com.lzhshtp.shangcheng.model.UserFollow">
        SELECT 
            f.lzhshtp_follow_id as followId,
            f.lzhshtp_follower_id as followerId,
            f.lzhshtp_following_id as followingId,
            f.lzhshtp_followed_at as followedAt
        FROM tb_lzhshtp_user_follows f
        WHERE f.lzhshtp_follower_id = #{followerId}
        ORDER BY f.lzhshtp_followed_at DESC
    </select>
    
    <!-- 分页查询用户的粉丝列表 -->
    <select id="selectFollowersByFollowingId" resultType="com.lzhshtp.shangcheng.model.UserFollow">
        SELECT 
            f.lzhshtp_follow_id as followId,
            f.lzhshtp_follower_id as followerId,
            f.lzhshtp_following_id as followingId,
            f.lzhshtp_followed_at as followedAt
        FROM tb_lzhshtp_user_follows f
        WHERE f.lzhshtp_following_id = #{followingId}
        ORDER BY f.lzhshtp_followed_at DESC
    </select>
    
    <!-- 获取用户的关注数量 -->
    <select id="countFollowingsByFollowerId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tb_lzhshtp_user_follows
        WHERE lzhshtp_follower_id = #{followerId}
    </select>
    
    <!-- 获取用户的粉丝数量 -->
    <select id="countFollowersByFollowingId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tb_lzhshtp_user_follows
        WHERE lzhshtp_following_id = #{followingId}
    </select>
    
</mapper> 