<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ScheduledTaskConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="ScheduledTaskConfigResultMap" type="com.lzhshtp.shangcheng.model.ScheduledTaskConfig">
        <id property="taskId" column="lzhshtp_task_id"/>
        <result property="taskName" column="lzhshtp_task_name"/>
        <result property="taskType" column="lzhshtp_task_type"/>
        <result property="cronExpression" column="lzhshtp_cron_expression"/>
        <result property="isEnabled" column="lzhshtp_is_enabled"/>
        <result property="configParams" column="lzhshtp_config_params"/>
        <result property="lastExecution" column="lzhshtp_last_execution"/>
        <result property="nextExecution" column="lzhshtp_next_execution"/>
        <result property="createdAt" column="lzhshtp_created_at"/>
        <result property="updatedAt" column="lzhshtp_updated_at"/>
    </resultMap>

    <!-- 根据任务名称查询配置 -->
    <select id="findByTaskName" resultMap="ScheduledTaskConfigResultMap">
        SELECT lzhshtp_task_id, lzhshtp_task_name, lzhshtp_task_type, lzhshtp_cron_expression,
               lzhshtp_is_enabled, lzhshtp_config_params, lzhshtp_last_execution,
               lzhshtp_next_execution, lzhshtp_created_at, lzhshtp_updated_at
        FROM tb_lzhshtp_scheduled_task_configs
        WHERE lzhshtp_task_name = #{taskName}
    </select>
    
    <!-- 根据任务类型查询配置列表 -->
    <select id="findByTaskType" resultMap="ScheduledTaskConfigResultMap">
        SELECT lzhshtp_task_id, lzhshtp_task_name, lzhshtp_task_type, lzhshtp_cron_expression,
               lzhshtp_is_enabled, lzhshtp_config_params, lzhshtp_last_execution,
               lzhshtp_next_execution, lzhshtp_created_at, lzhshtp_updated_at
        FROM tb_lzhshtp_scheduled_task_configs
        WHERE lzhshtp_task_type = #{taskType}
        ORDER BY lzhshtp_created_at DESC
    </select>

    <!-- 查询所有启用的任务配置 -->
    <select id="findEnabledTasks" resultMap="ScheduledTaskConfigResultMap">
        SELECT lzhshtp_task_id, lzhshtp_task_name, lzhshtp_task_type, lzhshtp_cron_expression,
               lzhshtp_is_enabled, lzhshtp_config_params, lzhshtp_last_execution,
               lzhshtp_next_execution, lzhshtp_created_at, lzhshtp_updated_at
        FROM tb_lzhshtp_scheduled_task_configs
        WHERE lzhshtp_is_enabled = true
        ORDER BY lzhshtp_task_type, lzhshtp_task_name
    </select>
    
</mapper>
