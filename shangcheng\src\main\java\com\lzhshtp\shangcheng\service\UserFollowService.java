package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.UserFollowDTO;

/**
 * 用户关注服务接口
 */
public interface UserFollowService {
    
    /**
     * 关注用户
     * 
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean followUser(Long followerId, Long followingId);
    
    /**
     * 取消关注用户
     * 
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean unfollowUser(Long followerId, Long followingId);
    
    /**
     * 检查用户是否已关注某人
     * 
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean isFollowing(Long followerId, Long followingId);
    
    /**
     * 获取用户的关注列表
     * 
     * @param followerId 关注者ID
     * @param page 页码
     * @param pageSize 每页数量
     * @return 关注列表
     */
    PageResult<UserFollowDTO> getFollowings(Long followerId, Integer page, Integer pageSize);
    
    /**
     * 获取用户的粉丝列表
     * 
     * @param followingId 被关注者ID
     * @param page 页码
     * @param pageSize 每页数量
     * @return 粉丝列表
     */
    PageResult<UserFollowDTO> getFollowers(Long followingId, Integer page, Integer pageSize);
    
    /**
     * 获取用户的关注数量
     * 
     * @param followerId 关注者ID
     * @return 关注数量
     */
    Integer getFollowingsCount(Long followerId);
    
    /**
     * 获取用户的粉丝数量
     * 
     * @param followingId 被关注者ID
     * @return 粉丝数量
     */
    Integer getFollowersCount(Long followingId);
} 