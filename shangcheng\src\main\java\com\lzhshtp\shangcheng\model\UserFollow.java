package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户关注实体类，对应数据库表tb_lzhshtp_user_follows
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_user_follows")
public class UserFollow {
    
    @TableId(value = "lzhshtp_follow_id", type = IdType.AUTO)
    private Long followId;
    
    @TableField("lzhshtp_follower_id")
    private Long followerId;
    
    @TableField("lzhshtp_following_id")
    private Long followingId;
    
    @TableField("lzhshtp_followed_at")
    private LocalDateTime followedAt;
} 