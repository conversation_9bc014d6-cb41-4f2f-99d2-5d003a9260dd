<template>
  <div v-if="visible" class="ai-chat-overlay" @click="handleOverlayClick">
    <div class="ai-chat-dialog" @click.stop>
      <!-- 头部 -->
      <div class="chat-header">
        <div class="header-left">
          <div class="avatar">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
          </div>
          <div class="title-info">
            <h3>智能客服</h3>
            <span class="status">{{ getStatusText() }}</span>
          </div>
        </div>
        <div class="header-controls">
          <!-- 商品助手Agent开关 -->
          <div class="agent-toggle">
            <label class="toggle-label">
              <input
                type="checkbox"
                v-model="agentEnabled"
                @change="onAgentToggle"
                class="toggle-input"
              />
              <span class="toggle-slider"></span>
              <span class="toggle-text">商品助手agent</span>
            </label>
          </div>

          <!-- RAG知识库开关 -->
          <div class="rag-toggle">
            <label class="toggle-label">
              <input
                type="checkbox"
                v-model="ragEnabled"
                @change="onRagToggle"
                class="toggle-input"
              />
              <span class="toggle-slider"></span>
              <span class="toggle-text">知识库</span>
            </label>
          </div>
          <button class="close-btn" @click="$emit('close')" title="关闭">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="chatContent">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="message ai-message">
            <div class="message-avatar">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
            </div>
            <div class="message-content">
              <div class="message-text">
                您好！👋 我是您的专属客服，有什么可以帮您？
              </div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-for="message in messages" :key="message.id" class="message" :class="message.type">
          <div v-if="message.type === 'ai-message'" class="message-avatar">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
          </div>
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="message ai-message typing-indicator">
          <div class="message-avatar">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
          </div>
          <div class="message-content">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品助手快速测试按钮 -->
      <div v-if="agentEnabled" class="quick-actions">
        <div class="quick-actions-header">
          <span class="quick-title">🤖 快速测试</span>
          <span class="quick-subtitle">点击按钮快速体验商品助手功能</span>
        </div>
        <div class="quick-buttons">
          <button @click="testSearch" class="quick-btn primary">
            🔍 搜索iPhone
          </button>
          <button @click="testDetail" class="quick-btn success">
            📦 查看商品详情
          </button>
          <button @click="testCompare" class="quick-btn warning">
            ⚖️ 比较商品
          </button>
          <button @click="testRecommend" class="quick-btn info">
            💡 推荐相似商品
          </button>
          <button @click="testCategories" class="quick-btn default">
            🏷️ 查看分类
          </button>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <button class="attach-btn" title="附件">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
          </button>
          <input
            v-model="inputMessage"
            type="text"
            placeholder="输入信息..."
            @keyup.enter="sendMessage"
            :disabled="isTyping"
            class="message-input"
          />
          <button
            class="emoji-btn"
            title="表情"
            @click="showEmojiPicker = !showEmojiPicker"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </button>
        </div>

        <!-- 表情选择器 -->
        <div v-if="showEmojiPicker" class="emoji-picker">
          <div class="emoji-grid">
            <span v-for="emoji in commonEmojis" :key="emoji" @click="addEmoji(emoji)" class="emoji-item">
              {{ emoji }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { sendAiMessageStream, createAiSession } from '@/api/ai'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// 响应式数据
const messages = ref([])
const inputMessage = ref('')
const isTyping = ref(false)
const showEmojiPicker = ref(false)
const chatContent = ref(null)
const currentSessionId = ref(null)
const streamCancel = ref(null)
const ragEnabled = ref(false) // RAG知识库开关
const agentEnabled = ref(false) // 商品助手Agent开关

// 常用表情
const commonEmojis = ['😊', '😂', '😍', '🤔', '😢', '😡', '👍', '👎', '❤️', '🎉', '🔥', '💯']

// 处理遮罩层点击
const handleOverlayClick = (e) => {
  if (e.target === e.currentTarget) {
    emit('close')
  }
}

// 格式化消息内容
const formatMessage = (content) => {
  return content.replace(/\n/g, '<br>')
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContent.value) {
      chatContent.value.scrollTop = chatContent.value.scrollHeight
    }
  })
}

// 添加表情
const addEmoji = (emoji) => {
  inputMessage.value += emoji
  showEmojiPicker.value = false
}

// RAG开关切换
const onRagToggle = () => {
  // 如果开启RAG，关闭Agent
  if (ragEnabled.value) {
    agentEnabled.value = false
    localStorage.setItem('agentEnabled', 'false')
  }

  // 保存用户偏好到localStorage
  localStorage.setItem('ragEnabled', ragEnabled.value.toString())

  // 显示提示消息
  ElMessage.info(`已切换到${getStatusText()}`)
}

const onAgentToggle = () => {
  // 如果开启Agent，关闭RAG
  if (agentEnabled.value) {
    ragEnabled.value = false
    localStorage.setItem('ragEnabled', 'false')
  }

  // 保存用户偏好到localStorage
  localStorage.setItem('agentEnabled', agentEnabled.value.toString())

  // 显示提示消息
  ElMessage.info(`已切换到${getStatusText()}`)
}

const getStatusText = () => {
  if (agentEnabled.value) {
    return '商品助手模式'
  } else if (ragEnabled.value) {
    return '知识库模式'
  } else {
    return '通用模式'
  }
}

// 发送消息
const sendMessage = async () => {
  const message = inputMessage.value.trim()
  if (!message || isTyping.value) return

  // 添加用户消息
  const userMessage = {
    id: Date.now(),
    type: 'user-message',
    content: message,
    timestamp: Date.now()
  }
  messages.value.push(userMessage)
  inputMessage.value = ''
  scrollToBottom()

  // 显示正在输入状态
  isTyping.value = true
  scrollToBottom()

  try {
    // 如果没有会话ID，先创建会话
    if (!currentSessionId.value) {
      const sessionResponse = await createAiSession('智能客服')
      if (sessionResponse.success) {
        currentSessionId.value = sessionResponse.data.sessionId
      }
    }

    // 准备AI回复消息 - 使用ref确保响应式
    const aiMessage = ref({
      id: Date.now() + 1,
      type: 'ai-message',
      content: '',
      timestamp: Date.now()
    })

    // 发送流式消息
    try {
      // 如果启用了Agent模式，使用Agent API
      if (agentEnabled.value) {
        await callAgentAPI(message, aiMessage)
      } else {
        // 使用普通的AI聊天API
        await sendAiMessageStream(
          {
            content: message,
            sessionId: currentSessionId.value,
            useRAG: ragEnabled.value
          },
        // onMessage - 接收到AI回复的内容片段
        (content) => {
          // 第一次收到消息时，添加消息到列表
          if (aiMessage.value.content === '') {
            isTyping.value = false
            messages.value.push(aiMessage.value)
          }

          // 累积消息内容
          aiMessage.value.content += content

          // 直接更新messages数组中的对象，确保响应式更新
          const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.value.id)
          if (messageIndex !== -1) {
            messages.value[messageIndex] = { ...aiMessage.value }
          }

          // 强制触发Vue响应式更新
          nextTick(() => {
            scrollToBottom()
          })
        },
        // onError - 处理错误
        (error) => {
          isTyping.value = false
          console.error('AI消息发送失败:', error)
          ElMessage.error('消息发送失败，请重试')

          // 如果已经添加了消息，更新为错误状态
          if (messages.value.includes(aiMessage.value)) {
            aiMessage.value.content = '抱歉，消息发送失败，请重试。'
          }
        },
        // onComplete - 消息接收完成
        () => {
          isTyping.value = false
          scrollToBottom()
        }
      )
      }
    } catch (error) {
      console.error('发送流式消息异常:', error)
      isTyping.value = false
      ElMessage.error('消息发送失败，请重试')
    }
  } catch (error) {
    isTyping.value = false
    console.error('发送消息失败:', error)
    ElMessage.error('消息发送失败，请重试')
  }
}

// Agent API调用函数
const callAgentAPI = async (message, aiMessage) => {
  try {
    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      throw new Error('请先登录')
    }

    // 调用Agent AI聊天接口
    const response = await fetch('/api/ai/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        content: message,
        params: {
          enableAgent: true,
          enableMemory: true,
          historyMessageCount: 10
        }
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = '' // 添加缓冲区来处理分块边界问题

    // 处理单行数据的函数
    function processLine(line) {
      if (line.startsWith('data:')) {
        const jsonStr = line.substring(5).trim() // 移除 "data:" 前缀

        if (jsonStr) {
          try {
            const jsonData = JSON.parse(jsonStr)

            // 处理内容
            if (jsonData.content !== undefined && jsonData.content !== null) {
              // 第一次收到消息时，添加消息到列表
              if (aiMessage.value.content === '') {
                isTyping.value = false
                messages.value.push(aiMessage.value)
              }

              // 累积消息内容
              aiMessage.value.content += jsonData.content

              // 直接更新messages数组中的对象，确保响应式更新
              const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.value.id)
              if (messageIndex !== -1) {
                messages.value[messageIndex] = { ...aiMessage.value }
              }

              // 强制触发Vue响应式更新
              nextTick(() => {
                scrollToBottom()
              })
            }

            // 检查是否结束
            if (jsonData.isEnd === true) {
              return true // 表示结束
            }

            // 检查错误
            if (jsonData.error) {
              console.error('AI服务器错误:', jsonData.error)
              throw new Error(jsonData.error)
            }

          } catch (parseError) {
            console.warn('AI响应解析失败:', parseError, '原始数据:', line)
          }
        }
      }
      return false
    }

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          // 处理缓冲区中剩余的数据
          if (buffer.trim()) {
            const lines = buffer.split('\n')
            for (let line of lines) {
              if (processLine(line.trim())) {
                break
              }
            }
          }
          break
        }

        // 解码数据块并添加到缓冲区
        const chunk = decoder.decode(value, { stream: true })
        buffer += chunk

        // 处理完整的行
        const lines = buffer.split('\n')

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || ''

        // 处理完整的行
        for (let line of lines) {
          if (processLine(line.trim())) {
            return
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

  } catch (error) {
    console.error('Agent API调用失败:', error)
    isTyping.value = false

    // 如果已经添加了消息，更新为错误状态
    if (messages.value.includes(aiMessage.value)) {
      aiMessage.value.content = '抱歉，商品助手暂时无法响应，请稍后再试。'
    } else {
      ElMessage.error('商品助手响应失败：' + error.message)
    }
  }
}

// 快速测试方法
const testSearch = () => {
  inputMessage.value = '搜索价格在2000-4000的iPhone手机'
}

const testDetail = () => {
  inputMessage.value = '查看商品ID 1的详细信息'
}

const testCompare = () => {
  inputMessage.value = '比较商品ID 1,2,3的性价比'
}

const testRecommend = () => {
  inputMessage.value = '根据商品1推荐一些相似的商品'
}

const testCategories = () => {
  inputMessage.value = '显示所有商品分类'
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      scrollToBottom()
    })
  } else {
    // 关闭时取消流式请求
    if (streamCancel.value) {
      streamCancel.value()
      streamCancel.value = null
    }
    showEmojiPicker.value = false
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (streamCancel.value) {
    streamCancel.value()
  }
})

// 组件挂载时读取用户偏好
onMounted(() => {
  const savedRagEnabled = localStorage.getItem('ragEnabled')
  if (savedRagEnabled !== null) {
    ragEnabled.value = savedRagEnabled === 'true'
  }

  const savedAgentEnabled = localStorage.getItem('agentEnabled')
  if (savedAgentEnabled !== null) {
    agentEnabled.value = savedAgentEnabled === 'true'
  }
})
</script>

<style scoped>
.ai-chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 20px;
}

.ai-chat-dialog {
  width: 400px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 头部样式 */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-info h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.status {
  font-size: 12px;
  opacity: 0.8;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rag-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  width: 40px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
}

.toggle-input:checked + .toggle-slider {
  background: #4CAF50;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* 快速测试按钮样式 */
.quick-actions {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.quick-actions-header {
  margin-bottom: 12px;
}

.quick-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
}

.quick-subtitle {
  font-size: 12px;
  color: #6b7280;
}

.quick-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.quick-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-btn.primary {
  background: #3b82f6;
  color: white;
}

.quick-btn.primary:hover {
  background: #2563eb;
}

.quick-btn.success {
  background: #10b981;
  color: white;
}

.quick-btn.success:hover {
  background: #059669;
}

.quick-btn.warning {
  background: #f59e0b;
  color: white;
}

.quick-btn.warning:hover {
  background: #d97706;
}

.quick-btn.info {
  background: #6366f1;
  color: white;
}

.quick-btn.info:hover {
  background: #4f46e5;
}

.quick-btn.default {
  background: #6b7280;
  color: white;
}

.quick-btn.default:hover {
  background: #4b5563;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: #28a745;
}

.message-content {
  max-width: 70%;
}

.user-message .message-content {
  text-align: right;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.4;
}

.user-message .message-text {
  background: #007bff;
  color: white;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  padding: 0 4px;
}

/* 正在输入指示器 */
.typing-indicator .message-content {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-input {
  border-top: 1px solid #e9ecef;
  background: white;
  padding: 16px 20px;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  border-radius: 24px;
  padding: 8px 12px;
}

.attach-btn,
.emoji-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.attach-btn:hover,
.emoji-btn:hover {
  background: #e9ecef;
}

.message-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  padding: 8px 0;
}

.message-input:disabled {
  opacity: 0.6;
}

/* 表情选择器 */
.emoji-picker {
  margin-top: 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
}

.emoji-item {
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  text-align: center;
  transition: background-color 0.2s;
}

.emoji-item:hover {
  background: #f8f9fa;
}

/* 滚动条样式 */
.chat-content::-webkit-scrollbar {
  width: 6px;
}

.chat-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chat-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-chat-overlay {
    padding: 0;
  }

  .ai-chat-dialog {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
}
</style>
