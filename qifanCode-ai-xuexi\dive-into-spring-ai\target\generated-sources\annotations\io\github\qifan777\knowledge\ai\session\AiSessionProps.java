package io.github.qifan777.knowledge.ai.session;

import io.github.qifan777.knowledge.ai.messge.AiMessage;
import io.github.qifan777.knowledge.ai.messge.AiMessageTableEx;
import io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntityProps;
import io.github.qifan777.knowledge.user.User;
import java.lang.String;
import java.time.LocalDateTime;
import java.util.function.Function;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.ast.Predicate;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = AiSession.class
)
@PropsFor(AiSession.class)
public interface AiSessionProps extends BaseEntityProps {
    TypedProp.Scalar<AiSession, String> ID = 
        TypedProp.scalar(ImmutableType.get(AiSession.class).getProp("id"));

    TypedProp.Scalar<AiSession, LocalDateTime> CREATED_TIME = 
        TypedProp.scalar(ImmutableType.get(AiSession.class).getProp("createdTime"));

    TypedProp.Scalar<AiSession, LocalDateTime> EDITED_TIME = 
        TypedProp.scalar(ImmutableType.get(AiSession.class).getProp("editedTime"));

    TypedProp.Reference<AiSession, User> EDITOR = 
        TypedProp.reference(ImmutableType.get(AiSession.class).getProp("editor"));

    TypedProp.Reference<AiSession, User> CREATOR = 
        TypedProp.reference(ImmutableType.get(AiSession.class).getProp("creator"));

    TypedProp.Scalar<AiSession, String> NAME = 
        TypedProp.scalar(ImmutableType.get(AiSession.class).getProp("name"));

    TypedProp.ReferenceList<AiSession, AiMessage> MESSAGES = 
        TypedProp.referenceList(ImmutableType.get(AiSession.class).getProp("messages"));

    PropExpression.Str name();

    Predicate messages(Function<AiMessageTableEx, Predicate> block);
}
