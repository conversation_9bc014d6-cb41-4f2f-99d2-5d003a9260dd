package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.RechargeRequest;
import com.lzhshtp.shangcheng.dto.RechargeResponse;

import java.util.Map;

/**
 * 充值服务接口
 */
public interface RechargeService {
    
    /**
     * 创建充值订单
     *
     * @param userId 用户ID
     * @param request 充值请求
     * @return 充值响应
     */
    RechargeResponse createRecharge(Long userId, RechargeRequest request);
    
    /**
     * 处理支付宝支付回调
     *
     * @param params 回调参数
     * @return 处理结果
     */
    boolean handleAlipayCallback(Map<String, String> params);
    
    /**
     * 处理支付宝支付返回
     *
     * @param params 返回参数
     * @return 处理结果
     */
    boolean handleAlipayReturn(Map<String, String> params);
}
