package com.lzhshtp.shangcheng.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lzhshtp.shangcheng.dto.MessageDTO;
import com.lzhshtp.shangcheng.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聊天WebSocket处理器
 * 处理实时聊天消息的发送和接收
 */
@Slf4j
@Component
public class ChatWebSocketHandler implements WebSocketHandler {

    // 存储用户ID和WebSocket会话的映射
    private static final ConcurrentHashMap<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    
    // 存储WebSocket会话和用户ID的反向映射
    private static final ConcurrentHashMap<String, Long> sessionUsers = new ConcurrentHashMap<>();

    @Autowired
    private JwtUtils jwtUtils;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        try {
            log.info("WebSocket连接建立，会话ID: {}, URI: {}", session.getId(), session.getUri());

            // 从查询参数中获取JWT token
            Long userId = getUserIdFromSession(session);

            if (userId != null) {
                // 存储用户连接
                userSessions.put(userId, session);
                sessionUsers.put(session.getId(), userId);

                log.info("用户 {} 建立WebSocket连接成功，会话ID: {}", userId, session.getId());

                // 发送连接成功消息
                sendMessageToSession(session, createSystemMessage("连接成功"));
            } else {
                log.warn("无效的JWT token，关闭连接: {}, URI: {}", session.getId(), session.getUri());
                session.close(CloseStatus.NOT_ACCEPTABLE);
            }
        } catch (Exception e) {
            log.error("建立WebSocket连接失败: {}, 错误: {}", session.getId(), e.getMessage(), e);
            session.close(CloseStatus.SERVER_ERROR);
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        // 这里可以处理客户端发送的消息，比如心跳包
        log.debug("收到WebSocket消息: {}", message.getPayload());
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误，会话ID: {}, 错误: {}", session.getId(), exception.getMessage());
        removeSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        Long userId = sessionUsers.get(session.getId());
        removeSession(session);
        log.info("用户 {} 断开WebSocket连接，会话ID: {}, 状态: {}", userId, session.getId(), closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 发送消息给指定用户
     * @param userId 用户ID
     * @param messageDTO 消息DTO
     */
    public static void sendMessageToUser(Long userId, MessageDTO messageDTO) {
        WebSocketSession session = userSessions.get(userId);
        if (session != null && session.isOpen()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonMessage = mapper.writeValueAsString(messageDTO);
                session.sendMessage(new TextMessage(jsonMessage));
                log.debug("发送WebSocket消息给用户 {}: {}", userId, messageDTO.getContent());
            } catch (IOException e) {
                log.error("发送WebSocket消息失败，用户ID: {}, 错误: {}", userId, e.getMessage());
                // 如果发送失败，移除无效的连接
                userSessions.remove(userId);
            }
        } else {
            log.debug("用户 {} 未连接WebSocket或连接已关闭", userId);
        }
    }

    /**
     * 广播消息给所有在线用户
     * @param messageDTO 消息DTO
     */
    public static void broadcastMessage(MessageDTO messageDTO) {
        userSessions.forEach((userId, session) -> {
            if (session.isOpen()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    String jsonMessage = mapper.writeValueAsString(messageDTO);
                    session.sendMessage(new TextMessage(jsonMessage));
                } catch (IOException e) {
                    log.error("广播消息失败，用户ID: {}, 错误: {}", userId, e.getMessage());
                }
            }
        });
    }

    /**
     * 获取在线用户数量
     * @return 在线用户数量
     */
    public static int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    public static boolean isUserOnline(Long userId) {
        WebSocketSession session = userSessions.get(userId);
        return session != null && session.isOpen();
    }

    /**
     * 从WebSocket会话中获取用户ID
     * @param session WebSocket会话
     * @return 用户ID
     */
    private Long getUserIdFromSession(WebSocketSession session) {
        try {
            URI uri = session.getUri();
            log.debug("WebSocket URI: {}", uri);

            if (uri != null) {
                String query = uri.getQuery();
                log.debug("查询参数: {}", query);

                if (query != null && query.contains("token=")) {
                    String token = query.substring(query.indexOf("token=") + 6);
                    if (token.contains("&")) {
                        token = token.substring(0, token.indexOf("&"));
                    }

                    log.debug("提取的token: {}", token.substring(0, Math.min(20, token.length())) + "...");

                    // 验证JWT token并获取用户ID
                    if (jwtUtils.validateToken(token)) {
                        Long userId = jwtUtils.getUserIdFromToken(token);
                        log.debug("从token中获取的用户ID: {}", userId);
                        return userId;
                    } else {
                        log.warn("JWT token验证失败");
                    }
                } else {
                    log.warn("查询参数中没有找到token");
                }
            } else {
                log.warn("WebSocket URI为空");
            }
        } catch (Exception e) {
            log.error("从WebSocket会话获取用户ID失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 移除会话
     * @param session WebSocket会话
     */
    private void removeSession(WebSocketSession session) {
        Long userId = sessionUsers.remove(session.getId());
        if (userId != null) {
            userSessions.remove(userId);
        }
    }

    /**
     * 发送消息到指定会话
     * @param session WebSocket会话
     * @param message 消息内容
     */
    private void sendMessageToSession(WebSocketSession session, String message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        } catch (IOException e) {
            log.error("发送消息到会话失败: {}", e.getMessage());
        }
    }

    /**
     * 创建系统消息
     * @param content 消息内容
     * @return JSON格式的系统消息
     */
    private String createSystemMessage(String content) {
        try {
            MessageDTO systemMessage = new MessageDTO();
            systemMessage.setContent(content);
            systemMessage.setIsSystemMessage(true);
            systemMessage.setSentAt(java.time.LocalDateTime.now());
            return objectMapper.writeValueAsString(systemMessage);
        } catch (Exception e) {
            return "{\"content\":\"" + content + "\",\"isSystemMessage\":true}";
        }
    }
}
