package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.UserFavoriteDTO;

import java.util.List;

/**
 * 用户收藏服务接口
 */
public interface UserFavoriteService {
    
    /**
     * 添加收藏
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 操作结果
     */
    ApiResponse<String> addFavorite(Long userId, Long productId);
    
    /**
     * 取消收藏（通过收藏ID）
     * @param favoriteId 收藏ID
     * @param userId 用户ID
     * @return 操作结果
     */
    ApiResponse<String> removeFavorite(Long favoriteId, Long userId);
    
    /**
     * 取消收藏（通过商品ID）
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 操作结果
     */
    ApiResponse<String> removeFavoriteByProduct(Long userId, Long productId);
    
    /**
     * 查询用户是否已收藏商品
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 是否已收藏
     */
    ApiResponse<Boolean> isFavorited(Long userId, Long productId);
    
    /**
     * 获取用户收藏列表
     * @param userId 用户ID
     * @return 收藏列表
     */
    ApiResponse<List<UserFavoriteDTO>> getUserFavorites(Long userId);
    
    /**
     * 获取商品被收藏次数
     * @param productId 商品ID
     * @return 收藏次数
     */
    ApiResponse<Integer> getProductFavoriteCount(Long productId);
} 