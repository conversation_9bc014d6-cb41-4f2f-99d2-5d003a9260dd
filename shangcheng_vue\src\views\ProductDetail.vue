<template>
  <div class="product-detail-page">
    <!-- 顶部导航 -->
    <nav class="top-nav">
      <div class="container nav-content">
        <router-link to="/home" class="logo">易转</router-link>
        <div class="search-area">
          <div class="search-bar">
            <input type="text" class="search-input" placeholder="搜索想要的商品...">
            <button class="search-button">搜索</button>
          </div>
        </div>
        <div class="nav-right">
          <router-link to="/profile" class="user-avatar" title="个人中心">
            <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 商品详情区域 -->
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container card-container">
        <div class="spinner"></div>
        <p>加载商品信息中...</p>
      </div>
      
      <template v-else>
        <!-- 商品主体信息 -->
        <div class="card-container">
          <div class="product-main">
            <!-- 商品图片区域 -->
            <div class="product-images">
              <div class="main-image">
                <img :src="currentImage" :alt="product.name">
              </div>
              <div class="thumbnail-list">
                <div 
                  v-for="(image, index) in product.images" 
                  :key="index" 
                  class="thumbnail-item" 
                  :class="{ active: currentImageIndex === index }"
                  @click="selectImage(index)"
                >
                  <img :src="image" :alt="`${product.name} - 图片 ${index + 1}`">
                </div>
              </div>
            </div>

            <!-- 商品信息区域 -->
            <div class="product-info">
              <h1 class="product-title">{{ product.name }}</h1>
              <div class="price-range">
                <span class="price-symbol">¥</span>
                <span class="price-value">{{ formatPrice(product.price) }}</span>
              </div>

              <!-- 官方验货标识 -->
              <div v-if="product.supportOfficialVerification" class="verification-badge">
                <span class="verification-icon">✅</span>
                <span class="verification-text">官方验货</span>
                <span class="verification-desc">卖家提供免费验货服务</span>
              </div>
              
              <div class="product-meta">
                <div class="meta-item">
                  <span class="meta-label">商品状态：</span>
                  <span class="meta-value">{{ product.condition }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">所在地区：</span>
                  <span class="meta-value">{{ product.location }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">交易方式：</span>
                  <span class="meta-value">{{ product.deliveryMethod }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">发布时间：</span>
                  <span class="meta-value">{{ product.postedDate }}</span>
                </div>
                <div class="meta-item seller-info">
                  <span class="meta-label">卖家信息：</span>
                  <div class="seller-info-value meta-value">
                    <router-link :to="`/seller/${product.sellerId}`" class="seller-profile-link">
                      <img :src="product.sellerAvatar || defaultAvatar" :alt="product.sellerName" class="seller-avatar">
                      <span>{{ product.sellerName }}</span>
                    </router-link>
                  </div>
                </div>
                <div class="meta-item">
                  <span class="meta-label">收藏人数：</span>
                  <span class="meta-value">{{ favoriteCount }} 人收藏</span>
                </div>
                <div class="meta-item description-item">
                  <span class="meta-label">商品描述：</span>
                  <div class="meta-value description-value">{{ product.description }}</div>
                </div>
              </div>

              <div class="action-buttons">
                <button class="buy-now-btn" @click="buyNow">立即购买</button>
                <button class="chat-btn" @click="chatWithSeller" v-if="product.sellerId && product.sellerId !== userInfo?.userId">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                  私聊卖家
                </button>
                <button class="add-favorite-btn" @click="toggleFavorite" :class="{ 'favorited': isFavorited }">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>
                  {{ isFavorited ? '已收藏' : '收藏' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户评价区域 -->
        <div class="card-container">
          <div class="user-reviews">
            <h2 class="section-title">
              用户评论 ({{ reviewStats.reviewCount || 0 }})
              <span v-if="reviewStats.averageRating" class="average-rating">
                平均评分: <strong>{{ reviewStats.averageRating.toFixed(1) }}</strong>
              </span>
            </h2>
            
            <!-- 加载状态 -->
            <div v-if="reviewLoading" class="review-loading">
              <div class="spinner small"></div>
              <p>加载评价中...</p>
            </div>
            
            <!-- 空状态 -->
            <div v-else-if="reviews.length === 0" class="empty-reviews">
              <p>暂无评价</p>
            </div>
            
            <!-- 评价列表 -->
            <div v-else class="review-list">
              <div v-for="review in reviews" :key="review.reviewId" class="review-item">
                <div class="review-user">
                  <img :src="review.reviewerAvatar || defaultAvatar" :alt="review.reviewerName" class="review-avatar">
                  <span class="review-username">{{ review.reviewerName }}</span>
                </div>
                <div class="review-content">
                  <div class="review-rating">
                    <span v-for="i in 5" :key="i" class="star" :class="{ filled: i <= review.rating }">★</span>
                  </div>
                  <p>{{ review.comment }}</p>
                  <div class="review-date">{{ formatDate(review.reviewDate) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 发表评论区域 -->
          <div class="post-comment">
            <h2 class="section-title">发表评论</h2>
            <div v-if="!userInfo" class="login-prompt">
              请<router-link to="/login">登录</router-link>后发表评论
            </div>
            <div v-else class="comment-form">
              <textarea 
                v-model="newComment" 
                placeholder="写下你的评论(5-500字)..." 
                class="comment-input"
                rows="3"
              ></textarea>
              <div class="comment-length" :class="{ 'error': newComment.trim().length > 0 && (newComment.trim().length < 5 || newComment.trim().length > 500) }">
                {{ newComment.trim().length }}/500 字符 (最少5字符)
              </div>
              <div class="rating-selection">
                <span class="rating-label">评分：</span>
                <div class="star-rating">
                  <span 
                    v-for="i in 5" 
                    :key="i" 
                    class="star" 
                    :class="{ filled: i <= commentRating }"
                    @click="commentRating = i"
                  >★</span>
                </div>
              </div>
              <button 
                class="submit-comment-btn" 
                @click="submitComment" 
                :disabled="submittingComment || newComment.trim().length < 5 || newComment.trim().length > 500"
              >
                {{ submittingComment ? '提交中...' : '提交评论' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 推荐商品区域 -->
        <div class="card-container">
          <div class="recommended-products">
            <h2 class="section-title">为你推荐</h2>
            <div v-if="recommendedProducts.length === 0" class="empty-recommendations">
              <p>暂无推荐商品</p>
            </div>
            <div v-else class="recommended-list">
              <div 
                v-for="item in recommendedProducts" 
                :key="item.id" 
                class="product-card"
                @click="goToProductDetail(item.id)"
              >
                <img :src="item.imageUrls && item.imageUrls.length > 0 ? item.imageUrls[0] : 'https://via.placeholder.com/300x300'" :alt="item.title" class="product-image">
                <div class="product-info">
                  <h3 class="product-title">{{ item.title }}</h3>
                  <div class="product-meta">
                    <span class="product-price"><span>¥</span>{{ formatPrice(item.price) }}</span>
                    <div class="product-seller">
                      <img :src="item.sellerAvatar || defaultAvatar" :alt="item.sellerName || '未知卖家'">
                      <span>{{ item.sellerName || '未知卖家' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getProductById, getRecommendedProducts } from '@/api/product';
import { getProductReviews, submitReview, getReviewStats } from '@/api/review';
import { addFavorite, removeFavorite, checkFavorite, getProductFavoriteCount } from '@/api/favorite';
import { getOrCreateConversation } from '@/api/message';
import { useUserStore } from '@/stores/user';
import FloatingActionButtons from '@/components/FloatingActionButtons.vue';

// 路由参数
const route = useRoute();
const router = useRouter();

// 用户状态
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 加载状态
const isLoading = ref(false);

// 商品数据
const product = ref({
  id: null,
  name: '',
  category: '',
  price: 0,
  condition: '',
  location: '',
  deliveryMethod: '',
  description: '',
  images: [],
  postedDate: '',
  sellerName: '',
  sellerId: null,
  sellerAvatar: '',
  supportOfficialVerification: false,
  verificationFee: 0
});

// 当前选中的图片
const currentImageIndex = ref(0);
const currentImage = computed(() => {
  return product.value.images[currentImageIndex.value];
});

// 选择图片
const selectImage = (index) => {
  currentImageIndex.value = index;
};

// 用户评论
const reviews = ref([]);
const reviewLoading = ref(false);
const reviewStats = ref({ reviewCount: 0, averageRating: 0 });

// 新评论
const newComment = ref('');
const commentRating = ref(5); // 默认5星
const submittingComment = ref(false);

// 收藏相关状态
const isFavorited = ref(false);
const favoriteCount = ref(0);
const favoriteLoading = ref(false);

// 获取商品评价数据
const fetchReviews = async (productId) => {
  if (!productId) return;
  
  reviewLoading.value = true;
  try {
    const response = await getProductReviews(productId);
    if (response && response.code === 200) {
      reviews.value = response.data || [];
    } else {
      console.error('获取评价数据失败:', response?.message || '未知错误');
    }
    
    // 获取评价统计信息
    const statsResponse = await getReviewStats(productId);
    if (statsResponse && statsResponse.code === 200) {
      reviewStats.value = statsResponse.data || { reviewCount: 0, averageRating: 0 };
    }
  } catch (error) {
    console.error('获取评价数据失败:', error);
  } finally {
    reviewLoading.value = false;
  }
};

// 提交评论
const submitComment = async () => {
  if (!userInfo.value) {
    alert('请先登录后再评论');
    router.push('/login');
    return;
  }
  
  const commentText = newComment.value.trim();
  if (!commentText) {
    alert('评论内容不能为空');
    return;
  }
  
  // 添加评论长度验证
  if (commentText.length < 5) {
    alert('评论内容太短，至少需要5个字符');
    return;
  }
  
  if (commentText.length > 500) {
    alert('评论内容太长，最多500个字符');
    return;
  }
  
  if (commentRating.value < 1 || commentRating.value > 5) {
    alert('请选择评分(1-5星)');
    return;
  }
  
  submittingComment.value = true;
  try {
    const reviewData = {
      productId: Number(route.params.id),
      rating: commentRating.value,
      comment: commentText
    };
    
    const response = await submitReview(reviewData);
    if (response && response.code === 200) {
      alert('评论发布成功');
      newComment.value = '';
      commentRating.value = 5;
      
      // 重新获取评价列表
      await fetchReviews(route.params.id);
    } else {
      alert(`评论发布失败: ${response?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('提交评论失败:', error);
    // 提供更详细的错误信息
    if (error.response && error.response.status === 400) {
      alert('评论发布失败: 评论内容长度应在5-500字符之间');
    } else {
      alert(`评论发布失败: ${error.message || '未知错误'}`);
    }
  } finally {
    submittingComment.value = false;
  }
};

// 推荐商品
const recommendedProducts = ref([]);

// 检查商品是否已收藏
const checkProductFavorite = async (productId) => {
  if (!userInfo.value) return;
  
  try {
    const response = await checkFavorite(productId);
    if (response && response.code === 200) {
      isFavorited.value = response.data;
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
  }
};

// 获取商品收藏数量
const fetchFavoriteCount = async (productId) => {
  try {
    const response = await getProductFavoriteCount(productId);
    if (response && response.code === 200) {
      favoriteCount.value = response.data;
    }
  } catch (error) {
    console.error('获取收藏数量失败:', error);
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!userInfo.value) {
    alert('请先登录后再收藏');
    router.push('/login');
    return;
  }
  
  if (favoriteLoading.value) return;
  
  favoriteLoading.value = true;
  try {
    if (isFavorited.value) {
      // 取消收藏
      const response = await removeFavorite(product.value.id);
      if (response && response.code === 200) {
        isFavorited.value = false;
        favoriteCount.value = Math.max(0, favoriteCount.value - 1);
      } else {
        alert(`取消收藏失败: ${response?.message || '未知错误'}`);
      }
    } else {
      // 添加收藏
      const response = await addFavorite(product.value.id);
      if (response && response.code === 200) {
        isFavorited.value = true;
        favoriteCount.value += 1;
      } else {
        alert(`收藏失败: ${response?.message || '未知错误'}`);
      }
    }
  } catch (error) {
    console.error('操作收藏失败:', error);
    alert(`操作失败: ${error.message || '未知错误'}`);
  } finally {
    favoriteLoading.value = false;
  }
};

// 获取商品数据
const fetchProductData = async () => {
  try {
    // 从路由获取商品ID
    const productId = route.params.id;
    
    if (productId) {
      // 显示加载状态
      isLoading.value = true;
      
      // 调用API获取商品数据
      const response = await getProductById(productId);
      if (response && response.code === 200) {
        // 转换API返回的数据格式以适应前端显示
        const productData = response.data;
        
        product.value = {
          id: productData.id,
          name: productData.title,
          category: productData.categoryName || '未分类',
          price: productData.price,
          condition: productData.condition || '未知',
          location: productData.location || '未知',
          deliveryMethod: productData.deliveryMethod || '未提供',
          description: productData.description || '暂无描述',
          images: productData.imageUrls && productData.imageUrls.length > 0 
            ? productData.imageUrls 
            : ['https://via.placeholder.com/500x500?text=No+Image'],
          postedDate: productData.postedDate 
            ? new Date(productData.postedDate).toLocaleString() 
            : '未知',
          sellerName: productData.sellerName || '未知卖家',
          sellerId: productData.sellerId,
          sellerAvatar: productData.sellerAvatar,
          supportOfficialVerification: productData.supportOfficialVerification || false,
          verificationFee: productData.verificationFee || 0
        };
        
        console.log('获取到商品数据:', product.value);
        
        // 获取商品评价
        await fetchReviews(productId);
        
        // 获取商品收藏状态和数量
        await checkProductFavorite(productId);
        await fetchFavoriteCount(productId);
      } else {
        console.error('获取商品数据失败:', response?.message || '未知错误');
        // 可以显示错误提示
      }
    }

    // 调用API获取推荐商品数据
    const recommendedResponse = await getRecommendedProducts(productId);
    if (recommendedResponse && recommendedResponse.code === 200) {
      recommendedProducts.value = recommendedResponse.data;
    } else {
      console.error('获取推荐商品数据失败:', recommendedResponse?.message || '未知错误');
    }
  } catch (error) {
    console.error('获取商品数据失败:', error);
    // 可以显示错误提示
  } finally {
    // 隐藏加载状态
    isLoading.value = false;
  }
};

// 跳转到商品详情页面
const goToProductDetail = (id) => {
  // 如果点击的是当前商品，不做任何操作
  if (id === Number(route.params.id)) {
    return;
  }
  // 跳转到新的商品详情页
  router.push({ name: 'ProductDetail', params: { id } });
};

// 私聊卖家
const chatWithSeller = async () => {
  if (!userInfo.value) {
    alert('请先登录后再私聊卖家');
    router.push('/login');
    return;
  }
  
  if (!product.value.sellerId) {
    alert('卖家信息不完整，无法私聊');
    return;
  }
  
  try {
    // 显示加载状态
    isLoading.value = true;
    
    // 获取或创建与卖家的会话
    const response = await getOrCreateConversation(product.value.sellerId);
    
    if (response && response.code === 200) {
      const conversationId = response.data;
      // 跳转到聊天界面，并传递会话ID
      router.push({
        path: '/chat',
        query: { conversationId }
      });
    } else {
      alert('创建会话失败，请稍后再试');
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    alert('创建会话失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 监听路由参数变化，当路由参数改变时重新获取商品数据
watch(
  () => route.params.id,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      // 重置当前图片索引
      currentImageIndex.value = 0;
      // 重置收藏状态
      isFavorited.value = false;
      favoriteCount.value = 0;
      // 重新获取商品数据
      fetchProductData();
    }
  }
);

onMounted(async () => {
  // 用户信息已在路由守卫中预先加载，不需要在这里重复加载
  
  // 获取商品数据
  fetchProductData();
});

// 价格格式化函数
const formatPrice = (price) => {
  return price.toLocaleString();
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 立即购买
const buyNow = () => {
  if (!userInfo.value) {
    alert('请先登录后再购买');
    router.push('/login');
    return;
  }
  
  // 跳转到订单创建页面
  router.push(`/order/create/${product.value.id}`);
};
</script>

<style scoped>
.product-detail-page {
  font-family: 'Noto Sans SC', sans-serif;
  background-color: #F5F5F5;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 卡片容器样式 */
.card-container {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  margin: 25px 0;
  padding: 30px;
}

/* 顶部导航 */
.top-nav {
  background: #FFFFFF;
  padding: 16px 0;
  border-bottom: 1px solid #EFEFEF;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: #FF0000;
  text-decoration: none;
}

.search-area {
  flex-grow: 1;
  margin: 0 32px;
  max-width: 600px;
}

.search-bar {
  display: flex;
  border: 2px solid #FF0000;
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: none;
  padding: 10px 20px;
  width: 100%;
  font-size: 16px;
}
.search-input:focus {
  outline: none;
}

.search-button {
  background: #FF0000;
  border: none;
  color: white;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}

.nav-right {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF0000;
}

/* 商品详情区域 */
.product-main {
  display: flex;
  gap: 40px;
  margin: 10px 0 20px;
  padding: 10px;
}

/* 商品图片区域 */
.product-images {
  flex: 1;
  max-width: 550px;
}

.main-image {
  width: 100%;
  height: 450px;
  border: 1px solid #EFEFEF;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.thumbnail-list {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.thumbnail-item {
  width: 90px;
  height: 90px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.thumbnail-item.active {
  border-color: #FF0000;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.thumbnail-item:hover:not(.active) {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品信息区域 */
.product-info {
  flex: 1;
}

.product-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.3;
}

.price-range {
  font-size: 32px;
  color: #FF0000;
  margin-bottom: 25px;
  font-weight: 700;
}

.price-symbol {
  font-size: 24px;
  margin-right: 4px;
}

/* 验货标识样式 */
.verification-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  margin-top: 10px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.verification-icon {
  font-size: 16px;
}

.verification-text {
  font-weight: bold;
}

.verification-desc {
  font-size: 12px;
  opacity: 0.9;
}

.product-meta {
  background-color: #F9F9F9;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  border: 1px solid #EFEFEF;
}

.meta-item {
  margin-bottom: 15px;
  display: flex;
  font-size: 16px;
}

.meta-label {
  color: #666;
  width: 120px;
  flex-shrink: 0;
  font-weight: 500;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

.seller-info-value {
  display: flex;
  align-items: center;
  gap: 10px;
}

.seller-profile-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.2s;
}

.seller-profile-link:hover {
  color: #FF0000;
}

.seller-profile-link:hover .seller-avatar {
  border-color: #FF0000;
  transform: scale(1.05);
}

.seller-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #EFEFEF;
  transition: all 0.2s;
}

.description-item {
  align-items: flex-start;
}

.description-value {
  white-space: pre-line;
  line-height: 1.6;
  margin-top: 5px;
}

.action-buttons {
  display: flex;
  gap: 20px;
  margin-top: 35px;
}

.buy-now-btn {
  background-color: #FF0000;
  color: white;
  border: none;
  padding: 15px 50px;
  font-size: 18px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(255, 0, 0, 0.2);
}

.buy-now-btn:hover {
  background-color: #E60000;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(255, 0, 0, 0.3);
}

.chat-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #FFFFFF;
  color: #666;
  border: 1px solid #DEDEDE;
  padding: 15px 30px;
  font-size: 18px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.chat-btn:hover {
  color: #FF0000;
  border-color: #FF0000;
  background-color: #FFF0F0;
  transform: translateY(-2px);
}

.add-favorite-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #FFFFFF;
  color: #666;
  border: 1px solid #DEDEDE;
  padding: 15px 30px;
  font-size: 18px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.add-favorite-btn:hover {
  color: #FF0000;
  border-color: #FF0000;
  background-color: #FFF0F0;
}

.add-favorite-btn.favorited {
  color: #FF0000;
  border-color: #FF0000;
  background-color: #FFF0F0;
}

.add-favorite-btn.favorited svg {
  fill: #FF0000;
}

/* 用户评价区域 */
.user-reviews {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EFEFEF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.average-rating {
  font-size: 16px;
  color: #666;
}

.average-rating strong {
  color: #FF0000;
}

.review-loading, .empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #999;
}

.spinner.small {
  width: 30px;
  height: 30px;
  border-width: 3px;
  margin-bottom: 15px;
}

.review-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-item {
  display: flex;
  gap: 15px;
}

.review-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}

.review-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 5px;
}

.review-username {
  font-size: 14px;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.review-content {
  flex: 1;
  background-color: #F9F9F9;
  padding: 15px;
  border-radius: 8px;
}

.review-rating {
  margin-bottom: 10px;
}

.star {
  color: #ddd;
  font-size: 18px;
  cursor: pointer;
}

.star.filled {
  color: #FFCC00;
}

.review-content p {
  margin: 0 0 10px;
}

.review-date {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 发表评论区域 */
.post-comment {
  margin-bottom: 40px;
}

.login-prompt {
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.login-prompt a {
  color: #FF0000;
  font-weight: bold;
  text-decoration: none;
}

.comment-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.comment-input {
  width: 100%;
  padding: 15px;
  border: 1px solid #DEDEDE;
  border-radius: 4px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
}

.comment-input:focus {
  outline: none;
  border-color: #FF0000;
}

.rating-selection {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rating-label {
  font-weight: 500;
}

.star-rating {
  display: flex;
  gap: 5px;
}

.submit-comment-btn {
  align-self: flex-end;
  background-color: #FF0000;
  color: white;
  border: none;
  padding: 10px 25px;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-comment-btn:hover:not(:disabled) {
  background-color: #E60000;
}

.submit-comment-btn:disabled {
  background-color: #ffb3b3;
  cursor: not-allowed;
}

/* 推荐商品区域 */
.recommended-products {
  margin-bottom: 20px;
}

.recommended-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.recommended-list .product-card {
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #EFEFEF;
  cursor: pointer;
}

.recommended-list .product-card:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.recommended-list .product-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  display: block;
}

.recommended-list .product-info {
  padding: 12px;
}

.recommended-list .product-title {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
  height: 48px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommended-list .product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommended-list .product-price {
  font-size: 18px;
  font-weight: bold;
  color: #FF0000;
}

.recommended-list .product-price span {
  font-size: 14px;
}

.recommended-list .product-seller {
  display: flex;
  align-items: center;
  gap: 6px;
}

.recommended-list .product-seller img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.recommended-list .product-seller span {
  font-size: 14px;
  color: #666;
}

.empty-recommendations {
  text-align: center;
  padding: 30px 0;
  color: #999;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-main {
    flex-direction: column;
  }
  
  .product-images {
    max-width: 100%;
  }
  
  .main-image {
    height: 300px;
  }
  
  .recommended-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #FF0000;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 发表评论区域样式补充 */
.comment-length {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 5px;
}

.comment-length.error {
  color: #FF0000;
}
</style> 