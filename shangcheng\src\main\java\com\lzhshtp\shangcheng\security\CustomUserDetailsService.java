package com.lzhshtp.shangcheng.security;

import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 自定义UserDetailsService实现
 */
@Service
@RequiredArgsConstructor
public class CustomUserDetailsService implements UserDetailsService {
    
    private final UserMapper userMapper;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 根据用户名查询用户
        User user = userMapper.findByUsername(username);
        
        // 如果用户不存在，则抛出异常
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        // 如果用户被禁用，则抛出异常
        if (!user.getIsActive()) {
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }
        
        // 创建并返回UserDetails对象
        // 将数据库角色映射为标准的Spring Security角色
        String springRole;
        switch (user.getRole()) {
            case "admin":
                springRole = "ROLE_ADMIN";
                break;
            case "general_user":
                springRole = "ROLE_USER";
                break;
            default:
                springRole = "ROLE_USER";
        }

        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPasswordHash(),
                Collections.singletonList(new SimpleGrantedAuthority(springRole))
        );
    }
} 