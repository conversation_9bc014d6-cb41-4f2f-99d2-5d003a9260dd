spring:
  servlet:
    multipart:
      max-file-size: 10MB      # 单个文件大小限制
      max-request-size: 100MB   # 单次请求总文件大小限制
      enabled: true

  # Redis配置（模仿qifancode）
  data:
    redis:
      database: 0
      timeout: 10s
      host: localhost
      port: 6379
      password: 123456  # 如果Redis没有密码，留空
      repositories:
        enabled: false  # 重要：禁用Redis repositories，避免冲突

  # AI配置 - 使用阿里云AI Starter
  ai:
    # 阿里灵积配置
    dash-scope:
      api-key: sk-38560676cdd14b8f89ff34405e044645  # 请替换为你的实际API Key
      enabled: true
      chat:
        model: qwen-max
      embedding:
        model: text-embedding-v2

    # 向量数据库配置
    vectorstore:
      redis:
        initialize-schema: true
        index-name: default
        prefix: default

aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com  # 替换为你的OSS endpoint
    accessKeyId: LTAI5tQriaoEmVT4qj45EVSb         # 替换为你的AccessKeyId
    accessKeySecret: ****************************** # 替换为你的AccessKeySecret
    bucketName: liuzhuoheng           # 替换为你的Bucket名称

  # 内容安全配置
  content-security:
    # 请替换为你的真实AccessKey ID
    access-key-id: ${ALIYUN_CONTENT_SECURITY_ACCESS_KEY_ID:LTAI5tLz2GedqiZbCDzYAjZ7}
    # 请替换为你的真实AccessKey Secret
    access-key-secret: ${ALIYUN_CONTENT_SECURITY_ACCESS_KEY_SECRET:******************************}
    # 图片审核服务endpoint（根据你的地区选择）
    endpoint: imageaudit.cn-shanghai.aliyuncs.com
    region-id: cn-shanghai

# 支付宝沙箱配置
alipay:
  app-id: 9021000150623401  # 请替换为你的支付宝沙箱APPID
  merchant-private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCEoF23vzyNgEogaUGtjyM1TWtzcgizlnIAWs9IBUJAaf/NE7xRpw91HEGnsc7WaIx2Ln12p4/V4lxW5u1o2/sPQEQGfAgifluWYIPONSaXvH+SE77XNwOLcds0cIScIyXZKkrLPDXAHfMcRXlvX3l+thiUXst+3S2oFaBhcyKdAQXgMEETLu9usTdfupCBrhhVflQJFbhJ9qI2PWUB0YyvIaXn8brHo6NRH+nYyDqBebSkTXftK/7D3jMJEBGGR8d7TTdYXMyMXGOsAPgLwL0PKjhezBI4k1wzSUEuKjERHqw82RJD77faP1Geo4HYsPtscFPMrrKC8B3D8FNAMmHLAgMBAAECggEAeoe+jfh8dS7FTVRxfWv011YDo9EDAQhJotJDFwLkxqYn0swc/EMgIM5OSiQYODa3Lqic+XatiKltLkyDopE6TkjNkEAfM98RtsjMfULVuPJJ2ier9SG6gKZcIwKhQa+nVA+iskmaTzhcVw5ItwC6Orr50noR/89QUy5H0kCRBgOLWJlnFEiwFngXtzSD4tZKQfIknEvIUXUlSC0M4O7tpFwEHn+2mo7hcvqIOgPhOAUjPKM90k93KUmDjyNopSp1GnZAiRFxUKzOF+ikZlHw8syvbtjQff+lL8oStCpRnA72BmxZ1Am37OYggiP5KHeqbrQyErNlp/RJ87tQUgjAgQKBgQC8/oGir4C/BnWQry0AmAzlQXSqi4xct5nniaE6kJKqVUgN+O63TaBj+h5NqUVM0pM9C2hxs38qAO6ct9lfP7+Zww3WFhktHYEdgX4XyVaJajZyH744+AT8BMnnWGntygaadjKDr/amoa/cpC0CrMFnzAeve+VBfkxqrXgKQW4lWwKBgQCzpc9LYI16tL44k8jK42E/9uEhL0PljZqMcpdgC7BNrcc5jYwmZAVCi5py84pSg6D+t94HeGFP2GUFnRpi4yA1rnVIiTx+H7t3qkEjESgYU/g/J1NCVSvqk5oLdFDFIu7uHoQwtrIR65E+YniLzZzub2LWcrCLkrGX5JLR9h6wUQKBgQCefs3W0lolWVRk5xPViK5f4NnnYKpw4yS10W0eATqOkICh/x6rKPWWgp7BQHC2sW37ZsLLf93oKunxEhnA0GRtne/Pl667cB/kz8EgT6A8E4/fe5F6kz0S4S01z6beXWvWQ8OvPnWaXsRuRFze/mQgGTJkz4F+wF1zpVCjQSYPOwKBgDyOGKj029vPzwrtLdLOAHr6Mfronr9jEwRioAn9Bqm4eR46S1Kphmhe/GnhE/AqdHtH6SyldE/gxnS09y78FmBh9SDFOcHd2mhE2H4Ei3pf258WF2eVpB8HL/bKd2tsEhPr720oOC/4MtZPhQjC/zIUE8HrmP0DLp/fqWnix4dxAoGACw8yHobIBKOZj+ks5SAHzFvnbmWH5qRj7vZ5rpvFm6sbjgp4h+OCks3XR9QCKLj/k27Gb7U+8qH82He9GwioaYA3e0gWhbz3M+XWqt/rkxvEK2pJn+aRbPRn36V4BmQjTOy6CYKXmb/4E598/FfQLoQxCSnJH6yFP1QMIGKI628=  # 请替换为你的商户私钥
  alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtvbRbqRRB19ho4H9I5f4kFR2e+ltz3Bez6HTUlPmHsoSDGjV2sH2JAAkM274TbZMpt7Fw0mO8i/+TxCaZ2DUVPOykWOOcIHulh4+fWayYYVImfbO83vdouFJ9RhnfsrPVsSdBa2OEskPCxb1Bm9XbPW5lXA+y/x27h5E9KeO2gJxm83zS25vYDSdeViL8WI2g3dGBGV1Shv8FVHH0Ibx+l7+YuJnLGhvV43NBNRb12uekiC3KrA7dSOp9Uka07anbTJuiSuZUChcdHYZL61t22V1BjOyr/86rjfomf9WCWsaME/6e8HNd85EsLSDyGYvlN8C72I6Fz3HLT/4hyNIlwIDAQAB  # 请替换为你的支付宝公钥
  # 订单支付回调地址
  order-notify-url: http://localhost:8080/api/orders/pay/notify  # 订单异步通知地址
  order-return-url: http://localhost:8080/api/orders/pay/return  # 订单同步通知地址
  # 充值回调地址
  recharge-notify-url: http://localhost:8080/api/recharge/alipay/notify  # 充值异步通知地址
  recharge-return-url: http://localhost:8080/api/recharge/alipay/return  # 充值同步通知地址
  sign-type: RSA2
  charset: utf-8
  gateway-url: https://openapi-sandbox.dl.alipaydev.com/gateway.do  # 沙箱环境网关
  # 同步处理模式（开发环境使用，生产环境应设为false）
  sync-mode: true  # 同步模式，支付后立即成功，不依赖异步回调

# 短信配置
sms:
  aliyun:
    app-code: 75d9d10117da4f46bce6030e9499d841  # 请替换为你的实际AppCode
    host: https://dfsns.market.alicloudapi.com
    path: /data/send_sms
    template-id: CST_ptdie100
    code-expire-minutes: 5
    send-limit-minutes: 1
    daily-limit: 10
