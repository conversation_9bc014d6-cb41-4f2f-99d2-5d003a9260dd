<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.MessageMapper">

    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.Message">
        <id property="lzhshtp_message_id" column="lzhshtp_message_id"/>
        <result property="lzhshtp_conversation_id" column="lzhshtp_conversation_id"/>
        <result property="lzhshtp_sender_id" column="lzhshtp_sender_id"/>
        <result property="lzhshtp_content" column="lzhshtp_content"/>
        <result property="lzhshtp_sent_at" column="lzhshtp_sent_at"/>
        <result property="lzhshtp_is_read" column="lzhshtp_is_read"/>
        <result property="lzhshtp_is_system_message" column="lzhshtp_is_system_message"/>
    </resultMap>

    <insert id="createMessage" parameterType="com.lzhshtp.shangcheng.model.Message" useGeneratedKeys="true" keyProperty="lzhshtp_message_id">
        INSERT INTO tb_lzhshtp_messages (lzhshtp_conversation_id, lzhshtp_sender_id, lzhshtp_content, lzhshtp_is_system_message)
        VALUES (#{lzhshtp_conversation_id}, #{lzhshtp_sender_id}, #{lzhshtp_content}, #{lzhshtp_is_system_message})
    </insert>

    <select id="findMessagesByConversationId" resultMap="BaseResultMap">
        SELECT * FROM tb_lzhshtp_messages
        WHERE lzhshtp_conversation_id = #{conversationId}
        ORDER BY lzhshtp_sent_at DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countMessagesByConversationId" resultType="int">
        SELECT COUNT(*) FROM tb_lzhshtp_messages WHERE lzhshtp_conversation_id = #{conversationId}
    </select>

    <select id="findLatestMessageByConversationId" resultMap="BaseResultMap">
        SELECT * FROM tb_lzhshtp_messages
        WHERE lzhshtp_conversation_id = #{conversationId}
        ORDER BY lzhshtp_sent_at DESC LIMIT 1
    </select>

    <update id="markMessagesAsRead">
        UPDATE tb_lzhshtp_messages SET
        lzhshtp_is_read = true
        WHERE lzhshtp_conversation_id = #{conversationId}
          AND lzhshtp_sender_id != #{currentUserId}
          AND lzhshtp_is_read = false
    </update>

    <select id="countUnreadMessages" resultType="int">
        SELECT COUNT(*) FROM tb_lzhshtp_messages
        WHERE lzhshtp_conversation_id = #{conversationId}
          AND lzhshtp_sender_id != #{userId}
          AND lzhshtp_is_read = false
    </select>

    <delete id="deleteMessage">
        DELETE FROM tb_lzhshtp_messages WHERE lzhshtp_message_id = #{messageId}
    </delete>

</mapper> 