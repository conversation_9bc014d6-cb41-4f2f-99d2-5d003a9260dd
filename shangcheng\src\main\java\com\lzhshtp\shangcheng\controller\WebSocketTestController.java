package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.websocket.ChatWebSocketHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket测试控制器
 */
@RestController
@RequestMapping("/api/websocket")
public class WebSocketTestController {

    @GetMapping("/status")
    public Map<String, Object> getWebSocketStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("onlineUsers", ChatWebSocketHandler.getOnlineUserCount());
        status.put("status", "WebSocket服务正常运行");
        return status;
    }
}
