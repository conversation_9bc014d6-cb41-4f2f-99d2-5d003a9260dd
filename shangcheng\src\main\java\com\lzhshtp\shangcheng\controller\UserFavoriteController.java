package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.UserFavoriteDTO;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserFavoriteService;
import com.lzhshtp.shangcheng.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户收藏控制器
 */
@RestController
@RequestMapping("/api/favorites")
public class UserFavoriteController {

    @Autowired
    private UserFavoriteService userFavoriteService;
    
    @Autowired
    private UserService userService;

    /**
     * 添加收藏
     */
    @PostMapping("/{productId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<String> addFavorite(@PathVariable Long productId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return userFavoriteService.addFavorite(user.getUserId(), productId);
    }

    /**
     * 取消收藏（通过收藏ID）
     */
    @DeleteMapping("/{favoriteId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<String> removeFavorite(@PathVariable Long favoriteId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return userFavoriteService.removeFavorite(favoriteId, user.getUserId());
    }

    /**
     * 取消收藏（通过商品ID）
     */
    @DeleteMapping("/product/{productId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<String> removeFavoriteByProduct(@PathVariable Long productId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return userFavoriteService.removeFavoriteByProduct(user.getUserId(), productId);
    }

    /**
     * 查询用户是否已收藏商品
     */
    @GetMapping("/check/{productId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> isFavorited(@PathVariable Long productId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return userFavoriteService.isFavorited(user.getUserId(), productId);
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<List<UserFavoriteDTO>> getUserFavorites() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        return userFavoriteService.getUserFavorites(user.getUserId());
    }

    /**
     * 获取商品被收藏次数
     */
    @GetMapping("/count/{productId}")
    public ApiResponse<Integer> getProductFavoriteCount(@PathVariable Long productId) {
        return userFavoriteService.getProductFavoriteCount(productId);
    }
} 