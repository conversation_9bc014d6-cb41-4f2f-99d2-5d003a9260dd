package io.github.qifan777.knowledge.ai.messge.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import io.github.qifan777.knowledge.ai.messge.AiMessage;
import io.github.qifan777.knowledge.ai.messge.AiMessageDraft;
import io.github.qifan777.knowledge.ai.messge.AiMessageFetcher;
import io.github.qifan777.knowledge.ai.messge.AiMessageProps;
import io.github.qifan777.knowledge.ai.session.AiSession;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.util.List;
import java.util.Objects;
import org.babyfish.jimmer.Input;
import org.babyfish.jimmer.impl.util.DtoPropAccessor;
import org.babyfish.jimmer.internal.FixedInputField;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.sql.fetcher.ViewMetadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.ai.chat.messages.MessageType;

/**
 * 历史消息
 */
@GeneratedBy(
        file = "<dive-into-spring-ai>/src/main/dto/AiMessage.dto"
)
@JsonDeserialize(
        builder = AiMessageInput.Builder.class
)
public class AiMessageInput implements Input<AiMessage> {
    public static final ViewMetadata<AiMessage, AiMessageInput> METADATA = 
        new ViewMetadata<AiMessage, AiMessageInput>(
            AiMessageFetcher.$
                .type()
                .textContent()
                .medias()
                .session(),
            AiMessageInput::new
    );

    private static final DtoPropAccessor SESSION_ID_ACCESSOR = new DtoPropAccessor(
        true,
        new int[] { AiMessageDraft.Producer.SLOT_SESSION },
        DtoPropAccessor.idReferenceGetter(AiSession.class, AiMessageProps.SESSION.unwrap().getAssociatedIdConverter(false)),
        DtoPropAccessor.idReferenceSetter(AiSession.class, AiMessageProps.SESSION.unwrap().getAssociatedIdConverter(false))
    );

    @FixedInputField
    private MessageType type;

    @FixedInputField
    private String textContent;

    private List<AiMessage.Media> medias;

    @FixedInputField
    private String sessionId;

    public AiMessageInput() {
    }

    public AiMessageInput(@NotNull AiMessage base) {
        this.type = base.type();
        this.textContent = base.textContent();
        this.medias = ((ImmutableSpi)base).__isLoaded(PropId.byIndex(AiMessageDraft.Producer.SLOT_MEDIAS)) ? base.medias() : null;
        this.sessionId = SESSION_ID_ACCESSOR.get(base);
    }

    /**
     * 消息类型(用户/助手/系统)
     */
    @NotNull
    public MessageType getType() {
        if (type == null) {
            throw new IllegalStateException("The property \"type\" is not specified");
        }
        return type;
    }

    public void setType(@NotNull MessageType type) {
        this.type = type;
    }

    /**
     * 消息内容
     */
    @NotNull
    public String getTextContent() {
        if (textContent == null) {
            throw new IllegalStateException("The property \"textContent\" is not specified");
        }
        return textContent;
    }

    public void setTextContent(@NotNull String textContent) {
        this.textContent = textContent;
    }

    @Nullable
    public List<AiMessage.Media> getMedias() {
        return medias;
    }

    public void setMedias(@Nullable List<AiMessage.Media> medias) {
        this.medias = medias;
    }

    /**
     * 会话
     */
    @NotNull
    public String getSessionId() {
        if (sessionId == null) {
            throw new IllegalStateException("The property \"sessionId\" is not specified");
        }
        return sessionId;
    }

    public void setSessionId(@NotNull String sessionId) {
        this.sessionId = sessionId;
    }

    @Override
    public AiMessage toEntity() {
        return AiMessageDraft.$.produce(__draft -> {
            __draft.setType(type);
            __draft.setTextContent(textContent);
            __draft.setMedias(medias);
            SESSION_ID_ACCESSOR.set(__draft, sessionId);
        });
    }

    @Override
    public int hashCode() {
        int hash = Objects.hashCode(type);
        hash = hash * 31 + Objects.hashCode(textContent);
        hash = hash * 31 + Objects.hashCode(medias);
        hash = hash * 31 + Objects.hashCode(sessionId);
        return hash;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        AiMessageInput other = (AiMessageInput) o;
        if (!Objects.equals(type, other.type)) {
            return false;
        }
        if (!Objects.equals(textContent, other.textContent)) {
            return false;
        }
        if (!Objects.equals(medias, other.medias)) {
            return false;
        }
        if (!Objects.equals(sessionId, other.sessionId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("AiMessageInput").append('(');
        builder.append("type=").append(type);
        builder.append(", textContent=").append(textContent);
        builder.append(", medias=").append(medias);
        builder.append(", sessionId=").append(sessionId);
        builder.append(')');
        return builder.toString();
    }

    @JsonPOJOBuilder(
            withPrefix = ""
    )
    public static class Builder {
        private MessageType type;

        private String textContent;

        private List<AiMessage.Media> medias;

        private String sessionId;

        public Builder type(MessageType type) {
            this.type = Objects.requireNonNull(type, "The property \"type\" cannot be null");
            return this;
        }

        public Builder textContent(String textContent) {
            this.textContent = Objects.requireNonNull(textContent, "The property \"textContent\" cannot be null");
            return this;
        }

        public Builder medias(List<AiMessage.Media> medias) {
            this.medias = medias;
            return this;
        }

        public Builder sessionId(String sessionId) {
            this.sessionId = Objects.requireNonNull(sessionId, "The property \"sessionId\" cannot be null");
            return this;
        }

        public AiMessageInput build() {
            AiMessageInput _input = new AiMessageInput();
            if (type == null) {
                throw Input.unknownNonNullProperty(AiMessageInput.class, "type");
            }
            _input.setType(type);
            if (textContent == null) {
                throw Input.unknownNonNullProperty(AiMessageInput.class, "textContent");
            }
            _input.setTextContent(textContent);
            _input.setMedias(medias);
            if (sessionId == null) {
                throw Input.unknownNonNullProperty(AiMessageInput.class, "sessionId");
            }
            _input.setSessionId(sessionId);
            return _input;
        }
    }
}
