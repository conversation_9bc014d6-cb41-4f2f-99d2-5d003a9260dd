import './assets/main.css'

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
// 不再直接导入adminRouter，因为已经在router/index.js中集成了管理员路由
// import { adminRouter } from './admin';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css'; // 引入 Element Plus 的完整样式

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router); // 只使用一个路由实例
// app.use(adminRouter); // 移除这行，避免冲突
app.use(ElementPlus);

app.mount('#app');
