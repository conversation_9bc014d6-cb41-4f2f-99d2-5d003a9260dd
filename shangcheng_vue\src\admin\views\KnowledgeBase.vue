<template>
  <div class="knowledge-base-page">
    <div class="page-header">
      <h1>知识库管理</h1>
      <p class="description">上传文档或添加文本内容到知识库，为AI客服提供专业知识支持</p>
    </div>

    <!-- 操作卡片 -->
    <div class="action-cards">
      <!-- 文档上传卡片 -->
      <el-card class="action-card">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>文档上传</span>
          </div>
        </template>
        
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :show-file-list="false"
            drag
            accept=".pdf,.doc,.docx,.txt,.md"
          >
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
              <p>将文件拖到此处，或<em>点击上传</em></p>
              <p class="upload-tip">支持 PDF、Word、TXT、Markdown 格式</p>
            </div>
          </el-upload>
          
          <div v-if="uploadLoading" class="upload-progress">
            <el-progress :percentage="uploadProgress" :status="uploadStatus" />
            <p>{{ uploadMessage }}</p>
          </div>
        </div>
      </el-card>

      <!-- 文本添加卡片 -->
      <el-card class="action-card">
        <template #header>
          <div class="card-header">
            <el-icon><EditPen /></el-icon>
            <span>文本知识</span>
          </div>
        </template>
        
        <el-form :model="textForm" label-width="80px">
          <el-form-item label="标题">
            <el-input v-model="textForm.title" placeholder="请输入知识标题" />
          </el-form-item>
          
          <el-form-item label="分类">
            <el-select v-model="textForm.category" placeholder="请选择分类">
              <el-option label="商品信息" value="product" />
              <el-option label="售后服务" value="service" />
              <el-option label="配送政策" value="shipping" />
              <el-option label="支付相关" value="payment" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="内容">
            <el-input
              v-model="textForm.content"
              type="textarea"
              :rows="6"
              placeholder="请输入知识内容..."
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="addTextKnowledge" :loading="textLoading">
              添加知识
            </el-button>
            <el-button @click="resetTextForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 测试检索 -->
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <el-icon><Search /></el-icon>
          <span>测试检索</span>
        </div>
      </template>
      
      <div class="test-section">
        <el-input
          v-model="testQuery"
          placeholder="输入测试问题，检验知识库效果..."
          @keyup.enter="testSearch"
        >
          <template #append>
            <el-button @click="testSearch" :loading="testLoading">
              <el-icon><Search /></el-icon>
              测试
            </el-button>
          </template>
        </el-input>
        
        <div v-if="testResults.length > 0" class="test-results">
          <h4>检索结果：</h4>
          <div v-for="(result, index) in testResults" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-index">#{{ index + 1 }}</span>
              <span class="result-title">{{ result.metadata.title || '未命名' }}</span>
              <el-tag size="small" type="info">{{ result.metadata.category || 'general' }}</el-tag>
            </div>
            <div class="result-content">{{ result.content }}</div>
            <div class="result-meta">
              <span>文件: {{ result.metadata.filename || '文本输入' }}</span>
              <span v-if="result.metadata.upload_time">
                时间: {{ formatTime(result.metadata.upload_time) }}
              </span>
            </div>
          </div>
        </div>
        
        <div v-else-if="testQuery && !testLoading" class="no-results">
          <el-empty description="未找到相关内容" />
        </div>
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <el-icon><QuestionFilled /></el-icon>
          <span>使用说明</span>
        </div>
      </template>
      
      <div class="help-content">
        <h4>📁 文档上传</h4>
        <ul>
          <li>支持 PDF、Word、TXT、Markdown 等格式</li>
          <li>系统会自动提取文本内容并进行向量化</li>
          <li>大文档会自动分割成小片段以提高检索精度</li>
        </ul>
        
        <h4>✏️ 文本知识</h4>
        <ul>
          <li>直接输入文本内容，适合添加FAQ、政策说明等</li>
          <li>建议按分类组织内容，便于管理和检索</li>
          <li>内容过长会自动分割处理</li>
        </ul>
        
        <h4>🔍 RAG检索</h4>
        <ul>
          <li>用户开启"知识库"模式后，AI会基于这里的内容回答</li>
          <li>系统会自动找到最相关的内容片段</li>
          <li>建议定期测试检索效果，优化知识库内容</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, UploadFilled, EditPen, Search, QuestionFilled } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const uploadLoading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadMessage = ref('')
const textLoading = ref(false)
const testLoading = ref(false)
const testQuery = ref('')
const testResults = ref([])

// 文本表单
const textForm = ref({
  title: '',
  category: '',
  content: ''
})

// 上传配置 - Element Plus上传组件需要完整URL，包含/api前缀
const uploadUrl = computed(() => '/api/admin/documents/upload')
const uploadHeaders = computed(() => {
  const adminToken = localStorage.getItem('adminToken')
  return adminToken ? {
    'Authorization': `Bearer ${adminToken}`
  } : {}
})

// 上传前检查
const beforeUpload = (file) => {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'text/markdown'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只支持 PDF、Word、TXT、Markdown 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  uploadLoading.value = true
  uploadProgress.value = 0
  uploadMessage.value = '正在上传文件...'
  return true
}

// 上传成功
const onUploadSuccess = (response) => {
  uploadLoading.value = false
  uploadProgress.value = 100
  uploadStatus.value = 'success'
  uploadMessage.value = response.message || '上传成功'
  ElMessage.success('文档上传成功!')
  
  // 3秒后重置状态
  setTimeout(() => {
    uploadProgress.value = 0
    uploadMessage.value = ''
  }, 3000)
}

// 上传失败
const onUploadError = (error) => {
  uploadLoading.value = false
  uploadStatus.value = 'exception'
  uploadMessage.value = '上传失败'
  ElMessage.error('文档上传失败!')
}

// 添加文本知识
const addTextKnowledge = async () => {
  if (!textForm.value.content.trim()) {
    ElMessage.warning('请输入知识内容')
    return
  }

  textLoading.value = true
  try {
    await request.post('/admin/documents/add-text', textForm.value)
    ElMessage.success('知识添加成功!')
    resetTextForm()
  } catch (error) {
    ElMessage.error('添加失败: ' + (error.response?.data?.message || error.message))
  } finally {
    textLoading.value = false
  }
}

// 重置文本表单
const resetTextForm = () => {
  textForm.value = {
    title: '',
    category: '',
    content: ''
  }
}

// 测试检索
const testSearch = async () => {
  if (!testQuery.value.trim()) {
    ElMessage.warning('请输入测试问题')
    return
  }

  testLoading.value = true
  try {
    const response = await request.post('/admin/documents/search', {
      query: testQuery.value,
      topK: 5
    })
    testResults.value = response.data || []
    
    if (testResults.value.length === 0) {
      ElMessage.info('未找到相关内容，建议添加更多知识')
    }
  } catch (error) {
    ElMessage.error('检索失败: ' + (error.response?.data?.message || error.message))
    testResults.value = []
  } finally {
    testLoading.value = false
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}
</script>

<style scoped>
.knowledge-base-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.action-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.action-card {
  min-height: 400px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.upload-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
}

.upload-progress {
  margin-top: 20px;
  text-align: center;
}

.test-card, .help-card {
  margin-bottom: 20px;
}

.test-section {
  margin-top: 20px;
}

.test-results {
  margin-top: 20px;
}

.result-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.result-index {
  background: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.result-title {
  font-weight: 600;
  color: #303133;
}

.result-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 8px;
}

.result-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 16px;
}

.no-results {
  margin-top: 20px;
  text-align: center;
}

.help-content h4 {
  color: #303133;
  margin: 16px 0 8px 0;
}

.help-content ul {
  color: #606266;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 4px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .action-cards {
    grid-template-columns: 1fr;
  }
}
</style>
