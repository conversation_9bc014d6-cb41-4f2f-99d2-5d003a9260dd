package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.MessageDTO;
import com.lzhshtp.shangcheng.dto.MessageRequest;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.mapper.ConversationMapper;
import com.lzhshtp.shangcheng.mapper.MessageMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Conversation;
import com.lzhshtp.shangcheng.model.Message;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ConversationService;
import com.lzhshtp.shangcheng.service.MessageService;
import com.lzhshtp.shangcheng.websocket.ChatWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ConversationService conversationService;

    @Override
    @Transactional
    public MessageDTO sendMessage(Long senderId, MessageRequest messageRequest) {
        // 获取或创建会话
        Long conversationId = conversationService.getOrCreateConversation(senderId, messageRequest.getReceiverId());
        
        // 创建消息
        Message message = new Message();
        message.setLzhshtp_conversation_id(conversationId);
        message.setLzhshtp_sender_id(senderId);
        message.setLzhshtp_content(messageRequest.getContent());
        message.setLzhshtp_is_read(false);
        message.setLzhshtp_is_system_message(false);
        
        // 保存消息
        messageMapper.createMessage(message);
        
        // 更新会话的最后一条消息预览
        String preview = messageRequest.getContent();
        if (preview.length() > 50) {
            preview = preview.substring(0, 47) + "...";
        }
        conversationService.updateConversationPreview(conversationId, preview);
        
        // 增加接收方的未读消息计数
        conversationService.incrementUnreadCount(conversationId, senderId);

        // 转换为DTO
        MessageDTO messageDTO = convertToDTO(message, senderId);

        // 通过WebSocket实时通知接收方
        try {
            ChatWebSocketHandler.sendMessageToUser(messageRequest.getReceiverId(), messageDTO);
        } catch (Exception e) {
            // WebSocket发送失败不影响消息保存，只记录日志
            System.err.println("WebSocket发送消息失败: " + e.getMessage());
        }

        return messageDTO;
    }
    
    @Override
    @Transactional
    public MessageDTO sendSystemMessage(Long adminId, MessageRequest messageRequest) {
        // 获取或创建会话
        Long conversationId = conversationService.getOrCreateConversation(adminId, messageRequest.getReceiverId());
        
        // 创建消息
        Message message = new Message();
        message.setLzhshtp_conversation_id(conversationId);
        message.setLzhshtp_sender_id(adminId);
        message.setLzhshtp_content(messageRequest.getContent());
        message.setLzhshtp_is_read(false);
        message.setLzhshtp_is_system_message(true); // 标记为系统消息
        
        // 保存消息
        messageMapper.createMessage(message);
        
        // 更新会话的最后一条消息预览
        String preview = "[系统消息] " + messageRequest.getContent();
        if (preview.length() > 50) {
            preview = preview.substring(0, 47) + "...";
        }
        conversationService.updateConversationPreview(conversationId, preview);
        
        // 增加接收方的未读消息计数
        conversationService.incrementUnreadCount(conversationId, adminId);

        // 转换为DTO
        MessageDTO messageDTO = convertToDTO(message, adminId);

        // 通过WebSocket实时通知接收方
        try {
            ChatWebSocketHandler.sendMessageToUser(messageRequest.getReceiverId(), messageDTO);
        } catch (Exception e) {
            // WebSocket发送失败不影响消息保存，只记录日志
            System.err.println("WebSocket发送系统消息失败: " + e.getMessage());
        }

        return messageDTO;
    }

    @Override
    public PageResult<MessageDTO> getConversationMessages(Long conversationId, Long currentUserId, int page, int size) {
        // 验证用户是否是会话参与者
        if (!conversationService.isConversationParticipant(conversationId, currentUserId)) {
            return new PageResult<>(new ArrayList<>(), 0, page, size);
        }
        
        // 计算偏移量
        int offset = (page - 1) * size;
        
        // 获取消息列表
        List<Message> messages = messageMapper.findMessagesByConversationId(conversationId, offset, size);
        
        // 获取消息总数
        int total = messageMapper.countMessagesByConversationId(conversationId);
        
        // 转换为DTO
        List<MessageDTO> messageDTOs = new ArrayList<>();
        for (Message message : messages) {
            messageDTOs.add(convertToDTO(message, currentUserId));
        }
        
        // 标记消息为已读
        markMessagesAsRead(conversationId, currentUserId);
        
        return new PageResult<>(messageDTOs, total, page, size);
    }

    @Override
    @Transactional
    public boolean markMessagesAsRead(Long conversationId, Long currentUserId) {
        // 验证用户是否是会话参与者
        if (!conversationService.isConversationParticipant(conversationId, currentUserId)) {
            return false;
        }
        
        // 标记消息为已读
        messageMapper.markMessagesAsRead(conversationId, currentUserId);
        
        // 重置用户的未读消息计数
        return conversationService.resetUnreadCount(conversationId, currentUserId);
    }

    @Override
    public boolean deleteMessage(Long messageId, Long currentUserId) {
        // 这里可以添加更多的验证逻辑，例如检查消息是否属于当前用户
        // 为简化，这里直接删除消息
        return messageMapper.deleteMessage(messageId) > 0;
    }

    @Override
    public int getTotalUnreadCount(Long userId) {
        // 获取用户的所有会话
        List<Conversation> conversations = conversationMapper.findConversationsByUserId(userId);
        int totalUnreadCount = 0;
        
        // 计算总未读消息数
        for (Conversation conversation : conversations) {
            if (conversation.getLzhshtp_user1_id().equals(userId)) {
                totalUnreadCount += conversation.getLzhshtp_unread_count_user1();
            } else {
                totalUnreadCount += conversation.getLzhshtp_unread_count_user2();
            }
        }
        
        return totalUnreadCount;
    }
    
    /**
     * 将Message对象转换为MessageDTO
     * @param message 消息对象
     * @param currentUserId 当前用户ID
     * @return 消息DTO
     */
    private MessageDTO convertToDTO(Message message, Long currentUserId) {
        MessageDTO dto = new MessageDTO();
        dto.setMessageId(message.getLzhshtp_message_id());
        dto.setConversationId(message.getLzhshtp_conversation_id());
        dto.setSenderId(message.getLzhshtp_sender_id());
        dto.setContent(message.getLzhshtp_content());
        dto.setSentAt(message.getLzhshtp_sent_at());
        dto.setIsRead(message.getLzhshtp_is_read());
        dto.setIsSystemMessage(message.getLzhshtp_is_system_message());
        
        // 判断消息是否由当前用户发送
        dto.setIsMine(message.getLzhshtp_sender_id().equals(currentUserId));
        
        // 获取发送者信息
        User sender = userMapper.selectById(message.getLzhshtp_sender_id());
        if (sender != null) {
            dto.setSenderUsername(sender.getUsername());
            dto.setSenderAvatar(sender.getAvatarUrl());
        }
        
        return dto;
    }
} 