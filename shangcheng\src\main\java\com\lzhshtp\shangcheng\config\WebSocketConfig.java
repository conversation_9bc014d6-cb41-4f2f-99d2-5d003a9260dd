package com.lzhshtp.shangcheng.config;

import com.lzhshtp.shangcheng.websocket.ChatWebSocketHandler;
import com.lzhshtp.shangcheng.websocket.SimpleWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 * 配置聊天功能的WebSocket连接
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private ChatWebSocketHandler chatWebSocketHandler;

    @Autowired
    private SimpleWebSocketHandler simpleWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册简单测试WebSocket处理器
        registry.addHandler(simpleWebSocketHandler, "/ws/test")
                .setAllowedOriginPatterns("*");

        // 注册聊天WebSocket处理器
        registry.addHandler(chatWebSocketHandler, "/ws/chat")
                .setAllowedOriginPatterns("*"); // 允许所有来源（开发环境）
    }
}
