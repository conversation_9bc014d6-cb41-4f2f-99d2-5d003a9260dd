package com.lzhshtp.shangcheng.ai.agent;

import com.lzhshtp.shangcheng.ai.agent.product.ProductAssistant;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.SearchService;
import com.lzhshtp.shangcheng.service.CategoryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;

/**
 * ProductAssistant测试类
 * 用于验证Agent是否能正确调用真实数据
 */
@SpringBootTest
@ActiveProfiles("test")
public class ProductAssistantTest {

    @Autowired(required = false)
    private ProductAssistant productAssistant;

    @Autowired
    private ProductService productService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private CategoryService categoryService;

    @Test
    public void testProductSearch() {
        System.out.println("=== 测试商品搜索功能 ===");
        
        // 测试搜索参数
        ProductQueryParams params = new ProductQueryParams();
        params.setKeyword("手机");
        params.setPage(1);
        params.setPageSize(5);
        params.setStatus("available");

        try {
            PageResult<ProductDTO> result = searchService.searchProducts(params);
            System.out.println("搜索结果总数: " + result.getTotal());
            System.out.println("当前页: " + result.getPage());
            System.out.println("总页数: " + result.getTotalPages());
            
            if (!result.getRecords().isEmpty()) {
                System.out.println("找到的商品:");
                for (ProductDTO product : result.getRecords()) {
                    System.out.println("- ID: " + product.getId() + 
                                     ", 标题: " + product.getTitle() + 
                                     ", 价格: ¥" + product.getPrice());
                }
            } else {
                System.out.println("没有找到商品数据");
            }
        } catch (Exception e) {
            System.err.println("搜索失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testProductDetail() {
        System.out.println("=== 测试商品详情功能 ===");
        
        try {
            ProductDTO product = productService.getProductById(1L);
            if (product != null) {
                System.out.println("商品详情:");
                System.out.println("- ID: " + product.getId());
                System.out.println("- 标题: " + product.getTitle());
                System.out.println("- 价格: ¥" + product.getPrice());
                System.out.println("- 描述: " + product.getDescription());
                System.out.println("- 分类: " + product.getCategoryName());
                System.out.println("- 卖家: " + product.getSellerName());
            } else {
                System.out.println("商品ID 1 不存在");
            }
        } catch (Exception e) {
            System.err.println("获取商品详情失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCategoryList() {
        System.out.println("=== 测试分类列表功能 ===");
        
        try {
            var categories = categoryService.getAllCategoriesTree();
            System.out.println("分类总数: " + categories.size());
            
            for (var category : categories) {
                System.out.println("- " + category.getName() + " (ID: " + category.getId() + ")");
                if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                    for (var child : category.getChildren()) {
                        System.out.println("  - " + child.getName() + " (ID: " + child.getId() + ")");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("获取分类列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testAgentDirectCall() {
        System.out.println("=== 测试Agent直接调用 ===");
        
        if (productAssistant == null) {
            System.out.println("ProductAssistant未注入，可能是AI配置问题");
            return;
        }

        try {
            // 测试搜索功能
            String searchResult = productAssistant.searchProducts(
                "手机", null, null, null, null, null, 1, 5);
            System.out.println("搜索结果:");
            System.out.println(searchResult);
            
            System.out.println("\n" + "=".repeat(50) + "\n");
            
            // 测试分类功能
            String categoryResult = productAssistant.getCategoryList();
            System.out.println("分类结果:");
            System.out.println(categoryResult);
            
        } catch (Exception e) {
            System.err.println("Agent调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testDatabaseConnection() {
        System.out.println("=== 测试数据库连接和数据 ===");
        
        try {
            // 测试获取所有商品
            ProductQueryParams params = new ProductQueryParams();
            params.setPage(1);
            params.setPageSize(10);
            
            PageResult<ProductDTO> allProducts = productService.getProductsByCondition(params);
            System.out.println("数据库中商品总数: " + allProducts.getTotal());
            
            if (allProducts.getTotal() == 0) {
                System.out.println("⚠️ 数据库中没有商品数据！");
                System.out.println("建议:");
                System.out.println("1. 检查数据库连接");
                System.out.println("2. 运行数据初始化脚本");
                System.out.println("3. 手动添加一些测试商品");
            } else {
                System.out.println("✅ 数据库连接正常，有商品数据");
                System.out.println("前5个商品:");
                for (ProductDTO product : allProducts.getRecords()) {
                    System.out.println("- " + product.getTitle() + " (¥" + product.getPrice() + ")");
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
