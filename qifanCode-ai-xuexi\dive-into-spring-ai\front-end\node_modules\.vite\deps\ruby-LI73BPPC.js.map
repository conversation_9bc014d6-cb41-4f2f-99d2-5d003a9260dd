{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/ruby.js"], "sourcesContent": ["function wordObj(words) {\n  var o = {};\n  for (var i = 0, e = words.length; i < e; ++i) o[words[i]] = true;\n  return o;\n}\n\nvar keywordList = [\n  \"alias\", \"and\", \"BEGIN\", \"begin\", \"break\", \"case\", \"class\", \"def\", \"defined?\", \"do\", \"else\",\n  \"elsif\", \"END\", \"end\", \"ensure\", \"false\", \"for\", \"if\", \"in\", \"module\", \"next\", \"not\", \"or\",\n  \"redo\", \"rescue\", \"retry\", \"return\", \"self\", \"super\", \"then\", \"true\", \"undef\", \"unless\",\n  \"until\", \"when\", \"while\", \"yield\", \"nil\", \"raise\", \"throw\", \"catch\", \"fail\", \"loop\", \"callcc\",\n  \"caller\", \"lambda\", \"proc\", \"public\", \"protected\", \"private\", \"require\", \"load\",\n  \"require_relative\", \"extend\", \"autoload\", \"__END__\", \"__FILE__\", \"__LINE__\", \"__dir__\"\n], keywords = wordObj(keywordList);\n\nvar indentWords = wordObj([\"def\", \"class\", \"case\", \"for\", \"while\", \"until\", \"module\",\n                           \"catch\", \"loop\", \"proc\", \"begin\"]);\nvar dedentWords = wordObj([\"end\", \"until\"]);\nvar opening = {\"[\": \"]\", \"{\": \"}\", \"(\": \")\"};\nvar closing = {\"]\": \"[\", \"}\": \"{\", \")\": \"(\"};\n\nvar curPunc;\n\nfunction chain(newtok, stream, state) {\n  state.tokenize.push(newtok);\n  return newtok(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  if (stream.sol() && stream.match(\"=begin\") && stream.eol()) {\n    state.tokenize.push(readBlockComment);\n    return \"comment\";\n  }\n  if (stream.eatSpace()) return null;\n  var ch = stream.next(), m;\n  if (ch == \"`\" || ch == \"'\" || ch == '\"') {\n    return chain(readQuoted(ch, \"string\", ch == '\"' || ch == \"`\"), stream, state);\n  } else if (ch == \"/\") {\n    if (regexpAhead(stream))\n      return chain(readQuoted(ch, \"string.special\", true), stream, state);\n    else\n      return \"operator\";\n  } else if (ch == \"%\") {\n    var style = \"string\", embed = true;\n    if (stream.eat(\"s\")) style = \"atom\";\n    else if (stream.eat(/[WQ]/)) style = \"string\";\n    else if (stream.eat(/[r]/)) style = \"string.special\";\n    else if (stream.eat(/[wxq]/)) { style = \"string\"; embed = false; }\n    var delim = stream.eat(/[^\\w\\s=]/);\n    if (!delim) return \"operator\";\n    if (opening.propertyIsEnumerable(delim)) delim = opening[delim];\n    return chain(readQuoted(delim, style, embed, true), stream, state);\n  } else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"<\" && (m = stream.match(/^<([-~])[\\`\\\"\\']?([a-zA-Z_?]\\w*)[\\`\\\"\\']?(?:;|$)/))) {\n    return chain(readHereDoc(m[2], m[1]), stream, state);\n  } else if (ch == \"0\") {\n    if (stream.eat(\"x\")) stream.eatWhile(/[\\da-fA-F]/);\n    else if (stream.eat(\"b\")) stream.eatWhile(/[01]/);\n    else stream.eatWhile(/[0-7]/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/^[\\d_]*(?:\\.[\\d_]+)?(?:[eE][+\\-]?[\\d_]+)?/);\n    return \"number\";\n  } else if (ch == \"?\") {\n    while (stream.match(/^\\\\[CM]-/)) {}\n    if (stream.eat(\"\\\\\")) stream.eatWhile(/\\w/);\n    else stream.next();\n    return \"string\";\n  } else if (ch == \":\") {\n    if (stream.eat(\"'\")) return chain(readQuoted(\"'\", \"atom\", false), stream, state);\n    if (stream.eat('\"')) return chain(readQuoted('\"', \"atom\", true), stream, state);\n\n    // :> :>> :< :<< are valid symbols\n    if (stream.eat(/[\\<\\>]/)) {\n      stream.eat(/[\\<\\>]/);\n      return \"atom\";\n    }\n\n    // :+ :- :/ :* :| :& :! are valid symbols\n    if (stream.eat(/[\\+\\-\\*\\/\\&\\|\\:\\!]/)) {\n      return \"atom\";\n    }\n\n    // Symbols can't start by a digit\n    if (stream.eat(/[a-zA-Z$@_\\xa1-\\uffff]/)) {\n      stream.eatWhile(/[\\w$\\xa1-\\uffff]/);\n      // Only one ? ! = is allowed and only as the last character\n      stream.eat(/[\\?\\!\\=]/);\n      return \"atom\";\n    }\n    return \"operator\";\n  } else if (ch == \"@\" && stream.match(/^@?[a-zA-Z_\\xa1-\\uffff]/)) {\n    stream.eat(\"@\");\n    stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n    return \"propertyName\";\n  } else if (ch == \"$\") {\n    if (stream.eat(/[a-zA-Z_]/)) {\n      stream.eatWhile(/[\\w]/);\n    } else if (stream.eat(/\\d/)) {\n      stream.eat(/\\d/);\n    } else {\n      stream.next(); // Must be a special global like $: or $!\n    }\n    return \"variableName.special\";\n  } else if (/[a-zA-Z_\\xa1-\\uffff]/.test(ch)) {\n    stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n    stream.eat(/[\\?\\!]/);\n    if (stream.eat(\":\")) return \"atom\";\n    return \"variable\";\n  } else if (ch == \"|\" && (state.varList || state.lastTok == \"{\" || state.lastTok == \"do\")) {\n    curPunc = \"|\";\n    return null;\n  } else if (/[\\(\\)\\[\\]{}\\\\;]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  } else if (ch == \"-\" && stream.eat(\">\")) {\n    return \"operator\";\n  } else if (/[=+\\-\\/*:\\.^%<>~|]/.test(ch)) {\n    var more = stream.eatWhile(/[=+\\-\\/*:\\.^%<>~|]/);\n    if (ch == \".\" && !more) curPunc = \".\";\n    return \"operator\";\n  } else {\n    return null;\n  }\n}\n\nfunction regexpAhead(stream) {\n  var start = stream.pos, depth = 0, next, found = false, escaped = false\n  while ((next = stream.next()) != null) {\n    if (!escaped) {\n      if (\"[{(\".indexOf(next) > -1) {\n        depth++\n      } else if (\"]})\".indexOf(next) > -1) {\n        depth--\n        if (depth < 0) break\n      } else if (next == \"/\" && depth == 0) {\n        found = true\n        break\n      }\n      escaped = next == \"\\\\\"\n    } else {\n      escaped = false\n    }\n  }\n  stream.backUp(stream.pos - start)\n  return found\n}\n\nfunction tokenBaseUntilBrace(depth) {\n  if (!depth) depth = 1;\n  return function(stream, state) {\n    if (stream.peek() == \"}\") {\n      if (depth == 1) {\n        state.tokenize.pop();\n        return state.tokenize[state.tokenize.length-1](stream, state);\n      } else {\n        state.tokenize[state.tokenize.length - 1] = tokenBaseUntilBrace(depth - 1);\n      }\n    } else if (stream.peek() == \"{\") {\n      state.tokenize[state.tokenize.length - 1] = tokenBaseUntilBrace(depth + 1);\n    }\n    return tokenBase(stream, state);\n  };\n}\nfunction tokenBaseOnce() {\n  var alreadyCalled = false;\n  return function(stream, state) {\n    if (alreadyCalled) {\n      state.tokenize.pop();\n      return state.tokenize[state.tokenize.length-1](stream, state);\n    }\n    alreadyCalled = true;\n    return tokenBase(stream, state);\n  };\n}\nfunction readQuoted(quote, style, embed, unescaped) {\n  return function(stream, state) {\n    var escaped = false, ch;\n\n    if (state.context.type === 'read-quoted-paused') {\n      state.context = state.context.prev;\n      stream.eat(\"}\");\n    }\n\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && (unescaped || !escaped)) {\n        state.tokenize.pop();\n        break;\n      }\n      if (embed && ch == \"#\" && !escaped) {\n        if (stream.eat(\"{\")) {\n          if (quote == \"}\") {\n            state.context = {prev: state.context, type: 'read-quoted-paused'};\n          }\n          state.tokenize.push(tokenBaseUntilBrace());\n          break;\n        } else if (/[@\\$]/.test(stream.peek())) {\n          state.tokenize.push(tokenBaseOnce());\n          break;\n        }\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return style;\n  };\n}\nfunction readHereDoc(phrase, mayIndent) {\n  return function(stream, state) {\n    if (mayIndent) stream.eatSpace()\n    if (stream.match(phrase)) state.tokenize.pop();\n    else stream.skipToEnd();\n    return \"string\";\n  };\n}\nfunction readBlockComment(stream, state) {\n  if (stream.sol() && stream.match(\"=end\") && stream.eol())\n    state.tokenize.pop();\n  stream.skipToEnd();\n  return \"comment\";\n}\n\nexport const ruby = {\n  name: \"ruby\",\n  startState: function(indentUnit) {\n    return {tokenize: [tokenBase],\n            indented: 0,\n            context: {type: \"top\", indented: -indentUnit},\n            continuedLine: false,\n            lastTok: null,\n            varList: false};\n  },\n\n  token: function(stream, state) {\n    curPunc = null;\n    if (stream.sol()) state.indented = stream.indentation();\n    var style = state.tokenize[state.tokenize.length-1](stream, state), kwtype;\n    var thisTok = curPunc;\n    if (style == \"variable\") {\n      var word = stream.current();\n      style = state.lastTok == \".\" ? \"property\"\n        : keywords.propertyIsEnumerable(stream.current()) ? \"keyword\"\n        : /^[A-Z]/.test(word) ? \"tag\"\n        : (state.lastTok == \"def\" || state.lastTok == \"class\" || state.varList) ? \"def\"\n        : \"variable\";\n      if (style == \"keyword\") {\n        thisTok = word;\n        if (indentWords.propertyIsEnumerable(word)) kwtype = \"indent\";\n        else if (dedentWords.propertyIsEnumerable(word)) kwtype = \"dedent\";\n        else if ((word == \"if\" || word == \"unless\") && stream.column() == stream.indentation())\n          kwtype = \"indent\";\n        else if (word == \"do\" && state.context.indented < state.indented)\n          kwtype = \"indent\";\n      }\n    }\n    if (curPunc || (style && style != \"comment\")) state.lastTok = thisTok;\n    if (curPunc == \"|\") state.varList = !state.varList;\n\n    if (kwtype == \"indent\" || /[\\(\\[\\{]/.test(curPunc))\n      state.context = {prev: state.context, type: curPunc || style, indented: state.indented};\n    else if ((kwtype == \"dedent\" || /[\\)\\]\\}]/.test(curPunc)) && state.context.prev)\n      state.context = state.context.prev;\n\n    if (stream.eol())\n      state.continuedLine = (curPunc == \"\\\\\" || style == \"operator\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize[state.tokenize.length-1] != tokenBase) return null;\n    var firstChar = textAfter && textAfter.charAt(0);\n    var ct = state.context;\n    var closed = ct.type == closing[firstChar] ||\n        ct.type == \"keyword\" && /^(?:end|until|else|elsif|when|rescue)\\b/.test(textAfter);\n    return ct.indented + (closed ? 0 : cx.unit) +\n      (state.continuedLine ? cx.unit : 0);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(?:end|rescue|elsif|else|\\})$/,\n    commentTokens: {line: \"#\"},\n    autocomplete: keywordList\n  }\n};\n\n"], "mappings": ";;;AAAA,SAAS,QAAQ,OAAO;AACtB,MAAI,IAAI,CAAC;AACT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE;AAAG,MAAE,MAAM,CAAC,CAAC,IAAI;AAC5D,SAAO;AACT;AAEA,IAAI,cAAc;AAAA,EAChB;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAY;AAAA,EAAM;AAAA,EACrF;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAO;AAAA,EACtF;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC/E;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACrF;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EACzE;AAAA,EAAoB;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAC/E;AAPA,IAOG,WAAW,QAAQ,WAAW;AAEjC,IAAI,cAAc,QAAQ;AAAA,EAAC;AAAA,EAAO;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EACjD;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAO,CAAC;AAC5D,IAAI,cAAc,QAAQ,CAAC,OAAO,OAAO,CAAC;AAC1C,IAAI,UAAU,EAAC,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG;AAC3C,IAAI,UAAU,EAAC,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG;AAE3C,IAAI;AAEJ,SAAS,MAAM,QAAQ,QAAQ,OAAO;AACpC,QAAM,SAAS,KAAK,MAAM;AAC1B,SAAO,OAAO,QAAQ,KAAK;AAC7B;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,IAAI,KAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC1D,UAAM,SAAS,KAAK,gBAAgB;AACpC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS;AAAG,WAAO;AAC9B,MAAI,KAAK,OAAO,KAAK,GAAG;AACxB,MAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,WAAO,MAAM,WAAW,IAAI,UAAU,MAAM,OAAO,MAAM,GAAG,GAAG,QAAQ,KAAK;AAAA,EAC9E,WAAW,MAAM,KAAK;AACpB,QAAI,YAAY,MAAM;AACpB,aAAO,MAAM,WAAW,IAAI,kBAAkB,IAAI,GAAG,QAAQ,KAAK;AAAA;AAElE,aAAO;AAAA,EACX,WAAW,MAAM,KAAK;AACpB,QAAI,QAAQ,UAAU,QAAQ;AAC9B,QAAI,OAAO,IAAI,GAAG;AAAG,cAAQ;AAAA,aACpB,OAAO,IAAI,MAAM;AAAG,cAAQ;AAAA,aAC5B,OAAO,IAAI,KAAK;AAAG,cAAQ;AAAA,aAC3B,OAAO,IAAI,OAAO,GAAG;AAAE,cAAQ;AAAU,cAAQ;AAAA,IAAO;AACjE,QAAI,QAAQ,OAAO,IAAI,UAAU;AACjC,QAAI,CAAC;AAAO,aAAO;AACnB,QAAI,QAAQ,qBAAqB,KAAK;AAAG,cAAQ,QAAQ,KAAK;AAC9D,WAAO,MAAM,WAAW,OAAO,OAAO,OAAO,IAAI,GAAG,QAAQ,KAAK;AAAA,EACnE,WAAW,MAAM,KAAK;AACpB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,IAAI,OAAO,MAAM,kDAAkD,IAAI;AAC9F,WAAO,MAAM,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACrD,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,IAAI,GAAG;AAAG,aAAO,SAAS,YAAY;AAAA,aACxC,OAAO,IAAI,GAAG;AAAG,aAAO,SAAS,MAAM;AAAA;AAC3C,aAAO,SAAS,OAAO;AAC5B,WAAO;AAAA,EACT,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,WAAO,MAAM,2CAA2C;AACxD,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO,OAAO,MAAM,UAAU,GAAG;AAAA,IAAC;AAClC,QAAI,OAAO,IAAI,IAAI;AAAG,aAAO,SAAS,IAAI;AAAA;AACrC,aAAO,KAAK;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,IAAI,GAAG;AAAG,aAAO,MAAM,WAAW,KAAK,QAAQ,KAAK,GAAG,QAAQ,KAAK;AAC/E,QAAI,OAAO,IAAI,GAAG;AAAG,aAAO,MAAM,WAAW,KAAK,QAAQ,IAAI,GAAG,QAAQ,KAAK;AAG9E,QAAI,OAAO,IAAI,QAAQ,GAAG;AACxB,aAAO,IAAI,QAAQ;AACnB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,IAAI,oBAAoB,GAAG;AACpC,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,IAAI,wBAAwB,GAAG;AACxC,aAAO,SAAS,kBAAkB;AAElC,aAAO,IAAI,UAAU;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,OAAO,MAAM,yBAAyB,GAAG;AAC/D,WAAO,IAAI,GAAG;AACd,WAAO,SAAS,iBAAiB;AACjC,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,IAAI,WAAW,GAAG;AAC3B,aAAO,SAAS,MAAM;AAAA,IACxB,WAAW,OAAO,IAAI,IAAI,GAAG;AAC3B,aAAO,IAAI,IAAI;AAAA,IACjB,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT,WAAW,uBAAuB,KAAK,EAAE,GAAG;AAC1C,WAAO,SAAS,iBAAiB;AACjC,WAAO,IAAI,QAAQ;AACnB,QAAI,OAAO,IAAI,GAAG;AAAG,aAAO;AAC5B,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,MAAM,WAAW,MAAM,WAAW,OAAO,MAAM,WAAW,OAAO;AACxF,cAAU;AACV,WAAO;AAAA,EACT,WAAW,kBAAkB,KAAK,EAAE,GAAG;AACrC,cAAU;AACV,WAAO;AAAA,EACT,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,WAAO;AAAA,EACT,WAAW,qBAAqB,KAAK,EAAE,GAAG;AACxC,QAAI,OAAO,OAAO,SAAS,oBAAoB;AAC/C,QAAI,MAAM,OAAO,CAAC;AAAM,gBAAU;AAClC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,MAAI,QAAQ,OAAO,KAAK,QAAQ,GAAG,MAAM,QAAQ,OAAO,UAAU;AAClE,UAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,QAAI,CAAC,SAAS;AACZ,UAAI,MAAM,QAAQ,IAAI,IAAI,IAAI;AAC5B;AAAA,MACF,WAAW,MAAM,QAAQ,IAAI,IAAI,IAAI;AACnC;AACA,YAAI,QAAQ;AAAG;AAAA,MACjB,WAAW,QAAQ,OAAO,SAAS,GAAG;AACpC,gBAAQ;AACR;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB,OAAO;AACL,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO,OAAO,OAAO,MAAM,KAAK;AAChC,SAAO;AACT;AAEA,SAAS,oBAAoB,OAAO;AAClC,MAAI,CAAC;AAAO,YAAQ;AACpB,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB,UAAI,SAAS,GAAG;AACd,cAAM,SAAS,IAAI;AACnB,eAAO,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC9D,OAAO;AACL,cAAM,SAAS,MAAM,SAAS,SAAS,CAAC,IAAI,oBAAoB,QAAQ,CAAC;AAAA,MAC3E;AAAA,IACF,WAAW,OAAO,KAAK,KAAK,KAAK;AAC/B,YAAM,SAAS,MAAM,SAAS,SAAS,CAAC,IAAI,oBAAoB,QAAQ,CAAC;AAAA,IAC3E;AACA,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC;AACF;AACA,SAAS,gBAAgB;AACvB,MAAI,gBAAgB;AACpB,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,eAAe;AACjB,YAAM,SAAS,IAAI;AACnB,aAAO,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK;AAAA,IAC9D;AACA,oBAAgB;AAChB,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC;AACF;AACA,SAAS,WAAW,OAAO,OAAO,OAAO,WAAW;AAClD,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AAErB,QAAI,MAAM,QAAQ,SAAS,sBAAsB;AAC/C,YAAM,UAAU,MAAM,QAAQ;AAC9B,aAAO,IAAI,GAAG;AAAA,IAChB;AAEA,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,UAAU,aAAa,CAAC,UAAU;AAC1C,cAAM,SAAS,IAAI;AACnB;AAAA,MACF;AACA,UAAI,SAAS,MAAM,OAAO,CAAC,SAAS;AAClC,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAI,SAAS,KAAK;AAChB,kBAAM,UAAU,EAAC,MAAM,MAAM,SAAS,MAAM,qBAAoB;AAAA,UAClE;AACA,gBAAM,SAAS,KAAK,oBAAoB,CAAC;AACzC;AAAA,QACF,WAAW,QAAQ,KAAK,OAAO,KAAK,CAAC,GAAG;AACtC,gBAAM,SAAS,KAAK,cAAc,CAAC;AACnC;AAAA,QACF;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,QAAQ,WAAW;AACtC,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI;AAAW,aAAO,SAAS;AAC/B,QAAI,OAAO,MAAM,MAAM;AAAG,YAAM,SAAS,IAAI;AAAA;AACxC,aAAO,UAAU;AACtB,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,OAAO,IAAI,KAAK,OAAO,MAAM,MAAM,KAAK,OAAO,IAAI;AACrD,UAAM,SAAS,IAAI;AACrB,SAAO,UAAU;AACjB,SAAO;AACT;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MAAC,UAAU,CAAC,SAAS;AAAA,MACpB,UAAU;AAAA,MACV,SAAS,EAAC,MAAM,OAAO,UAAU,CAAC,WAAU;AAAA,MAC5C,eAAe;AAAA,MACf,SAAS;AAAA,MACT,SAAS;AAAA,IAAK;AAAA,EACxB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,cAAU;AACV,QAAI,OAAO,IAAI;AAAG,YAAM,WAAW,OAAO,YAAY;AACtD,QAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,EAAE,QAAQ,KAAK,GAAG;AACpE,QAAI,UAAU;AACd,QAAI,SAAS,YAAY;AACvB,UAAI,OAAO,OAAO,QAAQ;AAC1B,cAAQ,MAAM,WAAW,MAAM,aAC3B,SAAS,qBAAqB,OAAO,QAAQ,CAAC,IAAI,YAClD,SAAS,KAAK,IAAI,IAAI,QACrB,MAAM,WAAW,SAAS,MAAM,WAAW,WAAW,MAAM,UAAW,QACxE;AACJ,UAAI,SAAS,WAAW;AACtB,kBAAU;AACV,YAAI,YAAY,qBAAqB,IAAI;AAAG,mBAAS;AAAA,iBAC5C,YAAY,qBAAqB,IAAI;AAAG,mBAAS;AAAA,kBAChD,QAAQ,QAAQ,QAAQ,aAAa,OAAO,OAAO,KAAK,OAAO,YAAY;AACnF,mBAAS;AAAA,iBACF,QAAQ,QAAQ,MAAM,QAAQ,WAAW,MAAM;AACtD,mBAAS;AAAA,MACb;AAAA,IACF;AACA,QAAI,WAAY,SAAS,SAAS;AAAY,YAAM,UAAU;AAC9D,QAAI,WAAW;AAAK,YAAM,UAAU,CAAC,MAAM;AAE3C,QAAI,UAAU,YAAY,WAAW,KAAK,OAAO;AAC/C,YAAM,UAAU,EAAC,MAAM,MAAM,SAAS,MAAM,WAAW,OAAO,UAAU,MAAM,SAAQ;AAAA,cAC9E,UAAU,YAAY,WAAW,KAAK,OAAO,MAAM,MAAM,QAAQ;AACzE,YAAM,UAAU,MAAM,QAAQ;AAEhC,QAAI,OAAO,IAAI;AACb,YAAM,gBAAiB,WAAW,QAAQ,SAAS;AACrD,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,SAAS,MAAM,SAAS,SAAO,CAAC,KAAK;AAAW,aAAO;AACjE,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC;AAC/C,QAAI,KAAK,MAAM;AACf,QAAI,SAAS,GAAG,QAAQ,QAAQ,SAAS,KACrC,GAAG,QAAQ,aAAa,0CAA0C,KAAK,SAAS;AACpF,WAAO,GAAG,YAAY,SAAS,IAAI,GAAG,SACnC,MAAM,gBAAgB,GAAG,OAAO;AAAA,EACrC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,IAAG;AAAA,IACzB,cAAc;AAAA,EAChB;AACF;", "names": []}