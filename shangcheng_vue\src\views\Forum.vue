<template>
  <div class="forum-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="logo">二手交易</div>
      <div class="search-box">
        <input type="text" placeholder="输入关键词" v-model="searchKeyword" />
        <button class="search-btn" @click="searchPosts">搜索</button>
      </div>
      <div class="user-actions">
        <div class="customer-service">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15C21 16.6569 19.6569 18 18 18H7.993C7.69741 18 7.41117 18.1215 7.20017 18.3475L4 22V5C4 3.34315 5.34315 2 7 2H17C18.6569 2 20 3.34315 20 5V15H21ZM12 8C12.5523 8 13 8.44772 13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8ZM12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11ZM12 14C12.5523 14 13 14.4477 13 15C13 15.5522 12.5523 16 12 16C11.4477 16 11 15.5522 11 15C11 14.4477 11.4477 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>客服</span>
        </div>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <div
        class="category-item"
        :class="{ active: currentCategory === 'all' }"
        @click="changeCategory('all')"
      >
        全部话题
      </div>
      <div
        v-for="category in forumCategories"
        :key="category.forumCategoryId"
        class="category-item"
        :class="{ active: currentCategory === category.forumCategoryId }"
        @click="changeCategory(category.forumCategoryId)"
      >
        {{ category.categoryName }}
      </div>
      <div
        class="category-item"
        :class="{ active: currentCategory === 'mine' }"
        @click="changeCategory('mine')"
      >
        我的
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 中间区域：热门话题和帖子列表 -->
      <div class="center-content">
        <div class="section-header">
          <span style="color: #e53935;" >{{ currentCategoryName }}</span>
          <div class="filter-options">
            <span
              class="filter-option"
              :class="{ active: currentFilter === 'all' }"
              @click="changeFilter('all')"
            >全部</span>
            <span
              class="filter-option"
              :class="{ active: currentFilter === 'latest' }"
              @click="changeFilter('latest')"
            >最新</span>
            <span
              class="filter-option"
              :class="{ active: currentFilter === 'hot' }"
              @click="changeFilter('hot')"
            >热门</span>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div v-if="loading && currentPage === 1" class="loading-indicator">
          <div class="spinner"></div>
          <span>加载中...</span>
        </div>

        <!-- 帖子列表 -->
        <div v-else class="post-list">
          <div class="post-item" 
               v-for="(post, index) in displayPosts" 
               :key="index" 
               :class="{ 'pinned-post': showPinnedIndicator && post.isPinned }">
            <div class="post-content" @click="viewPostDetail(post.id)">
              <div class="post-title">
                <span v-if="showPinnedIndicator && post.isPinned" class="pinned-tag">置顶</span>
                {{ post.title }}
              </div>
              <div class="post-info">
                <span class="author">发布者: {{ post.author }}</span>
                <span class="time">{{ post.time }}</span>
              </div>
            </div>
            <div class="post-stats">
              <div class="replies">{{ post.replies }} 回复</div>
              <div class="views">{{ post.views }} 浏览</div>
              <div class="category-tag">{{ post.category }}</div>
              <!-- 编辑和删除按钮，仅在"我的"分类中显示，且当前用户是帖子作者 -->
              <div v-if="currentCategory === 'mine' && isCurrentUserAuthor(post)" class="post-actions">
                <button class="edit-btn" @click.stop="editPost(post)">编辑</button>
                <button class="delete-btn" @click.stop="confirmDeletePost(post)">删除</button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="displayPosts.length === 0" class="empty-state">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15C21 16.6569 19.6569 18 18 18H7.993C7.69741 18 7.41117 18.1215 7.20017 18.3475L4 22V5C4 3.34315 5.34315 2 7 2H17C18.6569 2 20 3.34315 20 5V15H21Z"></path></svg>
            <p>暂无帖子数据</p>
          </div>

          <!-- 加载更多 -->
          <div v-if="displayPosts.length > 0" class="load-more-container">
            <div v-if="loading && currentPage > 1" class="loading-indicator-inline">
              <div class="spinner-small"></div>
              <span>加载中...</span>
            </div>
            <button v-else-if="hasMorePosts" @click="loadMorePosts" class="load-more-btn">
              加载更多帖子
            </button>
            <div v-else class="no-more-posts">
              已经到底啦，没有更多帖子了~
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域：发帖按钮 -->
      <div class="right-sidebar">
        <div class="post-btn" @click="createNewPost">
          <i class="plus-icon">+</i>
          <span>发帖子</span>
        </div>
      </div>
    </div>
    <!-- 发帖模态框 -->
    <CreatePostModal
      :show="isCreatePostModalVisible"
      :categories="forumCategories"
      @close="isCreatePostModalVisible = false"
      @post-created="handlePostCreated"
    />
    <!-- 编辑帖子模态框 -->
    <div class="modal-overlay" v-if="isEditModalVisible" @click.self="cancelEdit">
      <div class="modal-container">
        <div class="modal-header">
          <h3>编辑帖子</h3>
          <button class="close-btn" @click="cancelEdit">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitEditPost">
            <div class="form-group">
              <label for="edit-post-title">帖子标题</label>
              <input
                type="text"
                id="edit-post-title"
                v-model.trim="editingPost.title"
                placeholder="请输入标题"
                required
              />
              <small class="error-text" v-if="editErrors.title">{{ editErrors.title }}</small>
            </div>
            <div class="form-group">
              <label for="edit-post-category">选择分类</label>
              <select id="edit-post-category" v-model="editingPost.forumCategoryId" required>
                <option disabled value="">请选择一个分类</option>
                <option
                  v-for="category in forumCategories"
                  :key="category.forumCategoryId"
                  :value="category.forumCategoryId"
                >
                  {{ category.categoryName }}
                </option>
              </select>
              <small class="error-text" v-if="editErrors.category">{{ editErrors.category }}</small>
            </div>
            <div class="form-group">
              <label for="edit-post-content">帖子内容</label>
              <textarea
                id="edit-post-content"
                rows="8"
                v-model.trim="editingPost.content"
                placeholder="请输入帖子内容..."
                required
              ></textarea>
              <small class="error-text" v-if="editErrors.content">{{ editErrors.content }}</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="cancelEdit">取消</button>
          <button class="submit-btn" @click="submitEditPost" :disabled="isSubmittingEdit">
            <span v-if="isSubmittingEdit" class="spinner-small"></span>
            {{ isSubmittingEdit ? '保存中...' : '保存修改' }}
          </button>
        </div>
      </div>
    </div>
    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />
  </div>
</template>

<script>
import { getAllForumCategories } from '@/api/forumCategory'
import { getPostList, updatePost, deletePost } from '@/api/forum'
import { searchForumPosts, getForumPostSearchSuggestions } from '@/api/search'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useUserStore } from '@/stores/user'
import CreatePostModal from '@/components/CreatePostModal.vue'; // 导入模态框组件
import FloatingActionButtons from '@/components/FloatingActionButtons.vue'

export default {
  name: 'Forum',
  components: {
    CreatePostModal, // 注册组件
    FloatingActionButtons,
  },
  data() {
    return {
      isLoggedIn: false,
      username: '',
      userAvatar: 'https://via.placeholder.com/30',
      searchKeyword: '',
      currentCategory: 'all',
      currentFilter: 'all', // 修改默认筛选为"全部"
      currentPage: 1,
      pageSize: 5,
      totalPosts: 0,
      totalPages: 1,
      userStore: null,
      loading: false,
      hasMorePosts: true,
      isCreatePostModalVisible: false, // 控制模态框显示

      // 编辑帖子相关
      isEditModalVisible: false,
      isSubmittingEdit: false,
      editingPost: {
        id: null,
        title: '',
        content: '',
        forumCategoryId: ''
      },
      editErrors: {
        title: '',
        content: '',
        category: ''
      },

      // 从后端获取的论坛分类
      forumCategories: [],

      // 帖子列表
      posts: []
    }
  },
  computed: {
    // 当前分类名称
    currentCategoryName() {
      if (this.currentCategory === 'all') return '热门话题';
      if (this.currentCategory === 'mine') return '我的帖子';

      const category = this.forumCategories.find(c => c.forumCategoryId === this.currentCategory);
      return category ? category.categoryName : '热门话题';
    },

    // 是否显示置顶标识
    showPinnedIndicator() {
      // 只在"全部话题"分类的"全部"排序中显示置顶标识
      return this.currentCategory === 'all' && this.currentFilter === 'all';
    },

    // 显示的帖子
    displayPosts() {
      // 在前端确保置顶帖子显示在前面（作为后端排序的备份机制）
      if (this.currentCategory === 'all' && this.currentFilter === 'all') {
        const pinnedPosts = this.posts.filter(post => post.isPinned);
        const normalPosts = this.posts.filter(post => !post.isPinned);
        return [...pinnedPosts, ...normalPosts];
      }
      return this.posts;
    }
  },
  methods: {
    // 获取论坛分类数据
    async fetchForumCategories() {
      try {
        const response = await getAllForumCategories()
        if (response.success) {
          this.forumCategories = response.data
        } else {
          console.error('获取论坛分类失败:', response.message)
        }
      } catch (error) {
        console.error('获取论坛分类异常:', error)
      }
    },

    // 切换分类
    changeCategory(categoryId) {
      // 转换categoryId为相应的类型
      if (categoryId !== 'all' && categoryId !== 'mine') {
        // 从后端获取的分类ID是整数类型
        categoryId = parseInt(categoryId);
      }

      this.currentCategory = categoryId;
      this.currentPage = 1;
      this.posts = []; // 清空现有帖子
      this.hasMorePosts = true; // 重置加载更多状态
      this.loadPosts();
    },

    // 切换筛选条件
    changeFilter(filter) {
      this.currentFilter = filter;
      this.currentPage = 1;
      this.posts = []; // 清空现有帖子
      this.hasMorePosts = true; // 重置加载更多状态
      this.loadPosts();
    },

    // 搜索帖子
    searchPosts() {
      this.currentPage = 1;
      this.posts = []; // 清空现有帖子
      this.hasMorePosts = true; // 重置加载更多状态
      this.loadPosts();
    },

    // 加载帖子数据
    async loadPosts() {
      if (this.loading) return;

      this.loading = true;
      try {
        // 构建查询参数
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchKeyword || undefined
        };

        // 根据分类过滤
        if (this.currentCategory !== 'all' && this.currentCategory !== 'mine') {
          params.categoryId = this.currentCategory;
        }

        // 如果是"我的"分类，则查询当前用户的帖子
        if (this.currentCategory === 'mine') {
          // 获取当前用户ID
          if (this.userStore && this.userStore.userInfo) {
            params.authorId = this.userStore.userInfo.userId;
          }
        }

        // 根据筛选条件设置排序
        if (this.currentFilter === 'all') {
          // "全部"选项使用随机排序
          params.orderBy = 'random';
          // 如果是全部话题分类，启用置顶功能
          if (this.currentCategory === 'all') {
            params.enablePinned = true;
          }
        } else if (this.currentFilter === 'latest') {
          params.orderBy = 'posted_at';
          params.orderDirection = 'desc';
        } else if (this.currentFilter === 'hot') {
          params.orderBy = 'views_count';
          params.orderDirection = 'desc';
        } else if (this.currentFilter === 'recommend') {
          params.onlyPinned = true;
        }

        // 优先使用ElasticSearch搜索，失败时降级到数据库搜索
        let response
        if (this.searchKeyword && this.searchKeyword.trim()) {
          try {
            console.log('使用ElasticSearch搜索论坛帖子...')
            response = await searchForumPosts(params)
          } catch (error) {
            console.warn('ElasticSearch论坛搜索失败，降级到数据库搜索:', error)
            response = await getPostList(params)
          }
        } else {
          // 没有搜索关键词时使用原有API
          response = await getPostList(params)
        }
        if (response.success) {
          const data = response.data;
          const newPosts = data.records.map(post => ({
            id: post.postId,
            title: post.title,
            author: post.authorName,
            authorId: post.authorId, // 添加作者ID
            time: this.formatTime(post.postedAt),
            replies: post.commentCount || 0,
            views: post.viewsCount || 0,
            category: post.categoryName,
            isPinned: post.isPinned === 1 // 添加置顶标识
          }));

          // 追加新帖子到列表
          this.posts = [...this.posts, ...newPosts];

          this.totalPosts = data.total;
          this.totalPages = data.pages;

          // 判断是否还有更多数据
          this.hasMorePosts = newPosts.length === this.pageSize && this.currentPage < this.totalPages;
        } else {
          console.error('获取帖子列表失败:', response.message);
        }
      } catch (error) {
        console.error('获取帖子列表异常:', error);
      } finally {
        this.loading = false;
      }
    },

    // 加载更多帖子
    loadMorePosts() {
      if (this.loading || !this.hasMorePosts) return;

      this.currentPage++;
      this.loadPosts();
    },

    // 格式化时间为"多久之前"
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      try {
        const date = new Date(dateTimeStr);
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
      } catch (error) {
        console.error('时间格式化错误:', error);
        return dateTimeStr;
      }
    },

    // 查看帖子详情
    viewPostDetail(postId) {
      this.$router.push(`/forum/post/${postId}`);
    },

    // 创建新帖子 - 现在只打开模态框
    createNewPost() {
      if (!this.isLoggedIn) {
        // 可以选择在这里提示或直接在模态框内部处理
        // 为了更好的体验，可以在未登录时提示并跳转
        // this.$message.warning('请先登录后再发帖');
        this.$router.push('/login');
        return;
      }
      this.isCreatePostModalVisible = true;
    },
    
    // 发帖成功后的处理
    handlePostCreated() {
      this.isCreatePostModalVisible = false;
      // alert('发帖成功!'); // 可以在这里添加一个更友好的提示，例如使用一个轻量级的通知组件
      
      // 刷新帖子列表
      this.currentPage = 1;
      this.posts = [];
      this.hasMorePosts = true;
      this.loadPosts();
    },

    // 编辑帖子
    editPost(post) {
      // 获取帖子详情
      this.fetchPostDetail(post.id);
    },

    // 获取帖子详情用于编辑
    async fetchPostDetail(postId) {
      try {
        const { getPostDetail } = await import('@/api/forum');
        const response = await getPostDetail(postId);
        if (response.success) {
          const post = response.data;
          this.editingPost = {
            id: post.postId,
            title: post.title,
            content: post.content,
            forumCategoryId: post.forumCategoryId
          };
          this.isEditModalVisible = true;
        } else {
          alert('获取帖子详情失败: ' + response.message);
        }
      } catch (error) {
        console.error('获取帖子详情异常:', error);
        alert('获取帖子详情失败，请稍后再试。');
      }
    },

    // 取消编辑
    cancelEdit() {
      this.isEditModalVisible = false;
      this.editingPost = {
        id: null,
        title: '',
        content: '',
        forumCategoryId: ''
      };
      this.editErrors = {
        title: '',
        content: '',
        category: ''
      };
    },

    // 验证编辑表单
    validateEditForm() {
      this.editErrors = { title: '', content: '', category: '' };
      let isValid = true;
      
      if (!this.editingPost.title) {
        this.editErrors.title = '标题不能为空';
        isValid = false;
      } else if (this.editingPost.title.length > 100) {
        this.editErrors.title = '标题不能超过100个字符';
        isValid = false;
      }
      
      if (!this.editingPost.content) {
        this.editErrors.content = '内容不能为空';
        isValid = false;
      }
      
      if (!this.editingPost.forumCategoryId) {
        this.editErrors.category = '请选择一个分类';
        isValid = false;
      }
      
      return isValid;
    },

    // 提交编辑的帖子
    async submitEditPost() {
      if (!this.validateEditForm()) {
        return;
      }

      this.isSubmittingEdit = true;
      try {
        const response = await updatePost(this.editingPost.id, {
          title: this.editingPost.title,
          content: this.editingPost.content,
          forumCategoryId: this.editingPost.forumCategoryId
        });

        if (response.success) {
          alert('帖子更新成功！');
          this.cancelEdit();
          // 刷新帖子列表
          this.currentPage = 1;
          this.posts = [];
          this.hasMorePosts = true;
          this.loadPosts();
        } else {
          alert('帖子更新失败: ' + response.message);
        }
      } catch (error) {
        console.error('更新帖子异常:', error);
        if (error.response && error.response.data && error.response.data.message) {
          alert(`帖子更新失败: ${error.response.data.message}`);
        } else {
          alert('帖子更新失败，请稍后再试。');
        }
      } finally {
        this.isSubmittingEdit = false;
      }
    },

    // 确认删除帖子
    async confirmDeletePost(post) {
      if (confirm(`确定要删除帖子 "${post.title}" 吗？`)) {
        try {
          const response = await deletePost(post.id);
          if (response.success) {
            alert('帖子删除成功！');
            this.currentPage = 1; // 刷新列表
            this.posts = [];
            this.hasMorePosts = true;
            this.loadPosts();
          } else {
            alert('帖子删除失败：' + response.message);
          }
        } catch (error) {
          console.error('删除帖子异常:', error);
          alert('删除帖子失败，请稍后再试。');
        }
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      this.userStore = useUserStore();
      this.isLoggedIn = this.userStore.isLoggedIn;

      if (this.isLoggedIn) {
        // 获取用户信息
        this.fetchUserInfo();
      }
    },

    // 获取用户信息
    fetchUserInfo() {
      if (this.userStore && this.userStore.userInfo) {
        this.username = this.userStore.userInfo.username || '用户名';
        this.userAvatar = this.userStore.userInfo.avatarUrl || 'https://via.placeholder.com/30';
      }
    },

    // 跳转到登录页
    goToLogin() {
      this.$router.push('/login');
    },

    // 检测滚动到底部
    checkScrollBottom() {
      // 检查是否滚动到接近底部
      const scrollPosition = window.scrollY + window.innerHeight;
      const pageHeight = document.documentElement.scrollHeight;

      // 当滚动到距离底部200px时加载更多
      if (pageHeight - scrollPosition < 200 && this.hasMorePosts && !this.loading) {
        this.loadMorePosts();
      }
    },

    // 检查当前用户是否是帖子作者
    isCurrentUserAuthor(post) {
      return this.userStore && 
             this.userStore.userInfo && 
             post.authorId && 
             this.userStore.userInfo.userId === post.authorId;
    },
  },
  created() {
    this.checkLoginStatus();
    this.fetchForumCategories(); // 获取论坛分类
    this.loadPosts();
  },
  mounted() {
    // 添加滚动监听
    window.addEventListener('scroll', this.checkScrollBottom);
  },
  beforeUnmount() {
    // 移除滚动监听
    window.removeEventListener('scroll', this.checkScrollBottom);
  }
}
</script>

<style scoped>
.forum-container {
  width: 100%;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

/* 顶部导航栏样式 */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e53935;
  color: white;
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  height: 60px;
}

.logo {
  font-size: 30px;
  font-weight: bold;
  min-width: 100px;
}

.search-box {
  display: flex;
  flex: 1;
  max-width: 550px;
  margin: 0 20px;
}

.search-box input {
  flex: 1;
  padding: 8px 15px;
  border: none;
  border-radius: 20px 0 0 20px;
  outline: none;
  background-color: rgba(255, 255, 255, 0.8);
}

.search-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 0 20px 20px 0;
  background-color: #d32f2f;
  color: white;
  cursor: pointer;
}

.user-actions {
  display: flex;
  align-items: center;
}

.customer-service {
  display: flex;
  align-items: center;
  margin-right: 15px;
  cursor: pointer;
}

.service-icon {
  margin-right: 5px;
}

.login-btn {
  padding: 6px 15px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  border: 1px solid white;
  color: white;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 8px;
}

.username {
  font-size: 14px;
}

/* 分类导航样式 */
.category-nav {
  display: flex;
  background-color: #c62828;
  padding: 0 10% 10px;
  border-bottom: 1px solid #b71c1c;
  box-sizing: border-box;
  width: 100%;
  height: 57px;
}

.category-item {
  padding: 15px 20px;
  color: white;
  cursor: pointer;
  position: relative;
  font-size: 18px;
  font-weight: 500;
}

.category-item.active {
  font-weight: bold;
  border-bottom: 2px solid white;
}

/* 主体内容区样式 */
.main-content {
  display: flex;
  padding: 30px;
  background-color: #f5f5f5;
  max-width: 1400px;
  margin: 0 auto;
  font-size: 22px;
}

/* 中间内容区样式 */
.center-content {
  flex: 1;
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filter-options {
  display: flex;
}

.filter-option {
  margin-left: 15px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  font-weight: normal;
}

.filter-option.active {
  color: #e53935;
}

.post-list {
  margin-top: 15px;
}

.post-item {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.post-content {
  flex: 1;
}

.post-title {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.post-title:hover {
  color: #e53935;
}

.post-info {
  font-size: 12px;
  color: #999;
}

.author, .time {
  margin-right: 10px;
}

.post-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  color: #999;
}

.replies, .views {
  margin-bottom: 5px;
}

.category-tag {
  padding: 2px 8px;
  background-color: #f5f5f5;
  border-radius: 10px;
  font-size: 12px;
}

/* 右侧边栏样式 */
.right-sidebar {
  width: 220px;
  margin-left: 30px;
}

.post-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 12px;
  padding: 10px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  cursor: pointer;
  color: #e53935;
  font-weight: bold;
}

.plus-icon {
  margin-right: 5px;
  font-size: 18px;
}

/* 加载指示器样式 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: #e53935;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载更多区域 */
.load-more-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  width: 100%;
}

.load-more-btn {
  background-color: #e53935;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.loading-indicator-inline {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: #e53935;
  animation: spin 1s ease-in-out infinite;
}

.no-more-posts {
  color: #666;
  font-size: 14px;
  padding: 10px;
  text-align: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #666;
  text-align: center;
}

.empty-state svg {
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
}

/* 置顶帖子样式 */
.pinned-post {
  background-color: #f9f9f9; /* 浅灰色背景 */
  border-left: 4px solid #e53935; /* 红色边框 */
  padding-left: 10px; /* 缩进 */
  margin-bottom: 10px; /* 间距 */
}

.pinned-tag {
  background-color: #e53935;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-right: 5px;
}

/* 帖子操作按钮样式 */
.post-actions {
  display: flex;
  gap: 10px; /* 按钮之间的间距 */
  margin-top: 10px;
}

.edit-btn, .delete-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.edit-btn {
  background-color: #4CAF50; /* 绿色 */
  color: white;
}

.edit-btn:hover {
  background-color: #388E3C;
}

.delete-btn {
  background-color: #F44336; /* 红色 */
  color: white;
}

.delete-btn:hover {
  background-color: #D32F2F;
}

/* 编辑模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.close-btn {
  border: none;
  background: transparent;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical;
}

.error-text {
  color: #e53935;
  font-size: 0.875rem;
  margin-top: 5px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 25px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 10px;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.submit-btn {
  background-color: #e53935;
  color: white;
  display: flex;
  align-items: center;
}

.submit-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-overlay {
  animation: fadeIn 0.3s ease;
}
</style>
