package com.lzhshtp.shangcheng.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lzhshtp.shangcheng.dto.ForumCommentDTO;
import com.lzhshtp.shangcheng.dto.ForumCommentRequest;
import com.lzhshtp.shangcheng.model.ForumComment;

import java.util.List;

/**
 * 论坛评论服务接口
 */
public interface ForumCommentService extends IService<ForumComment> {

    /**
     * 创建评论
     * 
     * @param request 评论请求
     * @param userId 用户ID
     * @return 评论ID
     */
    Long createComment(ForumCommentRequest request, Long userId);
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteComment(Long commentId, Long userId);
    
    /**
     * 获取帖子的所有评论（树形结构）
     * 
     * @param postId 帖子ID
     * @return 评论列表
     */
    List<ForumCommentDTO> getCommentsByPostId(Long postId);
    
    /**
     * 获取评论的所有回复
     * 
     * @param commentId 评论ID
     * @return 回复列表
     */
    List<ForumCommentDTO> getRepliesByCommentId(Long commentId);
} 