package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.CategoryDTO;
import com.lzhshtp.shangcheng.service.CategoryService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/categories")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 获取所有分类（树形结构）
     */
    @GetMapping("/tree")
    public ApiResponse<List<CategoryDTO>> getAllCategoriesTree() {
        List<CategoryDTO> categories = categoryService.getAllCategoriesTree();
        return ApiResponse.success(categories);
    }

    /**
     * 获取所有分类（平铺结构）
     */
    @GetMapping
    public ApiResponse<List<CategoryDTO>> getAllCategories() {
        List<CategoryDTO> categories = categoryService.getAllCategories();
        return ApiResponse.success(categories);
    }

    /**
     * 根据ID获取分类
     */
    @GetMapping("/{id}")
    public ApiResponse<CategoryDTO> getCategoryById(@PathVariable("id") Integer id) {
        CategoryDTO category = categoryService.getCategoryById(id);
        if (category == null) {
            return ApiResponse.fail("分类不存在", 404);
        }
        return ApiResponse.success(category);
    }

    /**
     * 创建分类（需要管理员权限）
     */
    @PostMapping
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Integer> createCategory(@Valid @RequestBody CategoryDTO categoryDTO) {
        Integer categoryId = categoryService.createCategory(categoryDTO);
        return ApiResponse.success("分类创建成功", categoryId);
    }

    /**
     * 更新分类（需要管理员权限）
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Void> updateCategory(
            @PathVariable("id") Integer id,
            @Valid @RequestBody CategoryDTO categoryDTO) {

        boolean success = categoryService.updateCategory(id, categoryDTO);
        if (!success) {
            return ApiResponse.fail("分类不存在", 404);
        }
        return ApiResponse.success("分类更新成功", null);
    }

    /**
     * 删除分类（需要管理员权限）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Void> deleteCategory(@PathVariable("id") Integer id) {
        boolean success = categoryService.deleteCategory(id);
        if (!success) {
            return ApiResponse.fail("分类不存在或无法删除（存在子分类或关联商品）", 400);
        }
        return ApiResponse.success("分类删除成功", null);
    }
}
