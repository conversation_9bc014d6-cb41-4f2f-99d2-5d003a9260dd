package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 论坛帖子实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_forum_posts")
public class ForumPost {
    
    /**
     * 帖子ID
     */
    @TableId(value = "lzhshtp_post_id", type = IdType.AUTO)
    private Long postId;
    
    /**
     * 帖子标题
     */
    @TableField("lzhshtp_title")
    private String title;
    
    /**
     * 帖子内容
     */
    @TableField("lzhshtp_content")
    private String content;
    
    /**
     * 作者ID
     */
    @TableField("lzhshtp_author_id")
    private Long authorId;
    
    /**
     * 发布时间
     */
    @TableField("lzhshtp_posted_at")
    private LocalDateTime postedAt;
    
    /**
     * 所属分类ID
     */
    @TableField("lzhshtp_forum_category_id")
    private Integer forumCategoryId;
    
    /**
     * 浏览量
     */
    @TableField("lzhshtp_views_count")
    private Integer viewsCount;
    
    /**
     * 是否置顶
     */
    @TableField("lzhshtp_is_pinned")
    private Integer isPinned;
    
    /**
     * 帖子状态 0-已发布 1-下架
     */
    @TableField("lzhshtp_status")
    private Integer status;
    
    /**
     * 帖子状态常量
     */
    public static class PostStatus {
        public static final int PUBLISHED = 0;
        public static final int OFFLINE = 1;
    }
    
    /**
     * 置顶状态常量
     */
    public static class PinnedStatus {
        public static final int NOT_PINNED = 0;
        public static final int PINNED = 1;
    }
} 