package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 热门商品DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotProductDTO {
    private Long productId;         // 商品ID
    private String title;           // 商品标题
    private String categoryName;    // 分类名称
    private BigDecimal price;       // 价格
    private String sellerName;      // 卖家名称
    private Long value;             // 统计值（浏览量/收藏量/订单量）
    private String valueType;       // 统计类型
}
