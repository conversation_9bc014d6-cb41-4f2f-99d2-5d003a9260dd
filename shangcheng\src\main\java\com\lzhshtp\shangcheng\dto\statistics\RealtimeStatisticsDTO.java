package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 实时统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealtimeStatisticsDTO {
    
    // 今日实时数据
    private Long todayNewUsers;           // 今日新增用户
    private Long todayNewProducts;        // 今日新增商品
    private Long todayOrders;             // 今日订单数
    private BigDecimal todayRevenue;      // 今日交易额
    private Long todayForumPosts;         // 今日新增帖子
    private Long todayForumComments;      // 今日新增评论
    
    // 在线统计
    private Long onlineUsers;             // 当前在线用户数
    private Long activeUsers;             // 活跃用户数（最近1小时）
    
    // 最近1小时统计
    private Long lastHourUsers;           // 最近1小时新增用户
    private Long lastHourProducts;        // 最近1小时新增商品
    private Long lastHourOrders;          // 最近1小时订单数
    private BigDecimal lastHourRevenue;   // 最近1小时交易额
    
    // 系统状态
    private String systemStatus;          // 系统状态：normal, warning, error
    private Double systemLoad;            // 系统负载
    private Long totalMemory;             // 总内存
    private Long usedMemory;              // 已用内存
    private Double memoryUsage;           // 内存使用率
    
    // 数据库状态
    private Long dbConnections;           // 数据库连接数
    private Double dbResponseTime;        // 数据库响应时间（毫秒）
    
    // 最新动态
    private String latestUserRegistration;  // 最新用户注册
    private String latestProductPosted;     // 最新商品发布
    private String latestOrderCreated;      // 最新订单创建
    private String latestForumPost;         // 最新论坛帖子
    
    // 异常统计
    private Long todayErrors;             // 今日错误数
    private Long todayWarnings;           // 今日警告数
    private String lastErrorTime;         // 最后错误时间
    
    // 更新时间
    private String updateTime;            // 数据更新时间
}
