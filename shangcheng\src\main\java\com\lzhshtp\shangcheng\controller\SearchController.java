package com.lzhshtp.shangcheng.controller;


import com.lzhshtp.shangcheng.document.ForumPostDocument;
import com.lzhshtp.shangcheng.document.ProductDocument;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;
import com.lzhshtp.shangcheng.service.SearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 搜索控制器
 * 基于ElasticSearch的全文搜索功能
 */
@Slf4j
@RestController
@RequestMapping("/api/search")
public class SearchController {

    @Autowired
    private SearchService searchService;

    // ==================== 商品搜索 ====================

    /**
     * 搜索商品
     */
    @GetMapping("/products")
    public ApiResponse<PageResult<ProductDTO>> searchProducts(ProductQueryParams params) {
        try {
            PageResult<ProductDTO> result = searchService.searchProducts(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("商品搜索失败: {}", e.getMessage());
            return ApiResponse.fail("搜索失败，请稍后重试");
        }
    }

    /**
     * 获取商品搜索建议
     */
    @GetMapping("/products/suggestions")
    public ApiResponse<List<String>> getProductSearchSuggestions(@RequestParam String q) {
        try {
            List<String> suggestions = searchService.getProductSearchSuggestions(q);
            return ApiResponse.success(suggestions);
        } catch (Exception e) {
            log.error("获取商品搜索建议失败: {}", e.getMessage());
            return ApiResponse.fail("获取搜索建议失败");
        }
    }

    /**
     * 获取相似商品推荐
     */
    @GetMapping("/products/{productId}/similar")
    public ApiResponse<List<ProductDTO>> getSimilarProducts(@PathVariable Long productId) {
        try {
            List<ProductDTO> similarProducts = searchService.getSimilarProducts(productId);
            return ApiResponse.success(similarProducts);
        } catch (Exception e) {
            log.error("获取相似商品失败: {}", e.getMessage());
            return ApiResponse.fail("获取相似商品失败");
        }
    }

    // ==================== 论坛帖子搜索 ====================

    /**
     * 搜索论坛帖子
     */
    @GetMapping("/forum/posts")
    public ApiResponse<PageResult<ForumPostDTO>> searchForumPosts(ForumPostQueryRequest queryRequest) {
        try {
            PageResult<ForumPostDTO> result = searchService.searchForumPosts(queryRequest);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("论坛帖子搜索失败: {}", e.getMessage());
            return ApiResponse.fail("搜索失败，请稍后重试");
        }
    }

    /**
     * 获取帖子搜索建议
     */
    @GetMapping("/forum/posts/suggestions")
    public ApiResponse<List<String>> getForumPostSearchSuggestions(@RequestParam String q) {
        try {
            List<String> suggestions = searchService.getForumPostSearchSuggestions(q);
            return ApiResponse.success(suggestions);
        } catch (Exception e) {
            log.error("获取帖子搜索建议失败: {}", e.getMessage());
            return ApiResponse.fail("获取搜索建议失败");
        }
    }

    /**
     * 获取相似帖子推荐
     */
    @GetMapping("/forum/posts/{postId}/similar")
    public ApiResponse<List<ForumPostDTO>> getSimilarForumPosts(@PathVariable Long postId) {
        try {
            List<ForumPostDTO> similarPosts = searchService.getSimilarForumPosts(postId);
            return ApiResponse.success(similarPosts);
        } catch (Exception e) {
            log.error("获取相似帖子失败: {}", e.getMessage());
            return ApiResponse.fail("获取相似帖子失败");
        }
    }

    // ==================== 搜索分析 ====================

    /**
     * 获取热门搜索词
     */
    @GetMapping("/hot-keywords")
    public ApiResponse<List<String>> getHotSearchKeywords(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<String> hotKeywords = searchService.getHotSearchKeywords(limit);
            return ApiResponse.success(hotKeywords);
        } catch (Exception e) {
            log.error("获取热门搜索词失败: {}", e.getMessage());
            return ApiResponse.fail("获取热门搜索词失败");
        }
    }

    // ==================== 管理员功能 ====================

    /**
     * 重建搜索索引（管理员专用）
     */
    @PostMapping("/admin/rebuild-indexes")
    public ApiResponse<String> rebuildIndexes() {
        try {
            searchService.rebuildAllIndexes();
            return ApiResponse.success("索引重建成功");
        } catch (Exception e) {
            log.error("重建索引失败: {}", e.getMessage());
            return ApiResponse.fail("重建索引失败");
        }
    }

    /**
     * 同步商品到搜索索引（管理员专用）
     */
    @PostMapping("/admin/sync-product")
    public ApiResponse<String> syncProductToES(@RequestParam(required = false) Long productId) {
        try {
            searchService.syncProductToES(productId);
            String message = productId != null ?
                "商品 " + productId + " 同步成功" : "所有商品同步成功";
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("同步商品到ES失败: {}", e.getMessage());
            return ApiResponse.fail("同步失败");
        }
    }

    /**
     * 同步帖子到搜索索引（管理员专用）
     */
    @PostMapping("/admin/sync-forum-post")
    public ApiResponse<String> syncForumPostToES(@RequestParam(required = false) Long postId) {
        try {
            searchService.syncForumPostToES(postId);
            String message = postId != null ?
                "帖子 " + postId + " 同步成功" : "所有帖子同步成功";
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("同步帖子到ES失败: {}", e.getMessage());
            return ApiResponse.fail("同步失败");
        }
    }
}
