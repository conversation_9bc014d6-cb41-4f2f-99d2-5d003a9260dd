package com.lzhshtp.shangcheng.ai.agent;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Description;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Agent抽象基类
 * 提供Agent的通用功能
 */
@Slf4j
public abstract class AbstractAgent {

    /**
     * 获取指定的function bean名称
     *
     * @param clazz Function类列表
     * @return Function Call名称列表
     */
    public String[] getFunctions(Class<?>... clazz) {
        List<Class<?>> classList = Arrays.asList(clazz);
        return classList.stream()
                .map(this::convertToFunctionName)
                .toArray(String[]::new);
    }

    /**
     * 获取Agent自身的Function方法
     *
     * @param agentClass Agent类
     * @return Function名称列表
     */
    public String[] getAgentFunctions(Class<?> agentClass) {
        return Arrays.stream(agentClass.getDeclaredMethods())
                .filter(method -> method.isAnnotationPresent(Description.class))
                .map(method -> convertToFunctionName(method.getName()))
                .toArray(String[]::new);
    }

    /**
     * 将类名转换为Function名称（首字母小写）
     *
     * @param clazz 类
     * @return Function名称
     */
    private String convertToFunctionName(Class<?> clazz) {
        String className = clazz.getSimpleName();
        return StringUtils.uncapitalize(className);
    }

    /**
     * 将方法名转换为Function名称
     *
     * @param methodName 方法名
     * @return Function名称
     */
    private String convertToFunctionName(String methodName) {
        return StringUtils.uncapitalize(methodName);
    }
}
