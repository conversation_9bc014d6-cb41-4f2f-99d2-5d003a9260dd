{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/scheme.js"], "sourcesContent": ["var BUILTIN = \"builtin\", COMMENT = \"comment\", STRING = \"string\",\n    SYMBOL = \"symbol\", ATOM = \"atom\", NUMBER = \"number\", BRACKET = \"bracket\";\nvar INDENT_WORD_SKIP = 2;\n\nfunction makeKeywords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = makeKeywords(\"λ case-lambda call/cc class cond-expand define-class define-values exit-handler field import inherit init-field interface let*-values let-values let/ec mixin opt-lambda override protect provide public rename require require-for-syntax syntax syntax-case syntax-error unit/sig unless when with-syntax and begin call-with-current-continuation call-with-input-file call-with-output-file case cond define define-syntax define-macro defmacro delay do dynamic-wind else for-each if lambda let let* let-syntax letrec letrec-syntax map or syntax-rules abs acos angle append apply asin assoc assq assv atan boolean? caar cadr call-with-input-file call-with-output-file call-with-values car cdddar cddddr cdr ceiling char->integer char-alphabetic? char-ci<=? char-ci<? char-ci=? char-ci>=? char-ci>? char-downcase char-lower-case? char-numeric? char-ready? char-upcase char-upper-case? char-whitespace? char<=? char<? char=? char>=? char>? char? close-input-port close-output-port complex? cons cos current-input-port current-output-port denominator display eof-object? eq? equal? eqv? eval even? exact->inexact exact? exp expt #f floor force gcd imag-part inexact->exact inexact? input-port? integer->char integer? interaction-environment lcm length list list->string list->vector list-ref list-tail list? load log magnitude make-polar make-rectangular make-string make-vector max member memq memv min modulo negative? newline not null-environment null? number->string number? numerator odd? open-input-file open-output-file output-port? pair? peek-char port? positive? procedure? quasiquote quote quotient rational? rationalize read read-char real-part real? remainder reverse round scheme-report-environment set! set-car! set-cdr! sin sqrt string string->list string->number string->symbol string-append string-ci<=? string-ci<? string-ci=? string-ci>=? string-ci>? string-copy string-fill! string-length string-ref string-set! string<=? string<? string=? string>=? string>? string? substring symbol->string symbol? #t tan transcript-off transcript-on truncate values vector vector->list vector-fill! vector-length vector-ref vector-set! with-input-from-file with-output-to-file write write-char zero?\");\nvar indentKeys = makeKeywords(\"define let letrec let* lambda define-macro defmacro let-syntax letrec-syntax let-values let*-values define-syntax syntax-rules define-values when unless\");\n\nfunction stateStack(indent, type, prev) { // represents a state stack object\n  this.indent = indent;\n  this.type = type;\n  this.prev = prev;\n}\n\nfunction pushStack(state, indent, type) {\n  state.indentStack = new stateStack(indent, type, state.indentStack);\n}\n\nfunction popStack(state) {\n  state.indentStack = state.indentStack.prev;\n}\n\nvar binaryMatcher = new RegExp(/^(?:[-+]i|[-+][01]+#*(?:\\/[01]+#*)?i|[-+]?[01]+#*(?:\\/[01]+#*)?@[-+]?[01]+#*(?:\\/[01]+#*)?|[-+]?[01]+#*(?:\\/[01]+#*)?[-+](?:[01]+#*(?:\\/[01]+#*)?)?i|[-+]?[01]+#*(?:\\/[01]+#*)?)(?=[()\\s;\"]|$)/i);\nvar octalMatcher = new RegExp(/^(?:[-+]i|[-+][0-7]+#*(?:\\/[0-7]+#*)?i|[-+]?[0-7]+#*(?:\\/[0-7]+#*)?@[-+]?[0-7]+#*(?:\\/[0-7]+#*)?|[-+]?[0-7]+#*(?:\\/[0-7]+#*)?[-+](?:[0-7]+#*(?:\\/[0-7]+#*)?)?i|[-+]?[0-7]+#*(?:\\/[0-7]+#*)?)(?=[()\\s;\"]|$)/i);\nvar hexMatcher = new RegExp(/^(?:[-+]i|[-+][\\da-f]+#*(?:\\/[\\da-f]+#*)?i|[-+]?[\\da-f]+#*(?:\\/[\\da-f]+#*)?@[-+]?[\\da-f]+#*(?:\\/[\\da-f]+#*)?|[-+]?[\\da-f]+#*(?:\\/[\\da-f]+#*)?[-+](?:[\\da-f]+#*(?:\\/[\\da-f]+#*)?)?i|[-+]?[\\da-f]+#*(?:\\/[\\da-f]+#*)?)(?=[()\\s;\"]|$)/i);\nvar decimalMatcher = new RegExp(/^(?:[-+]i|[-+](?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*)i|[-+]?(?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*)@[-+]?(?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*)|[-+]?(?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*)[-+](?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*)?i|(?:(?:(?:\\d+#+\\.?#*|\\d+\\.\\d*#*|\\.\\d+#*|\\d+)(?:[esfdl][-+]?\\d+)?)|\\d+#*\\/\\d+#*))(?=[()\\s;\"]|$)/i);\n\nfunction isBinaryNumber (stream) {\n  return stream.match(binaryMatcher);\n}\n\nfunction isOctalNumber (stream) {\n  return stream.match(octalMatcher);\n}\n\nfunction isDecimalNumber (stream, backup) {\n  if (backup === true) {\n    stream.backUp(1);\n  }\n  return stream.match(decimalMatcher);\n}\n\nfunction isHexNumber (stream) {\n  return stream.match(hexMatcher);\n}\n\nfunction processEscapedSequence(stream, options) {\n  var next, escaped = false;\n  while ((next = stream.next()) != null) {\n    if (next == options.token && !escaped) {\n      options.state.mode = false;\n      break;\n    }\n    escaped = !escaped && next == \"\\\\\";\n  }\n}\n\nexport const scheme = {\n  name: \"scheme\",\n  startState: function () {\n    return {\n      indentStack: null,\n      indentation: 0,\n      mode: false,\n      sExprComment: false,\n      sExprQuote: false\n    };\n  },\n\n  token: function (stream, state) {\n    if (state.indentStack == null && stream.sol()) {\n      // update indentation, but only if indentStack is empty\n      state.indentation = stream.indentation();\n    }\n\n    // skip spaces\n    if (stream.eatSpace()) {\n      return null;\n    }\n    var returnType = null;\n\n    switch(state.mode){\n    case \"string\": // multi-line string parsing mode\n      processEscapedSequence(stream, {\n        token: \"\\\"\",\n        state: state\n      });\n      returnType = STRING; // continue on in scheme-string mode\n      break;\n    case \"symbol\": // escape symbol\n      processEscapedSequence(stream, {\n        token: \"|\",\n        state: state\n      });\n      returnType = SYMBOL; // continue on in scheme-symbol mode\n      break;\n    case \"comment\": // comment parsing mode\n      var next, maybeEnd = false;\n      while ((next = stream.next()) != null) {\n        if (next == \"#\" && maybeEnd) {\n\n          state.mode = false;\n          break;\n        }\n        maybeEnd = (next == \"|\");\n      }\n      returnType = COMMENT;\n      break;\n    case \"s-expr-comment\": // s-expr commenting mode\n      state.mode = false;\n      if(stream.peek() == \"(\" || stream.peek() == \"[\"){\n        // actually start scheme s-expr commenting mode\n        state.sExprComment = 0;\n      }else{\n        // if not we just comment the entire of the next token\n        stream.eatWhile(/[^\\s\\(\\)\\[\\]]/); // eat symbol atom\n        returnType = COMMENT;\n        break;\n      }\n    default: // default parsing mode\n      var ch = stream.next();\n\n      if (ch == \"\\\"\") {\n        state.mode = \"string\";\n        returnType = STRING;\n\n      } else if (ch == \"'\") {\n        if (stream.peek() == \"(\" || stream.peek() == \"[\"){\n          if (typeof state.sExprQuote != \"number\") {\n            state.sExprQuote = 0;\n          } // else already in a quoted expression\n          returnType = ATOM;\n        } else {\n          stream.eatWhile(/[\\w_\\-!$%&*+\\.\\/:<=>?@\\^~]/);\n          returnType = ATOM;\n        }\n      } else if (ch == '|') {\n        state.mode = \"symbol\";\n        returnType = SYMBOL;\n      } else if (ch == '#') {\n        if (stream.eat(\"|\")) {                    // Multi-line comment\n          state.mode = \"comment\"; // toggle to comment mode\n          returnType = COMMENT;\n        } else if (stream.eat(/[tf]/i)) {            // #t/#f (atom)\n          returnType = ATOM;\n        } else if (stream.eat(';')) {                // S-Expr comment\n          state.mode = \"s-expr-comment\";\n          returnType = COMMENT;\n        } else {\n          var numTest = null, hasExactness = false, hasRadix = true;\n          if (stream.eat(/[ei]/i)) {\n            hasExactness = true;\n          } else {\n            stream.backUp(1);       // must be radix specifier\n          }\n          if (stream.match(/^#b/i)) {\n            numTest = isBinaryNumber;\n          } else if (stream.match(/^#o/i)) {\n            numTest = isOctalNumber;\n          } else if (stream.match(/^#x/i)) {\n            numTest = isHexNumber;\n          } else if (stream.match(/^#d/i)) {\n            numTest = isDecimalNumber;\n          } else if (stream.match(/^[-+0-9.]/, false)) {\n            hasRadix = false;\n            numTest = isDecimalNumber;\n            // re-consume the initial # if all matches failed\n          } else if (!hasExactness) {\n            stream.eat('#');\n          }\n          if (numTest != null) {\n            if (hasRadix && !hasExactness) {\n              // consume optional exactness after radix\n              stream.match(/^#[ei]/i);\n            }\n            if (numTest(stream))\n              returnType = NUMBER;\n          }\n        }\n      } else if (/^[-+0-9.]/.test(ch) && isDecimalNumber(stream, true)) { // match non-prefixed number, must be decimal\n        returnType = NUMBER;\n      } else if (ch == \";\") { // comment\n        stream.skipToEnd(); // rest of the line is a comment\n        returnType = COMMENT;\n      } else if (ch == \"(\" || ch == \"[\") {\n        var keyWord = ''; var indentTemp = stream.column(), letter;\n        /**\n           Either\n           (indent-word ..\n           (non-indent-word ..\n           (;something else, bracket, etc.\n        */\n\n        while ((letter = stream.eat(/[^\\s\\(\\[\\;\\)\\]]/)) != null) {\n          keyWord += letter;\n        }\n\n        if (keyWord.length > 0 && indentKeys.propertyIsEnumerable(keyWord)) { // indent-word\n\n          pushStack(state, indentTemp + INDENT_WORD_SKIP, ch);\n        } else { // non-indent word\n          // we continue eating the spaces\n          stream.eatSpace();\n          if (stream.eol() || stream.peek() == \";\") {\n            // nothing significant after\n            // we restart indentation 1 space after\n            pushStack(state, indentTemp + 1, ch);\n          } else {\n            pushStack(state, indentTemp + stream.current().length, ch); // else we match\n          }\n        }\n        stream.backUp(stream.current().length - 1); // undo all the eating\n\n        if(typeof state.sExprComment == \"number\") state.sExprComment++;\n        if(typeof state.sExprQuote == \"number\") state.sExprQuote++;\n\n        returnType = BRACKET;\n      } else if (ch == \")\" || ch == \"]\") {\n        returnType = BRACKET;\n        if (state.indentStack != null && state.indentStack.type == (ch == \")\" ? \"(\" : \"[\")) {\n          popStack(state);\n\n          if(typeof state.sExprComment == \"number\"){\n            if(--state.sExprComment == 0){\n              returnType = COMMENT; // final closing bracket\n              state.sExprComment = false; // turn off s-expr commenting mode\n            }\n          }\n          if(typeof state.sExprQuote == \"number\"){\n            if(--state.sExprQuote == 0){\n              returnType = ATOM; // final closing bracket\n              state.sExprQuote = false; // turn off s-expr quote mode\n            }\n          }\n        }\n      } else {\n        stream.eatWhile(/[\\w_\\-!$%&*+\\.\\/:<=>?@\\^~]/);\n\n        if (keywords && keywords.propertyIsEnumerable(stream.current())) {\n          returnType = BUILTIN;\n        } else returnType = \"variable\";\n      }\n    }\n    return (typeof state.sExprComment == \"number\") ? COMMENT : ((typeof state.sExprQuote == \"number\") ? ATOM : returnType);\n  },\n\n  indent: function (state) {\n    if (state.indentStack == null) return state.indentation;\n    return state.indentStack.indent;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \";;\"}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,UAAU;AAAd,IAAyB,UAAU;AAAnC,IAA8C,SAAS;AAAvD,IACI,SAAS;AADb,IACuB,OAAO;AAD9B,IACsC,SAAS;AAD/C,IACyD,UAAU;AACnE,IAAI,mBAAmB;AAEvB,SAAS,aAAa,KAAK;AACzB,MAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAAG,QAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,WAAW,aAAa,opEAAopE;AAChrE,IAAI,aAAa,aAAa,0JAA0J;AAExL,SAAS,WAAW,QAAQ,MAAM,MAAM;AACtC,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,OAAO;AACd;AAEA,SAAS,UAAU,OAAO,QAAQ,MAAM;AACtC,QAAM,cAAc,IAAI,WAAW,QAAQ,MAAM,MAAM,WAAW;AACpE;AAEA,SAAS,SAAS,OAAO;AACvB,QAAM,cAAc,MAAM,YAAY;AACxC;AAEA,IAAI,gBAAgB,IAAI,OAAO,iMAAiM;AAChO,IAAI,eAAe,IAAI,OAAO,6MAA6M;AAC3O,IAAI,aAAa,IAAI,OAAO,qOAAqO;AACjQ,IAAI,iBAAiB,IAAI,OAAO,8gBAA8gB;AAE9iB,SAAS,eAAgB,QAAQ;AAC/B,SAAO,OAAO,MAAM,aAAa;AACnC;AAEA,SAAS,cAAe,QAAQ;AAC9B,SAAO,OAAO,MAAM,YAAY;AAClC;AAEA,SAAS,gBAAiB,QAAQ,QAAQ;AACxC,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO,CAAC;AAAA,EACjB;AACA,SAAO,OAAO,MAAM,cAAc;AACpC;AAEA,SAAS,YAAa,QAAQ;AAC5B,SAAO,OAAO,MAAM,UAAU;AAChC;AAEA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,MAAM,UAAU;AACpB,UAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,QAAI,QAAQ,QAAQ,SAAS,CAAC,SAAS;AACrC,cAAQ,MAAM,OAAO;AACrB;AAAA,IACF;AACA,cAAU,CAAC,WAAW,QAAQ;AAAA,EAChC;AACF;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,aAAa;AAAA,MACb,aAAa;AAAA,MACb,MAAM;AAAA,MACN,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,MAAM,eAAe,QAAQ,OAAO,IAAI,GAAG;AAE7C,YAAM,cAAc,OAAO,YAAY;AAAA,IACzC;AAGA,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,aAAa;AAEjB,YAAO,MAAM,MAAK;AAAA,MAClB,KAAK;AACH,+BAAuB,QAAQ;AAAA,UAC7B,OAAO;AAAA,UACP;AAAA,QACF,CAAC;AACD,qBAAa;AACb;AAAA,MACF,KAAK;AACH,+BAAuB,QAAQ;AAAA,UAC7B,OAAO;AAAA,UACP;AAAA,QACF,CAAC;AACD,qBAAa;AACb;AAAA,MACF,KAAK;AACH,YAAI,MAAM,WAAW;AACrB,gBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,cAAI,QAAQ,OAAO,UAAU;AAE3B,kBAAM,OAAO;AACb;AAAA,UACF;AACA,qBAAY,QAAQ;AAAA,QACtB;AACA,qBAAa;AACb;AAAA,MACF,KAAK;AACH,cAAM,OAAO;AACb,YAAG,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,KAAI;AAE9C,gBAAM,eAAe;AAAA,QACvB,OAAK;AAEH,iBAAO,SAAS,eAAe;AAC/B,uBAAa;AACb;AAAA,QACF;AAAA,MACF;AACE,YAAI,KAAK,OAAO,KAAK;AAErB,YAAI,MAAM,KAAM;AACd,gBAAM,OAAO;AACb,uBAAa;AAAA,QAEf,WAAW,MAAM,KAAK;AACpB,cAAI,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,KAAI;AAC/C,gBAAI,OAAO,MAAM,cAAc,UAAU;AACvC,oBAAM,aAAa;AAAA,YACrB;AACA,yBAAa;AAAA,UACf,OAAO;AACL,mBAAO,SAAS,4BAA4B;AAC5C,yBAAa;AAAA,UACf;AAAA,QACF,WAAW,MAAM,KAAK;AACpB,gBAAM,OAAO;AACb,uBAAa;AAAA,QACf,WAAW,MAAM,KAAK;AACpB,cAAI,OAAO,IAAI,GAAG,GAAG;AACnB,kBAAM,OAAO;AACb,yBAAa;AAAA,UACf,WAAW,OAAO,IAAI,OAAO,GAAG;AAC9B,yBAAa;AAAA,UACf,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,kBAAM,OAAO;AACb,yBAAa;AAAA,UACf,OAAO;AACL,gBAAI,UAAU,MAAM,eAAe,OAAO,WAAW;AACrD,gBAAI,OAAO,IAAI,OAAO,GAAG;AACvB,6BAAe;AAAA,YACjB,OAAO;AACL,qBAAO,OAAO,CAAC;AAAA,YACjB;AACA,gBAAI,OAAO,MAAM,MAAM,GAAG;AACxB,wBAAU;AAAA,YACZ,WAAW,OAAO,MAAM,MAAM,GAAG;AAC/B,wBAAU;AAAA,YACZ,WAAW,OAAO,MAAM,MAAM,GAAG;AAC/B,wBAAU;AAAA,YACZ,WAAW,OAAO,MAAM,MAAM,GAAG;AAC/B,wBAAU;AAAA,YACZ,WAAW,OAAO,MAAM,aAAa,KAAK,GAAG;AAC3C,yBAAW;AACX,wBAAU;AAAA,YAEZ,WAAW,CAAC,cAAc;AACxB,qBAAO,IAAI,GAAG;AAAA,YAChB;AACA,gBAAI,WAAW,MAAM;AACnB,kBAAI,YAAY,CAAC,cAAc;AAE7B,uBAAO,MAAM,SAAS;AAAA,cACxB;AACA,kBAAI,QAAQ,MAAM;AAChB,6BAAa;AAAA,YACjB;AAAA,UACF;AAAA,QACF,WAAW,YAAY,KAAK,EAAE,KAAK,gBAAgB,QAAQ,IAAI,GAAG;AAChE,uBAAa;AAAA,QACf,WAAW,MAAM,KAAK;AACpB,iBAAO,UAAU;AACjB,uBAAa;AAAA,QACf,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,cAAI,UAAU;AAAI,cAAI,aAAa,OAAO,OAAO,GAAG;AAQpD,kBAAQ,SAAS,OAAO,IAAI,iBAAiB,MAAM,MAAM;AACvD,uBAAW;AAAA,UACb;AAEA,cAAI,QAAQ,SAAS,KAAK,WAAW,qBAAqB,OAAO,GAAG;AAElE,sBAAU,OAAO,aAAa,kBAAkB,EAAE;AAAA,UACpD,OAAO;AAEL,mBAAO,SAAS;AAChB,gBAAI,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,KAAK;AAGxC,wBAAU,OAAO,aAAa,GAAG,EAAE;AAAA,YACrC,OAAO;AACL,wBAAU,OAAO,aAAa,OAAO,QAAQ,EAAE,QAAQ,EAAE;AAAA,YAC3D;AAAA,UACF;AACA,iBAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AAEzC,cAAG,OAAO,MAAM,gBAAgB;AAAU,kBAAM;AAChD,cAAG,OAAO,MAAM,cAAc;AAAU,kBAAM;AAE9C,uBAAa;AAAA,QACf,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,uBAAa;AACb,cAAI,MAAM,eAAe,QAAQ,MAAM,YAAY,SAAS,MAAM,MAAM,MAAM,MAAM;AAClF,qBAAS,KAAK;AAEd,gBAAG,OAAO,MAAM,gBAAgB,UAAS;AACvC,kBAAG,EAAE,MAAM,gBAAgB,GAAE;AAC3B,6BAAa;AACb,sBAAM,eAAe;AAAA,cACvB;AAAA,YACF;AACA,gBAAG,OAAO,MAAM,cAAc,UAAS;AACrC,kBAAG,EAAE,MAAM,cAAc,GAAE;AACzB,6BAAa;AACb,sBAAM,aAAa;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,SAAS,4BAA4B;AAE5C,cAAI,YAAY,SAAS,qBAAqB,OAAO,QAAQ,CAAC,GAAG;AAC/D,yBAAa;AAAA,UACf;AAAO,yBAAa;AAAA,QACtB;AAAA,IACF;AACA,WAAQ,OAAO,MAAM,gBAAgB,WAAY,UAAY,OAAO,MAAM,cAAc,WAAY,OAAO;AAAA,EAC7G;AAAA,EAEA,QAAQ,SAAU,OAAO;AACvB,QAAI,MAAM,eAAe;AAAM,aAAO,MAAM;AAC5C,WAAO,MAAM,YAAY;AAAA,EAC3B;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,IAC9C,eAAe,EAAC,MAAM,KAAI;AAAA,EAC5B;AACF;", "names": []}