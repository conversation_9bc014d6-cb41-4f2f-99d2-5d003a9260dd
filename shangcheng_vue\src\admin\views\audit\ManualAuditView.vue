<template>
  <div class="manual-audit-view">
    <div class="page-header">
      <h2>人工审核</h2>
      <p class="page-description">处理需要人工审核的商品</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ stats.pending }}</div>
        <div class="stat-label">待审核</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.in_progress }}</div>
        <div class="stat-label">审核中</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.completed }}</div>
        <div class="stat-label">已完成</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.material_requested || 0 }}</div>
        <div class="stat-label">等待补充材料</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.overdue }}</div>
        <div class="stat-label">超时</div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-form">
        <div class="form-row">
          <div class="form-item">
            <label>任务状态：</label>
            <select v-model="filterForm.status" class="form-select">
              <option value="">全部</option>
              <option value="pending">待认领</option>
              <option value="in_progress">审核中</option>
              <option value="completed">已完成</option>
              <option value="material_requested">等待补充材料</option>
            </select>
          </div>
          <div class="form-item">
            <label>商品ID：</label>
            <input
              v-model="filterForm.productId"
              type="text"
              placeholder="请输入商品ID"
              class="form-input"
            />
          </div>
          <div class="form-item">
            <button @click="handleFilter" class="search-btn">筛选</button>
            <button @click="handleResetFilter" class="reset-btn">重置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <div class="list-header">
        <span class="total-count">共 {{ pagination.total }} 个任务</span>
        <button @click="refreshTasks" class="refresh-btn">刷新</button>
      </div>

      <div class="task-cards">
        <div
          v-for="task in auditTasks"
          :key="task.taskId"
          class="task-card"
          :class="getTaskCardClass(task)"
        >
          <div class="task-header">
            <div class="task-info">
              <span class="task-id">任务 #{{ task.taskId }}</span>
              <span class="priority-badge" :class="getPriorityClass(task.priority)">
                {{ getPriorityText(task.priority) }}
              </span>
              <span class="status-badge" :class="getStatusClass(task.status)">
                {{ getStatusText(task.status) }}
              </span>
            </div>
            <div class="task-time">
              <span class="created-time">创建：{{ formatDateTime(task.createdTime) }}</span>
              <span class="deadline" :class="{ 'overdue': isOverdue(task.deadline) }">
                截止：{{ formatDateTime(task.deadline) }}
              </span>
            </div>
          </div>

          <div class="task-content">
            <div class="product-info">
              <h4>商品信息</h4>
              <div class="product-details">
                <div class="detail-item">
                  <label>商品ID：</label>
                  <span>{{ task.productId }}</span>
                </div>
                <div class="detail-item">
                  <label>商品标题：</label>
                  <span>{{ task.productTitle || '未知商品' }}</span>
                </div>
                <div class="detail-item">
                  <label>卖家ID：</label>
                  <span>{{ task.sellerId }}</span>
                </div>
                <div class="detail-item">
                  <label>商品价格：</label>
                  <span>¥{{ task.productPrice || 0 }}</span>
                </div>
              </div>
            </div>

            <div class="audit-reasons">
              <h4>审核原因</h4>
              <div class="reasons-list">
                <span
                  v-for="reason in parseAuditReasons(task.auditReasons)"
                  :key="reason"
                  class="reason-tag"
                >
                  {{ reason }}
                </span>
              </div>
            </div>
          </div>

          <div class="task-actions">
            <template v-if="task.status === 'pending'">
              <button @click="claimTask(task)" class="action-btn claim-btn">
                认领任务
              </button>
            </template>
            <template v-else-if="task.status === 'in_progress' && task.adminId === currentAdminId">
              <button @click="viewTaskDetail(task)" class="action-btn detail-btn">
                审核处理
              </button>
            </template>
            <template v-else-if="task.status === 'material_requested'">
              <button @click="viewTaskDetail(task)" class="action-btn material-btn">
                查看材料请求
              </button>
            </template>
            <template v-else>
              <button @click="viewTaskDetail(task)" class="action-btn view-btn">
                查看详情
              </button>
            </template>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.current }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </span>
        <button
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 审核处理弹窗 -->
    <div v-if="showAuditModal" class="modal-overlay" @click="closeAuditModal">
      <div class="modal-content audit-modal" @click.stop>
        <div class="modal-header">
          <h3>审核处理 - 任务 #{{ selectedTask?.taskId }}</h3>
          <button @click="closeAuditModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <!-- 商品详细信息 -->
          <div class="section">
            <h4>商品详细信息</h4>
            <div class="product-detail-grid">
              <div class="detail-item">
                <label>商品ID：</label>
                <span>{{ selectedTask?.productId }}</span>
              </div>
              <div class="detail-item">
                <label>商品标题：</label>
                <span>{{ selectedTask?.productTitle }}</span>
              </div>
              <div class="detail-item">
                <label>商品描述：</label>
                <span>{{ selectedTask?.productDescription }}</span>
              </div>
              <div class="detail-item">
                <label>商品价格：</label>
                <span>¥{{ selectedTask?.productPrice }}</span>
              </div>
              <div class="detail-item">
                <label>商品图片：</label>
                <div class="product-images">
                  <img
                    v-for="(image, index) in parseProductImages(selectedTask?.productImages)"
                    :key="index"
                    :src="image"
                    alt="商品图片"
                    class="product-image"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 补充材料 -->
          <div class="section" v-if="materialRequests.length > 0">
            <h4>补充材料</h4>
            <div class="material-requests">
              <div
                v-for="request in materialRequests"
                :key="request.requestId"
                class="material-request-item"
              >
                <div class="request-header">
                  <span class="request-time">{{ formatDateTime(request.requestTime) }}</span>
                  <span class="request-status" :class="getRequestStatusClass(request.status)">
                    {{ getRequestStatusText(request.status) }}
                  </span>
                </div>
                <div class="request-reason">
                  <strong>请求原因：</strong>{{ request.requestReason }}
                </div>
                <div class="required-materials">
                  <strong>需要材料：</strong>
                  <ul>
                    <li v-for="material in parseRequiredMaterials(request.requiredMaterials)" :key="material">
                      {{ material }}
                    </li>
                  </ul>
                </div>

                <!-- 卖家提交的材料 -->
                <div v-if="request.materials && request.materials.length > 0" class="submitted-materials">
                  <strong>已提交材料：</strong>
                  <div class="materials-list">
                    <div
                      v-for="material in request.materials"
                      :key="material.materialId"
                      class="material-item"
                    >
                      <div class="material-header">
                        <span class="material-type">{{ material.materialType }}</span>
                        <span class="submit-time">{{ formatDateTime(material.submitTime) }}</span>
                      </div>
                      <div class="material-description">{{ material.description }}</div>
                      <div class="material-files">
                        <a
                          v-for="(url, index) in parseMaterialUrls(material.materialUrls)"
                          :key="index"
                          :href="url"
                          target="_blank"
                          class="material-file-link"
                        >
                          查看文件{{ index + 1 }}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 无材料提示 -->
                <div v-else class="no-materials">
                  <span class="text-muted">卖家暂未提交材料</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 审核决策 -->
          <div class="section">
            <h4>审核决策</h4>
            <div class="decision-form">
              <div class="form-item">
                <label>审核结果：</label>
                <select v-model="auditDecision.decision" class="form-select">
                  <option value="">请选择</option>
                  <option value="approved">通过</option>
                  <option value="rejected">拒绝</option>
                  <option value="request_materials">要求补充材料</option>
                  <option value="escalate_to_second_review">升级到二度复审</option>
                </select>
              </div>
              <div class="form-item">
                <label>审核意见：</label>
                <textarea
                  v-model="auditDecision.comments"
                  placeholder="请输入审核意见..."
                  class="form-textarea"
                  rows="4"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeAuditModal" class="cancel-btn">取消</button>
          <!-- 只有当前管理员认领的进行中任务才能提交审核结果 -->
          <button
            v-if="selectedTask?.status === 'in_progress' && selectedTask?.adminId === currentAdminId"
            @click="submitAuditDecision"
            class="submit-btn"
            :disabled="!auditDecision.decision"
          >
            提交审核结果
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { manualAuditApi } from '@/admin/api/manualAudit'
import { useAdminStore } from '../../stores/admin'

// Store
const adminStore = useAdminStore()

// 响应式数据
const auditTasks = ref([])
const showAuditModal = ref(false)
const selectedTask = ref(null)

// 当前管理员ID - 从store获取
const currentAdminId = computed(() => {
  const adminId = adminStore.adminInfo?.userId || null
  console.log('当前管理员ID:', adminId, '管理员信息:', adminStore.adminInfo)
  return adminId
})

// 统计数据
const stats = reactive({
  pending: 0,
  in_progress: 0,
  completed: 0,
  overdue: 0
})

// 筛选表单
const filterForm = reactive({
  status: '',
  priority: '',
  productId: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 6,
  total: 0
})

// 审核决策表单
const auditDecision = reactive({
  decision: '',
  comments: ''
})

// 补充材料相关
const materialRequests = ref([])
const supplementaryMaterials = ref([])
const showMaterialsModal = ref(false)

// 页面加载时获取数据
onMounted(async () => {
  // 确保管理员信息已加载
  if (!adminStore.adminInfo) {
    try {
      await adminStore.fetchAdminInfo()
    } catch (error) {
      console.error('获取管理员信息失败:', error)
    }
  }

  fetchAuditTasks()
  fetchStats()
})

// 获取审核任务
const fetchAuditTasks = async () => {
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...(filterForm.status && { status: filterForm.status }),
      ...(filterForm.priority && { priority: filterForm.priority }),
      ...(filterForm.productId && { productId: filterForm.productId })
    }

    const result = await manualAuditApi.getTasks(params)

    if (result.success) {
      auditTasks.value = result.data.records || []
      pagination.total = result.data.total || 0
    } else {
      console.error('获取审核任务失败:', result.message)
      auditTasks.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取审核任务失败:', error)
    auditTasks.value = []
    pagination.total = 0
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const result = await manualAuditApi.getStats()

    if (result.success) {
      Object.assign(stats, result.data)
    } else {
      console.error('获取统计数据失败:', result.message)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 筛选
const handleFilter = () => {
  pagination.current = 1
  fetchAuditTasks()
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    priority: '',
    productId: ''
  })
  pagination.current = 1
  fetchAuditTasks()
}

// 刷新任务
const refreshTasks = () => {
  fetchAuditTasks()
  fetchStats()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= Math.ceil(pagination.total / pagination.pageSize)) {
    pagination.current = page
    fetchAuditTasks()
  }
}

// 认领任务
const claimTask = async (task) => {
  // 检查管理员ID
  if (!currentAdminId.value) {
    console.error('无法获取管理员ID，请重新登录')
    return
  }

  try {
    const result = await manualAuditApi.claimTask(task.taskId, currentAdminId.value)

    if (result.success) {
      task.status = 'in_progress'
      task.adminId = currentAdminId.value
      console.log('任务认领成功')
      fetchStats()
    } else {
      console.error('认领任务失败:', result.message)
    }
  } catch (error) {
    console.error('认领任务失败:', error)
  }
}

// 查看任务详情/审核处理
const viewTaskDetail = async (task) => {
  selectedTask.value = task
  showAuditModal.value = true

  // 重置审核决策表单
  auditDecision.decision = ''
  auditDecision.comments = ''

  // 获取补充材料
  await fetchMaterialRequests(task.productId)
}

// 获取材料请求
const fetchMaterialRequests = async (productId) => {
  try {
    const result = await manualAuditApi.getMaterialRequests(productId)
    if (result.success) {
      materialRequests.value = result.data || []

      // 获取每个请求的补充材料
      for (const request of materialRequests.value) {
        const materialsResult = await manualAuditApi.getSupplementaryMaterials(request.requestId)
        if (materialsResult.success) {
          request.materials = materialsResult.data || []
        }
      }
    }
  } catch (error) {
    console.error('获取材料请求失败:', error)
    materialRequests.value = []
  }
}

// 查看补充材料
const viewMaterials = () => {
  showMaterialsModal.value = true
}

// 关闭材料弹窗
const closeMaterialsModal = () => {
  showMaterialsModal.value = false
}

// 关闭审核弹窗
const closeAuditModal = () => {
  showAuditModal.value = false
  selectedTask.value = null
  materialRequests.value = []
}

// 提交审核决策
const submitAuditDecision = async () => {
  // 检查管理员ID
  if (!currentAdminId.value) {
    console.error('无法获取管理员ID，请重新登录')
    return
  }

  try {
    const decision = {
      adminId: currentAdminId.value,
      decision: auditDecision.decision,
      comments: auditDecision.comments
    }

    const result = await manualAuditApi.submitDecision(selectedTask.value.taskId, decision)

    if (result.success) {
      console.log('审核决策提交成功')
      closeAuditModal()
      refreshTasks()
    } else {
      console.error('提交审核决策失败:', result.message)
    }
  } catch (error) {
    console.error('提交审核决策失败:', error)
  }
}

// 工具函数
const getTaskCardClass = (task) => {
  const classes = []
  if (task.status === 'in_progress' && task.adminId === currentAdminId.value) {
    classes.push('my-task')
  }
  if (isOverdue(task.deadline)) {
    classes.push('overdue')
  }
  return classes.join(' ')
}

const getPriorityClass = (priority) => {
  if (priority >= 8) return 'priority-high'
  if (priority >= 5) return 'priority-medium'
  return 'priority-low'
}

const getPriorityText = (priority) => {
  if (priority >= 8) return '高'
  if (priority >= 5) return '中'
  return '低'
}

const getStatusClass = (status) => {
  const classMap = {
    'pending': 'status-pending',
    'in_progress': 'status-in_progress',
    'completed': 'status-completed',
    'material_requested': 'status-material_requested'
  }
  return classMap[status] || ''
}

const getStatusText = (status) => {
  const textMap = {
    'pending': '待认领',
    'in_progress': '审核中',
    'completed': '已完成',
    'material_requested': '等待补充材料'
  }
  return textMap[status] || '未知'
}

const isOverdue = (deadline) => {
  if (!deadline) return false

  // 处理数组格式的时间
  if (Array.isArray(deadline)) {
    const [year, month, day, hour, minute, second] = deadline
    const deadlineDate = new Date(year, month - 1, day, hour, minute, second)
    return deadlineDate < new Date()
  }

  // 处理标准格式的时间
  return new Date(deadline) < new Date()
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  // 处理数组格式的时间 [year, month, day, hour, minute, second]
  if (Array.isArray(dateTime)) {
    // 注意：JavaScript的月份是从0开始的，所以需要减1
    const [year, month, day, hour, minute, second] = dateTime
    const date = new Date(year, month - 1, day, hour, minute, second)
    return date.toLocaleString('zh-CN')
  }

  // 处理标准格式的时间
  return new Date(dateTime).toLocaleString('zh-CN')
}

const parseAuditReasons = (reasons) => {
  try {
    return JSON.parse(reasons || '[]')
  } catch {
    return []
  }
}

// 解析需要的材料
const parseRequiredMaterials = (materials) => {
  if (!materials) return []
  try {
    return JSON.parse(materials)
  } catch (e) {
    return [materials]
  }
}

// 解析材料文件URL
const parseMaterialUrls = (urls) => {
  if (!urls) return []
  try {
    return JSON.parse(urls)
  } catch (e) {
    return [urls]
  }
}

// 获取请求状态样式
const getRequestStatusClass = (status) => {
  const statusMap = {
    'waiting': 'status-waiting',
    'submitted': 'status-submitted',
    'approved': 'status-approved',
    'rejected': 'status-rejected'
  }
  return statusMap[status] || 'status-default'
}

// 获取请求状态文本
const getRequestStatusText = (status) => {
  const statusMap = {
    'waiting': '等待提交',
    'submitted': '已提交',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

const parseProductImages = (images) => {
  if (!images) return []
  return images.split(',').map(img => {
    const trimmedImg = img.trim()
    // 如果已经是完整URL，直接返回；否则添加/uploads/前缀
    if (trimmedImg.startsWith('http://') || trimmedImg.startsWith('https://')) {
      return trimmedImg
    }
    return `/uploads/${trimmedImg}`
  })
}

const formatAutoAuditResult = (result) => {
  if (!result) return '无自动审核结果'
  try {
    return JSON.stringify(JSON.parse(result), null, 2)
  } catch {
    return result
  }
}
</script>

<style scoped>
.manual-audit-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-item label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-input, .form-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn {
  background: #1890ff;
  color: white;
}

.reset-btn {
  background: #f5f5f5;
  color: #333;
  margin-left: 8px;
}

.task-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-count {
  color: #666;
  font-size: 14px;
}

.refresh-btn {
  padding: 6px 12px;
  background: #f5f5f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.task-cards {
  padding: 20px;
  display: grid;
  gap: 16px;
}

.task-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.task-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.task-card.my-task {
  border-color: #1890ff;
  background: #f6ffed;
}

.task-card.overdue {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.task-id {
  font-weight: 500;
  color: #333;
}

.priority-badge, .status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.priority-high {
  background: #fff2f0;
  color: #ff4d4f;
}

.priority-medium {
  background: #fffbe6;
  color: #faad14;
}

.priority-low {
  background: #f6ffed;
  color: #52c41a;
}

.status-pending {
  background: #f0f0f0;
  color: #666;
}

.status-in_progress {
  background: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-material_requested {
  background: #fff3cd;
  color: #856404;
}

.task-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.deadline.overdue {
  color: #ff4d4f;
  font-weight: 500;
}

.task-content {
  margin-bottom: 16px;
}

.product-info, .audit-reasons {
  margin-bottom: 12px;
}

.product-info h4, .audit-reasons h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.product-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.detail-item {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  min-width: 60px;
}

.reasons-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.reason-tag {
  background: #fff7e6;
  color: #fa8c16;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #ffd591;
}

.task-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.claim-btn {
  background: #52c41a;
  color: white;
}

.detail-btn {
  background: #1890ff;
  color: white;
}

.view-btn {
  background: #f5f5f5;
  color: #333;
}

.pagination {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 900px;
  max-height: 80vh;
  overflow-y: auto;
}

.audit-modal {
  max-width: 1000px;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

.section {
  margin-bottom: 24px;
}

.section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.product-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.product-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.auto-audit-result pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.decision-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn, .submit-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #333;
}

.submit-btn {
  background: #1890ff;
  color: white;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 补充材料样式 */
.material-requests {
  margin-top: 10px;
}

.material-request-item {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  background: #fafafa;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.request-time {
  font-size: 12px;
  color: #666;
}

.request-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-waiting {
  background: #fff3cd;
  color: #856404;
}

.status-submitted {
  background: #d1ecf1;
  color: #0c5460;
}

.status-approved {
  background: #d4edda;
  color: #155724;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.request-reason {
  margin-bottom: 10px;
  line-height: 1.5;
}

.required-materials ul {
  margin: 5px 0 0 20px;
  padding: 0;
}

.required-materials li {
  margin-bottom: 3px;
}

.submitted-materials {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.materials-list {
  margin-top: 10px;
}

.material-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.material-type {
  font-weight: 500;
  color: #333;
}

.submit-time {
  font-size: 12px;
  color: #666;
}

.material-description {
  margin-bottom: 8px;
  color: #555;
  line-height: 1.4;
}

.material-files {
  display: flex;
  gap: 10px;
}

.material-file-link {
  color: #007bff;
  text-decoration: none;
  font-size: 12px;
  padding: 2px 6px;
  border: 1px solid #007bff;
  border-radius: 3px;
  transition: all 0.2s;
}

.material-file-link:hover {
  background: #007bff;
  color: white;
}

.no-materials {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}
</style>
