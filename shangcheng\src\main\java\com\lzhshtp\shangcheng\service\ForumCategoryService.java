package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.ForumCategoryDTO;
import com.lzhshtp.shangcheng.dto.ForumCategoryRequest;
import com.lzhshtp.shangcheng.dto.ApiResponse;

import java.util.List;

/**
 * 论坛分类服务接口
 */
public interface ForumCategoryService {

    /**
     * 获取所有论坛分类
     *
     * @return 论坛分类列表
     */
    List<ForumCategoryDTO> getAllCategories();

    /**
     * 根据ID获取论坛分类
     *
     * @param id 论坛分类ID
     * @return 论坛分类信息
     */
    ForumCategoryDTO getCategoryById(Integer id);

    /**
     * 创建论坛分类
     *
     * @param request 论坛分类请求
     * @return 创建结果
     */
    ApiResponse<ForumCategoryDTO> createCategory(ForumCategoryRequest request);

    /**
     * 更新论坛分类
     *
     * @param id 论坛分类ID
     * @param request 论坛分类请求
     * @return 更新结果
     */
    ApiResponse<ForumCategoryDTO> updateCategory(Integer id, ForumCategoryRequest request);

    /**
     * 删除论坛分类
     *
     * @param id 论坛分类ID
     * @return 删除结果
     */
    ApiResponse<Void> deleteCategory(Integer id);
} 