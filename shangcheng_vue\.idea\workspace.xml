<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0482a784-e641-4c78-a311-e73a74758b73" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/admin/views/verifications/VerificationManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/admin/views/verifications/VerificationManagement.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/maven/apache-maven-3.6.1-bin/apache-maven-3.6.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.1-bin\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="19" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zLUnfd9vBToVrwKfOPzsblfdto" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Node.js.ai.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.user.js.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\IntelliJ IDEA 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0482a784-e641-4c78-a311-e73a74758b73" name="Changes" comment="" />
      <created>1751510016818</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751510016818</updated>
      <workItem from="1751510019185" duration="1846000" />
      <workItem from="1751561574326" duration="12605000" />
      <workItem from="1751723624052" duration="5000" />
      <workItem from="1751729512620" duration="20451000" />
      <workItem from="1751816509425" duration="13338000" />
      <workItem from="1751908388389" duration="9890000" />
      <workItem from="1752024626251" duration="3867000" />
      <workItem from="1752078090846" duration="8283000" />
      <workItem from="1752117815385" duration="3588000" />
      <workItem from="1752170176708" duration="6955000" />
      <workItem from="1752203560753" duration="5132000" />
      <workItem from="1752350050391" duration="4665000" />
      <workItem from="1752398509633" duration="6810000" />
      <workItem from="1752433960948" duration="6772000" />
      <workItem from="1752455157317" duration="10121000" />
      <workItem from="1752517559751" duration="5721000" />
      <workItem from="1752543031710" duration="13255000" />
      <workItem from="1752606302916" duration="6026000" />
      <workItem from="1752669953070" duration="1241000" />
      <workItem from="1752679766601" duration="4018000" />
      <workItem from="1752688465026" duration="8094000" />
      <workItem from="1752737094741" duration="6092000" />
      <workItem from="1752771056469" duration="1188000" />
      <workItem from="1752774317343" duration="1201000" />
      <workItem from="1752776125456" duration="11275000" />
      <workItem from="1753019784132" duration="57000" />
      <workItem from="1753058988134" duration="6081000" />
      <workItem from="1753098594333" duration="1182000" />
      <workItem from="1753356355050" duration="3833000" />
      <workItem from="1753362877477" duration="764000" />
      <workItem from="1753364039045" duration="2607000" />
      <workItem from="1753401894904" duration="599000" />
      <workItem from="1753404152696" duration="595000" />
      <workItem from="1753406816635" duration="11009000" />
      <workItem from="1753457621714" duration="10089000" />
      <workItem from="1753545521282" duration="1296000" />
      <workItem from="1753552161882" duration="4249000" />
      <workItem from="1753629672384" duration="8379000" />
      <workItem from="1753673881952" duration="7823000" />
      <workItem from="1753727517224" duration="2458000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>