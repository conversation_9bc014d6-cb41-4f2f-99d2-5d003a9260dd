package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用API响应类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private Integer code;
    
    /**
     * 成功响应
     * 
     * @param data 响应数据
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, "操作成功", data, 200);
    }
    
    /**
     * 成功响应
     *
     * @param message 成功消息
     * @param data 响应数据
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data, 200);
    }

    /**
     * 成功响应（无数据）
     *
     * @param message 成功消息
     * @return ApiResponse<Void>
     */
    public static ApiResponse<Void> successVoid(String message) {
        return new ApiResponse<>(true, message, null, 200);
    }
    
    /**
     * 失败响应
     * 
     * @param message 错误消息
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> fail(String message) {
        return new ApiResponse<>(false, message, null, 400);
    }
    
    /**
     * 失败响应
     *
     * @param message 错误消息
     * @param code 错误码
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> fail(String message, Integer code) {
        return new ApiResponse<>(false, message, null, code);
    }

    /**
     * 失败响应（带数据）
     *
     * @param message 错误消息
     * @param data 响应数据
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> fail(String message, T data) {
        return new ApiResponse<>(false, message, data, 400);
    }

    /**
     * 失败响应（带数据和错误码）
     *
     * @param message 错误消息
     * @param data 响应数据
     * @param code 错误码
     * @return ApiResponse
     */
    public static <T> ApiResponse<T> fail(String message, T data, Integer code) {
        return new ApiResponse<>(false, message, data, code);
    }
}