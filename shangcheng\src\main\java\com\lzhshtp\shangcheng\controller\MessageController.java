package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ConversationDTO;
import com.lzhshtp.shangcheng.dto.MessageDTO;
import com.lzhshtp.shangcheng.dto.MessageRequest;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ConversationService;
import com.lzhshtp.shangcheng.service.MessageService;
import com.lzhshtp.shangcheng.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息控制器
 * 处理与消息和会话相关的请求
 */
@RestController
@RequestMapping("/api/messages")
public class MessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户的所有会话列表
     * @return 会话DTO列表
     */
    @GetMapping("/conversations")
    public ResponseEntity<ApiResponse<List<ConversationDTO>>> getUserConversations() {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 获取会话列表
        List<ConversationDTO> conversations = conversationService.getUserConversations(currentUserId);

        return ResponseEntity.ok(ApiResponse.success(conversations));
    }

    /**
     * 获取会话中的消息列表
     * @param conversationId 会话ID
     * @param page 页码，默认为1
     * @param size 每页大小，默认为20
     * @return 消息DTO分页结果
     */
    @GetMapping("/conversations/{conversationId}")
    public ResponseEntity<ApiResponse<PageResult<MessageDTO>>> getConversationMessages(
            @PathVariable Long conversationId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 验证用户是否是会话参与者
        if (!conversationService.isConversationParticipant(conversationId, currentUserId)) {
            return ResponseEntity.badRequest().body(ApiResponse.fail("您不是该会话的参与者"));
        }

        // 获取消息列表
        PageResult<MessageDTO> messages = messageService.getConversationMessages(conversationId, currentUserId, page, size);

        return ResponseEntity.ok(ApiResponse.success(messages));
    }

    /**
     * 发送消息
     * @param messageRequest 消息请求
     * @return 发送的消息DTO
     */
    @PostMapping("/send")
    public ResponseEntity<ApiResponse<MessageDTO>> sendMessage(@RequestBody MessageRequest messageRequest) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 发送消息
        MessageDTO sentMessage = messageService.sendMessage(currentUserId, messageRequest);

        return ResponseEntity.ok(ApiResponse.success(sentMessage));
    }

    /**
     * 管理员发送系统消息
     * @param messageRequest 消息请求
     * @return 发送的消息DTO
     */
    @PostMapping("/admin/send-system")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<ApiResponse<MessageDTO>> sendSystemMessage(@RequestBody MessageRequest messageRequest) {
        // 获取当前登录管理员ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User admin = userService.findByUsername(username);
        Long adminId = admin.getUserId();

        // 发送系统消息（标记为系统消息）
        MessageDTO sentMessage = messageService.sendSystemMessage(adminId, messageRequest);

        return ResponseEntity.ok(ApiResponse.success(sentMessage));
    }

    /**
     * 将会话中的消息标记为已读
     * @param conversationId 会话ID
     * @return 操作结果
     */
    @PutMapping("/conversations/{conversationId}/read")
    public ResponseEntity<ApiResponse<Boolean>> markMessagesAsRead(@PathVariable Long conversationId) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 验证用户是否是会话参与者
        if (!conversationService.isConversationParticipant(conversationId, currentUserId)) {
            return ResponseEntity.badRequest().body(ApiResponse.fail("您不是该会话的参与者"));
        }

        // 标记消息为已读
        boolean success = messageService.markMessagesAsRead(conversationId, currentUserId);

        return ResponseEntity.ok(ApiResponse.success(success));
    }

    /**
     * 删除消息
     * @param messageId 消息ID
     * @return 操作结果
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<ApiResponse<Boolean>> deleteMessage(@PathVariable Long messageId) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 删除消息
        boolean success = messageService.deleteMessage(messageId, currentUserId);

        return ResponseEntity.ok(ApiResponse.success(success));
    }

    /**
     * 获取当前用户的未读消息总数
     * @return 未读消息数
     */
    @GetMapping("/unread-count")
    public ResponseEntity<ApiResponse<Integer>> getUnreadCount() {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 获取未读消息数
        int unreadCount = messageService.getTotalUnreadCount(currentUserId);

        return ResponseEntity.ok(ApiResponse.success(unreadCount));
    }

    /**
     * 获取或创建与指定用户的会话
     * @param userId 对方用户ID
     * @return 会话ID
     */
    @GetMapping("/conversations/with-user/{userId}")
    public ResponseEntity<ApiResponse<Long>> getOrCreateConversation(@PathVariable Long userId) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Long currentUserId = user.getUserId();

        // 获取或创建会话
        Long conversationId = conversationService.getOrCreateConversation(currentUserId, userId);

        return ResponseEntity.ok(ApiResponse.success(conversationId));
    }
}
