{"services": [{"typeName": "io.github.qifan777.knowledge.ai.document.DocumentController", "operations": [{"name": "embedding", "key": "embedding:org.springframework.web.multipart.MultipartFile", "doc": {"value": "嵌入文件\n", "parameters": {"file": "待嵌入的文件"}, "return": "是否成功"}, "parameters": [{"name": "file", "type": {"typeName": "org.springframework.web.multipart.MultipartFile"}, "index": 0}], "returnType": {"typeName": "boolean", "nullable": true}}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessageController", "operations": [{"name": "deleteHistory", "key": "deleteHistory:java.lang.String", "parameters": [{"name": "sessionId", "type": {"typeName": "java.lang.String"}, "index": 0}]}, {"name": "save", "key": "save:io.github.qifan777.knowledge.ai.messge.dto.AiMessageInput", "doc": {"value": "消息保存", "parameters": {"input": "用户发送的消息/AI回复的消息"}}, "parameters": [{"name": "input", "type": {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageInput"}, "index": 0}]}, {"name": "chatStreamWithHistory", "key": "chatStreamWithHistory:io.github.qifan777.knowledge.ai.messge.dto.AiMessageWrapper", "doc": {"parameters": {"input": "消息包含文本信息，会话id，多媒体信息（图片语言）。参考src/main/dto/AiMessage.dto"}, "return": "SSE流"}, "parameters": [{"name": "input", "type": {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageWrapper"}, "index": 0}], "returnType": {"typeName": "reactor.core.publisher.Flux", "arguments": [{"typeName": "org.springframework.http.codec.ServerSentEvent", "arguments": [{"typeName": "java.lang.String"}]}]}}]}, {"typeName": "io.github.qifan777.knowledge.ai.session.AiSessionController", "operations": [{"name": "findById", "key": "findById:java.lang.String", "doc": {"value": "根据id查询会话", "parameters": {"id": "会话id"}, "return": "会话信息"}, "parameters": [{"name": "id", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "io.github.qifan777.knowledge.ai.session.AiSession", "fetchBy": "FETCHER", "fetcherOwner": "io.github.qifan777.knowledge.ai.session.AiSessionRepository", "fetcherDoc": null}}, {"name": "save", "key": "save:io.github.qifan777.knowledge.ai.session.dto.AiSessionInput", "doc": {"value": "保存会话", "parameters": {"input": "会话dto参考src/main/dto/AiSession.dto"}, "return": "创建后的id"}, "parameters": [{"name": "input", "type": {"typeName": "io.github.qifan777.knowledge.ai.session.dto.AiSessionInput"}, "index": 0}], "returnType": {"typeName": "java.lang.String"}}, {"name": "findByUser", "key": "findByUser", "doc": {"value": "查询当前登录用户的会话\n", "return": "会话列表"}, "returnType": {"typeName": "java.util.List", "arguments": [{"typeName": "io.github.qifan777.knowledge.ai.session.AiSession", "fetchBy": "FETCHER", "fetcherOwner": "io.github.qifan777.knowledge.ai.session.AiSessionRepository", "fetcherDoc": null}]}}, {"name": "delete", "key": "delete:java.util.List", "doc": {"value": "批量删除会话", "parameters": {"ids": "会话id列表"}}, "parameters": [{"name": "ids", "type": {"typeName": "java.util.List", "arguments": [{"typeName": "java.lang.String"}]}, "index": 0}]}]}, {"typeName": "io.github.qifan777.knowledge.demo.DocumentDemoController", "operations": [{"name": "embedding", "key": "embedding:java.lang.String", "parameters": [{"name": "text", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "java.util.List", "arguments": [{"typeName": "float"}]}}, {"name": "readFromMultipart", "key": "readFromMultipart:org.springframework.web.multipart.MultipartFile", "parameters": [{"name": "file", "type": {"typeName": "org.springframework.web.multipart.MultipartFile"}, "index": 0}], "returnType": {"typeName": "java.lang.String"}}, {"name": "readFromLocalFile", "key": "readFromLocalFile:java.lang.String", "parameters": [{"name": "file", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "java.lang.String"}}, {"name": "split", "key": "split:org.springframework.web.multipart.MultipartFile", "parameters": [{"name": "file", "type": {"typeName": "org.springframework.web.multipart.MultipartFile"}, "index": 0}], "returnType": {"typeName": "java.util.List", "arguments": [{"typeName": "java.lang.String"}]}}, {"name": "writeVector", "key": "writeVector:org.springframework.web.multipart.MultipartFile", "parameters": [{"name": "file", "type": {"typeName": "org.springframework.web.multipart.MultipartFile"}, "index": 0}]}, {"name": "query", "key": "query:java.lang.String", "parameters": [{"name": "text", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "java.util.List", "arguments": [{"typeName": "org.springframework.ai.document.Document"}]}}]}, {"typeName": "io.github.qifan777.knowledge.demo.MessageDemoController", "operations": [{"name": "chat", "key": "chat:java.lang.String", "doc": {"value": "非流式问答\n", "parameters": {"prompt": "用户提问"}, "return": "org.springframework.ai.chat.model.ChatResponse"}, "parameters": [{"name": "prompt", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "java.lang.String"}}, {"name": "chatStream", "key": "chatStream:java.lang.String", "doc": {"value": "流式问答\n", "parameters": {"prompt": "用户提问"}, "return": "Flux<ServerSentEvent < String>> 流式响应"}, "parameters": [{"name": "prompt", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "reactor.core.publisher.Flux", "arguments": [{"typeName": "java.lang.String"}]}}, {"name": "chatStreamWithHistory", "key": "chatStreamWithHistory:java.lang.String:java.lang.String", "doc": {"value": "根据会话id，从数据库中查找历史消息，并将消息作为上下文回答。\n", "parameters": {"prompt": "用户的提问", "sessionId": "会话id"}, "return": "SSE流响应"}, "parameters": [{"name": "prompt", "type": {"typeName": "java.lang.String"}, "index": 0}, {"name": "sessionId", "type": {"typeName": "java.lang.String"}, "index": 1}], "returnType": {"typeName": "reactor.core.publisher.Flux", "arguments": [{"typeName": "java.lang.String"}]}}, {"name": "chatStreamWithRag", "key": "chatStreamWithRag:java.lang.String", "doc": {"value": "从向量数据库中查找文档，并将查询的文档作为上下文回答。\n", "parameters": {"prompt": "用户的提问"}, "return": "SSE流响应"}, "parameters": [{"name": "prompt", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "reactor.core.publisher.Flux", "arguments": [{"typeName": "java.lang.String"}]}}, {"name": "chatStreamWithFunction", "key": "chatStreamWithFunction:java.lang.String", "doc": {"value": "调用自定义函数回答用户的提问\n", "parameters": {"prompt": "用户的提问"}, "return": "SSE流式响应"}, "parameters": [{"name": "prompt", "type": {"typeName": "java.lang.String"}, "index": 0}], "returnType": {"typeName": "reactor.core.publisher.Flux", "arguments": [{"typeName": "java.lang.String"}]}}]}, {"typeName": "io.github.qifan777.knowledge.user.UserController", "operations": [{"name": "userInfo", "key": "userInfo", "returnType": {"typeName": "io.github.qifan777.knowledge.user.User", "fetchBy": "FETCHER", "fetcherOwner": "io.github.qifan777.knowledge.user.UserRepository", "fetcherDoc": null}}, {"name": "login", "key": "login:io.github.qifan777.knowledge.user.dto.UserLoginInput", "parameters": [{"name": "input", "type": {"typeName": "io.github.qifan777.knowledge.user.dto.UserLoginInput"}, "index": 0}], "returnType": {"typeName": "cn.dev33.satoken.stp.SaTokenInfo"}}, {"name": "register", "key": "register:io.github.qifan777.knowledge.user.dto.UserRegisterInput", "parameters": [{"name": "input", "type": {"typeName": "io.github.qifan777.knowledge.user.dto.UserRegisterInput"}, "index": 0}], "returnType": {"typeName": "cn.dev33.satoken.stp.SaTokenInfo"}}]}], "definitions": [{"typeName": "cn.dev33.satoken.stp.SaTokenInfo", "props": [{"name": "is<PERSON>ogin", "type": {"typeName": "boolean", "nullable": true}}, {"name": "loginDevice", "type": {"typeName": "java.lang.String"}}, {"name": "loginId", "type": {"typeName": "java.lang.Object"}}, {"name": "loginType", "type": {"typeName": "java.lang.String"}}, {"name": "sessionTimeout", "type": {"typeName": "long"}}, {"name": "tag", "type": {"typeName": "java.lang.String"}}, {"name": "tokenActiveTimeout", "type": {"typeName": "long"}}, {"name": "tokenName", "type": {"typeName": "java.lang.String"}}, {"name": "tokenSessionTimeout", "type": {"typeName": "long"}}, {"name": "tokenTimeout", "type": {"typeName": "long"}}, {"name": "tokenValue", "type": {"typeName": "java.lang.String"}}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessage", "kind": "IMMUTABLE", "doc": {"value": "历史消息"}, "props": [{"name": "type", "type": {"typeName": "org.springframework.ai.chat.messages.MessageType"}, "doc": {"value": "消息类型(用户/助手/系统)"}}, {"name": "textContent", "type": {"typeName": "java.lang.String"}, "doc": {"value": "消息内容"}}, {"name": "medias", "type": {"typeName": "java.util.List", "nullable": true, "arguments": [{"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessage$Media"}]}}, {"name": "sessionId", "type": {"typeName": "java.lang.String"}}, {"name": "session", "type": {"typeName": "io.github.qifan777.knowledge.ai.session.AiSession"}, "doc": {"value": "会话"}}], "superTypes": [{"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntity"}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessage$Media", "props": [{"name": "data", "type": {"typeName": "java.lang.String"}}, {"name": "type", "type": {"typeName": "java.lang.String"}}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageInput", "doc": {"value": "历史消息"}, "props": [{"name": "type", "type": {"typeName": "org.springframework.ai.chat.messages.MessageType"}, "doc": {"value": "消息类型(用户/助手/系统)"}}, {"name": "textContent", "type": {"typeName": "java.lang.String"}, "doc": {"value": "消息内容"}}, {"name": "medias", "type": {"typeName": "java.util.List", "nullable": true, "arguments": [{"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessage$Media"}]}}, {"name": "sessionId", "type": {"typeName": "java.lang.String"}, "doc": {"value": "会话"}}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageParams", "props": [{"name": "enableAgent", "type": {"typeName": "boolean", "nullable": true}}, {"name": "enableVectorStore", "type": {"typeName": "boolean", "nullable": true}}]}, {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageWrapper", "props": [{"name": "message", "type": {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageInput"}}, {"name": "params", "type": {"typeName": "io.github.qifan777.knowledge.ai.messge.dto.AiMessageParams"}}]}, {"typeName": "io.github.qifan777.knowledge.ai.session.AiSession", "kind": "IMMUTABLE", "doc": {"value": "会话"}, "props": [{"name": "name", "type": {"typeName": "java.lang.String"}, "doc": {"value": "会话名称"}}, {"name": "messages", "type": {"typeName": "java.util.List", "arguments": [{"typeName": "io.github.qifan777.knowledge.ai.messge.AiMessage"}]}, "doc": {"value": "一对多关联消息，按创建时间升序"}}], "superTypes": [{"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntity"}]}, {"typeName": "io.github.qifan777.knowledge.ai.session.dto.AiSessionInput", "doc": {"value": "会话"}, "props": [{"name": "id", "type": {"typeName": "java.lang.String", "nullable": true}}, {"name": "name", "type": {"typeName": "java.lang.String"}, "doc": {"value": "会话名称"}}]}, {"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseDateTime", "kind": "IMMUTABLE", "props": [{"name": "createdTime", "type": {"typeName": "java.time.LocalDateTime"}}, {"name": "editedTime", "type": {"typeName": "java.time.LocalDateTime"}}]}, {"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntity", "kind": "IMMUTABLE", "props": [{"name": "id", "type": {"typeName": "java.lang.String"}}, {"name": "editor", "type": {"typeName": "io.github.qifan777.knowledge.user.User"}}, {"name": "creator", "type": {"typeName": "io.github.qifan777.knowledge.user.User"}}], "superTypes": [{"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseDateTime"}]}, {"typeName": "io.github.qifan777.knowledge.user.User", "kind": "IMMUTABLE", "props": [{"name": "id", "type": {"typeName": "java.lang.String"}}, {"name": "phone", "type": {"typeName": "java.lang.String"}, "doc": {"value": "手机号"}}, {"name": "password", "type": {"typeName": "java.lang.String"}, "doc": {"value": "密码"}}, {"name": "avatar", "type": {"typeName": "java.lang.String", "nullable": true}, "doc": {"value": "头像"}}, {"name": "nickname", "type": {"typeName": "java.lang.String", "nullable": true}, "doc": {"value": "昵称"}}, {"name": "gender", "type": {"typeName": "java.lang.String", "nullable": true}, "doc": {"value": "性别"}}], "superTypes": [{"typeName": "io.github.qifan777.knowledge.infrastructure.jimmer.BaseDateTime"}]}, {"typeName": "io.github.qifan777.knowledge.user.dto.UserLoginInput", "props": [{"name": "phone", "type": {"typeName": "java.lang.String"}, "doc": {"value": "手机号"}}, {"name": "password", "type": {"typeName": "java.lang.String"}, "doc": {"value": "密码"}}]}, {"typeName": "io.github.qifan777.knowledge.user.dto.UserRegisterInput", "props": [{"name": "phone", "type": {"typeName": "java.lang.String"}, "doc": {"value": "手机号"}}, {"name": "password", "type": {"typeName": "java.lang.String"}, "doc": {"value": "密码"}}]}, {"typeName": "java.io.InputStream"}, {"typeName": "java.io.Serializable"}, {"typeName": "java.lang.Comparable"}, {"typeName": "java.net.URI", "props": [{"name": "authority", "type": {"typeName": "java.lang.String"}}, {"name": "fragment", "type": {"typeName": "java.lang.String"}}, {"name": "host", "type": {"typeName": "java.lang.String"}}, {"name": "path", "type": {"typeName": "java.lang.String"}}, {"name": "port", "type": {"typeName": "int"}}, {"name": "query", "type": {"typeName": "java.lang.String"}}, {"name": "rawAuthority", "type": {"typeName": "java.lang.String"}}, {"name": "rawFragment", "type": {"typeName": "java.lang.String"}}, {"name": "rawPath", "type": {"typeName": "java.lang.String"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": {"typeName": "java.lang.String"}}, {"name": "rawSchemeSpecificPart", "type": {"typeName": "java.lang.String"}}, {"name": "rawUserInfo", "type": {"typeName": "java.lang.String"}}, {"name": "scheme", "type": {"typeName": "java.lang.String"}}, {"name": "schemeSpecificPart", "type": {"typeName": "java.lang.String"}}, {"name": "userInfo", "type": {"typeName": "java.lang.String"}}, {"name": "absolute", "type": {"typeName": "boolean"}}, {"name": "opaque", "type": {"typeName": "boolean"}}], "superTypes": [{"typeName": "java.lang.Comparable", "arguments": [{"typeName": "java.net.URI"}]}, {"typeName": "java.io.Serializable"}]}, {"typeName": "java.net.URL", "props": [{"name": "authority", "type": {"typeName": "java.lang.String"}}, {"name": "content", "type": {"typeName": "java.lang.Object"}}, {"name": "defaultPort", "type": {"typeName": "int"}}, {"name": "file", "type": {"typeName": "java.lang.String"}}, {"name": "host", "type": {"typeName": "java.lang.String"}}, {"name": "path", "type": {"typeName": "java.lang.String"}}, {"name": "port", "type": {"typeName": "int"}}, {"name": "protocol", "type": {"typeName": "java.lang.String"}}, {"name": "query", "type": {"typeName": "java.lang.String"}}, {"name": "ref", "type": {"typeName": "java.lang.String"}}, {"name": "userInfo", "type": {"typeName": "java.lang.String"}}], "superTypes": [{"typeName": "java.io.Serializable"}]}, {"typeName": "java.nio.charset.Charset", "props": [{"name": "registered", "type": {"typeName": "boolean"}}], "superTypes": [{"typeName": "java.lang.Comparable", "arguments": [{"typeName": "java.nio.charset.Charset"}]}]}, {"typeName": "org.reactivestreams.Publisher"}, {"typeName": "org.springframework.ai.chat.messages.MessageType", "kind": "ENUM", "constants": [{"name": "ASSISTANT"}, {"name": "SYSTEM"}, {"name": "TOOL"}, {"name": "USER"}]}, {"typeName": "org.springframework.ai.document.Document", "props": [{"name": "content", "type": {"typeName": "java.lang.String"}}, {"name": "id", "type": {"typeName": "java.lang.String"}}, {"name": "media", "type": {"typeName": "org.springframework.ai.model.Media", "nullable": true}}, {"name": "metadata", "type": {"typeName": "java.util.Map", "arguments": [{"typeName": "java.lang.String"}, {"typeName": "java.lang.Object"}]}}, {"name": "score", "type": {"typeName": "double", "nullable": true}}, {"name": "text", "type": {"typeName": "boolean"}}]}, {"typeName": "org.springframework.ai.model.Media", "props": [{"name": "data", "type": {"typeName": "java.lang.Object"}}, {"name": "dataAsByteArray", "type": {"typeName": "java.util.List", "arguments": [{"typeName": "byte"}]}}, {"name": "id", "type": {"typeName": "java.lang.String"}}, {"name": "mimeType", "type": {"typeName": "org.springframework.util.MimeType"}}, {"name": "name", "type": {"typeName": "java.lang.String"}}]}, {"typeName": "org.springframework.core.io.InputStreamSource", "props": [{"name": "inputStream", "type": {"typeName": "java.io.InputStream"}}]}, {"typeName": "org.springframework.core.io.Resource", "props": [{"name": "contentAsByteArray", "type": {"typeName": "java.util.List", "arguments": [{"typeName": "byte"}]}}, {"name": "description", "type": {"typeName": "java.lang.String"}}, {"name": "file", "type": {"typeName": "boolean"}}, {"name": "filename", "type": {"typeName": "java.lang.String", "nullable": true}}, {"name": "uri", "type": {"typeName": "java.net.URI"}}, {"name": "url", "type": {"typeName": "java.net.URL"}}, {"name": "open", "type": {"typeName": "boolean"}}, {"name": "readable", "type": {"typeName": "boolean"}}], "superTypes": [{"typeName": "org.springframework.core.io.InputStreamSource"}]}, {"typeName": "org.springframework.http.codec.ServerSentEvent"}, {"typeName": "org.springframework.util.MimeType", "props": [{"name": "charset", "type": {"typeName": "java.nio.charset.Charset", "nullable": true}}, {"name": "parameters", "type": {"typeName": "java.util.Map", "arguments": [{"typeName": "java.lang.String"}, {"typeName": "java.lang.String"}]}}, {"name": "subtype", "type": {"typeName": "java.lang.String"}}, {"name": "subtypeSuffix", "type": {"typeName": "java.lang.String", "nullable": true}}, {"name": "type", "type": {"typeName": "java.lang.String"}}, {"name": "concrete", "type": {"typeName": "boolean"}}, {"name": "wildcardSubtype", "type": {"typeName": "boolean"}}, {"name": "wildcardType", "type": {"typeName": "boolean"}}], "superTypes": [{"typeName": "java.lang.Comparable", "arguments": [{"typeName": "org.springframework.util.MimeType"}]}, {"typeName": "java.io.Serializable"}]}, {"typeName": "org.springframework.web.multipart.MultipartFile", "props": [{"name": "bytes", "type": {"typeName": "java.util.List", "arguments": [{"typeName": "byte"}]}}, {"name": "contentType", "type": {"typeName": "java.lang.String", "nullable": true}}, {"name": "inputStream", "type": {"typeName": "java.io.InputStream"}}, {"name": "name", "type": {"typeName": "java.lang.String"}}, {"name": "originalFilename", "type": {"typeName": "java.lang.String", "nullable": true}}, {"name": "resource", "type": {"typeName": "org.springframework.core.io.Resource"}}, {"name": "size", "type": {"typeName": "long"}}, {"name": "empty", "type": {"typeName": "boolean"}}], "superTypes": [{"typeName": "org.springframework.core.io.InputStreamSource"}]}, {"typeName": "reactor.core.CorePublisher", "superTypes": [{"typeName": "org.reactivestreams.Publisher", "arguments": [{"typeName": "<reactor.core.CorePublisher::T>"}]}]}, {"typeName": "reactor.core.publisher.Flux", "props": [{"name": "prefetch", "type": {"typeName": "int"}}], "superTypes": [{"typeName": "reactor.core.CorePublisher", "arguments": [{"typeName": "<reactor.core.publisher.Flux::T>"}]}]}]}