import request from '@/utils/request'

/**
 * 人工审核任务API
 */
export const manualAuditApi = {
  /**
   * 获取人工审核任务列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.status - 任务状态
   * @param {number} params.priority - 优先级
   * @param {number} params.productId - 商品ID
   * @param {number} params.adminId - 管理员ID
   */
  getTasks(params = {}) {
    return request.get('/admin/audit/manual/tasks', { params })
  },

  /**
   * 获取人工审核统计数据
   */
  getStats() {
    return request.get('/admin/audit/manual/stats')
  },

  /**
   * 认领审核任务
   * @param {number} taskId - 任务ID
   * @param {number} adminId - 管理员ID
   */
  claimTask(taskId, adminId) {
    return request.post(`/admin/audit/manual/tasks/${taskId}/claim`, { adminId })
  },

  /**
   * 获取审核任务详情
   * @param {number} taskId - 任务ID
   */
  getTaskDetail(taskId) {
    return request.get(`/admin/audit/manual/tasks/${taskId}`)
  },

  /**
   * 提交人工审核决策
   * @param {number} taskId - 任务ID
   * @param {Object} decision - 审核决策
   * @param {number} decision.adminId - 管理员ID
   * @param {string} decision.decision - 决策结果 (approved/rejected/request_materials/escalate_to_second_review)
   * @param {string} decision.comments - 审核意见
   * @param {string} decision.requiredMaterials - 要求补充的材料
   */
  submitDecision(taskId, decision) {
    return request.post(`/admin/audit/manual/tasks/${taskId}/decision`, decision)
  },

  /**
   * 获取审核任务的完整材料
   * @param {number} taskId - 任务ID
   */
  getTaskMaterials(taskId) {
    return request.get(`/admin/audit/manual/tasks/${taskId}/materials`)
  },

  /**
   * 获取商品的材料请求
   * @param {number} productId - 商品ID
   */
  getMaterialRequests(productId) {
    return request.get(`/admin/audit/material-requests/product/${productId}`)
  },

  /**
   * 获取补充材料
   * @param {number} requestId - 材料请求ID
   */
  getSupplementaryMaterials(requestId) {
    return request.get(`/admin/audit/supplementary-materials/request/${requestId}`)
  }
}

export default manualAuditApi
