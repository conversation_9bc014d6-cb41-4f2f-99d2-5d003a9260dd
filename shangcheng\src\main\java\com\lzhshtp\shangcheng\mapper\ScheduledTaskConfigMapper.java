package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.ScheduledTaskConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 定时任务配置Mapper接口
 */
@Mapper
public interface ScheduledTaskConfigMapper extends BaseMapper<ScheduledTaskConfig> {
    
    /**
     * 根据任务名称查询配置
     * 
     * @param taskName 任务名称
     * @return 任务配置
     */
    ScheduledTaskConfig findByTaskName(@Param("taskName") String taskName);
    
    /**
     * 根据任务类型查询配置列表
     * 
     * @param taskType 任务类型
     * @return 任务配置列表
     */
    List<ScheduledTaskConfig> findByTaskType(@Param("taskType") String taskType);
    
    /**
     * 查询所有启用的任务配置
     * 
     * @return 启用的任务配置列表
     */
    List<ScheduledTaskConfig> findEnabledTasks();
}
