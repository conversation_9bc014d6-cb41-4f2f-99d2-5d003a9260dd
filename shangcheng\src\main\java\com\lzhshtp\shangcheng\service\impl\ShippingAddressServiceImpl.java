package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.ShippingAddressDTO;
import com.lzhshtp.shangcheng.dto.ShippingAddressRequest;
import com.lzhshtp.shangcheng.exception.BusinessException;
import com.lzhshtp.shangcheng.mapper.ShippingAddressMapper;
import com.lzhshtp.shangcheng.model.ShippingAddress;
import com.lzhshtp.shangcheng.service.ShippingAddressService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收货地址服务实现类
 */
@Service
public class ShippingAddressServiceImpl implements ShippingAddressService {

    @Autowired
    private ShippingAddressMapper shippingAddressMapper;

    @Override
    public List<ShippingAddressDTO> getUserAddresses(Long userId) {
        List<ShippingAddress> addresses = shippingAddressMapper.findByUserId(userId);
        return addresses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ShippingAddressDTO getAddressById(Long addressId, Long userId) {
        // 检查地址是否属于该用户
        if (!shippingAddressMapper.isAddressBelongsToUser(addressId, userId)) {
            throw new BusinessException("无权访问该地址");
        }
        
        ShippingAddress address = shippingAddressMapper.findById(addressId);
        if (address == null) {
            throw new BusinessException("地址不存在");
        }
        
        return convertToDTO(address);
    }

    @Override
    @Transactional
    public ShippingAddressDTO addAddress(ShippingAddressRequest request, Long userId) {
        ShippingAddress address = new ShippingAddress();
        BeanUtils.copyProperties(request, address);
        address.setUserId(userId);
        
        // 如果是默认地址，先将其他地址设为非默认
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            shippingAddressMapper.resetDefaultAddress(userId);
        }
        
        // 如果用户没有设置是否默认，默认设为false
        if (request.getIsDefault() == null) {
            address.setIsDefault(false);
        }
        
        // 如果是用户的第一个地址，自动设为默认地址
        List<ShippingAddress> existingAddresses = shippingAddressMapper.findByUserId(userId);
        if (existingAddresses.isEmpty()) {
            address.setIsDefault(true);
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        address.setCreatedAt(now);
        address.setUpdatedAt(now);
        
        shippingAddressMapper.insert(address);
        
        return convertToDTO(address);
    }

    @Override
    @Transactional
    public ShippingAddressDTO updateAddress(Long addressId, ShippingAddressRequest request, Long userId) {
        // 检查地址是否属于该用户
        if (!shippingAddressMapper.isAddressBelongsToUser(addressId, userId)) {
            throw new BusinessException("无权修改该地址");
        }
        
        ShippingAddress address = shippingAddressMapper.findById(addressId);
        if (address == null) {
            throw new BusinessException("地址不存在");
        }
        
        BeanUtils.copyProperties(request, address);
        address.setAddressId(addressId);
        address.setUserId(userId);
        
        // 如果是默认地址，先将其他地址设为非默认
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            shippingAddressMapper.resetDefaultAddress(userId);
        }
        
        // 更新地址
        shippingAddressMapper.update(address);
        
        return convertToDTO(address);
    }

    @Override
    @Transactional
    public boolean deleteAddress(Long addressId, Long userId) {
        // 检查地址是否属于该用户
        if (!shippingAddressMapper.isAddressBelongsToUser(addressId, userId)) {
            throw new BusinessException("无权删除该地址");
        }
        
        ShippingAddress address = shippingAddressMapper.findById(addressId);
        if (address == null) {
            throw new BusinessException("地址不存在");
        }
        
        // 删除地址
        int result = shippingAddressMapper.deleteById(addressId);
        
        // 如果删除的是默认地址，且用户还有其他地址，则将最新的地址设为默认地址
        if (Boolean.TRUE.equals(address.getIsDefault())) {
            List<ShippingAddress> remainingAddresses = shippingAddressMapper.findByUserId(userId);
            if (!remainingAddresses.isEmpty()) {
                shippingAddressMapper.setDefaultAddress(remainingAddresses.get(0).getAddressId());
            }
        }
        
        return result > 0;
    }

    @Override
    @Transactional
    public boolean setDefaultAddress(Long addressId, Long userId) {
        // 检查地址是否属于该用户
        if (!shippingAddressMapper.isAddressBelongsToUser(addressId, userId)) {
            throw new BusinessException("无权设置该地址为默认地址");
        }
        
        ShippingAddress address = shippingAddressMapper.findById(addressId);
        if (address == null) {
            throw new BusinessException("地址不存在");
        }
        
        // 先将该用户的所有地址设为非默认
        shippingAddressMapper.resetDefaultAddress(userId);
        
        // 将指定地址设为默认
        return shippingAddressMapper.setDefaultAddress(addressId) > 0;
    }

    @Override
    public ShippingAddressDTO getDefaultAddress(Long userId) {
        ShippingAddress defaultAddress = shippingAddressMapper.findDefaultAddress(userId);
        if (defaultAddress == null) {
            // 如果没有默认地址，返回最新的地址作为默认地址
            List<ShippingAddress> addresses = shippingAddressMapper.findByUserId(userId);
            if (!addresses.isEmpty()) {
                defaultAddress = addresses.get(0);
            }
        }
        
        return defaultAddress != null ? convertToDTO(defaultAddress) : null;
    }
    
    /**
     * 将实体对象转换为DTO对象
     */
    private ShippingAddressDTO convertToDTO(ShippingAddress address) {
        ShippingAddressDTO dto = new ShippingAddressDTO();
        BeanUtils.copyProperties(address, dto);
        return dto;
    }
} 