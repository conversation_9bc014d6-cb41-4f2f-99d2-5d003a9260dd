package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 分类统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryStatisticsDTO {
    private Integer categoryId;     // 分类ID
    private String categoryName;    // 分类名称
    private Long productCount;      // 商品数量
    private Double percentage;      // 占比
    private BigDecimal avgPrice;    // 该分类平均价格
    private Double soldRate;        // 该分类成交率
}
