package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDTO {
    private Long orderId;
    private Long productId;
    private String productTitle;
    private String productImage;
    private Long buyerId;
    private String buyerUsername;
    private Long sellerId;
    private String sellerUsername;
    private LocalDateTime orderDate;
    private BigDecimal totalAmount;
    private String status;
    private String statusText;  // 状态的中文描述
    private String shippingAddress;
    private String paymentMethod;
    private String transactionId;
} 