package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductStatisticsDTO {
    
    // 商品发布趋势
    private List<String> publishDates;         // 发布日期列表
    private List<Long> publishCounts;          // 对应日期的发布数量
    private List<Long> cumulativeProducts;     // 累计商品数
    
    // 商品状态分布
    private Map<String, Long> statusDistribution; // 状态分布
    // {available: 800, sold: 150, pending_review: 50, off_shelf_by_seller: 30, off_shelf_by_admin: 20, deleted: 10}
    
    // 商品分类分布
    private List<CategoryStatisticsDTO> categoryDistribution; // 分类分布
    
    // 商品价格分析
    private Map<String, Long> priceRangeDistribution; // 价格区间分布
    // {"0-100": 200, "100-500": 300, "500-1000": 150, "1000-5000": 100, "5000+": 50}
    
    private BigDecimal avgPrice;               // 平均价格
    private BigDecimal medianPrice;            // 中位数价格
    private BigDecimal maxPrice;               // 最高价格
    private BigDecimal minPrice;               // 最低价格
    
    // 商品成交分析
    private Double soldRate;                   // 成交率
    private Double avgDaysToSold;              // 平均成交天数
    
    // 商品质量分析
    private Map<String, Long> conditionDistribution; // 新旧程度分布
    // {"全新": 300, "九成新": 250, "八成新": 200, "七成新": 150, "其他": 100}
    
    // 商品地域分布
    private List<LocationStatisticsDTO> locationDistribution; // 地域分布
    
    // 配送方式分布
    private Map<String, Long> deliveryMethodDistribution; // 配送方式分布
    // {"快递": 600, "自提": 300, "同城面交": 200, "均可": 100}
    
    // 热门商品（按浏览量、收藏量等）
    private List<HotProductDTO> hotProductsByViews;      // 按浏览量排行
    private List<HotProductDTO> hotProductsByFavorites;  // 按收藏量排行
    private List<HotProductDTO> hotProductsByOrders;     // 按订单量排行
}


