<template>
  <div class="admin-layout">
    <!-- 顶部导航栏 -->
    <header class="admin-header">
      <h1 class="logo">二手交易平台管理后台</h1>
      <div class="header-right">
        <span v-if="adminStore.adminInfo" class="username">{{ adminStore.adminInfo.username }}</span>
        <button class="logout-btn" @click="handleLogout">退出登录</button>
      </div>
    </header>
    
    <div class="admin-container">
      <!-- 左侧导航菜单 -->
      <aside class="admin-sidebar" :class="{ 'collapsed': isCollapsed }">
        <div class="collapse-btn" @click="toggleCollapse">
          <i class="collapse-icon">{{ isCollapsed ? '>' : '<' }}</i>
        </div>
        
        <nav class="sidebar-menu">
          <!-- 普通菜单项 -->
          <router-link 
            v-for="menu in menuItems.filter(item => !item.children)" 
            :key="menu.path" 
            :to="menu.path" 
            class="menu-item"
            :class="{ 'active': currentPath === menu.path }"
          >
            <span class="menu-icon">{{ menu.icon }}</span>
            <span class="menu-title" v-show="!isCollapsed">{{ menu.title }}</span>
          </router-link>
          
          <!-- 带子菜单的菜单项 -->
          <div 
            v-for="menu in menuItems.filter(item => item.children)" 
            :key="menu.title" 
            class="submenu-container"
          >
            <!-- 父菜单项 -->
            <div 
              class="menu-item parent-menu" 
              :class="{ 'active': isSubmenuActive(menu) }"
              @click="toggleSubmenu(menu)"
            >
              <span class="menu-icon">{{ menu.icon }}</span>
              <span class="menu-title" v-show="!isCollapsed">{{ menu.title }}</span>
              <span class="submenu-arrow" v-show="!isCollapsed">{{ expandedMenus.includes(menu.title) ? '▼' : '▶' }}</span>
            </div>
            
            <!-- 子菜单 -->
            <div 
              class="submenu" 
              v-show="!isCollapsed && expandedMenus.includes(menu.title)"
            >
              <router-link 
                v-for="subItem in menu.children" 
                :key="subItem.path" 
                :to="subItem.path" 
                class="submenu-item"
                :class="{ 'active': currentPath === subItem.path }"
              >
                <span class="submenu-icon">{{ subItem.icon }}</span>
                <span class="submenu-title">{{ subItem.title }}</span>
              </router-link>
            </div>
          </div>
        </nav>
      </aside>
      
      <!-- 右侧内容区域 -->
      <main class="admin-content">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAdminStore } from '../stores/admin'
import { adminLogout } from '../api/auth'

const router = useRouter()
const route = useRoute()
const adminStore = useAdminStore()
const isCollapsed = ref(false)
const expandedMenus = ref([])

// 计算当前路径
const currentPath = computed(() => route.path)

// 菜单项配置
const menuItems = [
  { title: '数据统计', path: '/admin/dashboard', icon: '📊' },
  { title: '用户管理', path: '/admin/users', icon: '👥' },
  {
    title: '商品管理',
    icon: '📦',
    children: [
      { title: '商品列表', path: '/admin/products', icon: '📋' }
    ]
  },
  {
    title: '商品审核',
    icon: '✅',
    children: [
      { title: '自动审核查看', path: '/admin/audit/auto', icon: '🤖' },
      { title: '人工审核', path: '/admin/audit/manual', icon: '👨‍💼' },
      { title: '二度复审', path: '/admin/audit/second-review', icon: '🔍' },
      { title: '测试页面', path: '/admin/audit/test', icon: '🧪' }
    ]
  },
  { title: '商品分类管理', path: '/admin/categories', icon: '📑' },
  { title: '订单管理', path: '/admin/orders', icon: '📝' },
  { title: '退款管理', path: '/admin/refunds', icon: '💰' },
  { title: '验货管理', path: '/admin/verifications', icon: '🔍' },
  { title: '用户反馈', path: '/admin/feedback', icon: '📣' },
  { title: '论坛管理', path: '/admin/forum', icon: '💬' },
  { title: '论坛分类管理', path: '/admin/forum-categories', icon: '📋' },
  {
    title: '系统设置',
    icon: '⚙️',
    children: [
      { title: '管理员管理', path: '/admin/admins', icon: '👤' },
      { title: '定时任务', path: '/admin/scheduled-tasks', icon: '⏰' },
      { title: '同步ES', path: '/admin/sync-es', icon: '🔄' },
      { title: '知识库管理', path: '/admin/knowledge', icon: '🧠' }
    ]
  }
]

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  
  // 如果折叠了侧边栏，关闭所有展开的子菜单
  if (isCollapsed.value) {
    expandedMenus.value = []
  }
}

// 切换子菜单展开/折叠状态
const toggleSubmenu = (menu) => {
  const index = expandedMenus.value.indexOf(menu.title)
  if (index === -1) {
    expandedMenus.value.push(menu.title)
  } else {
    expandedMenus.value.splice(index, 1)
  }
}

// 判断子菜单是否处于活动状态
const isSubmenuActive = (menu) => {
  if (!menu.children) return false
  return menu.children.some(subItem => subItem.path === currentPath.value)
}

onMounted(async () => {
  // 获取管理员信息
  if (!adminStore.adminInfo) {
    try {
      await adminStore.fetchAdminInfo()
    } catch (error) {
      console.error('获取管理员信息失败:', error)
      router.push('/admin/login')
    }
  }
  
  // 自动展开当前活动的子菜单
  menuItems.forEach(menu => {
    if (menu.children && menu.children.some(subItem => subItem.path === currentPath.value)) {
      expandedMenus.value.push(menu.title)
    }
  })
})

const handleLogout = async () => {
  try {
    await adminLogout()
  } catch (error) {
    console.error('退出登录失败:', error)
  } finally {
    adminStore.logout()
    router.push('/admin/login')
  }
}
</script>

<style scoped>
.admin-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f0f2f5;
}

/* 顶部导航栏样式 */
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  background-color: #001529;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.logo {
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
}

.username {
  margin-right: 15px;
  font-size: 14px;
}

.logout-btn {
  padding: 5px 15px;
  background-color: transparent;
  border: 1px solid #fff;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 主容器样式 */
.admin-container {
  display: flex;
  flex: 1;
  height: calc(100vh - 64px);
}

/* 侧边栏样式 */
.admin-sidebar {
  width: 200px;
  background-color: #001529;
  color: white;
  transition: width 0.3s;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.admin-sidebar.collapsed {
  width: 80px;
}

.collapse-btn {
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 1px solid #002140;
  color: #aaa;
}

.collapse-icon {
  font-style: normal;
}

.sidebar-menu {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #ffffffa6;
  text-decoration: none;
  transition: all 0.3s;
  white-space: nowrap;
  cursor: pointer;
}

.menu-item:hover {
  color: #fff;
  background-color: #1890ff;
}

.menu-item.active {
  color: #fff;
  background-color: #1890ff;
}

.menu-icon {
  margin-right: 10px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.parent-menu {
  position: relative;
}

.submenu-arrow {
  position: absolute;
  right: 16px;
  font-size: 12px;
}

.submenu {
  background-color: #000c17;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px 10px 40px;
  color: #ffffffa6;
  text-decoration: none;
  transition: all 0.3s;
  white-space: nowrap;
}

.submenu-item:hover {
  color: #fff;
  background-color: #1890ff;
}

.submenu-item.active {
  color: #fff;
  background-color: #1890ff;
}

.submenu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* 内容区域样式 */
.admin-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    z-index: 5;
    height: calc(100vh - 64px);
    transform: translateX(0);
    transition: transform 0.3s;
  }
  
  .admin-sidebar.collapsed {
    transform: translateX(-100%);
    width: 200px;
  }
  
  .admin-content {
    margin-left: 0;
  }
}
</style> 