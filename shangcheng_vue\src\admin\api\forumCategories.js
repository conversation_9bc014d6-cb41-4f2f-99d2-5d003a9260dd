import request from '@/utils/request'

const BASE_URL = '/forum/categories'

/**
 * 获取所有论坛分类
 * @returns {Promise} - 返回论坛分类列表
 */
export function getAllCategories() {
  return request({
    url: BASE_URL,
    method: 'get'
  })
}

/**
 * 根据ID获取论坛分类详情
 * @param {number} id - 论坛分类ID
 * @returns {Promise} - 返回论坛分类详情
 */
export function getCategoryById(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'get'
  })
}

/**
 * 创建论坛分类
 * @param {Object} data - 分类数据
 * @param {string} data.categoryName - 分类名称
 * @param {string} data.description - 分类描述
 * @returns {Promise} - 返回创建结果
 */
export function createCategory(data) {
  return request({
    url: BASE_URL,
    method: 'post',
    data
  })
}

/**
 * 更新论坛分类
 * @param {number} id - 论坛分类ID
 * @param {Object} data - 分类数据
 * @param {string} data.categoryName - 分类名称
 * @param {string} data.description - 分类描述
 * @returns {Promise} - 返回更新结果
 */
export function updateCategory(id, data) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除论坛分类
 * @param {number} id - 论坛分类ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteCategory(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'delete'
  })
} 