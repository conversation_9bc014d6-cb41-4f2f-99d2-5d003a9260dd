package io.github.qifan777.knowledge.ai.messge;

import io.github.qifan777.knowledge.ai.session.AiSession;
import io.github.qifan777.knowledge.ai.session.AiSessionTable;
import io.github.qifan777.knowledge.infrastructure.jimmer.BaseEntityProps;
import io.github.qifan777.knowledge.user.User;
import java.lang.String;
import java.time.LocalDateTime;
import java.util.List;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;
import org.springframework.ai.chat.messages.MessageType;

@GeneratedBy(
        type = AiMessage.class
)
@PropsFor(AiMessage.class)
public interface AiMessageProps extends BaseEntityProps {
    TypedProp.Scalar<AiMessage, String> ID = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("id"));

    TypedProp.Scalar<AiMessage, LocalDateTime> CREATED_TIME = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("createdTime"));

    TypedProp.Scalar<AiMessage, LocalDateTime> EDITED_TIME = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("editedTime"));

    TypedProp.Reference<AiMessage, User> EDITOR = 
        TypedProp.reference(ImmutableType.get(AiMessage.class).getProp("editor"));

    TypedProp.Reference<AiMessage, User> CREATOR = 
        TypedProp.reference(ImmutableType.get(AiMessage.class).getProp("creator"));

    TypedProp.Scalar<AiMessage, MessageType> TYPE = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("type"));

    TypedProp.Scalar<AiMessage, String> TEXT_CONTENT = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("textContent"));

    TypedProp.Scalar<AiMessage, List<AiMessage.Media>> MEDIAS = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("medias"));

    TypedProp.Scalar<AiMessage, String> SESSION_ID = 
        TypedProp.scalar(ImmutableType.get(AiMessage.class).getProp("sessionId"));

    TypedProp.Reference<AiMessage, AiSession> SESSION = 
        TypedProp.reference(ImmutableType.get(AiMessage.class).getProp("session"));

    PropExpression.Cmp<MessageType> type();

    PropExpression.Str textContent();

    PropExpression<List<AiMessage.Media>> medias();

    AiSessionTable session();

    AiSessionTable session(JoinType joinType);

    PropExpression.Str sessionId();
}
