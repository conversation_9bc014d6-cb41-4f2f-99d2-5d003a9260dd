<template>
  <div class="success-overlay" v-if="visible" @click.self="close">
    <div class="success-dialog">
      <div class="success-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <h3>反馈提交成功</h3>
      <p>感谢您的反馈，我们会尽快处理！</p>
      <button class="btn btn-primary" @click="close">确定</button>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';

const props = defineProps({
  visible: {
    type: <PERSON>olean,
    default: false
  }
});

const emit = defineEmits(['close']);

// 关闭提示
const close = () => {
  emit('close');
};

// 监听ESC键关闭弹窗
const handleEscKey = (event) => {
  if (event.key === 'Escape' && props.visible) {
    close();
  }
};

// 挂载和卸载ESC键监听
onMounted(() => {
  document.addEventListener('keydown', handleEscKey);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey);
});
</script>

<style scoped>
.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.success-dialog {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 360px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 30px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  color: #67c23a;
  margin-bottom: 16px;
}

h3 {
  margin: 0 0 12px;
  font-size: 20px;
  font-weight: 600;
}

p {
  margin: 0 0 24px;
  color: #606266;
}

.btn {
  padding: 10px 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: #409eff;
  color: white;
}

.btn-primary:hover {
  background-color: #66b1ff;
}
</style> 