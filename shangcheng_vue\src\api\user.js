import request from '@/utils/request';

// 用户登录
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  });
}

// 用户注册
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/users/me',
    method: 'get'
  });
}

// 修改用户信息
export function updateUserInfo(data) {
  return request({
    url: '/users/me',
    method: 'put',
    data
  });
}

// 上传用户头像
export function updateAvatar(formData) {
  return request({
    url: '/users/me/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 退出登录
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  });
}

// 获取卖家信息
export function getSellerInfo(sellerId) {
  return request({
    url: `/users/seller/${sellerId}`,
    method: 'get'
  });
}

// 获取卖家商品列表
export function getSellerProducts(sellerId, params) {
  return request({
    url: `/products/seller/${sellerId}`,
    method: 'get',
    params
  });
}

// 绑定手机号
export function bindPhone(data) {
  return request({
    url: '/auth/bind-phone',
    method: 'post',
    data
  });
}

// 短信验证码登录
export function smsLogin(data) {
  return request({
    url: '/auth/sms-login',
    method: 'post',
    data
  });
}