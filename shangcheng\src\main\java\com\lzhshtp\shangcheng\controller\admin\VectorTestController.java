package com.lzhshtp.shangcheng.controller.admin;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 向量数据库测试控制器
 * 用于测试向量数据库是否正常工作
 */
@RestController
@RequestMapping("/api/admin/vector-test")
@RequiredArgsConstructor
@Slf4j
public class VectorTestController {

    private final VectorStore vectorStore;

    /**
     * 测试添加文档
     */
    @PostMapping("/add")
    public ApiResponse<String> addTestDocument(@RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            if (content == null || content.trim().isEmpty()) {
                return ApiResponse.fail("内容不能为空");
            }

            // 创建测试文档
            Document document = new Document(content);
            document.getMetadata().put("type", "test");
            document.getMetadata().put("timestamp", System.currentTimeMillis());

            // 添加到向量数据库
            vectorStore.add(List.of(document));

            log.info("测试文档添加成功: {}", content.substring(0, Math.min(50, content.length())));
            return ApiResponse.success("测试文档添加成功");

        } catch (Exception e) {
            log.error("添加测试文档失败", e);
            return ApiResponse.fail("添加失败: " + e.getMessage());
        }
    }

    /**
     * 测试搜索文档
     */
    @PostMapping("/search")
    public ApiResponse<List<Document>> searchTestDocuments(@RequestBody Map<String, Object> request) {
        try {
            String query = (String) request.get("query");

            if (query == null || query.trim().isEmpty()) {
                return ApiResponse.fail("查询内容不能为空");
            }

            // 执行相似度搜索（完全模仿qifancode）
            List<Document> results = vectorStore.similaritySearch(query);

            log.info("搜索完成，查询: {}, 返回 {} 个结果", query, results.size());
            return ApiResponse.success(results);

        } catch (Exception e) {
            log.error("搜索失败", e);
            return ApiResponse.fail("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 检查向量数据库连接状态
     */
    @GetMapping("/status")
    public ApiResponse<String> checkStatus() {
        try {
            // 尝试执行一个简单的搜索来测试连接
            vectorStore.similaritySearch("test");
            return ApiResponse.success("向量数据库连接正常");
        } catch (Exception e) {
            log.error("向量数据库连接异常", e);
            return ApiResponse.fail("向量数据库连接异常: " + e.getMessage());
        }
    }
}
