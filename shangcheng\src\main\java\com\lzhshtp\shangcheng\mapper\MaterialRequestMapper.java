package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 材料请求Mapper
 */
@Mapper
public interface MaterialRequestMapper extends BaseMapper<MaterialRequest> {
    
    /**
     * 根据商品ID查询材料请求
     */
    @Select("SELECT " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_requester_type AS requesterType, " +
            "lzhshtp_requester_task_id AS requesterTaskId, " +
            "lzhshtp_admin_id AS adminId, " +
            "lzhshtp_required_materials AS requiredMaterials, " +
            "lzhshtp_request_reason AS requestReason, " +
            "lzhshtp_status AS status, " +
            "lzhshtp_request_time AS requestTime, " +
            "lzhshtp_deadline AS deadline, " +
            "lzhshtp_seller_notified AS sellerNotified " +
            "FROM tb_lzhshtp_material_requests " +
            "WHERE lzhshtp_product_id = #{productId} " +
            "ORDER BY lzhshtp_request_time DESC")
    List<MaterialRequest> findByProductId(@Param("productId") Long productId);
    
    /**
     * 根据请求来源查询材料请求
     */
    @Select("SELECT " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_requester_type AS requesterType, " +
            "lzhshtp_requester_task_id AS requesterTaskId, " +
            "lzhshtp_admin_id AS adminId, " +
            "lzhshtp_required_materials AS requiredMaterials, " +
            "lzhshtp_request_reason AS requestReason, " +
            "lzhshtp_status AS status, " +
            "lzhshtp_request_time AS requestTime, " +
            "lzhshtp_deadline AS deadline, " +
            "lzhshtp_seller_notified AS sellerNotified " +
            "FROM tb_lzhshtp_material_requests " +
            "WHERE lzhshtp_requester_type = #{requesterType} AND lzhshtp_requester_task_id = #{taskId}")
    MaterialRequest findByRequesterTask(@Param("requesterType") String requesterType,
                                       @Param("taskId") Long taskId);
    
    /**
     * 查询待处理的材料请求
     */
    @Select("SELECT " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_requester_type AS requesterType, " +
            "lzhshtp_requester_task_id AS requesterTaskId, " +
            "lzhshtp_admin_id AS adminId, " +
            "lzhshtp_required_materials AS requiredMaterials, " +
            "lzhshtp_request_reason AS requestReason, " +
            "lzhshtp_status AS status, " +
            "lzhshtp_request_time AS requestTime, " +
            "lzhshtp_deadline AS deadline, " +
            "lzhshtp_seller_notified AS sellerNotified " +
            "FROM tb_lzhshtp_material_requests " +
            "WHERE lzhshtp_status = 'waiting' " +
            "ORDER BY lzhshtp_request_time ASC")
    List<MaterialRequest> findPendingRequests();
    
    /**
     * 根据状态统计数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_material_requests WHERE lzhshtp_status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 根据商品ID列表查询材料请求
     */
    @Select("<script>" +
            "SELECT " +
            "lzhshtp_request_id AS requestId, " +
            "lzhshtp_product_id AS productId, " +
            "lzhshtp_requester_type AS requesterType, " +
            "lzhshtp_requester_task_id AS requesterTaskId, " +
            "lzhshtp_admin_id AS adminId, " +
            "lzhshtp_required_materials AS requiredMaterials, " +
            "lzhshtp_request_reason AS requestReason, " +
            "lzhshtp_status AS status, " +
            "lzhshtp_request_time AS requestTime, " +
            "lzhshtp_deadline AS deadline, " +
            "lzhshtp_seller_notified AS sellerNotified " +
            "FROM tb_lzhshtp_material_requests " +
            "WHERE lzhshtp_product_id IN " +
            "<foreach collection='productIds' item='productId' open='(' separator=',' close=')'>" +
            "#{productId}" +
            "</foreach>" +
            "ORDER BY lzhshtp_request_time DESC" +
            "</script>")
    List<MaterialRequest> findByProductIds(@Param("productIds") List<Long> productIds);
    
    /**
     * 更新通知状态
     */
    @Update("UPDATE tb_lzhshtp_material_requests " +
            "SET lzhshtp_seller_notified = #{notified} " +
            "WHERE lzhshtp_request_id = #{requestId}")
    int updateNotificationStatus(@Param("requestId") Long requestId, 
                                @Param("notified") Boolean notified);
}
