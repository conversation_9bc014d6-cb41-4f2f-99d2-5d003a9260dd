--
-- 二手交易平台数据库 Schema
-- Version: 1.0
-- Generated by AI Assistant
--
-- 使用MySQL语法，如果使用其他数据库（如PostgreSQL），可能需要微调数据类型和关键字。
--

-- 1. 用户表 (tb_lzhshtp_users)
CREATE TABLE tb_lzhshtp_users (
    lzhshtp_user_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识ID，自增主键',
    lzhshtp_username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名，用于登录和展示，必须唯一',
    lzhshtp_password_hash VARCHAR(255) NOT NULL COMMENT '存储加密后的密码哈希值，不存储明文密码',
    lzhshtp_email VARCHAR(100) NOT NULL UNIQUE COMMENT '用户邮箱地址，必须唯一，用于登录和接收通知',
    lzhshtp_phone_number VARCHAR(20) UNIQUE COMMENT '用户手机号码，必须唯一，用于登录和联系',
    lzhshtp_avatar_url VARCHAR(255) COMMENT '用户头像的URL地址',
    lzhshtp_registration_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '用户注册时间，默认为当前时间戳',
    lzhshtp_last_login_date DATETIME COMMENT '用户最后一次登录时间',
    lzhshtp_credit_score INT NOT NULL DEFAULT 100 COMMENT '用户信用评分，初始值100，根据行为动态调整',
    lzhshtp_bio TEXT COMMENT '用户的自我介绍或个性签名',
    lzhshtp_location VARCHAR(100) COMMENT '用户所在地区信息，便于同城交易',
    lzhshtp_is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '账户状态，TRUE表示正常，FALSE表示被禁用',
    lzhshtp_role ENUM('admin', 'general_user', 'ai_customer_service') NOT NULL DEFAULT 'general_user' COMMENT '用户角色：管理员、普通用户、AI客服'
);

-- 2. 收货地址表 (tb_lzhshtp_shipping_addresses)
CREATE TABLE tb_lzhshtp_shipping_addresses (
    lzhshtp_address_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收货地址唯一标识ID，自增主键',
    lzhshtp_user_id BIGINT NOT NULL COMMENT '地址所属的用户ID',
    lzhshtp_recipient_name VARCHAR(100) NOT NULL COMMENT '收货人姓名',
    lzhshtp_phone_number VARCHAR(20) NOT NULL COMMENT '收货人联系电话',
    lzhshtp_province VARCHAR(50) NOT NULL COMMENT '省份名称',
    lzhshtp_city VARCHAR(50) NOT NULL COMMENT '城市名称',
    lzhshtp_district VARCHAR(50) COMMENT '区/县名称',
    lzhshtp_street_address TEXT NOT NULL COMMENT '详细街道地址信息',
    lzhshtp_postal_code VARCHAR(10) COMMENT '邮政编码',
    lzhshtp_is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认收货地址',
    lzhshtp_created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '地址创建时间',
    lzhshtp_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '地址最后更新时间'
);

-- 3. 商品分类表 (tb_lzhshtp_product_categories)
CREATE TABLE tb_lzhshtp_product_categories (
    lzhshtp_category_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品分类唯一标识ID，自增主键',
    lzhshtp_category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称，如电子产品、图书、服装等',
    lzhshtp_description TEXT COMMENT '分类的详细描述',
    lzhshtp_parent_category_id INT COMMENT '父级分类ID，用于实现多级分类结构，NULL表示顶级分类'
);

-- 4. 商品表 (tb_lzhshtp_products)
CREATE TABLE tb_lzhshtp_products (
    lzhshtp_product_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '商品唯一标识ID，自增主键',
    lzhshtp_title VARCHAR(255) NOT NULL COMMENT '商品标题，简要描述商品',
    lzhshtp_description TEXT NOT NULL COMMENT '商品的详细描述信息',
    lzhshtp_price DECIMAL(10, 2) NOT NULL COMMENT '商品售价，精确到分',
    lzhshtp_category_id INT NOT NULL COMMENT '商品所属分类的ID',
    `lzhshtp_condition` VARCHAR(50) COMMENT '商品新旧程度描述，如全新、九成新等',
    lzhshtp_location VARCHAR(100) COMMENT '商品所在地，便于同城交易',
    lzhshtp_delivery_method VARCHAR(50) COMMENT '交易方式，如快递、同城面交等',
    lzhshtp_image_urls TEXT COMMENT '商品图片URL列表，多个URL用逗号分隔或JSON数组形式存储',
    lzhshtp_posted_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '商品发布时间',
    lzhshtp_status ENUM('available', 'sold', 'pending_review', 'off_shelf_by_seller', 'off_shelf_by_admin', 'deleted') NOT NULL DEFAULT 'available' COMMENT '商品状态：可购买、已售出、待审核、卖家下架、管理员下架、已删除',
    lzhshtp_seller_id BIGINT NOT NULL COMMENT '发布商品的卖家用户ID'
);

-- 5. 订单表 (tb_lzhshtp_orders)
CREATE TABLE tb_lzhshtp_orders (
    lzhshtp_order_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单唯一标识ID，自增主键',
    lzhshtp_product_id BIGINT NOT NULL COMMENT '订单关联的商品ID',
    lzhshtp_buyer_id BIGINT NOT NULL COMMENT '购买者用户ID',
    lzhshtp_seller_id BIGINT NOT NULL COMMENT '卖家用户ID',
    lzhshtp_order_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    lzhshtp_total_amount DECIMAL(10, 2) NOT NULL COMMENT '订单总金额，精确到分',
    lzhshtp_status ENUM('pending_payment', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded') NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态：待付款、已付款、已发货、已送达、已完成、已取消、已退款',
    lzhshtp_shipping_address VARCHAR(500) NOT NULL COMMENT '收货地址信息，包含收货人、电话、详细地址等',
    lzhshtp_payment_method VARCHAR(50) COMMENT '支付方式，如支付宝、微信支付等',
    lzhshtp_transaction_id VARCHAR(255) COMMENT '支付平台返回的交易流水号'
);

-- 6. 商品评价表 (tb_lzhshtp_product_reviews)
CREATE TABLE tb_lzhshtp_product_reviews (
    lzhshtp_review_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评价唯一标识ID，自增主键',
    lzhshtp_product_id BIGINT NOT NULL COMMENT '被评价的商品ID',
    lzhshtp_reviewer_id BIGINT NOT NULL COMMENT '发表评价的用户ID',
    lzhshtp_seller_id BIGINT NOT NULL COMMENT '商品卖家的用户ID，冗余字段便于查询',
    lzhshtp_rating INT NOT NULL COMMENT '商品评分，1-5星',
    lzhshtp_comment TEXT COMMENT '评价详细内容',
    lzhshtp_review_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评价发布时间'
);

-- 7. 用户互评表 (tb_lzhshtp_user_ratings)
CREATE TABLE tb_lzhshtp_user_ratings (
    lzhshtp_rating_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户互评唯一标识ID，自增主键',
    lzhshtp_order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    lzhshtp_rater_id BIGINT NOT NULL COMMENT '评价发起人用户ID',
    lzhshtp_rated_user_id BIGINT NOT NULL COMMENT '被评价用户ID',
    lzhshtp_role_at_transaction ENUM('buyer', 'seller') NOT NULL COMMENT '被评价用户在交易中的角色：买家或卖家',
    lzhshtp_rating INT NOT NULL COMMENT '评分，1-5星',
    lzhshtp_comment TEXT COMMENT '评价详细内容',
    lzhshtp_rating_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评价发布时间',
    UNIQUE (lzhshtp_order_id, lzhshtp_rater_id, lzhshtp_rated_user_id) COMMENT '确保一笔订单中，一个用户对另一个用户只能评价一次'
);

-- 8. 消息表 (tb_lzhshtp_messages)
CREATE TABLE tb_lzhshtp_messages (
    lzhshtp_message_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息唯一标识ID，自增主键',
    lzhshtp_sender_id BIGINT NOT NULL COMMENT '消息发送者用户ID',
    lzhshtp_receiver_id BIGINT NOT NULL COMMENT '消息接收者用户ID',
    lzhshtp_content TEXT NOT NULL COMMENT '消息内容',
    lzhshtp_sent_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '消息发送时间',
    lzhshtp_is_read BOOLEAN NOT NULL DEFAULT FALSE COMMENT '消息是否已读',
    lzhshtp_is_system_message BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为系统消息，如AI客服或平台通知'
);

-- 9. 论坛分类表 (tb_lzhshtp_forum_categories)
CREATE TABLE tb_lzhshtp_forum_categories (
    lzhshtp_forum_category_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '论坛分类唯一标识ID，自增主键',
    lzhshtp_category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称，如二手讨论、技术交流等',
    lzhshtp_description TEXT COMMENT '分类的详细描述'
);

-- 10. 论坛帖子表 (tb_lzhshtp_forum_posts)
CREATE TABLE tb_lzhshtp_forum_posts (
    lzhshtp_post_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '帖子唯一标识ID，自增主键',
    lzhshtp_title VARCHAR(255) NOT NULL COMMENT '帖子标题',
    lzhshtp_content TEXT NOT NULL COMMENT '帖子正文内容',
    lzhshtp_author_id BIGINT NOT NULL COMMENT '帖子作者用户ID',
    lzhshtp_posted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '帖子发布时间',
    lzhshtp_forum_category_id INT NOT NULL COMMENT '帖子所属的论坛分类ID',
    lzhshtp_views_count INT NOT NULL DEFAULT 0 COMMENT '帖子浏览次数'
);

-- 11. 论坛评论表 (tb_lzhshtp_forum_comments)
CREATE TABLE tb_lzhshtp_forum_comments (
    lzhshtp_comment_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评论唯一标识ID，自增主键',
    lzhshtp_post_id BIGINT NOT NULL COMMENT '评论所属的帖子ID',
    lzhshtp_author_id BIGINT NOT NULL COMMENT '评论作者用户ID',
    lzhshtp_content TEXT NOT NULL COMMENT '评论内容',
    lzhshtp_commented_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评论发布时间',
    lzhshtp_parent_comment_id BIGINT COMMENT '父评论ID，用于实现评论回复功能，NULL表示顶级评论'
);

-- 12. 用户收藏表 (tb_lzhshtp_user_favorites)
CREATE TABLE tb_lzhshtp_user_favorites (
    lzhshtp_favorite_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏记录唯一标识ID，自增主键',
    lzhshtp_user_id BIGINT NOT NULL COMMENT '执行收藏操作的用户ID',
    lzhshtp_product_id BIGINT NOT NULL COMMENT '被收藏的商品ID',
    lzhshtp_favorited_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    UNIQUE (lzhshtp_user_id, lzhshtp_product_id) COMMENT '确保用户不会重复收藏同一商品'
);

-- 13. 用户反馈/举报表 (tb_lzhshtp_user_feedback)
CREATE TABLE tb_lzhshtp_user_feedback (
    lzhshtp_feedback_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '反馈记录唯一标识ID，自增主键',
    lzhshtp_reporter_id BIGINT NOT NULL COMMENT '提交反馈的用户ID',
    lzhshtp_feedback_type ENUM('bug_report', 'suggestion', 'complaint', 'abuse_report') NOT NULL COMMENT '反馈类型：程序漏洞、建议、投诉、举报',
    lzhshtp_related_entity_type VARCHAR(50) COMMENT '相关实体类型，如商品、用户等',
    lzhshtp_related_entity_id BIGINT COMMENT '相关实体ID',
    lzhshtp_content TEXT NOT NULL COMMENT '反馈或举报的详细内容',
    lzhshtp_submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    lzhshtp_status ENUM('pending', 'in_progress', 'resolved', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '处理状态：待处理、处理中、已解决、已驳回',
    lzhshtp_admin_notes TEXT COMMENT '管理员处理备注',
    lzhshtp_resolved_by_admin_id BIGINT COMMENT '处理该反馈的管理员用户ID'
);

-- 14. 商品审核日志表 (tb_lzhshtp_product_audit_logs)
CREATE TABLE tb_lzhshtp_product_audit_logs (
    lzhshtp_log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '审核日志唯一标识ID，自增主键',
    lzhshtp_product_id BIGINT NOT NULL COMMENT '被审核的商品ID',
    lzhshtp_auditor_id BIGINT COMMENT '执行审核的管理员ID，NULL表示系统自动操作',
    lzhshtp_action_type ENUM('submitted_for_review', 'approved', 'rejected', 'relisted_by_admin', 'off_shelved_by_admin', 'info_updated_by_admin') NOT NULL COMMENT '审核操作类型：提交审核、通过、拒绝、管理员重新上架、管理员下架、管理员更新信息',
    lzhshtp_notes TEXT COMMENT '审核备注，如拒绝原因、修改说明等',
    lzhshtp_audit_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '审核操作时间'
);

-- 15. 用户关注表 (tb_lzhshtp_user_follows)
CREATE TABLE tb_lzhshtp_user_follows (
    lzhshtp_follow_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关注记录唯一标识ID，自增主键',
    lzhshtp_follower_id BIGINT NOT NULL COMMENT '关注者（粉丝）用户ID',
    lzhshtp_following_id BIGINT NOT NULL COMMENT '被关注者用户ID',
    lzhshtp_followed_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间'
); 