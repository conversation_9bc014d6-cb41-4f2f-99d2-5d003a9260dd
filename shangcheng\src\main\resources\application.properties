spring.application.name=shangcheng
spring.datasource.url=**********************************************************************************************
spring.datasource.username=root
spring.datasource.password=1234
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis-Plus配置
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.lzhshtp.shangcheng.model
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.id-type=auto
mybatis-plus.global-config.db-config.table-prefix=tb_lzhshtp_

# JWT配置
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=123456
spring.data.redis.timeout=10000
spring.data.redis.jedis.pool.max-active=8
spring.data.redis.jedis.pool.max-idle=8
spring.data.redis.jedis.pool.min-idle=0
spring.data.redis.jedis.pool.max-wait=-1

# 服务器配置
server.port=8080
server.servlet.context-path=/

# 日志配置
logging.level.com.lzhshtp.shangcheng=debug

# ElasticSearch配置
spring.elasticsearch.uris=http://localhost:9200
spring.elasticsearch.username=
spring.elasticsearch.password=
spring.elasticsearch.connection-timeout=10s
spring.elasticsearch.socket-timeout=30s
