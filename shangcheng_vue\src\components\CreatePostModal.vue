<template>
  <transition name="modal-fade">
    <div class="modal-overlay" v-if="show" @click.self="close">
      <div class="modal-container">
        <div class="modal-header">
          <h3>发表新帖子</h3>
          <button class="close-btn" @click="close">&times;</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitPost">
            <div class="form-group">
              <label for="post-title">帖子标题</label>
              <input
                type="text"
                id="post-title"
                v-model.trim="post.title"
                placeholder="请输入标题"
                required
              />
              <small class="error-text" v-if="errors.title">{{ errors.title }}</small>
            </div>
            <div class="form-group">
              <label for="post-category">选择分类</label>
              <select id="post-category" v-model="post.forumCategoryId" required>
                <option disabled value="">请选择一个分类</option>
                <option
                  v-for="category in categories"
                  :key="category.forumCategoryId"
                  :value="category.forumCategoryId"
                >
                  {{ category.categoryName }}
                </option>
              </select>
              <small class="error-text" v-if="errors.category">{{ errors.category }}</small>
            </div>
            <div class="form-group">
              <label for="post-content">帖子内容</label>
              <textarea
                id="post-content"
                rows="8"
                v-model.trim="post.content"
                placeholder="请输入帖子内容..."
                required
              ></textarea>
              <small class="error-text" v-if="errors.content">{{ errors.content }}</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="close">取消</button>
          <button class="submit-btn" @click="submitPost" :disabled="loading">
            <span v-if="loading" class="spinner-small"></span>
            {{ loading ? '发布中...' : '立即发布' }}
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { createPost } from '@/api/forum';

export default {
  name: 'CreatePostModal',
  props: {
    show: {
      type: Boolean,
      required: true,
    },
    categories: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      post: {
        title: '',
        content: '',
        forumCategoryId: '',
      },
      errors: {
        title: '',
        content: '',
        category: '',
      },
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        document.addEventListener('keydown', this.handleEsc);
      } else {
        document.removeEventListener('keydown', this.handleEsc);
      }
    }
  },
  methods: {
    close() {
      if (this.loading) return;
      this.resetForm();
      this.$emit('close');
    },
    handleEsc(e) {
      if (e.key === 'Escape') {
        this.close();
      }
    },
    validateForm() {
      this.errors = { title: '', content: '', category: '' };
      let isValid = true;
      if (!this.post.title) {
        this.errors.title = '标题不能为空';
        isValid = false;
      } else if (this.post.title.length > 100) {
        this.errors.title = '标题不能超过100个字符';
        isValid = false;
      }
      if (!this.post.content) {
        this.errors.content = '内容不能为空';
        isValid = false;
      }
      if (!this.post.forumCategoryId) {
        this.errors.category = '请选择一个分类';
        isValid = false;
      }
      return isValid;
    },
    async submitPost() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      try {
        const response = await createPost(this.post);
        if (response.success) {
          this.$emit('post-created');
          this.close();
        } else {
          // This part might not be reached if the API call throws an error on non-2xx responses.
          // The catch block is more likely to handle it.
          alert(`发帖失败: ${response.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('发帖异常:', error);
        // Check if the error response from the server is available
        if (error.response && error.response.data && error.response.data.message) {
          alert(`发帖失败: ${error.response.data.message}`);
        } else {
          alert('发帖失败，请稍后重试');
        }
      } finally {
        this.loading = false;
      }
    },
    resetForm() {
      this.post = {
        title: '',
        content: '',
        forumCategoryId: '',
      };
      this.errors = {
        title: '',
        content: '',
        category: '',
      };
    },
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.close-btn {
  border: none;
  background: transparent;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical;
}

.error-text {
  color: #e53935;
  font-size: 0.875rem;
  margin-top: 5px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 25px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-left: 10px;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.submit-btn {
  background-color: #e53935;
  color: white;
  display: flex;
  align-items: center;
}

.submit-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .modal-container,
.modal-fade-leave-active .modal-container {
  transition: transform 0.3s ease;
}
.modal-fade-enter-from .modal-container,
.modal-fade-leave-to .modal-container {
  transform: translateY(-30px);
}

/* Spinner for loading state in button */
.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style> 