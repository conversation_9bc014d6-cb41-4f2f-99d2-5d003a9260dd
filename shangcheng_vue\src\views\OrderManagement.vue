<template>
  <div class="order-management">
    <header class="header">
      <h1>订单管理</h1>
      <div class="nav-actions">
        <router-link to="/home">返回首页</router-link>
        <router-link to="/user">用户中心</router-link>
      </div>
    </header>

    <div class="tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="{ active: activeTab === tab.key }"
        @click="activeTab = tab.key"
      >
        {{ tab.label }}
      </button>
    </div>

    <div class="order-list">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="orders.length === 0" class="empty">暂无订单</div>
      <div v-else>
        <div v-for="order in orders" :key="order.orderId" class="order-item">
          <div class="order-header">
            <span class="order-id">订单号：{{ order.orderId }}</span>
            <span class="order-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </span>
          </div>
          
          <div class="order-content">
            <div class="product-info">
              <img :src="order.productImage" :alt="order.productTitle" class="product-image">
              <div class="product-details">
                <h3>{{ order.productTitle }}</h3>
                <p class="price">¥{{ order.totalAmount }}</p>
                <p class="order-date">下单时间：{{ formatDate(order.orderDate) }}</p>
              </div>
            </div>
            
            <div class="order-actions">
              <!-- 买家操作 -->
              <template v-if="activeTab === 'buyer'">
                <button 
                  v-if="order.status === 'pending_payment'" 
                  @click="payOrder(order.orderId)"
                  class="btn-primary"
                >
                  立即支付
                </button>
                <button 
                  v-if="order.status === 'delivered'" 
                  @click="completeOrder(order.orderId)"
                  class="btn-success"
                >
                  确认收货
                </button>
                <button 
                  v-if="canRefund(order.status)" 
                  @click="showRefundDialog(order)"
                  class="btn-warning"
                >
                  申请退款
                </button>
                <button 
                  v-if="order.officialVerification" 
                  @click="viewVerification(order.orderId)"
                  class="btn-info"
                >
                  查看验货
                </button>
              </template>
              
              <!-- 卖家操作 -->
              <template v-if="activeTab === 'seller'">
                <button 
                  v-if="order.status === 'paid'" 
                  @click="shipOrder(order.orderId)"
                  class="btn-primary"
                >
                  发货
                </button>
                <button 
                  v-if="order.canPublishProduct === false" 
                  @click="viewForcedRefunds()"
                  class="btn-danger"
                >
                  处理强制退款
                </button>
              </template>
            </div>
          </div>
          
          <!-- 验货信息 -->
          <div v-if="order.officialVerification" class="verification-info">
            <span class="verification-badge">✅ 官方验货</span>
            <span v-if="order.verificationPayer === 'buyer'" class="verification-fee">
              验货费：¥{{ order.verificationFee }}
            </span>
            <span v-else class="verification-free">免费验货</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 退款申请对话框 -->
    <div v-if="showRefund" class="modal-overlay" @click="closeRefundDialog">
      <div class="modal-content" @click.stop>
        <h3>申请退款</h3>
        <form @submit.prevent="submitRefund">
          <div class="form-group">
            <label>退款类型</label>
            <select v-model="refundForm.refundType" required>
              <option value="refund_only">仅退款</option>
              <option value="return_refund">退货退款</option>
            </select>
          </div>
          <div class="form-group">
            <label>退款金额</label>
            <input 
              type="number" 
              v-model="refundForm.refundAmount" 
              :max="selectedOrder.totalAmount"
              step="0.01"
              required
            >
          </div>
          <div class="form-group">
            <label>退款原因</label>
            <textarea 
              v-model="refundForm.refundReason" 
              rows="4" 
              required
              placeholder="请详细说明退款原因"
            ></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" :disabled="submitting" class="btn-primary">
              {{ submitting ? '提交中...' : '提交申请' }}
            </button>
            <button type="button" @click="closeRefundDialog" class="btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getBuyerOrders, getSellerOrders, payWithBalance, shipOrder, completeOrder } from '@/api/order';
import { createRefund } from '@/api/refund';
import { getVerificationByOrderId } from '@/api/verification';

const router = useRouter();
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('buyer');
const orders = ref([]);
const showRefund = ref(false);
const selectedOrder = ref(null);

const tabs = [
  { key: 'buyer', label: '我的购买' },
  { key: 'seller', label: '我的销售' }
];

const refundForm = reactive({
  refundType: 'refund_only',
  refundAmount: 0,
  refundReason: ''
});

// 加载订单列表
const loadOrders = async () => {
  loading.value = true;
  try {
    const response = activeTab.value === 'buyer' 
      ? await getBuyerOrders()
      : await getSellerOrders();
    orders.value = response.data || [];
  } catch (error) {
    console.error('加载订单失败:', error);
    alert('加载订单失败');
  } finally {
    loading.value = false;
  }
};

// 支付订单
const payOrder = async (orderId) => {
  try {
    await payWithBalance(orderId);
    alert('支付成功');
    loadOrders();
  } catch (error) {
    console.error('支付失败:', error);
    alert('支付失败：' + (error.response?.data?.message || error.message));
  }
};

// 发货
const shipOrder = async (orderId) => {
  try {
    await shipOrder(orderId);
    alert('发货成功');
    loadOrders();
  } catch (error) {
    console.error('发货失败:', error);
    alert('发货失败：' + (error.response?.data?.message || error.message));
  }
};

// 确认收货
const completeOrder = async (orderId) => {
  try {
    await completeOrder(orderId);
    alert('确认收货成功');
    loadOrders();
  } catch (error) {
    console.error('确认收货失败:', error);
    alert('确认收货失败：' + (error.response?.data?.message || error.message));
  }
};

// 显示退款对话框
const showRefundDialog = (order) => {
  selectedOrder.value = order;
  refundForm.refundAmount = order.totalAmount;
  refundForm.refundReason = '';
  showRefund.value = true;
};

// 关闭退款对话框
const closeRefundDialog = () => {
  showRefund.value = false;
  selectedOrder.value = null;
};

// 提交退款申请
const submitRefund = async () => {
  submitting.value = true;
  try {
    await createRefund({
      orderId: selectedOrder.value.orderId,
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.refundReason,
      refundType: refundForm.refundType
    });
    alert('退款申请提交成功');
    closeRefundDialog();
    loadOrders();
  } catch (error) {
    console.error('提交退款申请失败:', error);
    alert('提交退款申请失败：' + (error.response?.data?.message || error.message));
  } finally {
    submitting.value = false;
  }
};

// 查看验货信息
const viewVerification = async (orderId) => {
  try {
    const response = await getVerificationByOrderId(orderId);
    if (response.data) {
      router.push(`/verification/${orderId}`);
    } else {
      alert('该订单暂无验货记录');
    }
  } catch (error) {
    console.error('查看验货信息失败:', error);
    alert('查看验货信息失败');
  }
};

// 查看强制退款任务
const viewForcedRefunds = () => {
  router.push('/refund/forced-tasks');
};

// 判断是否可以退款
const canRefund = (status) => {
  return ['paid', 'shipped', 'delivered'].includes(status);
};

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    'pending_payment': 'status-pending',
    'paid': 'status-paid',
    'shipped': 'status-shipped',
    'delivered': 'status-delivered',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled',
    'refunded': 'status-refunded'
  };
  return statusMap[status] || '';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending_payment': '待支付',
    'paid': '已支付',
    'shipped': '已发货',
    'delivered': '已送达',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
};

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString();
};

// 监听标签切换
const handleTabChange = () => {
  loadOrders();
};

onMounted(() => {
  loadOrders();
});

// 监听activeTab变化
watch(activeTab, handleTabChange);
</script>

<style scoped>
.order-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.nav-actions a {
  margin-left: 10px;
  padding: 8px 15px;
  background-color: #2196F3;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tabs button {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tabs button.active {
  color: #2196F3;
  border-bottom-color: #2196F3;
}

.order-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.order-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending { background-color: #ff9800; color: white; }
.status-paid { background-color: #2196F3; color: white; }
.status-shipped { background-color: #9c27b0; color: white; }
.status-delivered { background-color: #4caf50; color: white; }
.status-completed { background-color: #8bc34a; color: white; }
.status-cancelled { background-color: #f44336; color: white; }
.status-refunded { background-color: #607d8b; color: white; }

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 15px;
}

.product-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.price {
  color: #f44336;
  font-weight: bold;
  font-size: 18px;
  margin: 5px 0;
}

.order-date {
  color: #666;
  font-size: 12px;
  margin: 0;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.verification-info {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 10px;
}

.verification-badge {
  background-color: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.verification-fee {
  color: #ff9800;
  font-size: 12px;
}

.verification-free {
  color: #4caf50;
  font-size: 12px;
}

/* 按钮样式 */
.btn-primary { background-color: #2196F3; color: white; }
.btn-success { background-color: #4caf50; color: white; }
.btn-warning { background-color: #ff9800; color: white; }
.btn-danger { background-color: #f44336; color: white; }
.btn-info { background-color: #00bcd4; color: white; }
.btn-secondary { background-color: #9e9e9e; color: white; }

button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style>
