{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/textile.js"], "sourcesContent": ["var TOKEN_STYLES = {\n  addition: \"inserted\",\n  attributes: \"propertyName\",\n  bold: \"strong\",\n  cite: \"keyword\",\n  code: \"monospace\",\n  definitionList: \"list\",\n  deletion: \"deleted\",\n  div: \"punctuation\",\n  em: \"emphasis\",\n  footnote: \"variable\",\n  footCite: \"qualifier\",\n  header: \"heading\",\n  html: \"comment\",\n  image: \"atom\",\n  italic: \"emphasis\",\n  link: \"link\",\n  linkDefinition: \"link\",\n  list1: \"list\",\n  list2: \"list.special\",\n  list3: \"list\",\n  notextile: \"string.special\",\n  pre: \"operator\",\n  p: \"content\",\n  quote: \"bracket\",\n  span: \"quote\",\n  specialChar: \"character\",\n  strong: \"strong\",\n  sub: \"content.special\",\n  sup: \"content.special\",\n  table: \"variableName.special\",\n  tableHeading: \"operator\"\n};\n\nfunction startNewLine(stream, state) {\n  state.mode = Modes.newLayout;\n  state.tableHeading = false;\n\n  if (state.layoutType === \"definitionList\" && state.spanningLayout &&\n      stream.match(RE(\"definitionListEnd\"), false))\n    state.spanningLayout = false;\n}\n\nfunction handlePhraseModifier(stream, state, ch) {\n  if (ch === \"_\") {\n    if (stream.eat(\"_\"))\n      return togglePhraseModifier(stream, state, \"italic\", /__/, 2);\n    else\n      return togglePhraseModifier(stream, state, \"em\", /_/, 1);\n  }\n\n  if (ch === \"*\") {\n    if (stream.eat(\"*\")) {\n      return togglePhraseModifier(stream, state, \"bold\", /\\*\\*/, 2);\n    }\n    return togglePhraseModifier(stream, state, \"strong\", /\\*/, 1);\n  }\n\n  if (ch === \"[\") {\n    if (stream.match(/\\d+\\]/)) state.footCite = true;\n    return tokenStyles(state);\n  }\n\n  if (ch === \"(\") {\n    var spec = stream.match(/^(r|tm|c)\\)/);\n    if (spec)\n      return TOKEN_STYLES.specialChar\n  }\n\n  if (ch === \"<\" && stream.match(/(\\w+)[^>]+>[^<]+<\\/\\1>/))\n    return TOKEN_STYLES.html\n\n  if (ch === \"?\" && stream.eat(\"?\"))\n    return togglePhraseModifier(stream, state, \"cite\", /\\?\\?/, 2);\n\n  if (ch === \"=\" && stream.eat(\"=\"))\n    return togglePhraseModifier(stream, state, \"notextile\", /==/, 2);\n\n  if (ch === \"-\" && !stream.eat(\"-\"))\n    return togglePhraseModifier(stream, state, \"deletion\", /-/, 1);\n\n  if (ch === \"+\")\n    return togglePhraseModifier(stream, state, \"addition\", /\\+/, 1);\n\n  if (ch === \"~\")\n    return togglePhraseModifier(stream, state, \"sub\", /~/, 1);\n\n  if (ch === \"^\")\n    return togglePhraseModifier(stream, state, \"sup\", /\\^/, 1);\n\n  if (ch === \"%\")\n    return togglePhraseModifier(stream, state, \"span\", /%/, 1);\n\n  if (ch === \"@\")\n    return togglePhraseModifier(stream, state, \"code\", /@/, 1);\n\n  if (ch === \"!\") {\n    var type = togglePhraseModifier(stream, state, \"image\", /(?:\\([^\\)]+\\))?!/, 1);\n    stream.match(/^:\\S+/); // optional Url portion\n    return type;\n  }\n  return tokenStyles(state);\n}\n\nfunction togglePhraseModifier(stream, state, phraseModifier, closeRE, openSize) {\n  var charBefore = stream.pos > openSize ? stream.string.charAt(stream.pos - openSize - 1) : null;\n  var charAfter = stream.peek();\n  if (state[phraseModifier]) {\n    if ((!charAfter || /\\W/.test(charAfter)) && charBefore && /\\S/.test(charBefore)) {\n      var type = tokenStyles(state);\n      state[phraseModifier] = false;\n      return type;\n    }\n  } else if ((!charBefore || /\\W/.test(charBefore)) && charAfter && /\\S/.test(charAfter) &&\n             stream.match(new RegExp(\"^.*\\\\S\" + closeRE.source + \"(?:\\\\W|$)\"), false)) {\n    state[phraseModifier] = true;\n    state.mode = Modes.attributes;\n  }\n  return tokenStyles(state);\n};\n\nfunction tokenStyles(state) {\n  var disabled = textileDisabled(state);\n  if (disabled) return disabled;\n\n  var styles = [];\n  if (state.layoutType) styles.push(TOKEN_STYLES[state.layoutType]);\n\n  styles = styles.concat(activeStyles(\n    state, \"addition\", \"bold\", \"cite\", \"code\", \"deletion\", \"em\", \"footCite\",\n    \"image\", \"italic\", \"link\", \"span\", \"strong\", \"sub\", \"sup\", \"table\", \"tableHeading\"));\n\n  if (state.layoutType === \"header\")\n    styles.push(TOKEN_STYLES.header + \"-\" + state.header);\n\n  return styles.length ? styles.join(\" \") : null;\n}\n\nfunction textileDisabled(state) {\n  var type = state.layoutType;\n\n  switch(type) {\n  case \"notextile\":\n  case \"code\":\n  case \"pre\":\n    return TOKEN_STYLES[type];\n  default:\n    if (state.notextile)\n      return TOKEN_STYLES.notextile + (type ? (\" \" + TOKEN_STYLES[type]) : \"\");\n    return null;\n  }\n}\n\nfunction activeStyles(state) {\n  var styles = [];\n  for (var i = 1; i < arguments.length; ++i) {\n    if (state[arguments[i]])\n      styles.push(TOKEN_STYLES[arguments[i]]);\n  }\n  return styles;\n}\n\nfunction blankLine(state) {\n  var spanningLayout = state.spanningLayout, type = state.layoutType;\n\n  for (var key in state) if (state.hasOwnProperty(key))\n    delete state[key];\n\n  state.mode = Modes.newLayout;\n  if (spanningLayout) {\n    state.layoutType = type;\n    state.spanningLayout = true;\n  }\n}\n\nvar REs = {\n  cache: {},\n  single: {\n    bc: \"bc\",\n    bq: \"bq\",\n    definitionList: /- .*?:=+/,\n    definitionListEnd: /.*=:\\s*$/,\n    div: \"div\",\n    drawTable: /\\|.*\\|/,\n    foot: /fn\\d+/,\n    header: /h[1-6]/,\n    html: /\\s*<(?:\\/)?(\\w+)(?:[^>]+)?>(?:[^<]+<\\/\\1>)?/,\n    link: /[^\"]+\":\\S/,\n    linkDefinition: /\\[[^\\s\\]]+\\]\\S+/,\n    list: /(?:#+|\\*+)/,\n    notextile: \"notextile\",\n    para: \"p\",\n    pre: \"pre\",\n    table: \"table\",\n    tableCellAttributes: /[\\/\\\\]\\d+/,\n    tableHeading: /\\|_\\./,\n    tableText: /[^\"_\\*\\[\\(\\?\\+~\\^%@|-]+/,\n    text: /[^!\"_=\\*\\[\\(<\\?\\+~\\^%@-]+/\n  },\n  attributes: {\n    align: /(?:<>|<|>|=)/,\n    selector: /\\([^\\(][^\\)]+\\)/,\n    lang: /\\[[^\\[\\]]+\\]/,\n    pad: /(?:\\(+|\\)+){1,2}/,\n    css: /\\{[^\\}]+\\}/\n  },\n  createRe: function(name) {\n    switch (name) {\n    case \"drawTable\":\n      return REs.makeRe(\"^\", REs.single.drawTable, \"$\");\n    case \"html\":\n      return REs.makeRe(\"^\", REs.single.html, \"(?:\", REs.single.html, \")*\", \"$\");\n    case \"linkDefinition\":\n      return REs.makeRe(\"^\", REs.single.linkDefinition, \"$\");\n    case \"listLayout\":\n      return REs.makeRe(\"^\", REs.single.list, RE(\"allAttributes\"), \"*\\\\s+\");\n    case \"tableCellAttributes\":\n      return REs.makeRe(\"^\", REs.choiceRe(REs.single.tableCellAttributes,\n                                          RE(\"allAttributes\")), \"+\\\\.\");\n    case \"type\":\n      return REs.makeRe(\"^\", RE(\"allTypes\"));\n    case \"typeLayout\":\n      return REs.makeRe(\"^\", RE(\"allTypes\"), RE(\"allAttributes\"),\n                        \"*\\\\.\\\\.?\", \"(\\\\s+|$)\");\n    case \"attributes\":\n      return REs.makeRe(\"^\", RE(\"allAttributes\"), \"+\");\n\n    case \"allTypes\":\n      return REs.choiceRe(REs.single.div, REs.single.foot,\n                          REs.single.header, REs.single.bc, REs.single.bq,\n                          REs.single.notextile, REs.single.pre, REs.single.table,\n                          REs.single.para);\n\n    case \"allAttributes\":\n      return REs.choiceRe(REs.attributes.selector, REs.attributes.css,\n                          REs.attributes.lang, REs.attributes.align, REs.attributes.pad);\n\n    default:\n      return REs.makeRe(\"^\", REs.single[name]);\n    }\n  },\n  makeRe: function() {\n    var pattern = \"\";\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      pattern += (typeof arg === \"string\") ? arg : arg.source;\n    }\n    return new RegExp(pattern);\n  },\n  choiceRe: function() {\n    var parts = [arguments[0]];\n    for (var i = 1; i < arguments.length; ++i) {\n      parts[i * 2 - 1] = \"|\";\n      parts[i * 2] = arguments[i];\n    }\n\n    parts.unshift(\"(?:\");\n    parts.push(\")\");\n    return REs.makeRe.apply(null, parts);\n  }\n};\n\nfunction RE(name) {\n  return (REs.cache[name] || (REs.cache[name] = REs.createRe(name)));\n}\n\nvar Modes = {\n  newLayout: function(stream, state) {\n    if (stream.match(RE(\"typeLayout\"), false)) {\n      state.spanningLayout = false;\n      return (state.mode = Modes.blockType)(stream, state);\n    }\n    var newMode;\n    if (!textileDisabled(state)) {\n      if (stream.match(RE(\"listLayout\"), false))\n        newMode = Modes.list;\n      else if (stream.match(RE(\"drawTable\"), false))\n        newMode = Modes.table;\n      else if (stream.match(RE(\"linkDefinition\"), false))\n        newMode = Modes.linkDefinition;\n      else if (stream.match(RE(\"definitionList\")))\n        newMode = Modes.definitionList;\n      else if (stream.match(RE(\"html\"), false))\n        newMode = Modes.html;\n    }\n    return (state.mode = (newMode || Modes.text))(stream, state);\n  },\n\n  blockType: function(stream, state) {\n    var match, type;\n    state.layoutType = null;\n\n    if (match = stream.match(RE(\"type\")))\n      type = match[0];\n    else\n      return (state.mode = Modes.text)(stream, state);\n\n    if (match = type.match(RE(\"header\"))) {\n      state.layoutType = \"header\";\n      state.header = parseInt(match[0][1]);\n    } else if (type.match(RE(\"bq\"))) {\n      state.layoutType = \"quote\";\n    } else if (type.match(RE(\"bc\"))) {\n      state.layoutType = \"code\";\n    } else if (type.match(RE(\"foot\"))) {\n      state.layoutType = \"footnote\";\n    } else if (type.match(RE(\"notextile\"))) {\n      state.layoutType = \"notextile\";\n    } else if (type.match(RE(\"pre\"))) {\n      state.layoutType = \"pre\";\n    } else if (type.match(RE(\"div\"))) {\n      state.layoutType = \"div\";\n    } else if (type.match(RE(\"table\"))) {\n      state.layoutType = \"table\";\n    }\n\n    state.mode = Modes.attributes;\n    return tokenStyles(state);\n  },\n\n  text: function(stream, state) {\n    if (stream.match(RE(\"text\"))) return tokenStyles(state);\n\n    var ch = stream.next();\n    if (ch === '\"')\n      return (state.mode = Modes.link)(stream, state);\n    return handlePhraseModifier(stream, state, ch);\n  },\n\n  attributes: function(stream, state) {\n    state.mode = Modes.layoutLength;\n\n    if (stream.match(RE(\"attributes\")))\n      return TOKEN_STYLES.attributes\n    else\n      return tokenStyles(state);\n  },\n\n  layoutLength: function(stream, state) {\n    if (stream.eat(\".\") && stream.eat(\".\"))\n      state.spanningLayout = true;\n\n    state.mode = Modes.text;\n    return tokenStyles(state);\n  },\n\n  list: function(stream, state) {\n    var match = stream.match(RE(\"list\"));\n    state.listDepth = match[0].length;\n    var listMod = (state.listDepth - 1) % 3;\n    if (!listMod)\n      state.layoutType = \"list1\";\n    else if (listMod === 1)\n      state.layoutType = \"list2\";\n    else\n      state.layoutType = \"list3\";\n\n    state.mode = Modes.attributes;\n    return tokenStyles(state);\n  },\n\n  link: function(stream, state) {\n    state.mode = Modes.text;\n    if (stream.match(RE(\"link\"))) {\n      stream.match(/\\S+/);\n      return TOKEN_STYLES.link\n    }\n    return tokenStyles(state);\n  },\n\n  linkDefinition: function(stream) {\n    stream.skipToEnd();\n    return TOKEN_STYLES.linkDefinition\n  },\n\n  definitionList: function(stream, state) {\n    stream.match(RE(\"definitionList\"));\n\n    state.layoutType = \"definitionList\";\n\n    if (stream.match(/\\s*$/))\n      state.spanningLayout = true;\n    else\n      state.mode = Modes.attributes;\n\n    return tokenStyles(state);\n  },\n\n  html: function(stream) {\n    stream.skipToEnd();\n    return TOKEN_STYLES.html\n  },\n\n  table: function(stream, state) {\n    state.layoutType = \"table\";\n    return (state.mode = Modes.tableCell)(stream, state);\n  },\n\n  tableCell: function(stream, state) {\n    if (stream.match(RE(\"tableHeading\")))\n      state.tableHeading = true;\n    else\n      stream.eat(\"|\");\n\n    state.mode = Modes.tableCellAttributes;\n    return tokenStyles(state);\n  },\n\n  tableCellAttributes: function(stream, state) {\n    state.mode = Modes.tableText;\n\n    if (stream.match(RE(\"tableCellAttributes\")))\n      return TOKEN_STYLES.attributes\n    else\n      return tokenStyles(state);\n  },\n\n  tableText: function(stream, state) {\n    if (stream.match(RE(\"tableText\")))\n      return tokenStyles(state);\n\n    if (stream.peek() === \"|\") { // end of cell\n      state.mode = Modes.tableCell;\n      return tokenStyles(state);\n    }\n    return handlePhraseModifier(stream, state, stream.next());\n  }\n};\n\nexport const textile = {\n  name: \"textile\",\n  startState: function() {\n    return { mode: Modes.newLayout };\n  },\n  token: function(stream, state) {\n    if (stream.sol()) startNewLine(stream, state);\n    return state.mode(stream, state);\n  },\n  blankLine: blankLine\n};\n"], "mappings": ";;;AAAA,IAAI,eAAe;AAAA,EACjB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,WAAW;AAAA,EACX,KAAK;AAAA,EACL,GAAG;AAAA,EACH,OAAO;AAAA,EACP,MAAM;AAAA,EACN,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,cAAc;AAChB;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,QAAM,OAAO,MAAM;AACnB,QAAM,eAAe;AAErB,MAAI,MAAM,eAAe,oBAAoB,MAAM,kBAC/C,OAAO,MAAM,GAAG,mBAAmB,GAAG,KAAK;AAC7C,UAAM,iBAAiB;AAC3B;AAEA,SAAS,qBAAqB,QAAQ,OAAO,IAAI;AAC/C,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,IAAI,GAAG;AAChB,aAAO,qBAAqB,QAAQ,OAAO,UAAU,MAAM,CAAC;AAAA;AAE5D,aAAO,qBAAqB,QAAQ,OAAO,MAAM,KAAK,CAAC;AAAA,EAC3D;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,qBAAqB,QAAQ,OAAO,QAAQ,QAAQ,CAAC;AAAA,IAC9D;AACA,WAAO,qBAAqB,QAAQ,OAAO,UAAU,MAAM,CAAC;AAAA,EAC9D;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,MAAM,OAAO;AAAG,YAAM,WAAW;AAC5C,WAAO,YAAY,KAAK;AAAA,EAC1B;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,OAAO,MAAM,aAAa;AACrC,QAAI;AACF,aAAO,aAAa;AAAA,EACxB;AAEA,MAAI,OAAO,OAAO,OAAO,MAAM,wBAAwB;AACrD,WAAO,aAAa;AAEtB,MAAI,OAAO,OAAO,OAAO,IAAI,GAAG;AAC9B,WAAO,qBAAqB,QAAQ,OAAO,QAAQ,QAAQ,CAAC;AAE9D,MAAI,OAAO,OAAO,OAAO,IAAI,GAAG;AAC9B,WAAO,qBAAqB,QAAQ,OAAO,aAAa,MAAM,CAAC;AAEjE,MAAI,OAAO,OAAO,CAAC,OAAO,IAAI,GAAG;AAC/B,WAAO,qBAAqB,QAAQ,OAAO,YAAY,KAAK,CAAC;AAE/D,MAAI,OAAO;AACT,WAAO,qBAAqB,QAAQ,OAAO,YAAY,MAAM,CAAC;AAEhE,MAAI,OAAO;AACT,WAAO,qBAAqB,QAAQ,OAAO,OAAO,KAAK,CAAC;AAE1D,MAAI,OAAO;AACT,WAAO,qBAAqB,QAAQ,OAAO,OAAO,MAAM,CAAC;AAE3D,MAAI,OAAO;AACT,WAAO,qBAAqB,QAAQ,OAAO,QAAQ,KAAK,CAAC;AAE3D,MAAI,OAAO;AACT,WAAO,qBAAqB,QAAQ,OAAO,QAAQ,KAAK,CAAC;AAE3D,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,qBAAqB,QAAQ,OAAO,SAAS,oBAAoB,CAAC;AAC7E,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AACA,SAAO,YAAY,KAAK;AAC1B;AAEA,SAAS,qBAAqB,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAC9E,MAAI,aAAa,OAAO,MAAM,WAAW,OAAO,OAAO,OAAO,OAAO,MAAM,WAAW,CAAC,IAAI;AAC3F,MAAI,YAAY,OAAO,KAAK;AAC5B,MAAI,MAAM,cAAc,GAAG;AACzB,SAAK,CAAC,aAAa,KAAK,KAAK,SAAS,MAAM,cAAc,KAAK,KAAK,UAAU,GAAG;AAC/E,UAAI,OAAO,YAAY,KAAK;AAC5B,YAAM,cAAc,IAAI;AACxB,aAAO;AAAA,IACT;AAAA,EACF,YAAY,CAAC,cAAc,KAAK,KAAK,UAAU,MAAM,aAAa,KAAK,KAAK,SAAS,KAC1E,OAAO,MAAM,IAAI,OAAO,WAAW,QAAQ,SAAS,WAAW,GAAG,KAAK,GAAG;AACnF,UAAM,cAAc,IAAI;AACxB,UAAM,OAAO,MAAM;AAAA,EACrB;AACA,SAAO,YAAY,KAAK;AAC1B;AAEA,SAAS,YAAY,OAAO;AAC1B,MAAI,WAAW,gBAAgB,KAAK;AACpC,MAAI;AAAU,WAAO;AAErB,MAAI,SAAS,CAAC;AACd,MAAI,MAAM;AAAY,WAAO,KAAK,aAAa,MAAM,UAAU,CAAC;AAEhE,WAAS,OAAO,OAAO;AAAA,IACrB;AAAA,IAAO;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAM;AAAA,IAC7D;AAAA,IAAS;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAO;AAAA,IAAO;AAAA,IAAS;AAAA,EAAc,CAAC;AAErF,MAAI,MAAM,eAAe;AACvB,WAAO,KAAK,aAAa,SAAS,MAAM,MAAM,MAAM;AAEtD,SAAO,OAAO,SAAS,OAAO,KAAK,GAAG,IAAI;AAC5C;AAEA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,OAAO,MAAM;AAEjB,UAAO,MAAM;AAAA,IACb,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,aAAa,IAAI;AAAA,IAC1B;AACE,UAAI,MAAM;AACR,eAAO,aAAa,aAAa,OAAQ,MAAM,aAAa,IAAI,IAAK;AACvE,aAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,QAAI,MAAM,UAAU,CAAC,CAAC;AACpB,aAAO,KAAK,aAAa,UAAU,CAAC,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,iBAAiB,MAAM,gBAAgB,OAAO,MAAM;AAExD,WAAS,OAAO;AAAO,QAAI,MAAM,eAAe,GAAG;AACjD,aAAO,MAAM,GAAG;AAElB,QAAM,OAAO,MAAM;AACnB,MAAI,gBAAgB;AAClB,UAAM,aAAa;AACnB,UAAM,iBAAiB;AAAA,EACzB;AACF;AAEA,IAAI,MAAM;AAAA,EACR,OAAO,CAAC;AAAA,EACR,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,KAAK;AAAA,IACL,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,YAAQ,MAAM;AAAA,MACd,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,IAAI,OAAO,WAAW,GAAG;AAAA,MAClD,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,OAAO,IAAI,OAAO,MAAM,MAAM,GAAG;AAAA,MAC3E,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,IAAI,OAAO,gBAAgB,GAAG;AAAA,MACvD,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,GAAG,eAAe,GAAG,OAAO;AAAA,MACtE,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,IAAI;AAAA,UAAS,IAAI,OAAO;AAAA,UACX,GAAG,eAAe;AAAA,QAAC,GAAG,MAAM;AAAA,MAClE,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,GAAG,UAAU,CAAC;AAAA,MACvC,KAAK;AACH,eAAO,IAAI;AAAA,UAAO;AAAA,UAAK,GAAG,UAAU;AAAA,UAAG,GAAG,eAAe;AAAA,UACvC;AAAA,UAAY;AAAA,QAAU;AAAA,MAC1C,KAAK;AACH,eAAO,IAAI,OAAO,KAAK,GAAG,eAAe,GAAG,GAAG;AAAA,MAEjD,KAAK;AACH,eAAO,IAAI;AAAA,UAAS,IAAI,OAAO;AAAA,UAAK,IAAI,OAAO;AAAA,UAC3B,IAAI,OAAO;AAAA,UAAQ,IAAI,OAAO;AAAA,UAAI,IAAI,OAAO;AAAA,UAC7C,IAAI,OAAO;AAAA,UAAW,IAAI,OAAO;AAAA,UAAK,IAAI,OAAO;AAAA,UACjD,IAAI,OAAO;AAAA,QAAI;AAAA,MAErC,KAAK;AACH,eAAO,IAAI;AAAA,UAAS,IAAI,WAAW;AAAA,UAAU,IAAI,WAAW;AAAA,UACxC,IAAI,WAAW;AAAA,UAAM,IAAI,WAAW;AAAA,UAAO,IAAI,WAAW;AAAA,QAAG;AAAA,MAEnF;AACE,eAAO,IAAI,OAAO,KAAK,IAAI,OAAO,IAAI,CAAC;AAAA,IACzC;AAAA,EACF;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,UAAI,MAAM,UAAU,CAAC;AACrB,iBAAY,OAAO,QAAQ,WAAY,MAAM,IAAI;AAAA,IACnD;AACA,WAAO,IAAI,OAAO,OAAO;AAAA,EAC3B;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;AACzB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,IAC5B;AAEA,UAAM,QAAQ,KAAK;AACnB,UAAM,KAAK,GAAG;AACd,WAAO,IAAI,OAAO,MAAM,MAAM,KAAK;AAAA,EACrC;AACF;AAEA,SAAS,GAAG,MAAM;AAChB,SAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,IAAI,SAAS,IAAI;AACjE;AAEA,IAAI,QAAQ;AAAA,EACV,WAAW,SAAS,QAAQ,OAAO;AACjC,QAAI,OAAO,MAAM,GAAG,YAAY,GAAG,KAAK,GAAG;AACzC,YAAM,iBAAiB;AACvB,cAAQ,MAAM,OAAO,MAAM,WAAW,QAAQ,KAAK;AAAA,IACrD;AACA,QAAI;AACJ,QAAI,CAAC,gBAAgB,KAAK,GAAG;AAC3B,UAAI,OAAO,MAAM,GAAG,YAAY,GAAG,KAAK;AACtC,kBAAU,MAAM;AAAA,eACT,OAAO,MAAM,GAAG,WAAW,GAAG,KAAK;AAC1C,kBAAU,MAAM;AAAA,eACT,OAAO,MAAM,GAAG,gBAAgB,GAAG,KAAK;AAC/C,kBAAU,MAAM;AAAA,eACT,OAAO,MAAM,GAAG,gBAAgB,CAAC;AACxC,kBAAU,MAAM;AAAA,eACT,OAAO,MAAM,GAAG,MAAM,GAAG,KAAK;AACrC,kBAAU,MAAM;AAAA,IACpB;AACA,YAAQ,MAAM,OAAQ,WAAW,MAAM,MAAO,QAAQ,KAAK;AAAA,EAC7D;AAAA,EAEA,WAAW,SAAS,QAAQ,OAAO;AACjC,QAAI,OAAO;AACX,UAAM,aAAa;AAEnB,QAAI,QAAQ,OAAO,MAAM,GAAG,MAAM,CAAC;AACjC,aAAO,MAAM,CAAC;AAAA;AAEd,cAAQ,MAAM,OAAO,MAAM,MAAM,QAAQ,KAAK;AAEhD,QAAI,QAAQ,KAAK,MAAM,GAAG,QAAQ,CAAC,GAAG;AACpC,YAAM,aAAa;AACnB,YAAM,SAAS,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,IACrC,WAAW,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG;AAC/B,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG;AAC/B,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,MAAM,CAAC,GAAG;AACjC,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,WAAW,CAAC,GAAG;AACtC,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG;AAChC,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG;AAChC,YAAM,aAAa;AAAA,IACrB,WAAW,KAAK,MAAM,GAAG,OAAO,CAAC,GAAG;AAClC,YAAM,aAAa;AAAA,IACrB;AAEA,UAAM,OAAO,MAAM;AACnB,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,MAAM,SAAS,QAAQ,OAAO;AAC5B,QAAI,OAAO,MAAM,GAAG,MAAM,CAAC;AAAG,aAAO,YAAY,KAAK;AAEtD,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,OAAO;AACT,cAAQ,MAAM,OAAO,MAAM,MAAM,QAAQ,KAAK;AAChD,WAAO,qBAAqB,QAAQ,OAAO,EAAE;AAAA,EAC/C;AAAA,EAEA,YAAY,SAAS,QAAQ,OAAO;AAClC,UAAM,OAAO,MAAM;AAEnB,QAAI,OAAO,MAAM,GAAG,YAAY,CAAC;AAC/B,aAAO,aAAa;AAAA;AAEpB,aAAO,YAAY,KAAK;AAAA,EAC5B;AAAA,EAEA,cAAc,SAAS,QAAQ,OAAO;AACpC,QAAI,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG;AACnC,YAAM,iBAAiB;AAEzB,UAAM,OAAO,MAAM;AACnB,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,MAAM,SAAS,QAAQ,OAAO;AAC5B,QAAI,QAAQ,OAAO,MAAM,GAAG,MAAM,CAAC;AACnC,UAAM,YAAY,MAAM,CAAC,EAAE;AAC3B,QAAI,WAAW,MAAM,YAAY,KAAK;AACtC,QAAI,CAAC;AACH,YAAM,aAAa;AAAA,aACZ,YAAY;AACnB,YAAM,aAAa;AAAA;AAEnB,YAAM,aAAa;AAErB,UAAM,OAAO,MAAM;AACnB,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,MAAM,SAAS,QAAQ,OAAO;AAC5B,UAAM,OAAO,MAAM;AACnB,QAAI,OAAO,MAAM,GAAG,MAAM,CAAC,GAAG;AAC5B,aAAO,MAAM,KAAK;AAClB,aAAO,aAAa;AAAA,IACtB;AACA,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,gBAAgB,SAAS,QAAQ;AAC/B,WAAO,UAAU;AACjB,WAAO,aAAa;AAAA,EACtB;AAAA,EAEA,gBAAgB,SAAS,QAAQ,OAAO;AACtC,WAAO,MAAM,GAAG,gBAAgB,CAAC;AAEjC,UAAM,aAAa;AAEnB,QAAI,OAAO,MAAM,MAAM;AACrB,YAAM,iBAAiB;AAAA;AAEvB,YAAM,OAAO,MAAM;AAErB,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,MAAM,SAAS,QAAQ;AACrB,WAAO,UAAU;AACjB,WAAO,aAAa;AAAA,EACtB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAM,aAAa;AACnB,YAAQ,MAAM,OAAO,MAAM,WAAW,QAAQ,KAAK;AAAA,EACrD;AAAA,EAEA,WAAW,SAAS,QAAQ,OAAO;AACjC,QAAI,OAAO,MAAM,GAAG,cAAc,CAAC;AACjC,YAAM,eAAe;AAAA;AAErB,aAAO,IAAI,GAAG;AAEhB,UAAM,OAAO,MAAM;AACnB,WAAO,YAAY,KAAK;AAAA,EAC1B;AAAA,EAEA,qBAAqB,SAAS,QAAQ,OAAO;AAC3C,UAAM,OAAO,MAAM;AAEnB,QAAI,OAAO,MAAM,GAAG,qBAAqB,CAAC;AACxC,aAAO,aAAa;AAAA;AAEpB,aAAO,YAAY,KAAK;AAAA,EAC5B;AAAA,EAEA,WAAW,SAAS,QAAQ,OAAO;AACjC,QAAI,OAAO,MAAM,GAAG,WAAW,CAAC;AAC9B,aAAO,YAAY,KAAK;AAE1B,QAAI,OAAO,KAAK,MAAM,KAAK;AACzB,YAAM,OAAO,MAAM;AACnB,aAAO,YAAY,KAAK;AAAA,IAC1B;AACA,WAAO,qBAAqB,QAAQ,OAAO,OAAO,KAAK,CAAC;AAAA,EAC1D;AACF;AAEO,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO,EAAE,MAAM,MAAM,UAAU;AAAA,EACjC;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI;AAAG,mBAAa,QAAQ,KAAK;AAC5C,WAAO,MAAM,KAAK,QAAQ,KAAK;AAAA,EACjC;AAAA,EACA;AACF;", "names": []}