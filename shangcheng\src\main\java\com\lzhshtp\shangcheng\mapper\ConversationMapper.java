package com.lzhshtp.shangcheng.mapper;

import com.lzhshtp.shangcheng.model.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ConversationMapper {

    int createConversation(Conversation conversation);

    Conversation findConversationBetweenUsers(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    List<Conversation> findConversationsByUserId(@Param("userId") Long userId);

    Conversation findConversationById(@Param("conversationId") Long conversationId);

    int updateConversationPreview(@Param("conversationId") Long conversationId, @Param("preview") String preview);

    int incrementUnreadCountUser1(@Param("conversationId") Long conversationId);

    int incrementUnreadCountUser2(@Param("conversationId") Long conversationId);

    int resetUnreadCountUser1(@Param("conversationId") Long conversationId);

    int resetUnreadCountUser2(@Param("conversationId") Long conversationId);
} 