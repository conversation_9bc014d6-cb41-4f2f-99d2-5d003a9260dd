package com.lzhshtp.shangcheng.constants;

/**
 * 审核系统常量
 */
public class AuditConstants {
    
    // ========== 审核阈值 ==========
    
    /**
     * 信用分审核阈值
     */
    public static final int CREDIT_SCORE_THRESHOLD = 85;
    
    /**
     * 自动拒绝风险评分阈值
     */
    public static final int AUTO_REJECT_SCORE_THRESHOLD = 70;
    
    /**
     * 人工审核风险评分阈值
     */
    public static final int MANUAL_REVIEW_SCORE_THRESHOLD = 40;
    
    // ========== 发布频率限制 ==========
    
    /**
     * 每日最大发布商品数量
     */
    public static final int MAX_DAILY_POSTS = 10;
    
    /**
     * 每周最大发布商品数量
     */
    public static final int MAX_WEEKLY_POSTS = 30;
    
    // ========== 审核时间限制 ==========
    
    /**
     * 人工审核任务截止时间（小时）
     */
    public static final int MANUAL_AUDIT_DEADLINE_HOURS = 72;
    
    /**
     * 二度复审任务截止时间（小时）
     */
    public static final int SECOND_REVIEW_DEADLINE_HOURS = 120;
    
    /**
     * 材料提交截止时间（天）
     */
    public static final int MATERIAL_SUBMIT_DEADLINE_DAYS = 7;
    
    // ========== 审核决策枚举 ==========
    
    /**
     * 自动审核决策
     */
    public enum AutoAuditDecision {
        AUTO_APPROVE("auto_approve", "自动通过"),
        MANUAL_REVIEW("manual_review", "人工审核"),
        AUTO_REJECT("auto_reject", "自动拒绝");
        
        private final String code;
        private final String description;
        
        AutoAuditDecision(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 人工审核决策
     */
    public enum ManualAuditDecision {
        APPROVED("approved", "审核通过"),
        REJECTED("rejected", "审核拒绝"),
        REQUEST_MATERIALS("request_materials", "要求补充材料"),
        ESCALATE_TO_SECOND_REVIEW("escalate_to_second_review", "升级到二度复审");
        
        private final String code;
        private final String description;
        
        ManualAuditDecision(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 二度复审决策
     */
    public enum SecondReviewDecision {
        APPROVED("approved", "最终通过"),
        REJECTED("rejected", "最终拒绝"),
        REQUEST_MATERIALS("request_materials", "要求补充材料");
        
        private final String code;
        private final String description;
        
        SecondReviewDecision(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 风险等级
     */
    public enum RiskLevel {
        LOW("low", "低风险"),
        MEDIUM("medium", "中等风险"),
        HIGH("high", "高风险");
        
        private final String code;
        private final String description;
        
        RiskLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 敏感词类型
     */
    public enum SensitiveWordType {
        BANNED("banned", "违禁词"),
        SENSITIVE("sensitive", "敏感词"),
        BRAND("brand", "品牌词"),
        CATEGORY("category", "敏感类目");

        private final String code;
        private final String description;

        SensitiveWordType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
