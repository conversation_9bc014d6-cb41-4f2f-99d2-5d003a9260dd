import request from '@/utils/request';

/**
 * 获取商品评价列表
 * @param {number} productId 商品ID
 * @returns {Promise} 评价列表
 */
export function getProductReviews(productId) {
  return request({
    url: `/reviews/product/${productId}`,
    method: 'get'
  });
}

/**
 * 提交商品评价
 * @param {Object} reviewData 评价数据
 * @param {number} reviewData.productId 商品ID
 * @param {number} reviewData.rating 评分(1-5)
 * @param {string} reviewData.comment 评价内容
 * @returns {Promise} 评价结果
 */
export function submitReview(reviewData) {
  return request({
    url: '/reviews',
    method: 'post',
    data: reviewData
  });
}

/**
 * 获取商品评价统计信息
 * @param {number} productId 商品ID
 * @returns {Promise} 评价统计信息
 */
export function getReviewStats(productId) {
  return request({
    url: `/reviews/product/${productId}/stats`,
    method: 'get'
  });
}

/**
 * 更新商品评价
 * @param {number} reviewId 评价ID
 * @param {Object} reviewData 评价数据
 * @returns {Promise} 更新结果
 */
export function updateReview(reviewId, reviewData) {
  return request({
    url: `/reviews/${reviewId}`,
    method: 'put',
    data: reviewData
  });
}

/**
 * 删除商品评价
 * @param {number} reviewId 评价ID
 * @returns {Promise} 删除结果
 */
export function deleteReview(reviewId) {
  return request({
    url: `/reviews/${reviewId}`,
    method: 'delete'
  });
}

// ========================================
// 卖家评价系统相关API
// ========================================

/**
 * 提交卖家评价
 * @param {Object} reviewData 评价数据
 * @returns {Promise} 请求Promise
 */
export function submitSellerReview(reviewData) {
  return request({
    url: '/reviews/submit',
    method: 'post',
    data: reviewData
  })
}

/**
 * 检查订单是否可以评价
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function canReviewOrder(orderId) {
  return request({
    url: `/reviews/can-review/${orderId}`,
    method: 'get'
  })
}

/**
 * 检查订单是否已评价
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function hasReviewed(orderId) {
  return request({
    url: `/reviews/has-reviewed/${orderId}`,
    method: 'get'
  })
}

/**
 * 获取订单评价信息
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function getOrderReview(orderId) {
  return request({
    url: `/reviews/order/${orderId}`,
    method: 'get'
  })
}

/**
 * 获取卖家信用信息
 * @param {Number} sellerId 卖家ID
 * @returns {Promise} 请求Promise
 */
export function getSellerCreditInfo(sellerId) {
  return request({
    url: `/reviews/seller/${sellerId}/credit`,
    method: 'get'
  })
}

/**
 * 获取卖家评价列表
 * @param {Number} sellerId 卖家ID
 * @param {Number} page 页码
 * @param {Number} pageSize 页大小
 * @returns {Promise} 请求Promise
 */
export function getSellerReviews(sellerId, page = 1, pageSize = 10) {
  return request({
    url: `/reviews/seller/${sellerId}`,
    method: 'get',
    params: { page, pageSize }
  })
}

/**
 * 获取买家评价历史
 * @param {Number} page 页码
 * @param {Number} pageSize 页大小
 * @returns {Promise} 请求Promise
 */
export function getBuyerReviews(page = 1, pageSize = 10) {
  return request({
    url: '/reviews/buyer/my-reviews',
    method: 'get',
    params: { page, pageSize }
  })
}

/**
 * 申请评价申诉
 * @param {Number} reviewId 评价ID
 * @param {String} reason 申诉原因
 * @param {String} evidence 申诉证据
 * @returns {Promise} 请求Promise
 */
export function appealReview(reviewId, reason, evidence) {
  return request({
    url: `/reviews/${reviewId}/appeal`,
    method: 'post',
    params: { reason, evidence }
  })
}