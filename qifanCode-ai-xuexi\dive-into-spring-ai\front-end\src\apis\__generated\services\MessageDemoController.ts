import type {Executor} from '../';
import type {Flux} from '../model/static/';

export class MessageDemoController {
    
    constructor(private executor: Executor) {}
    
    /**
     * 非流式问答
     * 
     * @param prompt 用户提问
     * @return org.springframework.ai.chat.model.ChatResponse
     */
    chat: (options: MessageDemoControllerOptions['chat']) => Promise<
        string
    > = async(options) => {
        let _uri = '/demo/message/chat';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.prompt;
        _uri += _separator
        _uri += 'prompt='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<string>;
    }
    
    /**
     * 流式问答
     * 
     * @param prompt 用户提问
     * @return Flux<ServerSentEvent < String>> 流式响应
     */
    chatStream: (options: MessageDemoControllerOptions['chatStream']) => Promise<
        Flux<string>
    > = async(options) => {
        let _uri = '/demo/message/chat/stream';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.prompt;
        _uri += _separator
        _uri += 'prompt='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<Flux<string>>;
    }
    
    /**
     * 调用自定义函数回答用户的提问
     * 
     * @param prompt 用户的提问
     * @return SSE流式响应
     */
    chatStreamWithFunction: (options: MessageDemoControllerOptions['chatStreamWithFunction']) => Promise<
        Flux<string>
    > = async(options) => {
        let _uri = '/demo/message/chat/stream/function';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.prompt;
        _uri += _separator
        _uri += 'prompt='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<Flux<string>>;
    }
    
    /**
     * 根据会话id，从数据库中查找历史消息，并将消息作为上下文回答。
     * 
     * @param prompt 用户的提问
     * @param sessionId 会话id
     * @return SSE流响应
     */
    chatStreamWithHistory: (options: MessageDemoControllerOptions['chatStreamWithHistory']) => Promise<
        Flux<string>
    > = async(options) => {
        let _uri = '/demo/message/chat/stream/history';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.prompt;
        _uri += _separator
        _uri += 'prompt='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        _value = options.sessionId;
        _uri += _separator
        _uri += 'sessionId='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<Flux<string>>;
    }
    
    /**
     * 从向量数据库中查找文档，并将查询的文档作为上下文回答。
     * 
     * @param prompt 用户的提问
     * @return SSE流响应
     */
    chatStreamWithRag: (options: MessageDemoControllerOptions['chatStreamWithRag']) => Promise<
        Flux<string>
    > = async(options) => {
        let _uri = '/demo/message/chat/stream/database';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.prompt;
        _uri += _separator
        _uri += 'prompt='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<Flux<string>>;
    }
}

export type MessageDemoControllerOptions = {
    'chat': {
        /**
         * 用户提问
         */
        prompt: string
    }, 
    'chatStream': {
        /**
         * 用户提问
         */
        prompt: string
    }, 
    'chatStreamWithHistory': {
        /**
         * 用户的提问
         */
        prompt: string, 
        /**
         * 会话id
         */
        sessionId: string
    }, 
    'chatStreamWithRag': {
        /**
         * 用户的提问
         */
        prompt: string
    }, 
    'chatStreamWithFunction': {
        /**
         * 用户的提问
         */
        prompt: string
    }
}
