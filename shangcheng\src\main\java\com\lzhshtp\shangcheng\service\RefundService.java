package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.RefundRequestDTO;
import com.lzhshtp.shangcheng.dto.RefundResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 退款服务接口
 */
public interface RefundService {
    
    /**
     * 创建退款申请
     * 
     * @param userId 申请用户ID
     * @param request 退款申请信息
     * @return 退款申请ID
     */
    Long createRefundRequest(Long userId, RefundRequestDTO request);
    
    /**
     * 商家处理退款申请
     * 
     * @param userId 商家用户ID
     * @param response 处理响应
     * @return 是否处理成功
     */
    boolean handleSellerResponse(Long userId, RefundResponseDTO response);
    
    /**
     * 申请管理员介入
     *
     * @param userId 买家用户ID
     * @param refundId 退款申请ID
     * @param evidence 补充证据
     * @return 是否申请成功
     */
    boolean requestAdminIntervention(Long userId, Long refundId, String evidence);

    /**
     * 申请管理员介入（支持多文件上传）
     *
     * @param userId 买家用户ID
     * @param refundId 退款申请ID
     * @param evidence 补充证据文字说明
     * @param files 上传的文件数组
     * @return 是否申请成功
     */
    boolean requestAdminIntervention(Long userId, Long refundId, String evidence, MultipartFile[] files);
    
    /**
     * 管理员处理退款申请
     * 
     * @param adminId 管理员ID
     * @param response 处理响应
     * @return 是否处理成功
     */
    boolean handleAdminResponse(Long adminId, RefundResponseDTO response);
    
    /**
     * 处理强制退款任务
     * 
     * @param sellerId 卖家ID
     * @param forcedRefundId 强制退款任务ID
     * @return 是否处理成功
     */
    boolean processForcedRefund(Long sellerId, Long forcedRefundId);
    
    /**
     * 查询退款申请列表
     * 
     * @param userId 用户ID
     * @param userType 用户类型：buyer/seller/admin
     * @return 退款申请列表
     */
    List<RefundRequestDTO> getRefundRequests(Long userId, String userType);
    
    /**
     * 查询强制退款任务列表
     * 
     * @param sellerId 卖家ID
     * @return 强制退款任务列表
     */
    List<RefundRequestDTO> getForcedRefundTasks(Long sellerId);
    
    /**
     * 检查卖家是否有待处理的强制退款任务
     *
     * @param sellerId 卖家ID
     * @return 是否有待处理任务
     */
    boolean hasPendingForcedRefunds(Long sellerId);

    /**
     * 管理员查询退款申请列表
     *
     * @param page 页码
     * @param pageSize 页面大小
     * @param status 状态筛选
     * @param keyword 关键词搜索
     * @return 退款申请列表
     */
    List<RefundRequestDTO> getAdminRefundList(int page, int pageSize, String status, String keyword);

    /**
     * 管理员查询退款统计数据
     *
     * @return 统计数据
     */
    Object getAdminRefundStatistics();
}
