<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="307e3675-62a9-4b60-a4e4-521dc984259e" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/chat-view.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/components/markdown-message.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/components/message-input.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/components/message-row.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/components/session-item.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/components/text-loading.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/front-end/src/views/chat/store/chat-store.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/dto/AiMessage.dto" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/dto/AiSession.dto" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/AbstractAgent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/Agent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/chronologist/Chronologist.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/computer/ComputerAssistant.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/computer/CpuAnalyzer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/agent/computer/DirectoryReader.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/document/DocumentController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/messge/AiMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/messge/AiMessageChatMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/messge/AiMessageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/messge/dto/AiMessageParams.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/messge/dto/AiMessageWrapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/session/AiSession.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/session/AiSessionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/ai/session/AiSessionRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/demo/DocumentAnalyzerFunction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/demo/DocumentDeleteFunction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/demo/DocumentDemoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/demo/MessageDemoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/infrastructure/config/RedisVectorConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/front-end/src/router/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/front-end/src/router/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/user/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/io/github/qifan777/knowledge/user/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Vue Composition API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/maven/apache-maven-3.6.1-bin/apache-maven-3.6.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.1-bin\apache-maven-3.6.1\conf\settings.xml" />
        <option name="workOffline" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="19" />
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2xQtjDByYeDuWOyFaLd1SyCc9u4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ServerApplication.executor": "Run",
    "Maven.KnowledgeBaseChatSpringAI [clean].executor": "Run",
    "Maven.KnowledgeBaseChatSpringAI [install].executor": "Run",
    "Maven.KnowledgeBaseChatSpringAI [validate].executor": "Run",
    "Maven.KnowledgeBaseChatSpringAI [verify].executor": "Run",
    "Maven.dive-into-spring-ai [clean].executor": "Run",
    "Maven.dive-into-spring-ai [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.ServerApplication.executor": "Run",
    "git-widget-placeholder": "sprinAi-学习",
    "junie.onboarding.icon.badge.shown": "true",
    "last_directory_selection": "C:/Users/<USER>/Desktop/ruanjian/Ai_xianmu/qifanCode-ai-xuexi/dive-into-spring-ai/target/generated-test-sources/test-annotations",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/ruanjian/Ai_xianmu/qifanCode-ai-xuexi/dive-into-spring-ai/src/main/java/io/github/qifan777/knowledge/ai/messge",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.api.executor": "Run",
    "npm.dev.executor": "Run",
    "prettierjs.PrettierConfiguration.Package": "E:\\ruanjian\\Ai-shancheng\\qifanCode-ai-xuexi\\dive-into-spring-ai\\front-end\\node_modules\\prettier",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "E:\\ruanjian1\\Ai-shancheng\\qifanCode-ai-xuexi\\dive-into-spring-ai\\front-end\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ],
    "vue.recent.templates": [
      "Vue Composition API Component"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="io.github.qifan777.knowledge.ai.messge" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\ruanjian\Ai_xianmu\qifanCode-ai-xuexi\dive-into-spring-ai\src\main\java\io\github\qifan777\knowledge\ai\messge" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.ServerApplication">
    <configuration name="ServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="io.github.qifan777.knowledge.ServerApplication" />
      <module name="dive-into-spring-ai" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="io.github.qifan777.knowledge.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="dive-into-spring-ai" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="io.github.qifan777.knowledge.ServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="api" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/front-end/package.json" />
      <command value="run" />
      <scripts>
        <script value="api" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/front-end/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ServerApplication" />
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.api" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="307e3675-62a9-4b60-a4e4-521dc984259e" name="Changes" comment="" />
      <created>1747882257956</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747882257956</updated>
      <workItem from="1747882259246" duration="16957000" />
      <workItem from="1748176771538" duration="22093000" />
      <workItem from="1748271361949" duration="14192000" />
      <workItem from="1748330762771" duration="16376000" />
      <workItem from="1748356675948" duration="3416000" />
      <workItem from="1748360380050" duration="533000" />
      <workItem from="1748361022416" duration="411000" />
      <workItem from="1748361632836" duration="7222000" />
      <workItem from="1748395233958" duration="14920000" />
      <workItem from="1748451538869" duration="9984000" />
      <workItem from="1748509778505" duration="311000" />
      <workItem from="1748511187290" duration="28000" />
      <workItem from="1748571814604" duration="8522000" />
      <workItem from="1748914643939" duration="1230000" />
      <workItem from="1752049592639" duration="828000" />
      <workItem from="1752078087230" duration="25000" />
      <workItem from="1752718792492" duration="17067000" />
      <workItem from="1752771062998" duration="593000" />
      <workItem from="1752774314788" duration="2939000" />
      <workItem from="1752781532447" duration="1873000" />
      <workItem from="1752819814793" duration="116000" />
      <workItem from="1753733214969" duration="767000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>