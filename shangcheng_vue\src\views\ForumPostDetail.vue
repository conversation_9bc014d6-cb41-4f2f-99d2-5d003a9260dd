<template>
  <div class="post-detail-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="logo" @click="goToForum">二手交易</div>
      <div class="search-box">
        <input type="text" placeholder="输入关键词" v-model="searchKeyword" />
        <button class="search-btn" @click="searchPosts">搜索</button>
      </div>
      <div class="user-actions">
        <div class="customer-service">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15C21 16.6569 19.6569 18 18 18H7.993C7.69741 18 7.41117 18.1215 7.20017 18.3475L4 22V5C4 3.34315 5.34315 2 7 2H17C18.6569 2 20 3.34315 20 5V15H21ZM12 8C12.5523 8 13 8.44772 13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8ZM12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11ZM12 14C12.5523 14 13 14.4477 13 15C13 15.5522 12.5523 16 12 16C11.4477 16 11 15.5522 11 15C11 14.4477 11.4477 14 12 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>客服</span>
        </div>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 加载指示器 -->
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>加载中...</span>
      </div>

      <div v-else-if="error" class="error-message">
        <p>{{ error }}</p>
        <button @click="fetchPostDetail" class="retry-btn">重试</button>
      </div>

      <template v-else>
        <!-- 帖子详情内容 -->
        <div class="post-content">
          <div class="post-header">
            <div class="author-info">
              <div class="author-avatar" @click="goToAuthorProfile">
                <img 
                  :src="post.authorAvatar || defaultAvatar" 
                  :alt="post.authorName" 
                  @error="handleAvatarError"
                />
              </div>
              <div class="author-details">
                <h1 class="post-title">{{ post.title }}</h1>
                <div class="post-meta">
                  <span class="author" @click="goToAuthorProfile">发布者: {{ post.authorName }}</span>
                  <span class="time">发布时间: {{ formatTime(post.postedAt) }}</span>
                  <span class="category">分类: {{ post.categoryName }}</span>
                  <span class="views">浏览量: {{ post.viewsCount }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="post-body">
            <div class="post-text">
              {{ post.content }}
            </div>
          </div>
        </div>

        <!-- 回复列表 -->
        <div class="reply-section">
          <div class="section-header">
            <h2>全部回复 ({{ comments.length }})</h2>
          </div>
          
          <div v-if="commentsLoading" class="loading-indicator-inline">
            <div class="spinner-small"></div>
            <span>加载评论中...</span>
          </div>
          
          <div v-else-if="commentsError" class="error-message">
            <p>{{ commentsError }}</p>
            <button @click="fetchComments" class="retry-btn">重试加载评论</button>
          </div>
          
          <div v-else class="reply-list">
            <div v-if="comments.length === 0" class="empty-state">
              <p>暂无回复，来发表第一条评论吧！</p>
            </div>
            
            <div v-for="comment in comments" :key="comment.commentId" class="reply-item">
              <div class="reply-user">
                <img 
                  :src="comment.authorAvatar || defaultAvatar" 
                  :alt="comment.authorName" 
                  class="avatar"
                  @error="handleAvatarError"
                  @click="goToCommentAuthorProfile(comment.authorId)"
                />
                <div class="user-name" @click="goToCommentAuthorProfile(comment.authorId)">
                  {{ comment.authorName }}
                </div>
              </div>
              <div class="reply-content">
                <div class="reply-text">{{ comment.content }}</div>
                <div class="reply-info">
                  <div class="reply-time">{{ formatTime(comment.commentedAt) }}</div>
                  <div class="reply-actions">
                    <button class="reply-btn" @click="showReplyForm(comment)">回复</button>
                  </div>
                </div>
                
                <!-- 回复输入框 -->
                <div v-if="replyingTo && replyingTo.commentId === comment.commentId" class="reply-form-inline">
                  <textarea 
                    v-model="replyText" 
                    :placeholder="`回复 @${comment.authorName}...`" 
                    rows="3"
                    class="reply-textarea-inline"
                  ></textarea>
                  <div class="form-actions-inline">
                    <button class="cancel-btn" @click="cancelReply">取消</button>
                    <button 
                      class="submit-btn" 
                      @click="submitCommentReply(comment)" 
                      :disabled="replySubmitting || !replyText.trim()"
                    >
                      {{ replySubmitting ? '提交中...' : '回复' }}
                    </button>
                  </div>
                </div>
                
                <!-- 子评论列表 -->
                <div v-if="comment.replies && comment.replies.length > 0" class="nested-replies">
                  <div v-for="reply in comment.replies" :key="reply.commentId" class="nested-reply-item">
                    <div class="nested-reply-user">
                      <img 
                        :src="reply.authorAvatar || defaultAvatar" 
                        :alt="reply.authorName" 
                        class="avatar-small"
                        @error="handleAvatarError"
                        @click="goToCommentAuthorProfile(reply.authorId)"
                      />
                    </div>
                    <div class="nested-reply-content">
                      <div class="nested-reply-header">
                        <span class="reply-author" @click="goToCommentAuthorProfile(reply.authorId)">{{ reply.authorName }}</span>
                        <span class="reply-to" v-if="reply.parentAuthorName">回复 @{{ reply.parentAuthorName }}</span>
                      </div>
                      <div class="nested-reply-text">{{ reply.content }}</div>
                      <div class="nested-reply-info">
                        <div class="reply-time">{{ formatTime(reply.commentedAt) }}</div>
                        <div class="reply-actions">
                          <button class="reply-btn" @click="showReplyForm(reply, comment)">回复</button>
                        </div>
                      </div>
                      
                      <!-- 嵌套回复输入框 -->
                      <div v-if="replyingTo && replyingTo.commentId === reply.commentId" class="reply-form-inline">
                        <textarea 
                          v-model="replyText" 
                          :placeholder="`回复 @${reply.authorName}...`" 
                          rows="3"
                          class="reply-textarea-inline"
                        ></textarea>
                        <div class="form-actions-inline">
                          <button class="cancel-btn" @click="cancelReply">取消</button>
                          <button 
                            class="submit-btn" 
                            @click="submitCommentReply(reply, comment)" 
                            :disabled="replySubmitting || !replyText.trim()"
                          >
                            {{ replySubmitting ? '提交中...' : '回复' }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 发表回复 -->
        <div class="reply-form">
          <div class="section-header">
            <h2>发表回复</h2>
          </div>
          <div class="form-content">
            <textarea 
              v-model="replyContent" 
              placeholder="写下你的回复..." 
              rows="5"
              class="reply-textarea"
            ></textarea>
            <div class="form-actions">
              <button 
                class="submit-btn" 
                @click="submitReply" 
                :disabled="submitting || !replyContent.trim()"
              >
                {{ submitting ? '提交中...' : '发表回复' }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />
  </div>
</template>

<script>
import { getPostDetail, createComment, getPostComments } from '@/api/forum'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useUserStore } from '@/stores/user'
import FloatingActionButtons from '@/components/FloatingActionButtons.vue'

export default {
  name: 'ForumPostDetail',
  components: {
    FloatingActionButtons
  },
  data() {
    return {
      postId: null,
      searchKeyword: '',
      replyContent: '',
      loading: false,
      error: null,
      commentsLoading: false,
      commentsError: null,
      submitting: false,
      userStore: null,
      
      // 帖子详情
      post: {},
      
      // 评论列表
      comments: [],

      // 默认头像
      defaultAvatar: 'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>',

      // 回复相关
      replyingTo: null,
      replyText: '',
      replySubmitting: false
    }
  },
  methods: {
    // 搜索帖子
    searchPosts() {
      this.$router.push({
        path: '/forum',
        query: { keyword: this.searchKeyword }
      });
    },

    // 获取帖子详情
    async fetchPostDetail() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await getPostDetail(this.postId);
        if (response.success) {
          this.post = response.data;
          // 获取评论
          this.fetchComments();
        } else {
          this.error = response.message || '获取帖子详情失败';
        }
      } catch (error) {
        console.error('获取帖子详情异常:', error);
        this.error = '网络错误，请稍后重试';
      } finally {
        this.loading = false;
      }
    },
    
    // 获取评论列表
    async fetchComments() {
      this.commentsLoading = true;
      this.commentsError = null;
      
      try {
        const response = await getPostComments(this.postId);
        if (response.success) {
          this.comments = response.data;
        } else {
          this.commentsError = response.message || '获取评论失败';
        }
      } catch (error) {
        console.error('获取评论异常:', error);
        this.commentsError = '网络错误，请稍后重试';
      } finally {
        this.commentsLoading = false;
      }
    },

    // 提交回复
    async submitReply() {
      if (!this.replyContent.trim()) {
        alert('回复内容不能为空');
        return;
      }
      
      // 检查登录状态
      if (!this.userStore.isLoggedIn) {
        this.$router.push('/login');
        return;
      }
      
      this.submitting = true;
      
      try {
        const commentData = {
          postId: this.postId,
          content: this.replyContent.trim()
        };
        
        const response = await createComment(commentData);
        
        if (response.success) {
          // 清空回复内容
          this.replyContent = '';
          // 重新获取评论列表
          this.fetchComments();
        } else {
          alert('发表回复失败: ' + response.message);
        }
      } catch (error) {
        console.error('发表回复异常:', error);
        alert('发表回复失败，请稍后重试');
      } finally {
        this.submitting = false;
      }
    },

    // 格式化时间为"多久之前"
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      try {
        const date = new Date(dateTimeStr);
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
      } catch (error) {
        console.error('时间格式化错误:', error);
        return dateTimeStr;
      }
    },
    
    // 检查登录状态
    checkLoginStatus() {
      this.userStore = useUserStore();
      // 如果用户已登录但尚未加载用户信息，则获取用户信息
      if (this.userStore.isLoggedIn && !this.userStore.userInfo) {
        this.userStore.fetchUserInfo();
      }
    },

    // 跳转到作者个人主页
    goToAuthorProfile() {
      if (this.post && this.post.authorId) {
        // 检查是否是当前登录用户
        if (this.userStore.userInfo && this.userStore.userInfo.userId === this.post.authorId) {
          // 如果是当前登录用户，跳转到个人中心页面
          this.$router.push('/profile');
        } else {
          // 如果是其他用户，跳转到卖家详情页
          this.$router.push(`/seller/${this.post.authorId}`);
        }
      }
    },

    // 跳转到评论作者个人主页
    goToCommentAuthorProfile(authorId) {
      if (authorId) {
        // 检查是否是当前登录用户
        if (this.userStore.userInfo && this.userStore.userInfo.userId === authorId) {
          // 如果是当前登录用户，跳转到个人中心页面
          this.$router.push('/profile');
        } else {
          // 如果是其他用户，跳转到卖家详情页
          this.$router.push(`/seller/${authorId}`);
        }
      }
    },

    // 跳转到论坛主页
    goToForum() {
      this.$router.push('/forum');
    },

    // 处理头像加载失败
    handleAvatarError(e) {
      e.target.src = this.defaultAvatar;
    },

    // 显示回复输入框
    showReplyForm(comment, parentComment = null) {
      this.replyingTo = comment;
      this.replyText = ''; // 清空回复内容
      
      // 如果是嵌套回复，保存父评论信息
      if (parentComment) {
        this.replyingTo.parentComment = parentComment;
      }
    },

    // 取消回复
    cancelReply() {
      this.replyingTo = null;
      this.replyText = '';
    },

    // 提交评论回复
    async submitCommentReply(comment, parentComment = null) {
      if (!this.replyText.trim()) {
        alert('回复内容不能为空');
        return;
      }

      if (!this.userStore.isLoggedIn) {
        this.$router.push('/login');
        return;
      }

      this.replySubmitting = true;

      try {
        // 确定正确的父评论ID
        let commentId;
        
        // 如果提供了parentComment参数，使用它
        if (parentComment) {
          commentId = parentComment.commentId;
        } 
        // 否则，检查replyingTo中是否有parentComment
        else if (comment.parentComment) {
          commentId = comment.parentComment.commentId;
        } 
        // 最后，使用当前评论ID
        else {
          commentId = comment.commentId;
        }
        
        const replyData = {
          postId: this.postId,
          content: this.replyText.trim(),
          parentCommentId: commentId
        };

        const response = await createComment(replyData);

        if (response.success) {
          this.replyText = '';
          this.replyingTo = null;
          // 重新获取评论列表以包含新回复
          this.fetchComments();
        } else {
          alert('发表回复失败: ' + response.message);
        }
      } catch (error) {
        console.error('发表评论回复异常:', error);
        alert('发表回复失败，请稍后重试');
      } finally {
        this.replySubmitting = false;
      }
    }
  },
  created() {
    // 从路由获取帖子ID
    this.postId = this.$route.params.id;
    this.checkLoginStatus();
    
    if (this.postId) {
      this.fetchPostDetail();
    } else {
      this.error = '无效的帖子ID';
    }
  }
}
</script>

<style scoped>
.post-detail-container {
  width: 100%;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

/* 顶部导航栏样式 */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e53935;
  color: white;
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  height: 60px;
}

.logo {
  font-size: 30px;
  font-weight: bold;
  min-width: 100px;
  cursor: pointer;
}

.search-box {
  display: flex;
  flex: 1;
  max-width: 550px;
  margin: 0 20px;
}

.search-box input {
  flex: 1;
  padding: 8px 15px;
  border: none;
  border-radius: 20px 0 0 20px;
  outline: none;
  background-color: rgba(255, 255, 255, 0.8);
}

.search-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 0 20px 20px 0;
  background-color: #d32f2f;
  color: white;
  cursor: pointer;
}

.user-actions {
  display: flex;
  align-items: center;
}

.customer-service {
  display: flex;
  align-items: center;
  margin-right: 15px;
  cursor: pointer;
}

/* 主体内容区样式 */
.main-content {
  max-width: 1000px;
  margin: 30px auto;
  padding: 0 20px;
}

/* 帖子详情样式 */
.post-content {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.post-header {
  margin-bottom: 20px;
}

.author-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 2px solid #e53935;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.author-avatar:hover {
  transform: scale(1.05);
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details {
  flex: 1;
}

.post-title {
  font-size: 24px;
  margin: 0 0 15px 0;
  color: #333;
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: #777;
}

.post-meta span {
  margin-right: 15px;
}

.post-meta .author {
  color: #e53935;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}

.post-meta .author:hover {
  text-decoration: underline;
}

.post-body {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.post-text {
  margin-bottom: 20px;
}

.post-image {
  margin-bottom: 20px;
}

.post-image img {
  max-width: 100%;
  border-radius: 4px;
}

.post-contact {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px dashed #ddd;
  font-size: 14px;
  color: #666;
}

/* 回复列表样式 */
.reply-section {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 18px;
  margin: 0;
  color: #e53935;
}

.reply-list {
  margin-top: 20px;
}

.reply-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 70px;
  margin-right: 20px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 5px;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 1px solid #e0e0e0;
}

.avatar:hover {
  transform: scale(1.1);
  border-color: #e53935;
}

.user-name {
  font-size: 12px;
  color: #666;
  text-align: center;
  word-break: break-all;
  cursor: pointer;
  transition: color 0.2s ease;
}

.user-name:hover {
  color: #e53935;
  text-decoration: underline;
}

.reply-content {
  flex: 1;
}

.reply-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 10px;
}

.reply-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.reply-actions {
  display: flex;
  gap: 5px;
}

.reply-btn {
  padding: 4px 8px;
  border: 1px solid #e53935;
  border-radius: 4px;
  background-color: white;
  color: #e53935;
  cursor: pointer;
  font-size: 12px;
}

.reply-btn:hover {
  background-color: #ffebee;
}

.reply-form-inline {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.reply-textarea-inline {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  outline: none;
}

.reply-textarea-inline:focus {
  border-color: #e53935;
}

.form-actions-inline {
  margin-top: 10px;
  text-align: right;
}

.cancel-btn {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  color: #666;
  cursor: pointer;
  font-size: 12px;
}

.cancel-btn:hover {
  background-color: #f0f0f0;
}

.nested-replies {
  margin-left: 50px; /* 缩进 */
  border-left: 1px dashed #eee; /* 分隔线 */
  padding-left: 20px;
  margin-top: 15px;
}

.nested-reply-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.nested-reply-item:last-child {
  border-bottom: none;
}

.nested-reply-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px; /* 调整大小以适应嵌套回复 */
  margin-right: 15px;
}

.avatar-small {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin-bottom: 5px;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 1px solid #e0e0e0;
}

.avatar-small:hover {
  transform: scale(1.1);
  border-color: #e53935;
}

.nested-reply-content {
  flex: 1;
}

.nested-reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.reply-author {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}

.reply-author:hover {
  color: #e53935;
  text-decoration: underline;
}

.reply-to {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}

.nested-reply-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 10px;
}

.nested-reply-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}

.nested-reply-actions {
  display: flex;
  gap: 5px;
}

.nested-reply-btn {
  padding: 4px 8px;
  border: 1px solid #e53935;
  border-radius: 4px;
  background-color: white;
  color: #e53935;
  cursor: pointer;
  font-size: 12px;
}

.nested-reply-btn:hover {
  background-color: #ffebee;
}

/* 回复表单样式 */
.reply-form {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-content {
  margin-top: 20px;
}

.reply-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  outline: none;
}

.reply-textarea:focus {
  border-color: #e53935;
}

.form-actions {
  margin-top: 15px;
  text-align: right;
}

.submit-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  background-color: #e53935;
  color: white;
  cursor: pointer;
  font-size: 14px;
}

.submit-btn:hover {
  background-color: #d32f2f;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 加载指示器样式 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: #e53935;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

.loading-indicator-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #666;
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: #e53935;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
  text-align: center;
  padding: 30px 0;
  color: #e53935;
}

.retry-btn {
  padding: 8px 20px;
  border: 1px solid #e53935;
  border-radius: 4px;
  background-color: white;
  color: #e53935;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
}

.retry-btn:hover {
  background-color: #ffebee;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
}

@media (max-width: 768px) {
  .post-meta {
    flex-direction: column;
  }
  
  .post-meta span {
    margin-bottom: 5px;
  }
  
  .reply-item {
    flex-direction: column;
  }
  
  .reply-user {
    flex-direction: row;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .avatar {
    margin-right: 10px;
    margin-bottom: 0;
  }
}
</style> 