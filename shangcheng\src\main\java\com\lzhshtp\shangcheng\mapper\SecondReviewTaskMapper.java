package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 二度复审任务Mapper接口
 */
@Mapper
public interface SecondReviewTaskMapper extends BaseMapper<SecondReviewTask> {
    
    /**
     * 根据条件查询复审任务
     * 实现在 SecondReviewTaskMapper.xml 中
     */
    List<SecondReviewTask> findByConditions(Map<String, Object> params);

    /**
     * 根据条件统计复审任务数量
     * 实现在 SecondReviewTaskMapper.xml 中
     */
    Long countByConditions(Map<String, Object> params);
    
    /**
     * 根据状态统计数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_second_review_tasks WHERE lzhshtp_status = #{status}")
    Long countByStatus(@Param("status") String status);
    
    /**
     * 根据最终决策统计数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_second_review_tasks WHERE lzhshtp_admin_decision = #{decision}")
    Long countByFinalDecision(@Param("decision") String decision);
    
    /**
     * 根据商品ID查询复审任务
     */
    @Select("SELECT * FROM tb_lzhshtp_second_review_tasks WHERE lzhshtp_product_id = #{productId}")
    SecondReviewTask findByProductId(@Param("productId") Long productId);
}
