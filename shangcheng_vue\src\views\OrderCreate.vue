<template>
  <div class="order-create-page">
    <!-- 顶部导航栏 -->
    <nav class="top-nav">
        <div class="container nav-content">
          <router-link to="/home" class="logo" style="color: #FF0000;">易转</router-link>
            <div class="nav-right">
                <router-link to="/profile" class="user-avatar" title="个人中心">
                    <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
                </router-link>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container main-content">
      <!-- 收货地址部分 -->
      <div class="section address-section">
        <div class="section-header">
          <h2>收货地址</h2>
          <a class="manage-link" @click="manageAddress">管理地址</a>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>加载地址中...</p>
        </div>

        <!-- 地址列表 -->
        <div v-else class="address-list">
          <!-- 没有地址时显示 -->
          <div v-if="addresses.length === 0" class="no-address">
            <p>暂无收货地址，请添加</p>
          </div>

          <!-- 地址卡片 -->
          <div
            class="address-card"
            v-for="(address, index) in addresses"
            :key="address.id"
            :class="{ active: selectedAddressIndex === index }"
            @click="selectAddress(index)"
          >
            <div class="address-content">
              <div class="address-info">
                <p class="address-name">{{ address.name }} {{ address.phone }}</p>
                <p class="address-detail">{{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}</p>
              </div>
              <div class="address-actions">
                <div class="address-default" v-if="address.isDefault">默认</div>
                <button v-else class="set-default-btn" @click.stop="setAsDefault(address.id)">设为默认</button>
              </div>
            </div>
          </div>

          <!-- 添加地址按钮 -->
          <div class="add-address" @click="addNewAddress">
            <span class="add-icon">+</span>
            <span>添加收货地址</span>
          </div>

          <!-- 添加地址表单 -->
          <div class="address-form" v-if="showAddressForm">
            <h3>添加新地址</h3>
            <div class="form-group">
              <label>收货人姓名</label>
              <input
                type="text"
                v-model="addressForm.recipientName"
                placeholder="请输入收货人姓名"
                :class="{ 'error-input': formErrors.recipientName }"
              >
              <div class="error-message" v-if="formErrors.recipientName">{{ formErrors.recipientName }}</div>
            </div>
            <div class="form-group">
              <label>手机号码</label>
              <input
                type="text"
                v-model="addressForm.phoneNumber"
                placeholder="请输入手机号码"
                :class="{ 'error-input': formErrors.phoneNumber }"
              >
              <div class="error-message" v-if="formErrors.phoneNumber">{{ formErrors.phoneNumber }}</div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>省份</label>
                <input
                  type="text"
                  v-model="addressForm.province"
                  placeholder="请输入省份"
                  :class="{ 'error-input': formErrors.province }"
                >
                <div class="error-message" v-if="formErrors.province">{{ formErrors.province }}</div>
              </div>
              <div class="form-group">
                <label>城市</label>
                <input
                  type="text"
                  v-model="addressForm.city"
                  placeholder="请输入城市"
                  :class="{ 'error-input': formErrors.city }"
                >
                <div class="error-message" v-if="formErrors.city">{{ formErrors.city }}</div>
              </div>
            </div>
            <div class="form-group">
              <label>区/县</label>
              <input type="text" v-model="addressForm.district" placeholder="请输入区/县">
            </div>
            <div class="form-group">
              <label>详细地址</label>
              <textarea
                v-model="addressForm.streetAddress"
                placeholder="请输入详细地址"
                :class="{ 'error-input': formErrors.streetAddress }"
              ></textarea>
              <div class="error-message" v-if="formErrors.streetAddress">{{ formErrors.streetAddress }}</div>
            </div>
            <div class="form-group checkbox">
              <input type="checkbox" id="isDefault" v-model="addressForm.isDefault">
              <label for="isDefault">设为默认地址</label>
            </div>
            <div class="form-actions">
              <button class="cancel-btn" @click="showAddressForm = false">取消</button>
              <button class="submit-btn" @click="submitAddressForm">保存</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单信息部分 -->
      <div class="section order-section">
        <div class="section-header">
          <h2>订单信息</h2>
        </div>

        <!-- 加载状态 -->
        <div v-if="productLoading" class="loading-state">
          <div class="spinner"></div>
          <p>加载商品信息中...</p>
        </div>

        <!-- 商品信息 -->
        <div v-else-if="product" class="order-item">
          <div class="item-image">
            <img :src="product.imageUrls && product.imageUrls.length > 0 ? product.imageUrls[0] : 'https://via.placeholder.com/100x100'" :alt="product.title">
          </div>
          <div class="item-info">
            <h3 class="item-title">{{ product.title }}</h3>
            <p class="item-description">{{ product.description }}</p>
            <div class="item-meta">
              <span class="item-condition">成色: {{ getConditionText(product.condition) }}</span>
              <span class="item-delivery">配送方式: {{ getDeliveryMethodText(product.deliveryMethod) }}</span>
            </div>
            <div class="item-price">¥{{ product.price }}</div>

            <!-- 验货信息显示 -->
            <div v-if="product.supportOfficialVerification" class="verification-included">
              <span class="verification-badge">✅ 免费官方验货</span>
              <p class="verification-note">此商品由卖家提供官方验货服务</p>
            </div>
          </div>
        </div>

        <!-- 无商品信息 -->
        <div v-else class="no-product">
          <p>未找到商品信息，请返回重新选择</p>
          <button class="back-btn" @click="$router.push('/home')">返回首页</button>
        </div>
      </div>

      <!-- 验货选择部分 -->
      <div v-if="!product.supportOfficialVerification" class="section verification-section">
        <div class="section-header">
          <h2>验货服务</h2>
        </div>
        <div class="verification-option">
          <label class="verification-checkbox">
            <input
              type="checkbox"
              v-model="orderForm.officialVerification"
              @change="onVerificationChange"
            >
            <span class="checkmark"></span>
            <span class="verification-text">选择官方验货服务</span>
          </label>
          <div class="verification-details">
            <p class="verification-desc">
              🛡️ 官方验货服务确保商品质量，验货不通过将全额退款
            </p>
            <p class="verification-fee">验货费用：¥{{ verificationFee }}</p>
          </div>
        </div>
      </div>

      <!-- 价格明细部分 -->
      <div class="section price-section">
        <div class="section-header">
          <h2>价格明细</h2>
        </div>

        <div class="price-details">
          <div class="price-row">
            <span class="price-label">商品价格</span>
            <span class="price-value">¥{{ product.price || '0.00' }}</span>
          </div>
          <div class="price-row">
            <span class="price-label">运费</span>
            <span class="price-value">¥{{ shippingFee }}</span>
          </div>
          <div v-if="orderForm.officialVerification" class="price-row">
            <span class="price-label">验货费</span>
            <span class="price-value">¥{{ verificationFee }}</span>
          </div>
          <div class="price-row total">
            <span class="price-label">合计：</span>
            <span class="price-value">¥{{ totalPrice }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部结算栏 -->
    <div class="checkout-bar">
      <div class="container">
        <div class="checkout-left">
          <div class="total-price">
            <span>合计：</span>
            <span class="price">¥{{ totalPrice }}</span>
          </div>
          <div class="order-error" v-if="orderSubmitError">{{ orderSubmitError }}</div>
        </div>
        <button class="checkout-btn" @click="submitOrder" :disabled="!product || !product.id">确认购买</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getUserAddresses, setDefaultAddress, addAddress } from '@/api/address';
import { getProductById } from '@/api/product';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';

const router = useRouter();
const route = useRoute();

// 用户状态管理
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 快速链接数据
const quickLinks = ref(['穿戴甲', '电动车', '打印机', '手机挂绳', '休闲裤', '洞洞鞋']);

// 地址列表
const addresses = ref([]);

// 加载状态
const loading = ref(false);

// 选中的地址索引
const selectedAddressIndex = ref(0);
// 是否显示地址表单
const showAddressForm = ref(false);
// 新地址表单数据
const addressForm = ref({
  recipientName: '',
  phoneNumber: '',
  province: '',
  city: '',
  district: '',
  streetAddress: '',
  isDefault: false
});

// 选择地址
const selectAddress = (index) => {
  selectedAddressIndex.value = index;
};

// 加载用户地址列表
const loadAddresses = async () => {
  loading.value = true;
  try {
    const response = await getUserAddresses();
    if (response && response.code === 200 && response.data) {
      addresses.value = response.data.map(item => ({
        id: item.addressId,
        name: item.recipientName,
        phone: item.phoneNumber,
        province: item.province,
        city: item.city,
        district: item.district,
        detail: item.streetAddress,
        isDefault: item.isDefault
      }));

      // 如果有地址，默认选择第一个
      if (addresses.value.length > 0) {
        // 默认选择默认地址，如果没有默认地址则选择第一个
        const defaultIndex = addresses.value.findIndex(addr => addr.isDefault);
        selectedAddressIndex.value = defaultIndex >= 0 ? defaultIndex : 0;
      } else {
        selectedAddressIndex.value = -1;
      }
    }
  } catch (error) {
    console.error('获取地址列表失败:', error);
    ElMessage.error('获取地址列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 管理地址 - 跳转到用户个人中心的地址管理部分
const manageAddress = () => {
  // 先设置localStorage，然后再跳转
  window.localStorage.setItem('activeProfileSection', 'my-addresses');
  router.push('/profile');
};

// 添加新地址
const addNewAddress = () => {
  showAddressForm.value = true;
};

// 设置默认地址
const setAsDefault = async (addressId) => {
  try {
    const response = await setDefaultAddress(addressId);
    if (response && response.code === 200) {
      ElMessage.success('设置默认地址成功');
      // 重新加载地址列表
      await loadAddresses();
    }
  } catch (error) {
    console.error('设置默认地址失败:', error);
    ElMessage.error('设置默认地址失败，请稍后重试');
  }
};

// 表单错误信息
const formErrors = ref({
  recipientName: '',
  phoneNumber: '',
  province: '',
  city: '',
  streetAddress: ''
});

// 清除表单错误
const clearFormErrors = () => {
  Object.keys(formErrors.value).forEach(key => {
    formErrors.value[key] = '';
  });
};

// 验证表单
const validateAddressForm = () => {
  clearFormErrors();
  let isValid = true;

  if (!addressForm.value.recipientName) {
    formErrors.value.recipientName = '请输入收货人姓名';
    isValid = false;
  }

  if (!addressForm.value.phoneNumber) {
    formErrors.value.phoneNumber = '请输入手机号码';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(addressForm.value.phoneNumber)) {
    formErrors.value.phoneNumber = '请输入正确的手机号码';
    isValid = false;
  }

  if (!addressForm.value.province) {
    formErrors.value.province = '请输入省份';
    isValid = false;
  }

  if (!addressForm.value.city) {
    formErrors.value.city = '请输入城市';
    isValid = false;
  }

  if (!addressForm.value.streetAddress) {
    formErrors.value.streetAddress = '请输入详细地址';
    isValid = false;
  }

  return isValid;
};

// 提交新地址
const submitAddressForm = async () => {
  try {
    // 表单验证
    if (!validateAddressForm()) {
      return;
    }

    const response = await addAddress(addressForm.value);
    if (response && response.code === 200) {
      showAddressForm.value = false;
      // 清空表单
      addressForm.value = {
        recipientName: '',
        phoneNumber: '',
        province: '',
        city: '',
        district: '',
        streetAddress: '',
        isDefault: false
      };
      // 重新加载地址列表
      await loadAddresses();
      // 显示成功消息
      ElMessage.success({
        message: '添加地址成功',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('添加地址失败:', error);
    ElMessage.error({
      message: '添加地址失败，请稍后重试',
      duration: 2000
    });
  }
};

// 商品信息
const product = ref({});
const productLoading = ref(false);

// 运费
const shippingFee = ref(0.00);

// 验货相关数据
const verificationFee = ref(10.00);
const orderForm = reactive({
  officialVerification: false
});

// 计算总价
const totalPrice = computed(() => {
  const productPrice = product.value && product.value.price ? parseFloat(product.value.price) : 0;
  const shipping = parseFloat(shippingFee.value);
  const verification = orderForm.officialVerification ? parseFloat(verificationFee.value) : 0;
  return (productPrice + shipping + verification).toFixed(2);
});

// 验货选择变化处理
const onVerificationChange = () => {
  // 验货选择变化时重新计算总价
  console.log('验货选择变化:', orderForm.officialVerification);
};

// 订单提交状态
const orderSubmitError = ref('');

/**
 * 处理支付逻辑
 * @param {Object} paymentInfo - 支付信息对象
 */
const processPayment = async (paymentInfo) => {
  try {
    const { orderId, orderAmount, userBalance, balanceEnough } = paymentInfo;

    // 构建支付确认消息
    let confirmMessage = `订单创建成功！\n订单金额：¥${orderAmount}\n您的余额：¥${userBalance}\n`;

    if (balanceEnough) {
      confirmMessage += '\n余额充足，是否立即支付？';
    } else {
      confirmMessage += '\n余额不足，请先充值或选择稍后支付。';
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '支付确认',
      {
        confirmButtonText: balanceEnough ? '立即支付' : '去充值',
        cancelButtonText: '稍后支付',
        type: 'info',
        center: true,
      }
    );

    // 用户选择立即支付或去充值
    if (balanceEnough) {
      // 余额充足，直接支付
      await performBalancePayment(orderId);
    } else {
      // 余额不足，跳转到个人页面充值
      ElMessage.info('请先充值后再支付订单');
      router.push('/profile');
    }
  } catch {
    // 用户选择 "稍后支付" 或关闭了对话框
    ElMessage.info('您可以稍后在“我的订单”中完成支付。');
    // 跳转到个人中心，并激活订单列表
    window.localStorage.setItem('activeProfileSection', 'my-bought-orders');
    router.push('/profile');
  }
};

/**
 * 执行余额支付
 * @param {Number} orderId - 订单ID
 */
const performBalancePayment = async (orderId) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在支付...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const { payWithBalance } = await import('@/api/order');
    const response = await payWithBalance(orderId);

    loading.close();

    if (response && response.success) {
      ElMessage.success('支付成功！');
      // 跳转到个人中心，显示订单列表
      window.localStorage.setItem('activeProfileSection', 'my-bought-orders');
      router.push('/profile');
    } else {
      ElMessage.error(response?.message || '支付失败');
    }
  } catch (error) {
    loading.close();
    console.error('余额支付失败:', error);
    ElMessage.error('支付失败，请稍后重试');
  }
};

/**
 * 创建订单并获取支付信息
 * @param {object} orderData - 订单创建所需的数据
 */
const createOrderAndPay = async (orderData) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在创建订单...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const { createOrder, getPaymentInfo } = await import('@/api/order');
    const response = await createOrder(orderData);

    if (response && response.success) {
      const orderId = response.data;
      loading.text = '正在获取支付信息...';

      const payInfoResponse = await getPaymentInfo(orderId);
      loading.close();

      if (payInfoResponse && payInfoResponse.success) {
        await processPayment(payInfoResponse.data);
      } else {
        ElMessage.error(payInfoResponse?.message || '获取支付信息失败');
      }
    } else {
      loading.close();
      ElMessage.error(response?.message || '创建订单失败');
    }
  } catch (error) {
    loading.close();
    console.error('订单创建或支付过程出错:', error);
    ElMessage.error('订单创建失败，请稍后重试');
  }
};

// 提交订单 - 验证并显示确认对话框
const submitOrder = async () => {
  orderSubmitError.value = '';

  // 1. 验证输入
  if (!product.value || !product.value.id) {
    orderSubmitError.value = '商品信息不完整，请返回重新选择';
    return;
  }
  if (selectedAddressIndex.value === -1) {
    orderSubmitError.value = '请选择收货地址';
    return;
  }

  const selectedAddress = addresses.value[selectedAddressIndex.value];

  // 2. 显示确认对话框
  try {
    await ElMessageBox.confirm(
      `<div>
        <p><strong>商品名称:</strong> ${product.value.title}</p>
        <p><strong>商品价格:</strong> ¥${product.value.price}</p>
        <hr style="margin: 10px 0;">
        <p><strong>收货人:</strong> ${selectedAddress.name}</p>
        <p><strong>手机号:</strong> ${selectedAddress.phone}</p>
        <p><strong>地址:</strong> ${selectedAddress.province}${selectedAddress.city}${selectedAddress.district}${selectedAddress.detail}</p>
        <hr style="margin: 10px 0;">
        <p style="font-weight: bold; font-size: 16px;">总计: <span style="color: #FF4D4F;">¥${totalPrice.value}</span></p>
      </div>`,
      '确认订单信息',
      {
        confirmButtonText: '确认购买',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true, // 使用HTML字符串
        center: true, // 明确要求居中
      }
    );

    // 3. 用户确认后，继续创建订单流程
    const orderData = {
      productId: product.value.id,
      addressId: selectedAddress.id,
      paymentMethod: 'balance', // 支付方式：余额支付
      totalAmount: parseFloat(totalPrice.value),
      officialVerification: orderForm.officialVerification || product.value.supportOfficialVerification,
      verificationFee: orderForm.officialVerification ? verificationFee.value : (product.value.supportOfficialVerification ? product.value.verificationFee : 0),
      verificationPayer: product.value.supportOfficialVerification ? 'seller' : 'buyer'
    };
    await createOrderAndPay(orderData);

  } catch (error) {
    // 用户取消了确认对话框
    if (error !== 'cancel' && error !== 'close') {
        console.error("An error occurred during order submission:", error);
    } else {
        console.log('用户取消了订单确认');
        ElMessage.info('订单已取消');
    }
  }
};

// 获取商品信息
const fetchProductInfo = async (productId) => {
  productLoading.value = true;
  try {
    const response = await getProductById(productId);
    if (response && response.code === 200 && response.data) {
      product.value = response.data;
      console.log('获取到商品信息:', product.value);
    } else {
      ElMessage.error('获取商品信息失败');
      product.value = null;
    }
  } catch (error) {
    console.error('获取商品信息失败:', error);
    ElMessage.error('获取商品信息失败，请稍后重试');
    product.value = null;
  } finally {
    productLoading.value = false;
  }
};

// 获取商品成色文本
const getConditionText = (condition) => {
  const conditionMap = {
    'new': '全新',
    'like_new': '几乎全新',
    'good': '良好',
    'fair': '一般',
    'poor': '较差'
  };
  return conditionMap[condition] || condition || '未知';
};

// 获取配送方式文本
const getDeliveryMethodText = (method) => {
  const methodMap = {
    'pickup': '自提',
    'delivery': '快递',
    'both': '自提/快递均可'
  };
  return methodMap[method] || method || '未知';
};

// 组件挂载时获取商品信息和地址列表
onMounted(() => {
  const productId = route.params.productId;
  if (productId) {
    fetchProductInfo(productId);
  } else {
    ElMessage.warning('未指定商品ID，请返回重新选择');
    product.value = null;
  }

  // 加载用户地址列表
  loadAddresses();
});
</script>

<style scoped>
.order-create-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  --primary-color: #FF4D4F;
  --secondary-color: #FF7875;
  --text-color-dark: #333;
  --text-color-light: #666;
  --bg-color: #F5F5F5;
  --white: #FFFFFF;
  --border-color: #EFEFEF;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 顶部导航 */
.top-nav {
  background: var(--white);
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.search-area {
  flex-grow: 1;
  margin: 0 32px;
  max-width: 600px;
}

.search-bar {
  display: flex;
  border: 2px solid var(--primary-color);
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: none;
  padding: 10px 20px;
  width: 100%;
  font-size: 16px;
}
.search-input:focus {
  outline: none;
}

.search-button {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}

.search-tags {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 14px;
}

.search-tags a {
  color: var(--text-color-light);
  text-decoration: none;
}
.search-tags a:hover {
  color: var(--primary-color);
}

.nav-right {
  display: flex;
  align-items: center;
}

.nav-right a {
  color: var(--text-color-dark);
  text-decoration: none;
  font-size: 16px;
  transition: color 0.2s ease;
  margin-left: 20px;
}

.nav-right a:hover {
  color: var(--primary-color);
}

/* 导航右侧用户区域 */
.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: transform 0.2s ease;
}

.avatar-image:hover {
  transform: scale(1.1);
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.section-header h2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.manage-link {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.manage-link:hover {
  color: #ff6700;
}

/* 地址部分样式 */
.address-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.address-card {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.address-card:hover {
  border-color: #ffde00;
}

.address-card.active {
  border-color: var(--primary-color);
  background-color: #fff1f0;
}

.address-content {
  display: flex;
  justify-content: space-between;
}

.address-name {
  font-weight: 500;
  margin: 0 0 5px;
}

.address-detail {
  color: #666;
  margin: 0;
}

.address-actions {
  display: flex;
  align-items: center;
}

.address-default {
  padding: 2px 6px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 2px;
  font-size: 12px;
}

.set-default-btn {
  background: none;
  border: 1px solid #ddd;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.set-default-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.add-address {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border: 1px dashed #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
}

.add-address:hover {
  border-color: var(--primary-color);
  color: #333;
}

.add-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无地址状态 */
.no-address {
  text-align: center;
  padding: 20px;
  color: #999;
}

/* 表单样式 */
.address-form {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.address-form h3 {
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
}

.checkbox input {
  width: auto;
}

.checkbox label {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn {
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #666;
}

.submit-btn {
  background-color: var(--primary-color);
  border: none;
  color: white;
}

.submit-btn:hover {
  background-color: #ff4d4f;
}

/* 错误提示样式 */
.error-message {
  color: var(--primary-color);
  font-size: 12px;
  margin-top: 5px;
}

.error-input {
  border-color: var(--primary-color) !important;
}

.error-input:focus {
  outline-color: var(--primary-color);
}



/* 订单信息部分样式 */
.order-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
}

.item-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 10px;
  line-height: 1.4;
}

.item-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px;
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.item-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}

.item-condition, .item-delivery {
  font-size: 13px;
  color: #666;
}

.item-price {
  font-size: 18px;
  color: var(--primary-color);
  font-weight: 500;
}

.no-product {
  text-align: center;
  padding: 30px 0;
  color: #999;
}

.back-btn {
  display: inline-block;
  margin-top: 15px;
  padding: 8px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.back-btn:hover {
  background-color: var(--secondary-color);
}

/* 价格明细部分样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.price-label {
  color: #666;
}

.price-value {
  font-weight: 500;
}

.price-row.total {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.price-row.total .price-label,
.price-row.total .price-value {
  color: #ff6700;
  font-size: 16px;
  font-weight: 500;
}

/* 底部结算栏样式 */
.checkout-bar {
  background-color: #fff;
  padding: 15px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.checkout-bar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkout-left {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.total-price {
  font-size: 14px;
  margin-bottom: 5px;
}

.total-price .price {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 500;
}

.order-error {
  color: var(--primary-color);
  font-size: 13px;
}

.checkout-btn {
  margin-left: 20px;
}

.checkout-btn {
  background-color: #ff6700;
  color: #fff;
  border: none;
  padding: 10px 30px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.checkout-btn:hover {
  background-color: #ff4d00;
}

@media (max-width: 768px) {
  .address-content {
    flex-direction: column;
  }

  .address-default {
    align-self: flex-start;
    margin-top: 10px;
  }

  .order-item {
    flex-direction: column;
  }

  .item-image {
    width: 100%;
    height: auto;
    max-height: 200px;
  }
}

/* 验货相关样式 */
.verification-included {
  margin-top: 10px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-radius: 8px;
  color: white;
}

.verification-badge {
  font-size: 14px;
  font-weight: bold;
}

.verification-note {
  font-size: 12px;
  margin: 4px 0 0 0;
  opacity: 0.9;
}

.verification-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #dee2e6;
}

.verification-option {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.verification-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.verification-checkbox input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.verification-text {
  color: #007bff;
  font-weight: bold;
}

.verification-details {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #007bff;
}

.verification-desc {
  color: #28a745;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.verification-fee {
  color: #fd7e14;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}
</style>
