<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 二手交易平台</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        :root {
            --primary-color: #FF4D4F; /* Project Red */
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* 顶部导航 (Simplified for detail page) */
        .top-nav {
            background: var(--white);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
            margin-right: 24px;
        }

        .search-bar {
            flex-grow: 1;
            display: flex;
            border: 2px solid var(--primary-color);
            border-radius: 24px;
            overflow: hidden;
            background: #FFF;
        }

        .search-input {
            border: none; background: none; padding: 10px 20px;
            width: 100%; font-size: 14px;
        }
        .search-input:focus { outline: none; }

        .search-button {
            background: var(--primary-color); border: none; color: var(--white);
            padding: 0 24px; font-weight: 500; cursor: pointer; font-size: 15px;
        }

        /* 主内容 */
        main {
            padding: 24px 0;
        }

        .seller-info-bar {
            background: var(--white);
            padding: 12px 20px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        .seller-info-bar img { width: 40px; height: 40px; border-radius: 50%; }
        .seller-info-bar .name { font-weight: 500; font-size: 14px; }
        .seller-info-bar .meta { font-size: 12px; color: var(--text-color-light); }
        .seller-info-bar .tag {
            font-size: 10px; background-color: #FFF1F0; color: var(--primary-color);
            padding: 2px 6px; border-radius: 4px; margin-left: 8px;
        }

        .product-detail-container {
            background: var(--white);
            padding: 24px;
            border-radius: 8px;
            display: grid;
            grid-template-columns: 500px 1fr;
            gap: 40px;
        }

        /* Product Gallery */
        .product-gallery {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .thumbnail-list {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
        .thumbnail-list img {
            width: 80px; height: 80px; object-fit: cover; border-radius: 4px;
            border: 2px solid var(--border-color); cursor: pointer; transition: border-color 0.2s;
        }
        .thumbnail-list img.active { border-color: var(--primary-color); }
        
        .main-image-container { background: #f9f9f9; border-radius: 8px; }
        .main-image-container img {
            width: 100%;
            height: auto;
            aspect-ratio: 1/1;
            object-fit: cover;
            border-radius: 8px;
        }

        /* Product Info */
        .product-info .price {
            font-size: 28px;
            font-weight: bold;
            color: #F5222D;
            margin-bottom: 8px;
        }
        .product-info .price-label { font-size: 14px; color: #F5222D; }

        .product-info h1 {
            font-size: 18px; font-weight: 500; line-height: 1.6; margin-bottom: 16px;
        }

        .product-info .description {
            font-size: 14px; color: var(--text-color-light); line-height: 1.8;
            border-top: 1px dashed var(--border-color);
            border-bottom: 1px dashed var(--border-color);
            padding: 16px 0;
            margin-bottom: 24px;
        }

        .product-info .actions {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-top: 32px;
        }
        .product-info .actions button {
            padding: 14px; border-radius: 8px; font-size: 16px; font-weight: 500;
            cursor: pointer; transition: opacity 0.2s; border: none;
        }
        .product-info .actions button:hover { opacity: 0.9; }

        .want-button { background-color: var(--primary-color); color: var(--white); }
        .buy-button { background-color: #FFF1F0; color: var(--primary-color); }
        .favorite-button {
            background: var(--white); color: var(--text-color-dark);
            border: 1px solid var(--border-color);
        }

        /* Recommendations */
        .recommendations { margin-top: 32px; }
        .recommendations h2 { font-size: 20px; font-weight: 500; margin-bottom: 16px; }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
        }
        .product-card {
            background: var(--white); border-radius: 8px; overflow: hidden;
        }
        .product-card img { width: 100%; display: block; }
        .product-card-info { padding: 12px; }
        .product-card-title { font-size: 14px; margin-bottom: 8px; }
        .product-card-price { font-size: 16px; font-weight: bold; color: #F5222D; }

        /* Comments Section */
        .comments-section {
            margin-top: 32px;
            background: var(--white);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .comments-section h2 {
            font-size: 20px; font-weight: 500; margin-bottom: 20px;
            color: var(--text-color-dark);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .comment-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .comment-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px dashed var(--border-color);
        }
        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .comment-item img {
            width: 40px; height: 40px; border-radius: 50%; margin-right: 12px;
            flex-shrink: 0;
        }
        .comment-content {
            flex-grow: 1;
        }
        .comment-author {
            font-weight: 500;
            color: var(--text-color-dark);
            font-size: 15px;
            margin-bottom: 4px;
        }
        .comment-text {
            font-size: 14px;
            color: var(--text-color-light);
            line-height: 1.6;
            margin-bottom: 8px;
        }
        .comment-meta {
            font-size: 12px;
            color: #999;
        }

        .comment-input-area {
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .comment-input-area textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Noto Sans SC', sans-serif;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .comment-input-area textarea:focus {
            border-color: var(--primary-color);
        }
        .comment-input-area button {
            align-self: flex-end;
            background-color: var(--primary-color);
            color: var(--white);
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .comment-input-area button:hover {
            background-color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="container nav-content">
            <a href="index.html" class="logo">二手交易</a>
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索商品...">
                <button class="search-button">搜索</button>
            </div>
        </div>
    </nav>

    <main class="container">
        <div class="seller-info-bar">
            <img src="https://via.placeholder.com/40" alt="卖家头像">
            <div class="seller-details">
                <div class="name">张先生 <span class="tag">靠谱卖家</span></div>
                <div class="meta">2小时前来过 | 卖出1688件宝贝 | 好评率99%</div>
            </div>
        </div>

        <div class="product-detail-container">
            <div class="product-gallery">
                <div class="main-image-container">
                    <img src="https://via.placeholder.com/410x410/000000/FFFFFF?text=Product" alt="商品主图" id="mainImage">
                </div>
                <div class="thumbnail-list" id="thumbnailList">
                    <img src="https://via.placeholder.com/80x80/000000/FFFFFF?text=1" alt="缩略图1" class="active">
                    <img src="https://via.placeholder.com/80x80/555555/FFFFFF?text=2" alt="缩略图2">
                    <img src="https://via.placeholder.com/80x80/999999/FFFFFF?text=3" alt="缩略图3">
                    <img src="https://via.placeholder.com/80x80/cccccc/000000?text=4" alt="缩略图4">
                </div>
            </div>

            <div class="product-info">
                <div class="price"><span class="price-label">￥</span>138 - 168</div>
                <h1>康泰时 CONTAX 快门线 30cm L-30快门线 RTS/RX/G1/G2 相机</h1>
                <p class="description">
                    本店所售器材、配件均直接采购于日本，保正。<br>
                    日产原厂，尺寸（线长）：30cm；共两款，接口不同（请仔细参考后选择）；外观都可以，正常使用痕迹、无磕碰、无裂纹。
                </p>
                
                <div class="actions">
                    <button class="want-button">我想要</button>
                    <button class="buy-button">立即购买</button>
                    <button class="favorite-button">☆ 收藏</button>
                </div>
            </div>
        </div>

        <!-- 用户评论区 -->
        <section class="comments-section">
            <h2>用户评论 (3)</h2>
            <ul class="comment-list">
                <li class="comment-item">
                    <img src="https://via.placeholder.com/40" alt="用户头像">
                    <div class="comment-content">
                        <div class="comment-author">开心买家</div>
                        <p class="comment-text">商品和描述的一样，成色很新，卖家发货也快，满意！</p>
                        <div class="comment-meta">2023-10-26 14:30</div>
                    </div>
                </li>
                <li class="comment-item">
                    <img src="https://via.placeholder.com/40" alt="用户头像">
                    <div class="comment-content">
                        <div class="comment-author">摄影新人</div>
                        <p class="comment-text">快门线收到了，试了一下非常灵敏，客服也很耐心解答我的问题。</p>
                        <div class="comment-meta">2023-10-25 09:15</div>
                    </div>
                </li>
                <li class="comment-item">
                    <img src="https://via.placeholder.com/40" alt="用户头像">
                    <div class="comment-content">
                        <div class="comment-author">资深玩家</div>
                        <p class="comment-text">原厂线材就是不一样，手感很好，赞一个！</p>
                        <div class="comment-meta">2023-10-24 18:00</div>
                    </div>
                </li>
            </ul>

            <div class="comment-input-area">
                <h3>发表评论</h3>
                <textarea placeholder="写下你的评论..."></textarea>
                <button>提交评论</button>
            </div>
        </section>

        <div class="recommendations">
            <h2>为你推荐</h2>
            <div class="product-grid">
                <!-- Sample recommended items -->
                <div class="product-card">
                    <img src="https://via.placeholder.com/200x200" alt="推荐商品">
                    <div class="product-card-info">
                        <h3 class="product-card-title">包邮 康泰时快门线</h3>
                        <div class="product-card-price">￥99</div>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://via.placeholder.com/200x200" alt="推荐商品">
                    <div class="product-card-info">
                        <h3 class="product-card-title">康泰时 L-30电磁快门线</h3>
                        <div class="product-card-price">￥135</div>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://via.placeholder.com/200x200" alt="推荐商品">
                    <div class="product-card-info">
                        <h3 class="product-card-title">包邮 回流日本contax康泰时电子快门线</h3>
                        <div class="product-card-price">￥128</div>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://via.placeholder.com/200x200" alt="推荐商品">
                    <div class="product-card-info">
                        <h3 class="product-card-title">康泰时 原装CANON佳能数码相机快门线</h3>
                        <div class="product-card-price">￥66</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const mainImage = document.getElementById('mainImage');
            const thumbnails = document.querySelectorAll('#thumbnailList img');

            thumbnails.forEach(thumb => {
                thumb.addEventListener('click', function() {
                    // Use the same src for main image, just for demonstration
                    mainImage.src = this.src.replace('80x80', '410x410');
                    
                    document.querySelector('#thumbnailList img.active').classList.remove('active');
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html> 