package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lzhshtp.shangcheng.ai.agent.Agent;
import com.lzhshtp.shangcheng.config.AiConfig;
import com.lzhshtp.shangcheng.dto.AiChatRequest;
import com.lzhshtp.shangcheng.dto.AiChatResponse;
import com.lzhshtp.shangcheng.mapper.AiMessageMapper;
import com.lzhshtp.shangcheng.mapper.AiSessionMapper;
import com.lzhshtp.shangcheng.model.AiMessage;
import com.lzhshtp.shangcheng.model.AiSession;
import com.lzhshtp.shangcheng.service.AiChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * AI聊天服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiChatServiceImpl implements AiChatService {

    private final ChatModel chatModel;
    private final ChatMemory chatMemory;
    private final VectorStore vectorStore;
    private final AiSessionMapper aiSessionMapper;
    private final AiMessageMapper aiMessageMapper;
    private final AiConfig.AiPromptConfig aiPromptConfig;
    private final ApplicationContext applicationContext;

    @Override
    public Flux<AiChatResponse> chatStream(AiChatRequest request, Long userId) {
        // 根据参数选择处理方式
        if (request.getParams().getEnableAgent()) {
            return chatStreamWithAgent(request, userId);
        } else if (request.isUseRAG()) {
            return chatStreamWithRAG(request, userId);
        } else {
            return chatStreamNormal(request, userId);
        }
    }

    /**
     * 普通聊天流式响应
     */
    public Flux<AiChatResponse> chatStreamNormal(AiChatRequest request, Long userId) {
        return Flux.create(sink -> {
            try {
                // 1. 获取或创建会话
                AiSession session = getOrCreateSession(request, userId);

                // 2. 保存用户消息
                saveUserMessage(session.getId(), request.getContent(),
                    request.getMedias(), userId);

                // 3. 构建聊天客户端
                ChatClient chatClient = ChatClient.create(chatModel);

                // 4. 构建提示词请求
                ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
                    .system(getSystemPrompt(session.getName()));

                // 5. 添加记忆功能
                if (request.getParams().getEnableMemory()) {
                    // 🔍 添加调试日志，检查记忆功能
                    List<org.springframework.ai.chat.messages.Message> existingMessages =
                        chatMemory.get(session.getId(), request.getParams().getHistoryMessageCount());
                    log.info("🧠 会话 {} 加载了 {} 条历史消息，启用记忆功能",
                        session.getId(), existingMessages.size());

                    MessageChatMemoryAdvisor memoryAdvisor = new MessageChatMemoryAdvisor(
                        chatMemory, session.getId(), request.getParams().getHistoryMessageCount());
                    requestSpec = requestSpec.advisors(memoryAdvisor);
                }

                // 6. 设置用户输入并流式调用AI
                StringBuilder fullResponse = new StringBuilder();
                String messageId = UUID.randomUUID().toString();

                requestSpec
                    .user(request.getContent())
                    .stream()
                    .content()
                    .doOnNext(content -> {
                        fullResponse.append(content);
                        AiChatResponse response = AiChatResponse.stream(
                            session.getId(), messageId, content, false);
                        sink.next(response);
                    })
                    .doOnComplete(() -> {
                        try {
                            // 保存AI回复消息
                            saveAssistantMessage(session.getId(), fullResponse.toString(), userId);

                            // 发送结束标志
                            AiChatResponse endResponse = AiChatResponse.stream(
                                session.getId(), messageId, "", true);
                            sink.next(endResponse);
                            sink.complete();
                        } catch (Exception e) {
                            log.error("保存AI回复消息失败", e);
                            sink.error(e);
                        }
                    })
                    .doOnError(error -> {
                        log.error("AI流式对话失败", error);
                        AiChatResponse errorResponse = AiChatResponse.error(
                            session.getId(), "AI服务暂时不可用，请稍后重试");
                        sink.next(errorResponse);
                        sink.complete();
                    })
                    .subscribe();

            } catch (Exception e) {
                log.error("AI聊天服务异常", e);
                AiChatResponse errorResponse = AiChatResponse.error(null, "服务异常：" + e.getMessage());
                sink.next(errorResponse);
                sink.complete();
            }
        });
    }

    /**
     * 带RAG的聊天流式响应
     */
    public Flux<AiChatResponse> chatStreamWithRAG(AiChatRequest request, Long userId) {
        return Flux.create(sink -> {
            try {
                // 1. 获取或创建会话
                AiSession session = getOrCreateSession(request, userId);

                // 2. 保存用户消息
                saveUserMessage(session.getId(), request.getContent(),
                    request.getMedias(), userId);

                // 3. 构建聊天客户端
                ChatClient chatClient = ChatClient.create(chatModel);

                // 4. 构建提示词请求
                ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
                    .system(getRAGSystemPrompt(session.getName()));

                // 5. 添加记忆功能
                if (request.getParams().getEnableMemory()) {
                    MessageChatMemoryAdvisor memoryAdvisor = new MessageChatMemoryAdvisor(
                        chatMemory, session.getId(), request.getParams().getHistoryMessageCount());
                    requestSpec = requestSpec.advisors(memoryAdvisor);
                }

                // 6. 添加RAG功能
                String ragPrompt = """
                    下面是从知识库中检索到的相关信息：
                    ---------------------
                    {question_answer_context}
                    ---------------------
                    请基于上述知识库信息和对话历史来回答用户的问题。
                    如果知识库中没有相关信息，请基于你的通用知识回答，并提醒用户这是通用建议。
                    """;

                QuestionAnswerAdvisor ragAdvisor = new QuestionAnswerAdvisor(
                    vectorStore,
                    SearchRequest.builder().topK(5).build(),
                    ragPrompt
                );
                requestSpec = requestSpec.advisors(ragAdvisor);

                // 7. 设置用户输入并流式调用AI
                StringBuilder fullResponse = new StringBuilder();
                String messageId = UUID.randomUUID().toString();

                requestSpec
                    .user(request.getContent())
                    .stream()
                    .content()
                    .doOnNext(content -> {
                        fullResponse.append(content);
                        AiChatResponse response = AiChatResponse.stream(
                            session.getId(), messageId, content, false);
                        sink.next(response);
                    })
                    .doOnComplete(() -> {
                        try {
                            // 保存AI回复消息
                            saveAssistantMessage(session.getId(), fullResponse.toString(), userId);

                            // 发送结束标志
                            AiChatResponse endResponse = AiChatResponse.stream(
                                session.getId(), messageId, "", true);
                            sink.next(endResponse);
                            sink.complete();
                        } catch (Exception e) {
                            log.error("保存AI回复消息失败", e);
                            sink.error(e);
                        }
                    })
                    .doOnError(error -> {
                        log.error("AI流式对话失败", error);
                        AiChatResponse errorResponse = AiChatResponse.error(
                            session.getId(), "AI服务暂时不可用，请稍后重试");
                        sink.next(errorResponse);
                        sink.complete();
                    })
                    .subscribe();

            } catch (Exception e) {
                log.error("AI聊天服务异常", e);
                AiChatResponse errorResponse = AiChatResponse.error(null, "服务异常：" + e.getMessage());
                sink.next(errorResponse);
                sink.complete();
            }
        });
    }

    /**
     * 带Agent的聊天流式响应
     */
    public Flux<AiChatResponse> chatStreamWithAgent(AiChatRequest request, Long userId) {
        return Flux.create(sink -> {
            try {
                // 1. 获取或创建会话
                AiSession session = getOrCreateSession(request, userId);

                // 2. 保存用户消息
                saveUserMessage(session.getId(), request.getContent(),
                    request.getMedias(), userId);

                // 3. 获取可用的Agent
                String[] agentBeanNames = getAvailableAgents();

                // 4. 构建聊天客户端
                ChatClient chatClient = ChatClient.create(chatModel);

                // 5. 构建提示词请求
                ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
                    .system(getAgentSystemPrompt(session.getName()));

                // 6. 添加记忆功能
                if (request.getParams().getEnableMemory()) {
                    // 🔍 添加调试日志，检查Agent模式的记忆功能
                    List<org.springframework.ai.chat.messages.Message> existingMessages =
                        chatMemory.get(session.getId(), request.getParams().getHistoryMessageCount());
                    log.info("🤖 Agent模式 - 会话 {} 加载了 {} 条历史消息，启用记忆功能",
                        session.getId(), existingMessages.size());

                    MessageChatMemoryAdvisor memoryAdvisor = new MessageChatMemoryAdvisor(
                        chatMemory, session.getId(), request.getParams().getHistoryMessageCount());
                    requestSpec = requestSpec.advisors(memoryAdvisor);
                }

                // 7. 添加Agent功能
                if (agentBeanNames.length > 0) {
                    requestSpec = requestSpec.functions(agentBeanNames);
                }

                // 8. 设置用户输入并流式调用AI
                StringBuilder fullResponse = new StringBuilder();
                String messageId = UUID.randomUUID().toString();

                requestSpec
                    .user(request.getContent())
                    .stream()
                    .content()
                    .doOnNext(content -> {
                        fullResponse.append(content);
                        AiChatResponse response = AiChatResponse.stream(
                            session.getId(), messageId, content, false);
                        sink.next(response);
                    })
                    .doOnComplete(() -> {
                        // 保存AI回复消息
                        saveAiMessage(session.getId(), fullResponse.toString(), userId);

                        // 发送完成信号
                        AiChatResponse completeResponse = AiChatResponse.stream(
                            session.getId(), messageId, "", true);
                        sink.next(completeResponse);
                        sink.complete();
                    })
                    .doOnError(error -> {
                        log.error("AI Agent流式响应异常", error);
                        AiChatResponse errorResponse = AiChatResponse.error(
                            session.getId(), "AI Agent服务暂时不可用，请稍后重试");
                        sink.next(errorResponse);
                        sink.complete();
                    })
                    .subscribe();

            } catch (Exception e) {
                log.error("AI Agent聊天服务异常", e);
                AiChatResponse errorResponse = AiChatResponse.error(null, "Agent服务异常：" + e.getMessage());
                sink.next(errorResponse);
                sink.complete();
            }
        });
    }

    @Override
    public AiChatResponse chat(AiChatRequest request, Long userId) {
        try {
            // 1. 获取或创建会话
            AiSession session = getOrCreateSession(request, userId);

            // 2. 保存用户消息
            saveUserMessage(session.getId(), request.getContent(), request.getMedias(), userId);

            // 3. 构建聊天客户端
            ChatClient chatClient = ChatClient.create(chatModel);

            // 4. 构建提示词请求
            ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
                .system(getSystemPrompt(session.getName()));

            // 5. 添加记忆功能
            if (request.getParams().getEnableMemory()) {
                MessageChatMemoryAdvisor memoryAdvisor = new MessageChatMemoryAdvisor(
                    chatMemory, session.getId(), request.getParams().getHistoryMessageCount());
                requestSpec = requestSpec.advisors(memoryAdvisor);
            }

            // 6. 调用AI获取回复
            String aiResponse = requestSpec
                .user(request.getContent())
                .call()
                .content();

            // 6. 保存AI回复消息
            AiMessage assistantMessage = saveAssistantMessage(session.getId(), aiResponse, userId);

            return AiChatResponse.success(session.getId(), assistantMessage.getId(), aiResponse);

        } catch (Exception e) {
            log.error("AI聊天服务异常", e);
            return AiChatResponse.error(null, "服务异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public AiSession createSession(String sessionName, Long userId) {
        AiSession session = AiSession.builder()
            .id(UUID.randomUUID().toString())
            .name(StringUtils.hasText(sessionName) ? sessionName : "新对话")
            .creatorId(userId.toString())
            .editorId(userId.toString())
            .createdTime(LocalDateTime.now())
            .editedTime(LocalDateTime.now())
            .build();

        aiSessionMapper.insert(session);
        return session;
    }

    @Override
    public List<AiSession> getUserSessions(Long userId) {
        return aiSessionMapper.findByCreatorIdOrderByCreatedTimeDesc(userId.toString());
    }

    @Override
    public AiSession getSessionDetail(String sessionId, Long userId) {
        // 验证会话所有权
        AiSession session = getSessionByIdAndUserId(sessionId, userId);
        if (session != null) {
            // 加载消息列表
            List<AiMessage> messages = aiMessageMapper.findBySessionIdOrderByCreatedTimeAsc(sessionId);
            session.setMessages(messages);
        }
        return session;
    }

    @Override
    @Transactional
    public boolean deleteSession(String sessionId, Long userId) {
        // 验证会话所有权
        AiSession session = getSessionByIdAndUserId(sessionId, userId);
        if (session == null) {
            return false;
        }

        // 删除会话消息
        aiMessageMapper.deleteBySessionId(sessionId);

        // 删除会话
        aiSessionMapper.deleteById(sessionId);

        return true;
    }

    @Override
    @Transactional
    public boolean clearSessionHistory(String sessionId, Long userId) {
        // 验证会话所有权
        AiSession session = getSessionByIdAndUserId(sessionId, userId);
        if (session == null) {
            return false;
        }

        // 删除会话消息
        aiMessageMapper.deleteBySessionId(sessionId);

        return true;
    }

    @Override
    public List<AiMessage> getSessionMessages(String sessionId, Long userId, Integer limit) {
        // 验证会话所有权
        AiSession session = getSessionByIdAndUserId(sessionId, userId);
        if (session == null) {
            return List.of();
        }

        if (limit != null && limit > 0) {
            return aiMessageMapper.findRecentMessagesBySessionId(sessionId, limit);
        } else {
            return aiMessageMapper.findBySessionIdOrderByCreatedTimeAsc(sessionId);
        }
    }

    @Override
    @Transactional
    public AiMessage saveUserMessage(String sessionId, String content,
                                   List<AiMessage.MediaContent> medias, Long userId) {
        AiMessage message = AiMessage.builder()
            .id(UUID.randomUUID().toString())
            .aiSessionId(sessionId)
            .creatorId(userId.toString())
            .editorId(userId.toString())
            .type(AiMessage.MessageType.USER.getCode())
            .textContent(content)
            .medias(medias)
            .createdTime(LocalDateTime.now())
            .editedTime(LocalDateTime.now())
            .build();

        aiMessageMapper.insert(message);
        return message;
    }

    @Override
    @Transactional
    public AiMessage saveAssistantMessage(String sessionId, String content, Long userId) {
        AiMessage message = AiMessage.builder()
            .id(UUID.randomUUID().toString())
            .aiSessionId(sessionId)
            .creatorId(userId.toString())
            .editorId(userId.toString())
            .type(AiMessage.MessageType.ASSISTANT.getCode())
            .textContent(content)
            .createdTime(LocalDateTime.now())
            .editedTime(LocalDateTime.now())
            .build();

        aiMessageMapper.insert(message);
        return message;
    }

    /**
     * 获取或创建会话
     */
    private AiSession getOrCreateSession(AiChatRequest request, Long userId) {
        if (StringUtils.hasText(request.getSessionId())) {
            AiSession session = getSessionByIdAndUserId(request.getSessionId(), userId);
            if (session != null) {
                return session;
            }
        }

        // 创建新会话
        return createSession(request.getSessionName(), userId);
    }

    /**
     * 根据会话ID和用户ID获取会话
     */
    private AiSession getSessionByIdAndUserId(String sessionId, Long userId) {
        QueryWrapper<AiSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("zhshtp_id", sessionId)
                   .eq("zhshtp_creator_id", userId.toString());
        return aiSessionMapper.selectOne(queryWrapper);
    }

    /**
     * 根据会话名称获取系统提示词
     */
    private String getSystemPrompt(String sessionName) {
        // 根据会话名称推断会话类型
        String sessionType = inferSessionType(sessionName);
        return aiPromptConfig.getSystemPromptBySessionType(sessionType);
    }

    /**
     * 获取RAG模式的系统提示词
     */
    private String getRAGSystemPrompt(String sessionName) {
        String basePrompt = getSystemPrompt(sessionName);
        return basePrompt + "\n\n" + """
            重要提示：
            1. 优先使用知识库中的信息来回答问题
            2. 如果知识库信息不足，可以结合通用知识补充
            3. 明确区分知识库信息和通用知识
            4. 对于商品价格、库存等实时信息，建议用户咨询客服获取最新信息
            5. 保持回答的准确性和有用性
            """;
    }

    /**
     * 根据会话名称推断会话类型
     */
    private String inferSessionType(String sessionName) {
        if (sessionName == null) {
            return "general_chat";
        }

        String lowerName = sessionName.toLowerCase();
        if (lowerName.contains("客服") || lowerName.contains("咨询") || lowerName.contains("帮助")) {
            return "customer_service";
        } else if (lowerName.contains("商品") || lowerName.contains("产品")) {
            return "product_consultation";
        } else if (lowerName.contains("订单") || lowerName.contains("物流")) {
            return "order_inquiry";
        } else {
            return "general_chat";
        }
    }

    /**
     * 获取可用的Agent列表
     */
    private String[] getAvailableAgents() {
        try {
            Map<String, Object> agentBeans = applicationContext.getBeansWithAnnotation(Agent.class);
            return agentBeans.keySet().toArray(new String[0]);
        } catch (Exception e) {
            log.warn("获取Agent列表失败", e);
            return new String[0];
        }
    }

    /**
     * 获取Agent模式的系统提示词
     */
    private String getAgentSystemPrompt(String sessionName) {
        String basePrompt = getSystemPrompt(sessionName);
        return basePrompt + "\n\n" + """
            你现在拥有强大的Agent能力，可以调用各种专业工具来帮助用户：

            🤖 可用的智能助手：
            • 商品助手：搜索商品、获取商品信息、比较商品、推荐相似商品
            • 客服助手：解答平台问题、处理投诉、解释政策规则
            • 订单助手：查询订单状态、处理退换货、跟踪物流信息

            💡 使用指南：
            1. 根据用户需求智能选择合适的工具
            2. 可以组合使用多个工具来提供完整的解决方案
            3. 始终以用户体验为中心，提供准确、有用的信息
            4. 如果工具无法解决问题，提供替代建议或人工客服联系方式

            请友好、专业地为用户提供服务！
            """;
    }

    /**
     * 保存AI回复消息
     */
    private AiMessage saveAiMessage(String sessionId, String content, Long userId) {
        AiMessage message = AiMessage.builder()
            .aiSessionId(sessionId)
            .creatorId(userId.toString())
            .editorId(userId.toString())
            .type(AiMessage.MessageType.ASSISTANT.getCode())
            .textContent(content)
            .createdTime(LocalDateTime.now())
            .editedTime(LocalDateTime.now())
            .build();

        aiMessageMapper.insert(message);
        return message;
    }
}
