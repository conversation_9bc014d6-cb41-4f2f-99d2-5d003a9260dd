{"version": 3, "sources": ["../../@codemirror/lang-liquid/dist/index.js"], "sourcesContent": ["import { syntaxTree, LRLanguage, indentNodeProp, delimitedIndent, foldNodeProp, LanguageSupport } from '@codemirror/language';\nimport { html } from '@codemirror/lang-html';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\nimport { ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { EditorSelection } from '@codemirror/state';\nimport { EditorView } from '@codemirror/view';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst interpolationStart = 1,\n  tagStart = 2,\n  endTagStart = 3,\n  text = 180,\n  endrawTagStart = 4,\n  rawText = 181,\n  endcommentTagStart = 5,\n  commentText = 182;\n\nfunction wordChar(code) {\n    return code >= 65 && code <= 90 || code >= 97 && code <= 122;\n}\nconst base = /*@__PURE__*/new ExternalTokenizer(input => {\n    let start = input.pos;\n    for (;;) {\n        let { next } = input;\n        if (next < 0)\n            break;\n        if (next == 123 /* Ch.BraceL */) {\n            let after = input.peek(1);\n            if (after == 123 /* Ch.BraceL */) {\n                if (input.pos > start)\n                    break;\n                input.acceptToken(interpolationStart, 2);\n                return;\n            }\n            else if (after == 37 /* Ch.Percent */) {\n                if (input.pos > start)\n                    break;\n                let scan = 2, size = 2;\n                for (;;) {\n                    let next = input.peek(scan);\n                    if (next == 32 /* Ch.Space */ || next == 10 /* Ch.Newline */) {\n                        ++scan;\n                    }\n                    else if (next == 35 /* Ch.Hash */) {\n                        ++scan;\n                        for (;;) {\n                            let comment = input.peek(scan);\n                            if (comment < 0 || comment == 10 /* Ch.Newline */)\n                                break;\n                            scan++;\n                        }\n                    }\n                    else if (next == 45 /* Ch.Dash */ && size == 2) {\n                        size = ++scan;\n                    }\n                    else {\n                        let end = next == 101 /* Ch.e */ && input.peek(scan + 1) == 110 /* Ch.n */ && input.peek(scan + 2) == 100 /* Ch.d */;\n                        input.acceptToken(end ? endTagStart : tagStart, size);\n                        return;\n                    }\n                }\n            }\n        }\n        input.advance();\n        if (next == 10 /* Ch.Newline */)\n            break;\n    }\n    if (input.pos > start)\n        input.acceptToken(text);\n});\nfunction rawTokenizer(endTag, text, tagStart) {\n    return new ExternalTokenizer(input => {\n        let start = input.pos;\n        for (;;) {\n            let { next } = input;\n            if (next == 123 /* Ch.BraceL */ && input.peek(1) == 37 /* Ch.Percent */) {\n                let scan = 2;\n                for (;; scan++) {\n                    let ch = input.peek(scan);\n                    if (ch != 32 /* Ch.Space */ && ch != 10 /* Ch.Newline */)\n                        break;\n                }\n                let word = \"\";\n                for (;; scan++) {\n                    let next = input.peek(scan);\n                    if (!wordChar(next))\n                        break;\n                    word += String.fromCharCode(next);\n                }\n                if (word == endTag) {\n                    if (input.pos > start)\n                        break;\n                    input.acceptToken(tagStart, 2);\n                    break;\n                }\n            }\n            else if (next < 0) {\n                break;\n            }\n            input.advance();\n            if (next == 10 /* Ch.Newline */)\n                break;\n        }\n        if (input.pos > start)\n            input.acceptToken(text);\n    });\n}\nconst comment = /*@__PURE__*/rawTokenizer(\"endcomment\", commentText, endcommentTagStart);\nconst raw = /*@__PURE__*/rawTokenizer(\"endraw\", rawText, endrawTagStart);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_identifier = {__proto__:null,contains:32, or:36, and:36, true:50, false:50, empty:52, forloop:54, tablerowloop:56, continue:58, in:128, with:194, for:196, as:198, if:234, endif:238, unless:244, endunless:248, elsif:252, else:256, case:262, endcase:266, when:270, endfor:278, tablerow:284, endtablerow:288, break:292, cycle:298, echo:302, render:306, include:312, assign:316, capture:322, endcapture:326, increment:330, decrement:334};\nconst spec_TagName = {__proto__:null,if:82, endif:86, elsif:90, else:94, unless:100, endunless:104, case:110, endcase:114, when:118, for:126, endfor:136, tablerow:142, endtablerow:146, break:150, continue:154, cycle:158, comment:164, endcomment:170, raw:176, endraw:182, echo:186, render:190, include:202, assign:206, capture:212, endcapture:216, increment:220, decrement:224, liquid:228};\nconst parser = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"GYQYOPOOOOOP'#F{'#F{OeOXO'#CdOsQWO'#CfO!bQ`O'#DQO#{OPO'#DTO$ZOPO'#D^O$iOPO'#DcO$wOPO'#DkO%VOPO'#DsO%eOSO'#EOO%jOQO'#EUO%oOPO'#EhOOOP'#G`'#G`OOOP'#G]'#G]OOOP'#Fz'#FzQYOPOOOOOP-E9y-E9yOOQO'#Cg'#CgO&`QpO,59QO&gQpO'#G^OsQWO'#CsOOQO'#G^'#G^OOOP,59l,59lO)PQWO,59lOsQWO,59pOsQWO,59tO)WQWO,59vOsQWO,59yOsQWO,5:OOsQWO,5:SO!]QWO,5:WO!]QWO,5:`O)]QWO,5:dO)bQWO,5:fO)gQWO,5:hO)lQWO,5:kO)qQWO,5:qOsQWO,5:vOsQWO,5:xOsQWO,5;OOsQWO,5;QOsQWO,5;TOsQWO,5;XOsQWO,5;ZO+QQWO,5;]O+XOPO'#CdOOOP,59o,59oO#{OPO,59oO+gQ`O'#DWOOOP,59x,59xO$ZOPO,59xO+lQ`O'#DaOOOP,59},59}O$iOPO,59}O+qQ`O'#DfOOOP,5:V,5:VO$wOPO,5:VO+vQ`O'#DqOOOP,5:_,5:_O%VOPO,5:_O+{Q`O'#DvOOOS'#GQ'#GQO,QOSO'#ERO,YOSO,5:jOOOQ'#GR'#GRO,_OQO'#EXO,gOQO,5:pOOOP,5;S,5;SO%oOPO,5;SO,lQ`O'#EkOOOP-E9x-E9xO,qQ!bO,59SOsQWO,59VOsQWO,59VO,vQWO'#C|OOQO'#F|'#F|O,{QWO1G.lOOOP1G.l1G.lOsQWO,59VOsQWO,59ZO-TQpO,59_O-fQpO1G/WOOOP1G/W1G/WO-wQpO1G/[O.YQpO1G/`OOOP1G/b1G/bO.kQpO1G/eO.|QpO1G/jO/pQpO1G/nO/wQWO1G/rO/|QWO1G/zOOOP1G0O1G0OOOOP1G0Q1G0QO0RQWO1G0SOOOS1G0V1G0VOOOQ1G0]1G0]O0^QpO1G0bO0eQpO1G0dO1PQpO1G0jO1bQpO1G0lO1sQpO1G0oO2UQpO1G0sO2gQpO1G0uO2xQWO'#EsO3PQWO'#ExO3WQWO'#FRO3_QWO'#FYO3fQWO'#F^O3mQWO'#FqOOQO'#Ga'#GaOOQO'#GT'#GTO3tQWO1G0wOsQWO'#EtOsQWO'#EyOsQWO'#E}OOQO'#FP'#FPOsQWO'#FSOsQWO'#FWO!]QWO'#FZO!]QWO'#F_OOQO'#Fc'#FcOOQO'#Fe'#FeO3{QWO'#FfOsQWO'#FhOsQWO'#FjOsQWO'#FmOsQWO'#FoOsQWO'#FrOsQWO'#FvOsQWO'#FxOOOP1G0w1G0wOOOP1G/Z1G/ZO4QQWO,59rOOOP1G/d1G/dO4VQWO,59{OOOP1G/i1G/iO4[QWO,5:QOOOP1G/q1G/qO4aQWO,5:]OOOP1G/y1G/yO4fQWO,5:bOOOS-E:O-E:OOOOP1G0U1G0UO4kQ`O'#ESOOOQ-E:P-E:POOOP1G0[1G0[O4pQ`O'#EYOOOP1G0n1G0nO4uQWO,5;VOOQO1G.n1G.nOOQO1G.q1G.qO7ZQpO1G.qOOQO'#DO'#DOO7eQWO,59hOOQO-E9z-E9zOOOP7+$W7+$WO9_QpO1G.qO9iQpO1G.uOsQWO1G.yOOOP7+$r7+$rOOOP7+$v7+$vOOOP7+$z7+$zOOOP7+%P7+%POOOP7+%U7+%UOsQWO'#F}O<OQWO7+%YOOOP7+%Y7+%YOsQWO7+%^OsQWO7+%fO<WQWO'#GPO<]QWO7+%nOOOP7+%n7+%nO<eQWO7+%nO<jQWO7+%|OOOP7+%|7+%|O!]QWO'#E`OOQO'#GS'#GSO<rQWO7+&OOsQWO'#E`OOOP7+&O7+&OOOOP7+&U7+&UOOOP7+&W7+&WOOOP7+&Z7+&ZOOOP7+&_7+&_OOOP7+&a7+&aOOQO,5;_,5;_O2xQWO,5;_OOQO'#Ev'#EvOOQO,5;d,5;dO3PQWO,5;dOOQO'#E{'#E{OOQO,5;m,5;mO3WQWO,5;mOOQO'#FU'#FUOOQO,5;t,5;tO3_QWO,5;tOOQO'#F['#F[OOQO,5;x,5;xO3fQWO,5;xOOQO'#Fa'#FaOOQO,5<],5<]O3mQWO,5<]OOQO'#Ft'#FtOOQO-E:R-E:ROOOP7+&c7+&cO=QQpO,5;`O>kQpO,5;eO@UQpO,5;iOBRQpO,5;nOClQpO,5;rOE_QWO,5;uOEdQWO,5;yOEiQWO,5<QOG`QpO,5<SOIRQpO,5<UOKRQpO,5<XOMOQpO,5<ZON{QpO,5<^O!!fQpO,5<bO!$cQpO,5<dOOOP1G/^1G/^OOOP1G/g1G/gOOOP1G/l1G/lOOOP1G/w1G/wOOOP1G/|1G/|O!&`QWO,5:nO!&eQWO,5:tOOOP1G0q1G0qOsQWO1G/SO!&jQpO7+$eO!&{QpO,5<iOOQO-E9{-E9{OOOP<<Ht<<HtO!)^QpO<<HxO!)eQpO<<IQOOQO,5<k,5<kOOQO-E9}-E9}OOOP<<IY<<IYO!)lQWO<<IYOOOP<<Ih<<IhO!)tQWO,5:zOOQO-E:Q-E:QOOOP<<Ij<<IjO!)yQpO,5:zOOQO1G0y1G0yOOQO1G1O1G1OOOQO1G1X1G1XOOQO1G1`1G1`OOQO1G1d1G1dOOQO1G1w1G1wO!*hQWO1G1^OsQWO1G1aOsQWO1G1eO!,[QWO1G1lO!.OQWO1G1lO!.TQWO1G1nO!]QWO'#FlOOQO'#GU'#GUO!/wQWO1G1pOOOP1G0Y1G0YOOOP1G0`1G0`O!1kQpO7+$nOOQO<<HP<<HPOOQO'#Dp'#DpO!3nQWO'#DoOOQO'#GO'#GOO!5XQWOAN>dOOOPAN>dAN>dO!5aQWOAN>lOOOPAN>lAN>lO!5iQWOAN>tOOOPAN>tAN>tOsQWO1G0fO!]QWO1G0fO!5qQpO7+&{O!7QQpO7+'PO!8aQWO7+'WO!:TQWO,5<WOOQO-E:S-E:SOsQWO,5:ZOOQO-E9|-E9|OOOPG24OG24OOOOPG24WG24WOOOPG24`G24`O!:YQpO7+&QOOQO7+&Q7+&QO!:tQWO<<JgO!<UQWO<<JkO!=fQWO<<JrOsQWO1G1rO!?YQpO1G/uO!@|QpO7+'^\",\n  stateData: \"!B|~O%OOSUOS~OPROQSO$zPO~O$zPOPWXQWX$yWX~OfeOifOjfOkfOlfOmfOnfOofO%RbO~OuhOvgOyiO}jO!PkO!SlO!XmO!]nO!aoO!ipO!mqO!orO!qsO!ttO!zuO#PvO#RwO#XxO#ZyO#^zO#b{O#d|O#f}O~OPROQSOR!RO$zPO~OPROQSOR!UO$zPO~OPROQSOR!XO$zPO~OPROQSOR![O$zPO~OPROQSOR!_O$zPO~O$|!`O~O${!cO~OPROQSOR!hO$zPO~O]!jO`!qOa!kOb!lOq!mO~OX!pO~P%}Od!rOX%QX]%QX`%QXa%QXb%QXq%QXh%QXv%QX!^%QX#T%QX#U%QXm%QX#i%QX#k%QX#n%QX#r%QX#t%QX#w%QX#{%QX$S%QX$W%QX$Z%QX$]%QX$_%QX$b%QX$d%QX$g%QX$k%QX$m%QX#p%QX#y%QX$i%QXe%QX%R%QX#V%QX$P%QX$U%QX~Ov!uO~PsOv!xO~Ov#OO~Ov#PO~On#QO~Ov#RO~Ov#SO~Om#nO#U#kO#i#eO#n#fO#r#gO#t#hO#w#iO#{#jO$S#lO$W#mO$Z#oO$]#pO$_#qO$b#rO$d#sO$g#tO$k#uO$m#vO~Ov#wO~P)vO$zPOPWXQWXRWX~O{#yO~O!U#{O~O!Z#}O~O!f$PO~O!k$RO~O$|!`OT!uX~OT$UO~O${!cOS!{X~OS$XO~O#`$ZO~O^$[O~O%R$_O~OX$bOq!mO~O]!jO`!qOa!kOb!lOh$eO~O]!jO`!qOa!kOb!lOv$fO~O]!jO`!qOa!kOb!lOv$gO~O]!jO`!qOa!kOb!lOv$hO~O]!jO`!qOa!kOb!lOv$iO~O]!jO`!qOa!kOb!lOv$jO~O]!jO`!qOa!kOb!lO!^$kO~Ov$mO~P/_O!b$nO~O!b$oO~Os$sOv$rO!^$pO~Ov$uO~P%}O]!jO`!qOa!kOb!lOv$zO!^$vO#T$yO#U$yO~O]!jO`!qOa!kOb!lOv${O~O]!jO`!qOa!kOb!lOv$|O~O]!jO`!qOa!kOb!lOv$}O~O]!jO`!qOa!kOb!lOv%OO~O]!jO`!qOa!kOb!lOv%PO~O#k%SO~P)vO#p%VO~P)vO#y%YO~P)vO$P%]O~P)vO$U%`O~P)vO$i%cO~P)vOv%eO~P)vOn%mO~Ov%uO~Ov%vO~Ov%wO~Ov%xO~Ov%yO~O!w%zO~O!}%{O~Ov%|O~Oa!kOX_i]_iq_ih_iv_i!^_i#T_i#U_im_i#i_i#k_i#n_i#r_i#t_i#w_i#{_i$S_i$W_i$Z_i$]_i$__i$b_i$d_i$g_i$k_i$m_i#p_i#y_i$i_ie_i%R_i#V_i$P_i$U_i~O`!qOb!lO~P4zOs%}OXpaqpavpampa#Upa#ipa#npa#rpa#tpa#wpa#{pa$Spa$Wpa$Zpa$]pa$_pa$bpa$dpa$gpa$kpa$mpa#kpa#ppa#ypa$Ppa$Upa$ipa~O`_ib_i~P4zO`!qOa!kOb!lOXci]ciqcihcivci!^ci#Tci#Ucimci#ici#kci#nci#rci#tci#wci#{ci$Sci$Wci$Zci$]ci$_ci$bci$dci$gci$kci$mci#pci#yci$icieci%Rci#Vci$Pci$Uci~Ov&RO!^$kO~On&UO~Ov&WO!^$pO~On&XO~Oq!mOv&YO~Ov&]O!^$vO#T$yO#U$yO~O]!jO`!qOa!kOb!lOm#ha#U#ha#i#ha#k#ha#n#ha#r#ha#t#ha#w#ha#{#ha$S#ha$W#ha$Z#ha$]#ha$_#ha$b#ha$d#ha$g#ha$k#ha$m#ha~O]!jO`!qOa!kOb!lOm#ma#U#ma#i#ma#n#ma#p#ma#r#ma#t#ma#w#ma#{#ma$S#ma$W#ma$Z#ma$]#ma$_#ma$b#ma$d#ma$g#ma$k#ma$m#ma~O]!jO`!qOa!kOb!lOm#qav#qa#U#qa#i#qa#n#qa#r#qa#t#qa#w#qa#{#qa$S#qa$W#qa$Z#qa$]#qa$_#qa$b#qa$d#qa$g#qa$k#qa$m#qa#k#qa#p#qa#y#qa$P#qa$U#qa$i#qa~O]!jO`!qOa!kOb!lOm#va#U#va#i#va#n#va#r#va#t#va#w#va#y#va#{#va$S#va$W#va$Z#va$]#va$_#va$b#va$d#va$g#va$k#va$m#va~Om#zav#za#U#za#i#za#n#za#r#za#t#za#w#za#{#za$S#za$W#za$Z#za$]#za$_#za$b#za$d#za$g#za$k#za$m#za#k#za#p#za#y#za$P#za$U#za$i#za~P/_O!b&fO~O!b&gO~Os&iO!^$pOm$Yav$Ya#U$Ya#i$Ya#n$Ya#r$Ya#t$Ya#w$Ya#{$Ya$S$Ya$W$Ya$Z$Ya$]$Ya$_$Ya$b$Ya$d$Ya$g$Ya$k$Ya$m$Ya#k$Ya#p$Ya#y$Ya$P$Ya$U$Ya$i$Ya~Om$[av$[a#U$[a#i$[a#n$[a#r$[a#t$[a#w$[a#{$[a$S$[a$W$[a$Z$[a$]$[a$_$[a$b$[a$d$[a$g$[a$k$[a$m$[a#k$[a#p$[a#y$[a$P$[a$U$[a$i$[a~P%}O]!jO`!qOa!kOb!lO!^&kOm$^av$^a#U$^a#i$^a#n$^a#r$^a#t$^a#w$^a#{$^a$S$^a$W$^a$Z$^a$]$^a$_$^a$b$^a$d$^a$g$^a$k$^a$m$^a#k$^a#p$^a#y$^a$P$^a$U$^a$i$^a~O]!jO`!qOa!kOb!lOm$aav$aa#U$aa#i$aa#n$aa#r$aa#t$aa#w$aa#{$aa$S$aa$W$aa$Z$aa$]$aa$_$aa$b$aa$d$aa$g$aa$k$aa$m$aa#k$aa#p$aa#y$aa$P$aa$U$aa$i$aa~O]!jO`!qOa!kOb!lOm$cav$ca#U$ca#i$ca#n$ca#r$ca#t$ca#w$ca#{$ca$S$ca$W$ca$Z$ca$]$ca$_$ca$b$ca$d$ca$g$ca$k$ca$m$ca#k$ca#p$ca#y$ca$P$ca$U$ca$i$ca~O]!jO`!qOa!kOb!lOm$fa#U$fa#i$fa#n$fa#r$fa#t$fa#w$fa#{$fa$S$fa$W$fa$Z$fa$]$fa$_$fa$b$fa$d$fa$g$fa$i$fa$k$fa$m$fa~O]!jO`!qOa!kOb!lOm$jav$ja#U$ja#i$ja#n$ja#r$ja#t$ja#w$ja#{$ja$S$ja$W$ja$Z$ja$]$ja$_$ja$b$ja$d$ja$g$ja$k$ja$m$ja#k$ja#p$ja#y$ja$P$ja$U$ja$i$ja~O]!jO`!qOa!kOb!lOm$lav$la#U$la#i$la#n$la#r$la#t$la#w$la#{$la$S$la$W$la$Z$la$]$la$_$la$b$la$d$la$g$la$k$la$m$la#k$la#p$la#y$la$P$la$U$la$i$la~Ov&nO~Ov&oO~O]!jO`!qOa!kOb!lOe&qO~O]!jO`!qOa!kOb!lOv$qa!^$qam$qa#U$qa#i$qa#n$qa#r$qa#t$qa#w$qa#{$qa$S$qa$W$qa$Z$qa$]$qa$_$qa$b$qa$d$qa$g$qa$k$qa$m$qa#k$qa#p$qa#y$qa$P$qa$U$qa$i$qa~O]!jO`!qOa!kOb!lO%R&rO~Ov&vO~P!({Ov&xO~P!({Ov&zO!^$pO~Os&{O~O]!jO`!qOa!kOb!lO#V&|Ov#Sa!^#Sa#T#Sa#U#Sa~O!^$kOm#ziv#zi#U#zi#i#zi#n#zi#r#zi#t#zi#w#zi#{#zi$S#zi$W#zi$Z#zi$]#zi$_#zi$b#zi$d#zi$g#zi$k#zi$m#zi#k#zi#p#zi#y#zi$P#zi$U#zi$i#zi~O!^$pOm$Yiv$Yi#U$Yi#i$Yi#n$Yi#r$Yi#t$Yi#w$Yi#{$Yi$S$Yi$W$Yi$Z$Yi$]$Yi$_$Yi$b$Yi$d$Yi$g$Yi$k$Yi$m$Yi#k$Yi#p$Yi#y$Yi$P$Yi$U$Yi$i$Yi~On'PO~Oq!mOm$[iv$[i#U$[i#i$[i#n$[i#r$[i#t$[i#w$[i#{$[i$S$[i$W$[i$Z$[i$]$[i$_$[i$b$[i$d$[i$g$[i$k$[i$m$[i#k$[i#p$[i#y$[i$P$[i$U$[i$i$[i~O!^&kOm$^iv$^i#U$^i#i$^i#n$^i#r$^i#t$^i#w$^i#{$^i$S$^i$W$^i$Z$^i$]$^i$_$^i$b$^i$d$^i$g$^i$k$^i$m$^i#k$^i#p$^i#y$^i$P$^i$U$^i$i$^i~O]!jO`!qOa!kOb!lOXpqqpqvpqmpq#Upq#ipq#npq#rpq#tpq#wpq#{pq$Spq$Wpq$Zpq$]pq$_pq$bpq$dpq$gpq$kpq$mpq#kpq#ppq#ypq$Ppq$Upq$ipq~Os'SOv!cX%R!cXm!cX#U!cX#i!cX#n!cX#r!cX#t!cX#w!cX#{!cX$P!cX$S!cX$W!cX$Z!cX$]!cX$_!cX$b!cX$d!cX$g!cX$k!cX$m!cX$U!cX~Ov'UO%R&rO~Ov'VO%R&rO~Ov'WO!^$pO~Om#}q#U#}q#i#}q#n#}q#r#}q#t#}q#w#}q#{#}q$P#}q$S#}q$W#}q$Z#}q$]#}q$_#}q$b#}q$d#}q$g#}q$k#}q$m#}q~P!({Om$Rq#U$Rq#i$Rq#n$Rq#r$Rq#t$Rq#w$Rq#{$Rq$S$Rq$U$Rq$W$Rq$Z$Rq$]$Rq$_$Rq$b$Rq$d$Rq$g$Rq$k$Rq$m$Rq~P!({O!^$pOm$Yqv$Yq#U$Yq#i$Yq#n$Yq#r$Yq#t$Yq#w$Yq#{$Yq$S$Yq$W$Yq$Z$Yq$]$Yq$_$Yq$b$Yq$d$Yq$g$Yq$k$Yq$m$Yq#k$Yq#p$Yq#y$Yq$P$Yq$U$Yq$i$Yq~Os'^O~O]!jO`!qOa!kOb!lOv#Sq!^#Sq#T#Sq#U#Sq~O%R&rOm#}y#U#}y#i#}y#n#}y#r#}y#t#}y#w#}y#{#}y$P#}y$S#}y$W#}y$Z#}y$]#}y$_#}y$b#}y$d#}y$g#}y$k#}y$m#}y~O%R&rOm$Ry#U$Ry#i$Ry#n$Ry#r$Ry#t$Ry#w$Ry#{$Ry$S$Ry$U$Ry$W$Ry$Z$Ry$]$Ry$_$Ry$b$Ry$d$Ry$g$Ry$k$Ry$m$Ry~O!^$pOm$Yyv$Yy#U$Yy#i$Yy#n$Yy#r$Yy#t$Yy#w$Yy#{$Yy$S$Yy$W$Yy$Z$Yy$]$Yy$_$Yy$b$Yy$d$Yy$g$Yy$k$Yy$m$Yy#k$Yy#p$Yy#y$Yy$P$Yy$U$Yy$i$Yy~O]!jO`!qOa!kOb!lOv!ci%R!cim!ci#U!ci#i!ci#n!ci#r!ci#t!ci#w!ci#{!ci$P!ci$S!ci$W!ci$Z!ci$]!ci$_!ci$b!ci$d!ci$g!ci$k!ci$m!ci$U!ci~O]!jO`!qOa!kOb!lOm$`qv$`q!^$`q#U$`q#i$`q#n$`q#r$`q#t$`q#w$`q#{$`q$S$`q$W$`q$Z$`q$]$`q$_$`q$b$`q$d$`q$g$`q$k$`q$m$`q#k$`q#p$`q#y$`q$P$`q$U$`q$i$`q~O\",\n  goto: \"7V%UPPPPPPPP%VP%V%g&zPP&zPPP&zPPP&zPPPPPPPP'xP(QP(TPP(T(eP(uP(TP(TP(T({P)]P(T)cP)sP(TPP(T)yPP*Z*e*oP(T*uP+VP(TP(TP(TP(T+]P+m+pP(T+sP,T,WP(TP(TP,ZPPP(TP(TP(T,_P,oP(TP(TP(TP,u-VP-gP,u-mP-}P,uP,uP,u.TP.eP,uP,u.k.{P,u/RP/cP,uP,u,uP,uP,uP/i,uP,uP,u/mP/}P,uP,uP0T0s1Z1i1s2V2i2o2u2{3kPPPPPP3q4RP%V6um^OTUVWX[`!Q!T!W!Z!^!g!vdRehijlmnvwxyz{|!k!l!q!r#e#f#g#i#j#p#q#r#s#t#u#v$e$k$n$o$y%}&f&g&{'S'^Q!|oQ!}pQ%k#kQ%l#lQ&Z$vQ'Q&kR'Y&|!wfRehijlmnvwxyz{|!k!l!q!r#e#f#g#i#j#p#q#r#s#t#u#v$e$k$n$o$y%}&f&g&{'S'^]!nc!o#T$t%n&jR$`!mm]OTUVWX[`!Q!T!W!Z!^!gmTOTUVWX[`!Q!T!W!Z!^!gQ!PTR#x!QmUOTUVWX[`!Q!T!W!Z!^!gQ!SUR#z!TmVOTUVWX[`!Q!T!W!Z!^!gQ!VVR#|!WmWOTUVWX[`!Q!T!W!Z!^!ga&t&S&T&u&w&}'O'Z'[a&s&S&T&u&w&}'O'Z'[Q!YWR$O!ZmXOTUVWX[`!Q!T!W!Z!^!gQ!]XR$Q!^mYOTUVWX[`!Q!T!W!Z!^!gR!bYR$T!bmZOTUVWX[`!Q!T!W!Z!^!gR!eZR$W!eT$w#U$xm[OTUVWX[`!Q!T!W!Z!^!gQ!f[R$Y!gm#b}#[#]#^#_#`#a#d%R%U%X%[%_%bm#[}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%Q#[R&_%Rm#]}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%T#]R&`%Um#^}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%W#^R&a%Xm#_}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%Z#_R&b%[m#`}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%^#`R&c%_T&l%o&mm#a}#[#]#^#_#`#a#d%R%U%X%[%_%bQ%a#aR&d%bQ`OQ!QTQ!TUQ!WVQ!ZWQ!^XQ!g[_!i`!Q!T!W!Z!^!gSQO`SaQ!Oi!OTUVWX[!Q!T!W!Z!^!gQ!ocU$a!o$t&jQ$t#TR&j%nQ$l!{S&Q$l&eR&e%jQ&u&SQ&w&TW'T&u&w'Z'[Q'Z&}R'['OQ$q#QW&V$q&h&y']Q&h%mQ&y&XR']'PQ!aYR$S!aQ!dZR$V!dQ$x#UR&[$xQ#d}Q%R#[Q%U#]Q%X#^Q%[#_Q%_#`Q%b#a_%d#d%R%U%X%[%_%bQ&m%oR'R&mm_OTUVWX[`!Q!T!W!Z!^!gQcRQ!seQ!thQ!viQ!wjQ!ylQ!zmQ!{nQ#TvQ#UwQ#VxQ#WyQ#XzQ#Y{Q#Z|Q$]!kQ$^!lQ$c!qQ$d!rQ%f#eQ%g#fQ%h#gQ%i#iQ%j#jQ%n#pQ%o#qQ%p#rQ%q#sQ%r#tQ%s#uQ%t#vQ&O$eQ&P$kQ&S$nQ&T$oQ&^$yQ&p%}Q&}&fQ'O&gQ'X&{Q'_'SR'`'^m#c}#[#]#^#_#`#a#d%R%U%X%[%_%b\",\n  nodeNames: \"⚠ {{ {% {% {% {% InlineComment Template Text }} Interpolation VariableName MemberExpression . PropertyName BinaryExpression contains CompareOp LogicOp AssignmentExpression AssignOp ) ( RangeExpression .. BooleanLiteral empty forloop tablerowloop continue StringLiteral NumberLiteral Filter | FilterName : Tag TagName %} IfDirective Tag if EndTag endif Tag elsif Tag else UnlessDirective Tag unless EndTag endunless CaseDirective Tag case EndTag endcase Tag when , ForDirective Tag for in Parameter ParameterName EndTag endfor TableDirective Tag tablerow EndTag endtablerow Tag break Tag continue Tag cycle Comment Tag comment CommentText EndTag endcomment RawDirective Tag raw RawText EndTag endraw Tag echo Tag render RenderParameter with for as Tag include Tag assign CaptureDirective Tag capture EndTag endcapture Tag increment Tag decrement Tag liquid IfDirective Tag if EndTag endif UnlessDirective Tag unless EndTag endunless Tag elsif Tag else CaseDirective Tag case EndTag endcase Tag when ForDirective Tag EndTag endfor TableDirective Tag tablerow EndTag endtablerow Tag break Tag Tag cycle Tag echo Tag render RenderParameter Tag include Tag assign CaptureDirective Tag capture EndTag endcapture Tag increment Tag decrement\",\n  maxTerm: 189,\n  nodeProps: [\n    [\"closedBy\", 1,\"}}\",-4,2,3,4,5,\"%}\",22,\")\"],\n    [\"openedBy\", 9,\"{{\",21,\"(\",38,\"{%\"],\n    [\"group\", -12,11,12,15,19,23,25,26,27,28,29,30,31,\"Expression\"]\n  ],\n  skippedNodes: [0,6],\n  repeatNodeCount: 11,\n  tokenData: \")l~RlXY!yYZ!y]^!ypq!yqr#[rs#gst$Xuv$pwx${xy%hyz%m{|%r|}'^}!O'c!O!P'o!Q![&{![!](P!^!_(U!_!`(^!`!a(U!c!}(f#R#S(f#T#o(f#p#q)[#q#r)a%W;'S(f;'S;:j)U<%lO(f~#OS%O~XY!yYZ!y]^!ypq!y~#_P!_!`#b~#gOa~~#jUOY#gZr#grs#|s;'S#g;'S;=`$R<%lO#g~$ROn~~$UP;=`<%l#g~$^SU~OY$XZ;'S$X;'S;=`$j<%lO$X~$mP;=`<%l$X~$sP#q#r$v~${Ov~~%OUOY${Zw${wx#|x;'S${;'S;=`%b<%lO${~%eP;=`<%l${~%mOf~~%rOe~P%uQ!O!P%{!Q![&{P&OP!Q![&RP&WRoP!Q![&R!g!h&a#X#Y&aP&dR{|&m}!O&m!Q![&sP&pP!Q![&sP&xPoP!Q![&sP'QSoP!O!P%{!Q![&{!g!h&a#X#Y&a~'cO!^~~'fRuv$p!O!P%{!Q![&{~'tQ]S!O!P'z!Q![&R~(POh~~(UOs~~(ZPa~!_!`#b~(cPd~!_!`#b_(oV^WuQ%RT!Q![(f!c!}(f#R#S(f#T#o(f%W;'S(f;'S;:j)U<%lO(f_)XP;=`<%l(f~)aOq~~)dP#q#r)g~)lOX~\",\n  tokenizers: [base, raw, comment, 0, 1, 2, 3],\n  topRules: {\"Template\":[0,7]},\n  specialized: [{term: 187, get: (value) => spec_identifier[value] || -1},{term: 37, get: (value) => spec_TagName[value] || -1}],\n  tokenPrec: 0\n});\n\nfunction completions(words, type) {\n    return words.split(\" \").map(label => ({ label, type }));\n}\nconst Filters = /*@__PURE__*/completions(\"abs append at_least at_most capitalize ceil compact concat date default \" +\n    \"divided_by downcase escape escape_once first floor join last lstrip map minus modulo \" +\n    \"newline_to_br plus prepend remove remove_first replace replace_first reverse round rstrip \" +\n    \"size slice sort sort_natural split strip strip_html strip_newlines sum times truncate \" +\n    \"truncatewords uniq upcase url_decode url_encode where\", \"function\");\nconst Tags = /*@__PURE__*/completions(\"cycle comment endcomment raw endraw echo increment decrement liquid if elsif \" +\n    \"else endif unless endunless case endcase for endfor tablerow endtablerow break continue \" +\n    \"assign capture endcapture render include\", \"keyword\");\nconst Expressions = /*@__PURE__*/completions(\"empty forloop tablerowloop in with as contains\", \"keyword\");\nconst forloop = /*@__PURE__*/completions(\"first index index0 last length rindex\", \"property\");\nconst tablerowloop = /*@__PURE__*/completions(\"col col0 col_first col_last first index index0 last length rindex rindex0 row\", \"property\");\nfunction findContext(context) {\n    var _a;\n    let { state, pos } = context;\n    let node = syntaxTree(state).resolveInner(pos, -1).enterUnfinishedNodesBefore(pos);\n    let before = ((_a = node.childBefore(pos)) === null || _a === void 0 ? void 0 : _a.name) || node.name;\n    if (node.name == \"FilterName\")\n        return { type: \"filter\", node };\n    if (context.explicit && before == \"|\")\n        return { type: \"filter\" };\n    if (node.name == \"TagName\")\n        return { type: \"tag\", node };\n    if (context.explicit && before == \"{%\")\n        return { type: \"tag\" };\n    if (node.name == \"PropertyName\" && node.parent.name == \"MemberExpression\")\n        return { type: \"property\", node, target: node.parent };\n    if (node.name == \".\" && node.parent.name == \"MemberExpression\")\n        return { type: \"property\", target: node.parent };\n    if (node.name == \"MemberExpression\" && before == \".\")\n        return { type: \"property\", target: node };\n    if (node.name == \"VariableName\")\n        return { type: \"expression\", from: node.from };\n    let word = context.matchBefore(/[\\w\\u00c0-\\uffff]+$/);\n    if (word)\n        return { type: \"expression\", from: word.from };\n    if (context.explicit && node.name != \"CommentText\" && node.name != \"StringLiteral\" &&\n        node.name != \"NumberLiteral\" && node.name != \"InlineComment\")\n        return { type: \"expression\" };\n    return null;\n}\nfunction resolveProperties(state, node, context, properties) {\n    let path = [];\n    for (;;) {\n        let obj = node.getChild(\"Expression\");\n        if (!obj)\n            return [];\n        if (obj.name == \"forloop\") {\n            return path.length ? [] : forloop;\n        }\n        else if (obj.name == \"tablerowloop\") {\n            return path.length ? [] : tablerowloop;\n        }\n        else if (obj.name == \"VariableName\") {\n            path.unshift(state.sliceDoc(obj.from, obj.to));\n            break;\n        }\n        else if (obj.name == \"MemberExpression\") {\n            let name = obj.getChild(\"PropertyName\");\n            if (name)\n                path.unshift(state.sliceDoc(name.from, name.to));\n            node = obj;\n        }\n        else {\n            return [];\n        }\n    }\n    return properties ? properties(path, state, context) : [];\n}\n/**\nReturns a completion source for liquid templates. Optionally takes\na configuration that adds additional custom completions.\n*/\nfunction liquidCompletionSource(config = {}) {\n    let filters = config.filters ? config.filters.concat(Filters) : Filters;\n    let tags = config.tags ? config.tags.concat(Tags) : Tags;\n    let exprs = config.variables ? config.variables.concat(Expressions) : Expressions;\n    let { properties } = config;\n    return (context) => {\n        var _a;\n        let cx = findContext(context);\n        if (!cx)\n            return null;\n        let from = (_a = cx.from) !== null && _a !== void 0 ? _a : (cx.node ? cx.node.from : context.pos);\n        let options;\n        if (cx.type == \"filter\")\n            options = filters;\n        else if (cx.type == \"tag\")\n            options = tags;\n        else if (cx.type == \"expression\")\n            options = exprs;\n        else /* property */\n            options = resolveProperties(context.state, cx.target, context, properties);\n        return options.length ? { options, from, validFor: /^[\\w\\u00c0-\\uffff]*$/ } : null;\n    };\n}\n/**\nThis extension will, when the user types a `%` between two\nmatching braces, insert two percent signs instead and put the\ncursor between them.\n*/\nconst closePercentBrace = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text) => {\n    if (text != \"%\" || from != to || view.state.doc.sliceString(from - 1, to + 1) != \"{}\")\n        return false;\n    view.dispatch(view.state.changeByRange(range => ({\n        changes: { from: range.from, to: range.to, insert: \"%%\" },\n        range: EditorSelection.cursor(range.from + 1)\n    })), {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n    return true;\n});\n\nfunction directiveIndent(except) {\n    return (context) => {\n        let back = except.test(context.textAfter);\n        return context.lineIndent(context.node.from) + (back ? 0 : context.unit);\n    };\n}\nconst tagLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"liquid\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/styleTags({\n                \"cycle comment endcomment raw endraw echo increment decrement liquid in with as\": tags.keyword,\n                \"empty forloop tablerowloop\": tags.atom,\n                \"if elsif else endif unless endunless case endcase for endfor tablerow endtablerow break continue\": tags.controlKeyword,\n                \"assign capture endcapture\": tags.definitionKeyword,\n                \"contains\": tags.operatorKeyword,\n                \"render include\": tags.moduleKeyword,\n                VariableName: tags.variableName,\n                TagName: tags.tagName,\n                FilterName: /*@__PURE__*/tags.function(tags.variableName),\n                PropertyName: tags.propertyName,\n                CompareOp: tags.compareOperator,\n                AssignOp: tags.definitionOperator,\n                LogicOp: tags.logicOperator,\n                NumberLiteral: tags.number,\n                StringLiteral: tags.string,\n                BooleanLiteral: tags.bool,\n                InlineComment: tags.lineComment,\n                CommentText: tags.blockComment,\n                \"{% %} {{ }}\": tags.brace,\n                \"( )\": tags.paren,\n                \".\": tags.derefOperator,\n                \", .. : |\": tags.punctuation\n            }),\n            /*@__PURE__*/indentNodeProp.add({\n                Tag: /*@__PURE__*/delimitedIndent({ closing: \"%}\" }),\n                \"UnlessDirective ForDirective TablerowDirective CaptureDirective\": /*@__PURE__*/directiveIndent(/^\\s*(\\{%-?\\s*)?end\\w/),\n                IfDirective: /*@__PURE__*/directiveIndent(/^\\s*(\\{%-?\\s*)?(endif|else|elsif)\\b/),\n                CaseDirective: /*@__PURE__*/directiveIndent(/^\\s*(\\{%-?\\s*)?(endcase|when)\\b/),\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"UnlessDirective ForDirective TablerowDirective CaptureDirective IfDirective CaseDirective RawDirective Comment\"(tree) {\n                    let first = tree.firstChild, last = tree.lastChild;\n                    if (!first || first.name != \"Tag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"EndTag\" ? last.from : tree.to };\n                }\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"#\" },\n        indentOnInput: /^\\s*{%-?\\s*(?:end|elsif|else|when|)$/\n    }\n});\nconst baseHTML = /*@__PURE__*/html();\nfunction makeLiquid(base) {\n    return tagLanguage.configure({\n        wrap: parseMixed(node => node.type.isTop ? {\n            parser: base.parser,\n            overlay: n => n.name == \"Text\" || n.name == \"RawText\"\n        } : null)\n    }, \"liquid\");\n}\n/**\nA language provider for Liquid templates.\n*/\nconst liquidLanguage = /*@__PURE__*/makeLiquid(baseHTML.language);\n/**\nLiquid template support.\n*/\nfunction liquid(config = {}) {\n    let base = config.base || baseHTML;\n    let lang = base.language == baseHTML.language ? liquidLanguage : makeLiquid(base.language);\n    return new LanguageSupport(lang, [\n        base.support,\n        lang.data.of({ autocomplete: liquidCompletionSource(config) }),\n        base.language.data.of({ closeBrackets: { brackets: [\"{\"] } }),\n        closePercentBrace\n    ]);\n}\n\nexport { closePercentBrace, liquid, liquidCompletionSource, liquidLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,qBAAqB;AAA3B,IACE,WAAW;AADb,IAEE,cAAc;AAFhB,IAGE,OAAO;AAHT,IAIE,iBAAiB;AAJnB,IAKE,UAAU;AALZ,IAME,qBAAqB;AANvB,IAOE,cAAc;AAEhB,SAAS,SAAS,MAAM;AACpB,SAAO,QAAQ,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ;AAC7D;AACA,IAAM,OAAoB,IAAI,kBAAkB,WAAS;AACrD,MAAI,QAAQ,MAAM;AAClB,aAAS;AACL,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,OAAO;AACP;AACJ,QAAI,QAAQ,KAAqB;AAC7B,UAAI,QAAQ,MAAM,KAAK,CAAC;AACxB,UAAI,SAAS,KAAqB;AAC9B,YAAI,MAAM,MAAM;AACZ;AACJ,cAAM,YAAY,oBAAoB,CAAC;AACvC;AAAA,MACJ,WACS,SAAS,IAAqB;AACnC,YAAI,MAAM,MAAM;AACZ;AACJ,YAAI,OAAO,GAAG,OAAO;AACrB,mBAAS;AACL,cAAIA,QAAO,MAAM,KAAK,IAAI;AAC1B,cAAIA,SAAQ,MAAqBA,SAAQ,IAAqB;AAC1D,cAAE;AAAA,UACN,WACSA,SAAQ,IAAkB;AAC/B,cAAE;AACF,uBAAS;AACL,kBAAIC,WAAU,MAAM,KAAK,IAAI;AAC7B,kBAAIA,WAAU,KAAKA,YAAW;AAC1B;AACJ;AAAA,YACJ;AAAA,UACJ,WACSD,SAAQ,MAAoB,QAAQ,GAAG;AAC5C,mBAAO,EAAE;AAAA,UACb,OACK;AACD,gBAAI,MAAMA,SAAQ,OAAkB,MAAM,KAAK,OAAO,CAAC,KAAK,OAAkB,MAAM,KAAK,OAAO,CAAC,KAAK;AACtG,kBAAM,YAAY,MAAM,cAAc,UAAU,IAAI;AACpD;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,QAAQ;AACd,QAAI,QAAQ;AACR;AAAA,EACR;AACA,MAAI,MAAM,MAAM;AACZ,UAAM,YAAY,IAAI;AAC9B,CAAC;AACD,SAAS,aAAa,QAAQE,OAAMC,WAAU;AAC1C,SAAO,IAAI,kBAAkB,WAAS;AAClC,QAAI,QAAQ,MAAM;AAClB,eAAS;AACL,UAAI,EAAE,KAAK,IAAI;AACf,UAAI,QAAQ,OAAuB,MAAM,KAAK,CAAC,KAAK,IAAqB;AACrE,YAAI,OAAO;AACX,iBAAQ,QAAQ;AACZ,cAAI,KAAK,MAAM,KAAK,IAAI;AACxB,cAAI,MAAM,MAAqB,MAAM;AACjC;AAAA,QACR;AACA,YAAI,OAAO;AACX,iBAAQ,QAAQ;AACZ,cAAIH,QAAO,MAAM,KAAK,IAAI;AAC1B,cAAI,CAAC,SAASA,KAAI;AACd;AACJ,kBAAQ,OAAO,aAAaA,KAAI;AAAA,QACpC;AACA,YAAI,QAAQ,QAAQ;AAChB,cAAI,MAAM,MAAM;AACZ;AACJ,gBAAM,YAAYG,WAAU,CAAC;AAC7B;AAAA,QACJ;AAAA,MACJ,WACS,OAAO,GAAG;AACf;AAAA,MACJ;AACA,YAAM,QAAQ;AACd,UAAI,QAAQ;AACR;AAAA,IACR;AACA,QAAI,MAAM,MAAM;AACZ,YAAM,YAAYD,KAAI;AAAA,EAC9B,CAAC;AACL;AACA,IAAM,UAAuB,aAAa,cAAc,aAAa,kBAAkB;AACvF,IAAM,MAAmB,aAAa,UAAU,SAAS,cAAc;AAGvE,IAAM,kBAAkB,EAAC,WAAU,MAAK,UAAS,IAAI,IAAG,IAAI,KAAI,IAAI,MAAK,IAAI,OAAM,IAAI,OAAM,IAAI,SAAQ,IAAI,cAAa,IAAI,UAAS,IAAI,IAAG,KAAK,MAAK,KAAK,KAAI,KAAK,IAAG,KAAK,IAAG,KAAK,OAAM,KAAK,QAAO,KAAK,WAAU,KAAK,OAAM,KAAK,MAAK,KAAK,MAAK,KAAK,SAAQ,KAAK,MAAK,KAAK,QAAO,KAAK,UAAS,KAAK,aAAY,KAAK,OAAM,KAAK,OAAM,KAAK,MAAK,KAAK,QAAO,KAAK,SAAQ,KAAK,QAAO,KAAK,SAAQ,KAAK,YAAW,KAAK,WAAU,KAAK,WAAU,IAAG;AAC3b,IAAM,eAAe,EAAC,WAAU,MAAK,IAAG,IAAI,OAAM,IAAI,OAAM,IAAI,MAAK,IAAI,QAAO,KAAK,WAAU,KAAK,MAAK,KAAK,SAAQ,KAAK,MAAK,KAAK,KAAI,KAAK,QAAO,KAAK,UAAS,KAAK,aAAY,KAAK,OAAM,KAAK,UAAS,KAAK,OAAM,KAAK,SAAQ,KAAK,YAAW,KAAK,KAAI,KAAK,QAAO,KAAK,MAAK,KAAK,QAAO,KAAK,SAAQ,KAAK,QAAO,KAAK,SAAQ,KAAK,YAAW,KAAK,WAAU,KAAK,WAAU,KAAK,QAAO,IAAG;AACnY,IAAM,SAAsB,SAAS,YAAY;AAAA,EAC/C,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,YAAY,GAAE,MAAK,IAAG,GAAE,GAAE,GAAE,GAAE,MAAK,IAAG,GAAG;AAAA,IAC1C,CAAC,YAAY,GAAE,MAAK,IAAG,KAAI,IAAG,IAAI;AAAA,IAClC,CAAC,SAAS,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,YAAY;AAAA,EAChE;AAAA,EACA,cAAc,CAAC,GAAE,CAAC;AAAA,EAClB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,MAAM,KAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC3C,UAAU,EAAC,YAAW,CAAC,GAAE,CAAC,EAAC;AAAA,EAC3B,aAAa,CAAC,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,gBAAgB,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,IAAI,KAAK,CAAC,UAAU,aAAa,KAAK,KAAK,GAAE,CAAC;AAAA,EAC7H,WAAW;AACb,CAAC;AAED,SAAS,YAAY,OAAO,MAAM;AAC9B,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,YAAU,EAAE,OAAO,KAAK,EAAE;AAC1D;AACA,IAAM,UAAuB,YAAY,sYAIoB,UAAU;AACvE,IAAM,OAAoB,YAAY,iNAEU,SAAS;AACzD,IAAM,cAA2B,YAAY,kDAAkD,SAAS;AACxG,IAAM,UAAuB,YAAY,yCAAyC,UAAU;AAC5F,IAAM,eAA4B,YAAY,iFAAiF,UAAU;AACzI,SAAS,YAAY,SAAS;AAC1B,MAAI;AACJ,MAAI,EAAE,OAAO,IAAI,IAAI;AACrB,MAAI,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE,EAAE,2BAA2B,GAAG;AACjF,MAAI,WAAW,KAAK,KAAK,YAAY,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,KAAK;AACjG,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,UAAU,KAAK;AAClC,MAAI,QAAQ,YAAY,UAAU;AAC9B,WAAO,EAAE,MAAM,SAAS;AAC5B,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,OAAO,KAAK;AAC/B,MAAI,QAAQ,YAAY,UAAU;AAC9B,WAAO,EAAE,MAAM,MAAM;AACzB,MAAI,KAAK,QAAQ,kBAAkB,KAAK,OAAO,QAAQ;AACnD,WAAO,EAAE,MAAM,YAAY,MAAM,QAAQ,KAAK,OAAO;AACzD,MAAI,KAAK,QAAQ,OAAO,KAAK,OAAO,QAAQ;AACxC,WAAO,EAAE,MAAM,YAAY,QAAQ,KAAK,OAAO;AACnD,MAAI,KAAK,QAAQ,sBAAsB,UAAU;AAC7C,WAAO,EAAE,MAAM,YAAY,QAAQ,KAAK;AAC5C,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,cAAc,MAAM,KAAK,KAAK;AACjD,MAAI,OAAO,QAAQ,YAAY,qBAAqB;AACpD,MAAI;AACA,WAAO,EAAE,MAAM,cAAc,MAAM,KAAK,KAAK;AACjD,MAAI,QAAQ,YAAY,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,mBAC/D,KAAK,QAAQ,mBAAmB,KAAK,QAAQ;AAC7C,WAAO,EAAE,MAAM,aAAa;AAChC,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO,MAAM,SAAS,YAAY;AACzD,MAAI,OAAO,CAAC;AACZ,aAAS;AACL,QAAI,MAAM,KAAK,SAAS,YAAY;AACpC,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,QAAI,IAAI,QAAQ,WAAW;AACvB,aAAO,KAAK,SAAS,CAAC,IAAI;AAAA,IAC9B,WACS,IAAI,QAAQ,gBAAgB;AACjC,aAAO,KAAK,SAAS,CAAC,IAAI;AAAA,IAC9B,WACS,IAAI,QAAQ,gBAAgB;AACjC,WAAK,QAAQ,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE,CAAC;AAC7C;AAAA,IACJ,WACS,IAAI,QAAQ,oBAAoB;AACrC,UAAI,OAAO,IAAI,SAAS,cAAc;AACtC,UAAI;AACA,aAAK,QAAQ,MAAM,SAAS,KAAK,MAAM,KAAK,EAAE,CAAC;AACnD,aAAO;AAAA,IACX,OACK;AACD,aAAO,CAAC;AAAA,IACZ;AAAA,EACJ;AACA,SAAO,aAAa,WAAW,MAAM,OAAO,OAAO,IAAI,CAAC;AAC5D;AAKA,SAAS,uBAAuB,SAAS,CAAC,GAAG;AACzC,MAAI,UAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,IAAI;AAChE,MAAIE,QAAO,OAAO,OAAO,OAAO,KAAK,OAAO,IAAI,IAAI;AACpD,MAAI,QAAQ,OAAO,YAAY,OAAO,UAAU,OAAO,WAAW,IAAI;AACtE,MAAI,EAAE,WAAW,IAAI;AACrB,SAAO,CAAC,YAAY;AAChB,QAAI;AACJ,QAAI,KAAK,YAAY,OAAO;AAC5B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,QAAQ,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,KAAM,GAAG,OAAO,GAAG,KAAK,OAAO,QAAQ;AAC7F,QAAI;AACJ,QAAI,GAAG,QAAQ;AACX,gBAAU;AAAA,aACL,GAAG,QAAQ;AAChB,gBAAUA;AAAA,aACL,GAAG,QAAQ;AAChB,gBAAU;AAAA;AAEV,gBAAU,kBAAkB,QAAQ,OAAO,GAAG,QAAQ,SAAS,UAAU;AAC7E,WAAO,QAAQ,SAAS,EAAE,SAAS,MAAM,UAAU,uBAAuB,IAAI;AAAA,EAClF;AACJ;AAMA,IAAM,oBAAiC,WAAW,aAAa,GAAG,CAAC,MAAM,MAAM,IAAIF,UAAS;AACxF,MAAIA,SAAQ,OAAO,QAAQ,MAAM,KAAK,MAAM,IAAI,YAAY,OAAO,GAAG,KAAK,CAAC,KAAK;AAC7E,WAAO;AACX,OAAK,SAAS,KAAK,MAAM,cAAc,YAAU;AAAA,IAC7C,SAAS,EAAE,MAAM,MAAM,MAAM,IAAI,MAAM,IAAI,QAAQ,KAAK;AAAA,IACxD,OAAO,gBAAgB,OAAO,MAAM,OAAO,CAAC;AAAA,EAChD,EAAE,GAAG;AAAA,IACD,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX,CAAC;AAED,SAAS,gBAAgB,QAAQ;AAC7B,SAAO,CAAC,YAAY;AAChB,QAAI,OAAO,OAAO,KAAK,QAAQ,SAAS;AACxC,WAAO,QAAQ,WAAW,QAAQ,KAAK,IAAI,KAAK,OAAO,IAAI,QAAQ;AAAA,EACvE;AACJ;AACA,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,UAAU;AAAA,QACnB,kFAAkF,KAAK;AAAA,QACvF,8BAA8B,KAAK;AAAA,QACnC,oGAAoG,KAAK;AAAA,QACzG,6BAA6B,KAAK;AAAA,QAClC,YAAY,KAAK;AAAA,QACjB,kBAAkB,KAAK;AAAA,QACvB,cAAc,KAAK;AAAA,QACnB,SAAS,KAAK;AAAA,QACd,YAAyB,KAAK,SAAS,KAAK,YAAY;AAAA,QACxD,cAAc,KAAK;AAAA,QACnB,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,eAAe,KAAK;AAAA,QACpB,eAAe,KAAK;AAAA,QACpB,gBAAgB,KAAK;AAAA,QACrB,eAAe,KAAK;AAAA,QACpB,aAAa,KAAK;AAAA,QAClB,eAAe,KAAK;AAAA,QACpB,OAAO,KAAK;AAAA,QACZ,KAAK,KAAK;AAAA,QACV,YAAY,KAAK;AAAA,MACrB,CAAC;AAAA,MACY,eAAe,IAAI;AAAA,QAC5B,KAAkB,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAAA,QACnD,mEAAgF,gBAAgB,sBAAsB;AAAA,QACtH,aAA0B,gBAAgB,qCAAqC;AAAA,QAC/E,eAA4B,gBAAgB,iCAAiC;AAAA,MACjF,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,iHAAiH,MAAM;AACnH,cAAI,QAAQ,KAAK,YAAY,OAAO,KAAK;AACzC,cAAI,CAAC,SAAS,MAAM,QAAQ;AACxB,mBAAO;AACX,iBAAO,EAAE,MAAM,MAAM,IAAI,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,KAAK,GAAG;AAAA,QAC7E;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,MAAM,IAAI;AAAA,IAC3B,eAAe;AAAA,EACnB;AACJ,CAAC;AACD,IAAM,WAAwB,KAAK;AACnC,SAAS,WAAWG,OAAM;AACtB,SAAO,YAAY,UAAU;AAAA,IACzB,MAAM,WAAW,UAAQ,KAAK,KAAK,QAAQ;AAAA,MACvC,QAAQA,MAAK;AAAA,MACb,SAAS,OAAK,EAAE,QAAQ,UAAU,EAAE,QAAQ;AAAA,IAChD,IAAI,IAAI;AAAA,EACZ,GAAG,QAAQ;AACf;AAIA,IAAM,iBAA8B,WAAW,SAAS,QAAQ;AAIhE,SAAS,OAAO,SAAS,CAAC,GAAG;AACzB,MAAIA,QAAO,OAAO,QAAQ;AAC1B,MAAI,OAAOA,MAAK,YAAY,SAAS,WAAW,iBAAiB,WAAWA,MAAK,QAAQ;AACzF,SAAO,IAAI,gBAAgB,MAAM;AAAA,IAC7BA,MAAK;AAAA,IACL,KAAK,KAAK,GAAG,EAAE,cAAc,uBAAuB,MAAM,EAAE,CAAC;AAAA,IAC7DA,MAAK,SAAS,KAAK,GAAG,EAAE,eAAe,EAAE,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC;AAAA,IAC5D;AAAA,EACJ,CAAC;AACL;", "names": ["next", "comment", "text", "tagStart", "tags", "base"]}