import request from '@/utils/request'

/**
 * 获取用户的材料请求列表
 */
export function getMaterialRequests() {
  return request({
    url: '/user/material-requests',
    method: 'get'
  })
}

/**
 * 获取特定材料请求的详情
 * @param {number} requestId - 材料请求ID
 */
export function getMaterialRequestDetail(requestId) {
  return request({
    url: `/user/material-requests/${requestId}`,
    method: 'get'
  })
}

/**
 * 提交补充材料
 * @param {FormData} formData - 包含材料信息和文件的表单数据
 */
export function submitSupplementaryMaterial(formData) {
  return request({
    url: '/user/supplementary-materials',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户提交的补充材料列表
 * @param {number} requestId - 材料请求ID（可选）
 */
export function getSupplementaryMaterials(requestId = null) {
  const url = requestId 
    ? `/user/supplementary-materials?requestId=${requestId}`
    : '/user/supplementary-materials'
  
  return request({
    url,
    method: 'get'
  })
}

/**
 * 删除补充材料
 * @param {number} materialId - 材料ID
 */
export function deleteSupplementaryMaterial(materialId) {
  return request({
    url: `/user/supplementary-materials/${materialId}`,
    method: 'delete'
  })
}

/**
 * 更新补充材料
 * @param {number} materialId - 材料ID
 * @param {object} data - 更新的材料信息
 */
export function updateSupplementaryMaterial(materialId, data) {
  return request({
    url: `/user/supplementary-materials/${materialId}`,
    method: 'put',
    data
  })
}
