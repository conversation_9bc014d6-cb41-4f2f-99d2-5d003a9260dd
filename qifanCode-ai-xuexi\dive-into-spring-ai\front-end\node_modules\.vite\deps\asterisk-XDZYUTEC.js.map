{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/asterisk.js"], "sourcesContent": ["var atoms    = [\"exten\", \"same\", \"include\",\"ignorepat\",\"switch\"],\n    dpcmd    = [\"#include\",\"#exec\"],\n    apps     = [\n      \"addqueuemember\",\"adsiprog\",\"aelsub\",\"agentlogin\",\"agentmonitoroutgoing\",\"agi\",\n      \"alarmreceiver\",\"amd\",\"answer\",\"authenticate\",\"background\",\"backgrounddetect\",\n      \"bridge\",\"busy\",\"callcompletioncancel\",\"callcompletionrequest\",\"celgenuserevent\",\n      \"changemonitor\",\"chanisavail\",\"channelredirect\",\"chanspy\",\"clearhash\",\"confbridge\",\n      \"congestion\",\"continuewhile\",\"controlplayback\",\"dahdiacceptr2call\",\"dahdibarge\",\n      \"dahdiras\",\"dahdiscan\",\"dahdisendcallreroutingfacility\",\"dahdisendkeypadfacility\",\n      \"datetime\",\"dbdel\",\"dbdeltree\",\"deadagi\",\"dial\",\"dictate\",\"directory\",\"disa\",\n      \"dumpchan\",\"eagi\",\"echo\",\"endwhile\",\"exec\",\"execif\",\"execiftime\",\"exitwhile\",\"extenspy\",\n      \"externalivr\",\"festival\",\"flash\",\"followme\",\"forkcdr\",\"getcpeid\",\"gosub\",\"gosubif\",\n      \"goto\",\"gotoif\",\"gotoiftime\",\"hangup\",\"iax2provision\",\"ices\",\"importvar\",\"incomplete\",\n      \"ivrdemo\",\"jabberjoin\",\"jabberleave\",\"jabbersend\",\"jabbersendgroup\",\"jabberstatus\",\n      \"jack\",\"log\",\"macro\",\"macroexclusive\",\"macroexit\",\"macroif\",\"mailboxexists\",\"meetme\",\n      \"meetmeadmin\",\"meetmechanneladmin\",\"meetmecount\",\"milliwatt\",\"minivmaccmess\",\"minivmdelete\",\n      \"minivmgreet\",\"minivmmwi\",\"minivmnotify\",\"minivmrecord\",\"mixmonitor\",\"monitor\",\"morsecode\",\n      \"mp3player\",\"mset\",\"musiconhold\",\"nbscat\",\"nocdr\",\"noop\",\"odbc\",\"odbc\",\"odbcfinish\",\n      \"originate\",\"ospauth\",\"ospfinish\",\"osplookup\",\"ospnext\",\"page\",\"park\",\"parkandannounce\",\n      \"parkedcall\",\"pausemonitor\",\"pausequeuemember\",\"pickup\",\"pickupchan\",\"playback\",\"playtones\",\n      \"privacymanager\",\"proceeding\",\"progress\",\"queue\",\"queuelog\",\"raiseexception\",\"read\",\"readexten\",\n      \"readfile\",\"receivefax\",\"receivefax\",\"receivefax\",\"record\",\"removequeuemember\",\n      \"resetcdr\",\"retrydial\",\"return\",\"ringing\",\"sayalpha\",\"saycountedadj\",\"saycountednoun\",\n      \"saycountpl\",\"saydigits\",\"saynumber\",\"sayphonetic\",\"sayunixtime\",\"senddtmf\",\"sendfax\",\n      \"sendfax\",\"sendfax\",\"sendimage\",\"sendtext\",\"sendurl\",\"set\",\"setamaflags\",\n      \"setcallerpres\",\"setmusiconhold\",\"sipaddheader\",\"sipdtmfmode\",\"sipremoveheader\",\"skel\",\n      \"slastation\",\"slatrunk\",\"sms\",\"softhangup\",\"speechactivategrammar\",\"speechbackground\",\n      \"speechcreate\",\"speechdeactivategrammar\",\"speechdestroy\",\"speechloadgrammar\",\"speechprocessingsound\",\n      \"speechstart\",\"speechunloadgrammar\",\"stackpop\",\"startmusiconhold\",\"stopmixmonitor\",\"stopmonitor\",\n      \"stopmusiconhold\",\"stopplaytones\",\"system\",\"testclient\",\"testserver\",\"transfer\",\"tryexec\",\n      \"trysystem\",\"unpausemonitor\",\"unpausequeuemember\",\"userevent\",\"verbose\",\"vmauthenticate\",\n      \"vmsayname\",\"voicemail\",\"voicemailmain\",\"wait\",\"waitexten\",\"waitfornoise\",\"waitforring\",\n      \"waitforsilence\",\"waitmusiconhold\",\"waituntil\",\"while\",\"zapateller\"\n    ];\n\nfunction basicToken(stream,state){\n  var cur = '';\n  var ch = stream.next();\n  // comment\n  if (state.blockComment) {\n    if (ch == \"-\" && stream.match(\"-;\", true)) {\n      state.blockComment = false;\n    } else if (stream.skipTo(\"--;\")) {\n      stream.next();\n      stream.next();\n      stream.next();\n      state.blockComment = false;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"comment\";\n  }\n  if(ch == \";\") {\n    if (stream.match(\"--\", true)) {\n      if (!stream.match(\"-\", false)) {  // Except ;--- is not a block comment\n        state.blockComment = true;\n        return \"comment\";\n      }\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // context\n  if(ch == '[') {\n    stream.skipTo(']');\n    stream.eat(']');\n    return \"header\";\n  }\n  // string\n  if(ch == '\"') {\n    stream.skipTo('\"');\n    return \"string\";\n  }\n  if(ch == \"'\") {\n    stream.skipTo(\"'\");\n    return \"string.special\";\n  }\n  // dialplan commands\n  if(ch == '#') {\n    stream.eatWhile(/\\w/);\n    cur = stream.current();\n    if(dpcmd.indexOf(cur) !== -1) {\n      stream.skipToEnd();\n      return \"strong\";\n    }\n  }\n  // application args\n  if(ch == '$'){\n    var ch1 = stream.peek();\n    if(ch1 == '{'){\n      stream.skipTo('}');\n      stream.eat('}');\n      return \"variableName.special\";\n    }\n  }\n  // extension\n  stream.eatWhile(/\\w/);\n  cur = stream.current();\n  if(atoms.indexOf(cur) !== -1) {\n    state.extenStart = true;\n    switch(cur) {\n    case 'same': state.extenSame = true; break;\n    case 'include':\n    case 'switch':\n    case 'ignorepat':\n      state.extenInclude = true;break;\n    default:break;\n    }\n    return \"atom\";\n  }\n}\n\nexport const asterisk = {\n  name: \"asterisk\",\n  startState: function() {\n    return {\n      blockComment: false,\n      extenStart: false,\n      extenSame:  false,\n      extenInclude: false,\n      extenExten: false,\n      extenPriority: false,\n      extenApplication: false\n    };\n  },\n  token: function(stream, state) {\n\n    var cur = '';\n    if(stream.eatSpace()) return null;\n    // extension started\n    if(state.extenStart){\n      stream.eatWhile(/[^\\s]/);\n      cur = stream.current();\n      if(/^=>?$/.test(cur)){\n        state.extenExten = true;\n        state.extenStart = false;\n        return \"strong\";\n      } else {\n        state.extenStart = false;\n        stream.skipToEnd();\n        return \"error\";\n      }\n    } else if(state.extenExten) {\n      // set exten and priority\n      state.extenExten = false;\n      state.extenPriority = true;\n      stream.eatWhile(/[^,]/);\n      if(state.extenInclude) {\n        stream.skipToEnd();\n        state.extenPriority = false;\n        state.extenInclude = false;\n      }\n      if(state.extenSame) {\n        state.extenPriority = false;\n        state.extenSame = false;\n        state.extenApplication = true;\n      }\n      return \"tag\";\n    } else if(state.extenPriority) {\n      state.extenPriority = false;\n      state.extenApplication = true;\n      stream.next(); // get comma\n      if(state.extenSame) return null;\n      stream.eatWhile(/[^,]/);\n      return \"number\";\n    } else if(state.extenApplication) {\n      stream.eatWhile(/,/);\n      cur = stream.current();\n      if(cur === ',') return null;\n      stream.eatWhile(/\\w/);\n      cur = stream.current().toLowerCase();\n      state.extenApplication = false;\n      if(apps.indexOf(cur) !== -1){\n        return \"def\";\n      }\n    } else{\n      return basicToken(stream,state);\n    }\n\n    return null;\n  },\n\n  languageData: {\n    commentTokens: {line: \";\", block: {open: \";--\", close: \"--;\"}}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,QAAW,CAAC,SAAS,QAAQ,WAAU,aAAY,QAAQ;AAA/D,IACI,QAAW,CAAC,YAAW,OAAO;AADlC,IAEI,OAAW;AAAA,EACT;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAuB;AAAA,EACzE;AAAA,EAAgB;AAAA,EAAM;AAAA,EAAS;AAAA,EAAe;AAAA,EAAa;AAAA,EAC3D;AAAA,EAAS;AAAA,EAAO;AAAA,EAAuB;AAAA,EAAwB;AAAA,EAC/D;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAU;AAAA,EAAY;AAAA,EACtE;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAkB;AAAA,EAAoB;AAAA,EACnE;AAAA,EAAW;AAAA,EAAY;AAAA,EAAiC;AAAA,EACxD;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAU;AAAA,EAAO;AAAA,EAAU;AAAA,EAAY;AAAA,EACtE;AAAA,EAAW;AAAA,EAAO;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAC7E;AAAA,EAAc;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EACzE;AAAA,EAAO;AAAA,EAAS;AAAA,EAAa;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAY;AAAA,EACzE;AAAA,EAAU;AAAA,EAAa;AAAA,EAAc;AAAA,EAAa;AAAA,EAAkB;AAAA,EACpE;AAAA,EAAO;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAU;AAAA,EAAgB;AAAA,EAC5E;AAAA,EAAc;AAAA,EAAqB;AAAA,EAAc;AAAA,EAAY;AAAA,EAAgB;AAAA,EAC7E;AAAA,EAAc;AAAA,EAAY;AAAA,EAAe;AAAA,EAAe;AAAA,EAAa;AAAA,EAAU;AAAA,EAC/E;AAAA,EAAY;AAAA,EAAO;AAAA,EAAc;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EACvE;AAAA,EAAY;AAAA,EAAU;AAAA,EAAY;AAAA,EAAY;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EACtE;AAAA,EAAa;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAChF;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAO;AAAA,EACpF;AAAA,EAAW;AAAA,EAAa;AAAA,EAAa;AAAA,EAAa;AAAA,EAAS;AAAA,EAC3D;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAU;AAAA,EAAW;AAAA,EAAgB;AAAA,EACrE;AAAA,EAAa;AAAA,EAAY;AAAA,EAAY;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EAC5E;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAAM;AAAA,EAC3D;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAc;AAAA,EAAkB;AAAA,EAChF;AAAA,EAAa;AAAA,EAAW;AAAA,EAAM;AAAA,EAAa;AAAA,EAAwB;AAAA,EACnE;AAAA,EAAe;AAAA,EAA0B;AAAA,EAAgB;AAAA,EAAoB;AAAA,EAC7E;AAAA,EAAc;AAAA,EAAsB;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAiB;AAAA,EACnF;AAAA,EAAkB;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAa;AAAA,EAAW;AAAA,EAChF;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAqB;AAAA,EAAY;AAAA,EAAU;AAAA,EACxE;AAAA,EAAY;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAY;AAAA,EAAe;AAAA,EAC1E;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAQ;AACzD;AAEJ,SAAS,WAAW,QAAO,OAAM;AAC/B,MAAI,MAAM;AACV,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,MAAM,cAAc;AACtB,QAAI,MAAM,OAAO,OAAO,MAAM,MAAM,IAAI,GAAG;AACzC,YAAM,eAAe;AAAA,IACvB,WAAW,OAAO,OAAO,KAAK,GAAG;AAC/B,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACA,MAAG,MAAM,KAAK;AACZ,QAAI,OAAO,MAAM,MAAM,IAAI,GAAG;AAC5B,UAAI,CAAC,OAAO,MAAM,KAAK,KAAK,GAAG;AAC7B,cAAM,eAAe;AACrB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAG,MAAM,KAAK;AACZ,WAAO,OAAO,GAAG;AACjB,WAAO,IAAI,GAAG;AACd,WAAO;AAAA,EACT;AAEA,MAAG,MAAM,KAAK;AACZ,WAAO,OAAO,GAAG;AACjB,WAAO;AAAA,EACT;AACA,MAAG,MAAM,KAAK;AACZ,WAAO,OAAO,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAG,MAAM,KAAK;AACZ,WAAO,SAAS,IAAI;AACpB,UAAM,OAAO,QAAQ;AACrB,QAAG,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC5B,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAG,MAAM,KAAI;AACX,QAAI,MAAM,OAAO,KAAK;AACtB,QAAG,OAAO,KAAI;AACZ,aAAO,OAAO,GAAG;AACjB,aAAO,IAAI,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,SAAS,IAAI;AACpB,QAAM,OAAO,QAAQ;AACrB,MAAG,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC5B,UAAM,aAAa;AACnB,YAAO,KAAK;AAAA,MACZ,KAAK;AAAQ,cAAM,YAAY;AAAM;AAAA,MACrC,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,cAAM,eAAe;AAAK;AAAA,MAC5B;AAAQ;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAE7B,QAAI,MAAM;AACV,QAAG,OAAO,SAAS;AAAG,aAAO;AAE7B,QAAG,MAAM,YAAW;AAClB,aAAO,SAAS,OAAO;AACvB,YAAM,OAAO,QAAQ;AACrB,UAAG,QAAQ,KAAK,GAAG,GAAE;AACnB,cAAM,aAAa;AACnB,cAAM,aAAa;AACnB,eAAO;AAAA,MACT,OAAO;AACL,cAAM,aAAa;AACnB,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AAAA,IACF,WAAU,MAAM,YAAY;AAE1B,YAAM,aAAa;AACnB,YAAM,gBAAgB;AACtB,aAAO,SAAS,MAAM;AACtB,UAAG,MAAM,cAAc;AACrB,eAAO,UAAU;AACjB,cAAM,gBAAgB;AACtB,cAAM,eAAe;AAAA,MACvB;AACA,UAAG,MAAM,WAAW;AAClB,cAAM,gBAAgB;AACtB,cAAM,YAAY;AAClB,cAAM,mBAAmB;AAAA,MAC3B;AACA,aAAO;AAAA,IACT,WAAU,MAAM,eAAe;AAC7B,YAAM,gBAAgB;AACtB,YAAM,mBAAmB;AACzB,aAAO,KAAK;AACZ,UAAG,MAAM;AAAW,eAAO;AAC3B,aAAO,SAAS,MAAM;AACtB,aAAO;AAAA,IACT,WAAU,MAAM,kBAAkB;AAChC,aAAO,SAAS,GAAG;AACnB,YAAM,OAAO,QAAQ;AACrB,UAAG,QAAQ;AAAK,eAAO;AACvB,aAAO,SAAS,IAAI;AACpB,YAAM,OAAO,QAAQ,EAAE,YAAY;AACnC,YAAM,mBAAmB;AACzB,UAAG,KAAK,QAAQ,GAAG,MAAM,IAAG;AAC1B,eAAO;AAAA,MACT;AAAA,IACF,OAAM;AACJ,aAAO,WAAW,QAAO,KAAK;AAAA,IAChC;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,OAAO,OAAO,MAAK,EAAC;AAAA,EAC/D;AACF;", "names": []}