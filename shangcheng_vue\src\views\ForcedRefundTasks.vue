<template>
  <div class="forced-refund-tasks">
    <header class="header">
      <h1>强制退款任务</h1>
      <div class="nav-actions">
        <router-link to="/refunds">返回退款管理</router-link>
        <router-link to="/home">返回首页</router-link>
      </div>
    </header>

    <div class="warning-notice">
      <h3>⚠️ 重要提醒</h3>
      <p>您有未完成的强制退款任务，在完成所有任务前，您将无法发布新商品。</p>
      <p>请及时充值并处理这些退款任务，以恢复商品发布权限。</p>
    </div>

    <div class="balance-info">
      <h3>当前余额：¥{{ userBalance }}</h3>
      <router-link to="/recharge" class="recharge-btn">立即充值</router-link>
    </div>

    <div class="task-list">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="tasks.length === 0" class="empty">
        <h3>🎉 恭喜！</h3>
        <p>您没有待处理的强制退款任务，可以正常发布商品了。</p>
      </div>
      <div v-else>
        <div v-for="task in tasks" :key="task.refundId" class="task-item">
          <div class="task-header">
            <span class="task-id">任务ID：{{ task.refundId }}</span>
            <span class="task-amount">需退款：¥{{ task.refundAmount }}</span>
          </div>
          
          <div class="task-content">
            <div class="task-info">
              <h3>{{ task.productTitle }}</h3>
              <p class="buyer-info">买家：{{ task.buyerName }}</p>
              <p class="refund-reason">退款原因：{{ task.refundReason }}</p>
              <p class="admin-note">管理员说明：{{ task.adminResponse }}</p>
              <p class="task-date">任务创建时间：{{ formatDate(task.createdTime) }}</p>
            </div>
            
            <div class="task-actions">
              <button 
                v-if="canProcess(task.refundAmount)"
                @click="processTask(task)"
                class="btn-success"
                :disabled="processing"
              >
                {{ processing ? '处理中...' : '立即处理' }}
              </button>
              <button 
                v-else
                @click="goToRecharge"
                class="btn-warning"
              >
                余额不足，去充值
              </button>
            </div>
          </div>
          
          <div class="balance-check">
            <span v-if="canProcess(task.refundAmount)" class="sufficient">
              ✅ 余额充足，可以处理此任务
            </span>
            <span v-else class="insufficient">
              ❌ 余额不足，还需充值 ¥{{ (task.refundAmount - userBalance).toFixed(2) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理确认对话框 -->
    <div v-if="showConfirm" class="modal-overlay" @click="closeConfirmDialog">
      <div class="modal-content" @click.stop>
        <h3>确认处理退款任务</h3>
        <div class="confirm-info">
          <p><strong>退款金额：</strong>¥{{ selectedTask?.refundAmount }}</p>
          <p><strong>当前余额：</strong>¥{{ userBalance }}</p>
          <p><strong>处理后余额：</strong>¥{{ (userBalance - selectedTask?.refundAmount).toFixed(2) }}</p>
        </div>
        <p class="confirm-warning">
          处理此任务后，将从您的余额中扣除相应金额退还给买家。
          完成所有强制退款任务后，您将恢复商品发布权限。
        </p>
        <div class="form-actions">
          <button 
            @click="confirmProcess" 
            :disabled="processing" 
            class="btn-success"
          >
            {{ processing ? '处理中...' : '确认处理' }}
          </button>
          <button @click="closeConfirmDialog" class="btn-secondary">
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getForcedRefundTasks, processForcedRefund } from '@/api/refund';
import { getUserInfo } from '@/api/user';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const processing = ref(false);
const tasks = ref([]);
const userBalance = ref(0);
const showConfirm = ref(false);
const selectedTask = ref(null);

// 加载用户余额
const loadUserBalance = async () => {
  try {
    const response = await getUserInfo();
    userBalance.value = parseFloat(response.data.balance || 0);
  } catch (error) {
    console.error('加载用户余额失败:', error);
  }
};

// 加载强制退款任务
const loadTasks = async () => {
  loading.value = true;
  try {
    const response = await getForcedRefundTasks();
    tasks.value = response.data || [];
  } catch (error) {
    console.error('加载强制退款任务失败:', error);
    alert('加载强制退款任务失败');
  } finally {
    loading.value = false;
  }
};

// 检查是否可以处理任务
const canProcess = (amount) => {
  return userBalance.value >= amount;
};

// 处理任务
const processTask = (task) => {
  selectedTask.value = task;
  showConfirm.value = true;
};

// 关闭确认对话框
const closeConfirmDialog = () => {
  showConfirm.value = false;
  selectedTask.value = null;
};

// 确认处理
const confirmProcess = async () => {
  if (!selectedTask.value) return;
  
  processing.value = true;
  try {
    await processForcedRefund(selectedTask.value.forcedRefundId);
    alert('强制退款任务处理成功！');
    
    // 重新加载数据
    await loadUserBalance();
    await loadTasks();
    
    closeConfirmDialog();
    
    // 如果没有更多任务，提示用户
    if (tasks.value.length === 0) {
      alert('🎉 恭喜！所有强制退款任务已完成，您已恢复商品发布权限！');
    }
  } catch (error) {
    console.error('处理强制退款任务失败:', error);
    alert('处理失败：' + (error.response?.data?.message || error.message));
  } finally {
    processing.value = false;
  }
};

// 去充值
const goToRecharge = () => {
  router.push('/recharge');
};

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString();
};

onMounted(async () => {
  await loadUserBalance();
  await loadTasks();
});
</script>

<style scoped>
.forced-refund-tasks {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.nav-actions a {
  margin-left: 10px;
  padding: 8px 15px;
  background-color: #2196F3;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.warning-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.warning-notice h3 {
  color: #856404;
  margin: 0 0 10px 0;
}

.warning-notice p {
  color: #856404;
  margin: 5px 0;
}

.balance-info {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-info h3 {
  color: #155724;
  margin: 0;
}

.recharge-btn {
  padding: 8px 15px;
  background-color: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.task-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-id {
  font-weight: bold;
  color: #666;
}

.task-amount {
  font-size: 18px;
  font-weight: bold;
  color: #f44336;
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.task-info {
  flex: 1;
}

.task-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.task-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.buyer-info {
  color: #2196F3;
  font-weight: bold;
}

.admin-note {
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.task-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.balance-check {
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.sufficient {
  color: #28a745;
  background-color: #d4edda;
}

.insufficient {
  color: #dc3545;
  background-color: #f8d7da;
}

/* 按钮样式 */
.btn-success { 
  background-color: #28a745; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-warning { 
  background-color: #ffc107; 
  color: #212529; 
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-secondary { 
  background-color: #6c757d; 
  color: white; 
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.confirm-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.confirm-info p {
  margin: 5px 0;
  font-size: 14px;
}

.confirm-warning {
  color: #856404;
  background-color: #fff3cd;
  padding: 10px;
  border-radius: 4px;
  margin: 15px 0;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.empty h3 {
  color: #28a745;
  margin-bottom: 10px;
}
</style>
