package io.github.qifan777.knowledge;

import io.github.qifan777.knowledge.ai.messge.AiMessageFetcher;
import io.github.qifan777.knowledge.ai.session.AiSessionFetcher;
import io.github.qifan777.knowledge.user.UserFetcher;
import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Fetchers {
    AiMessageFetcher AI_MESSAGE_FETCHER = AiMessageFetcher.$;

    AiSessionFetcher AI_SESSION_FETCHER = AiSessionFetcher.$;

    UserFetcher USER_FETCHER = UserFetcher.$;
}
