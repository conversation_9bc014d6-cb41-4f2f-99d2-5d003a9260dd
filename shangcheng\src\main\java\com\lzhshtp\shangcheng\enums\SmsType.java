package com.lzhshtp.shangcheng.enums;

/**
 * 短信验证码类型枚举
 */
public enum SmsType {
    
    LOGIN("login", "登录验证码"),
    REGISTER("register", "注册验证码"),
    RESET_PASSWORD("reset_password", "重置密码验证码"),
    BIND("bind", "绑定手机号验证码");
    
    private final String code;
    private final String description;
    
    SmsType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static SmsType fromCode(String code) {
        for (SmsType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 验证类型是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }
}
