<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手交易平台 - 便捷的在线交易平台</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF4D4F;
            --secondary-color: #FF7875;
            --text-color: #333333;
            --light-bg: #F5F5F5;
            --white: #FFFFFF;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: var(--white);
            padding: 12px 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: var(--text-color);
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
            background-color: #FFF1F0;
        }

        /* 主要内容区 */
        .main-content {
            margin-top: 80px;
            padding: 0 5%;
        }

        /* 英雄区域 */
        .hero-section {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border-radius: 16px;
            padding: 60px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 500px;
        }

        .hero-text {
            color: white;
            max-width: 50%;
            z-index: 2;
        }

        .hero-text h1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-text p {
            font-size: 20px;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
        }

        .hero-button {
            padding: 16px 40px;
            border-radius: 28px;
            font-size: 18px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .primary-button {
            background: white;
            color: var(--primary-color);
        }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .secondary-button {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 2px solid white;
        }

        .secondary-button:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .hero-image {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            width: 45%;
            max-width: 600px;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(-50%) translateX(0px);
            }
            50% {
                transform: translateY(-50%) translateX(20px);
            }
            100% {
                transform: translateY(-50%) translateX(0px);
            }
        }

        /* 特点展示 */
        .features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 60px 0;
        }

        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            background: #FFF1F0;
            width: 80px;
            height: 80px;
            border-radius: 50%;
        }

        .feature-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
            color: var(--text-color);
        }

        .feature-desc {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }

        /* 数据统计 */
        .stats {
            background: var(--white);
            border-radius: 16px;
            padding: 60px;
            margin: 60px 0;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .stat-item h3 {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 12px;
        }

        .stat-item p {
            font-size: 16px;
            color: #666;
        }

        /* 信任保障 */
        .trust-section {
            padding: 60px 0;
            text-align: center;
        }

        .section-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 40px;
            color: var(--text-color);
        }

        .trust-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
        }

        .trust-item {
            background: white;
            padding: 30px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .trust-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }

        .trust-icon {
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 16px;
        }

        .trust-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .trust-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        /* 动画 reveal */
        .reveal {
            opacity: 0;
            transform: translateY(40px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .reveal.visible .feature-card,
        .reveal.visible .stat-item,
        .reveal.visible .trust-item,
        .reveal.visible .product-card {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 精选商品样式 */
        .products-section {
            margin: 60px 0;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }

        .product-image-wrapper {
            position: relative;
            padding-top: 100%;
            overflow: hidden;
        }

        .product-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 77, 79, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .product-info {
            padding: 16px;
        }

        .product-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-color);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-meta {
            display: flex;
            align-items: baseline;
            gap: 8px;
        }

        .price-wrapper {
            display: flex;
            align-items: baseline;
            color: var(--primary-color);
        }

        .price-symbol {
            font-size: 14px;
            font-weight: 500;
        }

        .price-value {
            font-size: 20px;
            font-weight: bold;
        }

        .original-price {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
        }

        .view-more-wrapper {
            text-align: center;
            margin-top: 40px;
        }

        .view-more-button {
            display: inline-block;
            padding: 12px 24px;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            border: 2px solid var(--primary-color);
            border-radius: 24px;
            transition: all 0.3s ease;
        }

        .view-more-button:hover {
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 900px) {
            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .products-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 40px 20px;
                min-height: auto;
            }

            .hero-text {
                max-width: 100%;
            }

            .hero-text h1 {
                font-size: 32px;
            }

            .hero-image {
                display: none;
            }

            .features {
                grid-template-columns: 1fr;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
                padding: 30px;
            }

            .trust-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 分类标签样式 */
        .category-tabs {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            overflow-x: auto;
            padding-bottom: 8px;
            -webkit-overflow-scrolling: touch;
        }

        .category-tabs::-webkit-scrollbar {
            height: 4px;
        }

        .category-tabs::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .category-tabs::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 2px;
        }

        .category-tab {
            padding: 8px 20px;
            border: none;
            background: none;
            font-size: 15px;
            color: #666;
            cursor: pointer;
            white-space: nowrap;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .category-tab:hover {
            color: var(--primary-color);
            background: #FFF1F0;
        }

        .category-tab.active {
            color: white;
            background: var(--primary-color);
        }

        /* 商品网格调整 */
        .products-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
        }

        @media (max-width: 1400px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1000px) {
            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .category-tabs {
                padding-bottom: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="top-nav">
        <div class="logo-section">
            <a href="#" class="logo">二手交易</a>
            <div class="nav-links">
                <a href="main.html">浏览商品</a>
                <a href="#about">关于我们</a>
                <a href="#help">帮助中心</a>
            </div>
        </div>
        <div class="nav-links">
            <a href="login.html">登录</a>
            <a href="#register">注册</a>
        </div>
    </nav>

    <div class="main-content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-text">
                <h1>便捷的在线二手交易平台</h1>
                <p>安全可靠的交易环境，专业的验货服务，让您的二手交易更加放心。一站式解决您的闲置买卖需求。</p>
                <div class="hero-buttons">
                    <a href="main.html" class="hero-button primary-button">立即浏览</a>
                    <a href="#register" class="hero-button secondary-button">免费注册</a>
                </div>
            </div>
            <img src="https://via.placeholder.com/600x400" alt="Platform Preview" class="hero-image">
        </section>

        <!-- 特点展示 -->
        <div class="features reveal">
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                </div>
                <h3 class="feature-title">专业验证</h3>
                <p class="feature-desc">专业验货团队，层层把关商品质量，确保交易双方权益</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>
                </div>
                <h3 class="feature-title">价格透明</h3>
                <p class="feature-desc">市场价格参考，避免虚高标价，让交易更透明</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                </div>
                <h3 class="feature-title">交易保障</h3>
                <p class="feature-desc">担保交易，七天无理由退货，让您购物无忧</p>
            </div>
        </div>

        <!-- 数据统计 -->
        <div class="stats reveal">
            <div class="stat-item">
                <h3>100万+</h3>
                <p>注册用户</p>
            </div>
            <div class="stat-item">
                <h3>50万+</h3>
                <p>商品数量</p>
            </div>
            <div class="stat-item">
                <h3>99%</h3>
                <p>好评率</p>
            </div>
            <div class="stat-item">
                <h3>10万+</h3>
                <p>月成交量</p>
            </div>
        </div>

        <!-- 信任保障 -->
        <section class="trust-section reveal">
            <h2 class="section-title">四重交易保障</h2>
            <div class="trust-grid">
                <div class="trust-item">
                    <div class="trust-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5.52 19c.64-2.2 1.84-3 3.22-3h6.52c1.38 0 2.58.8 3.22 3"/><circle cx="12" cy="10" r="3"/><circle cx="12" cy="12" r="10"/></svg>
                    </div>
                    <h3 class="trust-title">身份认证</h3>
                    <p class="trust-desc">严格的用户认证体系，确保交易双方身份真实可靠</p>
                </div>
                <div class="trust-item">
                    <div class="trust-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
                    </div>
                    <h3 class="trust-title">担保交易</h3>
                    <p class="trust-desc">平台担保交易，确保资金安全，买卖双方无忧</p>
                </div>
                <div class="trust-item">
                    <div class="trust-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
                    </div>
                    <h3 class="trust-title">验货服务</h3>
                    <p class="trust-desc">专业验货团队，确保商品品质，杜绝假冒伪劣</p>
                </div>
                <div class="trust-item">
                    <div class="trust-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>
                    </div>
                    <h3 class="trust-title">售后保障</h3>
                    <p class="trust-desc">7天无理由退货，365天质保，让您购物无忧</p>
                </div>
            </div>
        </section>

        <!-- 精选商品 -->
        <section class="products-section reveal">
            <h2 class="section-title">精选好物</h2>
            <div class="category-tabs">
                <button class="category-tab active">全部</button>
                <button class="category-tab">手机平板</button>
                <button class="category-tab">电脑数码</button>
                <button class="category-tab">相机摄影</button>
                <button class="category-tab">游戏设备</button>
                <button class="category-tab">智能穿戴</button>
            </div>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="iPhone 14" class="product-image">
                        <span class="product-tag">99新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">iPhone 14 Pro Max 256G</h3>
                        <p class="product-desc">官方验机，无维修史，电池健康度98%</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">7988</span>
                            </div>
                            <span class="original-price">¥9588</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="MacBook Pro" class="product-image">
                        <span class="product-tag">95新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">MacBook Pro M2 14寸</h3>
                        <p class="product-desc">原装配件齐全，性能完好，轻微使用痕迹</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">11999</span>
                            </div>
                            <span class="original-price">¥14999</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="iPad Pro" class="product-image">
                        <span class="product-tag">98新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">iPad Pro 2022 11寸 512G</h3>
                        <p class="product-desc">官方翻新机，全新电池，原装配件</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">5388</span>
                            </div>
                            <span class="original-price">¥6288</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="华为手表" class="product-image">
                        <span class="product-tag">99新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">华为 WATCH Fit 3</h3>
                        <p class="product-desc">月光白，全新未拆封，支持验机</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">588</span>
                            </div>
                            <span class="original-price">¥688</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="索尼相机" class="product-image">
                        <span class="product-tag">95新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">索尼 A7M4 全画幅微单</h3>
                        <p class="product-desc">快门数5000次，配件齐全，成色极好</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">12999</span>
                            </div>
                            <span class="original-price">¥15999</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="Switch游戏机" class="product-image">
                        <span class="product-tag">95新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Nintendo Switch OLED</h3>
                        <p class="product-desc">白色主机，带塞尔达+马里奥赛车</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">1899</span>
                            </div>
                            <span class="original-price">¥2299</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="联想笔记本" class="product-image">
                        <span class="product-tag">9成新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">联想 拯救者 Y7000P</h3>
                        <p class="product-desc">i7-13700H/3070显卡/32G内存</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">6999</span>
                            </div>
                            <span class="original-price">¥8999</span>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image-wrapper">
                        <img src="https://via.placeholder.com/300x300" alt="佳能镜头" class="product-image">
                        <span class="product-tag">98新</span>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">佳能 RF24-70mm F2.8</h3>
                        <p class="product-desc">红圈镜皇，防抖，带原厂遮光罩</p>
                        <div class="product-meta">
                            <div class="price-wrapper">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">13500</span>
                            </div>
                            <span class="original-price">¥16999</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="view-more-wrapper">
                <a href="main.html" class="view-more-button">查看更多商品 ></a>
            </div>
        </section>

        <!-- 开始使用区域 -->
        <section class="cta-section reveal">
            <h2 class="cta-title">开始您的二手交易之旅</h2>
            <p class="cta-desc">加入我们的平台，享受安全、便捷的二手交易体验</p>
            <a href="main.html" class="cta-button">立即开始</a>
        </section>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const revealElements = document.querySelectorAll('.reveal');

            const revealObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        
                        const children = entry.target.querySelectorAll('.feature-card, .stat-item, .trust-item, .product-card');
                        children.forEach((child, index) => {
                            child.style.animationDelay = `${index * 100}ms`;
                        });

                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1
            });

            revealElements.forEach(elem => {
                revealObserver.observe(elem);
            });
        });
    </script>
</body>
</html> 