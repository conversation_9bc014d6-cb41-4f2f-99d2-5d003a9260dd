package com.lzhshtp.shangcheng.service.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.mapper.AutoAuditRecordMapper;
import com.lzhshtp.shangcheng.mapper.ManualAuditTaskMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.SecondReviewTaskMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人工审核服务
 */
@Slf4j
@Service
public class ManualAuditService {

    @Autowired
    private ManualAuditTaskMapper manualAuditTaskMapper;

    @Autowired
    private AutoAuditRecordMapper autoAuditRecordMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SecondReviewTaskMapper secondReviewTaskMapper;

    @Autowired
    private MaterialRequestService materialRequestService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 创建人工审核任务
     */
    @Transactional
    public ManualAuditTask createManualAuditTask(Long productId, List<String> reasons) {
        log.info("创建人工审核任务，商品ID: {}", productId);

        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new RuntimeException("商品不存在");
        }

        // 获取自动审核记录
        AutoAuditRecord autoAuditRecord = autoAuditRecordMapper.findByProductId(productId);

        return createManualAuditTaskWithRecord(product, autoAuditRecord, reasons);
    }

    /**
     * 创建人工审核任务（带自动审核记录）
     */
    @Transactional
    public ManualAuditTask createManualAuditTaskWithRecord(Product product, AutoAuditRecord autoAuditRecord, List<String> reasons) {
        log.info("创建人工审核任务，商品ID: {}, 原因: {}", product.getId(), reasons);

        // 计算优先级
        int priority = calculatePriority(reasons);

        // 计算截止时间
        LocalDateTime deadline = LocalDateTime.now().plusHours(AuditConstants.MANUAL_AUDIT_DEADLINE_HOURS);

        ManualAuditTask task = ManualAuditTask.builder()
            .productId(product.getId())
            .sellerId(product.getSellerId())
            .autoAuditRecordId(autoAuditRecord != null ? autoAuditRecord.getRecordId() : null)
            .auditReasons(toJsonString(reasons))
            .status("pending")
            .priority(priority)
            .createdTime(LocalDateTime.now())
            .deadline(deadline)
            .build();

        manualAuditTaskMapper.insert(task);

        // 更新商品状态为人工审核中
        product.setStatus("manual_review");
        productMapper.updateById(product);

        log.info("人工审核任务创建成功，任务ID: {}", task.getTaskId());
        return task;
    }

    /**
     * 获取待审核任务列表
     */
    public List<ManualAuditTask> getPendingTasks() {
        return manualAuditTaskMapper.findPendingTasks();
    }

    /**
     * 管理员开始审核任务
     */
    @Transactional
    public boolean startAuditTask(Long taskId, Long adminId) {
        log.info("管理员开始审核任务，任务ID: {}, 管理员ID: {}", taskId, adminId);

        int updated = manualAuditTaskMapper.startAuditTask(taskId, adminId, LocalDateTime.now());

        if (updated > 0) {
            log.info("任务认领成功，任务ID: {}, 管理员ID: {}", taskId, adminId);
            return true;
        } else {
            log.warn("任务认领失败，可能已被其他管理员认领，任务ID: {}", taskId);
            return false;
        }
    }

    /**
     * 获取管理员正在处理的任务
     */
    public List<ManualAuditTask> getTasksByAdmin(Long adminId) {
        return manualAuditTaskMapper.findTasksByAdmin(adminId);
    }

    /**
     * 管理员做出审核决策
     */
    @Transactional
    public void makeAuditDecision(Long taskId, Long adminId, String decision, String comments) {
        log.info("管理员做出审核决策，任务ID: {}, 管理员ID: {}, 决策: {}", taskId, adminId, decision);

        ManualAuditTask task = manualAuditTaskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("审核任务不存在");
        }

        if (!adminId.equals(task.getAdminId())) {
            throw new RuntimeException("只能处理自己认领的任务");
        }

        // 更新任务状态
        task.setAdminDecision(decision);
        task.setAdminComments(comments);
        task.setStatus("completed");
        task.setCompletedTime(LocalDateTime.now());

        manualAuditTaskMapper.updateById(task);

        // 根据决策更新商品状态
        Product product = productMapper.selectById(task.getProductId());
        switch (decision) {
            case "approved":
                // 检查是否需要二度复审
                if (needSecondReview(task)) {
                    // 创建二度复审任务
                    createSecondReviewTask(task, "价格过高或其他需要二度复审的情况");
                } else {
                    // 直接通过
                    product.setStatus("available");
                    productMapper.updateById(product);
                    log.info("商品审核通过，商品ID: {}", product.getId());
                }
                break;

            case "rejected":
                // 审核拒绝时直接删除商品
                productMapper.deleteById(product.getId());
                log.info("商品审核拒绝，已删除商品，商品ID: {}", product.getId());
                break;

            case "request_materials":
                // 更新商品状态为材料请求中
                product.setStatus("material_requested");
                productMapper.updateById(product);

                // 更新任务状态为材料请求中
                task.setStatus("material_requested");
                manualAuditTaskMapper.updateById(task);

                // 创建材料请求
                createMaterialRequest(task, adminId, comments);

                log.info("要求补充材料，商品ID: {}, 任务ID: {}", product.getId(), task.getTaskId());
                break;

            case "escalate_to_second_review":
                createSecondReviewTask(task, "人工审核升级到二度复审");
                break;
        }

        log.info("审核决策处理完成，任务ID: {}, 决策: {}", taskId, decision);
    }

    /**
     * 计算任务优先级
     */
    private int calculatePriority(List<String> reasons) {
        int priority = 0;

        for (String reason : reasons) {
            if (reason.contains("违禁") || reason.contains("敏感")) {
                priority += 10;
            } else if (reason.contains("品牌") || reason.contains("价格")) {
                priority += 5;
            } else {
                priority += 1;
            }
        }

        return Math.min(priority, 10); // 最高优先级为10
    }

    /**
     * 判断是否需要二度复审
     * 只有价格相关风险和信用分过低才需要二度复审
     */
    private boolean needSecondReview(ManualAuditTask task) {
        try {
            Product product = productMapper.selectById(task.getProductId());
            User seller = userMapper.selectById(task.getSellerId());
            List<String> reasons = fromJsonString(task.getAuditReasons(), new TypeReference<List<String>>() {});


            // 2. 高价值商品 - 价格超过10000元需要二度复审
            if (product.getPrice() != null && product.getPrice().compareTo(new BigDecimal("10000")) > 0) {
                log.info("触发二度复审：高价值商品（{}元），商品ID: {}", product.getPrice(), product.getId());
                return true;
            }

            // 3. 卖家信用分过低 - 低于50分需要二度复审
            if (seller.getCreditScore() != null && seller.getCreditScore() < 50) {
                log.info("触发二度复审：卖家信用分过低（{}分），商品ID: {}", seller.getCreditScore(), product.getId());
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("判断是否需要二度复审失败", e);
            // 异常情况下，为了安全起见，进入二度复审
            return true;
        }
    }



    /**
     * 创建材料请求
     */
    private void createMaterialRequest(ManualAuditTask task, Long adminId, String comments) {
        log.info("创建材料请求，任务ID: {}, 管理员ID: {}", task.getTaskId(), adminId);

        // 根据审核情况确定需要的材料类型
        List<String> requiredMaterials = determineRequiredMaterials(task, comments);

        // 生成请求原因
        String requestReason = generateMaterialRequestReason(task, comments);

        // 创建材料请求
        MaterialRequest materialRequest = materialRequestService.createMaterialRequest(
            task.getProductId(),
            "manual_audit",
            task.getTaskId(),
            adminId,
            requiredMaterials,
            requestReason
        );

        // TODO: 发送通知给卖家（后续实现）
        log.info("材料请求创建成功，请求ID: {}", materialRequest.getRequestId());
    }

    /**
     * 根据审核情况确定需要的材料
     */
    private List<String> determineRequiredMaterials(ManualAuditTask task, String comments) {
        List<String> materials = new ArrayList<>();

        // 根据审核原因和管理员备注确定需要的材料
        String auditReasons = task.getAuditReasons();
        String lowerComments = comments != null ? comments.toLowerCase() : "";

        // 价格相关问题
        if (auditReasons.contains("价格") || lowerComments.contains("价格") || lowerComments.contains("发票")) {
            materials.add("商品发票或采购凭证");
            materials.add("价格说明文件");
        }

        // 品牌相关问题
        if (auditReasons.contains("品牌") || lowerComments.contains("品牌") || lowerComments.contains("授权")) {
            materials.add("品牌授权书");
            materials.add("正品证明");
        }

        // 商品描述问题
        if (auditReasons.contains("文字") || lowerComments.contains("描述") || lowerComments.contains("说明")) {
            materials.add("商品详细说明");
            materials.add("产品规格书");
        }

        // 图片问题
        if (auditReasons.contains("图片") || lowerComments.contains("图片") || lowerComments.contains("照片")) {
            materials.add("商品实物照片");
            materials.add("包装照片");
        }

        // 如果没有特定要求，添加通用材料
        if (materials.isEmpty()) {
            materials.add("商品相关证明文件");
            materials.add("补充说明材料");
        }

        return materials;
    }

    /**
     * 生成材料请求原因
     */
    private String generateMaterialRequestReason(ManualAuditTask task, String comments) {
        StringBuilder reason = new StringBuilder();
        reason.append("根据人工审核结果，需要您提供以下材料以完成商品审核：\n\n");

        if (comments != null && !comments.trim().isEmpty()) {
            reason.append("审核说明：").append(comments).append("\n\n");
        }

        reason.append("请在规定时间内提交相关材料，逾期未提交将影响商品上架。");

        return reason.toString();
    }

    /**
     * 创建二度复审任务
     */
    private void createSecondReviewTask(ManualAuditTask manualTask, String reason) {
        log.info("创建二度复审任务，人工审核任务ID: {}, 原因: {}", manualTask.getTaskId(), reason);

        try {
            // 获取商品信息
            Product product = productMapper.selectById(manualTask.getProductId());
            if (product == null) {
                throw new RuntimeException("商品不存在，无法创建二度复审任务");
            }

            // 检查是否已存在该商品的二度复审任务
            SecondReviewTask existingTask = secondReviewTaskMapper.findByProductId(manualTask.getProductId());
            if (existingTask != null && !"completed".equals(existingTask.getStatus())) {
                log.warn("商品ID: {} 已存在未完成的二度复审任务，任务ID: {}", manualTask.getProductId(), existingTask.getTaskId());
                return;
            }

            // 收集所有之前的审核材料
            Map<String, Object> allPreviousMaterials = collectPreviousAuditMaterials(manualTask);

            // 计算截止时间（二度复审通常给更多时间，比如72小时）
            LocalDateTime deadline = LocalDateTime.now().plusHours(72);

            // 创建二度复审任务
            SecondReviewTask secondReviewTask = SecondReviewTask.builder()
                .productId(manualTask.getProductId())
                .manualTaskId(manualTask.getTaskId())
                .escalationReason(reason)
                .status("pending")
                .createdTime(LocalDateTime.now())
                .deadline(deadline)
                .build();

            // 设置所有之前的审核材料（转换为JSON字符串）
            try {
                String materialsJson = toJsonString(allPreviousMaterials);
                secondReviewTask.setAllPreviousMaterials(materialsJson);
            } catch (Exception e) {
                log.error("序列化审核材料失败，人工审核任务ID: {}", manualTask.getTaskId(), e);
                secondReviewTask.setAllPreviousMaterials("{}");
            }

            // 保存二度复审任务
            secondReviewTaskMapper.insert(secondReviewTask);

            // 更新商品状态为二度复审中
            product.setStatus("second_review");
            productMapper.updateById(product);

            log.info("二度复审任务创建成功，任务ID: {}, 商品ID: {}, 截止时间: {}",
                secondReviewTask.getTaskId(), product.getId(), deadline);

        } catch (Exception e) {
            log.error("创建二度复审任务失败，人工审核任务ID: {}, 原因: {}", manualTask.getTaskId(), reason, e);
            throw new RuntimeException("创建二度复审任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 收集所有之前的审核材料
     */
    private Map<String, Object> collectPreviousAuditMaterials(ManualAuditTask manualTask) {
        Map<String, Object> materials = new HashMap<>();

        try {
            // 收集自动审核结果
            if (manualTask.getAutoAuditRecordId() != null) {
                AutoAuditRecord autoAuditRecord = autoAuditRecordMapper.selectById(manualTask.getAutoAuditRecordId());
                if (autoAuditRecord != null) {
                    Map<String, Object> autoAuditData = new HashMap<>();
                    autoAuditData.put("recordId", autoAuditRecord.getRecordId());
                    autoAuditData.put("finalDecision", autoAuditRecord.getFinalDecision());
                    autoAuditData.put("decisionReason", autoAuditRecord.getDecisionReason());
                    autoAuditData.put("riskScore", autoAuditRecord.getRiskScore());
                    autoAuditData.put("textAuditResult", autoAuditRecord.getTextAuditResult());
                    autoAuditData.put("imageAuditResult", autoAuditRecord.getImageAuditResult());
                    autoAuditData.put("creditAuditResult", autoAuditRecord.getCreditAuditResult());
                    autoAuditData.put("priceAuditResult", autoAuditRecord.getPriceAuditResult());
                    autoAuditData.put("createdTime", autoAuditRecord.getCreatedTime());
                    materials.put("autoAudit", autoAuditData);
                }
            }

            // 收集人工审核结果
            Map<String, Object> manualAuditData = new HashMap<>();
            manualAuditData.put("taskId", manualTask.getTaskId());
            manualAuditData.put("auditReasons", manualTask.getAuditReasons());
            manualAuditData.put("adminDecision", manualTask.getAdminDecision());
            manualAuditData.put("adminComments", manualTask.getAdminComments());
            manualAuditData.put("priority", manualTask.getPriority());
            manualAuditData.put("createdTime", manualTask.getCreatedTime());
            manualAuditData.put("assignedTime", manualTask.getAssignedTime());
            manualAuditData.put("completedTime", manualTask.getCompletedTime());
            materials.put("manualAudit", manualAuditData);

            // 收集商品基本信息
            Product product = productMapper.selectById(manualTask.getProductId());
            if (product != null) {
                Map<String, Object> productData = new HashMap<>();
                productData.put("id", product.getId());
                productData.put("title", product.getTitle());
                productData.put("description", product.getDescription());
                productData.put("price", product.getPrice());
                productData.put("categoryId", product.getCategoryId());
                productData.put("condition", product.getCondition());
                productData.put("status", product.getStatus());
                productData.put("postedDate", product.getPostedDate());
                materials.put("product", productData);
            }

            // 收集卖家信息
            User seller = userMapper.selectById(manualTask.getSellerId());
            if (seller != null) {
                Map<String, Object> sellerData = new HashMap<>();
                sellerData.put("userId", seller.getUserId());
                sellerData.put("username", seller.getUsername());
                sellerData.put("creditScore", seller.getCreditScore());
                sellerData.put("registrationDate", seller.getRegistrationDate());
                sellerData.put("isActive", seller.getIsActive());
                sellerData.put("role", seller.getRole());
                materials.put("seller", sellerData);
            }

        } catch (Exception e) {
            log.error("收集审核材料失败，人工审核任务ID: {}", manualTask.getTaskId(), e);
        }

        return materials;
    }

    /**
     * 构建自动审核结果摘要
     */
    private String buildAutoAuditResultSummary(ManualAuditTask manualTask) {
        try {
            if (manualTask.getAutoAuditRecordId() == null) {
                return "无自动审核记录";
            }

            AutoAuditRecord autoAuditRecord = autoAuditRecordMapper.selectById(manualTask.getAutoAuditRecordId());
            if (autoAuditRecord == null) {
                return "自动审核记录不存在";
            }

            StringBuilder summary = new StringBuilder();
            summary.append("最终决策: ").append(autoAuditRecord.getFinalDecision()).append("\n");
            summary.append("风险评分: ").append(autoAuditRecord.getRiskScore()).append("\n");

            if (autoAuditRecord.getDecisionReason() != null && !autoAuditRecord.getDecisionReason().trim().isEmpty()) {
                summary.append("决策原因: ").append(autoAuditRecord.getDecisionReason()).append("\n");
            }

            summary.append("审核时间: ").append(autoAuditRecord.getCreatedTime());

            return summary.toString();

        } catch (Exception e) {
            log.error("构建自动审核结果摘要失败，人工审核任务ID: {}", manualTask.getTaskId(), e);
            return "自动审核结果摘要构建失败";
        }
    }

    /**
     * 构建人工审核结果摘要
     */
    private String buildManualAuditResultSummary(ManualAuditTask manualTask) {
        try {
            StringBuilder summary = new StringBuilder();

            // 审核原因
            if (manualTask.getAuditReasons() != null && !manualTask.getAuditReasons().trim().isEmpty()) {
                summary.append("审核原因: ").append(manualTask.getAuditReasons()).append("\n");
            }

            // 管理员决策
            if (manualTask.getAdminDecision() != null) {
                summary.append("管理员决策: ").append(manualTask.getAdminDecision()).append("\n");
            }

            // 管理员备注
            if (manualTask.getAdminComments() != null && !manualTask.getAdminComments().trim().isEmpty()) {
                summary.append("管理员备注: ").append(manualTask.getAdminComments()).append("\n");
            }

            // 优先级
            summary.append("优先级: ").append(manualTask.getPriority()).append("\n");

            // 时间信息
            summary.append("创建时间: ").append(manualTask.getCreatedTime()).append("\n");
            if (manualTask.getAssignedTime() != null) {
                summary.append("分配时间: ").append(manualTask.getAssignedTime()).append("\n");
            }
            if (manualTask.getCompletedTime() != null) {
                summary.append("完成时间: ").append(manualTask.getCompletedTime());
            }

            return summary.toString();

        } catch (Exception e) {
            log.error("构建人工审核结果摘要失败，人工审核任务ID: {}", manualTask.getTaskId(), e);
            return "人工审核结果摘要构建失败";
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("转换JSON失败", e);
            return null;
        }
    }

    /**
     * 从JSON字符串转换为对象
     */
    private <T> T fromJsonString(String json, TypeReference<T> typeRef) {
        try {
            return objectMapper.readValue(json, typeRef);
        } catch (Exception e) {
            log.error("解析JSON失败", e);
            return null;
        }
    }
}
