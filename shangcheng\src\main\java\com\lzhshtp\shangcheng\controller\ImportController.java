package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ImportResultDTO;
import com.lzhshtp.shangcheng.service.ImportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 批量导入控制器
 */
@RestController
@RequestMapping("/api/admin/import")
@PreAuthorize("hasRole('ROLE_ADMIN')")
public class ImportController {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportController.class);
    
    @Autowired
    private ImportService importService;
    
    /**
     * 批量导入用户
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/users")
    public ApiResponse<ImportResultDTO> importUsers(@RequestParam("file") MultipartFile file) {
        logger.info("开始批量导入用户，文件名：{}", file.getOriginalFilename());
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ApiResponse.fail("上传文件不能为空");
            }
            
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB限制
                return ApiResponse.fail("文件大小不能超过10MB");
            }
            
            // 执行导入
            ImportResultDTO result = importService.importUsers(file);
            
            if (result.getSuccessCount() > 0) {
                return ApiResponse.success("用户批量导入完成", result);
            } else {
                return ApiResponse.fail("用户批量导入失败，没有成功导入任何数据", result);
            }
            
        } catch (Exception e) {
            logger.error("批量导入用户失败", e);
            return ApiResponse.fail("批量导入用户失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量导入分类
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/categories")
    public ApiResponse<ImportResultDTO> importCategories(@RequestParam("file") MultipartFile file) {
        logger.info("开始批量导入分类，文件名：{}", file.getOriginalFilename());
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ApiResponse.fail("上传文件不能为空");
            }
            
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB限制
                return ApiResponse.fail("文件大小不能超过10MB");
            }
            
            // 执行导入
            ImportResultDTO result = importService.importCategories(file);
            
            if (result.getSuccessCount() > 0) {
                return ApiResponse.success("分类批量导入完成", result);
            } else {
                return ApiResponse.fail("分类批量导入失败，没有成功导入任何数据", result);
            }
            
        } catch (Exception e) {
            logger.error("批量导入分类失败", e);
            return ApiResponse.fail("批量导入分类失败：" + e.getMessage());
        }
    }
    
    /**
     * 下载用户导入模板
     *
     * @return Excel模板文件
     */
    @GetMapping("/templates/users")
    public ResponseEntity<byte[]> downloadUserTemplate() {
        logger.info("下载用户导入模板");

        try {
            byte[] template = importService.generateUserTemplate();

            // 对中文文件名进行URL编码
            String fileName = "用户导入模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(template);

        } catch (Exception e) {
            logger.error("下载用户导入模板失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 下载分类导入模板
     * 
     * @return Excel模板文件
     */
    @GetMapping("/templates/categories")
    public ResponseEntity<byte[]> downloadCategoryTemplate() {
        logger.info("下载分类导入模板");
        
        try {
            byte[] template = importService.generateCategoryTemplate();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "分类导入模板.xlsx");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(template);
                    
        } catch (Exception e) {
            logger.error("下载分类导入模板失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
