import request from '@/utils/request'

// ==================== 商品搜索 ====================

/**
 * 搜索商品
 * @param {Object} params 搜索参数
 */
export function searchProducts(params) {
  return request({
    url: '/search/products',
    method: 'get',
    params
  })
}

/**
 * 获取商品搜索建议
 * @param {string} q 搜索关键词
 */
export function getProductSearchSuggestions(q) {
  return request({
    url: '/search/products/suggestions',
    method: 'get',
    params: { q }
  })
}

/**
 * 获取相似商品推荐
 * @param {number} productId 商品ID
 */
export function getSimilarProducts(productId) {
  return request({
    url: `/search/products/${productId}/similar`,
    method: 'get'
  })
}

// ==================== 论坛帖子搜索 ====================

/**
 * 搜索论坛帖子
 * @param {Object} params 搜索参数
 */
export function searchForumPosts(params) {
  return request({
    url: '/search/forum/posts',
    method: 'get',
    params
  })
}

/**
 * 获取帖子搜索建议
 * @param {string} q 搜索关键词
 */
export function getForumPostSearchSuggestions(q) {
  return request({
    url: '/search/forum/posts/suggestions',
    method: 'get',
    params: { q }
  })
}

/**
 * 获取相似帖子推荐
 * @param {number} postId 帖子ID
 */
export function getSimilarForumPosts(postId) {
  return request({
    url: `/search/forum/posts/${postId}/similar`,
    method: 'get'
  })
}

// ==================== 搜索分析 ====================

/**
 * 获取热门搜索词
 * @param {number} limit 返回数量
 */
export function getHotSearchKeywords(limit = 10) {
  return request({
    url: '/search/hot-keywords',
    method: 'get',
    params: { limit }
  })
}

// ==================== 管理员功能 ====================

/**
 * 重建搜索索引
 */
export function rebuildSearchIndexes() {
  return request({
    url: '/search/admin/rebuild-indexes',
    method: 'post'
  })
}

/**
 * 同步商品到搜索索引
 * @param {number} productId 商品ID，可选
 */
export function syncProductToES(productId) {
  return request({
    url: '/search/admin/sync-product',
    method: 'post',
    params: productId ? { productId } : {}
  })
}

/**
 * 同步帖子到搜索索引
 * @param {number} postId 帖子ID，可选
 */
export function syncForumPostToES(postId) {
  return request({
    url: '/search/admin/sync-forum-post',
    method: 'post',
    params: postId ? { postId } : {}
  })
}
