<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商品 - 二手交易平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FF4D4F; /* Red */
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
            --shadow-light: rgba(0,0,0,0.05);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 900px;
            margin: 30px auto;
            padding: 0 16px;
        }

        /* Top Nav - Simplified */
        .top-nav {
            background: var(--white);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .top-nav .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }
        .top-nav .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }
        .top-nav .back-link {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            margin-left: 20px;
            transition: color 0.2s ease;
        }
        .top-nav .back-link:hover { color: var(--primary-color); }

        .publish-form-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
        }

        .publish-form-card h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color-dark);
            font-size: 15px;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Noto Sans SC', sans-serif;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        /* 图片上传 */
        .image-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
            margin-bottom: 20px;
        }
        .image-upload-area:hover { border-color: var(--primary-color); }
        .image-upload-area input[type="file"] { display: none; }
        .image-upload-area p { color: var(--text-color-light); font-size: 14px; margin-top: 10px; }
        .image-upload-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
            justify-content: center;
        }
        .image-upload-preview img {
            width: 100px; height: 100px; object-fit: cover; border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .price-options { display: flex; gap: 20px; align-items: center; margin-bottom: 20px; }
        .price-options .radio-group { display: flex; align-items: center; gap: 8px; }
        .price-options input[type="radio"] { width: auto; margin-right: 4px; }

        .form-actions { text-align: center; margin-top: 30px; }
        .submit-button {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 14px 40px;
            border: none;
            border-radius: 28px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        .submit-button:hover { background-color: var(--secondary-color); transform: translateY(-2px); }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container { margin: 20px auto; padding: 0 10px; }
            .publish-form-card { padding: 20px; }
            .publish-form-card h1 { font-size: 24px; margin-bottom: 20px; }
            .price-options { flex-direction: column; align-items: flex-start; }
        }
    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="nav-content">
            <a href="main.html" class="logo">二手交易</a>
            <a href="main.html" class="back-link">返回首页</a>
        </div>
    </nav>

    <div class="container">
        <div class="publish-form-card">
            <h1>发布您的闲置商品</h1>
            <form>
                <div class="form-group">
                    <label for="title">商品标题</label>
                    <input type="text" id="title" name="title" placeholder="简洁明了的标题能吸引更多买家" required>
                </div>

                <div class="form-group">
                    <label for="description">商品描述</label>
                    <textarea id="description" name="description" placeholder="详细描述商品的新旧程度、功能、购买渠道、瑕疵等信息" required></textarea>
                </div>

                <div class="form-group">
                    <label>商品图片 (最多9张)</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <input type="file" id="imageUploadInput" accept="image/*" multiple>
                        <p>点击或拖拽图片到此处上传</p>
                        <div class="image-upload-preview" id="imagePreview"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="category">商品分类</label>
                    <select id="category" name="category" required>
                        <option value="">请选择分类</option>
                        <option value="electronics">数码产品</option>
                        <option value="clothing">服饰箱包</option>
                        <option value="home">家居生活</option>
                        <option value="books">图书文具</option>
                        <option value="virtual">虚拟物品</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="price">期望价格</label>
                    <input type="number" id="price" name="price" placeholder="请输入价格 (元)" step="0.01" min="0" required>
                </div>

                <div class="form-group">
                    <label>价格方式</label>
                    <div class="price-options">
                        <div class="radio-group">
                            <input type="radio" id="fixedPrice" name="priceType" value="fixed" checked>
                            <label for="fixedPrice">一口价</label>
                        </div>
                        <div class="radio-group">
                            <input type="radio" id="negotiable" name="priceType" value="negotiable">
                            <label for="negotiable">可议价</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="condition">新旧程度</label>
                    <select id="condition" name="condition" required>
                        <option value="">请选择新旧程度</option>
                        <option value="new">全新</option>
                        <option value="99">99新</option>
                        <option value="95">95新</option>
                        <option value="9">9成新</option>
                        <option value="8">8成新</option>
                        <option value="7">7成新及以下</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="location">所在地</label>
                    <input type="text" id="location" name="location" placeholder="如：上海市徐汇区" required>
                </div>

                <div class="form-group">
                    <label for="delivery">交易方式</label>
                    <select id="delivery" name="delivery" required>
                        <option value="">请选择交易方式</option>
                        <option value="face-to-face">同城面交</option>
                        <option value="express">快递邮寄</option>
                        <option value="both">面交/快递均可</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="submit" class="submit-button">立即发布</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const imageUploadArea = document.getElementById('imageUploadArea');
            const imageUploadInput = document.getElementById('imageUploadInput');
            const imagePreview = document.getElementById('imagePreview');

            imageUploadArea.addEventListener('click', () => {
                imageUploadInput.click();
            });

            imageUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                imageUploadArea.style.borderColor = 'var(--primary-color)';
            });

            imageUploadArea.addEventListener('dragleave', () => {
                imageUploadArea.style.borderColor = 'var(--border-color)';
            });

            imageUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                imageUploadArea.style.borderColor = 'var(--border-color)';
                handleFiles(e.dataTransfer.files);
            });

            imageUploadInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            function handleFiles(files) {
                imagePreview.innerHTML = ''; // Clear existing previews
                Array.from(files).slice(0, 9).forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            imagePreview.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
</body>
</html> 