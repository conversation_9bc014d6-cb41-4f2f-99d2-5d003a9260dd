package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductDTO {
    private Long id;
    private String title;
    private String description;
    private BigDecimal price;
    private Integer categoryId;
    private String categoryName;  // 分类名称，用于显示
    private String condition;
    private String location;
    private String deliveryMethod;
    private List<String> imageUrls;  // 转换为列表方便前端使用
    private LocalDateTime postedDate;
    private String status;
    private Long sellerId;
    private String sellerName;  // 卖家名称，用于显示
    private String sellerAvatar;  // 卖家头像URL，用于显示
    private Boolean supportOfficialVerification;  // 是否支持官方验货
    private BigDecimal verificationFee;  // 验货费用
} 