package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 验货结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VerificationResultDTO {
    
    @NotNull(message = "验货记录ID不能为空")
    private Long verificationId;
    
    @NotNull(message = "验货结果不能为空")
    private Boolean passed; // true: 通过, false: 不通过
    
    @NotBlank(message = "验货结果描述不能为空")
    private String result; // 验货结果描述
    
    private List<String> imageUrls; // 验货图片URLs
}
