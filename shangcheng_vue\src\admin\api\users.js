import request from '@/utils/request';

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export function getUserList(params) {
  return request({
    url: '/users/admin/users',
    method: 'get',
    params
  });
}

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 * @returns {Promise} 请求Promise对象
 */
export function getUserDetail(userId) {
  return request({
    url: `/users/admin/users/${userId}`,
    method: 'get'
  });
}

/**
 * 更新用户状态
 * @param {Object} data - 用户状态更新数据
 * @returns {Promise} 请求Promise对象
 */
export function updateUserStatus(data) {
  return request({
    url: '/users/admin/users/status',
    method: 'put',
    data
  });
}

/**
 * 批量更新用户状态
 * @param {Object} data - 批量用户状态更新数据
 * @returns {Promise} 请求Promise对象
 */
export function batchUpdateUserStatus(data) {
  return request({
    url: '/users/admin/users/batch-status',
    method: 'put',
    data
  });
} 