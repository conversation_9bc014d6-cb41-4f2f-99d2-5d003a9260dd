package com.lzhshtp.shangcheng.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AlipayConfig {
    
    // 应用ID,您的APPID，收款账号既是您的APPID对应支付宝账号
    @Value("${alipay.app-id}")
    private String appId;
    
    // 商户私钥，您的PKCS8格式RSA2私钥
    @Value("${alipay.merchant-private-key}")
    private String merchantPrivateKey;
    
    // 支付宝公钥
    @Value("${alipay.alipay-public-key}")
    private String alipayPublicKey;
    
    // 订单支付回调地址
    @Value("${alipay.order-notify-url}")
    private String orderNotifyUrl;

    @Value("${alipay.order-return-url}")
    private String orderReturnUrl;

    // 充值回调地址
    @Value("${alipay.recharge-notify-url}")
    private String rechargeNotifyUrl;

    @Value("${alipay.recharge-return-url}")
    private String rechargeReturnUrl;

    // 同步处理模式
    @Value("${alipay.sync-mode:false}")
    private boolean syncMode;
    
    // 签名方式
    @Value("${alipay.sign-type:RSA2}")
    private String signType;
    
    // 字符编码格式
    @Value("${alipay.charset:utf-8}")
    private String charset;
    
    // 支付宝网关，沙箱环境使用：https://openapi.alipaydev.com/gateway.do
    @Value("${alipay.gateway-url:https://openapi.alipaydev.com/gateway.do}")
    private String gatewayUrl;
    
    // 支付宝网关
    @Value("${alipay.log-path:/tmp/alipay.log}")
    private String logPath;
    
    @Bean
    public AlipayClient alipayClient() {
        return new DefaultAlipayClient(gatewayUrl, appId, merchantPrivateKey, "json", charset, alipayPublicKey, signType);
    }
    
    public String getAppId() {
        return appId;
    }
    
    public String getMerchantPrivateKey() {
        return merchantPrivateKey;
    }
    
    public String getAlipayPublicKey() {
        return alipayPublicKey;
    }
    
    public String getOrderNotifyUrl() {
        return orderNotifyUrl;
    }

    public String getOrderReturnUrl() {
        return orderReturnUrl;
    }

    public String getRechargeNotifyUrl() {
        return rechargeNotifyUrl;
    }

    public String getRechargeReturnUrl() {
        return rechargeReturnUrl;
    }

    public boolean isSyncMode() {
        return syncMode;
    }
    
    public String getSignType() {
        return signType;
    }
    
    public String getCharset() {
        return charset;
    }
    
    public String getGatewayUrl() {
        return gatewayUrl;
    }
    
    public String getLogPath() {
        return logPath;
    }
} 