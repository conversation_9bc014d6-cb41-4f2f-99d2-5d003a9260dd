package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 卖家信用信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SellerCreditInfo {

    /**
     * 卖家ID
     */
    private Long sellerId;

    /**
     * 卖家名称
     */
    private String sellerName;

    /**
     * 当前信用分
     */
    private Integer creditScore;

    /**
     * 信用等级
     */
    private String creditLevel;

    /**
     * 信用等级颜色
     */
    private String creditLevelColor;

    /**
     * 总评价数
     */
    private Integer totalReviews;

    /**
     * 平均评分
     */
    private BigDecimal averageRating;

    /**
     * 好评率（百分比）
     */
    private BigDecimal positiveRate;

    /**
     * 好评数
     */
    private Integer positiveReviews;

    /**
     * 差评数
     */
    private Integer negativeReviews;

    /**
     * 星级分布
     */
    private Map<Integer, Integer> ratingDistribution;

    /**
     * 最近评价列表
     */
    private List<ReviewResponse> recentReviews;

    /**
     * 获取信用等级
     */
    public static String getCreditLevel(int score) {
        if (score >= 95) return "钻石卖家";
        if (score >= 85) return "金牌卖家";
        if (score >= 70) return "银牌卖家";
        if (score >= 50) return "铜牌卖家";
        return "普通卖家";
    }

    /**
     * 获取信用等级颜色
     */
    public static String getCreditLevelColor(int score) {
        if (score >= 95) return "#1890ff";  // 钻石蓝
        if (score >= 85) return "#faad14";  // 金色
        if (score >= 70) return "#722ed1";  // 银紫色
        if (score >= 50) return "#fa8c16";  // 铜橙色
        return "#8c8c8c";                   // 灰色
    }

    /**
     * 获取信用等级图标
     */
    public static String getCreditLevelIcon(int score) {
        if (score >= 95) return "💎";  // 钻石
        if (score >= 85) return "🥇";  // 金牌
        if (score >= 70) return "🥈";  // 银牌
        if (score >= 50) return "🥉";  // 铜牌
        return "⭐";                   // 普通星星
    }

    /**
     * 获取信用等级描述
     */
    public static String getCreditLevelDescription(int score) {
        if (score >= 95) return "信誉极佳，值得信赖的顶级卖家";
        if (score >= 85) return "信誉优秀，服务质量很好";
        if (score >= 70) return "信誉良好，值得信赖";
        if (score >= 50) return "信誉一般，请谨慎选择";
        return "信誉较低，建议谨慎交易";
    }

    /**
     * 是否为优质卖家
     */
    public boolean isQualitySeller() {
        return creditScore != null && creditScore >= 85;
    }

    /**
     * 是否需要警告
     */
    public boolean needsWarning() {
        return creditScore != null && creditScore < 50;
    }

    /**
     * 获取好评率百分比字符串
     */
    public String getPositiveRateString() {
        if (positiveRate == null) {
            return "0%";
        }
        return positiveRate.setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 获取平均评分字符串
     */
    public String getAverageRatingString() {
        if (averageRating == null) {
            return "0.0";
        }
        return averageRating.setScale(1, BigDecimal.ROUND_HALF_UP).toString();
    }
}
