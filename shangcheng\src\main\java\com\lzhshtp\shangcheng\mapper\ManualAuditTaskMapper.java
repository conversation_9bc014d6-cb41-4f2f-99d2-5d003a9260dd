package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 人工审核任务Mapper
 */
@Mapper
public interface ManualAuditTaskMapper extends BaseMapper<ManualAuditTask> {
    
    /**
     * 查询待审核的任务列表（管理员未认领的任务）
     * 使用XML映射
     */
    List<ManualAuditTask> findPendingTasks();
    
    /**
     * 查询指定管理员正在处理的任务
     */
    @Select("SELECT * FROM tb_lzhshtp_manual_audit_tasks " +
            "WHERE lzhshtp_admin_id = #{adminId} AND lzhshtp_status = 'in_progress' " +
            "ORDER BY lzhshtp_created_time ASC")
    List<ManualAuditTask> findTasksByAdmin(@Param("adminId") Long adminId);
    
    /**
     * 管理员开始审核任务
     */
    @Update("UPDATE tb_lzhshtp_manual_audit_tasks " +
            "SET lzhshtp_admin_id = #{adminId}, lzhshtp_status = 'in_progress', " +
            "lzhshtp_assigned_time = #{assignedTime} " +
            "WHERE lzhshtp_task_id = #{taskId} AND lzhshtp_admin_id IS NULL")
    int startAuditTask(@Param("taskId") Long taskId, 
                       @Param("adminId") Long adminId, 
                       @Param("assignedTime") LocalDateTime assignedTime);
    
    /**
     * 根据商品ID查询人工审核任务
     */
    @Select("SELECT * FROM tb_lzhshtp_manual_audit_tasks WHERE lzhshtp_product_id = #{productId}")
    ManualAuditTask findByProductId(@Param("productId") Long productId);

    /**
     * 根据条件查询审核任务
     * 实现在 ManualAuditTaskMapper.xml 中
     */
    List<ManualAuditTask> findByConditions(Map<String, Object> params);

    /**
     * 根据条件统计审核任务数量
     * 实现在 ManualAuditTaskMapper.xml 中
     */
    Long countByConditions(Map<String, Object> params);

    /**
     * 根据状态统计数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_manual_audit_tasks WHERE lzhshtp_status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 统计超时任务数量
     * 实现在 ManualAuditTaskMapper.xml 中
     */
    Long countOverdueTasks(@Param("currentTime") String currentTime);
}
