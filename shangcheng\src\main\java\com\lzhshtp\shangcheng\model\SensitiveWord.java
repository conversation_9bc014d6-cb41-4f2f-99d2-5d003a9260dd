package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 敏感词库实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_sensitive_words")
public class SensitiveWord {
    
    @TableId(value = "lzhshtp_word_id", type = IdType.AUTO)
    private Long wordId;
    
    @TableField("lzhshtp_word")
    private String word;
    
    @TableField("lzhshtp_word_type")
    private String wordType;  // banned, sensitive, brand
    
    @TableField("lzhshtp_severity_level")
    private Integer severityLevel;
    
    @TableField("lzhshtp_category")
    private String category;
    
    @TableField("lzhshtp_is_active")
    private Boolean isActive;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    @TableField("lzhshtp_created_by")
    private Long createdBy;
}
