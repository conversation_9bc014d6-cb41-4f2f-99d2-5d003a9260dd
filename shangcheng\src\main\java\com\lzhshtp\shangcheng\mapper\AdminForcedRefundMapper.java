package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.AdminForcedRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

import java.util.List;

/**
 * 管理员强制退款Mapper接口
 */
@Mapper
public interface AdminForcedRefundMapper extends BaseMapper<AdminForcedRefund> {
    
    /**
     * 根据卖家ID查询待处理的强制退款
     */
    @Select("SELECT " +
            "lzhshtp_forced_refund_id, lzhshtp_refund_request_id, lzhshtp_seller_id, lzhshtp_required_amount, " +
            "lzhshtp_status, lzhshtp_admin_id, lzhshtp_admin_note, lzhshtp_created_time, lzhshtp_completed_time " +
            "FROM tb_lzhshtp_admin_forced_refunds WHERE lzhshtp_seller_id = #{sellerId} AND lzhshtp_status = 'pending' ORDER BY lzhshtp_created_time ASC")
    @Results({
        @Result(column = "lzhshtp_forced_refund_id", property = "forcedRefundId"),
        @Result(column = "lzhshtp_refund_request_id", property = "refundRequestId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_required_amount", property = "requiredAmount"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_admin_id", property = "adminId"),
        @Result(column = "lzhshtp_admin_note", property = "adminNote"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_completed_time", property = "completedTime")
    })
    List<AdminForcedRefund> selectPendingBySellerId(@Param("sellerId") Long sellerId);
    
    /**
     * 查询所有待处理的强制退款
     */
    @Select("SELECT " +
            "lzhshtp_forced_refund_id, lzhshtp_refund_request_id, lzhshtp_seller_id, lzhshtp_required_amount, " +
            "lzhshtp_status, lzhshtp_admin_id, lzhshtp_admin_note, lzhshtp_created_time, lzhshtp_completed_time " +
            "FROM tb_lzhshtp_admin_forced_refunds WHERE lzhshtp_status = 'pending' ORDER BY lzhshtp_created_time ASC")
    @Results({
        @Result(column = "lzhshtp_forced_refund_id", property = "forcedRefundId"),
        @Result(column = "lzhshtp_refund_request_id", property = "refundRequestId"),
        @Result(column = "lzhshtp_seller_id", property = "sellerId"),
        @Result(column = "lzhshtp_required_amount", property = "requiredAmount"),
        @Result(column = "lzhshtp_status", property = "status"),
        @Result(column = "lzhshtp_admin_id", property = "adminId"),
        @Result(column = "lzhshtp_admin_note", property = "adminNote"),
        @Result(column = "lzhshtp_created_time", property = "createdTime"),
        @Result(column = "lzhshtp_completed_time", property = "completedTime")
    })
    List<AdminForcedRefund> selectAllPending();
    
    /**
     * 检查卖家是否有待处理的强制退款
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_admin_forced_refunds WHERE lzhshtp_seller_id = #{sellerId} AND lzhshtp_status = 'pending'")
    int countPendingBySellerId(@Param("sellerId") Long sellerId);
}
