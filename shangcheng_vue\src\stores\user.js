import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoggedIn = ref(!!localStorage.getItem('token'))

  // 获取用户信息
  async function fetchUserInfo() {
    if (!token.value) {
      console.log('fetchUserInfo: 没有token，无法获取用户信息');
      return;
    }
    
    try {
      console.log('fetchUserInfo: 开始获取用户信息，当前token:', token.value);
      const res = await getUserInfo();
      console.log('fetchUserInfo: 获取用户信息响应', res);
      
      if (res.code === 200 && res.data) {
        userInfo.value = res.data;
        console.log('fetchUserInfo: 用户信息已保存', userInfo.value);
        return userInfo.value;
      } else {
        console.error('fetchUserInfo: 获取用户信息失败，响应不符合预期', res);
        throw new Error('获取用户信息失败: ' + (res.message || '未知错误'));
      }
    } catch (error) {
      console.error('fetchUserInfo: 获取用户信息失败:', error);
      // 如果是401错误，可能是token过期，清除token
      if (error.response && error.response.status === 401) {
        console.warn('fetchUserInfo: token可能已过期，清除登录状态');
        logout();
      }
      throw error;
    }
  }

  // 设置用户登录状态
  function setToken(newToken) {
    console.log('setToken: 设置新token', newToken)
    token.value = newToken
    localStorage.setItem('token', newToken)
    isLoggedIn.value = true
  }

  // 设置用户信息
  function setUser(userData) {
    console.log('setUser: 设置用户信息', userData)
    userInfo.value = userData
    if (userData.token) {
      setToken(userData.token)
    }
  }

  // 清除用户状态
  function logout() {
    console.log('logout: 清除用户状态')
    token.value = ''
    userInfo.value = null
    isLoggedIn.value = false
    localStorage.removeItem('token')
  }

  return {
    userInfo,
    token,
    isLoggedIn,
    fetchUserInfo,
    setToken,
    setUser,
    logout
  }
}) 