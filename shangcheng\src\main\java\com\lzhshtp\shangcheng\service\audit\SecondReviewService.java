package com.lzhshtp.shangcheng.service.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.audit.SecondReviewDecisionRequest;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.SecondReviewTaskMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.MaterialRequest;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 二度复审服务
 */
@Slf4j
@Service
public class SecondReviewService {

    @Autowired
    private SecondReviewTaskMapper secondReviewTaskMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SupplementaryMaterialService supplementaryMaterialService;

    @Autowired
    private MaterialRequestService materialRequestService;

    /**
     * 获取二度复审任务列表
     */
    public PageResult<SecondReviewTask> getSecondReviewTasks(Integer page, Integer pageSize,
                                                           String status, String escalationReason,
                                                           Long productId, Long reviewerId) {

        // 构建查询条件
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }
        if (StringUtils.hasText(escalationReason)) {
            params.put("escalationReason", escalationReason);
        }
        if (productId != null) {
            params.put("productId", productId);
        }
        if (reviewerId != null) {
            params.put("reviewerId", reviewerId);
        }

        // 分页查询
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("limit", pageSize);

        List<SecondReviewTask> tasks = secondReviewTaskMapper.findByConditions(params);
        Long total = secondReviewTaskMapper.countByConditions(params);

        // 补充商品和用户信息
        for (SecondReviewTask task : tasks) {
            if (task != null) {
                enrichTaskWithDetails(task);
            }
        }

        return new PageResult<>(tasks, total, page, pageSize);
    }

    /**
     * 获取二度复审统计数据
     */
    public Map<String, Object> getSecondReviewStats() {
        // 获取各种状态的统计数据
        Long pendingCount = secondReviewTaskMapper.countByStatus("pending");
        Long inProgressCount = secondReviewTaskMapper.countByStatus("in_progress");
        Long completedCount = secondReviewTaskMapper.countByStatus("completed");
        Long approvedCount = secondReviewTaskMapper.countByFinalDecision("approved");
        Long rejectedCount = secondReviewTaskMapper.countByFinalDecision("rejected");

        Map<String, Object> stats = new HashMap<>();
        stats.put("pending", pendingCount != null ? pendingCount : 0);
        stats.put("in_progress", inProgressCount != null ? inProgressCount : 0);
        stats.put("completed", completedCount != null ? completedCount : 0);
        stats.put("approved", approvedCount != null ? approvedCount : 0);
        stats.put("rejected", rejectedCount != null ? rejectedCount : 0);

        return stats;
    }

    /**
     * 认领二度复审任务
     */
    public boolean claimSecondReviewTask(Long taskId, Long reviewerId) {
        SecondReviewTask task = secondReviewTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("复审任务不存在");
        }

        if (!"pending".equals(task.getStatus())) {
            return false; // 任务已被认领或已完成
        }

        // 更新任务状态
        task.setStatus("in_progress");
        task.setReviewerId(reviewerId);
        task.setClaimedTime(LocalDateTime.now());

        int updated = secondReviewTaskMapper.updateById(task);
        return updated > 0;
    }

    /**
     * 获取二度复审任务详情
     */
    public SecondReviewTask getSecondReviewTaskDetail(Long taskId) {
        SecondReviewTask task = secondReviewTaskMapper.selectById(taskId);

        if (task != null) {
            enrichTaskWithDetails(task);
        }

        return task;
    }

    /**
     * 提交二度复审决策
     */
    @Transactional
    public void submitSecondReviewDecision(Long taskId, SecondReviewDecisionRequest request) {
        log.info("提交二度复审决策，任务ID: {}, 决策: {}", taskId, request.getDecision());

        SecondReviewTask task = secondReviewTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("复审任务不存在");
        }

        if (!"in_progress".equals(task.getStatus())) {
            throw new RuntimeException("任务状态不正确，无法提交决策");
        }

        // 更新任务基本信息
        task.setFinalDecision(request.getDecision());
        task.setReviewComments(request.getComments());
        task.setRiskLevel(request.getRiskLevel());

        // 根据决策类型处理
        Product product = productMapper.selectById(task.getProductId());
        switch (request.getDecision()) {
            case "approved":
                // 二度复审通过，商品最终通过
                task.setStatus("completed");
                task.setCompletedTime(LocalDateTime.now());
                product.setStatus("available");
                productMapper.updateById(product);
                log.info("二度复审通过，商品上架，商品ID: {}", product.getId());
                break;

            case "rejected":
                // 二度复审拒绝，直接删除商品
                task.setStatus("completed");
                task.setCompletedTime(LocalDateTime.now());
                productMapper.deleteById(product.getId());
                log.info("二度复审拒绝，已删除商品，商品ID: {}", product.getId());
                break;

            case "request_materials":
                // 要求补充材料
                task.setStatus("material_requested");
                product.setStatus("material_requested");
                productMapper.updateById(product);

                // 创建材料请求
                createSecondReviewMaterialRequest(task, request.getReviewerId(), request.getComments());
                log.info("二度复审要求补充材料，商品ID: {}, 任务ID: {}", product.getId(), task.getTaskId());
                break;

            default:
                throw new RuntimeException("不支持的决策类型: " + request.getDecision());
        }

        secondReviewTaskMapper.updateById(task);
        log.info("二度复审决策处理完成，任务ID: {}, 决策: {}", taskId, request.getDecision());
    }

    /**
     * 获取二度复审任务的所有材料
     */
    public Map<String, Object> getSecondReviewTaskMaterials(Long taskId) {
        SecondReviewTask task = secondReviewTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("复审任务不存在");
        }

        Map<String, Object> result = new HashMap<>();

        // 获取商品信息
        Product product = productMapper.selectById(task.getProductId());
        result.put("product", product);

        // 解析并返回所有之前的审核材料
        if (task.getAllPreviousMaterials() != null) {
            try {
                String materialsJson = task.getAllPreviousMaterials().toString();
                Map<String, Object> allMaterials = objectMapper.readValue(materialsJson,
                    new TypeReference<Map<String, Object>>() {});
                result.putAll(allMaterials);
            } catch (Exception e) {
                log.error("解析审核材料JSON失败，任务ID: {}", taskId, e);
                result.put("error", "审核材料解析失败");
            }
        }

        // 添加升级原因
        result.put("escalationReason", task.getEscalationReason());

        return result;
    }

    /**
     * 丰富任务详情
     */
    private void enrichTaskWithDetails(SecondReviewTask task) {
        if (task == null) {
            return;
        }

        log.info("开始丰富任务详情，任务ID: {}", task.getTaskId());

        // 补充商品信息
        if (task.getProductId() != null) {
            Product product = productMapper.selectById(task.getProductId());
            if (product != null) {
                task.setProductTitle(product.getTitle());
                task.setProductDescription(product.getDescription());
                task.setProductPrice(product.getPrice());
                task.setProductImages(product.getImageUrls());
                task.setProductCondition(product.getCondition());
            }
        }

        // 解析JSON格式的审核材料
        if (task.getAllPreviousMaterials() != null) {
            try {
                String materialsJson = task.getAllPreviousMaterials().toString();
                log.info("任务ID: {} 的审核材料JSON: {}", task.getTaskId(), materialsJson);
                Map<String, Object> materials = objectMapper.readValue(materialsJson,
                    new TypeReference<Map<String, Object>>() {});
                log.info("解析后的材料: {}", materials);

                // 提取自动审核结果摘要
                if (materials.containsKey("autoAudit")) {
                    Map<String, Object> autoAudit = (Map<String, Object>) materials.get("autoAudit");
                    StringBuilder autoSummary = new StringBuilder();

                    String finalDecision = (String) autoAudit.get("finalDecision");
                    String finalDecisionText = convertFinalDecisionToText(finalDecision);
                    log.info("转换自动审核决策: {} -> {}", finalDecision, finalDecisionText);
                    autoSummary.append("最终决策: ").append(finalDecisionText).append("\n");
                    String autoResult = autoSummary.toString();
                    task.setAutoAuditResult(autoResult);
                    log.info("设置自动审核结果: {}", autoResult);
                }

                // 提取人工审核结果摘要
                if (materials.containsKey("manualAudit")) {
                    Map<String, Object> manualAudit = (Map<String, Object>) materials.get("manualAudit");
                    StringBuilder manualSummary = new StringBuilder();

                    String adminDecision = (String) manualAudit.get("adminDecision");
                    String adminDecisionText = convertAdminDecisionToText(adminDecision);
                    log.info("转换管理员决策: {} -> {}", adminDecision, adminDecisionText);
                    manualSummary.append("管理员决策: ").append(adminDecisionText).append("\n");
                    if (manualAudit.get("adminComments") != null) {
                        manualSummary.append("备注: ").append(manualAudit.get("adminComments"));
                    }
                    String manualResult = manualSummary.toString();
                    task.setManualAuditResult(manualResult);
                    log.info("设置人工审核结果: {}", manualResult);
                }

            } catch (Exception e) {
                log.error("解析审核材料JSON失败，任务ID: {}", task.getTaskId(), e);
                task.setAutoAuditResult("解析失败");
                task.setManualAuditResult("解析失败");
            }
        }
    }

    /**
     * 根据决策更新商品状态
     */
    private void updateProductStatusByDecision(Long productId, String decision) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            return;
        }

        switch (decision) {
            case "approved":
                product.setStatus("available");
                break;
            case "rejected":
                product.setStatus("off_shelf_by_admin");
                break;
            case "request_materials":
                product.setStatus("material_requested");
                break;
        }

        productMapper.updateById(product);
    }

    /**
     * 转换最终决策为中文文本
     */
    private String convertFinalDecisionToText(String finalDecision) {
        if (finalDecision == null) {
            return "未知";
        }

        switch (finalDecision) {
            case "approved":
                return "通过";
            case "rejected":
                return "拒绝";
            case "manual_review":
                return "需要人工审核";
            case "second_review":
                return "需要二度复审";
            default:
                return finalDecision;
        }
    }

    /**
     * 转换管理员决策为中文文本
     */
    private String convertAdminDecisionToText(String adminDecision) {
        if (adminDecision == null) {
            return "未决策";
        }

        switch (adminDecision) {
            case "approved":
                return "通过";
            case "rejected":
                return "拒绝";
            case "request_materials":
                return "要求补充材料";
            case "escalate_to_second_review":
                return "升级到二度复审";
            default:
                return adminDecision;
        }
    }

    /**
     * 创建二度复审材料请求
     */
    private void createSecondReviewMaterialRequest(SecondReviewTask task, Long adminId, String comments) {
        log.info("创建二度复审材料请求，任务ID: {}, 管理员ID: {}", task.getTaskId(), adminId);

        // 根据审核情况确定需要的材料类型
        List<String> requiredMaterials = determineRequiredMaterialsForSecondReview(task, comments);

        // 生成请求原因
        String requestReason = generateSecondReviewMaterialRequestReason(task, comments);

        // 创建材料请求
        MaterialRequest materialRequest = materialRequestService.createMaterialRequest(
            task.getProductId(),
            "second_review",
            task.getTaskId(),
            adminId,
            requiredMaterials,
            requestReason
        );

        log.info("二度复审材料请求创建成功，请求ID: {}", materialRequest.getRequestId());
    }

    /**
     * 确定二度复审需要的材料类型
     */
    private List<String> determineRequiredMaterialsForSecondReview(SecondReviewTask task, String comments) {
        List<String> materials = new ArrayList<>();

        // 根据升级原因和备注确定需要的材料
        if (comments != null && comments.contains("价格")) {
            materials.add("价格证明材料");
            materials.add("市场价格对比");
        }

        if (comments != null && comments.contains("真实性")) {
            materials.add("商品真实性证明");
            materials.add("购买凭证");
        }

        if (comments != null && comments.contains("质量")) {
            materials.add("质量检测报告");
            materials.add("商品详细图片");
        }

        // 默认材料
        if (materials.isEmpty()) {
            materials.add("补充商品信息");
            materials.add("相关证明材料");
        }

        return materials;
    }

    /**
     * 生成二度复审材料请求原因
     */
    private String generateSecondReviewMaterialRequestReason(SecondReviewTask task, String comments) {
        StringBuilder reason = new StringBuilder();
        reason.append("二度复审过程中发现需要补充材料。");

        if (task.getEscalationReason() != null) {
            reason.append("升级原因：").append(task.getEscalationReason()).append("。");
        }

        if (comments != null && !comments.trim().isEmpty()) {
            reason.append("具体要求：").append(comments);
        }

        return reason.toString();
    }
}
