package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.mapper.CreditScoreLogMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.CreditScoreLog;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.CreditScoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 信用分管理服务实现类
 */
@Service
public class CreditScoreServiceImpl implements CreditScoreService {

    private static final Logger logger = LoggerFactory.getLogger(CreditScoreServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CreditScoreLogMapper creditScoreLogMapper;

    /**
     * 更新用户信用分
     */
    @Override
    @Transactional
    public int updateCreditScore(Long userId, int change, CreditScoreLog.ChangeType changeType, 
                                String reason, Long relatedOrderId, Long relatedReviewId, Long operatorId) {
        try {
            // 获取用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }

            int oldScore = user.getCreditScore();
            int newScore = Math.max(10, Math.min(100, oldScore + change)); // 限制在10-100分之间

            // 更新用户信用分
            user.setCreditScore(newScore);
            userMapper.updateById(user);

            // 记录变更日志
            CreditScoreLog log = CreditScoreLog.builder()
                    .userId(userId)
                    .changeType(changeType)
                    .changeReason(reason)
                    .scoreBefore(oldScore)
                    .scoreAfter(newScore)
                    .scoreChange(change)
                    .relatedOrderId(relatedOrderId)
                    .relatedReviewId(relatedReviewId)
                    .operatorId(operatorId)
                    .createdTime(LocalDateTime.now())
                    .build();

            creditScoreLogMapper.insert(log);

            logger.info("用户信用分更新成功，用户ID: {}, 变化: {}, 原因: {}, 分数: {} -> {}", 
                       userId, change, reason, oldScore, newScore);

            return newScore;

        } catch (Exception e) {
            logger.error("更新用户信用分失败，用户ID: {}, 变化: {}", userId, change, e);
            throw e;
        }
    }

    /**
     * 更新用户信用分（简化版本）
     */
    @Override
    public int updateCreditScore(Long userId, int change, CreditScoreLog.ChangeType changeType, String reason) {
        return updateCreditScore(userId, change, changeType, reason, null, null, null);
    }

    /**
     * 获取用户信用分变更历史
     */
    @Override
    public List<Map<String, Object>> getUserCreditHistory(Long userId, int limit) {
        try {
            return creditScoreLogMapper.getUserCreditHistory(userId, limit);
        } catch (Exception e) {
            logger.error("获取用户信用分历史失败，用户ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * 获取用户信用分统计
     */
    @Override
    public Map<String, Object> getUserCreditStatistics(Long userId, int days) {
        try {
            return creditScoreLogMapper.getUserCreditStatistics(userId, days);
        } catch (Exception e) {
            logger.error("获取用户信用分统计失败，用户ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * 获取系统信用分统计
     */
    @Override
    public List<Map<String, Object>> getSystemCreditStatistics(int days) {
        try {
            return creditScoreLogMapper.getSystemCreditStatistics(days);
        } catch (Exception e) {
            logger.error("获取系统信用分统计失败", e);
            throw e;
        }
    }

    /**
     * 获取信用等级
     */
    @Override
    public String getCreditLevel(int score) {
        if (score >= 95) return "钻石卖家";
        if (score >= 85) return "金牌卖家";
        if (score >= 70) return "银牌卖家";
        if (score >= 50) return "铜牌卖家";
        return "普通卖家";
    }

    /**
     * 获取信用等级颜色
     */
    @Override
    public String getCreditLevelColor(int score) {
        if (score >= 95) return "#1890ff";  // 钻石蓝
        if (score >= 85) return "#faad14";  // 金色
        if (score >= 70) return "#722ed1";  // 银紫色
        if (score >= 50) return "#fa8c16";  // 铜橙色
        return "#8c8c8c";                   // 灰色
    }

    /**
     * 获取信用等级描述
     */
    @Override
    public String getCreditLevelDescription(int score) {
        if (score >= 95) return "信誉极佳，值得信赖的顶级卖家";
        if (score >= 85) return "信誉优秀，服务质量很好";
        if (score >= 70) return "信誉良好，值得信赖";
        if (score >= 50) return "信誉一般，请谨慎选择";
        return "信誉较低，建议谨慎交易";
    }

    /**
     * 检查用户信用分变更频率
     */
    @Override
    public int checkCreditChangeFrequency(Long userId, int hours) {
        try {
            return creditScoreLogMapper.countRecentChanges(userId, hours);
        } catch (Exception e) {
            logger.error("检查信用分变更频率失败，用户ID: {}", userId, e);
            return 0;
        }
    }

    /**
     * 批量更新信用分
     */
    @Override
    @Transactional
    public int batchUpdateCreditScore(List<Map<String, Object>> updates) {
        int successCount = 0;
        for (Map<String, Object> update : updates) {
            try {
                Long userId = (Long) update.get("userId");
                Integer change = (Integer) update.get("change");
                String reason = (String) update.get("reason");
                
                updateCreditScore(userId, change, CreditScoreLog.ChangeType.SYSTEM, reason);
                successCount++;
            } catch (Exception e) {
                logger.error("批量更新信用分失败，更新项: {}", update, e);
            }
        }
        return successCount;
    }

    /**
     * 重置用户信用分
     */
    @Override
    @Transactional
    public boolean resetCreditScore(Long userId, int newScore, String reason, Long operatorId) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return false;
            }

            int oldScore = user.getCreditScore();
            int change = newScore - oldScore;

            return updateCreditScore(userId, change, CreditScoreLog.ChangeType.SYSTEM, 
                                   "管理员重置：" + reason, null, null, operatorId) == newScore;

        } catch (Exception e) {
            logger.error("重置用户信用分失败，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 获取信用分排行榜
     */
    @Override
    public List<Map<String, Object>> getCreditScoreRanking(int limit) {
        try {
            // TODO: 实现排行榜查询
            return List.of();
        } catch (Exception e) {
            logger.error("获取信用分排行榜失败", e);
            throw e;
        }
    }
}
