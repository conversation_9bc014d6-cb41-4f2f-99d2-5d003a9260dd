<template>
  <div class="category-list-container">
    <div class="page-header">
      <h2>商品分类管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="handleImportCategories">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="primary" @click="handleAddCategory(null)">新增顶级分类</el-button>
        <el-button @click="refreshCategoryList">刷新</el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索分类名称"
        clearable
        class="search-input"
        @keyup.enter="filterCategories"
      >
        <template #append>
          <el-button @click="filterCategories">搜索</el-button>
        </template>
      </el-input>
    </div>

    <!-- 分类表格 -->
    <el-table
      v-loading="loading"
      :data="filteredCategories"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="name" label="分类名称" min-width="200">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="description" label="描述" min-width="300">
        <template #default="{ row }">
          <span>{{ row.description || '无描述' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button
            size="small" 
            type="primary" 
            @click="handleEditCategory(row)"
          >
            编辑
          </el-button>
          
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDeleteCategory(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分类编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="categoryForm.description" 
            type="textarea" 
            placeholder="请输入分类描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCategoryForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <ImportDialog
      :visible="importDialogVisible"
      @update:visible="importDialogVisible = $event"
      import-type="category"
      @import-success="handleImportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Upload } from '@element-plus/icons-vue';
import {
  getAllCategories,
  createCategory,
  updateCategory,
  deleteCategory
} from '@/admin/api/categories';
import ImportDialog from '@/admin/components/ImportDialog.vue';

// 数据状态
const loading = ref(false);
const categories = ref([]);
const searchKeyword = ref('');

// 导入相关
const importDialogVisible = ref(false);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('新增分类');
const isEdit = ref(false);
const categoryFormRef = ref(null);
const categoryForm = ref({
  id: null,
  name: '',
  description: ''
});

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 过滤后的分类列表
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return categories.value;
  }

  // 简单的平铺列表搜索
  return categories.value.filter(category =>
    category.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  );
});

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true;
  try {
    const res = await getAllCategories();
    if (res.code === 200) {
      categories.value = res.data || [];
    } else {
      ElMessage.error(res.message || '获取分类列表失败');
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};



// 刷新分类列表
const refreshCategoryList = () => {
  fetchCategories();
};

// 过滤分类
const filterCategories = () => {
  // 已通过计算属性实现
};

// 打开新增分类对话框
const handleAddCategory = () => {
  isEdit.value = false;
  dialogTitle.value = '新增分类';
  categoryForm.value = {
    id: null,
    name: '',
    description: ''
  };
  dialogVisible.value = true;
};

// 打开编辑分类对话框
const handleEditCategory = (category) => {
  isEdit.value = true;
  dialogTitle.value = '编辑分类';
  categoryForm.value = {
    id: category.id,
    name: category.name,
    description: category.description || ''
  };
  dialogVisible.value = true;
};

// 删除分类
const handleDeleteCategory = (category) => {
  ElMessageBox.confirm(
    `确定要删除分类 "${category.name}" 吗？如果存在子分类或关联商品将无法删除。`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await deleteCategory(category.id);
      if (res.code === 200) {
        ElMessage.success('删除分类成功');
        refreshCategoryList();
      } else {
        ElMessage.error(res.message || '删除分类失败');
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      ElMessage.error('删除分类失败');
    }
  }).catch(() => {});
};

// 提交分类表单
const submitCategoryForm = async () => {
  if (!categoryFormRef.value) return;

  try {
    // 表单验证
    await categoryFormRef.value.validate();

    const formData = {
      name: categoryForm.value.name,
      description: categoryForm.value.description
    };
    
    let res;
    if (isEdit.value) {
      // 更新分类
      res = await updateCategory(categoryForm.value.id, formData);
    } else {
      // 创建分类
      res = await createCategory(formData);
    }
    
    if (res.code === 200) {
      ElMessage.success(isEdit.value ? '更新分类成功' : '创建分类成功');
      dialogVisible.value = false;
      refreshCategoryList();
    } else {
      ElMessage.error(res.message || (isEdit.value ? '更新分类失败' : '创建分类失败'));
    }
  } catch (error) {
    console.error(isEdit.value ? '更新分类失败:' : '创建分类失败:', error);
    ElMessage.error(isEdit.value ? '更新分类失败' : '创建分类失败');
  }
};

// 重置表单
const resetForm = () => {
  if (categoryFormRef.value) {
    categoryFormRef.value.resetFields();
  }
  categoryForm.value = {
    id: null,
    name: '',
    description: ''
  };
};

// 打开导入对话框
const handleImportCategories = () => {
  importDialogVisible.value = true;
};

// 导入成功回调
const handleImportSuccess = (result) => {
  ElMessage.success(`分类导入完成！成功导入 ${result.successCount} 个分类`);
  // 刷新分类列表
  fetchCategories();
};

// 初始化
onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.category-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
}

.search-filter-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.search-input {
  width: 300px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 