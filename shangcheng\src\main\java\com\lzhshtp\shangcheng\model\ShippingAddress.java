package com.lzhshtp.shangcheng.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 收货地址实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingAddress {
    private Long addressId;       // 收货地址唯一标识ID
    private Long userId;          // 所属用户ID
    private String recipientName; // 收货人姓名
    private String phoneNumber;   // 收货人电话
    private String province;      // 省份
    private String city;          // 城市
    private String district;      // 区/县
    private String streetAddress; // 详细街道地址
    private String postalCode;    // 邮政编码
    private Boolean isDefault;    // 是否是用户的默认地址
    private LocalDateTime createdAt; // 地址创建时间
    private LocalDateTime updatedAt; // 地址最后更新时间
} 