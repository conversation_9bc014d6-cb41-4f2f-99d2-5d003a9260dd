package com.lzhshtp.shangcheng.config;

import com.lzhshtp.shangcheng.mapper.AiMessageMapper;
import com.lzhshtp.shangcheng.model.AiMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于数据库的ChatMemory实现
 * 从数据库加载历史消息，实现真正的AI记忆功能
 */
@Slf4j
@Component
public class DatabaseChatMemory implements ChatMemory {

    @Autowired
    private AiMessageMapper aiMessageMapper;

    /**
     * 添加消息到记忆中
     * 注意：消息已经通过AiChatService保存到数据库，这里不需要重复保存
     */
    @Override
    public void add(String conversationId, List<Message> messages) {
        // 数据库ChatMemory不需要在这里保存消息
        // 消息已经通过AiChatService的saveUserMessage和saveAiMessage方法保存到数据库
        log.debug("DatabaseChatMemory.add() 被调用，会话ID: {}, 消息数量: {}", 
            conversationId, messages.size());
    }

    /**
     * 从数据库获取指定会话的最近N条消息
     */
    @Override
    public List<Message> get(String conversationId, int lastN) {
        try {
            log.info("🔍 DatabaseChatMemory.get() 被调用 - 会话ID: {}, 请求消息数: {}", conversationId, lastN);

            // 使用优化的查询方法，直接获取最近N条消息（倒序）
            List<AiMessage> dbMessages = aiMessageMapper.findRecentMessagesBySessionId(conversationId, lastN);

            if (dbMessages == null || dbMessages.isEmpty()) {
                log.info("⚠️ 会话 {} 没有找到历史消息", conversationId);
                return new ArrayList<>();
            }

            log.info("📋 从数据库查询到 {} 条原始消息", dbMessages.size());

            // 调试：检查查询结果中是否有null值
            long nullCount = dbMessages.stream().filter(msg -> msg == null).count();
            if (nullCount > 0) {
                log.error("⚠️ 查询结果中包含 {} 个null值！", nullCount);
                // 打印所有消息的详细信息
                for (int i = 0; i < dbMessages.size(); i++) {
                    AiMessage msg = dbMessages.get(i);
                    if (msg == null) {
                        log.error("❌ 索引 {} 的消息为null", i);
                    } else {
                        log.info("✅ 索引 {} 的消息: ID={}, Type={}, Content={}",
                            i, msg.getId(), msg.getType(),
                            msg.getTextContent() != null ? msg.getTextContent().substring(0, Math.min(50, msg.getTextContent().length())) + "..." : "null");
                    }
                }
            }

            // 因为查询结果是倒序的，需要反转为正序（时间从早到晚）
            Collections.reverse(dbMessages);

            // 转换为Spring AI的Message格式，先过滤掉null的AiMessage
            List<Message> springAiMessages = dbMessages.stream()
                .filter(aiMessage -> aiMessage != null)  // 先过滤掉null的AiMessage
                .map(this::convertToSpringAiMessage)
                .filter(message -> message != null)      // 再过滤掉转换失败的Message
                .collect(Collectors.toList());

            log.info("✅ 成功加载会话 {} 的 {} 条历史消息用于AI记忆", conversationId, springAiMessages.size());

            // 打印消息内容用于调试
            for (int i = 0; i < springAiMessages.size(); i++) {
                Message msg = springAiMessages.get(i);
                String type = msg instanceof UserMessage ? "USER" : "ASSISTANT";
                String content = msg.getContent();
                log.info("📝 消息 {}: [{}] {}", i + 1, type,
                    content.length() > 50 ? content.substring(0, 50) + "..." : content);
            }

            return springAiMessages;

        } catch (Exception e) {
            log.error("❌ 从数据库加载会话消息失败，会话ID: {}", conversationId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 清除指定会话的所有消息记忆
     */
    @Override
    public void clear(String conversationId) {
        try {
            log.info("清除会话 {} 的所有消息记忆", conversationId);
            // 可以选择是否删除数据库中的消息，这里只记录日志
            // aiMessageMapper.deleteBySessionId(conversationId);
        } catch (Exception e) {
            log.error("清除会话消息失败，会话ID: {}", conversationId, e);
        }
    }

    /**
     * 将数据库中的AiMessage转换为Spring AI的Message
     */
    private Message convertToSpringAiMessage(AiMessage aiMessage) {
        try {
            // 添加空指针检查
            if (aiMessage == null) {
                log.warn("⚠️ 尝试转换null的AiMessage");
                return null;
            }

            String content = aiMessage.getTextContent();  // 修正字段名
            String messageType = aiMessage.getType();

            // 检查必要字段
            if (content == null || messageType == null) {
                log.warn("⚠️ AiMessage缺少必要字段 - ID: {}, content: {}, type: {}",
                    aiMessage.getId(), content != null ? "存在" : "null", messageType);
                return null;
            }

            // 根据消息类型创建对应的Message对象
            switch (messageType) {
                case "user":
                    return new UserMessage(content);
                case "assistant":
                    return new AssistantMessage(content);
                case "USER":  // 兼容大写
                    return new UserMessage(content);
                case "ASSISTANT":  // 兼容大写
                    return new AssistantMessage(content);
                default:
                    log.warn("未知的消息类型: {}, 消息ID: {}", messageType, aiMessage.getId());
                    return null;
            }
        } catch (Exception e) {
            String messageId = aiMessage != null ? String.valueOf(aiMessage.getId()) : "null";
            log.error("转换消息格式失败，消息ID: {}", messageId, e);
            return null;
        }
    }

    /**
     * 获取会话的消息总数
     */
    public int getMessageCount(String conversationId) {
        try {
            return aiMessageMapper.countBySessionId(conversationId);
        } catch (Exception e) {
            log.error("获取会话消息总数失败，会话ID: {}", conversationId, e);
            return 0;
        }
    }

    /**
     * 检查会话是否存在消息
     */
    public boolean hasMessages(String conversationId) {
        return getMessageCount(conversationId) > 0;
    }
}
