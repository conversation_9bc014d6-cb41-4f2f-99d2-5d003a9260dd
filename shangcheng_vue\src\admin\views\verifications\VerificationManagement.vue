<template>
  <div class="verification-management">
    <!-- 页面标题和统计 -->
    <div class="page-header">
      <h1>验货管理</h1>
    </div>

    <!-- 筛选和操作栏 -->
    <div class="filter-bar">
      <div class="filter-left">
        <el-select v-model="filters.status" placeholder="验货状态" clearable @change="loadVerifications">
          <el-option label="全部" value=""></el-option>
          <el-option label="等待收货" value="waiting_goods"></el-option>
          <el-option label="验货中" value="verifying"></el-option>
          <el-option label="验货通过" value="passed"></el-option>
          <el-option label="验货失败" value="failed"></el-option>
          <el-option label="已转发" value="forwarded"></el-option>
        </el-select>

        <el-select v-model="filters.verificationPayer" placeholder="付费方" clearable @change="loadVerifications">
          <el-option label="全部" value=""></el-option>
          <el-option label="买家付费" value="buyer"></el-option>
          <el-option label="卖家付费" value="seller"></el-option>
        </el-select>

        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadVerifications">
        </el-date-picker>

        <el-input
          v-model="filters.keyword"
          placeholder="搜索订单号/商品名"
          clearable
          @keyup.enter="loadVerifications"
          style="width: 200px;">
          <template #append>
            <el-button @click="loadVerifications" icon="Search"></el-button>
          </template>
        </el-input>
      </div>

      <div class="filter-right">
        <el-button @click="loadVerifications" icon="Refresh">刷新</el-button>
      </div>
    </div>

    <!-- 验货记录列表 -->
    <div class="verification-list">
      <el-table
        :data="verifications"
        v-loading="loading"
        stripe
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>

        <el-table-column prop="verificationId" label="验货单号" width="120">
          <template #default="{ row }">
            <el-link @click="viewDetail(row)" type="primary">{{ row.verificationId }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="orderId" label="订单号" width="120"></el-table-column>

        <el-table-column label="商品信息" width="250">
          <template #default="{ row }">
            <div class="product-info">
              <img :src="row.productImage" class="product-image" />
              <div class="product-details">
                <div class="product-title">{{ row.productTitle }}</div>
                <div class="product-price">¥{{ row.productPrice }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="买家信息" width="120">
          <template #default="{ row }">
            <div>{{ row.buyerName }}</div>
          </template>
        </el-table-column>

        <el-table-column label="卖家信息" width="120">
          <template #default="{ row }">
            <div>{{ row.sellerName }}</div>
          </template>
        </el-table-column>

        <el-table-column label="付费方" width="80">
          <template #default="{ row }">
            <el-tag :type="row.verificationPayer === 'buyer' ? 'warning' : 'success'">
              {{ row.verificationPayer === 'buyer' ? '买家' : '卖家' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="verificationFee" label="验货费" width="80">
          <template #default="{ row }">
            <span class="amount">¥{{ row.verificationFee }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.verificationStatus)">
              {{ getStatusText(row.verificationStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdTime" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.verificationStatus === 'waiting_goods'"
              @click="startVerification(row)"
              type="primary"
              size="small">
              开始验货
            </el-button>
            <el-button
              v-if="row.verificationStatus === 'verifying'"
              @click="showResultDialog(row)"
              type="success"
              size="small">
              提交结果
            </el-button>
            <el-button
              v-if="row.verificationStatus === 'passed'"
              @click="forwardToBuyer(row)"
              type="warning"
              size="small">
              转发买家
            </el-button>
            <el-button @click="viewDetail(row)" type="info" size="small">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadVerifications"
          @current-change="loadVerifications">
        </el-pagination>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedVerifications.length > 0" class="batch-actions">
      <span>已选择 {{ selectedVerifications.length }} 项</span>
      <el-button @click="batchStartVerification" type="primary">批量开始验货</el-button>
    </div>

    <!-- 验货结果对话框 -->
    <el-dialog v-model="showResultModal" title="提交验货结果" width="600px">
      <el-form :model="resultForm" label-width="100px">
        <el-form-item label="验货结果" required>
          <el-radio-group v-model="resultForm.passed">
            <el-radio :label="true">验货通过</el-radio>
            <el-radio :label="false">验货失败</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="验货说明" required>
          <el-input
            v-model="resultForm.result"
            type="textarea"
            :rows="4"
            placeholder="请详细描述验货情况..."
            maxlength="1000"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showResultModal = false">取消</el-button>
        <el-button @click="submitResult" type="primary" :loading="submitting">提交结果</el-button>
      </template>
    </el-dialog>

    <!-- 验货详情对话框 -->
    <el-dialog v-model="showDetailModal" title="验货详情" width="800px">
      <div v-if="selectedVerification" class="verification-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>验货单号：</label>
              <span>{{ selectedVerification.verificationId }}</span>
            </div>
            <div class="detail-item">
              <label>订单号：</label>
              <span>{{ selectedVerification.orderId }}</span>
            </div>
            <div class="detail-item">
              <label>验货费用：</label>
              <span class="amount">¥{{ selectedVerification.verificationFee }}</span>
            </div>
            <div class="detail-item">
              <label>付费方：</label>
              <span>{{ selectedVerification.verificationPayer === 'buyer' ? '买家' : '卖家' }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDate(selectedVerification.createdTime) }}</span>
            </div>
            <div class="detail-item">
              <label>验货状态：</label>
              <el-tag :type="getStatusType(selectedVerification.verificationStatus)">
                {{ getStatusText(selectedVerification.verificationStatus) }}
              </el-tag>
            </div>
          </div>
        </div>

        <div v-if="selectedVerification.verificationResult" class="detail-section">
          <h3>验货结果</h3>
          <p>{{ selectedVerification.verificationResult }}</p>
        </div>

        <div v-if="getVerificationImages(selectedVerification).length > 0" class="detail-section">
          <h3>验货图片</h3>
          <div class="image-gallery">
            <img
              v-for="(image, index) in getVerificationImages(selectedVerification)"
              :key="index"
              :src="image"
              class="verification-image"
              @click="previewImage(image)" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  getPendingVerifications,
  getAllVerifications,
  updateVerificationStatus,
  submitVerificationResult,
  forwardToBuyer as forwardToBuyerAPI,
  getVerificationStatistics,
  exportVerificationData,
  batchHandleVerifications,
  uploadVerificationImages
} from '../../api/verifications'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const verifications = ref([])
const selectedVerifications = ref([])
const statistics = ref({})

// 筛选条件
const filters = reactive({
  status: 'verifying', // 默认显示验货中的记录
  verificationPayer: '',
  dateRange: null,
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框控制
const showResultModal = ref(false)
const showDetailModal = ref(false)
const selectedVerification = ref(null)

// 验货结果表单
const resultForm = reactive({
  passed: true,
  result: '',
  imageFiles: [],
  imageUrls: []
})

// 上传配置
const uploadUrl = computed(() => '/api/verifications/upload/images')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
}))

// 页面初始化
onMounted(() => {
  loadVerifications()
  loadStatistics()
})

// 加载验货列表
const loadVerifications = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      status: filters.status,
      verificationPayer: filters.verificationPayer,
      keyword: filters.keyword
    }

    if (filters.dateRange) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await getAllVerifications(params)
    if (response.success) {
      // 适配后端返回的分页格式
      if (response.data.records) {
        verifications.value = response.data.records
        pagination.total = response.data.total
      } else {
        // 兼容旧格式（直接返回数组）
        verifications.value = response.data || []
        pagination.total = response.data ? response.data.length : 0
      }
    }
  } catch (error) {
    ElMessage.error('加载验货列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getVerificationStatistics()
    if (response.success) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 开始验货
const startVerification = async (verification) => {
  try {
    await updateVerificationStatus(verification.verificationId, 'verifying')
    ElMessage.success('已开始验货')
    loadVerifications()
    loadStatistics()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 显示验货结果对话框
const showResultDialog = (verification) => {
  selectedVerification.value = verification
  resultForm.passed = true
  resultForm.result = ''
  resultForm.imageFiles = []
  resultForm.imageUrls = []
  showResultModal.value = true
}

// 提交验货结果
const submitResult = async () => {
  if (!resultForm.result.trim()) {
    ElMessage.warning('请输入验货说明')
    return
  }

  submitting.value = true
  try {
    await submitVerificationResult({
      verificationId: selectedVerification.value.verificationId,
      passed: resultForm.passed,
      result: resultForm.result,
      imageUrls: resultForm.imageUrls
    })

    ElMessage.success('验货结果提交成功')
    showResultModal.value = false
    loadVerifications()
    loadStatistics()
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 转发给买家
const forwardToBuyer = async (verification) => {
  try {
    await ElMessageBox.confirm('确认将商品转发给买家？', '转发确认')

    await forwardToBuyerAPI(verification.verificationId)
    ElMessage.success('已转发给买家')
    loadVerifications()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('转发失败')
    }
  }
}

// 安全获取验货图片数组
const getVerificationImages = (verification) => {
  if (!verification || !verification.verificationImages) {
    return []
  }

  const images = verification.verificationImages

  // 如果已经是数组，直接返回
  if (Array.isArray(images)) {
    return images.filter(img => img && img.trim())
  }

  // 如果是字符串，尝试解析
  if (typeof images === 'string') {
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(images)
      if (Array.isArray(parsed)) {
        return parsed.filter(img => img && img.trim())
      }
    } catch (e) {
      // 如果不是JSON，按逗号分割
      return images.split(',').map(img => img.trim()).filter(img => img)
    }
  }

  return []
}

// 查看详情
const viewDetail = (verification) => {
  selectedVerification.value = verification
  showDetailModal.value = true
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedVerifications.value = selection
}

// 批量开始验货
const batchStartVerification = async () => {
  try {
    await ElMessageBox.confirm('确认批量开始验货？', '批量操作')

    const verificationIds = selectedVerifications.value.map(item => item.verificationId)
    await batchHandleVerifications({
      verificationIds,
      action: 'start_verification'
    })

    ElMessage.success('批量操作成功')
    loadVerifications()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

// 图片上传成功
const handleImageSuccess = (response, file) => {
  if (response.success) {
    resultForm.imageUrls.push(response.data.url)
  }
}

// 图片上传前检查
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 预览图片
const previewImage = (imageUrl) => {
  // 实现图片预览功能
  window.open(imageUrl, '_blank')
}

// 导出数据
const exportData = async () => {
  try {
    const response = await exportVerificationData(filters)
    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `验货数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    'waiting_goods': 'info',
    'verifying': 'warning',
    'passed': 'success',
    'failed': 'danger',
    'forwarded': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'waiting_goods': '等待收货',
    'verifying': '验货中',
    'passed': '验货通过',
    'failed': '验货失败',
    'forwarded': '已转发'
  }
  return textMap[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}
</script>

<style scoped>
.verification-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 20px 0;
  color: #333;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-left {
  display: flex;
  gap: 15px;
  align-items: center;
}

.verification-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.product-details {
  flex: 1;
}

.product-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.product-price {
  font-size: 12px;
  color: #666;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination {
  padding: 20px;
  text-align: center;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  gap: 15px;
}

.verification-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h3 {
  margin: 0 0 10px 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  display: flex;
}

.detail-item label {
  font-weight: bold;
  color: #666;
  min-width: 80px;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
}

.verification-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.verification-image:hover {
  transform: scale(1.05);
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
