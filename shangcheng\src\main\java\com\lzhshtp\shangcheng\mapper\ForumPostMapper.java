package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.model.ForumPost;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 论坛帖子Mapper接口
 */
public interface ForumPostMapper extends BaseMapper<ForumPost> {

    /**
     * 增加帖子浏览量
     * 
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE tb_lzhshtp_forum_posts SET lzhshtp_views_count = lzhshtp_views_count + 1 WHERE lzhshtp_post_id = #{postId}")
    int increaseViewCount(@Param("postId") Long postId);
    
    /**
     * 查询帖子列表（带分页和条件筛选）
     * 
     * @param categoryId 分类ID
     * @param authorId 作者ID
     * @param keyword 关键词
     * @param onlyPinned 是否只看置顶
     * @param enablePinned 是否启用置顶功能（置顶帖子优先显示）
     * @param orderBy 排序字段
     * @return 帖子列表
     */
    List<ForumPost> selectPostList(
            @Param("categoryId") Integer categoryId,
            @Param("authorId") Long authorId,
            @Param("keyword") String keyword,
            @Param("onlyPinned") Boolean onlyPinned,
            @Param("enablePinned") Boolean enablePinned,
            @Param("orderBy") String orderBy
    );
    
    /**
     * 查询帖子详情
     * 
     * @param postId 帖子ID
     * @return 帖子详情
     */
    ForumPost selectPostDetail(@Param("postId") Long postId);
} 