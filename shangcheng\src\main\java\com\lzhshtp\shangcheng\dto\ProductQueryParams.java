package com.lzhshtp.shangcheng.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductQueryParams {
    private String keyword;        // 搜索关键词
    private Integer categoryId;    // 分类ID
    private BigDecimal minPrice;   // 最低价格
    private BigDecimal maxPrice;   // 最高价格
    private String condition;      // 商品新旧程度
    private String location;       // 位置
    private String deliveryMethod; // 交易方式
    private Boolean sameCity;      // 是否同城
    private String sortBy;         // 排序字段
    private String sortDirection;  // 排序方向（asc/desc）
    private Integer page;          // 当前页码
    private Integer pageSize;      // 每页数量
    private String status;         // 商品状态
    private Long sellerId;         // 卖家ID
    private Long excludeSellerId;  // 排除的卖家ID（不显示该卖家的商品）
} 