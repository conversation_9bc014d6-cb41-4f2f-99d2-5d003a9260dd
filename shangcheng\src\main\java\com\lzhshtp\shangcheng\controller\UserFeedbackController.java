package com.lzhshtp.shangcheng.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.FeedbackStatusUpdateRequest;
import com.lzhshtp.shangcheng.dto.UserFeedbackDTO;
import com.lzhshtp.shangcheng.dto.UserFeedbackQueryParams;
import com.lzhshtp.shangcheng.dto.UserFeedbackRequest;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserFeedbackService;
import com.lzhshtp.shangcheng.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户反馈/举报控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/feedback")
@RequiredArgsConstructor
public class UserFeedbackController {

    private final UserFeedbackService userFeedbackService;
    private final UserService userService;

    /**
     * 提交反馈/举报
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> submitFeedback(
            @RequestBody @Valid UserFeedbackRequest request,
            @AuthenticationPrincipal UserDetails userDetails
    ) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }

        Long feedbackId = userFeedbackService.submitFeedback(request, user.getUserId());
        return ApiResponse.success(feedbackId);
    }

    /**
     * 分页查询反馈列表
     * 普通用户只能查询自己的反馈，管理员可以查询所有反馈
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<IPage<UserFeedbackDTO>> getFeedbackList(
            UserFeedbackQueryParams queryParams,
            @AuthenticationPrincipal UserDetails userDetails
    ) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }

        // 普通用户只能查询自己的反馈
        boolean isAdmin = "admin".equals(user.getRole());
        if (!isAdmin && queryParams.getReporterId() == null) {
            queryParams.setReporterId(user.getUserId());
        }

        IPage<UserFeedbackDTO> page = userFeedbackService.getFeedbackList(queryParams);
        return ApiResponse.success(page);
    }

    /**
     * 获取反馈详情
     */
    @GetMapping("/{feedbackId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<UserFeedbackDTO> getFeedbackDetail(
            @PathVariable Long feedbackId,
            @AuthenticationPrincipal UserDetails userDetails
    ) {
        try {
            User user = userService.findByUsername(userDetails.getUsername());
            if (user == null) {
                log.warn("获取反馈详情失败：用户不存在, username={}", userDetails.getUsername());
                return ApiResponse.fail("用户不存在");
            }

            UserFeedbackDTO feedback = userFeedbackService.getFeedbackDetail(feedbackId);
            if (feedback == null) {
                log.warn("获取反馈详情失败：反馈不存在, feedbackId={}", feedbackId);
                return ApiResponse.fail("反馈不存在");
            }

            // 普通用户只能查看自己的反馈
            boolean isAdmin = "admin".equals(user.getRole());
            if (!isAdmin && feedback.getReporterId() != null && !feedback.getReporterId().equals(user.getUserId())) {
                log.warn("获取反馈详情失败：无权查看, userId={}, feedbackId={}, reporterId={}",
                         user.getUserId(), feedbackId, feedback.getReporterId());
                return ApiResponse.fail("无权查看此反馈");
            }

            return ApiResponse.success(feedback);
        } catch (Exception e) {
            log.error("获取反馈详情出错, feedbackId={}", feedbackId, e);
            return ApiResponse.fail("获取反馈详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新反馈状态（管理员功能）
     */
    @PutMapping("/{feedbackId}/status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Boolean> updateFeedbackStatus(
            @PathVariable Long feedbackId,
            @RequestBody @Valid FeedbackStatusUpdateRequest request,
            @AuthenticationPrincipal UserDetails userDetails
    ) {
        User admin = userService.findByUsername(userDetails.getUsername());
        if (admin == null) {
            return ApiResponse.fail("用户不存在");
        }

        boolean result = userFeedbackService.updateFeedbackStatus(feedbackId, request, admin.getUserId());
        return ApiResponse.success(result);
    }

    /**
     * 删除反馈
     * 用户只能删除自己的反馈，管理员可以删除任何反馈
     */
    @DeleteMapping("/{feedbackId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> deleteFeedback(
            @PathVariable Long feedbackId,
            @AuthenticationPrincipal UserDetails userDetails
    ) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }

        boolean result = userFeedbackService.deleteFeedback(feedbackId, user.getUserId());
        return ApiResponse.success(result);
    }
}
