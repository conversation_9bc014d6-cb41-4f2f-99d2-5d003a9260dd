package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.config.DatabaseChatMemory;
import com.lzhshtp.shangcheng.mapper.AiMessageMapper;
import com.lzhshtp.shangcheng.model.AiMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI测试控制器
 * 用于测试DashScope连接和基本功能
 */
@RestController
@RequestMapping("/api/ai/test")
@RequiredArgsConstructor
@Slf4j
public class AiTestController {

    private final ChatModel chatModel;
    private final AiMessageMapper aiMessageMapper;
    private final DatabaseChatMemory databaseChatMemory;

    /**
     * 测试基本的AI聊天功能
     */
    @GetMapping("/chat")
    public String testChat(@RequestParam(defaultValue = "你好") String message) {
        try {
            log.info("测试AI聊天，输入消息: {}", message);
            
            String response = ChatClient.create(chatModel)
                    .prompt()
                    .user(message)
                    .call()
                    .content();
            
            log.info("AI响应成功: {}", response);
            return response;
        } catch (Exception e) {
            log.error("AI聊天测试失败", e);
            return "测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试带系统提示的AI聊天
     */
    @PostMapping("/chat-with-system")
    public String testChatWithSystem(@RequestParam String message, 
                                   @RequestParam(defaultValue = "你是一个友好的助手") String systemPrompt) {
        try {
            log.info("测试带系统提示的AI聊天，系统提示: {}, 用户消息: {}", systemPrompt, message);
            
            String response = ChatClient.create(chatModel)
                    .prompt()
                    .system(systemPrompt)
                    .user(message)
                    .call()
                    .content();
            
            log.info("AI响应成功: {}", response);
            return response;
        } catch (Exception e) {
            log.error("带系统提示的AI聊天测试失败", e);
            return "测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试ChatModel是否正确注入
     */
    @GetMapping("/model-info")
    public String getModelInfo() {
        try {
            return "ChatModel类型: " + chatModel.getClass().getName();
        } catch (Exception e) {
            log.error("获取模型信息失败", e);
            return "获取模型信息失败: " + e.getMessage();
        }
    }

    /**
     * 测试数据库消息查询
     */
    @GetMapping("/db-messages/{sessionId}")
    public String testDatabaseMessages(@PathVariable String sessionId, 
                                     @RequestParam(defaultValue = "10") int limit) {
        try {
            log.info("测试数据库消息查询，会话ID: {}, 限制: {}", sessionId, limit);
            
            List<AiMessage> messages = aiMessageMapper.findRecentMessagesBySessionId(sessionId, limit);
            
            StringBuilder result = new StringBuilder();
            result.append("查询到 ").append(messages.size()).append(" 条消息:\n");
            
            for (int i = 0; i < messages.size(); i++) {
                AiMessage msg = messages.get(i);
                if (msg == null) {
                    result.append("消息 ").append(i).append(": NULL\n");
                } else {
                    result.append("消息 ").append(i).append(": ID=").append(msg.getId())
                          .append(", Type=").append(msg.getType())
                          .append(", Content=").append(msg.getTextContent() != null ? 
                              msg.getTextContent().substring(0, Math.min(50, msg.getTextContent().length())) + "..." : "null")
                          .append("\n");
                }
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("测试数据库消息查询失败", e);
            return "测试失败: " + e.getMessage();
        }
    }

    /**
     * 测试DatabaseChatMemory
     */
    @GetMapping("/memory/{sessionId}")
    public String testChatMemory(@PathVariable String sessionId, 
                               @RequestParam(defaultValue = "10") int limit) {
        try {
            log.info("测试DatabaseChatMemory，会话ID: {}, 限制: {}", sessionId, limit);
            
            var messages = databaseChatMemory.get(sessionId, limit);
            
            StringBuilder result = new StringBuilder();
            result.append("ChatMemory返回 ").append(messages.size()).append(" 条消息:\n");
            
            for (int i = 0; i < messages.size(); i++) {
                var msg = messages.get(i);
                String type = msg.getClass().getSimpleName();
                String content = msg.getContent();
                result.append("消息 ").append(i).append(": [").append(type).append("] ")
                      .append(content.length() > 50 ? content.substring(0, 50) + "..." : content)
                      .append("\n");
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("测试DatabaseChatMemory失败", e);
            return "测试失败: " + e.getMessage();
        }
    }
}
