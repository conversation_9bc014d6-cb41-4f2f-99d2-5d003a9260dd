import type {Executor} from '../';
import type {Document} from '../model/static/';

export class DocumentDemoController {
    
    constructor(private executor: Executor) {}
    
    embedding: (options: DocumentDemoControllerOptions['embedding']) => Promise<
        Array<number>
    > = async(options) => {
        let _uri = '/demo/document/embedding';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.text;
        _uri += _separator
        _uri += 'text='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'POST'})) as Promise<Array<number>>;
    }
    
    query: (options: DocumentDemoControllerOptions['query']) => Promise<
        Array<Document>
    > = async(options) => {
        let _uri = '/demo/document/query';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.text;
        _uri += _separator
        _uri += 'text='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'POST'})) as Promise<Array<Document>>;
    }
    
    readFromLocalFile: (options: DocumentDemoControllerOptions['readFromLocalFile']) => Promise<
        string
    > = async(options) => {
        let _uri = '/demo/document/etl/reader/local-file';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.file;
        _uri += _separator
        _uri += 'file='
        _uri += encodeURIComponent(_value);
        _separator = '&';
        return (await this.executor({uri: _uri, method: 'POST'})) as Promise<string>;
    }
    
    readFromMultipart: (options: DocumentDemoControllerOptions['readFromMultipart']) => Promise<
        string
    > = async(options) => {
        let _uri = '/demo/document/etl/reader/multipart';
        const _formData = new FormData();
        const _body = options.body;
        _formData.append("file", _body.file);
        return (await this.executor({uri: _uri, method: 'POST', body: _formData})) as Promise<string>;
    }
    
    split: (options: DocumentDemoControllerOptions['split']) => Promise<
        Array<string>
    > = async(options) => {
        let _uri = '/demo/document/etl/treasfrom/split';
        const _formData = new FormData();
        const _body = options.body;
        _formData.append("file", _body.file);
        return (await this.executor({uri: _uri, method: 'POST', body: _formData})) as Promise<Array<string>>;
    }
    
    writeVector: (options: DocumentDemoControllerOptions['writeVector']) => Promise<
        void
    > = async(options) => {
        let _uri = '/demo/document/etl/write/vector';
        const _formData = new FormData();
        const _body = options.body;
        _formData.append("file", _body.file);
        return (await this.executor({uri: _uri, method: 'POST', body: _formData})) as Promise<void>;
    }
}

export type DocumentDemoControllerOptions = {
    'embedding': {
        text: string
    }, 
    'readFromMultipart': {
        body: {
            file: File
        }
    }, 
    'readFromLocalFile': {
        file: string
    }, 
    'split': {
        body: {
            file: File
        }
    }, 
    'writeVector': {
        body: {
            file: File
        }
    }, 
    'query': {
        text: string
    }
}
