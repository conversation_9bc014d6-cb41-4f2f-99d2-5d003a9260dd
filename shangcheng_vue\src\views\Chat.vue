<template>
  <div class="chat-page">
  <div class="common-layout">   
      <!-- 顶部导航 -->
      <nav class="top-nav">
        <div class="nav-content">
          <router-link to="/home" class="logo" style="color: #FF0000;">易转</router-link>
          <div class="nav-right">
            <router-link to="/profile" class="user-avatar" title="个人中心">
              <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
            </router-link>
          </div>
        </div>
      </nav>

      <!-- 主要内容区域 -->
      <el-main class="main-content">
          <el-card class="message-card" v-loading="loading">
          <el-row :gutter="20" class="message-container">
            <!-- 左侧边栏 - 消息列表 -->
            <el-col :span="8" class="message-list-col">
              <div class="message-list-header">
                <span class="message-title">消息</span>
                  <el-badge :value="unreadCount" class="message-count" type="danger" :hidden="unreadCount === 0">
                  <span class="badge-content"></span>
                </el-badge>
              </div>
                
                <!-- 没有会话时显示空状态 -->
                <div v-if="conversations.length === 0" class="empty-conversations">
                  <p>暂无消息</p>
                  <p class="hint">您还没有任何消息记录</p>
                </div>
                
                <!-- 会话列表 -->
                <div 
                  v-else
                  v-for="conversation in conversations" 
                  :key="conversation.conversationId" 
                  class="message-item"
                  :class="{'active': selectedConversation?.conversationId === conversation.conversationId}"
                  @click="selectConversation(conversation)"
                >
                  <el-badge :value="conversation.unreadCount" class="notification-badge" type="danger" :hidden="conversation.unreadCount === 0">
                    <el-avatar :size="50" :src="conversation.otherUserAvatar || defaultAvatar" />
                </el-badge>
                <div class="message-content">
                  <div class="message-title-line">
                      <span class="message-type">{{ conversation.otherUsername }}</span>
                  </div>
                  <div class="message-summary">
                      <span class="message-text">{{ conversation.lastMessagePreview }}</span>
                    </div>
                    <div class="message-time">{{ formatTime(conversation.updatedAt) }}</div>
                  </div>
                </div>
            </el-col>

            <!-- 右侧内容 - 聊天区域 -->
            <el-col :span="16" class="chat-area-col">
                <!-- 未选择联系人时显示的空状态 -->
                <div v-if="!selectedConversation" class="chat-empty-state">
                <img src="@/assets/fish_placeholder.svg" alt="未选择联系人" class="empty-image" />
                <p class="empty-text">尚未选择任何联系人</p>
                <p class="empty-subtext">快点左侧列表聊起来吧~</p>
              </div>
                
                <!-- 选择联系人后显示的聊天界面 -->
                <div v-else class="chat-interface">
                  <!-- 聊天头部 -->
                  <div class="chat-header">
                    <span class="chat-title">{{ selectedConversation.otherUsername }}</span>
                    <span :class="['connection-status', isWebSocketConnected ? 'connected' : 'disconnected']"
                          :title="isWebSocketConnected ? '实时连接正常' : '连接断开，使用轮询模式'">
                      {{ isWebSocketConnected ? '●' : '○' }}
                    </span>
                  </div>
                  
                  <!-- 聊天消息区域 -->
                  <div class="chat-messages" ref="messageContainer">
                    <!-- 加载更多消息 -->
                    <div v-if="hasMoreMessages" class="load-more" @click="loadMoreMessages">
                      加载更多消息
                    </div>
                    
                    <!-- 没有消息时显示提示 -->
                    <div v-if="messages.length === 0" class="no-messages">
                      <p>暂无消息记录</p>
                      <p>发送一条消息开始聊天吧</p>
                    </div>
                    
                    <!-- 消息列表 -->
                    <div v-else v-for="message in messages" :key="message.messageId" class="message-bubble-wrapper">
                      <!-- 系统消息 -->
                      <div v-if="message.isSystemMessage" class="system-message">
                        {{ message.content }}
                      </div>
                      
                      <!-- 用户消息 -->
                      <div v-else :class="['message-bubble', message.isMine ? 'mine' : 'other']">
                        <!-- 我的消息：头像在右，气泡在左 -->
                        <template v-if="message.isMine">
                          <div class="message-content-bubble mine-bubble">
                            <div class="message-text">{{ message.content }}</div>
                            <div class="message-time-bubble">
                              {{ formatTime(message.sentAt) }}
                              <span class="read-status">
                                {{ message.isRead ? '已读' : '未读' }}
                              </span>
                            </div>
                          </div>
                          <el-avatar 
                            :size="40" 
                            :src="userInfo?.avatarUrl || defaultAvatar" 
                            class="message-avatar"
                          />
                        </template>
                        
                        <!-- 对方消息：头像在左，气泡在右 -->
                        <template v-else>
                          <el-avatar 
                            :size="40" 
                            :src="message.senderAvatar || defaultAvatar" 
                            class="message-avatar"
                          />
                          <div class="message-content-bubble other-bubble">
                            <div class="message-text">{{ message.content }}</div>
                            <div class="message-time-bubble">
                              {{ formatTime(message.sentAt) }}
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 消息输入区域 -->
                  <div class="message-input-area">
                    <el-input
                      v-model="newMessage"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入消息..."
                      resize="none"
                      @keyup.enter.ctrl="sendMessage"
                    />
                    <div class="message-actions">
                      <span class="hint">按Ctrl+Enter发送</span>
                      <el-button type="primary" @click="sendMessage" :disabled="!newMessage.trim()">
                        发送
                      </el-button>
                    </div>
                  </div>
                </div>
            </el-col>
          </el-row>
        </el-card>
      </el-main>
     
    </div>
   
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { useRouter, useRoute, RouterLink } from 'vue-router';
import { ElContainer, ElMain, ElCard, ElRow, ElCol, ElBadge, ElIcon, ElAvatar, ElInput, ElButton, ElMessage } from 'element-plus';
import { FolderOpened, Setting } from '@element-plus/icons-vue';
import { 
  getUserConversations, 
  getConversationMessages, 
  sendMessage as apiSendMessage, 
  markMessagesAsRead, 
  getUnreadCount
} from '@/api/message';


// 路由实例
const router = useRouter();
const route = useRoute();

// 用户状态管理
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 会话和消息数据
const conversations = ref([]);
const selectedConversation = ref(null);
const messages = ref([]);
const newMessage = ref('');
const unreadCount = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreMessages = ref(false);
const messageContainer = ref(null);
const loading = ref(false);

// WebSocket相关
let websocket = null;
const isWebSocketConnected = ref(false);

// 在组件挂载时获取数据
onMounted(async () => {
  if (!userStore.userInfo) {
    await userStore.fetchUserInfo();
  }
  
  // 获取会话列表
  await fetchConversations();
  
  // 获取未读消息总数
  await fetchUnreadCount();
  
  // 如果路由中有指定的会话ID，则自动选择该会话
  const conversationId = router.currentRoute.value.query.conversationId;
  if (conversationId && conversations.value.length > 0) {
    const targetConversation = conversations.value.find(c => c.conversationId === Number(conversationId));
    if (targetConversation) {
      selectConversation(targetConversation);
    }
  }
  
  // 建立WebSocket连接
  connectWebSocket();

  // 设置定时器，每2分钟轻量同步一次（作为WebSocket的备用机制）
  const timer = setInterval(() => {
    // 始终同步会话列表和未读消息数
    fetchConversations();
    fetchUnreadCount();

    // 如果WebSocket连接失败，才同步当前会话的消息
    if (!isWebSocketConnected.value && selectedConversation.value) {
      console.log('WebSocket断开，使用轮询同步消息');
      fetchMessages();
    }
  }, 120000); // 2分钟轮询一次，减少对实时性的干扰

  // 组件卸载时清除定时器和WebSocket连接
  onUnmounted(() => {
    clearInterval(timer);
    disconnectWebSocket();
  });
});

// 监听选中的会话变化
watch(selectedConversation, async (newVal) => {
  if (newVal) {
    currentPage.value = 1;
    messages.value = [];
    await fetchMessages();
    
    // 标记消息为已读
    await markAsRead();
    
    // 更新未读消息数
    await fetchUnreadCount();
    
    // 更新当前选中的会话的未读数
    const index = conversations.value.findIndex(c => c.conversationId === newVal.conversationId);
    if (index !== -1) {
      conversations.value[index].unreadCount = 0;
    }
  }
});

// 监听消息变化，自动滚动到底部
watch(messages, async () => {
  await nextTick();
  scrollToBottom();
}, { deep: true });

// 获取会话列表
const fetchConversations = async () => {
  try {
    loading.value = true;
    const response = await getUserConversations();
    conversations.value = response.data;
    loading.value = false;
  } catch (error) {
    loading.value = false;
    ElMessage.error('获取会话列表失败');
    console.error('获取会话列表失败:', error);
  }
};

// 获取未读消息总数
const fetchUnreadCount = async () => {
  try {
    const response = await getUnreadCount();
    unreadCount.value = response.data;
  } catch (error) {
    console.error('获取未读消息数失败:', error);
  }
};

// 获取会话消息
const fetchMessages = async () => {
  if (!selectedConversation.value) return;
  
  try {
    loading.value = true;
    const response = await getConversationMessages(
      selectedConversation.value.conversationId, 
      currentPage.value, 
      pageSize.value
    );
    
    const data = response.data;
    // 修复数据结构不匹配问题，并确保消息按时间排序（旧消息在上，新消息在下）
    const newMessages = data.records || [];

    // 确保每条消息都有isMine字段
    newMessages.forEach(message => {
      if (message.isMine === undefined || message.isMine === null) {
        message.isMine = message.senderId === userInfo.value?.userId;
      }
    });

    // 如果是第一次加载（当前消息列表为空），直接设置
    if (messages.value.length === 0) {
      console.log('首次加载消息，数量:', newMessages.length);
      messages.value = newMessages.sort((a, b) => {
        return new Date(a.sentAt) - new Date(b.sentAt);
      });
    } else {
      // 如果已有消息，只在必要时进行合并
      const existingMessageIds = new Set(messages.value.map(m => m.messageId));
      const uniqueNewMessages = newMessages.filter(m => !existingMessageIds.has(m.messageId));

      console.log('API获取消息 - 已有:', messages.value.length, '新获取:', newMessages.length, '真正新的:', uniqueNewMessages.length);

      // 只有真正有新消息时才进行合并
      if (uniqueNewMessages.length > 0) {
        console.log('发现新消息，进行合并');
        // 将新消息添加到末尾，保持现有消息的顺序
        messages.value.push(...uniqueNewMessages);

        // 只对新添加的部分进行排序，减少对现有消息顺序的影响
        messages.value.sort((a, b) => {
          return new Date(a.sentAt) - new Date(b.sentAt);
        });
      } else {
        console.log('没有新消息，保持现有列表不变');
      }
    }
    hasMoreMessages.value = currentPage.value < (data.totalPages || 0);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    ElMessage.error('获取消息失败');
    console.error('获取消息失败:', error);
  }
};

// 加载更多消息
const loadMoreMessages = async () => {
  currentPage.value++;
  try {
    loading.value = true;
    const response = await getConversationMessages(
      selectedConversation.value.conversationId, 
      currentPage.value, 
      pageSize.value
    );
    
    const data = response.data;
    // 获取历史消息
    const historyMessages = data.records || [];
    
    // 将历史消息按时间排序后添加到消息列表前面
    if (historyMessages.length > 0) {
      const sortedHistoryMessages = historyMessages.sort((a, b) => {
        return new Date(a.sentAt) - new Date(b.sentAt);
      });
      
      messages.value = [...sortedHistoryMessages, ...messages.value];
    }
    
    hasMoreMessages.value = currentPage.value < (data.totalPages || 0);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    ElMessage.error('获取历史消息失败');
    console.error('获取历史消息失败:', error);
  }
};

// 标记消息为已读
const markAsRead = async () => {
  if (!selectedConversation.value) return;
  
  try {
    await markMessagesAsRead(selectedConversation.value.conversationId);
  } catch (error) {
    console.error('标记消息为已读失败:', error);
  }
};

// 发送消息
const sendMessage = async () => {
  if (!newMessage.value.trim() || !selectedConversation.value) return;
  
  try {
    loading.value = true;
    const messageRequest = {
      receiverId: selectedConversation.value.otherUserId,
      content: newMessage.value.trim()
    };
    
    const response = await apiSendMessage(messageRequest);

    // 确保返回的消息有正确的isMine字段
    const sentMessage = response.data;
    if (sentMessage.isMine === undefined || sentMessage.isMine === null) {
      sentMessage.isMine = sentMessage.senderId === userInfo.value?.userId;
    }

    // 检查消息是否已存在（避免重复添加）
    const existingMessage = messages.value.find(m => m.messageId === sentMessage.messageId);
    if (!existingMessage) {
      // 添加新消息到列表底部
      messages.value.push(sentMessage);
    }
    
    // 更新会话列表中的最后一条消息
    const index = conversations.value.findIndex(c => c.conversationId === selectedConversation.value.conversationId);
    if (index !== -1) {
      conversations.value[index].lastMessagePreview = newMessage.value.trim();
      conversations.value[index].updatedAt = new Date().toISOString();
      
      // 将该会话移到顶部
      const conversation = conversations.value.splice(index, 1)[0];
      conversations.value.unshift(conversation);
    }
    
    // 清空输入框
    newMessage.value = '';
    
    // 滚动到底部
    scrollToBottom();
    loading.value = false;
  } catch (error) {
    loading.value = false;
    ElMessage.error('发送消息失败');
    console.error('发送消息失败:', error);
  }
};

// 选择会话
const selectConversation = (conversation) => {
  selectedConversation.value = conversation;
};

// 格式化时间
const formatTime = (dateTimeString) => {
  if (!dateTimeString) return '';
  
  const date = new Date(dateTimeString);
  const now = new Date();
  
  // 如果是今天，只显示时间
  if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }
  
  // 如果是昨天，显示"昨天"
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  }
  
  // 如果是今年，显示月-日
  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}-${date.getDate()}`;
  }
  
  // 其他情况，显示年-月-日
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
};

// 滚动到底部
const scrollToBottom = () => {
  if (messageContainer.value) {
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight;
  }
};

// WebSocket连接函数
const connectWebSocket = () => {
  try {
    const token = userStore.token;
    if (!token) {
      console.warn('用户未登录，无法建立WebSocket连接');
      return;
    }

    // 根据当前环境动态设置WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname === 'localhost' ? 'localhost:8080' : window.location.host;
    const wsUrl = `${protocol}//${host}/ws/chat?token=${token}`;
    console.log('WebSocket连接URL:', wsUrl);
    websocket = new WebSocket(wsUrl);

    websocket.onopen = () => {
      console.log('WebSocket连接已建立');
      isWebSocketConnected.value = true;
      ElMessage.success('实时聊天已连接', { duration: 2000 });
    };

    websocket.onmessage = (event) => {
      try {
        const newMessage = JSON.parse(event.data);
        console.log('收到WebSocket消息:', newMessage);
        console.log('当前消息列表长度:', messages.value.length);

        // 如果是系统消息
        if (newMessage.isSystemMessage) {
          ElMessage.info(newMessage.content);
          return;
        }

        // 如果是当前会话的消息，直接添加到消息列表
        if (selectedConversation.value &&
            newMessage.conversationId === selectedConversation.value.conversationId) {

          // 设置isMine字段，判断消息是否由当前用户发送
          newMessage.isMine = newMessage.senderId === userInfo.value?.userId;

          // 检查消息是否已存在（避免重复添加）
          const existingMessage = messages.value.find(m => m.messageId === newMessage.messageId);
          if (!existingMessage) {
            console.log('WebSocket添加新消息:', newMessage.content, 'isMine:', newMessage.isMine);
            // 直接添加到末尾（WebSocket消息通常是最新的）
            messages.value.push(newMessage);

            nextTick(() => {
              scrollToBottom();
            });
          } else {
            console.log('WebSocket消息已存在，跳过:', newMessage.messageId);
          }
        }

        // 更新会话列表和未读消息数
        fetchConversations();
        fetchUnreadCount();

      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    websocket.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason);
      isWebSocketConnected.value = false;

      // 如果不是主动关闭，尝试重连
      if (event.code !== 1000) {
        console.log('WebSocket异常关闭，3秒后尝试重连...');
        setTimeout(() => {
          if (!isWebSocketConnected.value) {
            connectWebSocket();
          }
        }, 3000);
      }
    };

    websocket.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      isWebSocketConnected.value = false;
      ElMessage.error('实时聊天连接失败，将使用轮询模式');
    };

  } catch (error) {
    console.error('建立WebSocket连接失败:', error);
  }
};

// 断开WebSocket连接
const disconnectWebSocket = () => {
  if (websocket) {
    websocket.close(1000, '用户主动断开连接');
    websocket = null;
    isWebSocketConnected.value = false;
  }
};
</script>

<style scoped>
:root {
    --primary-color: #FF0000; /* 纯红色 */
    --secondary-color: #FF3333;
    --text-color-dark: #333;
    --text-color-light: #666;
    --bg-color: #EAEAEA; /* 保持较深的页面背景色 */
    --white: #FFFFFF;
    --border-color: #EFEFEF;
    --shadow-light: rgba(0,0,0,0.05);
}

.common-layout {
  background-color: #f0f2f5; /* 浅灰色背景 */
  min-height: 100vh;
}

/* 顶部导航 - 复用自UserProfile */
.top-nav {
    background: #FFFFFF;
    padding: 18px 0;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}
.top-nav .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}
.top-nav .logo {
    font-size: 36px;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}
.top-nav .nav-right {
  display: flex;
  align-items: center;
}
.user-avatar {
  display: flex;
  align-items: center;
}
.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF0000;
  transition: transform 0.2s ease;
}
.avatar-image:hover {
  transform: scale(1.1);
}

.main-content {
  padding: 40px; /* 增加内边距，使主要内容区域变大 */
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 卡片顶部对齐 */
}

.message-card {
  width: 95%; /* 增加宽度 */
  max-width: 1300px; /* 增加最大宽度 */
  min-height: 750px; /* 增加最小高度，使其变长 */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 12px; /* 添加圆角 */
  border: 1px solid #e0e0e0; /* 添加边框 */
}

.message-container {
  height: 100%; /* 确保行占据卡片全部高度 */
}

.message-list-col {
  border-right: 1px solid #ebeef5;
  padding-right: 0; /* 移除默认内边距 */
  height: 750px; /* 固定高度 */
  overflow-y: auto; /* 允许滚动 */
}

.message-list-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
}

.message-list-header .message-title {
  font-size: 18px;
  font-weight: bold;
  margin-right: 10px;
}

.message-list-header .message-count {
  margin-right: auto; /* 将操作推到右侧 */
}

.empty-conversations {
  text-align: center;
  padding: 50px 20px;
  color: #909399;
}

.empty-conversations p {
  margin: 5px 0;
}

.empty-conversations .hint {
  font-size: 14px;
  color: #c0c4cc;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.message-item:hover {
  background-color: #f5f7fa;
}

.message-item.active {
  background-color: #f0f7ff;
  border-left: 3px solid #FF0000;
}

.message-item .notification-badge {
  margin-right: 15px;
}

.message-item .message-content {
  flex-grow: 1;
}

.message-item .message-title-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.message-item .message-type {
  font-weight: bold;
  color: #303133;
}

.message-item .message-summary {
  font-size: 14px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-item .message-time {
  font-size: 12px;
  color: #c0c4cc;
  text-align: right;
  margin-top: 5px;
}

.chat-area-col {
  display: flex;
  flex-direction: column;
  height: 750px; /* 固定高度 */
}

.chat-empty-state {
  text-align: center;
  color: #909399;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.chat-empty-state .empty-image {
  width: 150px; /* 根据需要调整大小 */
  height: auto;
  margin-bottom: 20px;
}

.chat-empty-state .empty-text {
  font-size: 18px;
  margin-bottom: 5px;
}

.chat-empty-state .empty-subtext {
  font-size: 14px;
}

/* 聊天界面样式 */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: white;
}

.chat-header .chat-title {
  font-size: 18px;
  font-weight: bold;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.no-messages {
  text-align: center;
  padding: 50px 0;
  color: #909399;
}

.load-more {
  text-align: center;
  color: #409eff;
  cursor: pointer;
  padding: 10px 0;
  font-size: 14px;
}

.load-more:hover {
  text-decoration: underline;
}

.message-bubble-wrapper {
  margin-bottom: 15px;
}

.system-message {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin: 10px 0;
  background-color: rgba(144, 147, 153, 0.1);
  padding: 5px 10px;
  border-radius: 10px;
  display: inline-block;
  margin-left: 50%;
  transform: translateX(-50%);
}

.message-bubble {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.message-bubble.mine {
  justify-content: flex-end; /* 将自己的消息靠右对齐 */
}

.message-avatar {
  margin: 0 10px;
}

.message-content-bubble {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 10px;
  position: relative;
}

.other-bubble {
  background-color: white;
  border: 1px solid #e0e0e0;
}

.mine-bubble {
  background-color: #2196F3; /* 蓝色 */
  color: white;
}

.message-text {
  word-break: break-word;
}

.message-time-bubble {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 5px;
  text-align: right;
}

.read-status {
  margin-left: 5px;
  font-size: 12px;
}

.mine-bubble .message-time-bubble {
  color: rgba(255, 255, 255, 0.8);
}

.message-input-area {
  padding: 15px;
  background-color: white;
  border-top: 1px solid #ebeef5;
}

.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.message-actions .hint {
  color: #909399;
  font-size: 12px;
}

/* WebSocket连接状态指示器 */
.connection-status {
  margin-left: 10px;
  font-size: 12px;
  font-weight: bold;
}

.connection-status.connected {
  color: #67C23A; /* 绿色表示连接正常 */
}

.connection-status.disconnected {
  color: #F56C6C; /* 红色表示连接断开 */
}
</style> 