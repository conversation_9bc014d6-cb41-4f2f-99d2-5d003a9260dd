import request from '@/utils/request'

/**
 * 发送短信验证码
 * @param {string} phone 手机号
 * @param {string} type 验证码类型 (login, bind, register)
 */
export function sendSmsCode(phone, type = 'login') {
  return request({
    url: '/sms/send',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: `phone=${phone}&type=${type}`
  })
}

/**
 * 验证短信验证码
 * @param {string} phone 手机号
 * @param {string} code 验证码
 * @param {string} type 验证码类型
 */
export function verifySmsCode(phone, code, type = 'login') {
  return request({
    url: '/sms/verify',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: `phone=${phone}&code=${code}&type=${type}`
  })
}

/**
 * 检查短信发送状态
 * @param {string} phone 手机号
 */
export function checkSmsStatus(phone) {
  return request({
    url: '/sms/check',
    method: 'get',
    params: { phone }
  })
}
