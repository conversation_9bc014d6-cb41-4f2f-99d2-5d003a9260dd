package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.mapper.CategoryMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Category;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ProductService;
import com.lzhshtp.shangcheng.service.SearchService;
import com.lzhshtp.shangcheng.service.audit.ProductAuditService;
import com.lzhshtp.shangcheng.utils.OssUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    private static final Logger logger = LoggerFactory.getLogger(ProductServiceImpl.class);

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private SearchService searchService;

    @Autowired
    private ProductAuditService productAuditService;

    @Override
    public PageResult<ProductDTO> getProductsByCondition(ProductQueryParams params) {
        // 设置默认值
        if (params.getPage() == null || params.getPage() < 1) {
            params.setPage(1);
        }
        if (params.getPageSize() == null || params.getPageSize() < 1) {
            params.setPageSize(10);
        }

        // 创建分页对象
        Page<Product> page = new Page<>(params.getPage(), params.getPageSize());

        // 添加调试日志
        logger.info("商品查询参数: categoryId={}, condition={}, location={}, deliveryMethod={}, sameCity={}",
                   params.getCategoryId(), params.getCondition(), params.getLocation(),
                   params.getDeliveryMethod(), params.getSameCity());

        // 执行查询
        IPage<Product> productPage = productMapper.selectProductsByCondition(page, params);

        // 转换为DTO
        List<ProductDTO> productDTOs = productPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 返回分页结果
        return new PageResult<>(productDTOs, productPage.getTotal(), params.getPage(), params.getPageSize());
    }

    @Override
    public ProductDTO getProductById(Long productId) {
        Product product = productMapper.selectProductDetail(productId);
        if (product == null) {
            return null;
        }
        return convertToDTO(product);
    }

    @Override
    @Transactional
    public Long createProduct(ProductDTO productDTO) {
        Product product = new Product();
        BeanUtils.copyProperties(productDTO, product);

        // 设置默认值
        product.setPostedDate(LocalDateTime.now());
        product.setStatus(Product.ProductStatus.PENDING_REVIEW);

        // 处理图片URL
        if (productDTO.getImageUrls() != null && !productDTO.getImageUrls().isEmpty()) {
            product.setImageUrls(String.join(",", productDTO.getImageUrls()));
        }

        // 保存商品
        productMapper.insert(product);

        // 异步同步到ElasticSearch
        try {
            searchService.syncProductToES(product.getId());
            logger.info("商品创建成功并同步到ES，ID: {}", product.getId());
        } catch (Exception e) {
            logger.warn("商品同步到ES失败，ID: {}, 错误: {}", product.getId(), e.getMessage());
        }

        // 触发自动审核
        try {
            logger.info("开始对新创建的商品进行自动审核，商品ID: {}", product.getId());
            productAuditService.submitProductForAudit(product.getId());
            logger.info("商品自动审核已提交，商品ID: {}", product.getId());
        } catch (Exception e) {
            logger.error("商品自动审核提交失败，商品ID: {}, 错误: {}", product.getId(), e.getMessage());
            // 审核失败不影响商品创建，只记录日志
        }

        return product.getId();
    }

    @Override
    @Transactional
    public boolean updateProduct(Long productId, ProductDTO productDTO) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            return false;
        }

        BeanUtils.copyProperties(productDTO, product);
        product.setId(productId);  // 确保ID不变

        // 处理图片URL
        if (productDTO.getImageUrls() != null && !productDTO.getImageUrls().isEmpty()) {
            product.setImageUrls(String.join(",", productDTO.getImageUrls()));
        }

        boolean result = productMapper.updateById(product) > 0;

        // 异步同步到ElasticSearch
        if (result) {
            try {
                searchService.syncProductToES(productId);
                logger.info("商品更新成功并同步到ES，ID: {}", productId);
            } catch (Exception e) {
                logger.warn("商品同步到ES失败，ID: {}, 错误: {}", productId, e.getMessage());
            }
        }

        return result;
    }

    @Override
    @Transactional
    public boolean deleteProduct(Long productId) {
        // 逻辑删除，将状态改为deleted
        return updateProductStatus(productId, Product.ProductStatus.DELETED);
    }

    @Override
    @Transactional
    public boolean updateProductStatus(Long productId, String status) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            return false;
        }

        product.setStatus(status);
        boolean result = productMapper.updateById(product) > 0;

        // 异步同步到ElasticSearch
        if (result) {
            try {
                if (Product.ProductStatus.DELETED.equals(status)) {
                    // 如果是删除状态，从ES中删除
                    searchService.deleteProductFromES(productId);
                    logger.info("商品删除成功并从ES中移除，ID: {}", productId);
                } else {
                    // 其他状态更新，同步到ES
                    searchService.syncProductToES(productId);
                    logger.info("商品状态更新成功并同步到ES，ID: {}, 新状态: {}", productId, status);
                }
            } catch (Exception e) {
                logger.warn("商品ES同步失败，ID: {}, 状态: {}, 错误: {}", productId, status, e.getMessage());
            }
        }

        return result;
    }

    @Override
    @Transactional
    public boolean updateProductImage(Long productId, String imageUrl) {
        logger.info("开始更新商品图片，商品ID: {}，新图片URL: {}", productId, imageUrl);

        Product product = productMapper.selectById(productId);
        if (product == null) {
            logger.error("商品不存在，ID: {}", productId);
            return false;
        }

        // 更新商品图片URL（替换第一张图片）
        if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
            List<String> imageUrls = Arrays.asList(product.getImageUrls().split(","));
            if (!imageUrls.isEmpty()) {
                // 获取第一张图片URL（旧图片）
                String oldImageUrl = imageUrls.get(0);
                logger.info("获取到旧图片URL: {}", oldImageUrl);

                // 创建一个可修改的列表
                List<String> updatedUrls = new ArrayList<>(imageUrls);
                // 替换第一张图片
                updatedUrls.set(0, imageUrl);
                // 更新图片URL字符串
                product.setImageUrls(String.join(",", updatedUrls));

                // 删除旧图片
                try {
                    logger.info("准备删除旧图片: {}", oldImageUrl);
                    ossUtil.deleteFile(oldImageUrl);
                    logger.info("旧图片删除成功");
                } catch (Exception e) {
                    // 删除失败不影响更新操作，记录日志即可
                    logger.error("删除旧图片失败: {}", e.getMessage(), e);
                }
            } else {
                logger.info("商品没有现有图片，直接设置新图片");
                product.setImageUrls(imageUrl);
            }
        } else {
            logger.info("商品没有现有图片URL，直接设置新图片");
            product.setImageUrls(imageUrl);
        }

        boolean result = productMapper.updateById(product) > 0;
        logger.info("商品图片更新结果: {}", result ? "成功" : "失败");
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> batchDeleteProducts(List<Long> productIds) {
        logger.info("开始批量删除商品，商品ID列表: {}", productIds);

        Map<String, Object> result = new HashMap<>();
        List<Long> successIds = new ArrayList<>();
        List<Long> failIds = new ArrayList<>();

        for (Long productId : productIds) {
            try {
                boolean success = updateProductStatus(productId, Product.ProductStatus.DELETED);
                if (success) {
                    successIds.add(productId);
                    logger.info("成功删除商品，ID: {}", productId);
                } else {
                    failIds.add(productId);
                    logger.warn("删除商品失败，ID: {}", productId);
                }
            } catch (Exception e) {
                failIds.add(productId);
                logger.error("删除商品异常，ID: {}", productId, e);
            }
        }

        result.put("successCount", successIds.size());
        result.put("failCount", failIds.size());
        result.put("successIds", successIds);
        result.put("failIds", failIds);

        logger.info("批量删除商品完成，成功: {}，失败: {}", successIds.size(), failIds.size());
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> batchApproveProducts(List<Long> productIds) {
        logger.info("开始批量上架商品，商品ID列表: {}", productIds);

        Map<String, Object> result = new HashMap<>();
        List<Long> successIds = new ArrayList<>();
        List<Long> failIds = new ArrayList<>();
        List<Long> notPendingIds = new ArrayList<>();

        for (Long productId : productIds) {
            try {
                // 获取商品信息，检查状态
                Product product = productMapper.selectById(productId);
                if (product == null) {
                    failIds.add(productId);
                    logger.warn("商品不存在，ID: {}", productId);
                    continue;
                }

                // 只有管理员下架的商品才能被批量上架
                if (!Product.ProductStatus.OFF_SHELF_BY_ADMIN.equals(product.getStatus())) {
                    notPendingIds.add(productId);
                    logger.warn("商品状态不是待审核，无法上架，ID: {}, 当前状态: {}", productId, product.getStatus());
                    continue;
                }

                boolean success = updateProductStatus(productId, Product.ProductStatus.AVAILABLE);
                if (success) {
                    successIds.add(productId);
                    logger.info("成功上架商品，ID: {}", productId);
                } else {
                    failIds.add(productId);
                    logger.warn("上架商品失败，ID: {}", productId);
                }
            } catch (Exception e) {
                failIds.add(productId);
                logger.error("上架商品异常，ID: {}", productId, e);
            }
        }

        result.put("successCount", successIds.size());
        result.put("failCount", failIds.size());
        result.put("notPendingCount", notPendingIds.size());
        result.put("successIds", successIds);
        result.put("failIds", failIds);
        result.put("notPendingIds", notPendingIds);

        logger.info("批量上架商品完成，成功: {}，失败: {}，非待审核状态: {}",
                successIds.size(), failIds.size(), notPendingIds.size());
        return result;
    }

    /**
     * 将Product实体转换为ProductDTO
     */
    private ProductDTO convertToDTO(Product product) {
        ProductDTO dto = new ProductDTO();
        BeanUtils.copyProperties(product, dto);

        // 处理图片URL列表
        if (product.getImageUrls() != null && !product.getImageUrls().isEmpty()) {
            dto.setImageUrls(Arrays.asList(product.getImageUrls().split(",")));
        } else {
            dto.setImageUrls(new ArrayList<>());
        }

        // 获取分类名称
        if (product.getCategoryId() != null) {
            Category category = categoryMapper.selectById(product.getCategoryId());
            if (category != null) {
                dto.setCategoryName(category.getName());
            }
        }

        // 获取卖家名称和头像
        if (product.getSellerId() != null) {
            User seller = userMapper.selectById(product.getSellerId());
            if (seller != null) {
                dto.setSellerName(seller.getUsername());
                dto.setSellerAvatar(seller.getAvatarUrl());
            } else {
                dto.setSellerName("未知用户");
                dto.setSellerAvatar(null);
            }
        } else {
            dto.setSellerName("未知用户");
            dto.setSellerAvatar(null);
        }

        return dto;
    }
}
