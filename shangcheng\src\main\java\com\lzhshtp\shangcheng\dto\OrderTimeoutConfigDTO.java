package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单超时配置DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderTimeoutConfigDTO {

    /**
     * 是否启用自动取消
     */
    private Boolean enabled;

    /**
     * 超时时间（分钟）
     */
    private Integer timeoutMinutes;

    /**
     * 检查频率（分钟）
     */
    private Integer checkInterval;

    /**
     * 是否发送通知
     */
    private Boolean sendNotification;

    /**
     * 通知方式列表
     */
    private List<String> notificationMethods;

    /**
     * 通知对象列表
     */
    private List<String> notificationTargets;

    /**
     * 生成Cron表达式
     */
    public String generateCronExpression() {
        if (checkInterval == null || checkInterval <= 0) {
            return "0 */5 * * * ?"; // 默认每5分钟
        }
        return String.format("0 */%d * * * ?", checkInterval);
    }
}
