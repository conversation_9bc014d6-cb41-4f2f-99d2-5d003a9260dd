package com.lzhshtp.shangcheng.controller;

import java.util.List;

import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ProductReviewDTO;
import com.lzhshtp.shangcheng.dto.ProductReviewRequest;
import com.lzhshtp.shangcheng.service.ProductReviewService;

import jakarta.validation.Valid;

/**
 * 商品评价Controller
 */
@RestController
@RequestMapping("/api/reviews")
public class ProductReviewController {

    @Autowired
    private ProductReviewService productReviewService;
    @Autowired
    private UserService userService;

    /**
     * 添加商品评价
     *
     * @param request 评价请求
     * @return 评价DTO
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ProductReviewDTO>> addReview(@Valid @RequestBody ProductReviewRequest request) {
        try {
            // 获取当前登录用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            Long userId = user.getUserId(); // 获取真正的用户ID

            ProductReviewDTO review = productReviewService.addReview(userId, request);
            return ResponseEntity.ok(ApiResponse.success(review));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.fail(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("添加评价失败：" + e.getMessage()));
        }
    }

    /**
     * 根据商品ID获取评价列表
     *
     * @param productId 商品ID
     * @return 评价列表
     */
    @GetMapping("/product/{productId}")
    public ResponseEntity<ApiResponse<List<ProductReviewDTO>>> getReviewsByProductId(@PathVariable Long productId) {
        try {
            List<ProductReviewDTO> reviews = productReviewService.getReviewsByProductId(productId);
            return ResponseEntity.ok(ApiResponse.success(reviews));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("获取评价列表失败：" + e.getMessage()));
        }
    }

    /**
     * 获取当前用户的评价列表
     *
     * @return 评价列表
     */
    @GetMapping("/my-reviews")
    public ResponseEntity<ApiResponse<List<ProductReviewDTO>>> getMyReviews() {
        try {
            // 获取当前登录用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            Long userId = user.getUserId(); // 获取真正的用户ID

            List<ProductReviewDTO> reviews = productReviewService.getReviewsByUserId(userId);
            return ResponseEntity.ok(ApiResponse.success(reviews));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("获取我的评价列表失败：" + e.getMessage()));
        }
    }

    /**
     * 根据评价ID获取评价
     *
     * @param reviewId 评价ID
     * @return 评价DTO
     */
    @GetMapping("/{reviewId}")
    public ResponseEntity<ApiResponse<ProductReviewDTO>> getReviewById(@PathVariable Long reviewId) {
        try {
            ProductReviewDTO review = productReviewService.getReviewById(reviewId);
            if (review == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(ApiResponse.success(review));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("获取评价失败：" + e.getMessage()));
        }
    }

    /**
     * 更新评价
     *
     * @param reviewId 评价ID
     * @param request 评价请求
     * @return 更新后的评价DTO
     */
    @PutMapping("/{reviewId}")
    public ResponseEntity<ApiResponse<ProductReviewDTO>> updateReview(
            @PathVariable Long reviewId,
            @Valid @RequestBody ProductReviewRequest request) {
        try {
            // 获取当前登录用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            Long userId = user.getUserId(); // 获取真正的用户ID

            ProductReviewDTO review = productReviewService.updateReview(userId, reviewId, request);
            return ResponseEntity.ok(ApiResponse.success(review));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.fail(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("更新评价失败：" + e.getMessage()));
        }
    }

    /**
     * 删除评价
     *
     * @param reviewId 评价ID
     * @return 删除结果
     */
    @DeleteMapping("/{reviewId}")
    public ResponseEntity<ApiResponse<Boolean>> deleteReview(@PathVariable Long reviewId) {
        try {
            // 获取当前登录用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            Long userId = user.getUserId(); // 获取真正的用户ID


            boolean result = productReviewService.deleteReview(userId, reviewId);
            if (result) {
                return ResponseEntity.ok(ApiResponse.success(true));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.fail("删除评价失败，可能是评价不存在或无权删除"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("删除评价失败：" + e.getMessage()));
        }
    }

    /**
     * 获取商品的评价统计信息
     *
     * @param productId 商品ID
     * @return 评价统计信息
     */
    @GetMapping("/product/{productId}/stats")
    public ResponseEntity<ApiResponse<Object>> getReviewStats(@PathVariable Long productId) {
        try {
            int count = productReviewService.getReviewCount(productId);
            Double avgRating = productReviewService.getAverageRating(productId);

            // 创建包含统计信息的对象
            var stats = new Object() {
                public final int reviewCount = count;
                public final Double averageRating = avgRating;
            };

            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("获取评价统计信息失败：" + e.getMessage()));
        }
    }


}
