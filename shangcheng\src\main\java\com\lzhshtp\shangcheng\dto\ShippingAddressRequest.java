package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 收货地址请求类，用于新增和更新地址
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingAddressRequest {
    @NotBlank(message = "收货人姓名不能为空")
    @Size(max = 100, message = "收货人姓名不能超过100个字符")
    private String recipientName; // 收货人姓名
    
    @NotBlank(message = "收货人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    private String phoneNumber;   // 收货人电话
    
    @NotBlank(message = "省份不能为空")
    @Size(max = 50, message = "省份不能超过50个字符")
    private String province;      // 省份
    
    @NotBlank(message = "城市不能为空")
    @Size(max = 50, message = "城市不能超过50个字符")
    private String city;          // 城市
    
    @Size(max = 50, message = "区/县不能超过50个字符")
    private String district;      // 区/县
    
    @NotBlank(message = "详细地址不能为空")
    private String streetAddress; // 详细街道地址
    
    @Pattern(regexp = "^\\d{6}$", message = "请输入正确的邮政编码")
    private String postalCode;    // 邮政编码
    
    private Boolean isDefault;    // 是否是用户的默认地址
} 