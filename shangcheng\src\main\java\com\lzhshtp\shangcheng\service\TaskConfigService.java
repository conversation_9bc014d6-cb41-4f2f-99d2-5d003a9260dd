package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;
import com.lzhshtp.shangcheng.model.ScheduledTaskConfig;

import java.util.List;

/**
 * 定时任务配置服务接口
 */
public interface TaskConfigService {
    
    /**
     * 获取订单超时配置
     * 
     * @return 订单超时配置
     */
    OrderTimeoutConfigDTO getOrderTimeoutConfig();
    
    /**
     * 更新订单超时配置
     * 
     * @param config 订单超时配置
     * @return 是否更新成功
     */
    boolean updateOrderTimeoutConfig(OrderTimeoutConfigDTO config);
    
    /**
     * 获取所有任务配置
     * 
     * @return 任务配置列表
     */
    List<ScheduledTaskConfig> getAllTaskConfigs();
    
    /**
     * 根据任务名称获取配置
     * 
     * @param taskName 任务名称
     * @return 任务配置
     */
    ScheduledTaskConfig getTaskConfigByName(String taskName);
    
    /**
     * 启用/禁用任务
     * 
     * @param taskName 任务名称
     * @param enabled 是否启用
     * @return 是否操作成功
     */
    boolean toggleTask(String taskName, boolean enabled);
    
    /**
     * 手动执行任务
     * 
     * @param taskName 任务名称
     * @return 执行结果
     */
    String executeTaskManually(String taskName);
}
