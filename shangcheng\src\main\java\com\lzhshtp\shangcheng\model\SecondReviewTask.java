package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 二度复审任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_second_review_tasks")
public class SecondReviewTask {
    
    /**
     * 任务ID
     */
    @TableId(value = "lzhshtp_task_id", type = IdType.AUTO)
    private Long taskId;
    
    /**
     * 商品ID
     */
    @TableField("lzhshtp_product_id")
    private Long productId;
    

    
    /**
     * 原人工审核任务ID
     */
    @TableField("lzhshtp_manual_audit_task_id")
    private Long manualTaskId;

    /**
     * 复审员ID（高级管理员ID）
     */
    @TableField("lzhshtp_senior_admin_id")
    private Long reviewerId;
    
    /**
     * 任务状态：pending-待复审，processing-复审中，completed-已完成
     */
    @TableField("lzhshtp_status")
    private String status;
    
    /**
     * 升级原因
     */
    @TableField("lzhshtp_escalation_reason")
    private String escalationReason;
    
    /**
     * 自动审核结果摘要（不存储在数据库中，从allPreviousMaterials中提取）
     */
    @TableField(exist = false)
    private String autoAuditResult;

    /**
     * 人工审核结果摘要（不存储在数据库中，从allPreviousMaterials中提取）
     */
    @TableField(exist = false)
    private String manualAuditResult;
    
    /**
     * 最终决策：approved-最终通过，rejected-最终拒绝，request_materials-要求补充材料
     */
    @TableField("lzhshtp_admin_decision")
    private String finalDecision;

    /**
     * 复审意见
     */
    @TableField("lzhshtp_admin_comments")
    private String reviewComments;
    
    /**
     * 风险评估（不存储在数据库中，从allPreviousMaterials中提取）
     */
    @TableField(exist = false)
    private String riskLevel;
    
    /**
     * 创建时间
     */
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    /**
     * 任务分配时间
     */
    @TableField("lzhshtp_assigned_time")
    private LocalDateTime claimedTime;
    
    /**
     * 完成时间
     */
    @TableField("lzhshtp_completed_time")
    private LocalDateTime completedTime;
    
    /**
     * 截止时间
     */
    @TableField("lzhshtp_deadline")
    private LocalDateTime deadline;
    
    // 以下字段用于前端显示，不存储在数据库中
    @TableField(exist = false)
    private String productTitle;
    
    @TableField(exist = false)
    private String productDescription;
    
    @TableField(exist = false)
    private BigDecimal productPrice;
    
    @TableField(exist = false)
    private String productImages;
    
    @TableField(exist = false)
    private String categoryName;
    
    @TableField(exist = false)
    private String productCondition;
    
    @TableField(exist = false)
    private String sellerNickname;
    
    @TableField(exist = false)
    private Integer sellerCreditScore;
    
    @TableField(exist = false)
    private LocalDateTime sellerRegisterTime;
    
    @TableField(exist = false)
    private Integer sellerPostCount;
    
    @TableField(exist = false)
    private LocalDateTime autoAuditTime;
    
    @TableField(exist = false)
    private LocalDateTime manualAuditTime;
    
    /**
     * 所有之前的审核材料和记录
     */
    @TableField("lzhshtp_all_previous_materials")
    private Object allPreviousMaterials;
}
