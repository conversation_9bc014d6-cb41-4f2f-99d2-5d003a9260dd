package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.model.SecondReviewTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 二度复审服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class SecondReviewServiceTest {

    @Autowired
    private SecondReviewService secondReviewService;

    @Test
    public void testGetSecondReviewTasks() {
        // 测试获取二度复审任务列表
        PageResult<SecondReviewTask> result = secondReviewService.getSecondReviewTasks(
            1, 10, null, null, null, null);
        
        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertTrue(result.getTotal() >= 0);
        
        System.out.println("获取到 " + result.getTotal() + " 个二度复审任务");
    }

    @Test
    public void testGetSecondReviewStats() {
        // 测试获取统计数据
        Map<String, Object> stats = secondReviewService.getSecondReviewStats();
        
        assertNotNull(stats);
        assertTrue(stats.containsKey("pending"));
        assertTrue(stats.containsKey("in_progress"));
        assertTrue(stats.containsKey("approved"));
        assertTrue(stats.containsKey("rejected"));
        
        System.out.println("统计数据: " + stats);
    }
}
