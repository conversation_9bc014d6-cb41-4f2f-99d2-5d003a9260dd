package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物流跟踪实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_logistics_tracking")
public class LogisticsTracking {
    
    @TableId(value = "lzhshtp_tracking_id", type = IdType.AUTO)
    private Long trackingId;
    
    @TableField("lzhshtp_order_id")
    private Long orderId;
    
    @TableField("lzhshtp_tracking_type")
    private String trackingType;
    
    @TableField("lzhshtp_tracking_number")
    private String trackingNumber;
    
    @TableField("lzhshtp_current_status")
    private String currentStatus;
    
    @TableField("lzhshtp_current_location")
    private String currentLocation;
    
    @TableField("lzhshtp_logistics_company")
    private String logisticsCompany;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    @TableField("lzhshtp_updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 物流类型枚举
     */
    public static class TrackingType {
        public static final String SELLER_TO_BUYER = "seller_to_buyer";
        public static final String SELLER_TO_OFFICIAL = "seller_to_official";
        public static final String OFFICIAL_TO_BUYER = "official_to_buyer";
        public static final String BUYER_TO_SELLER = "buyer_to_seller";
    }
}
