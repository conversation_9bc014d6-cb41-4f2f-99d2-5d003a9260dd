<template>
  <div class="feedback-management">
    <div class="page-header">
      <h2 class="page-title">用户反馈管理</h2>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="stats">
      <div class="stat-card">
        <div class="stat-value">{{ stats.pendingCount || 0 }}</div>
        <div class="stat-label">待处理</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.inProgressCount || 0 }}</div>
        <div class="stat-label">处理中</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.resolvedCount || 0 }}</div>
        <div class="stat-label">已解决</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ stats.rejectedCount || 0 }}</div>
        <div class="stat-label">已拒绝</div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-container">
      <div class="filter-item">
        <label>关键词:</label>
        <input
          type="text"
          v-model="queryParams.keyword"
          placeholder="输入关键词搜索"
          @keyup.enter="fetchFeedbackList"
        />
      </div>

      <div class="filter-item">
        <label>反馈类型:</label>
        <select v-model="queryParams.feedbackType">
          <option value="">全部</option>
          <option value="bug_report">问题反馈</option>
          <option value="suggestion">建议</option>
          <option value="complaint">投诉</option>
          <option value="abuse_report">举报</option>
        </select>
      </div>

      <div class="filter-item">
        <label>处理状态:</label>
        <select v-model="queryParams.status">
          <option value="">全部</option>
          <option value="pending">待处理</option>
          <option value="in_progress">处理中</option>
          <option value="resolved">已解决</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>

      <button class="search-btn" @click="fetchFeedbackList">搜索</button>
      <button class="reset-btn" @click="resetFilters">重置</button>
    </div>

    <!-- 反馈列表 -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>用户</th>
            <th>类型</th>
            <th>状态</th>
            <th>提交时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in feedbackList" :key="item.feedbackId">
            <td>{{ item.feedbackId }}</td>
            <td>{{ item.reporterName }}</td>
            <td>{{ item.feedbackTypeDesc }}</td>
            <td>
              <span class="status-badge" :class="getStatusClass(item.status)">
                {{ item.statusDesc }}
              </span>
            </td>
            <td>{{ formatDateTime(item.submittedAt) }}</td>
            <td>
              <button class="action-btn reply-btn" @click="replyToUser(item)">
                <span class="btn-icon">💬</span> 回复
              </button>
              <button class="action-btn view-btn" @click="viewFeedback(item)">
                <span class="btn-icon">📝</span> 处理
              </button>
              <button class="action-btn delete-btn" @click="confirmDelete(item)">
                <span class="btn-icon">🗑️</span> 删除
              </button>
            </td>
          </tr>
          <tr v-if="feedbackList.length === 0">
            <td colspan="6" class="no-data">暂无数据</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" v-if="total > 0">
      <div class="pagination-info">
        共 {{ total }} 条数据，当前 {{ currentPage }}/{{ totalPages }} 页
      </div>
      <div class="pagination-btns">
        <button
          :disabled="currentPage <= 1"
          @click="changePage(currentPage - 1)"
        >上一页</button>
        <button
          :disabled="currentPage >= totalPages"
          @click="changePage(currentPage + 1)"
        >下一页</button>
      </div>
    </div>

    <!-- 反馈详情弹窗 -->
    <div class="modal" v-if="showDetailModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>反馈详情</h2>
          <button class="close-btn" @click="showDetailModal = false">&times;</button>
        </div>
        <div class="modal-body" v-if="currentFeedback">
          <div class="detail-item">
            <span class="detail-label">反馈ID:</span>
            <span class="detail-value">{{ currentFeedback.feedbackId }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">提交用户:</span>
            <span class="detail-value">{{ currentFeedback.reporterName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">反馈类型:</span>
            <span class="detail-value">{{ currentFeedback.feedbackTypeDesc }}</span>
          </div>
          <div class="detail-item" v-if="currentFeedback.relatedEntityType">
            <span class="detail-label">关联对象:</span>
            <span class="detail-value">
              {{ getEntityTypeDesc(currentFeedback.relatedEntityType) }}
              {{ currentFeedback.relatedEntityName ? ` - ${currentFeedback.relatedEntityName}` : '' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">提交时间:</span>
            <span class="detail-value">{{ formatDateTime(currentFeedback.submittedAt) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">状态:</span>
            <span class="detail-value status-badge" :class="getStatusClass(currentFeedback.status)">
              {{ currentFeedback.statusDesc }}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">反馈内容:</span>
            <div class="detail-content">{{ currentFeedback.content }}</div>
          </div>
          <div class="detail-item" v-if="currentFeedback.adminNotes">
            <span class="detail-label">处理备注:</span>
            <div class="detail-content">{{ currentFeedback.adminNotes }}</div>
          </div>
          <div class="detail-item" v-if="currentFeedback.resolvedByAdminName">
            <span class="detail-label">处理人员:</span>
            <span class="detail-value">{{ currentFeedback.resolvedByAdminName }}</span>
          </div>

          <!-- 状态处理表单 -->
          <div class="status-form">
            <h3>处理反馈</h3>
            <div class="form-item">
              <label>状态:</label>
              <select v-model="statusForm.status">
                <option value="pending">待处理</option>
                <option value="in_progress">处理中</option>
                <option value="resolved">已解决</option>
                <option value="rejected">已拒绝</option>
              </select>
            </div>
            <div class="form-item">
              <label>处理备注:</label>
              <textarea v-model="statusForm.adminNotes" rows="4" placeholder="请输入处理备注..."></textarea>
            </div>
            <div class="form-btns">
              <button class="save-btn" @click="updateStatus">保存</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="modal" v-if="showDeleteModal">
      <div class="modal-content delete-confirm">
        <div class="modal-header">
          <h2>确认删除</h2>
          <button class="close-btn" @click="showDeleteModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <p>确定要删除此反馈记录吗？此操作不可恢复。</p>
          <div class="confirm-btns">
            <button class="cancel-btn" @click="showDeleteModal = false">取消</button>
            <button class="confirm-btn" @click="deleteFeedbackItem">确认删除</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回复用户弹窗 -->
    <div class="modal" v-if="showReplyModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>回复用户</h2>
          <button class="close-btn" @click="showReplyModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="replyTarget">
            <div class="detail-item">
              <span class="detail-label">回复用户:</span>
              <span class="detail-value">{{ replyTarget.reporterName }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">反馈内容:</span>
              <div class="detail-content">{{ replyTarget.content }}</div>
            </div>
            <div class="form-item">
              <label>回复内容:</label>
              <textarea 
                v-model="replyForm.content" 
                rows="6" 
                placeholder="请输入回复内容..."
                class="reply-textarea"
              ></textarea>
            </div>
            <div class="form-btns">
              <button 
                class="save-btn" 
                @click="sendReply" 
                :disabled="!replyForm.content || sendingReply"
              >
                {{ sendingReply ? '发送中...' : '发送回复' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { getFeedbackList, getFeedbackDetail, updateFeedbackStatus, deleteFeedback } from '@/admin/api/feedback'
import { getOrCreateConversation, sendSystemMessage } from '@/admin/api/messages'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  feedbackType: '',
  status: '',
  keyword: ''
})

// 数据状态
const feedbackList = ref([])
const total = ref(0)
const currentPage = ref(1)
const totalPages = computed(() => Math.ceil(total.value / queryParams.pageSize))
const stats = ref(null)

// 弹窗状态
const showDetailModal = ref(false)
const showDeleteModal = ref(false)
const showReplyModal = ref(false) // 新增回复弹窗状态
const currentFeedback = ref(null)
const feedbackToDelete = ref(null)
const replyTarget = ref(null) // 新增回复目标
const replyForm = reactive({ // 新增回复表单
  content: ''
})
const sendingReply = ref(false) // 新增发送中状态

// 状态更新表单
const statusForm = reactive({
  status: '',
  adminNotes: ''
})

// 获取反馈列表
const fetchFeedbackList = async () => {
  try {
    const res = await getFeedbackList(queryParams)
    if (res.code === 200 && res.data) {
      feedbackList.value = res.data.records || []
      total.value = res.data.total || 0
      currentPage.value = queryParams.pageNum
    } else {
      console.error('获取反馈列表失败:', res.message)
    }
  } catch (error) {
    console.error('获取反馈列表出错:', error)
  }
}

// 获取反馈统计信息
const fetchFeedbackStats = async () => {
  try {
    // 这里假设后端已经实现了统计接口
    // 实际项目中需要后端支持或前端计算
    const pendingCount = feedbackList.value.filter(item => item.status === 'pending').length
    const inProgressCount = feedbackList.value.filter(item => item.status === 'in_progress').length
    const resolvedCount = feedbackList.value.filter(item => item.status === 'resolved').length
    const rejectedCount = feedbackList.value.filter(item => item.status === 'rejected').length

    stats.value = {
      pendingCount,
      inProgressCount,
      resolvedCount,
      rejectedCount
    }
  } catch (error) {
    console.error('获取反馈统计信息出错:', error)
  }
}

// 重置筛选条件
const resetFilters = () => {
  queryParams.keyword = ''
  queryParams.feedbackType = ''
  queryParams.status = ''
  queryParams.pageNum = 1
  fetchFeedbackList()
}

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return
  queryParams.pageNum = page
  fetchFeedbackList()
}

// 查看反馈详情
const viewFeedback = async (feedback) => {
  try {
    const res = await getFeedbackDetail(feedback.feedbackId)
    if (res.code === 200 && res.data) {
      currentFeedback.value = res.data
      statusForm.status = res.data.status
      statusForm.adminNotes = res.data.adminNotes || ''
      showDetailModal.value = true
    } else {
      console.error('获取反馈详情失败:', res.message)
    }
  } catch (error) {
    console.error('获取反馈详情出错:', error)
  }
}

// 更新反馈状态
const updateStatus = async () => {
  if (!currentFeedback.value) return

  try {
    const res = await updateFeedbackStatus(currentFeedback.value.feedbackId, statusForm)
    if (res.code === 200) {
      alert('状态更新成功')
      showDetailModal.value = false
      fetchFeedbackList() // 刷新列表
    } else {
      alert('状态更新失败: ' + res.message)
    }
  } catch (error) {
    console.error('更新反馈状态出错:', error)
    alert('状态更新出错，请重试')
  }
}

// 确认删除弹窗
const confirmDelete = (feedback) => {
  feedbackToDelete.value = feedback
  showDeleteModal.value = true
}

// 删除反馈
const deleteFeedbackItem = async () => {
  if (!feedbackToDelete.value) return

  try {
    const res = await deleteFeedback(feedbackToDelete.value.feedbackId)
    if (res.code === 200) {
      alert('删除成功')
      showDeleteModal.value = false
      fetchFeedbackList() // 刷新列表
      fetchFeedbackStats() // 更新统计
    } else {
      alert('删除失败: ' + res.message)
    }
  } catch (error) {
    console.error('删除反馈出错:', error)
    alert('删除出错，请重试')
  }
}

// 回复用户
const replyToUser = async (feedback) => {
  try {
    // 获取完整的反馈详情，确保有用户ID等信息
    const res = await getFeedbackDetail(feedback.feedbackId)
    if (res.code === 200 && res.data) {
      replyTarget.value = res.data
      replyForm.content = '' // 清空回复内容
      showReplyModal.value = true
    } else {
      alert('获取反馈详情失败: ' + res.message)
    }
  } catch (error) {
    console.error('获取反馈详情出错:', error)
    alert('获取反馈详情出错，请重试')
  }
}

// 发送回复
const sendReply = async () => {
  if (!replyTarget.value || !replyForm.content) return

  sendingReply.value = true
  try {
    // 首先获取或创建与用户的会话
    const conversationRes = await getOrCreateConversation(replyTarget.value.reporterId)
    if (conversationRes.code === 200 && conversationRes.data) {
      // 获取会话ID
      const conversationId = conversationRes.data
      
      // 准备消息数据
      const messageData = {
        receiverId: replyTarget.value.reporterId,
        content: replyForm.content
      }
      
      // 发送系统消息
      const messageRes = await sendSystemMessage(messageData)
      if (messageRes.code === 200) {
        // 如果反馈状态是"待处理"，则更新为"处理中"
        if (replyTarget.value.status === 'pending') {
          const statusUpdateData = {
            status: 'in_progress',
            adminNotes: `已回复用户: ${replyForm.content.substring(0, 50)}${replyForm.content.length > 50 ? '...' : ''}`
          }
          
          try {
            await updateFeedbackStatus(replyTarget.value.feedbackId, statusUpdateData)
            // 刷新列表
            fetchFeedbackList()
          } catch (error) {
            console.error('更新反馈状态出错:', error)
          }
        }
        
        alert('回复发送成功！')
        showReplyModal.value = false
        replyTarget.value = null
        replyForm.content = ''
      } else {
        alert('回复发送失败: ' + messageRes.message)
      }
    } else {
      alert('创建或获取对话失败: ' + conversationRes.message)
    }
  } catch (error) {
    console.error('发送回复出错:', error)
    alert('发送回复出错，请重试')
  } finally {
    sendingReply.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'pending': return 'status-pending'
    case 'in_progress': return 'status-in-progress'
    case 'resolved': return 'status-resolved'
    case 'rejected': return 'status-rejected'
    default: return ''
  }
}

// 获取实体类型描述
const getEntityTypeDesc = (type) => {
  const typeMap = {
    'product': '商品',
    'user': '用户',
    'post': '帖子',
    'comment': '评论'
  }
  return typeMap[type] || type
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchFeedbackList()
})
</script>

<style scoped>
.feedback-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  color: #333;
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 筛选区域样式 */
.filter-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  margin-right: 10px;
  font-size: 14px;
  color: #666;
}

.filter-item input, .filter-item select {
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-width: 120px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn {
  background-color: #1890ff;
  color: white;
}

.reset-btn {
  background-color: #f0f0f0;
  color: #666;
}

/* 表格样式 */
.table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th, .data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.data-table th {
  background-color: #fafafa;
  font-weight: 500;
  color: #333;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

/* 状态标签样式 */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-in-progress {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-resolved {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

/* 操作按钮样式 */
.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;
  display: inline-flex;
  align-items: center;
}

.btn-icon {
  margin-right: 4px;
  font-size: 14px;
}

.reply-btn {
  background-color: #52c41a; /* 绿色，表示回复 */
  color: white;
}

.view-btn {
  background-color: #1890ff;
  color: white;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination-btns button {
  padding: 6px 12px;
  margin-left: 8px;
  border: 1px solid #d9d9d9;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
}

.pagination-btns button:disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

/* 详情样式 */
.detail-item {
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 500;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.detail-content {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-line;
}

/* 表单样式 */
.status-form {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed #e8e8e8;
}

.status-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #666;
}

.form-item select, .form-item textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.form-btns {
  display: flex;
  justify-content: flex-end;
}

.save-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 回复弹窗样式 */
.reply-textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 100px; /* 确保文本区域有足够高度 */
  resize: vertical; /* 允许垂直调整大小 */
}

/* 删除确认弹窗样式 */
.delete-confirm .modal-body {
  text-align: center;
}

.confirm-btns {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #ff4d4f;
  color: white;
}
</style>
