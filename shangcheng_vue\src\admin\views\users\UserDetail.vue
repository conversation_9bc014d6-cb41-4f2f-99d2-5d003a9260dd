<template>
  <div class="user-detail-container">
    <div class="page-header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>用户详情</h2>
      </div>
      <div class="header-actions">
        <el-button 
          v-if="userDetail && userDetail.isActive" 
          type="danger" 
          @click="handleDisableUser"
        >
          禁用账户
        </el-button>
        <el-button 
          v-else-if="userDetail" 
          type="success" 
          @click="handleEnableUser"
        >
          启用账户
        </el-button>
      </div>
    </div>
    
    <div class="detail-content" v-loading="loading">
      <template v-if="userDetail">
        <!-- 基本信息卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h3>基本信息</h3>
            </div>
          </template>
          
          <div class="user-profile">
            <div class="avatar-container">
              <el-avatar 
                :size="100" 
                :src="userDetail.avatarUrl || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" 
              />
            </div>
            
            <div class="info-container">
              <div class="info-row">
                <span class="info-label">用户ID：</span>
                <span class="info-value">{{ userDetail.userId }}</span>
              </div>
              
              <div class="info-row">
                <span class="info-label">用户名：</span>
                <span class="info-value">{{ userDetail.username }}</span>
              </div>
              
              <div class="info-row" v-if="userDetail.email">
                <span class="info-label">邮箱：</span>
                <span class="info-value">{{ userDetail.email }}</span>
              </div>
              
              <div class="info-row" v-if="userDetail.phoneNumber">
                <span class="info-label">手机号：</span>
                <span class="info-value">{{ userDetail.phoneNumber }}</span>
              </div>
              
              <div class="info-row">
                <span class="info-label">角色：</span>
                <span class="info-value">
                  <el-tag v-if="userDetail.role === 'general_user'" type="success">普通用户</el-tag>
                  <el-tag v-else-if="userDetail.role === 'ai_customer_service'" type="warning">AI客服</el-tag>
                  <el-tag v-else type="info">{{ userDetail.role }}</el-tag>
                </span>
              </div>
              
              <div class="info-row">
                <span class="info-label">状态：</span>
                <span class="info-value">
                  <el-tag v-if="userDetail.isActive" type="success">正常</el-tag>
                  <el-tag v-else type="danger">禁用</el-tag>
                </span>
              </div>
              
              <div class="info-row">
                <span class="info-label">信用分：</span>
                <span class="info-value">{{ userDetail.creditScore }}</span>
              </div>
              
              <div class="info-row">
                <span class="info-label">注册时间：</span>
                <span class="info-value">{{ formatDateTime(userDetail.registrationDate) }}</span>
              </div>
              
              <div class="info-row">
                <span class="info-label">最后登录：</span>
                <span class="info-value">{{ userDetail.lastLoginDate ? formatDateTime(userDetail.lastLoginDate) : '从未登录' }}</span>
              </div>
            </div>
          </div>
          
          <div class="bio-section" v-if="userDetail.bio">
            <div class="info-row">
              <span class="info-label">个人简介：</span>
            </div>
            <div class="bio-content">{{ userDetail.bio }}</div>
          </div>
          
          <div class="location-section" v-if="userDetail.location">
            <div class="info-row">
              <span class="info-label">所在地区：</span>
              <span class="info-value">{{ userDetail.location }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 统计数据卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h3>统计数据</h3>
            </div>
          </template>
          
          <div class="stats-container">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ userDetail.productCount }}</div>
                  <div class="stat-label">发布商品</div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ userDetail.soldProductCount }}</div>
                  <div class="stat-label">已售出</div>
                </div>
              </el-col>
              
            </el-row>
            
            <el-divider />
            
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ userDetail.buyOrderCount }}</div>
                  <div class="stat-label">购买订单</div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ userDetail.sellOrderCount }}</div>
                  <div class="stat-label">销售订单</div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-value">{{ userDetail.forumPostCount }}</div>
                  <div class="stat-label">论坛发帖</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </template>
      
      <div v-else-if="!loading" class="no-data">
        <el-empty description="未找到用户数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserDetail, updateUserStatus } from '@/admin/api/users'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const userDetail = ref(null)

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

// 获取用户详情
const fetchUserDetail = async () => {
  const userId = route.params.userId
  if (!userId) {
    ElMessage.error('用户ID不能为空')
    return
  }
  
  loading.value = true
  try {
    const res = await getUserDetail(userId)
    if (res.code === 200) {
      userDetail.value = res.data
    } else {
      ElMessage.error(res.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 禁用用户
const handleDisableUser = () => {
  if (!userDetail.value) return
  
  ElMessageBox.confirm(`确定要禁用用户 "${userDetail.value.username}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await updateUserStatus({
        userId: userDetail.value.userId,
        isActive: false
      })
      
      if (res.code === 200) {
        ElMessage.success('禁用用户成功')
        userDetail.value.isActive = false
      } else {
        ElMessage.error(res.message || '禁用用户失败')
      }
    } catch (error) {
      console.error('禁用用户失败:', error)
      ElMessage.error('禁用用户失败')
    }
  }).catch(() => {})
}

// 启用用户
const handleEnableUser = () => {
  if (!userDetail.value) return
  
  ElMessageBox.confirm(`确定要启用用户 "${userDetail.value.username}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(async () => {
    try {
      const res = await updateUserStatus({
        userId: userDetail.value.userId,
        isActive: true
      })
      
      if (res.code === 200) {
        ElMessage.success('启用用户成功')
        userDetail.value.isActive = true
      } else {
        ElMessage.error(res.message || '启用用户失败')
      }
    } catch (error) {
      console.error('启用用户失败:', error)
      ElMessage.error('启用用户失败')
    }
  }).catch(() => {})
}

// 初始化
onMounted(() => {
  fetchUserDetail()
})
</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 0 15px;
  font-size: 24px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
}

.user-profile {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.info-container {
  flex: 1;
}

.info-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.info-label {
  width: 100px;
  color: #606266;
  font-weight: bold;
}

.info-value {
  color: #303133;
}

.bio-content {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  color: #606266;
  white-space: pre-wrap;
}

.stats-container {
  padding: 10px 0;
}

.stat-item {
  text-align: center;
  padding: 15px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  margin-top: 5px;
  color: #606266;
}

.no-data {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    flex-direction: column;
    align-items: center;
  }
  
  .avatar-container {
    margin-bottom: 20px;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    width: 100%;
    margin-bottom: 5px;
  }
}
</style> 