package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.ImportResultDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 批量导入服务接口
 */
public interface ImportService {
    
    /**
     * 批量导入用户
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResultDTO importUsers(MultipartFile file);
    
    /**
     * 批量导入分类
     * 
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResultDTO importCategories(MultipartFile file);
    
    /**
     * 生成用户导入模板
     * 
     * @return Excel文件字节数组
     */
    byte[] generateUserTemplate();
    
    /**
     * 生成分类导入模板
     * 
     * @return Excel文件字节数组
     */
    byte[] generateCategoryTemplate();
}
