package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.ForumCategoryDTO;
import com.lzhshtp.shangcheng.dto.ForumCategoryRequest;
import com.lzhshtp.shangcheng.service.ForumCategoryService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 论坛分类控制器
 */
@RestController
@RequestMapping("/api/forum/categories")
public class ForumCategoryController {

    @Autowired
    private ForumCategoryService forumCategoryService;

    /**
     * 获取所有论坛分类
     *
     * @return 论坛分类列表
     */
    @GetMapping
    public ApiResponse<List<ForumCategoryDTO>> getAllCategories() {
        List<ForumCategoryDTO> categories = forumCategoryService.getAllCategories();
        return ApiResponse.success(categories);
    }

    /**
     * 根据ID获取论坛分类
     *
     * @param id 论坛分类ID
     * @return 论坛分类信息
     */
    @GetMapping("/{id}")
    public ApiResponse<ForumCategoryDTO> getCategoryById(@PathVariable Integer id) {
        ForumCategoryDTO category = forumCategoryService.getCategoryById(id);
        if (category == null) {
            return ApiResponse.fail("论坛分类不存在", 404);
        }
        return ApiResponse.success(category);
    }

    /**
     * 创建论坛分类
     *
     * @param request 论坛分类请求
     * @return 创建结果
     */
    @PostMapping
    public ApiResponse<ForumCategoryDTO> createCategory(@Valid @RequestBody ForumCategoryRequest request) {
        return forumCategoryService.createCategory(request);
    }

    /**
     * 更新论坛分类
     *
     * @param id 论坛分类ID
     * @param request 论坛分类请求
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public ApiResponse<ForumCategoryDTO> updateCategory(@PathVariable Integer id, @Valid @RequestBody ForumCategoryRequest request) {
        return forumCategoryService.updateCategory(id, request);
    }

    /**
     * 删除论坛分类
     *
     * @param id 论坛分类ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCategory(@PathVariable Integer id) {
        return forumCategoryService.deleteCategory(id);
    }
}
