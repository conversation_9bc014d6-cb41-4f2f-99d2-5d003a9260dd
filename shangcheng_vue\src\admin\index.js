import AdminLogin from './views/AdminLogin.vue'
import Dashboard from './views/Dashboard.vue'
import DashboardHome from './views/DashboardHome.vue'
import UserList from './views/users/UserList.vue'
import UserDetail from './views/users/UserDetail.vue'
import CategoryList from './views/categories/CategoryList.vue'
import OrderManagement from './views/orders/OrderManagement.vue'
import ForumCategoryManagement from './views/forum/ForumCategoryManagement.vue'
import ForumManagement from './views/forum/ForumManagement.vue'
import FeedbackList from './views/feedback/FeedbackList.vue'
import ProductAudit from './views/products/ProductAudit.vue'
import ProductList from './views/products/ProductList.vue'
import AutoAuditView from './views/audit/AutoAuditView.vue'
import ManualAuditView from './views/audit/ManualAuditView.vue'
import SecondReviewView from './views/audit/SecondReviewView.vue'
import AuditTestView from './views/audit/AuditTestView.vue'
import AdminManagement from './views/settings/AdminManagement.vue'
import ScheduledTasks from './views/ScheduledTasks.vue'
import KnowledgeBase from './views/KnowledgeBase.vue'
import SyncES from './views/SyncES.vue'
import RefundManagement from './views/refunds/RefundManagement.vue'
import VerificationManagement from './views/verifications/VerificationManagement.vue'

// 创建系统设置的占位组件
const SystemSettings = { template: '<div><h2>系统设置</h2><p>这里是系统设置页面内容</p></div>' }

// 管理员路由配置
const adminRoutes = [
  {
    path: '/admin',
    redirect: '/admin/dashboard'
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin
  },
  {
    path: '/admin/dashboard',
    component: Dashboard,
    meta: { requiresAdminAuth: true },
    children: [
      {
        path: '',
        name: 'AdminDashboardHome',
        component: DashboardHome,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/users',
        name: 'AdminUsers',
        component: UserList,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/users/:userId',
        name: 'AdminUserDetail',
        component: UserDetail,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/products',
        name: 'AdminProducts',
        component: ProductList,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/products/audit',
        name: 'AdminProductsAudit',
        component: ProductAudit,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/audit/auto',
        name: 'AdminAutoAudit',
        component: AutoAuditView,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/audit/manual',
        name: 'AdminManualAudit',
        component: ManualAuditView,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/audit/second-review',
        name: 'AdminSecondReview',
        component: SecondReviewView,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/audit/test',
        name: 'AdminAuditTest',
        component: AuditTestView,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/categories',
        name: 'AdminCategories',
        component: CategoryList,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/orders',
        name: 'AdminOrders',
        component: OrderManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/refunds',
        name: 'AdminRefunds',
        component: RefundManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/verifications',
        name: 'AdminVerifications',
        component: VerificationManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/feedback',
        name: 'AdminFeedback',
        component: FeedbackList,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/forum',
        name: 'AdminForum',
        component: ForumManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/forum-categories',
        name: 'AdminForumCategories',
        component: ForumCategoryManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/settings',
        name: 'AdminSettings',
        component: SystemSettings,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/admins',
        name: 'AdminManagement',
        component: AdminManagement,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/scheduled-tasks',
        name: 'ScheduledTasks',
        component: ScheduledTasks,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/knowledge',
        name: 'AdminKnowledge',
        component: KnowledgeBase,
        meta: { requiresAdminAuth: true }
      },
      {
        path: '/admin/sync-es',
        name: 'AdminSyncES',
        component: SyncES,
        meta: { requiresAdminAuth: true }
      }
    ]
  }
]

// 管理员路由守卫函数，将在主路由中使用
const adminRouteGuard = async (to, from, next, adminStore) => {
  // 检查路由是否需要管理员认证
  if (to.matched.some(record => record.meta.requiresAdminAuth)) {
    console.log('管理员路由守卫: 检测到需要管理员权限的路由', to.path);
    
    // 检查管理员是否已登录
    const adminToken = localStorage.getItem('adminToken')
    if (!adminToken) {
      console.log('管理员路由守卫: 未检测到管理员token，跳转到登录页');
      // 未登录则跳转到管理员登录页
      next({
        path: '/admin/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 已登录，验证管理员信息
    if (!adminStore.adminInfo) {
      console.log('管理员路由守卫: 无管理员信息，尝试获取');
      try {
        const adminInfo = await adminStore.fetchAdminInfo();
        
        // 验证是否为管理员
        if (adminInfo && adminInfo.role === 'admin') {
          console.log('管理员路由守卫: 验证成功，允许访问');
          next(); // 是管理员则放行
        } else {
          console.error('管理员路由守卫: 用户不是管理员');
          // 不是管理员，清除token并跳转到登录页
          adminStore.logout();
          next({
            path: '/admin/login',
            query: { redirect: to.fullPath }
          });
        }
      } catch (error) {
        console.error('管理员路由守卫: 验证管理员信息失败:', error);
        
        // 尝试从localStorage获取缓存的管理员信息
        try {
          const savedAdminInfoStr = localStorage.getItem('adminInfo');
          if (savedAdminInfoStr) {
            const savedAdminInfo = JSON.parse(savedAdminInfoStr);
            if (savedAdminInfo && savedAdminInfo.role === 'admin') {
              console.log('管理员路由守卫: 使用缓存的管理员信息，允许访问');
              adminStore.adminInfo = savedAdminInfo;
              next();
              return;
            }
          }
        } catch (e) {
          console.error('管理员路由守卫: 解析缓存的管理员信息失败', e);
        }
        
        // 如果还是失败，清除登录状态并跳转到登录页
        adminStore.logout();
        next({
          path: '/admin/login',
          query: { redirect: to.fullPath }
        });
      }
    } else {
      // 已有管理员信息，直接放行
      console.log('管理员路由守卫: 已有管理员信息，直接放行');
      next();
    }
  } else {
    next(); // 不需要认证的路由直接放行
  }
}

export { adminRoutes, adminRouteGuard } 