package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 热门帖子DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotPostDTO {
    private Long postId;                // 帖子ID
    private String title;               // 帖子标题
    private String categoryName;        // 分类名称
    private String authorName;          // 作者名称
    private Long viewCount;             // 浏览量
    private Long commentCount;          // 评论数
    private String postedAt;            // 发布时间
}
