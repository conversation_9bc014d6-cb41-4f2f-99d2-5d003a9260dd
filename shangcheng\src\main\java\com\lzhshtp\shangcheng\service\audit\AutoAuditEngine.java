package com.lzhshtp.shangcheng.service.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.mapper.AutoAuditRecordMapper;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自动审核引擎
 */
@Slf4j
@Service
public class AutoAuditEngine {

    @Autowired
    private TextAuditService textAuditService;

    @Autowired
    private CreditScoreAuditService creditScoreAuditService;

    @Autowired
    private ImageAuditService imageAuditService;

    @Autowired
    private AutoAuditRecordMapper autoAuditRecordMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 执行自动审核 - 四个审核全部完成后统一判断
     */
    public AutoAuditRecord performAutoAudit(Product product) {
        log.info("开始自动审核，商品ID: {}", product.getId());

        AuditResultDTO textResult = null;
        AuditResultDTO imageResult = null;
        AuditResultDTO creditResult = null;
        AuditResultDTO priceResult = null;

        try {
            // 1. 文字审核 - 必须执行
            textResult = textAuditService.auditText(product);
            log.info("文字审核结果: {}", textResult.getReason());

            // 如果包含违规词，直接拒绝，不继续后续审核
            if (!textResult.getPassed() && textResult.getRiskLevel() == AuditConstants.RiskLevel.HIGH) {
                return createAutoAuditRecord(product, textResult, null, null, null,
                    AuditConstants.AutoAuditDecision.AUTO_REJECT, "文字审核不通过：" + textResult.getReason());
            }

            // 2. 图片审核 - 必须执行
            imageResult = imageAuditService.auditImages(product);
            log.info("图片审核结果: {}", imageResult.getReason());

            // 如果图片违规，直接拒绝，不继续后续审核
            if (!imageResult.getPassed() && imageResult.getRiskLevel() == AuditConstants.RiskLevel.HIGH) {
                return createAutoAuditRecord(product, textResult, imageResult, null, null,
                    AuditConstants.AutoAuditDecision.AUTO_REJECT, "图片审核不通过：" + imageResult.getReason());
            }

            // 3. 信用分审核 - 必须执行
            creditResult = creditScoreAuditService.auditCreditScore(product.getSellerId());
            log.info("信用分审核结果: {}", creditResult.getReason());

            // 4. 价格审核 - 必须执行
            priceResult = checkPriceAudit(product);
            log.info("价格审核结果: {}", priceResult.getReason());

            // 所有四个审核都完成，现在统一判断最终结果
            return makeComprehensiveDecision(product, textResult, imageResult, creditResult, priceResult);

        } catch (Exception e) {
            log.error("自动审核异常，商品ID: {}", product.getId(), e);

            // 异常情况下进入人工审核，记录已完成的审核结果
            return createAutoAuditRecord(product, textResult, imageResult, creditResult, priceResult,
                AuditConstants.AutoAuditDecision.MANUAL_REVIEW, "系统异常，需要人工审核");
        }
    }



    /**
     * 综合判断最终审核结果
     */
    private AutoAuditRecord makeComprehensiveDecision(Product product,
                                                     AuditResultDTO textResult,
                                                     AuditResultDTO imageResult,
                                                     AuditResultDTO creditResult,
                                                     AuditResultDTO priceResult) {

        List<String> manualReviewReasons = new ArrayList<>();
        List<String> autoRejectReasons = new ArrayList<>();

        // 检查是否有需要直接拒绝的情况
        if (!textResult.getPassed() && textResult.getRiskLevel() == AuditConstants.RiskLevel.HIGH) {
            autoRejectReasons.add("文字审核：" + textResult.getReason());
        } else if (!textResult.getPassed()) {
            manualReviewReasons.add("文字审核：" + textResult.getReason());
        }

        if (!imageResult.getPassed() && imageResult.getRiskLevel() == AuditConstants.RiskLevel.HIGH) {
            autoRejectReasons.add("图片审核：" + imageResult.getReason());
        } else if (!imageResult.getPassed()) {
            manualReviewReasons.add("图片审核：" + imageResult.getReason());
        }

        // 信用分过低直接拒绝
        if (!creditResult.getPassed() && creditResult.getScore() >= 80) {
            autoRejectReasons.add("信用分审核：" + creditResult.getReason());
        } else if (!creditResult.getPassed()) {
            manualReviewReasons.add("信用分审核：" + creditResult.getReason());
        }

        // 价格异常严重的直接拒绝
        if (!priceResult.getPassed() && priceResult.getScore() >= 90) {
            autoRejectReasons.add("价格审核：" + priceResult.getReason());
        } else if (!priceResult.getPassed()) {
            manualReviewReasons.add("价格审核：" + priceResult.getReason());
        }

        // 决策逻辑
        AuditConstants.AutoAuditDecision decision;
        String decisionReason;

        if (!autoRejectReasons.isEmpty()) {
            // 有严重违规，直接拒绝并删除
            decision = AuditConstants.AutoAuditDecision.AUTO_REJECT;
            decisionReason = "严重违规，自动拒绝：" + String.join("；", autoRejectReasons);
        } else if (!manualReviewReasons.isEmpty()) {
            // 有疑点，需要人工审核
            decision = AuditConstants.AutoAuditDecision.MANUAL_REVIEW;
            decisionReason = "需要人工审核：" + String.join("；", manualReviewReasons);
        } else {
            // 全部通过
            decision = AuditConstants.AutoAuditDecision.AUTO_APPROVE;
            decisionReason = "自动审核通过";
        }

        return createAutoAuditRecord(product, textResult, imageResult, creditResult, priceResult,
            decision, decisionReason);
    }

    /**
     * 创建自动审核记录
     */
    private AutoAuditRecord createAutoAuditRecord(Product product,
                                                 AuditResultDTO textResult,
                                                 AuditResultDTO imageResult,
                                                 AuditResultDTO creditResult,
                                                 AuditResultDTO priceResult,
                                                 AuditConstants.AutoAuditDecision decision,
                                                 String decisionReason) {

        AutoAuditRecord record = AutoAuditRecord.builder()
            .productId(product.getId())
            .textAuditResult(toJsonString(textResult))
            .imageAuditResult(toJsonString(imageResult))
            .creditAuditResult(toJsonString(creditResult))
            .priceAuditResult(toJsonString(priceResult))
            .finalDecision(decision.getCode())
            .decisionReason(decisionReason)
            .riskScore(BigDecimal.ZERO) // 线性审核不需要评分
            .createdTime(LocalDateTime.now())
            .build();

        // 保存到数据库
        autoAuditRecordMapper.insert(record);

        log.info("自动审核完成，商品ID: {}, 决策: {}, 原因: {}",
            product.getId(), decision.getDescription(), decisionReason);

        return record;
    }

    /**
     * 价格审核（简单实现）
     */
    private AuditResultDTO checkPriceAudit(Product product) {
        if (product.getPrice() == null) {
            return AuditResultDTO.fail(AuditConstants.RiskLevel.MEDIUM, 30, "商品价格为空");
        }

        // 简单的价格检查：价格过高检测
        if (product.getPrice().compareTo(new BigDecimal("5000")) > 0) {
            Map<String, Object> details = new HashMap<>();
            details.put("price", product.getPrice());

            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(40)
                .reason("商品价格过高（" + product.getPrice() + "元），需要人工审核")
                .details(details)
                .build();
        }

        return AuditResultDTO.pass("price_audit");
    }

    /**
     * 获取商品的自动审核记录
     */
    public AutoAuditRecord getAutoAuditRecord(Long productId) {
        return autoAuditRecordMapper.findByProductId(productId);
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("转换JSON失败", e);
            return null;
        }
    }
}
