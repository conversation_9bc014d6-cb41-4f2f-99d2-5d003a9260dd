package com.lzhshtp.shangcheng.dto;

import lombok.Data;

/**
 * 用户查询参数
 */
@Data
public class UserQueryParams {
    /**
     * 搜索关键词，匹配用户名、ID、手机号或邮箱
     */
    private String keyword;
    
    /**
     * 用户角色筛选
     */
    private String role;
    
    /**
     * 用户状态筛选
     */
    private Boolean isActive;
    
    /**
     * 页码，默认为1
     */
    private Integer page = 1;
    
    /**
     * 每页大小，默认为10
     */
    private Integer pageSize = 10;
    
    /**
     * 获取分页的偏移量
     */
    public int getOffset() {
        return (page - 1) * pageSize;
    }
} 