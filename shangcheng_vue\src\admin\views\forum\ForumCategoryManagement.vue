<template>
  <div class="forum-category-management">
    <div class="page-header">
      <h2 class="page-title">论坛分类管理</h2>
      <button class="add-btn" @click="showAddDialog">添加分类</button>
    </div>

    <!-- 搜索框 -->
    <div class="search-container">
      <div class="search-box">
        <input 
          v-model="searchKeyword" 
          type="text" 
          placeholder="搜索分类名称" 
          @keyup.enter="filterCategories"
        >
        <button class="search-btn" @click="filterCategories">搜索</button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="displayCategories.length === 0" class="empty-state">
        <div class="empty-icon">📂</div>
        <p>暂无分类数据</p>
      </div>
      
      <!-- 分类表格 -->
      <table v-else class="data-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>分类名称</th>
            <th>描述</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="category in displayCategories" :key="category.forumCategoryId">
            <td>{{ category.forumCategoryId }}</td>
            <td>{{ category.categoryName }}</td>
            <td>{{ category.description || '无描述' }}</td>
            <td class="actions">
              <button class="action-btn edit-btn" @click="showEditDialog(category)">编辑</button>
              <button class="action-btn delete-btn" @click="confirmDelete(category)">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 新增/编辑分类对话框 -->
    <div v-if="dialogVisible" class="dialog-backdrop" @click="closeDialogOnBackdrop">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ isEdit ? '编辑分类' : '新增分类' }}</h3>
          <button class="close-btn" @click="dialogVisible = false">&times;</button>
        </div>
        
        <div class="dialog-body">
          <div class="form-group">
            <label for="categoryName">分类名称<span class="required">*</span></label>
            <input 
              id="categoryName"
              v-model="formData.categoryName" 
              type="text" 
              placeholder="请输入分类名称"
              :class="{ 'input-error': errors.categoryName }"
            >
            <div v-if="errors.categoryName" class="error-message">{{ errors.categoryName }}</div>
          </div>
          
          <div class="form-group">
            <label for="description">分类描述</label>
            <textarea 
              id="description"
              v-model="formData.description" 
              placeholder="请输入分类描述"
              rows="4"
            ></textarea>
          </div>
        </div>
        
        <div class="dialog-footer">
          <button class="cancel-btn" @click="dialogVisible = false">取消</button>
          <button 
            class="confirm-btn" 
            @click="submitForm" 
            :disabled="formSubmitting"
          >
            {{ formSubmitting ? '提交中...' : '确认' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="deleteDialogVisible" class="dialog-backdrop" @click="closeDialogOnBackdrop">
      <div class="dialog delete-dialog" @click.stop>
        <div class="dialog-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="deleteDialogVisible = false">&times;</button>
        </div>
        
        <div class="dialog-body">
          <p class="delete-message">
            确定要删除分类 <strong>{{ currentCategory?.categoryName }}</strong> 吗？
          </p>
          <p class="delete-warning">
            警告：删除后不可恢复，如果该分类下有帖子，可能会影响相关功能。
          </p>
        </div>
        
        <div class="dialog-footer">
          <button class="cancel-btn" @click="deleteDialogVisible = false">取消</button>
          <button 
            class="delete-confirm-btn" 
            @click="deleteCategory" 
            :disabled="deleteSubmitting"
          >
            {{ deleteSubmitting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message.show" class="message-toast" :class="message.type">
      {{ message.content }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  getAllCategories, 
  createCategory, 
  updateCategory, 
  deleteCategory as deleteCategoryApi 
} from '@/admin/api/forumCategories'

// 数据状态
const categories = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// 对话框状态
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const isEdit = ref(false)
const currentCategory = ref(null)
const formSubmitting = ref(false)
const deleteSubmitting = ref(false)

// 表单数据
const formData = reactive({
  categoryName: '',
  description: ''
})

// 表单错误
const errors = reactive({
  categoryName: ''
})

// 消息提示
const message = reactive({
  show: false,
  content: '',
  type: 'success'
})

// 过滤后的分类列表
const displayCategories = computed(() => {
  if (!searchKeyword.value) return categories.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return categories.value.filter(item => 
    item.categoryName.toLowerCase().includes(keyword) || 
    (item.description && item.description.toLowerCase().includes(keyword))
  )
})

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const res = await getAllCategories()
    if (res.code === 200) {
      categories.value = res.data
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('获取论坛分类失败:', error)
    showMessage('获取论坛分类失败，请刷新重试', 'error')
  } finally {
    loading.value = false
  }
}

// 过滤分类
const filterCategories = () => {
  // 搜索功能通过计算属性实现，这里只是为了触发搜索按钮点击的操作感
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (category) => {
  isEdit.value = true
  currentCategory.value = category
  formData.categoryName = category.categoryName
  formData.description = category.description || ''
  dialogVisible.value = true
}

// 确认删除
const confirmDelete = (category) => {
  currentCategory.value = category
  deleteDialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  formData.categoryName = ''
  formData.description = ''
  errors.categoryName = ''
}

// 验证表单
const validateForm = () => {
  let valid = true
  
  // 验证分类名称
  if (!formData.categoryName.trim()) {
    errors.categoryName = '分类名称不能为空'
    valid = false
  } else if (formData.categoryName.length > 50) {
    errors.categoryName = '分类名称不能超过50个字符'
    valid = false
  } else {
    errors.categoryName = ''
  }
  
  return valid
}

// 提交表单
const submitForm = async () => {
  if (!validateForm()) return
  
  formSubmitting.value = true
  try {
    let res
    
    if (isEdit.value) {
      // 编辑分类
      res = await updateCategory(currentCategory.value.forumCategoryId, formData)
    } else {
      // 新增分类
      res = await createCategory(formData)
    }
    
    if (res.code === 200) {
      showMessage(isEdit.value ? '更新分类成功' : '新增分类成功', 'success')
      dialogVisible.value = false
      fetchCategories() // 刷新列表
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error(isEdit.value ? '更新分类失败:' : '新增分类失败:', error)
    showMessage(isEdit.value ? '更新分类失败' : '新增分类失败', 'error')
  } finally {
    formSubmitting.value = false
  }
}

// 删除分类
const deleteCategory = async () => {
  if (!currentCategory.value) return
  
  deleteSubmitting.value = true
  try {
    const res = await deleteCategoryApi(currentCategory.value.forumCategoryId)
    
    if (res.code === 200) {
      showMessage('删除分类成功', 'success')
      deleteDialogVisible.value = false
      fetchCategories() // 刷新列表
    } else {
      showMessage(res.message, 'error')
    }
  } catch (error) {
    console.error('删除分类失败:', error)
    showMessage('删除分类失败', 'error')
  } finally {
    deleteSubmitting.value = false
  }
}

// 在背景点击时关闭对话框
const closeDialogOnBackdrop = (e) => {
  if (e.target.classList.contains('dialog-backdrop')) {
    dialogVisible.value = false
    deleteDialogVisible.value = false
  }
}

// 显示消息提示
const showMessage = (content, type = 'success') => {
  message.content = content
  message.type = type
  message.show = true
  
  // 3秒后自动关闭
  setTimeout(() => {
    message.show = false
  }, 3000)
}

// 初始化
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
.forum-category-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 500px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.add-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-btn:hover {
  background-color: #40a9ff;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  max-width: 400px;
}

.search-box input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.search-box input:focus {
  border-color: #1890ff;
}

.search-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-btn:hover {
  background-color: #40a9ff;
}

/* 表格样式 */
.table-container {
  margin-bottom: 20px;
  min-height: 300px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.data-table th {
  background-color: #fafafa;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
}

.edit-btn {
  background-color: #1890ff;
  color: white;
}

.edit-btn:hover {
  background-color: #40a9ff;
}

.delete-btn {
  background-color: #ff4d4f;
  color: white;
}

.delete-btn:hover {
  background-color: #ff7875;
}

/* 对话框样式 */
.dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background-color: #fff;
  border-radius: 4px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #666;
}

.dialog-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
}

.required {
  color: #ff4d4f;
  margin-left: 4px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  outline: none;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #1890ff;
}

.input-error {
  border-color: #ff4d4f !important;
}

.error-message {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

.dialog-footer {
  padding: 10px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  padding: 7px 15px;
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
}

.cancel-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.confirm-btn {
  padding: 7px 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn:hover {
  background-color: #40a9ff;
}

.confirm-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

/* 删除对话框样式 */
.delete-dialog {
  max-width: 450px;
}

.delete-message {
  margin-top: 0;
  font-size: 15px;
}

.delete-warning {
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 0;
}

.delete-confirm-btn {
  padding: 7px 15px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.delete-confirm-btn:hover {
  background-color: #ff7875;
}

.delete-confirm-btn:disabled {
  background-color: #aaa;
  cursor: not-allowed;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1001;
  animation: fadeInOut 3s ease-in-out;
}

.message-toast.success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.message-toast.error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-10px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .add-btn {
    width: 100%;
  }
  
  .search-box {
    max-width: 100%;
  }
  
  .data-table th,
  .data-table td {
    padding: 10px;
  }
  
  .actions {
    flex-direction: column;
    gap: 5px;
  }
  
  .action-btn {
    width: 100%;
  }
}
</style> 