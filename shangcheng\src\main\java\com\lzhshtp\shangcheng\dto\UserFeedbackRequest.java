package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 用户反馈/举报请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackRequest {
    
    /**
     * 反馈类型: bug_report(问题反馈), suggestion(建议), complaint(投诉), abuse_report(举报)
     */
    @NotBlank(message = "反馈类型不能为空")
    private String feedbackType;
    
    /**
     * 如果是举报，则指明举报对象类型（例如 'product', 'user', 'post'）
     */
    private String relatedEntityType;
    
    /**
     * 关联的实体ID（例如：举报商品则为product_id）
     */
    private Long relatedEntityId;
    
    /**
     * 反馈或举报的详细内容
     */
    @NotBlank(message = "反馈内容不能为空")
    @Size(min = 10, max = 1000, message = "反馈内容长度应在10-1000字符之间")
    private String content;
} 