<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 二手交易平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FF4D4F; /* Red */
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
            --shadow-light: rgba(0,0,0,0.05);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 16px;
        }

        /* Top Nav - Reused */
        .top-nav {
            background: var(--white);
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .top-nav .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }
        .top-nav .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }
        .top-nav .nav-links a {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            margin-left: 20px;
            transition: color 0.2s ease;
        }
        .top-nav .nav-links a:hover { color: var(--primary-color); }

        /* Main Profile Layout */
        .profile-wrapper {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .sidebar {
            flex-shrink: 0;
            width: 250px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            padding: 20px 0;
        }

        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        .sidebar-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
            border: 3px solid var(--primary-color);
        }
        .sidebar-header h2 { font-size: 20px; font-weight: 500; margin-bottom: 4px; }
        .sidebar-header p { font-size: 14px; color: var(--text-color-light); }

        .sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .sidebar-nav li a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            transition: all 0.2s ease;
            border-left: 4px solid transparent;
        }
        .sidebar-nav li a:hover,
        .sidebar-nav li a.active {
            background-color: #FFF1F0;
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        .sidebar-nav svg {
            width: 20px;
            height: 20px;
        }

        .profile-content {
            flex-grow: 1;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            padding: 30px;
        }

        .content-section-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
        }

        /* Placeholder for content */
        .placeholder-content {
            background-color: var(--bg-color);
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            color: var(--text-color-light);
            font-size: 16px;
        }

        /* Responsive */
        @media (max-width: 900px) {
            .profile-wrapper { flex-direction: column; }
            .sidebar { width: 100%; padding: 0; margin-bottom: 20px; }
            .sidebar-nav ul { display: flex; flex-wrap: wrap; justify-content: center; }
            .sidebar-nav li { width: 50%; /* Adjust as needed */ }
            .sidebar-nav li a { justify-content: center; border-left: none; border-bottom: 3px solid transparent; }
            .sidebar-nav li a:hover, .sidebar-nav li a.active { border-bottom-color: var(--primary-color); }
            .profile-content { padding: 20px; }
            .content-section-title { font-size: 20px; }
        }

        @media (max-width: 600px) {
            .sidebar-nav li { width: 100%; }
            .sidebar-nav li a { justify-content: flex-start; padding: 12px 16px; }
        }
    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="nav-content">
            <a href="main.html" class="logo">二手交易</a>
            <div class="nav-links">
                <a href="main.html">浏览商品</a>
                <a href="forum.html">论坛</a>
                <a href="#">消息</a>
                <a href="#">客服</a>
                <a href="login.html">登录/注册</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="profile-wrapper">
            <aside class="sidebar">
                <div class="sidebar-header">
                    <img src="https://via.placeholder.com/80/FF6347/FFFFFF?text=User" alt="用户头像">
                    <h2>用户名</h2>
                    <p>ID: 12345678</p>
                </div>
                <nav class="sidebar-nav">
                    <ul>
                        <li><a href="#" class="active" data-section="my-products">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>
                            我的发布
                        </a></li>
                        <li><a href="#" data-section="my-orders">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg>
                            我的订单
                        </a></li>
                        <li><a href="#" data-section="my-favorites">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>
                            我的收藏
                        </a></li>
                        <li><a href="#" data-section="account-settings">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0-.33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1.51-1V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
                            账户设置
                        </a></li>
                        <li><a href="#" data-section="message-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                            消息中心
                        </a></li>
                    </ul>
                </nav>
            </aside>

            <main class="profile-content" id="profileContent">
                <div id="my-products-section">
                    <h2 class="content-section-title">我的发布</h2>
                    <div class="placeholder-content">
                        <p>您还没有发布任何商品。</p>
                        <p>点击这里 <a href="publish-product.html" style="color: var(--primary-color); text-decoration: underline;">发布您的第一个商品</a>！</p>
                    </div>
                </div>
                
                <div id="my-orders-section" style="display: none;">
                    <h2 class="content-section-title">我的订单</h2>
                    <div class="placeholder-content">
                        <p>您还没有任何订单。</p>
                    </div>
                </div>

                <div id="my-favorites-section" style="display: none;">
                    <h2 class="content-section-title">我的收藏</h2>
                    <div class="placeholder-content">
                        <p>您还没有收藏任何商品或帖子。</p>
                    </div>
                </div>

                <div id="account-settings-section" style="display: none;">
                    <h2 class="content-section-title">账户设置</h2>
                    <div class="placeholder-content">
                        <p>这里将是您的账户设置内容。</p>
                    </div>
                </div>

                <div id="message-center-section" style="display: none;">
                    <h2 class="content-section-title">消息中心</h2>
                    <div class="placeholder-content">
                        <p>这里将显示您的消息。</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
            const profileContent = document.getElementById('profileContent');

            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Remove active class from all links
                    sidebarLinks.forEach(item => item.classList.remove('active'));

                    // Add active class to clicked link
                    e.target.classList.add('active');

                    // Hide all content sections
                    profileContent.querySelectorAll('div[id$="-section"]').forEach(section => {
                        section.style.display = 'none';
                    });

                    // Show the selected content section
                    const targetSectionId = e.target.dataset.section + '-section';
                    document.getElementById(targetSectionId).style.display = 'block';
                });
            });
        });
    </script>
</body>
</html> 