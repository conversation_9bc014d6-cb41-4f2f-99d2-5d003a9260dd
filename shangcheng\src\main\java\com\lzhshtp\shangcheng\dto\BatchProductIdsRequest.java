package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchProductIdsRequest {
    @NotEmpty(message = "商品ID列表不能为空")
    @Size(min = 1, max = 100, message = "一次最多处理100个商品")
    private List<Long> productIds;
} 