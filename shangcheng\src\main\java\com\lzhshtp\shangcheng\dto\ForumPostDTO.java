package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 论坛帖子数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForumPostDTO {
    
    /**
     * 帖子ID
     */
    private Long postId;
    
    /**
     * 帖子标题
     */
    private String title;
    
    /**
     * 帖子内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者名称
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 发布时间
     */
    private LocalDateTime postedAt;
    
    /**
     * 所属分类ID
     */
    private Integer forumCategoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 浏览量
     */
    private Integer viewsCount;
    
    /**
     * 是否置顶 0-否 1-是
     */
    private Integer isPinned;
    
    /**
     * 帖子状态 0-已发布 1-下架
     */
    private Integer status;
    
    /**
     * 评论数量
     */
    private Integer commentCount;
} 