package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_product_categories")
public class Category {
    
    @TableId(value = "lzhshtp_category_id", type = IdType.AUTO)
    private Integer id;
    
    @TableField("lzhshtp_category_name")
    private String name;
    
    @TableField("lzhshtp_description")
    private String description;
    
    @TableField("lzhshtp_parent_category_id")
    private Integer parentId;
} 