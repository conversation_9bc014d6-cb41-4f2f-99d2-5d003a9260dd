{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/haskell.js"], "sourcesContent": ["function switchState(source, setState, f) {\n  setState(f);\n  return f(source, setState);\n}\n\n// These should all be Unicode extended, as per the Haskell 2010 report\nvar smallRE = /[a-z_]/;\nvar largeRE = /[A-Z]/;\nvar digitRE = /\\d/;\nvar hexitRE = /[0-9A-Fa-f]/;\nvar octitRE = /[0-7]/;\nvar idRE = /[a-z_A-Z0-9'\\xa1-\\uffff]/;\nvar symbolRE = /[-!#$%&*+.\\/<=>?@\\\\^|~:]/;\nvar specialRE = /[(),;[\\]`{}]/;\nvar whiteCharRE = /[ \\t\\v\\f]/; // newlines are handled in tokenizer\n\nfunction normal(source, setState) {\n  if (source.eatWhile(whiteCharRE)) {\n    return null;\n  }\n\n  var ch = source.next();\n  if (specialRE.test(ch)) {\n    if (ch == '{' && source.eat('-')) {\n      var t = \"comment\";\n      if (source.eat('#')) {\n        t = \"meta\";\n      }\n      return switchState(source, setState, ncomment(t, 1));\n    }\n    return null;\n  }\n\n  if (ch == '\\'') {\n    if (source.eat('\\\\')) {\n      source.next();  // should handle other escapes here\n    }\n    else {\n      source.next();\n    }\n    if (source.eat('\\'')) {\n      return \"string\";\n    }\n    return \"error\";\n  }\n\n  if (ch == '\"') {\n    return switchState(source, setState, stringLiteral);\n  }\n\n  if (largeRE.test(ch)) {\n    source.eatWhile(idRE);\n    if (source.eat('.')) {\n      return \"qualifier\";\n    }\n    return \"type\";\n  }\n\n  if (smallRE.test(ch)) {\n    source.eatWhile(idRE);\n    return \"variable\";\n  }\n\n  if (digitRE.test(ch)) {\n    if (ch == '0') {\n      if (source.eat(/[xX]/)) {\n        source.eatWhile(hexitRE); // should require at least 1\n        return \"integer\";\n      }\n      if (source.eat(/[oO]/)) {\n        source.eatWhile(octitRE); // should require at least 1\n        return \"number\";\n      }\n    }\n    source.eatWhile(digitRE);\n    var t = \"number\";\n    if (source.match(/^\\.\\d+/)) {\n      t = \"number\";\n    }\n    if (source.eat(/[eE]/)) {\n      t = \"number\";\n      source.eat(/[-+]/);\n      source.eatWhile(digitRE); // should require at least 1\n    }\n    return t;\n  }\n\n  if (ch == \".\" && source.eat(\".\"))\n    return \"keyword\";\n\n  if (symbolRE.test(ch)) {\n    if (ch == '-' && source.eat(/-/)) {\n      source.eatWhile(/-/);\n      if (!source.eat(symbolRE)) {\n        source.skipToEnd();\n        return \"comment\";\n      }\n    }\n    source.eatWhile(symbolRE);\n    return \"variable\"\n  }\n\n  return \"error\";\n}\n\nfunction ncomment(type, nest) {\n  if (nest == 0) {\n    return normal;\n  }\n  return function(source, setState) {\n    var currNest = nest;\n    while (!source.eol()) {\n      var ch = source.next();\n      if (ch == '{' && source.eat('-')) {\n        ++currNest;\n      }\n      else if (ch == '-' && source.eat('}')) {\n        --currNest;\n        if (currNest == 0) {\n          setState(normal);\n          return type;\n        }\n      }\n    }\n    setState(ncomment(type, currNest));\n    return type;\n  };\n}\n\nfunction stringLiteral(source, setState) {\n  while (!source.eol()) {\n    var ch = source.next();\n    if (ch == '\"') {\n      setState(normal);\n      return \"string\";\n    }\n    if (ch == '\\\\') {\n      if (source.eol() || source.eat(whiteCharRE)) {\n        setState(stringGap);\n        return \"string\";\n      }\n      if (source.eat('&')) {\n      }\n      else {\n        source.next(); // should handle other escapes here\n      }\n    }\n  }\n  setState(normal);\n  return \"error\";\n}\n\nfunction stringGap(source, setState) {\n  if (source.eat('\\\\')) {\n    return switchState(source, setState, stringLiteral);\n  }\n  source.next();\n  setState(normal);\n  return \"error\";\n}\n\n\nvar wellKnownWords = (function() {\n  var wkw = {};\n  function setType(t) {\n    return function () {\n      for (var i = 0; i < arguments.length; i++)\n        wkw[arguments[i]] = t;\n    };\n  }\n\n  setType(\"keyword\")(\n    \"case\", \"class\", \"data\", \"default\", \"deriving\", \"do\", \"else\", \"foreign\",\n    \"if\", \"import\", \"in\", \"infix\", \"infixl\", \"infixr\", \"instance\", \"let\",\n    \"module\", \"newtype\", \"of\", \"then\", \"type\", \"where\", \"_\");\n\n  setType(\"keyword\")(\n    \"\\.\\.\", \":\", \"::\", \"=\", \"\\\\\", \"<-\", \"->\", \"@\", \"~\", \"=>\");\n\n  setType(\"builtin\")(\n    \"!!\", \"$!\", \"$\", \"&&\", \"+\", \"++\", \"-\", \".\", \"/\", \"/=\", \"<\", \"<*\", \"<=\",\n    \"<$>\", \"<*>\", \"=<<\", \"==\", \">\", \">=\", \">>\", \">>=\", \"^\", \"^^\", \"||\", \"*\",\n    \"*>\", \"**\");\n\n  setType(\"builtin\")(\n    \"Applicative\", \"Bool\", \"Bounded\", \"Char\", \"Double\", \"EQ\", \"Either\", \"Enum\",\n    \"Eq\", \"False\", \"FilePath\", \"Float\", \"Floating\", \"Fractional\", \"Functor\",\n    \"GT\", \"IO\", \"IOError\", \"Int\", \"Integer\", \"Integral\", \"Just\", \"LT\", \"Left\",\n    \"Maybe\", \"Monad\", \"Nothing\", \"Num\", \"Ord\", \"Ordering\", \"Rational\", \"Read\",\n    \"ReadS\", \"Real\", \"RealFloat\", \"RealFrac\", \"Right\", \"Show\", \"ShowS\",\n    \"String\", \"True\");\n\n  setType(\"builtin\")(\n    \"abs\", \"acos\", \"acosh\", \"all\", \"and\", \"any\", \"appendFile\", \"asTypeOf\",\n    \"asin\", \"asinh\", \"atan\", \"atan2\", \"atanh\", \"break\", \"catch\", \"ceiling\",\n    \"compare\", \"concat\", \"concatMap\", \"const\", \"cos\", \"cosh\", \"curry\",\n    \"cycle\", \"decodeFloat\", \"div\", \"divMod\", \"drop\", \"dropWhile\", \"either\",\n    \"elem\", \"encodeFloat\", \"enumFrom\", \"enumFromThen\", \"enumFromThenTo\",\n    \"enumFromTo\", \"error\", \"even\", \"exp\", \"exponent\", \"fail\", \"filter\",\n    \"flip\", \"floatDigits\", \"floatRadix\", \"floatRange\", \"floor\", \"fmap\",\n    \"foldl\", \"foldl1\", \"foldr\", \"foldr1\", \"fromEnum\", \"fromInteger\",\n    \"fromIntegral\", \"fromRational\", \"fst\", \"gcd\", \"getChar\", \"getContents\",\n    \"getLine\", \"head\", \"id\", \"init\", \"interact\", \"ioError\", \"isDenormalized\",\n    \"isIEEE\", \"isInfinite\", \"isNaN\", \"isNegativeZero\", \"iterate\", \"last\",\n    \"lcm\", \"length\", \"lex\", \"lines\", \"log\", \"logBase\", \"lookup\", \"map\",\n    \"mapM\", \"mapM_\", \"max\", \"maxBound\", \"maximum\", \"maybe\", \"min\", \"minBound\",\n    \"minimum\", \"mod\", \"negate\", \"not\", \"notElem\", \"null\", \"odd\", \"or\",\n    \"otherwise\", \"pi\", \"pred\", \"print\", \"product\", \"properFraction\", \"pure\",\n    \"putChar\", \"putStr\", \"putStrLn\", \"quot\", \"quotRem\", \"read\", \"readFile\",\n    \"readIO\", \"readList\", \"readLn\", \"readParen\", \"reads\", \"readsPrec\",\n    \"realToFrac\", \"recip\", \"rem\", \"repeat\", \"replicate\", \"return\", \"reverse\",\n    \"round\", \"scaleFloat\", \"scanl\", \"scanl1\", \"scanr\", \"scanr1\", \"seq\",\n    \"sequence\", \"sequence_\", \"show\", \"showChar\", \"showList\", \"showParen\",\n    \"showString\", \"shows\", \"showsPrec\", \"significand\", \"signum\", \"sin\",\n    \"sinh\", \"snd\", \"span\", \"splitAt\", \"sqrt\", \"subtract\", \"succ\", \"sum\",\n    \"tail\", \"take\", \"takeWhile\", \"tan\", \"tanh\", \"toEnum\", \"toInteger\",\n    \"toRational\", \"truncate\", \"uncurry\", \"undefined\", \"unlines\", \"until\",\n    \"unwords\", \"unzip\", \"unzip3\", \"userError\", \"words\", \"writeFile\", \"zip\",\n    \"zip3\", \"zipWith\", \"zipWith3\");\n\n  return wkw;\n})();\n\nexport const haskell = {\n  name: \"haskell\",\n  startState: function ()  { return { f: normal }; },\n  copyState:  function (s) { return { f: s.f }; },\n\n  token: function(stream, state) {\n    var t = state.f(stream, function(s) { state.f = s; });\n    var w = stream.current();\n    return wellKnownWords.hasOwnProperty(w) ? wellKnownWords[w] : t;\n  },\n\n  languageData: {\n    commentTokens: {line: \"--\", block: {open: \"{-\", close: \"-}\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,YAAY,QAAQ,UAAU,GAAG;AACxC,WAAS,CAAC;AACV,SAAO,EAAE,QAAQ,QAAQ;AAC3B;AAGA,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,cAAc;AAElB,SAAS,OAAO,QAAQ,UAAU;AAChC,MAAI,OAAO,SAAS,WAAW,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,UAAU,KAAK,EAAE,GAAG;AACtB,QAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,UAAI,IAAI;AACR,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAI;AAAA,MACN;AACA,aAAO,YAAY,QAAQ,UAAU,SAAS,GAAG,CAAC,CAAC;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAM;AACd,QAAI,OAAO,IAAI,IAAI,GAAG;AACpB,aAAO,KAAK;AAAA,IACd,OACK;AACH,aAAO,KAAK;AAAA,IACd;AACA,QAAI,OAAO,IAAI,GAAI,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK;AACb,WAAO,YAAY,QAAQ,UAAU,aAAa;AAAA,EACpD;AAEA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,WAAO,SAAS,IAAI;AACpB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,QAAI,MAAM,KAAK;AACb,UAAI,OAAO,IAAI,MAAM,GAAG;AACtB,eAAO,SAAS,OAAO;AACvB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,IAAI,MAAM,GAAG;AACtB,eAAO,SAAS,OAAO;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,OAAO;AACvB,QAAI,IAAI;AACR,QAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,UAAI;AAAA,IACN;AACA,QAAI,OAAO,IAAI,MAAM,GAAG;AACtB,UAAI;AACJ,aAAO,IAAI,MAAM;AACjB,aAAO,SAAS,OAAO;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG;AAC7B,WAAO;AAET,MAAI,SAAS,KAAK,EAAE,GAAG;AACrB,QAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,aAAO,SAAS,GAAG;AACnB,UAAI,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzB,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,MAAM,MAAM;AAC5B,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,SAAO,SAAS,QAAQ,UAAU;AAChC,QAAI,WAAW;AACf,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,KAAK,OAAO,KAAK;AACrB,UAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,UAAE;AAAA,MACJ,WACS,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACrC,UAAE;AACF,YAAI,YAAY,GAAG;AACjB,mBAAS,MAAM;AACf,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,aAAS,SAAS,MAAM,QAAQ,CAAC;AACjC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,QAAQ,UAAU;AACvC,SAAO,CAAC,OAAO,IAAI,GAAG;AACpB,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,KAAK;AACb,eAAS,MAAM;AACf,aAAO;AAAA,IACT;AACA,QAAI,MAAM,MAAM;AACd,UAAI,OAAO,IAAI,KAAK,OAAO,IAAI,WAAW,GAAG;AAC3C,iBAAS,SAAS;AAClB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,IAAI,GAAG,GAAG;AAAA,MACrB,OACK;AACH,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,WAAS,MAAM;AACf,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,UAAU;AACnC,MAAI,OAAO,IAAI,IAAI,GAAG;AACpB,WAAO,YAAY,QAAQ,UAAU,aAAa;AAAA,EACpD;AACA,SAAO,KAAK;AACZ,WAAS,MAAM;AACf,SAAO;AACT;AAGA,IAAI,iBAAkB,WAAW;AAC/B,MAAI,MAAM,CAAC;AACX,WAAS,QAAQ,GAAG;AAClB,WAAO,WAAY;AACjB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,YAAI,UAAU,CAAC,CAAC,IAAI;AAAA,IACxB;AAAA,EACF;AAEA,UAAQ,SAAS;AAAA,IACf;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAY;AAAA,IAAM;AAAA,IAAQ;AAAA,IAC9D;AAAA,IAAM;AAAA,IAAU;AAAA,IAAM;AAAA,IAAS;AAAA,IAAU;AAAA,IAAU;AAAA,IAAY;AAAA,IAC/D;AAAA,IAAU;AAAA,IAAW;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAS;AAAA,EAAG;AAEzD,UAAQ,SAAS;AAAA,IACf;AAAA,IAAQ;AAAA,IAAK;AAAA,IAAM;AAAA,IAAK;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAK;AAAA,IAAK;AAAA,EAAI;AAE1D,UAAQ,SAAS;AAAA,IACf;AAAA,IAAM;AAAA,IAAM;AAAA,IAAK;AAAA,IAAM;AAAA,IAAK;AAAA,IAAM;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAM;AAAA,IAAK;AAAA,IAAM;AAAA,IAClE;AAAA,IAAO;AAAA,IAAO;AAAA,IAAO;AAAA,IAAM;AAAA,IAAK;AAAA,IAAM;AAAA,IAAM;AAAA,IAAO;AAAA,IAAK;AAAA,IAAM;AAAA,IAAM;AAAA,IACpE;AAAA,IAAM;AAAA,EAAI;AAEZ,UAAQ,SAAS;AAAA,IACf;AAAA,IAAe;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAU;AAAA,IAAM;AAAA,IAAU;AAAA,IACpE;AAAA,IAAM;AAAA,IAAS;AAAA,IAAY;AAAA,IAAS;AAAA,IAAY;AAAA,IAAc;AAAA,IAC9D;AAAA,IAAM;AAAA,IAAM;AAAA,IAAW;AAAA,IAAO;AAAA,IAAW;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAM;AAAA,IACnE;AAAA,IAAS;AAAA,IAAS;AAAA,IAAW;AAAA,IAAO;AAAA,IAAO;AAAA,IAAY;AAAA,IAAY;AAAA,IACnE;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAY;AAAA,IAAS;AAAA,IAAQ;AAAA,IAC3D;AAAA,IAAU;AAAA,EAAM;AAElB,UAAQ,SAAS;AAAA,IACf;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAO;AAAA,IAAO;AAAA,IAAO;AAAA,IAAc;AAAA,IAC3D;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAAS;AAAA,IAC7D;AAAA,IAAW;AAAA,IAAU;AAAA,IAAa;AAAA,IAAS;AAAA,IAAO;AAAA,IAAQ;AAAA,IAC1D;AAAA,IAAS;AAAA,IAAe;AAAA,IAAO;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAa;AAAA,IAC9D;AAAA,IAAQ;AAAA,IAAe;AAAA,IAAY;AAAA,IAAgB;AAAA,IACnD;AAAA,IAAc;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAO;AAAA,IAAY;AAAA,IAAQ;AAAA,IAC1D;AAAA,IAAQ;AAAA,IAAe;AAAA,IAAc;AAAA,IAAc;AAAA,IAAS;AAAA,IAC5D;AAAA,IAAS;AAAA,IAAU;AAAA,IAAS;AAAA,IAAU;AAAA,IAAY;AAAA,IAClD;AAAA,IAAgB;AAAA,IAAgB;AAAA,IAAO;AAAA,IAAO;AAAA,IAAW;AAAA,IACzD;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAW;AAAA,IACxD;AAAA,IAAU;AAAA,IAAc;AAAA,IAAS;AAAA,IAAkB;AAAA,IAAW;AAAA,IAC9D;AAAA,IAAO;AAAA,IAAU;AAAA,IAAO;AAAA,IAAS;AAAA,IAAO;AAAA,IAAW;AAAA,IAAU;AAAA,IAC7D;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAO;AAAA,IAAY;AAAA,IAAW;AAAA,IAAS;AAAA,IAAO;AAAA,IAC/D;AAAA,IAAW;AAAA,IAAO;AAAA,IAAU;AAAA,IAAO;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAO;AAAA,IAC7D;AAAA,IAAa;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAW;AAAA,IAAkB;AAAA,IACjE;AAAA,IAAW;AAAA,IAAU;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAQ;AAAA,IAC5D;AAAA,IAAU;AAAA,IAAY;AAAA,IAAU;AAAA,IAAa;AAAA,IAAS;AAAA,IACtD;AAAA,IAAc;AAAA,IAAS;AAAA,IAAO;AAAA,IAAU;AAAA,IAAa;AAAA,IAAU;AAAA,IAC/D;AAAA,IAAS;AAAA,IAAc;AAAA,IAAS;AAAA,IAAU;AAAA,IAAS;AAAA,IAAU;AAAA,IAC7D;AAAA,IAAY;AAAA,IAAa;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAY;AAAA,IACzD;AAAA,IAAc;AAAA,IAAS;AAAA,IAAa;AAAA,IAAe;AAAA,IAAU;AAAA,IAC7D;AAAA,IAAQ;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAW;AAAA,IAAQ;AAAA,IAAY;AAAA,IAAQ;AAAA,IAC9D;AAAA,IAAQ;AAAA,IAAQ;AAAA,IAAa;AAAA,IAAO;AAAA,IAAQ;AAAA,IAAU;AAAA,IACtD;AAAA,IAAc;AAAA,IAAY;AAAA,IAAW;AAAA,IAAa;AAAA,IAAW;AAAA,IAC7D;AAAA,IAAW;AAAA,IAAS;AAAA,IAAU;AAAA,IAAa;AAAA,IAAS;AAAA,IAAa;AAAA,IACjE;AAAA,IAAQ;AAAA,IAAW;AAAA,EAAU;AAE/B,SAAO;AACT,EAAG;AAEI,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,YAAY,WAAa;AAAE,WAAO,EAAE,GAAG,OAAO;AAAA,EAAG;AAAA,EACjD,WAAY,SAAU,GAAG;AAAE,WAAO,EAAE,GAAG,EAAE,EAAE;AAAA,EAAG;AAAA,EAE9C,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,IAAI,MAAM,EAAE,QAAQ,SAAS,GAAG;AAAE,YAAM,IAAI;AAAA,IAAG,CAAC;AACpD,QAAI,IAAI,OAAO,QAAQ;AACvB,WAAO,eAAe,eAAe,CAAC,IAAI,eAAe,CAAC,IAAI;AAAA,EAChE;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": []}