package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.SensitiveWord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 敏感词库Mapper
 */
@Mapper
public interface SensitiveWordMapper extends BaseMapper<SensitiveWord> {
    
    /**
     * 根据类型查询敏感词列表
     * 支持的类型：banned(违禁词)、sensitive(敏感词)、brand(品牌词)、category(敏感类目)
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findByWordType(@Param("wordType") String wordType);

    /**
     * 查询所有启用的敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findAllActive();

    /**
     * 根据词查询敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    SensitiveWord findByWord(@Param("word") String word);

    /**
     * 根据类别查询敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findByCategory(@Param("category") String category);

    /**
     * 根据严重程度查询敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findBySeverityLevel(@Param("severityLevel") Integer severityLevel);

    /**
     * 模糊查询敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findByWordLike(@Param("keyword") String keyword);

    /**
     * 统计各类型敏感词数量
     * 实现在 SensitiveWordMapper.xml 中
     */
    Long countByWordType(@Param("wordType") String wordType);

    /**
     * 批量查询敏感词
     * 实现在 SensitiveWordMapper.xml 中
     */
    List<SensitiveWord> findByWords(@Param("words") List<String> words);
}
