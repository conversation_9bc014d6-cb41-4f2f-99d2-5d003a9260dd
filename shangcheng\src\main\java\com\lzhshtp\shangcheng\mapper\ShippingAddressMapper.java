package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.ShippingAddress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收货地址Mapper接口
 */
@Mapper
public interface ShippingAddressMapper extends BaseMapper<ShippingAddress> {
    
    /**
     * 根据用户ID获取所有收货地址
     * 
     * @param userId 用户ID
     * @return 收货地址列表
     */
    List<ShippingAddress> findByUserId(@Param("userId") Long userId);
    
    /**
     * 根据ID获取收货地址
     * 
     * @param addressId 收货地址ID
     * @return 收货地址
     */
    ShippingAddress findById(@Param("addressId") Long addressId);
    
    /**
     * 新增收货地址
     * 
     * @param address 收货地址
     * @return 影响的行数
     */
    int insert(ShippingAddress address);
    
    /**
     * 更新收货地址
     * 
     * @param address 收货地址
     * @return 影响的行数
     */
    int update(ShippingAddress address);
    
    /**
     * 删除收货地址
     * 
     * @param addressId 收货地址ID
     * @return 影响的行数
     */
    int deleteById(@Param("addressId") Long addressId);
    
    /**
     * 将用户的所有地址设置为非默认
     * 
     * @param userId 用户ID
     * @return 影响的行数
     */
    int resetDefaultAddress(@Param("userId") Long userId);
    
    /**
     * 设置默认地址
     * 
     * @param addressId 收货地址ID
     * @return 影响的行数
     */
    int setDefaultAddress(@Param("addressId") Long addressId);
    
    /**
     * 获取用户的默认地址
     * 
     * @param userId 用户ID
     * @return 默认收货地址
     */
    ShippingAddress findDefaultAddress(@Param("userId") Long userId);
    
    /**
     * 检查地址是否属于指定用户
     * 
     * @param addressId 收货地址ID
     * @param userId 用户ID
     * @return 如果地址属于该用户，则返回true
     */
    boolean isAddressBelongsToUser(@Param("addressId") Long addressId, @Param("userId") Long userId);
} 