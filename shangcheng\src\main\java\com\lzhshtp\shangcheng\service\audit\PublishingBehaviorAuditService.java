package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发布行为审核服务
 */
@Slf4j
@Service
public class PublishingBehaviorAuditService {
    
    @Autowired
    private ProductMapper productMapper;
    
    /**
     * 执行发布行为审核
     */
    public AuditResultDTO auditPublishingBehavior(Long userId) {
        log.info("开始发布行为审核，用户ID: {}", userId);
        
        // 1. 检查发布频率
        AuditResultDTO frequencyResult = checkPublishingFrequency(userId);
        if (!frequencyResult.getPassed()) {
            return frequencyResult;
        }
        
        // 2. 检查重复内容
        AuditResultDTO duplicateResult = checkDuplicateContent(userId);
        if (!duplicateResult.getPassed()) {
            return duplicateResult;
        }
        
        // 3. 检查发布时间模式
        AuditResultDTO timePatternResult = checkPublishingTimePattern(userId);
        if (!timePatternResult.getPassed()) {
            return timePatternResult;
        }
        
        log.info("发布行为审核通过，用户ID: {}", userId);
        return AuditResultDTO.pass("publishing_behavior");
    }
    
    /**
     * 检查发布频率
     */
    private AuditResultDTO checkPublishingFrequency(Long userId) {
        int todayCount = productMapper.countTodayPostsByUser(userId);
        int weekCount = productMapper.countWeekPostsByUser(userId);
        
        Map<String, Object> details = new HashMap<>();
        details.put("today_count", todayCount);
        details.put("week_count", weekCount);
        
        // 今日发布频率检测
        if (todayCount >= 20) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(80)
                .reason("单日发布商品过多（" + todayCount + "个），疑似批量发布或机器人行为")
                .details(details)
                .build();
        } else if (todayCount >= AuditConstants.MAX_DAILY_POSTS) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(50)
                .reason("单日发布商品较多（" + todayCount + "个），需要人工审核")
                .details(details)
                .build();
        } else if (todayCount >= 5) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.LOW)
                .score(20)
                .reason("单日发布商品偏多（" + todayCount + "个），需要关注")
                .details(details)
                .build();
        }
        
        // 一周发布频率检测
        if (weekCount >= 50) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(60)
                .reason("一周发布商品过多（" + weekCount + "个），可能是商业用户")
                .details(details)
                .build();
        } else if (weekCount >= AuditConstants.MAX_WEEKLY_POSTS) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.LOW)
                .score(25)
                .reason("一周发布商品较多（" + weekCount + "个），需要关注")
                .details(details)
                .build();
        }
        
        return AuditResultDTO.pass("publishing_frequency");
    }
    
    /**
     * 检查重复内容
     */
    private AuditResultDTO checkDuplicateContent(Long userId) {
        int duplicateTitleCount = productMapper.countDuplicateTitlesByUser(userId);
        int similarDescriptionCount = productMapper.countSimilarDescriptionsByUser(userId);
        
        Map<String, Object> details = new HashMap<>();
        details.put("duplicate_titles", duplicateTitleCount);
        details.put("similar_descriptions", similarDescriptionCount);
        
        if (duplicateTitleCount >= 5) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(70)
                .reason("发布重复标题商品过多（" + duplicateTitleCount + "个），疑似刷屏行为")
                .details(details)
                .build();
        } else if (duplicateTitleCount >= 3) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(40)
                .reason("发布重复标题商品较多（" + duplicateTitleCount + "个），需要审核")
                .details(details)
                .build();
        }
        
        if (similarDescriptionCount >= 10) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(45)
                .reason("发布相似描述商品过多（" + similarDescriptionCount + "个），内容质量可疑")
                .details(details)
                .build();
        }
        
        return AuditResultDTO.pass("duplicate_content");
    }
    
    /**
     * 检查发布时间模式
     */
    private AuditResultDTO checkPublishingTimePattern(Long userId) {
        List<LocalDateTime> recentPostTimes = productMapper.getRecentPostTimesByUser(userId, 20);
        
        if (recentPostTimes.size() < 5) {
            return AuditResultDTO.pass("publishing_time_pattern");
        }
        
        // 检测是否在很短时间内连续发布
        int rapidPostCount = 0;
        for (int i = 1; i < recentPostTimes.size(); i++) {
            LocalDateTime current = recentPostTimes.get(i);
            LocalDateTime previous = recentPostTimes.get(i - 1);
            
            // 如果两次发布间隔小于1分钟
            if (ChronoUnit.MINUTES.between(previous, current) < 1) {
                rapidPostCount++;
            }
        }
        
        Map<String, Object> details = new HashMap<>();
        details.put("rapid_posts", rapidPostCount);
        details.put("recent_posts_count", recentPostTimes.size());
        
        if (rapidPostCount >= 5) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(75)
                .reason("短时间内连续发布商品（" + rapidPostCount + "次间隔小于1分钟），疑似机器人行为")
                .details(details)
                .build();
        } else if (rapidPostCount >= 3) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(45)
                .reason("短时间内连续发布商品较多，行为异常")
                .details(details)
                .build();
        }
        
        // 检测是否只在深夜发布（可能是机器人）
        long nightPostCount = recentPostTimes.stream()
            .mapToInt(time -> time.getHour())
            .filter(hour -> hour >= 0 && hour <= 5) // 凌晨0-5点
            .count();
        
        if (nightPostCount >= recentPostTimes.size() * 0.8) { // 80%以上在深夜发布
            details.put("night_post_ratio", nightPostCount * 100 / recentPostTimes.size());
            
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(35)
                .reason("大部分商品在深夜发布，行为模式异常")
                .details(details)
                .build();
        }
        
        return AuditResultDTO.pass("publishing_time_pattern");
    }
}
