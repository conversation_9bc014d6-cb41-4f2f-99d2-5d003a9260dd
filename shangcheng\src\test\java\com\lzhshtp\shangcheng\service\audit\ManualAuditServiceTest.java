package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.mapper.*;
import com.lzhshtp.shangcheng.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * ManualAuditService测试类
 */
@ExtendWith(MockitoExtension.class)
class ManualAuditServiceTest {

    @Mock
    private ManualAuditTaskMapper manualAuditTaskMapper;

    @Mock
    private ProductMapper productMapper;

    @Mock
    private AutoAuditRecordMapper autoAuditRecordMapper;

    @Mock
    private UserMapper userMapper;

    @Mock
    private SecondReviewTaskMapper secondReviewTaskMapper;

    @Mock
    private MaterialRequestService materialRequestService;

    @Mock
    private com.fasterxml.jackson.databind.ObjectMapper objectMapper;

    @InjectMocks
    private ManualAuditService manualAuditService;

    private ManualAuditTask testManualTask;
    private Product testProduct;
    private User testSeller;
    private AutoAuditRecord testAutoAuditRecord;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testProduct = new Product();
        testProduct.setId(1L);
        testProduct.setTitle("测试商品");
        testProduct.setDescription("测试商品描述");
        testProduct.setPrice(new BigDecimal("15000"));
        testProduct.setCategoryId(1);
        testProduct.setCondition("全新");
        testProduct.setStatus("manual_review");
        testProduct.setSellerId(100L);
        testProduct.setPostedDate(LocalDateTime.now());

        testSeller = new User();
        testSeller.setUserId(100L);
        testSeller.setUsername("testSeller");
        testSeller.setCreditScore(30); // 低信用分，触发二度复审
        testSeller.setRegistrationDate(LocalDateTime.now().minusMonths(6));
        testSeller.setIsActive(true);
        testSeller.setRole("general_user");

        testAutoAuditRecord = new AutoAuditRecord();
        testAutoAuditRecord.setRecordId(1L);
        testAutoAuditRecord.setProductId(1L);
        testAutoAuditRecord.setFinalDecision("manual_review");
        testAutoAuditRecord.setDecisionReason("需要人工审核");
        testAutoAuditRecord.setRiskScore(new BigDecimal("75"));
        testAutoAuditRecord.setCreatedTime(LocalDateTime.now());

        testManualTask = new ManualAuditTask();
        testManualTask.setTaskId(1L);
        testManualTask.setProductId(1L);
        testManualTask.setSellerId(100L);
        testManualTask.setAutoAuditRecordId(1L);
        testManualTask.setAuditReasons("[\"价格过高\", \"卖家信用分低\"]");
        testManualTask.setStatus("completed");
        testManualTask.setPriority(2);
        testManualTask.setAdminDecision("approved");
        testManualTask.setAdminComments("商品质量良好，但需要二度复审");
        testManualTask.setCreatedTime(LocalDateTime.now().minusHours(2));
        testManualTask.setAssignedTime(LocalDateTime.now().minusHours(1));
        testManualTask.setCompletedTime(LocalDateTime.now());
    }

    @Test
    void testCreateSecondReviewTask_Success() {
        // 准备mock数据
        when(productMapper.selectById(1L)).thenReturn(testProduct);
        when(userMapper.selectById(100L)).thenReturn(testSeller);
        when(autoAuditRecordMapper.selectById(1L)).thenReturn(testAutoAuditRecord);
        when(secondReviewTaskMapper.findByProductId(1L)).thenReturn(null); // 没有现有的二度复审任务
        when(secondReviewTaskMapper.insert(any(SecondReviewTask.class))).thenReturn(1);
        when(productMapper.updateById(any(Product.class))).thenReturn(1);
        try {
            when(objectMapper.writeValueAsString(any())).thenReturn("{}"); // Mock JSON序列化
        } catch (JsonProcessingException e) {
            // 不会发生，因为是Mock
        }

        // 执行测试 - 通过反射调用私有方法
        try {
            java.lang.reflect.Method method = ManualAuditService.class.getDeclaredMethod(
                "createSecondReviewTask", ManualAuditTask.class, String.class);
            method.setAccessible(true);
            method.invoke(manualAuditService, testManualTask, "价格过高需要二度复审");

            // 验证调用 - 允许多次调用productMapper.selectById
            verify(productMapper, atLeastOnce()).selectById(1L);
            verify(secondReviewTaskMapper).findByProductId(1L);
            verify(secondReviewTaskMapper).insert(any(SecondReviewTask.class));
            verify(productMapper).updateById(argThat(product ->
                "second_review".equals(product.getStatus())));

        } catch (Exception e) {
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    void testCreateSecondReviewTask_ExistingTask() {
        // 准备mock数据 - 已存在未完成的二度复审任务
        SecondReviewTask existingTask = new SecondReviewTask();
        existingTask.setTaskId(999L);
        existingTask.setStatus("pending");

        when(productMapper.selectById(1L)).thenReturn(testProduct);
        when(secondReviewTaskMapper.findByProductId(1L)).thenReturn(existingTask);

        // 执行测试
        try {
            java.lang.reflect.Method method = ManualAuditService.class.getDeclaredMethod(
                "createSecondReviewTask", ManualAuditTask.class, String.class);
            method.setAccessible(true);
            method.invoke(manualAuditService, testManualTask, "价格过高需要二度复审");

            // 验证不会创建新的二度复审任务
            verify(secondReviewTaskMapper, never()).insert(any(SecondReviewTask.class));

        } catch (Exception e) {
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    void testNeedSecondReview_HighPrice() {
        // 测试高价商品触发二度复审
        testProduct.setPrice(new BigDecimal("15000")); // 超过10000元
        testSeller.setCreditScore(80); // 正常信用分

        when(productMapper.selectById(1L)).thenReturn(testProduct);
        when(userMapper.selectById(100L)).thenReturn(testSeller);

        // Mock JSON解析
        try {
            when(objectMapper.readValue(anyString(), any(com.fasterxml.jackson.core.type.TypeReference.class)))
                .thenReturn(java.util.Arrays.asList("价格过高"));
        } catch (JsonProcessingException e) {
            // 不会发生，因为是Mock
        }

        // 执行测试 - 通过反射调用私有方法
        try {
            java.lang.reflect.Method method = ManualAuditService.class.getDeclaredMethod(
                "needSecondReview", ManualAuditTask.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(manualAuditService, testManualTask);

            // 验证结果
            assert result : "高价商品应该触发二度复审";

        } catch (Exception e) {
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    void testNeedSecondReview_LowCreditScore() {
        // 测试低信用分触发二度复审
        testProduct.setPrice(new BigDecimal("5000")); // 正常价格
        testSeller.setCreditScore(30); // 低信用分

        when(productMapper.selectById(1L)).thenReturn(testProduct);
        when(userMapper.selectById(100L)).thenReturn(testSeller);

        // Mock JSON解析
        try {
            when(objectMapper.readValue(anyString(), any(com.fasterxml.jackson.core.type.TypeReference.class)))
                .thenReturn(java.util.Arrays.asList("信用分低"));
        } catch (JsonProcessingException e) {
            // 不会发生，因为是Mock
        }

        // 执行测试
        try {
            java.lang.reflect.Method method = ManualAuditService.class.getDeclaredMethod(
                "needSecondReview", ManualAuditTask.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(manualAuditService, testManualTask);

            // 验证结果
            assert result : "低信用分应该触发二度复审";

        } catch (Exception e) {
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    void testNeedSecondReview_NoTrigger() {
        // 测试不触发二度复审的情况
        testProduct.setPrice(new BigDecimal("5000")); // 正常价格
        testSeller.setCreditScore(80); // 正常信用分

        when(productMapper.selectById(1L)).thenReturn(testProduct);
        when(userMapper.selectById(100L)).thenReturn(testSeller);

        // Mock JSON解析
        try {
            when(objectMapper.readValue(anyString(), any(com.fasterxml.jackson.core.type.TypeReference.class)))
                .thenReturn(java.util.Arrays.asList("其他原因"));
        } catch (JsonProcessingException e) {
            // 不会发生，因为是Mock
        }

        // 执行测试
        try {
            java.lang.reflect.Method method = ManualAuditService.class.getDeclaredMethod(
                "needSecondReview", ManualAuditTask.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(manualAuditService, testManualTask);

            // 验证结果
            assert !result : "正常情况不应该触发二度复审";

        } catch (Exception e) {
            throw new RuntimeException("测试失败", e);
        }
    }
}
