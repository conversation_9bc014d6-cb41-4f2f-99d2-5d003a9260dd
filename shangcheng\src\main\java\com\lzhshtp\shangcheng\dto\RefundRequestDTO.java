package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款申请DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundRequestDTO {
    
    private Long refundId;
    
    @NotNull(message = "订单ID不能为空")
    private Long orderId;
    
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.01", message = "退款金额必须大于0.01元")
    private BigDecimal refundAmount;
    
    @NotBlank(message = "退款原因不能为空")
    private String refundReason;
    
    @NotBlank(message = "退款类型不能为空")
    private String refundType; // refund_only, return_refund
    
    private String status;
    private String sellerResponse;
    private String adminResponse;
    private List<String> evidenceUrls; // 证据图片URLs
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 关联信息
    private String buyerName;
    private String sellerName;
    private String productTitle;
    private String orderStatus;

    // 强制退款任务相关
    private Long forcedRefundId; // 强制退款任务ID（仅在强制退款任务中使用）
}
