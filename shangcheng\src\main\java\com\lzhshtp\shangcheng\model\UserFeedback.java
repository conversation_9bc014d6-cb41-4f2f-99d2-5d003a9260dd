package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户反馈/举报实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_user_feedback")
public class UserFeedback {
    
    /**
     * 反馈记录唯一标识ID
     */
    @TableId(value = "lzhshtp_feedback_id", type = IdType.AUTO)
    private Long feedbackId;
    
    /**
     * 提交反馈的用户ID
     */
    @TableField("lzhshtp_reporter_id")
    private Long reporterId;
    
    /**
     * 反馈类型: bug_report(问题反馈), suggestion(建议), complaint(投诉), abuse_report(举报)
     */
    @TableField("lzhshtp_feedback_type")
    private String feedbackType;
    
    /**
     * 如果是举报，则指明举报对象类型（例如 'product', 'user', 'post'）
     */
    @TableField("lzhshtp_related_entity_type")
    private String relatedEntityType;
    
    /**
     * 关联的实体ID（例如：举报商品则为product_id）
     */
    @TableField("lzhshtp_related_entity_id")
    private Long relatedEntityId;
    
    /**
     * 反馈或举报的详细内容
     */
    @TableField("lzhshtp_content")
    private String content;
    
    /**
     * 提交时间
     */
    @TableField("lzhshtp_submitted_at")
    private LocalDateTime submittedAt;
    
    /**
     * 处理状态: pending(待处理), in_progress(处理中), resolved(已解决), rejected(已拒绝)
     */
    @TableField("lzhshtp_status")
    private String status;
    
    /**
     * 管理员处理时的备注
     */
    @TableField("lzhshtp_admin_notes")
    private String adminNotes;
    
    /**
     * 处理该反馈的管理员用户ID
     */
    @TableField("lzhshtp_resolved_by_admin_id")
    private Long resolvedByAdminId;
} 