package io.github.qifan777.knowledge.infrastructure.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@JsonComponent
@Slf4j
public class LocalDateTimeConvert {

  public static class Serializer extends JsonSerializer<LocalDateTime> {


    @Override
    public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator,
        SerializerProvider serializerProvider) throws IOException {
      DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(
          "yyyy-MM-dd HH:mm:ss");
      String format = dateTimeFormatter.format(localDateTime);
      jsonGenerator.writeString(format);
    }
  }

  public static class Deserializer extends JsonDeserializer<LocalDateTime> {


    @Override
    public LocalDateTime deserialize(JsonParser jsonParser,
        DeserializationContext deserializationContext) throws IOException {
      String text = jsonParser.getText();
      DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(
          "yyyy-MM-dd HH:mm:ss");
      return LocalDateTime.parse(text, dateTimeFormatter);
    }
  }
}
