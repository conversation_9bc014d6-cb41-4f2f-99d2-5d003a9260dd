import request from '@/utils/request';

/**
 * 查询待验货列表（验货员）
 * @returns {Promise} 请求Promise
 */
export function getPendingVerifications() {
  return request({
    url: '/verifications/pending',
    method: 'get'
  });
}

/**
 * 查询所有待验货列表（管理员）
 * @returns {Promise} 请求Promise
 */
export function getAllPendingVerifications() {
  return request({
    url: '/verifications/pending/all',
    method: 'get'
  });
}

/**
 * 更新验货状态
 * @param {Number} verificationId 验货记录ID
 * @param {String} status 新状态
 * @returns {Promise} 请求Promise
 */
export function updateVerificationStatus(verificationId, status) {
  return request({
    url: `/verifications/${verificationId}/status`,
    method: 'post',
    params: { status }
  });
}

/**
 * 提交验货结果
 * @param {Object} data 验货结果数据
 * @returns {Promise} 请求Promise
 */
export function submitVerificationResult(data) {
  return request({
    url: '/verifications/result',
    method: 'post',
    data
  });
}

/**
 * 验货通过，转发给买家
 * @param {Number} verificationId 验货记录ID
 * @returns {Promise} 请求Promise
 */
export function forwardToBuyer(verificationId) {
  return request({
    url: `/verifications/forward/${verificationId}`,
    method: 'post'
  });
}

/**
 * 根据订单ID查询验货记录
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function getVerificationByOrderId(orderId) {
  return request({
    url: `/verifications/order/${orderId}`,
    method: 'get'
  });
}
