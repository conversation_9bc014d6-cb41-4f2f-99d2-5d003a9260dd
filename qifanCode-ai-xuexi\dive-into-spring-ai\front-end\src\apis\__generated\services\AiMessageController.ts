import type {Executor} from '../';
import type {
    AiMessageInput, 
    AiMessageWrapper, 
    Flux, 
    ServerSentEvent
} from '../model/static/';

export class AiMessageController {
    
    constructor(private executor: Executor) {}
    
    /**
     * @param input 消息包含文本信息，会话id，多媒体信息（图片语言）。参考src/main/dto/AiMessage.dto
     * @return SSE流
     */
    chatStreamWithHistory: (options: AiMessageControllerOptions['chatStreamWithHistory']) => Promise<
        Flux<ServerSentEvent<string>>
    > = async(options) => {
        let _uri = '/message/chat';
        return (await this.executor({uri: _uri, method: 'POST', body: options.body})) as Promise<Flux<ServerSentEvent<string>>>;
    }
    
    deleteHistory: (options: AiMessageControllerOptions['deleteHistory']) => Promise<
        void
    > = async(options) => {
        let _uri = '/message/history/';
        _uri += encodeURIComponent(options.sessionId);
        return (await this.executor({uri: _uri, method: 'DELETE'})) as Promise<void>;
    }
    
    /**
     * 消息保存
     * @param input 用户发送的消息/AI回复的消息
     */
    save: (options: AiMessageControllerOptions['save']) => Promise<
        void
    > = async(options) => {
        let _uri = '/message';
        return (await this.executor({uri: _uri, method: 'POST', body: options.body})) as Promise<void>;
    }
}

export type AiMessageControllerOptions = {
    'deleteHistory': {
        sessionId: string
    }, 
    'save': {
        /**
         * 用户发送的消息/AI回复的消息
         */
        body: AiMessageInput
    }, 
    'chatStreamWithHistory': {
        /**
         * 消息包含文本信息，会话id，多媒体信息（图片语言）。参考src/main/dto/AiMessage.dto
         */
        body: AiMessageWrapper
    }
}
