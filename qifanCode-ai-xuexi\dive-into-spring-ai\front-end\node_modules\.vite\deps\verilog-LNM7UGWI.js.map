{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/verilog.js"], "sourcesContent": ["function mkVerilog(parserConfig) {\n\n  var statementIndentUnit = parserConfig.statementIndentUnit,\n      dontAlignCalls = parserConfig.dontAlignCalls,\n      noIndentKeywords = parserConfig.noIndentKeywords || [],\n      multiLineStrings = parserConfig.multiLineStrings,\n      hooks = parserConfig.hooks || {};\n\n  function words(str) {\n    var obj = {}, words = str.split(\" \");\n    for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n    return obj;\n  }\n\n  /**\n   * Keywords from IEEE 1800-2012\n   */\n  var keywords = words(\n    \"accept_on alias always always_comb always_ff always_latch and assert assign assume automatic before begin bind \" +\n      \"bins binsof bit break buf bufif0 bufif1 byte case casex casez cell chandle checker class clocking cmos config \" +\n      \"const constraint context continue cover covergroup coverpoint cross deassign default defparam design disable \" +\n      \"dist do edge else end endcase endchecker endclass endclocking endconfig endfunction endgenerate endgroup \" +\n      \"endinterface endmodule endpackage endprimitive endprogram endproperty endspecify endsequence endtable endtask \" +\n      \"enum event eventually expect export extends extern final first_match for force foreach forever fork forkjoin \" +\n      \"function generate genvar global highz0 highz1 if iff ifnone ignore_bins illegal_bins implements implies import \" +\n      \"incdir include initial inout input inside instance int integer interconnect interface intersect join join_any \" +\n      \"join_none large let liblist library local localparam logic longint macromodule matches medium modport module \" +\n      \"nand negedge nettype new nexttime nmos nor noshowcancelled not notif0 notif1 null or output package packed \" +\n      \"parameter pmos posedge primitive priority program property protected pull0 pull1 pulldown pullup \" +\n      \"pulsestyle_ondetect pulsestyle_onevent pure rand randc randcase randsequence rcmos real realtime ref reg \" +\n      \"reject_on release repeat restrict return rnmos rpmos rtran rtranif0 rtranif1 s_always s_eventually s_nexttime \" +\n      \"s_until s_until_with scalared sequence shortint shortreal showcancelled signed small soft solve specify \" +\n      \"specparam static string strong strong0 strong1 struct super supply0 supply1 sync_accept_on sync_reject_on \" +\n      \"table tagged task this throughout time timeprecision timeunit tran tranif0 tranif1 tri tri0 tri1 triand trior \" +\n      \"trireg type typedef union unique unique0 unsigned until until_with untyped use uwire var vectored virtual void \" +\n      \"wait wait_order wand weak weak0 weak1 while wildcard wire with within wor xnor xor\");\n\n  /** Operators from IEEE 1800-2012\n      unary_operator ::=\n      + | - | ! | ~ | & | ~& | | | ~| | ^ | ~^ | ^~\n      binary_operator ::=\n      + | - | * | / | % | == | != | === | !== | ==? | !=? | && | || | **\n      | < | <= | > | >= | & | | | ^ | ^~ | ~^ | >> | << | >>> | <<<\n      | -> | <->\n      inc_or_dec_operator ::= ++ | --\n      unary_module_path_operator ::=\n      ! | ~ | & | ~& | | | ~| | ^ | ~^ | ^~\n      binary_module_path_operator ::=\n      == | != | && | || | & | | | ^ | ^~ | ~^\n  */\n  var isOperatorChar = /[\\+\\-\\*\\/!~&|^%=?:]/;\n  var isBracketChar = /[\\[\\]{}()]/;\n\n  var unsignedNumber = /\\d[0-9_]*/;\n  var decimalLiteral = /\\d*\\s*'s?d\\s*\\d[0-9_]*/i;\n  var binaryLiteral = /\\d*\\s*'s?b\\s*[xz01][xz01_]*/i;\n  var octLiteral = /\\d*\\s*'s?o\\s*[xz0-7][xz0-7_]*/i;\n  var hexLiteral = /\\d*\\s*'s?h\\s*[0-9a-fxz?][0-9a-fxz?_]*/i;\n  var realLiteral = /(\\d[\\d_]*(\\.\\d[\\d_]*)?E-?[\\d_]+)|(\\d[\\d_]*\\.\\d[\\d_]*)/i;\n\n  var closingBracketOrWord = /^((\\w+)|[)}\\]])/;\n  var closingBracket = /[)}\\]]/;\n\n  var curPunc;\n  var curKeyword;\n\n  // Block openings which are closed by a matching keyword in the form of (\"end\" + keyword)\n  // E.g. \"task\" => \"endtask\"\n  var blockKeywords = words(\n    \"case checker class clocking config function generate interface module package \" +\n      \"primitive program property specify sequence table task\"\n  );\n\n  // Opening/closing pairs\n  var openClose = {};\n  for (var keyword in blockKeywords) {\n    openClose[keyword] = \"end\" + keyword;\n  }\n  openClose[\"begin\"] = \"end\";\n  openClose[\"casex\"] = \"endcase\";\n  openClose[\"casez\"] = \"endcase\";\n  openClose[\"do\"   ] = \"while\";\n  openClose[\"fork\" ] = \"join;join_any;join_none\";\n  openClose[\"covergroup\"] = \"endgroup\";\n\n  for (var i in noIndentKeywords) {\n    var keyword = noIndentKeywords[i];\n    if (openClose[keyword]) {\n      openClose[keyword] = undefined;\n    }\n  }\n\n  // Keywords which open statements that are ended with a semi-colon\n  var statementKeywords = words(\"always always_comb always_ff always_latch assert assign assume else export for foreach forever if import initial repeat while\");\n\n  function tokenBase(stream, state) {\n    var ch = stream.peek(), style;\n    if (hooks[ch] && (style = hooks[ch](stream, state)) != false) return style;\n    if (hooks.tokenBase && (style = hooks.tokenBase(stream, state)) != false)\n      return style;\n\n    if (/[,;:\\.]/.test(ch)) {\n      curPunc = stream.next();\n      return null;\n    }\n    if (isBracketChar.test(ch)) {\n      curPunc = stream.next();\n      return \"bracket\";\n    }\n    // Macros (tick-defines)\n    if (ch == '`') {\n      stream.next();\n      if (stream.eatWhile(/[\\w\\$_]/)) {\n        return \"def\";\n      } else {\n        return null;\n      }\n    }\n    // System calls\n    if (ch == '$') {\n      stream.next();\n      if (stream.eatWhile(/[\\w\\$_]/)) {\n        return \"meta\";\n      } else {\n        return null;\n      }\n    }\n    // Time literals\n    if (ch == '#') {\n      stream.next();\n      stream.eatWhile(/[\\d_.]/);\n      return \"def\";\n    }\n    // Strings\n    if (ch == '\"') {\n      stream.next();\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    // Comments\n    if (ch == \"/\") {\n      stream.next();\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      }\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n      stream.backUp(1);\n    }\n\n    // Numeric literals\n    if (stream.match(realLiteral) ||\n        stream.match(decimalLiteral) ||\n        stream.match(binaryLiteral) ||\n        stream.match(octLiteral) ||\n        stream.match(hexLiteral) ||\n        stream.match(unsignedNumber) ||\n        stream.match(realLiteral)) {\n      return \"number\";\n    }\n\n    // Operators\n    if (stream.eatWhile(isOperatorChar)) {\n      return \"meta\";\n    }\n\n    // Keywords / plain variables\n    if (stream.eatWhile(/[\\w\\$_]/)) {\n      var cur = stream.current();\n      if (keywords[cur]) {\n        if (openClose[cur]) {\n          curPunc = \"newblock\";\n        }\n        if (statementKeywords[cur]) {\n          curPunc = \"newstatement\";\n        }\n        curKeyword = cur;\n        return \"keyword\";\n      }\n      return \"variable\";\n    }\n\n    stream.next();\n    return null;\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) {end = true; break;}\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = tokenBase;\n      return \"string\";\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return \"comment\";\n  }\n\n  function Context(indented, column, type, align, prev) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.align = align;\n    this.prev = prev;\n  }\n  function pushContext(state, col, type) {\n    var indent = state.indented;\n    var c = new Context(indent, col, type, null, state.context);\n    return state.context = c;\n  }\n  function popContext(state) {\n    var t = state.context.type;\n    if (t == \")\" || t == \"]\" || t == \"}\") {\n      state.indented = state.context.indented;\n    }\n    return state.context = state.context.prev;\n  }\n\n  function isClosing(text, contextClosing) {\n    if (text == contextClosing) {\n      return true;\n    } else {\n      // contextClosing may be multiple keywords separated by ;\n      var closingKeywords = contextClosing.split(\";\");\n      for (var i in closingKeywords) {\n        if (text == closingKeywords[i]) {\n          return true;\n        }\n      }\n      return false;\n    }\n  }\n\n  function buildElectricInputRegEx() {\n    // Reindentation should occur on any bracket char: {}()[]\n    // or on a match of any of the block closing keywords, at\n    // the end of a line\n    var allClosings = [];\n    for (var i in openClose) {\n      if (openClose[i]) {\n        var closings = openClose[i].split(\";\");\n        for (var j in closings) {\n          allClosings.push(closings[j]);\n        }\n      }\n    }\n    var re = new RegExp(\"[{}()\\\\[\\\\]]|(\" + allClosings.join(\"|\") + \")$\");\n    return re;\n  }\n\n  // Interface\n  return {\n    name: \"verilog\",\n\n    startState: function(indentUnit) {\n      var state = {\n        tokenize: null,\n        context: new Context(-indentUnit, 0, \"top\", false),\n        indented: 0,\n        startOfLine: true\n      };\n      if (hooks.startState) hooks.startState(state);\n      return state;\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (hooks.token) {\n        // Call hook, with an optional return value of a style to override verilog styling.\n        var style = hooks.token(stream, state);\n        if (style !== undefined) {\n          return style;\n        }\n      }\n      if (stream.eatSpace()) return null;\n      curPunc = null;\n      curKeyword = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\" || style == \"meta\" || style == \"variable\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if (curPunc == ctx.type) {\n        popContext(state);\n      } else if ((curPunc == \";\" && ctx.type == \"statement\") ||\n                 (ctx.type && isClosing(curKeyword, ctx.type))) {\n        ctx = popContext(state);\n        while (ctx && ctx.type == \"statement\") ctx = popContext(state);\n      } else if (curPunc == \"{\") {\n        pushContext(state, stream.column(), \"}\");\n      } else if (curPunc == \"[\") {\n        pushContext(state, stream.column(), \"]\");\n      } else if (curPunc == \"(\") {\n        pushContext(state, stream.column(), \")\");\n      } else if (ctx && ctx.type == \"endcase\" && curPunc == \":\") {\n        pushContext(state, stream.column(), \"statement\");\n      } else if (curPunc == \"newstatement\") {\n        pushContext(state, stream.column(), \"statement\");\n      } else if (curPunc == \"newblock\") {\n        if (curKeyword == \"function\" && ctx && (ctx.type == \"statement\" || ctx.type == \"endgroup\")) {\n          // The 'function' keyword can appear in some other contexts where it actually does not\n          // indicate a function (import/export DPI and covergroup definitions).\n          // Do nothing in this case\n        } else if (curKeyword == \"task\" && ctx && ctx.type == \"statement\") {\n          // Same thing for task\n        } else {\n          var close = openClose[curKeyword];\n          pushContext(state, stream.column(), close);\n        }\n      }\n\n      state.startOfLine = false;\n      return style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize != tokenBase && state.tokenize != null) return null;\n      if (hooks.indent) {\n        var fromHook = hooks.indent(state);\n        if (fromHook >= 0) return fromHook;\n      }\n      var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n      if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n      var closing = false;\n      var possibleClosing = textAfter.match(closingBracketOrWord);\n      if (possibleClosing)\n        closing = isClosing(possibleClosing[0], ctx.type);\n      if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : statementIndentUnit || cx.unit);\n      else if (closingBracket.test(ctx.type) && ctx.align && !dontAlignCalls) return ctx.column + (closing ? 0 : 1);\n      else if (ctx.type == \")\" && !closing) return ctx.indented + (statementIndentUnit || cx.unit);\n      else return ctx.indented + (closing ? 0 : cx.unit);\n    },\n\n    languageData: {\n      indentOnInput: buildElectricInputRegEx(),\n      commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n    }\n  };\n};\n\nexport const verilog = mkVerilog({})\n\n// TL-Verilog mode.\n// See tl-x.org for language spec.\n// See the mode in action at makerchip.com.\n// Contact: <EMAIL>\n\n// TLV Identifier prefixes.\n// Note that sign is not treated separately, so \"+/-\" versions of numeric identifiers\n// are included.\nvar tlvIdentifierStyle = {\n  \"|\": \"link\",\n  \">\": \"property\",  // Should condition this off for > TLV 1c.\n  \"$\": \"variable\",\n  \"$$\": \"variable\",\n  \"?$\": \"qualifier\",\n  \"?*\": \"qualifier\",\n  \"-\": \"contentSeparator\",\n  \"/\": \"property\",\n  \"/-\": \"property\",\n  \"@\": \"variableName.special\",\n  \"@-\": \"variableName.special\",\n  \"@++\": \"variableName.special\",\n  \"@+=\": \"variableName.special\",\n  \"@+=-\": \"variableName.special\",\n  \"@--\": \"variableName.special\",\n  \"@-=\": \"variableName.special\",\n  \"%+\": \"tag\",\n  \"%-\": \"tag\",\n  \"%\": \"tag\",\n  \">>\": \"tag\",\n  \"<<\": \"tag\",\n  \"<>\": \"tag\",\n  \"#\": \"tag\",  // Need to choose a style for this.\n  \"^\": \"attribute\",\n  \"^^\": \"attribute\",\n  \"^!\": \"attribute\",\n  \"*\": \"variable\",\n  \"**\": \"variable\",\n  \"\\\\\": \"keyword\",\n  \"\\\"\": \"comment\"\n};\n\n// Lines starting with these characters define scope (result in indentation).\nvar tlvScopePrefixChars = {\n  \"/\": \"beh-hier\",\n  \">\": \"beh-hier\",\n  \"-\": \"phys-hier\",\n  \"|\": \"pipe\",\n  \"?\": \"when\",\n  \"@\": \"stage\",\n  \"\\\\\": \"keyword\"\n};\nvar tlvIndentUnit = 3;\nvar tlvTrackStatements = false;\nvar tlvIdentMatch = /^([~!@#\\$%\\^&\\*-\\+=\\?\\/\\\\\\|'\"<>]+)([\\d\\w_]*)/;  // Matches an identifier.\n// Note that ':' is excluded, because of it's use in [:].\nvar tlvLineIndentationMatch = /^[! ] */;\nvar tlvCommentMatch = /^\\/[\\/\\*]/;\n\nexport const tlv = mkVerilog({\n  hooks: {\n    electricInput: false,\n\n    // Return undefined for verilog tokenizing, or style for TLV token (null not used).\n    // Standard CM styles are used for most formatting, but some TL-Verilog-specific highlighting\n    // can be enabled with the definition of cm-tlv-* styles, including highlighting for:\n    //   - M4 tokens\n    //   - TLV scope indentation\n    //   - Statement delimitation (enabled by tlvTrackStatements)\n    token: function(stream, state) {\n      var style = undefined;\n      var match;  // Return value of pattern matches.\n\n      // Set highlighting mode based on code region (TLV or SV).\n      if (stream.sol() && ! state.tlvInBlockComment) {\n        // Process region.\n        if (stream.peek() == '\\\\') {\n          style = \"def\";\n          stream.skipToEnd();\n          if (stream.string.match(/\\\\SV/)) {\n            state.tlvCodeActive = false;\n          } else if (stream.string.match(/\\\\TLV/)){\n            state.tlvCodeActive = true;\n          }\n        }\n        // Correct indentation in the face of a line prefix char.\n        if (state.tlvCodeActive && stream.pos == 0 &&\n            (state.indented == 0) && (match = stream.match(tlvLineIndentationMatch, false))) {\n          state.indented = match[0].length;\n        }\n\n        // Compute indentation state:\n        //   o Auto indentation on next line\n        //   o Indentation scope styles\n        var indented = state.indented;\n        var depth = indented / tlvIndentUnit;\n        if (depth <= state.tlvIndentationStyle.length) {\n          // not deeper than current scope\n\n          var blankline = stream.string.length == indented;\n          var chPos = depth * tlvIndentUnit;\n          if (chPos < stream.string.length) {\n            var bodyString = stream.string.slice(chPos);\n            var ch = bodyString[0];\n            if (tlvScopePrefixChars[ch] && ((match = bodyString.match(tlvIdentMatch)) &&\n                                            tlvIdentifierStyle[match[1]])) {\n              // This line begins scope.\n              // Next line gets indented one level.\n              indented += tlvIndentUnit;\n              // Style the next level of indentation (except non-region keyword identifiers,\n              //   which are statements themselves)\n              if (!(ch == \"\\\\\" && chPos > 0)) {\n                state.tlvIndentationStyle[depth] = tlvScopePrefixChars[ch];\n                if (tlvTrackStatements) {state.statementComment = false;}\n                depth++;\n              }\n            }\n          }\n          // Clear out deeper indentation levels unless line is blank.\n          if (!blankline) {\n            while (state.tlvIndentationStyle.length > depth) {\n              state.tlvIndentationStyle.pop();\n            }\n          }\n        }\n        // Set next level of indentation.\n        state.tlvNextIndent = indented;\n      }\n\n      if (state.tlvCodeActive) {\n        // Highlight as TLV.\n\n        var beginStatement = false;\n        if (tlvTrackStatements) {\n          // This starts a statement if the position is at the scope level\n          // and we're not within a statement leading comment.\n          beginStatement =\n            (stream.peek() != \" \") &&   // not a space\n            (style === undefined) &&    // not a region identifier\n            !state.tlvInBlockComment && // not in block comment\n            //!stream.match(tlvCommentMatch, false) && // not comment start\n          (stream.column() == state.tlvIndentationStyle.length * tlvIndentUnit);  // at scope level\n          if (beginStatement) {\n            if (state.statementComment) {\n              // statement already started by comment\n              beginStatement = false;\n            }\n            state.statementComment =\n              stream.match(tlvCommentMatch, false); // comment start\n          }\n        }\n\n        var match;\n        if (style !== undefined) {\n        } else if (state.tlvInBlockComment) {\n          // In a block comment.\n          if (stream.match(/^.*?\\*\\//)) {\n            // Exit block comment.\n            state.tlvInBlockComment = false;\n            if (tlvTrackStatements && !stream.eol()) {\n              // Anything after comment is assumed to be real statement content.\n              state.statementComment = false;\n            }\n          } else {\n            stream.skipToEnd();\n          }\n          style = \"comment\";\n        } else if ((match = stream.match(tlvCommentMatch)) && !state.tlvInBlockComment) {\n          // Start comment.\n          if (match[0] == \"//\") {\n            // Line comment.\n            stream.skipToEnd();\n          } else {\n            // Block comment.\n            state.tlvInBlockComment = true;\n          }\n          style = \"comment\";\n        } else if (match = stream.match(tlvIdentMatch)) {\n          // looks like an identifier (or identifier prefix)\n          var prefix = match[1];\n          var mnemonic = match[2];\n          if (// is identifier prefix\n            tlvIdentifierStyle.hasOwnProperty(prefix) &&\n              // has mnemonic or we're at the end of the line (maybe it hasn't been typed yet)\n            (mnemonic.length > 0 || stream.eol())) {\n            style = tlvIdentifierStyle[prefix];\n          } else {\n            // Just swallow one character and try again.\n            // This enables subsequent identifier match with preceding symbol character, which\n            //   is legal within a statement.  (Eg, !$reset).  It also enables detection of\n            //   comment start with preceding symbols.\n            stream.backUp(stream.current().length - 1);\n          }\n        } else if (stream.match(/^\\t+/)) {\n          // Highlight tabs, which are illegal.\n          style = \"invalid\";\n        } else if (stream.match(/^[\\[\\]{}\\(\\);\\:]+/)) {\n          // [:], (), {}, ;.\n          style = \"meta\";\n        } else if (match = stream.match(/^[mM]4([\\+_])?[\\w\\d_]*/)) {\n          // m4 pre proc\n          style = (match[1] == \"+\") ? \"keyword.special\" : \"keyword\";\n        } else if (stream.match(/^ +/)){\n          // Skip over spaces.\n          if (stream.eol()) {\n            // Trailing spaces.\n            style = \"error\";\n          }\n        } else if (stream.match(/^[\\w\\d_]+/)) {\n          // alpha-numeric token.\n          style = \"number\";\n        } else {\n          // Eat the next char w/ no formatting.\n          stream.next();\n        }\n      } else {\n        if (stream.match(/^[mM]4([\\w\\d_]*)/)) {\n          // m4 pre proc\n          style = \"keyword\";\n        }\n      }\n      return style;\n    },\n\n    indent: function(state) {\n      return (state.tlvCodeActive == true) ? state.tlvNextIndent : -1;\n    },\n\n    startState: function(state) {\n      state.tlvIndentationStyle = [];  // Styles to use for each level of indentation.\n      state.tlvCodeActive = true;  // True when we're in a TLV region (and at beginning of file).\n      state.tlvNextIndent = -1;    // The number of spaces to autoindent the next line if tlvCodeActive.\n      state.tlvInBlockComment = false;  // True inside /**/ comment.\n      if (tlvTrackStatements) {\n        state.statementComment = false;  // True inside a statement's header comment.\n      }\n    }\n\n  }\n});\n"], "mappings": ";;;AAAA,SAAS,UAAU,cAAc;AAE/B,MAAI,sBAAsB,aAAa,qBACnC,iBAAiB,aAAa,gBAC9B,mBAAmB,aAAa,oBAAoB,CAAC,GACrD,mBAAmB,aAAa,kBAChC,QAAQ,aAAa,SAAS,CAAC;AAEnC,WAAS,MAAM,KAAK;AAClB,QAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,aAASC,KAAI,GAAGA,KAAID,OAAM,QAAQ,EAAEC;AAAG,UAAID,OAAMC,EAAC,CAAC,IAAI;AACvD,WAAO;AAAA,EACT;AAKA,MAAI,WAAW;AAAA,IACb;AAAA,EAiBsF;AAexF,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AAEpB,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AACpB,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,cAAc;AAElB,MAAI,uBAAuB;AAC3B,MAAI,iBAAiB;AAErB,MAAI;AACJ,MAAI;AAIJ,MAAI,gBAAgB;AAAA,IAClB;AAAA,EAEF;AAGA,MAAI,YAAY,CAAC;AACjB,WAAS,WAAW,eAAe;AACjC,cAAU,OAAO,IAAI,QAAQ;AAAA,EAC/B;AACA,YAAU,OAAO,IAAI;AACrB,YAAU,OAAO,IAAI;AACrB,YAAU,OAAO,IAAI;AACrB,YAAU,IAAO,IAAI;AACrB,YAAU,MAAO,IAAI;AACrB,YAAU,YAAY,IAAI;AAE1B,WAAS,KAAK,kBAAkB;AAC9B,QAAI,UAAU,iBAAiB,CAAC;AAChC,QAAI,UAAU,OAAO,GAAG;AACtB,gBAAU,OAAO,IAAI;AAAA,IACvB;AAAA,EACF;AAGA,MAAI,oBAAoB,MAAM,+HAA+H;AAE7J,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK,GAAG;AACxB,QAAI,MAAM,EAAE,MAAM,QAAQ,MAAM,EAAE,EAAE,QAAQ,KAAK,MAAM;AAAO,aAAO;AACrE,QAAI,MAAM,cAAc,QAAQ,MAAM,UAAU,QAAQ,KAAK,MAAM;AACjE,aAAO;AAET,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,gBAAU,OAAO,KAAK;AACtB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,KAAK,EAAE,GAAG;AAC1B,gBAAU,OAAO,KAAK;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,UAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,UAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,YAAM,WAAW,YAAY,EAAE;AAC/B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAM,WAAW;AACjB,eAAO,aAAa,QAAQ,KAAK;AAAA,MACnC;AACA,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAO,UAAU;AACjB,eAAO;AAAA,MACT;AACA,aAAO,OAAO,CAAC;AAAA,IACjB;AAGA,QAAI,OAAO,MAAM,WAAW,KACxB,OAAO,MAAM,cAAc,KAC3B,OAAO,MAAM,aAAa,KAC1B,OAAO,MAAM,UAAU,KACvB,OAAO,MAAM,UAAU,KACvB,OAAO,MAAM,cAAc,KAC3B,OAAO,MAAM,WAAW,GAAG;AAC7B,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,SAAS,cAAc,GAAG;AACnC,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,UAAI,MAAM,OAAO,QAAQ;AACzB,UAAI,SAAS,GAAG,GAAG;AACjB,YAAI,UAAU,GAAG,GAAG;AAClB,oBAAU;AAAA,QACZ;AACA,YAAI,kBAAkB,GAAG,GAAG;AAC1B,oBAAU;AAAA,QACZ;AACA,qBAAa;AACb,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,UAAU,OAAO,MAAM,MAAM;AACjC,cAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,YAAI,QAAQ,SAAS,CAAC,SAAS;AAAC,gBAAM;AAAM;AAAA,QAAM;AAClD,kBAAU,CAAC,WAAW,QAAQ;AAAA,MAChC;AACA,UAAI,OAAO,EAAE,WAAW;AACtB,cAAM,WAAW;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,WAAW,OAAO;AACtB,WAAO,KAAK,OAAO,KAAK,GAAG;AACzB,UAAI,MAAM,OAAO,UAAU;AACzB,cAAM,WAAW;AACjB;AAAA,MACF;AACA,iBAAY,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACA,WAAS,YAAY,OAAO,KAAK,MAAM;AACrC,QAAI,SAAS,MAAM;AACnB,QAAI,IAAI,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAC1D,WAAO,MAAM,UAAU;AAAA,EACzB;AACA,WAAS,WAAW,OAAO;AACzB,QAAI,IAAI,MAAM,QAAQ;AACtB,QAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK;AACpC,YAAM,WAAW,MAAM,QAAQ;AAAA,IACjC;AACA,WAAO,MAAM,UAAU,MAAM,QAAQ;AAAA,EACvC;AAEA,WAAS,UAAU,MAAM,gBAAgB;AACvC,QAAI,QAAQ,gBAAgB;AAC1B,aAAO;AAAA,IACT,OAAO;AAEL,UAAI,kBAAkB,eAAe,MAAM,GAAG;AAC9C,eAASA,MAAK,iBAAiB;AAC7B,YAAI,QAAQ,gBAAgBA,EAAC,GAAG;AAC9B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,0BAA0B;AAIjC,QAAI,cAAc,CAAC;AACnB,aAASA,MAAK,WAAW;AACvB,UAAI,UAAUA,EAAC,GAAG;AAChB,YAAI,WAAW,UAAUA,EAAC,EAAE,MAAM,GAAG;AACrC,iBAAS,KAAK,UAAU;AACtB,sBAAY,KAAK,SAAS,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,IAAI,OAAO,mBAAmB,YAAY,KAAK,GAAG,IAAI,IAAI;AACnE,WAAO;AAAA,EACT;AAGA,SAAO;AAAA,IACL,MAAM;AAAA,IAEN,YAAY,SAAS,YAAY;AAC/B,UAAI,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,KAAK;AAAA,QACjD,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,UAAI,MAAM;AAAY,cAAM,WAAW,KAAK;AAC5C,aAAO;AAAA,IACT;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,MAAM,MAAM;AAChB,UAAI,OAAO,IAAI,GAAG;AAChB,YAAI,IAAI,SAAS;AAAM,cAAI,QAAQ;AACnC,cAAM,WAAW,OAAO,YAAY;AACpC,cAAM,cAAc;AAAA,MACtB;AACA,UAAI,MAAM,OAAO;AAEf,YAAI,QAAQ,MAAM,MAAM,QAAQ,KAAK;AACrC,YAAI,UAAU,QAAW;AACvB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,OAAO,SAAS;AAAG,eAAO;AAC9B,gBAAU;AACV,mBAAa;AACb,UAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,UAAI,SAAS,aAAa,SAAS,UAAU,SAAS;AAAY,eAAO;AACzE,UAAI,IAAI,SAAS;AAAM,YAAI,QAAQ;AAEnC,UAAI,WAAW,IAAI,MAAM;AACvB,mBAAW,KAAK;AAAA,MAClB,WAAY,WAAW,OAAO,IAAI,QAAQ,eAC9B,IAAI,QAAQ,UAAU,YAAY,IAAI,IAAI,GAAI;AACxD,cAAM,WAAW,KAAK;AACtB,eAAO,OAAO,IAAI,QAAQ;AAAa,gBAAM,WAAW,KAAK;AAAA,MAC/D,WAAW,WAAW,KAAK;AACzB,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,MACzC,WAAW,WAAW,KAAK;AACzB,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,MACzC,WAAW,WAAW,KAAK;AACzB,oBAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,MACzC,WAAW,OAAO,IAAI,QAAQ,aAAa,WAAW,KAAK;AACzD,oBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AAAA,MACjD,WAAW,WAAW,gBAAgB;AACpC,oBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AAAA,MACjD,WAAW,WAAW,YAAY;AAChC,YAAI,cAAc,cAAc,QAAQ,IAAI,QAAQ,eAAe,IAAI,QAAQ,aAAa;AAAA,QAI5F,WAAW,cAAc,UAAU,OAAO,IAAI,QAAQ,aAAa;AAAA,QAEnE,OAAO;AACL,cAAI,QAAQ,UAAU,UAAU;AAChC,sBAAY,OAAO,OAAO,OAAO,GAAG,KAAK;AAAA,QAC3C;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,UAAI,MAAM,YAAY,aAAa,MAAM,YAAY;AAAM,eAAO;AAClE,UAAI,MAAM,QAAQ;AAChB,YAAI,WAAW,MAAM,OAAO,KAAK;AACjC,YAAI,YAAY;AAAG,iBAAO;AAAA,MAC5B;AACA,UAAI,MAAM,MAAM,SAAS,YAAY,aAAa,UAAU,OAAO,CAAC;AACpE,UAAI,IAAI,QAAQ,eAAe,aAAa;AAAK,cAAM,IAAI;AAC3D,UAAI,UAAU;AACd,UAAI,kBAAkB,UAAU,MAAM,oBAAoB;AAC1D,UAAI;AACF,kBAAU,UAAU,gBAAgB,CAAC,GAAG,IAAI,IAAI;AAClD,UAAI,IAAI,QAAQ;AAAa,eAAO,IAAI,YAAY,aAAa,MAAM,IAAI,uBAAuB,GAAG;AAAA,eAC5F,eAAe,KAAK,IAAI,IAAI,KAAK,IAAI,SAAS,CAAC;AAAgB,eAAO,IAAI,UAAU,UAAU,IAAI;AAAA,eAClG,IAAI,QAAQ,OAAO,CAAC;AAAS,eAAO,IAAI,YAAY,uBAAuB,GAAG;AAAA;AAClF,eAAO,IAAI,YAAY,UAAU,IAAI,GAAG;AAAA,IAC/C;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe,wBAAwB;AAAA,MACvC,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC9D;AAAA,EACF;AACF;AAEO,IAAM,UAAU,UAAU,CAAC,CAAC;AAUnC,IAAI,qBAAqB;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAM;AACR;AAGA,IAAI,sBAAsB;AAAA,EACxB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AAEpB,IAAI,0BAA0B;AAC9B,IAAI,kBAAkB;AAEf,IAAM,MAAM,UAAU;AAAA,EAC3B,OAAO;AAAA,IACL,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQf,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,QAAQ;AACZ,UAAI;AAGJ,UAAI,OAAO,IAAI,KAAK,CAAE,MAAM,mBAAmB;AAE7C,YAAI,OAAO,KAAK,KAAK,MAAM;AACzB,kBAAQ;AACR,iBAAO,UAAU;AACjB,cAAI,OAAO,OAAO,MAAM,MAAM,GAAG;AAC/B,kBAAM,gBAAgB;AAAA,UACxB,WAAW,OAAO,OAAO,MAAM,OAAO,GAAE;AACtC,kBAAM,gBAAgB;AAAA,UACxB;AAAA,QACF;AAEA,YAAI,MAAM,iBAAiB,OAAO,OAAO,KACpC,MAAM,YAAY,MAAO,QAAQ,OAAO,MAAM,yBAAyB,KAAK,IAAI;AACnF,gBAAM,WAAW,MAAM,CAAC,EAAE;AAAA,QAC5B;AAKA,YAAI,WAAW,MAAM;AACrB,YAAI,QAAQ,WAAW;AACvB,YAAI,SAAS,MAAM,oBAAoB,QAAQ;AAG7C,cAAI,YAAY,OAAO,OAAO,UAAU;AACxC,cAAI,QAAQ,QAAQ;AACpB,cAAI,QAAQ,OAAO,OAAO,QAAQ;AAChC,gBAAI,aAAa,OAAO,OAAO,MAAM,KAAK;AAC1C,gBAAI,KAAK,WAAW,CAAC;AACrB,gBAAI,oBAAoB,EAAE,OAAO,QAAQ,WAAW,MAAM,aAAa,MACvC,mBAAmB,MAAM,CAAC,CAAC,IAAI;AAG7D,0BAAY;AAGZ,kBAAI,EAAE,MAAM,QAAQ,QAAQ,IAAI;AAC9B,sBAAM,oBAAoB,KAAK,IAAI,oBAAoB,EAAE;AACzD,oBAAI,oBAAoB;AAAC,wBAAM,mBAAmB;AAAA,gBAAM;AACxD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,WAAW;AACd,mBAAO,MAAM,oBAAoB,SAAS,OAAO;AAC/C,oBAAM,oBAAoB,IAAI;AAAA,YAChC;AAAA,UACF;AAAA,QACF;AAEA,cAAM,gBAAgB;AAAA,MACxB;AAEA,UAAI,MAAM,eAAe;AAGvB,YAAI,iBAAiB;AACrB,YAAI,oBAAoB;AAGtB,2BACG,OAAO,KAAK,KAAK;AAAA,UACjB,UAAU;AAAA,UACX,CAAC,MAAM;AAAA;AAAA,UAER,OAAO,OAAO,KAAK,MAAM,oBAAoB,SAAS;AACvD,cAAI,gBAAgB;AAClB,gBAAI,MAAM,kBAAkB;AAE1B,+BAAiB;AAAA,YACnB;AACA,kBAAM,mBACJ,OAAO,MAAM,iBAAiB,KAAK;AAAA,UACvC;AAAA,QACF;AAEA,YAAI;AACJ,YAAI,UAAU,QAAW;AAAA,QACzB,WAAW,MAAM,mBAAmB;AAElC,cAAI,OAAO,MAAM,UAAU,GAAG;AAE5B,kBAAM,oBAAoB;AAC1B,gBAAI,sBAAsB,CAAC,OAAO,IAAI,GAAG;AAEvC,oBAAM,mBAAmB;AAAA,YAC3B;AAAA,UACF,OAAO;AACL,mBAAO,UAAU;AAAA,UACnB;AACA,kBAAQ;AAAA,QACV,YAAY,QAAQ,OAAO,MAAM,eAAe,MAAM,CAAC,MAAM,mBAAmB;AAE9E,cAAI,MAAM,CAAC,KAAK,MAAM;AAEpB,mBAAO,UAAU;AAAA,UACnB,OAAO;AAEL,kBAAM,oBAAoB;AAAA,UAC5B;AACA,kBAAQ;AAAA,QACV,WAAW,QAAQ,OAAO,MAAM,aAAa,GAAG;AAE9C,cAAI,SAAS,MAAM,CAAC;AACpB,cAAI,WAAW,MAAM,CAAC;AACtB;AAAA;AAAA,YACE,mBAAmB,eAAe,MAAM;AAAA,aAEvC,SAAS,SAAS,KAAK,OAAO,IAAI;AAAA,YAAI;AACvC,oBAAQ,mBAAmB,MAAM;AAAA,UACnC,OAAO;AAKL,mBAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AAAA,UAC3C;AAAA,QACF,WAAW,OAAO,MAAM,MAAM,GAAG;AAE/B,kBAAQ;AAAA,QACV,WAAW,OAAO,MAAM,mBAAmB,GAAG;AAE5C,kBAAQ;AAAA,QACV,WAAW,QAAQ,OAAO,MAAM,wBAAwB,GAAG;AAEzD,kBAAS,MAAM,CAAC,KAAK,MAAO,oBAAoB;AAAA,QAClD,WAAW,OAAO,MAAM,KAAK,GAAE;AAE7B,cAAI,OAAO,IAAI,GAAG;AAEhB,oBAAQ;AAAA,UACV;AAAA,QACF,WAAW,OAAO,MAAM,WAAW,GAAG;AAEpC,kBAAQ;AAAA,QACV,OAAO;AAEL,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,OAAO;AACL,YAAI,OAAO,MAAM,kBAAkB,GAAG;AAEpC,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IAEA,QAAQ,SAAS,OAAO;AACtB,aAAQ,MAAM,iBAAiB,OAAQ,MAAM,gBAAgB;AAAA,IAC/D;AAAA,IAEA,YAAY,SAAS,OAAO;AAC1B,YAAM,sBAAsB,CAAC;AAC7B,YAAM,gBAAgB;AACtB,YAAM,gBAAgB;AACtB,YAAM,oBAAoB;AAC1B,UAAI,oBAAoB;AACtB,cAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,EAEF;AACF,CAAC;", "names": ["words", "i"]}