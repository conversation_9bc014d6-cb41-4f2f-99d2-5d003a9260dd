package com.lzhshtp.shangcheng.utils;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.lzhshtp.shangcheng.config.AlipayConfig;
import com.lzhshtp.shangcheng.dto.OrderDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Component
public class AlipayUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AlipayUtil.class);
    
    @Autowired
    private AlipayConfig alipayConfig;
    
    @Autowired
    private AlipayClient alipayClient;
    
    /**
     * 生成支付宝支付链接
     *
     * @param order 订单信息
     * @return 支付链接
     */
    public String generatePayLink(OrderDTO order) {
        try {
            // 设置请求参数
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            // 设置订单支付回调地址
            alipayRequest.setReturnUrl(alipayConfig.getOrderReturnUrl());
            alipayRequest.setNotifyUrl(alipayConfig.getOrderNotifyUrl());
            
            // 商户订单号，商户网站订单系统中唯一订单号
            String outTradeNo = order.getOrderId().toString();
            // 付款金额，必填
            String totalAmount = order.getTotalAmount().toString();
            // 订单名称，必填
            String subject = "易转-商品购买-" + order.getProductTitle();
            // 商品描述，可空
            String body = "订单号：" + outTradeNo;
            
            // 设置请求参数
            alipayRequest.setBizContent("{\"out_trade_no\":\"" + outTradeNo + "\","
                    + "\"total_amount\":\"" + totalAmount + "\","
                    + "\"subject\":\"" + subject + "\","
                    + "\"body\":\"" + body + "\","
                    + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");
            
            // 请求
            String result = alipayClient.pageExecute(alipayRequest).getBody();
            logger.info("支付宝支付链接生成成功：{}", result);
            return result;
        } catch (AlipayApiException e) {
            logger.error("生成支付宝支付链接失败", e);
            return null;
        }
    }

    /**
     * 生成充值支付链接
     *
     * @param rechargeOrderNo 充值订单号
     * @param amount 充值金额
     * @param userId 用户ID
     * @return 支付链接
     */
    public String generateRechargePayLink(String rechargeOrderNo, BigDecimal amount, Long userId) {
        try {
            // 设置请求参数
            AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
            // 设置充值回调地址
            alipayRequest.setReturnUrl(alipayConfig.getRechargeReturnUrl());
            alipayRequest.setNotifyUrl(alipayConfig.getRechargeNotifyUrl());

            // 商户订单号
            String outTradeNo = rechargeOrderNo;
            // 付款金额
            String totalAmount = amount.toString();
            // 订单名称
            String subject = "易转-账户充值";
            // 商品描述
            String body = "用户ID：" + userId + "，充值金额：" + amount + "元";

            // 设置请求参数
            alipayRequest.setBizContent("{\"out_trade_no\":\"" + outTradeNo + "\","
                    + "\"total_amount\":\"" + totalAmount + "\","
                    + "\"subject\":\"" + subject + "\","
                    + "\"body\":\"" + body + "\","
                    + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");

            // 请求
            String result = alipayClient.pageExecute(alipayRequest).getBody();
            logger.info("支付宝充值支付链接生成成功：{}", result);
            return result;
        } catch (AlipayApiException e) {
            logger.error("生成支付宝充值支付链接失败", e);
            return null;
        }
    }

    /**
     * 验证支付宝回调签名
     *
     * @param params 回调参数
     * @return 验证结果
     */
    public boolean verifyCallback(Map<String, String> params) {
        try {
            return AlipaySignature.rsaCheckV1(
                    params,
                    alipayConfig.getAlipayPublicKey(),
                    alipayConfig.getCharset(),
                    alipayConfig.getSignType());
        } catch (AlipayApiException e) {
            logger.error("验证支付宝回调签名失败", e);
            return false;
        }
    }
}