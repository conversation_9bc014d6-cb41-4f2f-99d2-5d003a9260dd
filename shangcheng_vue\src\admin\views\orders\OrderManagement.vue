<template>
  <div class="order-management">
    <h2 class="page-title">订单管理</h2>
    <div class="page-description">
      <div class="alert alert-info">
        <i class="info-icon">ℹ️</i>
        <span>管理员只能查看订单信息，不能修改订单内容，以保证C2C交易的公平性。</span>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <div class="search-box">
        <input 
          v-model="queryParams.keyword" 
          type="text" 
          placeholder="搜索订单号/买家/卖家/商品名称"
          @keyup.enter="handleSearch"
        >
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
      
      <div class="status-filter">
        <select v-model="queryParams.status" @change="handleSearch">
          <option value="">全部状态</option>
          <option v-for="option in statusOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>
      
      <div class="page-size-selector">
        <span>每页显示：</span>
        <select v-model="queryParams.pageSize" @change="handleSearch">
          <option :value="10">10条</option>
          <option :value="20">20条</option>
          <option :value="50">50条</option>
        </select>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="order-list-container">
      <table class="order-table" v-if="!loading && orderList.length > 0">
        <thead>
          <tr>
            <th>订单号</th>
            <th>商品信息</th>
            <th>买家</th>
            <th>卖家</th>
            <th>金额</th>
            <th>订单状态</th>
            <th>下单时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="order in orderList" :key="order.orderId">
            <td>{{ order.orderId }}</td>
            <td class="product-info">
              <div class="product-image" v-if="order.productImage">
                <img :src="order.productImage" :alt="order.productTitle">
              </div>
              <div class="product-title">{{ order.productTitle }}</div>
            </td>
            <td>{{ order.buyerUsername }}</td>
            <td>{{ order.sellerUsername }}</td>
            <td class="price">¥{{ order.totalAmount }}</td>
            <td>
              <span class="status-tag" :class="getStatusClass(order.status)">
                {{ order.statusText }}
              </span>
            </td>
            <td>{{ formatDate(order.orderDate) }}</td>
            <td>
              <button class="view-btn" @click="viewOrderDetail(order)">查看详情</button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && orderList.length === 0">
        <div class="empty-icon">📭</div>
        <p>暂无订单数据</p>
      </div>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="!loading && totalPages > 0">
      <button 
        class="page-btn" 
        :disabled="queryParams.page === 1"
        @click="changePage(queryParams.page - 1)"
      >
        上一页
      </button>
      
      <span class="page-info">{{ queryParams.page }} / {{ totalPages }}</span>
      
      <button 
        class="page-btn" 
        :disabled="queryParams.page === totalPages"
        @click="changePage(queryParams.page + 1)"
      >
        下一页
      </button>
    </div>

    <!-- 订单详情弹窗 -->
    <div class="modal" v-if="showDetailModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>订单详情</h3>
          <button class="close-btn" @click="showDetailModal = false">×</button>
        </div>
        <div class="modal-body" v-if="currentOrder">
          <div class="detail-item">
            <span class="detail-label">订单号：</span>
            <span>{{ currentOrder.orderId }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">商品名称：</span>
            <span>{{ currentOrder.productTitle }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">买家：</span>
            <span>{{ currentOrder.buyerUsername }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">卖家：</span>
            <span>{{ currentOrder.sellerUsername }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">订单金额：</span>
            <span>¥{{ currentOrder.totalAmount }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">订单状态：</span>
            <span class="status-tag" :class="getStatusClass(currentOrder.status)">
              {{ currentOrder.statusText }}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">下单时间：</span>
            <span>{{ formatDate(currentOrder.orderDate) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">收货地址：</span>
            <span>{{ currentOrder.shippingAddress }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">支付方式：</span>
            <span>{{ currentOrder.paymentMethod }}</span>
          </div>
          <div class="detail-item" v-if="currentOrder.transactionId">
            <span class="detail-label">交易号：</span>
            <span>{{ currentOrder.transactionId }}</span>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="showDetailModal = false">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { getOrderList, getOrderStatusOptions, getOrderStatusType } from '@/admin/api/orders'

// 状态选项
const statusOptions = getOrderStatusOptions()

// 查询参数
const queryParams = reactive({
  keyword: '',
  status: '',
  page: 1,
  pageSize: 10
})

// 数据状态
const orderList = ref([])
const loading = ref(false)
const total = ref(0)
const totalPages = computed(() => Math.ceil(total.value / queryParams.pageSize))

// 详情弹窗
const showDetailModal = ref(false)
const currentOrder = ref(null)

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const res = await getOrderList(queryParams)
    if (res.code === 200) {
      orderList.value = res.data.records
      total.value = res.data.total
    } else {
      console.error('获取订单列表失败:', res.message)
    }
  } catch (error) {
    console.error('获取订单列表出错:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchOrderList()
}

// 切换页码
const changePage = (page) => {
  queryParams.page = page
  fetchOrderList()
}

// 查看订单详情
const viewOrderDetail = (order) => {
  currentOrder.value = order
  showDetailModal.value = true
}

// 获取状态样式类
const getStatusClass = (status) => {
  return `status-${getOrderStatusType(status)}`
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped>
.order-management {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
}

.page-description {
  margin-bottom: 20px;
}

.alert {
  padding: 10px 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.alert-info {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}

.info-icon {
  margin-right: 10px;
  font-size: 16px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.search-box {
  display: flex;
  flex: 1;
  min-width: 300px;
}

.search-box input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px 0 0 4px;
  outline: none;
}

.search-box input:focus {
  border-color: #1890ff;
}

.search-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-btn:hover {
  background-color: #40a9ff;
}

.status-filter select,
.page-size-selector select {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-list-container {
  margin-bottom: 20px;
  min-height: 400px;
}

.order-table {
  width: 100%;
  border-collapse: collapse;
}

.order-table th,
.order-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.order-table th {
  background-color: #fafafa;
  font-weight: 500;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-image {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 4px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.price {
  font-weight: 500;
  color: #ff4d4f;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.status-primary {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-success {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-warning {
  background-color: #fffbe6;
  color: #faad14;
}

.status-danger {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-info {
  background-color: #f4f4f5;
  color: #909399;
}

.status-default {
  background-color: #f4f4f5;
  color: #909399;
}

.view-btn {
  padding: 4px 8px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

.view-btn:hover {
  background-color: #40a9ff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.page-btn {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  color: #1890ff;
  border-color: #1890ff;
}

.page-btn:disabled {
  cursor: not-allowed;
  color: #d9d9d9;
}

.page-info {
  color: #666;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 4px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #666;
}

.modal-body {
  padding: 24px;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
}

.detail-label {
  width: 100px;
  color: #666;
}

.modal-footer {
  padding: 10px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.btn {
  padding: 6px 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
}

.btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}
</style> 