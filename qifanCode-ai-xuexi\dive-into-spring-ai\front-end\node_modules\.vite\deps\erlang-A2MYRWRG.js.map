{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/erlang.js"], "sourcesContent": ["/////////////////////////////////////////////////////////////////////////////\n// constants\n\nvar typeWords = [\n  \"-type\", \"-spec\", \"-export_type\", \"-opaque\"];\n\nvar keywordWords = [\n  \"after\",\"begin\",\"catch\",\"case\",\"cond\",\"end\",\"fun\",\"if\",\n  \"let\",\"of\",\"query\",\"receive\",\"try\",\"when\"];\n\nvar separatorRE    = /[\\->,;]/;\nvar separatorWords = [\n  \"->\",\";\",\",\"];\n\nvar operatorAtomWords = [\n  \"and\",\"andalso\",\"band\",\"bnot\",\"bor\",\"bsl\",\"bsr\",\"bxor\",\n  \"div\",\"not\",\"or\",\"orelse\",\"rem\",\"xor\"];\n\nvar operatorSymbolRE    = /[\\+\\-\\*\\/<>=\\|:!]/;\nvar operatorSymbolWords = [\n  \"=\",\"+\",\"-\",\"*\",\"/\",\">\",\">=\",\"<\",\"=<\",\"=:=\",\"==\",\"=/=\",\"/=\",\"||\",\"<-\",\"!\"];\n\nvar openParenRE    = /[<\\(\\[\\{]/;\nvar openParenWords = [\n  \"<<\",\"(\",\"[\",\"{\"];\n\nvar closeParenRE    = /[>\\)\\]\\}]/;\nvar closeParenWords = [\n  \"}\",\"]\",\")\",\">>\"];\n\nvar guardWords = [\n  \"is_atom\",\"is_binary\",\"is_bitstring\",\"is_boolean\",\"is_float\",\n  \"is_function\",\"is_integer\",\"is_list\",\"is_number\",\"is_pid\",\n  \"is_port\",\"is_record\",\"is_reference\",\"is_tuple\",\n  \"atom\",\"binary\",\"bitstring\",\"boolean\",\"function\",\"integer\",\"list\",\n  \"number\",\"pid\",\"port\",\"record\",\"reference\",\"tuple\"];\n\nvar bifWords = [\n  \"abs\",\"adler32\",\"adler32_combine\",\"alive\",\"apply\",\"atom_to_binary\",\n  \"atom_to_list\",\"binary_to_atom\",\"binary_to_existing_atom\",\n  \"binary_to_list\",\"binary_to_term\",\"bit_size\",\"bitstring_to_list\",\n  \"byte_size\",\"check_process_code\",\"contact_binary\",\"crc32\",\n  \"crc32_combine\",\"date\",\"decode_packet\",\"delete_module\",\n  \"disconnect_node\",\"element\",\"erase\",\"exit\",\"float\",\"float_to_list\",\n  \"garbage_collect\",\"get\",\"get_keys\",\"group_leader\",\"halt\",\"hd\",\n  \"integer_to_list\",\"internal_bif\",\"iolist_size\",\"iolist_to_binary\",\n  \"is_alive\",\"is_atom\",\"is_binary\",\"is_bitstring\",\"is_boolean\",\n  \"is_float\",\"is_function\",\"is_integer\",\"is_list\",\"is_number\",\"is_pid\",\n  \"is_port\",\"is_process_alive\",\"is_record\",\"is_reference\",\"is_tuple\",\n  \"length\",\"link\",\"list_to_atom\",\"list_to_binary\",\"list_to_bitstring\",\n  \"list_to_existing_atom\",\"list_to_float\",\"list_to_integer\",\n  \"list_to_pid\",\"list_to_tuple\",\"load_module\",\"make_ref\",\"module_loaded\",\n  \"monitor_node\",\"node\",\"node_link\",\"node_unlink\",\"nodes\",\"notalive\",\n  \"now\",\"open_port\",\"pid_to_list\",\"port_close\",\"port_command\",\n  \"port_connect\",\"port_control\",\"pre_loaded\",\"process_flag\",\n  \"process_info\",\"processes\",\"purge_module\",\"put\",\"register\",\n  \"registered\",\"round\",\"self\",\"setelement\",\"size\",\"spawn\",\"spawn_link\",\n  \"spawn_monitor\",\"spawn_opt\",\"split_binary\",\"statistics\",\n  \"term_to_binary\",\"time\",\"throw\",\"tl\",\"trunc\",\"tuple_size\",\n  \"tuple_to_list\",\"unlink\",\"unregister\",\"whereis\"];\n\n// upper case: [A-Z] [Ø-Þ] [À-Ö]\n// lower case: [a-z] [ß-ö] [ø-ÿ]\nvar anumRE       = /[\\w@Ø-ÞÀ-Öß-öø-ÿ]/;\nvar escapesRE    =\n    /[0-7]{1,3}|[bdefnrstv\\\\\"']|\\^[a-zA-Z]|x[0-9a-zA-Z]{2}|x{[0-9a-zA-Z]+}/;\n\n/////////////////////////////////////////////////////////////////////////////\n// tokenizer\n\nfunction tokenizer(stream,state) {\n  // in multi-line string\n  if (state.in_string) {\n    state.in_string = (!doubleQuote(stream));\n    return rval(state,stream,\"string\");\n  }\n\n  // in multi-line atom\n  if (state.in_atom) {\n    state.in_atom = (!singleQuote(stream));\n    return rval(state,stream,\"atom\");\n  }\n\n  // whitespace\n  if (stream.eatSpace()) {\n    return rval(state,stream,\"whitespace\");\n  }\n\n  // attributes and type specs\n  if (!peekToken(state) &&\n      stream.match(/-\\s*[a-zß-öø-ÿ][\\wØ-ÞÀ-Öß-öø-ÿ]*/)) {\n    if (is_member(stream.current(),typeWords)) {\n      return rval(state,stream,\"type\");\n    }else{\n      return rval(state,stream,\"attribute\");\n    }\n  }\n\n  var ch = stream.next();\n\n  // comment\n  if (ch == '%') {\n    stream.skipToEnd();\n    return rval(state,stream,\"comment\");\n  }\n\n  // colon\n  if (ch == \":\") {\n    return rval(state,stream,\"colon\");\n  }\n\n  // macro\n  if (ch == '?') {\n    stream.eatSpace();\n    stream.eatWhile(anumRE);\n    return rval(state,stream,\"macro\");\n  }\n\n  // record\n  if (ch == \"#\") {\n    stream.eatSpace();\n    stream.eatWhile(anumRE);\n    return rval(state,stream,\"record\");\n  }\n\n  // dollar escape\n  if (ch == \"$\") {\n    if (stream.next() == \"\\\\\" && !stream.match(escapesRE)) {\n      return rval(state,stream,\"error\");\n    }\n    return rval(state,stream,\"number\");\n  }\n\n  // dot\n  if (ch == \".\") {\n    return rval(state,stream,\"dot\");\n  }\n\n  // quoted atom\n  if (ch == '\\'') {\n    if (!(state.in_atom = (!singleQuote(stream)))) {\n      if (stream.match(/\\s*\\/\\s*[0-9]/,false)) {\n        stream.match(/\\s*\\/\\s*[0-9]/,true);\n        return rval(state,stream,\"fun\");      // 'f'/0 style fun\n      }\n      if (stream.match(/\\s*\\(/,false) || stream.match(/\\s*:/,false)) {\n        return rval(state,stream,\"function\");\n      }\n    }\n    return rval(state,stream,\"atom\");\n  }\n\n  // string\n  if (ch == '\"') {\n    state.in_string = (!doubleQuote(stream));\n    return rval(state,stream,\"string\");\n  }\n\n  // variable\n  if (/[A-Z_Ø-ÞÀ-Ö]/.test(ch)) {\n    stream.eatWhile(anumRE);\n    return rval(state,stream,\"variable\");\n  }\n\n  // atom/keyword/BIF/function\n  if (/[a-z_ß-öø-ÿ]/.test(ch)) {\n    stream.eatWhile(anumRE);\n\n    if (stream.match(/\\s*\\/\\s*[0-9]/,false)) {\n      stream.match(/\\s*\\/\\s*[0-9]/,true);\n      return rval(state,stream,\"fun\");      // f/0 style fun\n    }\n\n    var w = stream.current();\n\n    if (is_member(w,keywordWords)) {\n      return rval(state,stream,\"keyword\");\n    }else if (is_member(w,operatorAtomWords)) {\n      return rval(state,stream,\"operator\");\n    }else if (stream.match(/\\s*\\(/,false)) {\n      // 'put' and 'erlang:put' are bifs, 'foo:put' is not\n      if (is_member(w,bifWords) &&\n          ((peekToken(state).token != \":\") ||\n           (peekToken(state,2).token == \"erlang\"))) {\n        return rval(state,stream,\"builtin\");\n      }else if (is_member(w,guardWords)) {\n        return rval(state,stream,\"guard\");\n      }else{\n        return rval(state,stream,\"function\");\n      }\n    }else if (lookahead(stream) == \":\") {\n      if (w == \"erlang\") {\n        return rval(state,stream,\"builtin\");\n      } else {\n        return rval(state,stream,\"function\");\n      }\n    }else if (is_member(w,[\"true\",\"false\"])) {\n      return rval(state,stream,\"boolean\");\n    }else{\n      return rval(state,stream,\"atom\");\n    }\n  }\n\n  // number\n  var digitRE      = /[0-9]/;\n  var radixRE      = /[0-9a-zA-Z]/;         // 36#zZ style int\n  if (digitRE.test(ch)) {\n    stream.eatWhile(digitRE);\n    if (stream.eat('#')) {                // 36#aZ  style integer\n      if (!stream.eatWhile(radixRE)) {\n        stream.backUp(1);                 //\"36#\" - syntax error\n      }\n    } else if (stream.eat('.')) {       // float\n      if (!stream.eatWhile(digitRE)) {\n        stream.backUp(1);        // \"3.\" - probably end of function\n      } else {\n        if (stream.eat(/[eE]/)) {        // float with exponent\n          if (stream.eat(/[-+]/)) {\n            if (!stream.eatWhile(digitRE)) {\n              stream.backUp(2);            // \"2e-\" - syntax error\n            }\n          } else {\n            if (!stream.eatWhile(digitRE)) {\n              stream.backUp(1);            // \"2e\" - syntax error\n            }\n          }\n        }\n      }\n    }\n    return rval(state,stream,\"number\");   // normal integer\n  }\n\n  // open parens\n  if (nongreedy(stream,openParenRE,openParenWords)) {\n    return rval(state,stream,\"open_paren\");\n  }\n\n  // close parens\n  if (nongreedy(stream,closeParenRE,closeParenWords)) {\n    return rval(state,stream,\"close_paren\");\n  }\n\n  // separators\n  if (greedy(stream,separatorRE,separatorWords)) {\n    return rval(state,stream,\"separator\");\n  }\n\n  // operators\n  if (greedy(stream,operatorSymbolRE,operatorSymbolWords)) {\n    return rval(state,stream,\"operator\");\n  }\n\n  return rval(state,stream,null);\n}\n\n/////////////////////////////////////////////////////////////////////////////\n// utilities\nfunction nongreedy(stream,re,words) {\n  if (stream.current().length == 1 && re.test(stream.current())) {\n    stream.backUp(1);\n    while (re.test(stream.peek())) {\n      stream.next();\n      if (is_member(stream.current(),words)) {\n        return true;\n      }\n    }\n    stream.backUp(stream.current().length-1);\n  }\n  return false;\n}\n\nfunction greedy(stream,re,words) {\n  if (stream.current().length == 1 && re.test(stream.current())) {\n    while (re.test(stream.peek())) {\n      stream.next();\n    }\n    while (0 < stream.current().length) {\n      if (is_member(stream.current(),words)) {\n        return true;\n      }else{\n        stream.backUp(1);\n      }\n    }\n    stream.next();\n  }\n  return false;\n}\n\nfunction doubleQuote(stream) {\n  return quote(stream, '\"', '\\\\');\n}\n\nfunction singleQuote(stream) {\n  return quote(stream,'\\'','\\\\');\n}\n\nfunction quote(stream,quoteChar,escapeChar) {\n  while (!stream.eol()) {\n    var ch = stream.next();\n    if (ch == quoteChar) {\n      return true;\n    }else if (ch == escapeChar) {\n      stream.next();\n    }\n  }\n  return false;\n}\n\nfunction lookahead(stream) {\n  var m = stream.match(/^\\s*([^\\s%])/, false)\n  return m ? m[1] : \"\";\n}\n\nfunction is_member(element,list) {\n  return (-1 < list.indexOf(element));\n}\n\nfunction rval(state,stream,type) {\n\n  // parse stack\n  pushToken(state,realToken(type,stream));\n\n  // map erlang token type to CodeMirror style class\n  //     erlang             -> CodeMirror tag\n  switch (type) {\n  case \"atom\":        return \"atom\";\n  case \"attribute\":   return \"attribute\";\n  case \"boolean\":     return \"atom\";\n  case \"builtin\":     return \"builtin\";\n  case \"close_paren\": return null;\n  case \"colon\":       return null;\n  case \"comment\":     return \"comment\";\n  case \"dot\":         return null;\n  case \"error\":       return \"error\";\n  case \"fun\":         return \"meta\";\n  case \"function\":    return \"tag\";\n  case \"guard\":       return \"property\";\n  case \"keyword\":     return \"keyword\";\n  case \"macro\":       return \"macroName\";\n  case \"number\":      return \"number\";\n  case \"open_paren\":  return null;\n  case \"operator\":    return \"operator\";\n  case \"record\":      return \"bracket\";\n  case \"separator\":   return null;\n  case \"string\":      return \"string\";\n  case \"type\":        return \"def\";\n  case \"variable\":    return \"variable\";\n  default:            return null;\n  }\n}\n\nfunction aToken(tok,col,ind,typ) {\n  return {token:  tok,\n          column: col,\n          indent: ind,\n          type:   typ};\n}\n\nfunction realToken(type,stream) {\n  return aToken(stream.current(),\n                stream.column(),\n                stream.indentation(),\n                type);\n}\n\nfunction fakeToken(type) {\n  return aToken(type,0,0,type);\n}\n\nfunction peekToken(state,depth) {\n  var len = state.tokenStack.length;\n  var dep = (depth ? depth : 1);\n\n  if (len < dep) {\n    return false;\n  }else{\n    return state.tokenStack[len-dep];\n  }\n}\n\nfunction pushToken(state,token) {\n\n  if (!(token.type == \"comment\" || token.type == \"whitespace\")) {\n    state.tokenStack = maybe_drop_pre(state.tokenStack,token);\n    state.tokenStack = maybe_drop_post(state.tokenStack);\n  }\n}\n\nfunction maybe_drop_pre(s,token) {\n  var last = s.length-1;\n\n  if (0 < last && s[last].type === \"record\" && token.type === \"dot\") {\n    s.pop();\n  }else if (0 < last && s[last].type === \"group\") {\n    s.pop();\n    s.push(token);\n  }else{\n    s.push(token);\n  }\n  return s;\n}\n\nfunction maybe_drop_post(s) {\n  if (!s.length) return s\n  var last = s.length-1;\n\n  if (s[last].type === \"dot\") {\n    return [];\n  }\n  if (last > 1 && s[last].type === \"fun\" && s[last-1].token === \"fun\") {\n    return s.slice(0,last-1);\n  }\n  switch (s[last].token) {\n  case \"}\":    return d(s,{g:[\"{\"]});\n  case \"]\":    return d(s,{i:[\"[\"]});\n  case \")\":    return d(s,{i:[\"(\"]});\n  case \">>\":   return d(s,{i:[\"<<\"]});\n  case \"end\":  return d(s,{i:[\"begin\",\"case\",\"fun\",\"if\",\"receive\",\"try\"]});\n  case \",\":    return d(s,{e:[\"begin\",\"try\",\"when\",\"->\",\n                              \",\",\"(\",\"[\",\"{\",\"<<\"]});\n  case \"->\":   return d(s,{r:[\"when\"],\n                           m:[\"try\",\"if\",\"case\",\"receive\"]});\n  case \";\":    return d(s,{E:[\"case\",\"fun\",\"if\",\"receive\",\"try\",\"when\"]});\n  case \"catch\":return d(s,{e:[\"try\"]});\n  case \"of\":   return d(s,{e:[\"case\"]});\n  case \"after\":return d(s,{e:[\"receive\",\"try\"]});\n  default:     return s;\n  }\n}\n\nfunction d(stack,tt) {\n  // stack is a stack of Token objects.\n  // tt is an object; {type:tokens}\n  // type is a char, tokens is a list of token strings.\n  // The function returns (possibly truncated) stack.\n  // It will descend the stack, looking for a Token such that Token.token\n  //  is a member of tokens. If it does not find that, it will normally (but\n  //  see \"E\" below) return stack. If it does find a match, it will remove\n  //  all the Tokens between the top and the matched Token.\n  // If type is \"m\", that is all it does.\n  // If type is \"i\", it will also remove the matched Token and the top Token.\n  // If type is \"g\", like \"i\", but add a fake \"group\" token at the top.\n  // If type is \"r\", it will remove the matched Token, but not the top Token.\n  // If type is \"e\", it will keep the matched Token but not the top Token.\n  // If type is \"E\", it behaves as for type \"e\", except if there is no match,\n  //  in which case it will return an empty stack.\n\n  for (var type in tt) {\n    var len = stack.length-1;\n    var tokens = tt[type];\n    for (var i = len-1; -1 < i ; i--) {\n      if (is_member(stack[i].token,tokens)) {\n        var ss = stack.slice(0,i);\n        switch (type) {\n        case \"m\": return ss.concat(stack[i]).concat(stack[len]);\n        case \"r\": return ss.concat(stack[len]);\n        case \"i\": return ss;\n        case \"g\": return ss.concat(fakeToken(\"group\"));\n        case \"E\": return ss.concat(stack[i]);\n        case \"e\": return ss.concat(stack[i]);\n        }\n      }\n    }\n  }\n  return (type == \"E\" ? [] : stack);\n}\n\n/////////////////////////////////////////////////////////////////////////////\n// indenter\n\nfunction indenter(state, textAfter, cx) {\n  var t;\n  var wordAfter = wordafter(textAfter);\n  var currT = peekToken(state,1);\n  var prevT = peekToken(state,2);\n\n  if (state.in_string || state.in_atom) {\n    return null;\n  }else if (!prevT) {\n    return 0;\n  }else if (currT.token == \"when\") {\n    return currT.column + cx.unit;\n  }else if (wordAfter === \"when\" && prevT.type === \"function\") {\n    return prevT.indent+cx.unit;\n  }else if (wordAfter === \"(\" && currT.token === \"fun\") {\n    return  currT.column+3;\n  }else if (wordAfter === \"catch\" && (t = getToken(state,[\"try\"]))) {\n    return t.column;\n  }else if (is_member(wordAfter,[\"end\",\"after\",\"of\"])) {\n    t = getToken(state,[\"begin\",\"case\",\"fun\",\"if\",\"receive\",\"try\"]);\n    return t ? t.column : null;\n  }else if (is_member(wordAfter,closeParenWords)) {\n    t = getToken(state,openParenWords);\n    return t ? t.column : null;\n  }else if (is_member(currT.token,[\",\",\"|\",\"||\"]) ||\n            is_member(wordAfter,[\",\",\"|\",\"||\"])) {\n    t = postcommaToken(state);\n    return t ? t.column+t.token.length : cx.unit;\n  }else if (currT.token == \"->\") {\n    if (is_member(prevT.token, [\"receive\",\"case\",\"if\",\"try\"])) {\n      return prevT.column+cx.unit+cx.unit;\n    }else{\n      return prevT.column+cx.unit;\n    }\n  }else if (is_member(currT.token,openParenWords)) {\n    return currT.column+currT.token.length;\n  }else{\n    t = defaultToken(state);\n    return truthy(t) ? t.column+cx.unit : 0;\n  }\n}\n\nfunction wordafter(str) {\n  var m = str.match(/,|[a-z]+|\\}|\\]|\\)|>>|\\|+|\\(/);\n\n  return truthy(m) && (m.index === 0) ? m[0] : \"\";\n}\n\nfunction postcommaToken(state) {\n  var objs = state.tokenStack.slice(0,-1);\n  var i = getTokenIndex(objs,\"type\",[\"open_paren\"]);\n\n  return truthy(objs[i]) ? objs[i] : false;\n}\n\nfunction defaultToken(state) {\n  var objs = state.tokenStack;\n  var stop = getTokenIndex(objs,\"type\",[\"open_paren\",\"separator\",\"keyword\"]);\n  var oper = getTokenIndex(objs,\"type\",[\"operator\"]);\n\n  if (truthy(stop) && truthy(oper) && stop < oper) {\n    return objs[stop+1];\n  } else if (truthy(stop)) {\n    return objs[stop];\n  } else {\n    return false;\n  }\n}\n\nfunction getToken(state,tokens) {\n  var objs = state.tokenStack;\n  var i = getTokenIndex(objs,\"token\",tokens);\n\n  return truthy(objs[i]) ? objs[i] : false;\n}\n\nfunction getTokenIndex(objs,propname,propvals) {\n\n  for (var i = objs.length-1; -1 < i ; i--) {\n    if (is_member(objs[i][propname],propvals)) {\n      return i;\n    }\n  }\n  return false;\n}\n\nfunction truthy(x) {\n  return (x !== false) && (x != null);\n}\n\n/////////////////////////////////////////////////////////////////////////////\n// this object defines the mode\n\nexport const erlang = {\n  name: \"erlang\",\n  startState() {\n    return {tokenStack: [],\n            in_string:  false,\n            in_atom:    false};\n  },\n\n  token: tokenizer,\n\n  indent: indenter,\n\n  languageData: {\n    commentTokens: {line: \"%\"}\n  }\n};\n\n"], "mappings": ";;;AAGA,IAAI,YAAY;AAAA,EACd;AAAA,EAAS;AAAA,EAAS;AAAA,EAAgB;AAAS;AAE7C,IAAI,eAAe;AAAA,EACjB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAClD;AAAA,EAAM;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAM;AAAM;AAE3C,IAAI,cAAiB;AACrB,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAK;AAAA,EAAI;AAAG;AAEd,IAAI,oBAAoB;AAAA,EACtB;AAAA,EAAM;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAChD;AAAA,EAAM;AAAA,EAAM;AAAA,EAAK;AAAA,EAAS;AAAA,EAAM;AAAK;AAEvC,IAAI,mBAAsB;AAC1B,IAAI,sBAAsB;AAAA,EACxB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAG;AAE3E,IAAI,cAAiB;AACrB,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAG;AAElB,IAAI,eAAkB;AACtB,IAAI,kBAAkB;AAAA,EACpB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAI;AAElB,IAAI,aAAa;AAAA,EACf;AAAA,EAAU;AAAA,EAAY;AAAA,EAAe;AAAA,EAAa;AAAA,EAClD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EACjD;AAAA,EAAU;AAAA,EAAY;AAAA,EAAe;AAAA,EACrC;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AAAA,EAAU;AAAA,EAC3D;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA,EAAY;AAAO;AAEpD,IAAI,WAAW;AAAA,EACb;AAAA,EAAM;AAAA,EAAU;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAClD;AAAA,EAAe;AAAA,EAAiB;AAAA,EAChC;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAW;AAAA,EAC7C;AAAA,EAAY;AAAA,EAAqB;AAAA,EAAiB;AAAA,EAClD;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAgB;AAAA,EACvC;AAAA,EAAkB;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EACnD;AAAA,EAAkB;AAAA,EAAM;AAAA,EAAW;AAAA,EAAe;AAAA,EAAO;AAAA,EACzD;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAc;AAAA,EAC/C;AAAA,EAAW;AAAA,EAAU;AAAA,EAAY;AAAA,EAAe;AAAA,EAChD;AAAA,EAAW;AAAA,EAAc;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAmB;AAAA,EAAY;AAAA,EAAe;AAAA,EACxD;AAAA,EAAS;AAAA,EAAO;AAAA,EAAe;AAAA,EAAiB;AAAA,EAChD;AAAA,EAAwB;AAAA,EAAgB;AAAA,EACxC;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAW;AAAA,EACvD;AAAA,EAAe;AAAA,EAAO;AAAA,EAAY;AAAA,EAAc;AAAA,EAAQ;AAAA,EACxD;AAAA,EAAM;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAC7C;AAAA,EAAe;AAAA,EAAe;AAAA,EAAa;AAAA,EAC3C;AAAA,EAAe;AAAA,EAAY;AAAA,EAAe;AAAA,EAAM;AAAA,EAChD;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAa;AAAA,EAAO;AAAA,EAAQ;AAAA,EACxD;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAe;AAAA,EAC3C;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAQ;AAAA,EAC7C;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAa;AAAS;AAIjD,IAAI,SAAe;AACnB,IAAI,YACA;AAKJ,SAAS,UAAU,QAAO,OAAO;AAE/B,MAAI,MAAM,WAAW;AACnB,UAAM,YAAa,CAAC,YAAY,MAAM;AACtC,WAAO,KAAK,OAAM,QAAO,QAAQ;AAAA,EACnC;AAGA,MAAI,MAAM,SAAS;AACjB,UAAM,UAAW,CAAC,YAAY,MAAM;AACpC,WAAO,KAAK,OAAM,QAAO,MAAM;AAAA,EACjC;AAGA,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO,KAAK,OAAM,QAAO,YAAY;AAAA,EACvC;AAGA,MAAI,CAAC,UAAU,KAAK,KAChB,OAAO,MAAM,kCAAkC,GAAG;AACpD,QAAI,UAAU,OAAO,QAAQ,GAAE,SAAS,GAAG;AACzC,aAAO,KAAK,OAAM,QAAO,MAAM;AAAA,IACjC,OAAK;AACH,aAAO,KAAK,OAAM,QAAO,WAAW;AAAA,IACtC;AAAA,EACF;AAEA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,MAAM,KAAK;AACb,WAAO,UAAU;AACjB,WAAO,KAAK,OAAM,QAAO,SAAS;AAAA,EACpC;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,KAAK,OAAM,QAAO,OAAO;AAAA,EAClC;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,SAAS;AAChB,WAAO,SAAS,MAAM;AACtB,WAAO,KAAK,OAAM,QAAO,OAAO;AAAA,EAClC;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,SAAS;AAChB,WAAO,SAAS,MAAM;AACtB,WAAO,KAAK,OAAM,QAAO,QAAQ;AAAA,EACnC;AAGA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,OAAO,MAAM,SAAS,GAAG;AACrD,aAAO,KAAK,OAAM,QAAO,OAAO;AAAA,IAClC;AACA,WAAO,KAAK,OAAM,QAAO,QAAQ;AAAA,EACnC;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,KAAK,OAAM,QAAO,KAAK;AAAA,EAChC;AAGA,MAAI,MAAM,KAAM;AACd,QAAI,EAAE,MAAM,UAAW,CAAC,YAAY,MAAM,IAAK;AAC7C,UAAI,OAAO,MAAM,iBAAgB,KAAK,GAAG;AACvC,eAAO,MAAM,iBAAgB,IAAI;AACjC,eAAO,KAAK,OAAM,QAAO,KAAK;AAAA,MAChC;AACA,UAAI,OAAO,MAAM,SAAQ,KAAK,KAAK,OAAO,MAAM,QAAO,KAAK,GAAG;AAC7D,eAAO,KAAK,OAAM,QAAO,UAAU;AAAA,MACrC;AAAA,IACF;AACA,WAAO,KAAK,OAAM,QAAO,MAAM;AAAA,EACjC;AAGA,MAAI,MAAM,KAAK;AACb,UAAM,YAAa,CAAC,YAAY,MAAM;AACtC,WAAO,KAAK,OAAM,QAAO,QAAQ;AAAA,EACnC;AAGA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,MAAM;AACtB,WAAO,KAAK,OAAM,QAAO,UAAU;AAAA,EACrC;AAGA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,MAAM;AAEtB,QAAI,OAAO,MAAM,iBAAgB,KAAK,GAAG;AACvC,aAAO,MAAM,iBAAgB,IAAI;AACjC,aAAO,KAAK,OAAM,QAAO,KAAK;AAAA,IAChC;AAEA,QAAI,IAAI,OAAO,QAAQ;AAEvB,QAAI,UAAU,GAAE,YAAY,GAAG;AAC7B,aAAO,KAAK,OAAM,QAAO,SAAS;AAAA,IACpC,WAAU,UAAU,GAAE,iBAAiB,GAAG;AACxC,aAAO,KAAK,OAAM,QAAO,UAAU;AAAA,IACrC,WAAU,OAAO,MAAM,SAAQ,KAAK,GAAG;AAErC,UAAI,UAAU,GAAE,QAAQ,MAClB,UAAU,KAAK,EAAE,SAAS,OAC1B,UAAU,OAAM,CAAC,EAAE,SAAS,WAAY;AAC5C,eAAO,KAAK,OAAM,QAAO,SAAS;AAAA,MACpC,WAAU,UAAU,GAAE,UAAU,GAAG;AACjC,eAAO,KAAK,OAAM,QAAO,OAAO;AAAA,MAClC,OAAK;AACH,eAAO,KAAK,OAAM,QAAO,UAAU;AAAA,MACrC;AAAA,IACF,WAAU,UAAU,MAAM,KAAK,KAAK;AAClC,UAAI,KAAK,UAAU;AACjB,eAAO,KAAK,OAAM,QAAO,SAAS;AAAA,MACpC,OAAO;AACL,eAAO,KAAK,OAAM,QAAO,UAAU;AAAA,MACrC;AAAA,IACF,WAAU,UAAU,GAAE,CAAC,QAAO,OAAO,CAAC,GAAG;AACvC,aAAO,KAAK,OAAM,QAAO,SAAS;AAAA,IACpC,OAAK;AACH,aAAO,KAAK,OAAM,QAAO,MAAM;AAAA,IACjC;AAAA,EACF;AAGA,MAAI,UAAe;AACnB,MAAI,UAAe;AACnB,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,WAAO,SAAS,OAAO;AACvB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,UAAI,CAAC,OAAO,SAAS,OAAO,GAAG;AAC7B,eAAO,OAAO,CAAC;AAAA,MACjB;AAAA,IACF,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,UAAI,CAAC,OAAO,SAAS,OAAO,GAAG;AAC7B,eAAO,OAAO,CAAC;AAAA,MACjB,OAAO;AACL,YAAI,OAAO,IAAI,MAAM,GAAG;AACtB,cAAI,OAAO,IAAI,MAAM,GAAG;AACtB,gBAAI,CAAC,OAAO,SAAS,OAAO,GAAG;AAC7B,qBAAO,OAAO,CAAC;AAAA,YACjB;AAAA,UACF,OAAO;AACL,gBAAI,CAAC,OAAO,SAAS,OAAO,GAAG;AAC7B,qBAAO,OAAO,CAAC;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,OAAM,QAAO,QAAQ;AAAA,EACnC;AAGA,MAAI,UAAU,QAAO,aAAY,cAAc,GAAG;AAChD,WAAO,KAAK,OAAM,QAAO,YAAY;AAAA,EACvC;AAGA,MAAI,UAAU,QAAO,cAAa,eAAe,GAAG;AAClD,WAAO,KAAK,OAAM,QAAO,aAAa;AAAA,EACxC;AAGA,MAAI,OAAO,QAAO,aAAY,cAAc,GAAG;AAC7C,WAAO,KAAK,OAAM,QAAO,WAAW;AAAA,EACtC;AAGA,MAAI,OAAO,QAAO,kBAAiB,mBAAmB,GAAG;AACvD,WAAO,KAAK,OAAM,QAAO,UAAU;AAAA,EACrC;AAEA,SAAO,KAAK,OAAM,QAAO,IAAI;AAC/B;AAIA,SAAS,UAAU,QAAO,IAAG,OAAO;AAClC,MAAI,OAAO,QAAQ,EAAE,UAAU,KAAK,GAAG,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7D,WAAO,OAAO,CAAC;AACf,WAAO,GAAG,KAAK,OAAO,KAAK,CAAC,GAAG;AAC7B,aAAO,KAAK;AACZ,UAAI,UAAU,OAAO,QAAQ,GAAE,KAAK,GAAG;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,OAAO,OAAO,QAAQ,EAAE,SAAO,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AAEA,SAAS,OAAO,QAAO,IAAG,OAAO;AAC/B,MAAI,OAAO,QAAQ,EAAE,UAAU,KAAK,GAAG,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7D,WAAO,GAAG,KAAK,OAAO,KAAK,CAAC,GAAG;AAC7B,aAAO,KAAK;AAAA,IACd;AACA,WAAO,IAAI,OAAO,QAAQ,EAAE,QAAQ;AAClC,UAAI,UAAU,OAAO,QAAQ,GAAE,KAAK,GAAG;AACrC,eAAO;AAAA,MACT,OAAK;AACH,eAAO,OAAO,CAAC;AAAA,MACjB;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,YAAY,QAAQ;AAC3B,SAAO,MAAM,QAAQ,KAAK,IAAI;AAChC;AAEA,SAAS,YAAY,QAAQ;AAC3B,SAAO,MAAM,QAAO,KAAK,IAAI;AAC/B;AAEA,SAAS,MAAM,QAAO,WAAU,YAAY;AAC1C,SAAO,CAAC,OAAO,IAAI,GAAG;AACpB,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,WAAW;AACnB,aAAO;AAAA,IACT,WAAU,MAAM,YAAY;AAC1B,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ;AACzB,MAAI,IAAI,OAAO,MAAM,gBAAgB,KAAK;AAC1C,SAAO,IAAI,EAAE,CAAC,IAAI;AACpB;AAEA,SAAS,UAAU,SAAQ,MAAM;AAC/B,SAAQ,KAAK,KAAK,QAAQ,OAAO;AACnC;AAEA,SAAS,KAAK,OAAM,QAAO,MAAM;AAG/B,YAAU,OAAM,UAAU,MAAK,MAAM,CAAC;AAItC,UAAQ,MAAM;AAAA,IACd,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B,KAAK;AAAe,aAAO;AAAA,IAC3B;AAAoB,aAAO;AAAA,EAC3B;AACF;AAEA,SAAS,OAAO,KAAI,KAAI,KAAI,KAAK;AAC/B,SAAO;AAAA,IAAC,OAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAQ;AAAA,EAAG;AACrB;AAEA,SAAS,UAAU,MAAK,QAAQ;AAC9B,SAAO;AAAA,IAAO,OAAO,QAAQ;AAAA,IACf,OAAO,OAAO;AAAA,IACd,OAAO,YAAY;AAAA,IACnB;AAAA,EAAI;AACpB;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,OAAO,MAAK,GAAE,GAAE,IAAI;AAC7B;AAEA,SAAS,UAAU,OAAM,OAAO;AAC9B,MAAI,MAAM,MAAM,WAAW;AAC3B,MAAI,MAAO,QAAQ,QAAQ;AAE3B,MAAI,MAAM,KAAK;AACb,WAAO;AAAA,EACT,OAAK;AACH,WAAO,MAAM,WAAW,MAAI,GAAG;AAAA,EACjC;AACF;AAEA,SAAS,UAAU,OAAM,OAAO;AAE9B,MAAI,EAAE,MAAM,QAAQ,aAAa,MAAM,QAAQ,eAAe;AAC5D,UAAM,aAAa,eAAe,MAAM,YAAW,KAAK;AACxD,UAAM,aAAa,gBAAgB,MAAM,UAAU;AAAA,EACrD;AACF;AAEA,SAAS,eAAe,GAAE,OAAO;AAC/B,MAAI,OAAO,EAAE,SAAO;AAEpB,MAAI,IAAI,QAAQ,EAAE,IAAI,EAAE,SAAS,YAAY,MAAM,SAAS,OAAO;AACjE,MAAE,IAAI;AAAA,EACR,WAAU,IAAI,QAAQ,EAAE,IAAI,EAAE,SAAS,SAAS;AAC9C,MAAE,IAAI;AACN,MAAE,KAAK,KAAK;AAAA,EACd,OAAK;AACH,MAAE,KAAK,KAAK;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,GAAG;AAC1B,MAAI,CAAC,EAAE;AAAQ,WAAO;AACtB,MAAI,OAAO,EAAE,SAAO;AAEpB,MAAI,EAAE,IAAI,EAAE,SAAS,OAAO;AAC1B,WAAO,CAAC;AAAA,EACV;AACA,MAAI,OAAO,KAAK,EAAE,IAAI,EAAE,SAAS,SAAS,EAAE,OAAK,CAAC,EAAE,UAAU,OAAO;AACnE,WAAO,EAAE,MAAM,GAAE,OAAK,CAAC;AAAA,EACzB;AACA,UAAQ,EAAE,IAAI,EAAE,OAAO;AAAA,IACvB,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,GAAG,EAAC,CAAC;AAAA,IACjC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,GAAG,EAAC,CAAC;AAAA,IACjC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,GAAG,EAAC,CAAC;AAAA,IACjC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,IAAI,EAAC,CAAC;AAAA,IAClC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,SAAQ,QAAO,OAAM,MAAK,WAAU,KAAK,EAAC,CAAC;AAAA,IACvE,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAO;AAAA,QACrB;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,MAAI,EAAC,CAAC;AAAA,IAClD,KAAK;AAAQ,aAAO,EAAE,GAAE;AAAA,QAAC,GAAE,CAAC,MAAM;AAAA,QACT,GAAE,CAAC,OAAM,MAAK,QAAO,SAAS;AAAA,MAAC,CAAC;AAAA,IACzD,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,QAAO,OAAM,MAAK,WAAU,OAAM,MAAM,EAAC,CAAC;AAAA,IACtE,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,KAAK,EAAC,CAAC;AAAA,IACnC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,MAAM,EAAC,CAAC;AAAA,IACpC,KAAK;AAAQ,aAAO,EAAE,GAAE,EAAC,GAAE,CAAC,WAAU,KAAK,EAAC,CAAC;AAAA,IAC7C;AAAa,aAAO;AAAA,EACpB;AACF;AAEA,SAAS,EAAE,OAAM,IAAI;AAiBnB,WAAS,QAAQ,IAAI;AACnB,QAAI,MAAM,MAAM,SAAO;AACvB,QAAI,SAAS,GAAG,IAAI;AACpB,aAAS,IAAI,MAAI,GAAG,KAAK,GAAI,KAAK;AAChC,UAAI,UAAU,MAAM,CAAC,EAAE,OAAM,MAAM,GAAG;AACpC,YAAI,KAAK,MAAM,MAAM,GAAE,CAAC;AACxB,gBAAQ,MAAM;AAAA,UACd,KAAK;AAAK,mBAAO,GAAG,OAAO,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,GAAG,CAAC;AAAA,UACtD,KAAK;AAAK,mBAAO,GAAG,OAAO,MAAM,GAAG,CAAC;AAAA,UACrC,KAAK;AAAK,mBAAO;AAAA,UACjB,KAAK;AAAK,mBAAO,GAAG,OAAO,UAAU,OAAO,CAAC;AAAA,UAC7C,KAAK;AAAK,mBAAO,GAAG,OAAO,MAAM,CAAC,CAAC;AAAA,UACnC,KAAK;AAAK,mBAAO,GAAG,OAAO,MAAM,CAAC,CAAC;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAQ,QAAQ,MAAM,CAAC,IAAI;AAC7B;AAKA,SAAS,SAAS,OAAO,WAAW,IAAI;AACtC,MAAI;AACJ,MAAI,YAAY,UAAU,SAAS;AACnC,MAAI,QAAQ,UAAU,OAAM,CAAC;AAC7B,MAAI,QAAQ,UAAU,OAAM,CAAC;AAE7B,MAAI,MAAM,aAAa,MAAM,SAAS;AACpC,WAAO;AAAA,EACT,WAAU,CAAC,OAAO;AAChB,WAAO;AAAA,EACT,WAAU,MAAM,SAAS,QAAQ;AAC/B,WAAO,MAAM,SAAS,GAAG;AAAA,EAC3B,WAAU,cAAc,UAAU,MAAM,SAAS,YAAY;AAC3D,WAAO,MAAM,SAAO,GAAG;AAAA,EACzB,WAAU,cAAc,OAAO,MAAM,UAAU,OAAO;AACpD,WAAQ,MAAM,SAAO;AAAA,EACvB,WAAU,cAAc,YAAY,IAAI,SAAS,OAAM,CAAC,KAAK,CAAC,IAAI;AAChE,WAAO,EAAE;AAAA,EACX,WAAU,UAAU,WAAU,CAAC,OAAM,SAAQ,IAAI,CAAC,GAAG;AACnD,QAAI,SAAS,OAAM,CAAC,SAAQ,QAAO,OAAM,MAAK,WAAU,KAAK,CAAC;AAC9D,WAAO,IAAI,EAAE,SAAS;AAAA,EACxB,WAAU,UAAU,WAAU,eAAe,GAAG;AAC9C,QAAI,SAAS,OAAM,cAAc;AACjC,WAAO,IAAI,EAAE,SAAS;AAAA,EACxB,WAAU,UAAU,MAAM,OAAM,CAAC,KAAI,KAAI,IAAI,CAAC,KACpC,UAAU,WAAU,CAAC,KAAI,KAAI,IAAI,CAAC,GAAG;AAC7C,QAAI,eAAe,KAAK;AACxB,WAAO,IAAI,EAAE,SAAO,EAAE,MAAM,SAAS,GAAG;AAAA,EAC1C,WAAU,MAAM,SAAS,MAAM;AAC7B,QAAI,UAAU,MAAM,OAAO,CAAC,WAAU,QAAO,MAAK,KAAK,CAAC,GAAG;AACzD,aAAO,MAAM,SAAO,GAAG,OAAK,GAAG;AAAA,IACjC,OAAK;AACH,aAAO,MAAM,SAAO,GAAG;AAAA,IACzB;AAAA,EACF,WAAU,UAAU,MAAM,OAAM,cAAc,GAAG;AAC/C,WAAO,MAAM,SAAO,MAAM,MAAM;AAAA,EAClC,OAAK;AACH,QAAI,aAAa,KAAK;AACtB,WAAO,OAAO,CAAC,IAAI,EAAE,SAAO,GAAG,OAAO;AAAA,EACxC;AACF;AAEA,SAAS,UAAU,KAAK;AACtB,MAAI,IAAI,IAAI,MAAM,6BAA6B;AAE/C,SAAO,OAAO,CAAC,KAAM,EAAE,UAAU,IAAK,EAAE,CAAC,IAAI;AAC/C;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI,OAAO,MAAM,WAAW,MAAM,GAAE,EAAE;AACtC,MAAI,IAAI,cAAc,MAAK,QAAO,CAAC,YAAY,CAAC;AAEhD,SAAO,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AACrC;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAI,OAAO,MAAM;AACjB,MAAI,OAAO,cAAc,MAAK,QAAO,CAAC,cAAa,aAAY,SAAS,CAAC;AACzE,MAAI,OAAO,cAAc,MAAK,QAAO,CAAC,UAAU,CAAC;AAEjD,MAAI,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM;AAC/C,WAAO,KAAK,OAAK,CAAC;AAAA,EACpB,WAAW,OAAO,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI;AAAA,EAClB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,OAAM,QAAQ;AAC9B,MAAI,OAAO,MAAM;AACjB,MAAI,IAAI,cAAc,MAAK,SAAQ,MAAM;AAEzC,SAAO,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;AACrC;AAEA,SAAS,cAAc,MAAK,UAAS,UAAU;AAE7C,WAAS,IAAI,KAAK,SAAO,GAAG,KAAK,GAAI,KAAK;AACxC,QAAI,UAAU,KAAK,CAAC,EAAE,QAAQ,GAAE,QAAQ,GAAG;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,OAAO,GAAG;AACjB,SAAQ,MAAM,SAAW,KAAK;AAChC;AAKO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,aAAa;AACX,WAAO;AAAA,MAAC,YAAY,CAAC;AAAA,MACb,WAAY;AAAA,MACZ,SAAY;AAAA,IAAK;AAAA,EAC3B;AAAA,EAEA,OAAO;AAAA,EAEP,QAAQ;AAAA,EAER,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}