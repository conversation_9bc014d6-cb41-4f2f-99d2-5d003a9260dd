package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.statistics.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 管理员统计服务接口
 */
public interface AdminStatisticsService {

    /**
     * 获取平台概览统计数据
     * 
     * @return 概览统计数据
     */
    OverviewStatisticsDTO getOverviewStatistics();

    /**
     * 获取用户统计数据
     * 
     * @param days 统计天数
     * @return 用户统计数据
     */
    UserStatisticsDTO getUserStatistics(Integer days);

    /**
     * 获取商品统计数据
     * 
     * @param days 统计天数
     * @return 商品统计数据
     */
    ProductStatisticsDTO getProductStatistics(Integer days);

    /**
     * 获取订单统计数据
     * 
     * @param days 统计天数
     * @return 订单统计数据
     */
    OrderStatisticsDTO getOrderStatistics(Integer days);

    /**
     * 获取论坛统计数据
     * 
     * @param days 统计天数
     * @return 论坛统计数据
     */
    ForumStatisticsDTO getForumStatistics(Integer days);

    /**
     * 获取热门商品排行
     * 
     * @param limit 排行数量
     * @param type 排行类型
     * @return 热门商品列表
     */
    List<HotProductDTO> getHotProducts(Integer limit, String type);

    /**
     * 获取用户地域分布统计
     * 
     * @return 地域分布数据
     */
    List<LocationStatisticsDTO> getUserLocationStatistics();

    /**
     * 获取实时统计数据
     * 
     * @return 实时统计数据
     */
    RealtimeStatisticsDTO getRealtimeStatistics();

    /**
     * 获取自定义时间范围的统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型
     * @return 自定义统计数据
     */
    Map<String, Object> getCustomStatistics(LocalDate startDate, LocalDate endDate, String type);
}
