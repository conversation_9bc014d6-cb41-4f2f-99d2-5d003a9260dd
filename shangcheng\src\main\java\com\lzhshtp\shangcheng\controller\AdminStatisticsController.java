package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.statistics.*;
import com.lzhshtp.shangcheng.service.AdminStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 管理员数据统计控制器
 */
@RestController
@RequestMapping("/api/admin/statistics")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminStatisticsController {

    private final AdminStatisticsService statisticsService;

    /**
     * 获取平台概览统计数据
     * 
     * @return 概览统计数据
     */
    @GetMapping("/overview")
    public ApiResponse<OverviewStatisticsDTO> getOverviewStatistics() {
        try {
            OverviewStatisticsDTO overview = statisticsService.getOverviewStatistics();
            return ApiResponse.success(overview);
        } catch (Exception e) {
            log.error("获取概览统计数据失败", e);
            return ApiResponse.fail("获取概览统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户统计数据
     * 
     * @param days 统计天数，默认30天
     * @return 用户统计数据
     */
    @GetMapping("/users")
    public ApiResponse<UserStatisticsDTO> getUserStatistics(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            UserStatisticsDTO userStats = statisticsService.getUserStatistics(days);
            return ApiResponse.success(userStats);
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            return ApiResponse.fail("获取用户统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品统计数据
     * 
     * @param days 统计天数，默认30天
     * @return 商品统计数据
     */
    @GetMapping("/products")
    public ApiResponse<ProductStatisticsDTO> getProductStatistics(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            ProductStatisticsDTO productStats = statisticsService.getProductStatistics(days);
            return ApiResponse.success(productStats);
        } catch (Exception e) {
            log.error("获取商品统计数据失败", e);
            return ApiResponse.fail("获取商品统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单统计数据
     * 
     * @param days 统计天数，默认30天
     * @return 订单统计数据
     */
    @GetMapping("/orders")
    public ApiResponse<OrderStatisticsDTO> getOrderStatistics(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            OrderStatisticsDTO orderStats = statisticsService.getOrderStatistics(days);
            return ApiResponse.success(orderStats);
        } catch (Exception e) {
            log.error("获取订单统计数据失败", e);
            return ApiResponse.fail("获取订单统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取论坛统计数据
     * 
     * @param days 统计天数，默认30天
     * @return 论坛统计数据
     */
    @GetMapping("/forum")
    public ApiResponse<ForumStatisticsDTO> getForumStatistics(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            ForumStatisticsDTO forumStats = statisticsService.getForumStatistics(days);
            return ApiResponse.success(forumStats);
        } catch (Exception e) {
            log.error("获取论坛统计数据失败", e);
            return ApiResponse.fail("获取论坛统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门商品排行
     * 
     * @param limit 排行数量，默认10
     * @param type 排行类型：views(浏览量)、favorites(收藏量)、orders(订单量)
     * @return 热门商品排行
     */
    @GetMapping("/hot-products")
    public ApiResponse<List<HotProductDTO>> getHotProducts(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(defaultValue = "views") String type) {
        try {
            List<HotProductDTO> hotProducts = statisticsService.getHotProducts(limit, type);
            return ApiResponse.success(hotProducts);
        } catch (Exception e) {
            log.error("获取热门商品排行失败", e);
            return ApiResponse.fail("获取热门商品排行失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户地域分布统计
     * 
     * @return 地域分布数据
     */
    @GetMapping("/user-locations")
    public ApiResponse<List<LocationStatisticsDTO>> getUserLocationStatistics() {
        try {
            List<LocationStatisticsDTO> locationStats = statisticsService.getUserLocationStatistics();
            return ApiResponse.success(locationStats);
        } catch (Exception e) {
            log.error("获取用户地域分布统计失败", e);
            return ApiResponse.fail("获取用户地域分布统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时统计数据（用于Dashboard实时更新）
     * 
     * @return 实时统计数据
     */
    @GetMapping("/realtime")
    public ApiResponse<RealtimeStatisticsDTO> getRealtimeStatistics() {
        try {
            RealtimeStatisticsDTO realtimeStats = statisticsService.getRealtimeStatistics();
            return ApiResponse.success(realtimeStats);
        } catch (Exception e) {
            log.error("获取实时统计数据失败", e);
            return ApiResponse.fail("获取实时统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取自定义时间范围的统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param type 统计类型：users、products、orders、forum
     * @return 自定义统计数据
     */
    @GetMapping("/custom")
    public ApiResponse<Map<String, Object>> getCustomStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam String type) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            Map<String, Object> customStats = statisticsService.getCustomStatistics(start, end, type);
            return ApiResponse.success(customStats);
        } catch (Exception e) {
            log.error("获取自定义统计数据失败", e);
            return ApiResponse.fail("获取自定义统计数据失败: " + e.getMessage());
        }
    }
}
