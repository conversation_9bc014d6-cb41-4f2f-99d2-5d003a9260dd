package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户详情数据传输对象，包含统计数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailDTO {
    // 基本用户信息
    private Long userId;
    private String username;
    private String email;
    private String phoneNumber;
    private String role;
    private Boolean isActive;
    private LocalDateTime registrationDate;
    private LocalDateTime lastLoginDate;
    private Integer creditScore;
    private String avatarUrl;
    private String location;
    private String bio;
    private BigDecimal balance;

    // 统计数据
    private Integer productCount;      // 发布商品数量
    private Integer soldProductCount;  // 已售出商品数量
    private Integer buyOrderCount;     // 购买订单数量
    private Integer sellOrderCount;    // 销售订单数量
    private Integer forumPostCount;    // 论坛发帖数量
    private Integer forumCommentCount; // 论坛评论数量
} 