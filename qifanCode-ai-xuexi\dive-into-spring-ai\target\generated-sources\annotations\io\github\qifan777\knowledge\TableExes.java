package io.github.qifan777.knowledge;

import io.github.qifan777.knowledge.ai.messge.AiMessageTableEx;
import io.github.qifan777.knowledge.ai.session.AiSessionTableEx;
import io.github.qifan777.knowledge.user.UserTableEx;
import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface TableExes {
    AiMessageTableEx AI_MESSAGE_TABLE_EX = AiMessageTableEx.$;

    AiSessionTableEx AI_SESSION_TABLE_EX = AiSessionTableEx.$;

    UserTableEx USER_TABLE_EX = UserTableEx.$;
}
