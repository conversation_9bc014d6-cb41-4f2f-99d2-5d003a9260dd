import request from '@/utils/request';

// 获取所有分类（树形结构）
export function getCategoryTree() {
  return request({
    url: '/categories/tree',
    method: 'get'
  });
}

// 获取所有分类（平铺结构）
export function getAllCategories() {
  return request({
    url: '/categories',
    method: 'get'
  });
}

// 根据ID获取分类
export function getCategoryById(id) {
  return request({
    url: `/categories/${id}`,
    method: 'get'
  });
} 