package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.dto.UserDTO;
import com.lzhshtp.shangcheng.model.User;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

/**
 * 用户数据访问层接口
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    @Select("SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_username = #{username}")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "passwordHash", column = "lzhshtp_password_hash"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "bio", column = "lzhshtp_bio"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role"),
        @Result(property = "balance", column = "lzhshtp_balance"),
        @Result(property = "canPublishProduct", column = "lzhshtp_can_publish_product")
    })
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    @Select("SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_email = #{email}")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "passwordHash", column = "lzhshtp_password_hash"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "bio", column = "lzhshtp_bio"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role"),
        @Result(property = "balance", column = "lzhshtp_balance"),
        @Result(property = "canPublishProduct", column = "lzhshtp_can_publish_product")
    })
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     *
     * @param phoneNumber 手机号
     * @return 用户对象
     */
    @Select("SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_phone_number = #{phoneNumber}")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "passwordHash", column = "lzhshtp_password_hash"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "bio", column = "lzhshtp_bio"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role"),
        @Result(property = "balance", column = "lzhshtp_balance"),
        @Result(property = "canPublishProduct", column = "lzhshtp_can_publish_product")
    })
    User findByPhoneNumber(String phoneNumber);

    /**
     * 自定义根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @Select("SELECT * FROM tb_lzhshtp_users WHERE lzhshtp_user_id = #{id}")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "passwordHash", column = "lzhshtp_password_hash"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "bio", column = "lzhshtp_bio"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role"),
        @Result(property = "balance", column = "lzhshtp_balance"),
        @Result(property = "canPublishProduct", column = "lzhshtp_can_publish_product")
    })
    User selectById(@org.apache.ibatis.annotations.Param("id") Long id);

    /**
     * 动态SQL提供者，用于构建用户列表查询SQL
     */
    class UserSqlProvider {
        /**
         * 构建查询用户列表的SQL
         */
        public String getUserList(final @Param("keyword") String keyword,
                                  final @Param("role") String role,
                                  final @Param("isActive") Boolean isActive) {
            return new SQL() {{
                SELECT("lzhshtp_user_id, lzhshtp_username, lzhshtp_email, lzhshtp_phone_number, " +
                       "lzhshtp_avatar_url, lzhshtp_registration_date, lzhshtp_last_login_date, " +
                       "lzhshtp_credit_score, lzhshtp_location, lzhshtp_is_active, lzhshtp_role");
                FROM("tb_lzhshtp_users");
                WHERE("lzhshtp_role != 'admin'");

                if (keyword != null && !keyword.trim().isEmpty()) {
                    AND();
                    WHERE("(" +
                          "lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_email LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_phone_number LIKE CONCAT('%', #{keyword}, '%')" +
                          (isNumeric(keyword) ? " OR lzhshtp_user_id = " + keyword : "") +
                          ")");
                }

                if (role != null && !role.trim().isEmpty()) {
                    AND();
                    WHERE("lzhshtp_role = #{role}");
                }

                if (isActive != null) {
                    AND();
                    WHERE("lzhshtp_is_active = #{isActive}");
                }

                ORDER_BY("lzhshtp_registration_date DESC");
            }}.toString();
        }

        /**
         * 构建统计用户总数的SQL
         */
        public String countUsers(final @Param("keyword") String keyword,
                                final @Param("role") String role,
                                final @Param("isActive") Boolean isActive) {
            return new SQL() {{
                SELECT("COUNT(*)");
                FROM("tb_lzhshtp_users");
                WHERE("lzhshtp_role != 'admin'");

                if (keyword != null && !keyword.trim().isEmpty()) {
                    AND();
                    WHERE("(" +
                          "lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_email LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_phone_number LIKE CONCAT('%', #{keyword}, '%')" +
                          (isNumeric(keyword) ? " OR lzhshtp_user_id = " + keyword : "") +
                          ")");
                }

                if (role != null && !role.trim().isEmpty()) {
                    AND();
                    WHERE("lzhshtp_role = #{role}");
                }

                if (isActive != null) {
                    AND();
                    WHERE("lzhshtp_is_active = #{isActive}");
                }
            }}.toString();
        }

        /**
         * 构建查询管理员列表的SQL（不排除admin）
         */
        public String getAdminList(final @Param("keyword") String keyword,
                                   final @Param("isActive") Boolean isActive,
                                   final @Param("userId") Long userId,
                                   final @Param("offset") int offset,
                                   final @Param("pageSize") int pageSize) {
            return new SQL() {{
                SELECT("lzhshtp_user_id, lzhshtp_username, lzhshtp_email, lzhshtp_phone_number, " +
                       "lzhshtp_avatar_url, lzhshtp_registration_date, lzhshtp_last_login_date, " +
                       "lzhshtp_credit_score, lzhshtp_location, lzhshtp_is_active, lzhshtp_role");
                FROM("tb_lzhshtp_users");
                WHERE("lzhshtp_role = 'admin'");

                if (keyword != null && !keyword.trim().isEmpty()) {
                    AND();
                    WHERE("(" +
                          "lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_email LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_phone_number LIKE CONCAT('%', #{keyword}, '%')" +
                          (isNumeric(keyword) ? " OR lzhshtp_user_id = " + keyword : "") +
                          ")");
                }

                if (isActive != null) {
                    AND();
                    WHERE("lzhshtp_is_active = #{isActive}");
                }

                ORDER_BY("lzhshtp_registration_date DESC");
                LIMIT("#{pageSize}");
                OFFSET("#{offset}");
            }}.toString();
        }

        /**
         * 构建统计管理员总数的SQL
         */
        public String countAdmins(final @Param("keyword") String keyword,
                                  final @Param("isActive") Boolean isActive,
                                  final @Param("userId") Long userId) {
            return new SQL() {{
                SELECT("COUNT(*)");
                FROM("tb_lzhshtp_users");
                WHERE("lzhshtp_role = 'admin'");

                if (keyword != null && !keyword.trim().isEmpty()) {
                    AND();
                    WHERE("(" +
                          "lzhshtp_username LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_email LIKE CONCAT('%', #{keyword}, '%') OR " +
                          "lzhshtp_phone_number LIKE CONCAT('%', #{keyword}, '%')" +
                          (isNumeric(keyword) ? " OR lzhshtp_user_id = " + keyword : "") +
                          ")");
                }

                if (isActive != null) {
                    AND();
                    WHERE("lzhshtp_is_active = #{isActive}");
                }
            }}.toString();
        }

        /**
         * 检查字符串是否为数字
         */
        private boolean isNumeric(String str) {
            try {
                Long.parseLong(str);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
    }

    /**
     * 查询用户列表
     *
     * @param keyword 关键词（用户名、邮箱、手机号、ID）
     * @param role 角色
     * @param isActive 是否激活
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 用户列表
     */
    @SelectProvider(type = UserSqlProvider.class, method = "getUserList")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role")
    })
    List<UserDTO> getUserList(@Param("keyword") String keyword,
                             @Param("role") String role,
                             @Param("isActive") Boolean isActive,
                             @Param("userId") Long userId,
                             @Param("offset") int offset,
                             @Param("pageSize") int pageSize);

    /**
     * 统计符合条件的用户总数
     *
     * @param keyword 关键词（用户名、邮箱、手机号、ID）
     * @param role 角色
     * @param isActive 是否激活
     * @return 用户总数
     */
    @SelectProvider(type = UserSqlProvider.class, method = "countUsers")
    int countUsers(@Param("keyword") String keyword,
                  @Param("role") String role,
                  @Param("isActive") Boolean isActive,
                  @Param("userId") Long userId);

    /**
     * 查询管理员列表
     *
     * @param keyword 关键词（用户名、邮箱、手机号、ID）
     * @param isActive 是否激活
     * @param userId 用户ID
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 管理员列表
     */
    @SelectProvider(type = UserSqlProvider.class, method = "getAdminList")
    @Results({
        @Result(property = "userId", column = "lzhshtp_user_id"),
        @Result(property = "username", column = "lzhshtp_username"),
        @Result(property = "email", column = "lzhshtp_email"),
        @Result(property = "phoneNumber", column = "lzhshtp_phone_number"),
        @Result(property = "avatarUrl", column = "lzhshtp_avatar_url"),
        @Result(property = "registrationDate", column = "lzhshtp_registration_date"),
        @Result(property = "lastLoginDate", column = "lzhshtp_last_login_date"),
        @Result(property = "creditScore", column = "lzhshtp_credit_score"),
        @Result(property = "location", column = "lzhshtp_location"),
        @Result(property = "isActive", column = "lzhshtp_is_active"),
        @Result(property = "role", column = "lzhshtp_role")
    })
    List<UserDTO> getAdminList(@Param("keyword") String keyword,
                              @Param("isActive") Boolean isActive,
                              @Param("userId") Long userId,
                              @Param("offset") int offset,
                              @Param("pageSize") int pageSize);

    /**
     * 统计符合条件的管理员总数
     *
     * @param keyword 关键词（用户名、邮箱、手机号、ID）
     * @param isActive 是否激活
     * @param userId 用户ID
     * @return 管理员总数
     */
    @SelectProvider(type = UserSqlProvider.class, method = "countAdmins")
    int countAdmins(@Param("keyword") String keyword,
                   @Param("isActive") Boolean isActive,
                   @Param("userId") Long userId);

    /**
     * 获取用户发布的商品数量
     *
     * @param userId 用户ID
     * @return 商品数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products WHERE lzhshtp_seller_id = #{userId}")
    int getProductCount(Long userId);

    /**
     * 获取用户已售出的商品数量
     *
     * @param userId 用户ID
     * @return 已售出商品数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products WHERE lzhshtp_seller_id = #{userId} AND lzhshtp_status = 'sold'")
    int getSoldProductCount(Long userId);

    /**
     * 获取用户购买订单数量
     *
     * @param userId 用户ID
     * @return 购买订单数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_orders WHERE lzhshtp_buyer_id = #{userId}")
    int getBuyOrderCount(Long userId);

    /**
     * 获取用户销售订单数量
     *
     * @param userId 用户ID
     * @return 销售订单数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_orders WHERE lzhshtp_seller_id = #{userId}")
    int getSellOrderCount(Long userId);

    /**
     * 获取用户论坛发帖数量
     *
     * @param userId 用户ID
     * @return 论坛发帖数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_posts WHERE lzhshtp_author_id = #{userId}")
    int getForumPostCount(Long userId);

    /**
     * 获取用户论坛评论数量
     *
     * @param userId 用户ID
     * @return 论坛评论数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_comments WHERE lzhshtp_author_id = #{userId}")
    int getForumCommentCount(Long userId);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param isActive 是否激活
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE tb_lzhshtp_users SET lzhshtp_is_active = #{isActive} " +
            "WHERE lzhshtp_user_id IN " +
            "<foreach collection='userIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateUserStatus(@Param("userIds") List<Long> userIds, @Param("isActive") Boolean isActive);
}
