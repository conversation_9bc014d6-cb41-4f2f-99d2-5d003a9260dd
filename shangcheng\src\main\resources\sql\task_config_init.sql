-- 定时任务配置初始化SQL

-- 插入订单超时自动取消任务的默认配置
INSERT INTO tb_lzhshtp_scheduled_task_configs 
(lzhshtp_task_name, lzhshtp_task_type, lzhshtp_cron_expression, lzhshtp_is_enabled, lzhshtp_config_params, lzhshtp_created_at, lzhshtp_updated_at) 
VALUES 
('order_timeout_cancel', 'order_timeout', '0 */5 * * * ?', true, 
'{"enabled": true, "timeoutMinutes": 30, "checkInterval": 5, "sendNotification": true, "notificationMethods": ["message"], "notificationTargets": ["buyer", "seller"]}',
NOW(), NOW())
ON DUPLICATE KEY UPDATE 
lzhshtp_updated_at = NOW();

-- 查询插入结果
SELECT * FROM tb_lzhshtp_scheduled_task_configs WHERE lzhshtp_task_name = 'order_timeout_cancel';
