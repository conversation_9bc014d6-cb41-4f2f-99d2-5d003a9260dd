package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户关注DTO，用于前后端数据传输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserFollowDTO {
    private Long followId;
    private Long followerId;
    private Long followingId;
    private String followerUsername;
    private String followerAvatarUrl;
    private String followingUsername;
    private String followingAvatarUrl;
    private LocalDateTime followedAt;
} 