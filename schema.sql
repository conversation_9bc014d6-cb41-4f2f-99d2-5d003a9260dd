--
-- 二手交易平台数据库 Schema
-- Version: 1.0
-- Generated by AI Assistant
--
-- 使用MySQL语法，如果使用其他数据库（如PostgreSQL），可能需要微调数据类型和关键字。
--

-- 1. 用户表 (tb_lzhshtp_users)
CREATE TABLE tb_lzhshtp_users (
    lzhshtp_user_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 用户唯一标识ID
    lzhshtp_username VARCHAR(50) NOT NULL UNIQUE,     -- 用户名，用于登录和展示，必须唯一
    lzhshtp_password_hash VARCHAR(255) NOT NULL,    -- 存储加密后的密码哈希值
    lzhshtp_email VARCHAR(100) NOT NULL UNIQUE,      -- 用户邮箱地址，必须唯一，可用于登录和通知
    lzhshtp_phone_number VARCHAR(20) UNIQUE,         -- 用户手机号码，必须唯一，可用于登录和联系
    lzhshtp_avatar_url VARCHAR(255),                  -- 用户头像的URL
    lzhshtp_registration_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 用户注册时间
    lzhshtp_last_login_date DATETIME,                 -- 用户最后登录时间
    lzhshtp_credit_score INT NOT NULL DEFAULT 100, -- 个人信用度，初始值100，根据行为动态调整
    lzhshtp_bio TEXT,                                -- 用户的自我介绍或签名
    lzhshtp_location VARCHAR(100),                   -- 用户所在地区信息
    lzhshtp_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 用户账户余额
    lzhshtp_can_publish_product BOOLEAN NOT NULL DEFAULT TRUE, -- 是否允许上架商品
    lzhshtp_is_active BOOLEAN NOT NULL DEFAULT TRUE,  -- 布尔值，表示用户账户是否活跃（可被管理员禁用）
    lzhshtp_role ENUM('admin', 'general_user', 'ai_customer_service') NOT NULL DEFAULT 'general_user' -- 用户角色：管理员、普通用户、AI客服
);

-- 2. 收货地址表 (tb_lzhshtp_shipping_addresses)
CREATE TABLE tb_lzhshtp_shipping_addresses (
    lzhshtp_address_id BIGINT AUTO_INCREMENT PRIMARY KEY,   -- 收货地址唯一标识ID
    lzhshtp_user_id BIGINT NOT NULL,                        -- 所属用户ID
    lzhshtp_recipient_name VARCHAR(100) NOT NULL,           -- 收货人姓名
    lzhshtp_phone_number VARCHAR(20) NOT NULL,              -- 收货人电话
    lzhshtp_province VARCHAR(50) NOT NULL,                  -- 省份
    lzhshtp_city VARCHAR(50) NOT NULL,                      -- 城市
    lzhshtp_district VARCHAR(50),                           -- 区/县
    lzhshtp_street_address TEXT NOT NULL,                   -- 详细街道地址
    lzhshtp_postal_code VARCHAR(10),                        -- 邮政编码
    lzhshtp_is_default BOOLEAN NOT NULL DEFAULT FALSE,      -- 是否是用户的默认地址
    lzhshtp_created_at DATETIME DEFAULT CURRENT_TIMESTAMP,    -- 地址创建时间
    lzhshtp_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 地址最后更新时间
);

-- 3. 商品分类表 (tb_lzhshtp_product_categories)
CREATE TABLE tb_lzhshtp_product_categories (
    lzhshtp_category_id INT AUTO_INCREMENT PRIMARY KEY, -- 商品分类唯一标识ID
    lzhshtp_category_name VARCHAR(50) NOT NULL UNIQUE,  -- 分类名称（例如：'电子产品', '图书', '服装'）
    lzhshtp_description TEXT,                           -- 分类描述
    lzhshtp_parent_category_id INT                      -- 父级分类ID，用于支持多级分类
);

-- 4. 商品表 (tb_lzhshtp_products)
CREATE TABLE tb_lzhshtp_products (
    lzhshtp_product_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 商品唯一标识ID
    lzhshtp_title VARCHAR(255) NOT NULL,               -- 商品标题
    lzhshtp_description TEXT NOT NULL,                 -- 商品详细描述
    lzhshtp_price DECIMAL(10, 2) NOT NULL,            -- 商品售价
    lzhshtp_category_id INT NOT NULL,                  -- 商品所属分类ID
    `lzhshtp_condition` VARCHAR(50),                   -- 商品新旧程度（例如：'全新', '九成新'），使用反引号避免与SQL关键字冲突
    lzhshtp_location VARCHAR(100),
    lzhshtp_delivery_method VARCHAR(50),               -- 交易/配送方式（例如：'快递', '同城面交'）
    lzhshtp_image_urls TEXT,                           -- 商品图片，只保存一张
    lzhshtp_posted_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 商品发布时间
    lzhshtp_status ENUM('available', 'sold', 'pending_review', 'manual_review', 'second_review', 'material_requested', 'off_shelf_by_seller', 'off_shelf_by_admin', 'deleted') NOT NULL DEFAULT 'pending_review', -- 商品状态：新增审核相关状态
    lzhshtp_support_official_verification BOOLEAN NOT NULL DEFAULT FALSE, -- 卖家是否提供官方验货
    lzhshtp_verification_fee DECIMAL(10,2) DEFAULT 0.00, -- 官方验货费用
    lzhshtp_seller_id BIGINT NOT NULL                 -- 卖家用户ID
);

-- 5. 订单表 (tb_lzhshtp_orders)
CREATE TABLE tb_lzhshtp_orders (
    lzhshtp_order_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 订单唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                -- 订单中的商品ID
    lzhshtp_buyer_id BIGINT NOT NULL,                  -- 买家用户ID
    lzhshtp_seller_id BIGINT NOT NULL,                 -- 卖家用户ID
    lzhshtp_order_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 订单创建时间
    lzhshtp_total_amount DECIMAL(10, 2) NOT NULL,     -- 订单总金额
    lzhshtp_status ENUM('pending_payment', 'paid', 'shipped', 'verifying', 'delivered', 'completed', 'cancelled', 'refunded') NOT NULL DEFAULT 'pending_payment', -- 订单状态
    lzhshtp_shipping_address VARCHAR(500) NOT NULL,    -- 收货地址信息 (现在直接存储为字符串)
    lzhshtp_payment_method VARCHAR(50),                -- 支付方式
    lzhshtp_transaction_id VARCHAR(255),               -- 支付平台返回的交易ID
    lzhshtp_current_location VARCHAR(255),             -- 货物当前位置
    lzhshtp_official_verification BOOLEAN NOT NULL DEFAULT FALSE, -- 是否官方验货
    lzhshtp_verification_fee DECIMAL(10,2) DEFAULT 0.00, -- 验货费用
    lzhshtp_verification_payer ENUM('buyer', 'seller') -- 验货费用承担方
);

-- 6. 商品评价表 (tb_lzhshtp_product_reviews)
CREATE TABLE tb_lzhshtp_product_reviews (
    lzhshtp_review_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 商品评价唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                -- 被评价的商品ID
    lzhshtp_reviewer_id BIGINT NOT NULL,               -- 发布评价的用户ID
    lzhshtp_seller_id BIGINT NOT NULL,                 -- 被评价商品的卖家ID（冗余字段，便于查询）
    lzhshtp_rating INT NOT NULL,                       -- 评分（例如：1-5星）
    lzhshtp_comment TEXT,                              -- 评价内容
    lzhshtp_review_date DATETIME DEFAULT CURRENT_TIMESTAMP -- 评价发布时间
);

-- 7. 用户互评表 (tb_lzhshtp_user_ratings)
CREATE TABLE tb_lzhshtp_user_ratings (
    lzhshtp_rating_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 用户互评唯一标识ID
    lzhshtp_order_id BIGINT NOT NULL,                  -- 本次评价所属的订单ID
    lzhshtp_rater_id BIGINT NOT NULL,                  -- 给出评价的用户ID
    lzhshtp_rated_user_id BIGINT NOT NULL,             -- 接收评价的用户ID
    lzhshtp_role_at_transaction ENUM('buyer', 'seller') NOT NULL, -- 被评价用户在本次交易中的角色（'buyer' 或 'seller'）
    lzhshtp_rating INT NOT NULL,                       -- 评分（例如：1-5星）
    lzhshtp_comment TEXT,                              -- 评价内容
    lzhshtp_rating_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 评价发布时间
    UNIQUE (lzhshtp_order_id, lzhshtp_rater_id, lzhshtp_rated_user_id) -- 确保一笔订单中，一个用户对另一个用户的评价只有一条
);

-- 8. 消息表 (tb_lzhshtp_messages)
CREATE TABLE tb_lzhshtp_messages (
    lzhshtp_message_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 消息唯一标识ID
    lzhshtp_conversation_id BIGINT NOT NULL,           -- 所属会话ID
    lzhshtp_sender_id BIGINT NOT NULL,                 -- 消息发送方用户ID
    lzhshtp_content TEXT NOT NULL,                     -- 消息内容
    lzhshtp_sent_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 消息发送时间
    lzhshtp_is_read BOOLEAN NOT NULL DEFAULT FALSE,    -- 布尔值，表示消息是否已读
    lzhshtp_is_system_message BOOLEAN NOT NULL DEFAULT FALSE -- 布尔值，表示是否为系统消息（例如AI客服或平台通知）
);

-- 8.1 会话表 (tb_lzhshtp_conversations)
CREATE TABLE tb_lzhshtp_conversations (
    lzhshtp_conversation_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 会话唯一标识ID
    lzhshtp_user1_id BIGINT NOT NULL,                  -- 会话参与者1的用户ID
    lzhshtp_user2_id BIGINT NOT NULL,                  -- 会话参与者2的用户ID
    lzhshtp_created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 会话创建时间
    lzhshtp_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 会话最后更新时间
    lzhshtp_last_message_preview VARCHAR(100),         -- 最后一条消息的预览
    lzhshtp_unread_count_user1 INT DEFAULT 0,          -- 用户1的未读消息数量
    lzhshtp_unread_count_user2 INT DEFAULT 0,          -- 用户2的未读消息数量
    UNIQUE (lzhshtp_user1_id, lzhshtp_user2_id)        -- 确保两个用户之间只有一个会话
);

-- 9. 论坛分类表 (tb_lzhshtp_forum_categories)
CREATE TABLE tb_lzhshtp_forum_categories (
    lzhshtp_forum_category_id INT AUTO_INCREMENT PRIMARY KEY, -- 论坛分类唯一标识ID
    lzhshtp_category_name VARCHAR(50) NOT NULL UNIQUE,  -- 分类名称（例如：'二手讨论', '技术交流'）
    lzhshtp_description TEXT                            -- 分类描述
);

-- 10. 论坛帖子表 (tb_lzhshtp_forum_posts)
CREATE TABLE tb_lzhshtp_forum_posts (
    lzhshtp_post_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 论坛帖子唯一标识ID
    lzhshtp_title VARCHAR(255) NOT NULL,               -- 帖子标题
    lzhshtp_content TEXT NOT NULL,                     -- 帖子内容
    lzhshtp_author_id BIGINT NOT NULL,                 -- 帖子作者用户ID
    lzhshtp_posted_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 帖子发布时间
    lzhshtp_forum_category_id INT NOT NULL,            -- 帖子所属分类ID
    lzhshtp_views_count INT NOT NULL DEFAULT 0，        -- 帖子浏览量
    lzhshtp_is_pinned TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否置顶(0-否，1-是)',
    lzhshtp_status TINYINT NOT NULL DEFAULT 0 COMMENT '帖子状态(0已发布，1-下架)';
);

-- 11. 论坛评论表 (tb_lzhshtp_forum_comments)
CREATE TABLE tb_lzhshtp_forum_comments (
    lzhshtp_comment_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 论坛评论唯一标识ID
    lzhshtp_post_id BIGINT NOT NULL,                   -- 所属帖子ID
    lzhshtp_author_id BIGINT NOT NULL,                 -- 评论作者用户ID
    lzhshtp_content TEXT NOT NULL,                     -- 评论内容
    lzhshtp_commented_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 评论发布时间
    lzhshtp_parent_comment_id BIGINT                   -- 父评论ID，用于回复评论
);

-- 12. 用户收藏表 (tb_lzhshtp_user_favorites)
CREATE TABLE tb_lzhshtp_user_favorites (
    lzhshtp_favorite_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 收藏记录唯一标识ID
    lzhshtp_user_id BIGINT NOT NULL,                   -- 收藏该商品的用户ID
    lzhshtp_product_id BIGINT NOT NULL,                -- 被收藏的商品ID
    lzhshtp_favorited_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 收藏时间
    UNIQUE (lzhshtp_user_id, lzhshtp_product_id)               -- 确保一个用户不会重复收藏同一个商品
);

-- 13. 用户反馈/举报表 (tb_lzhshtp_user_feedback)
CREATE TABLE tb_lzhshtp_user_feedback (
    lzhshtp_feedback_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 反馈记录唯一标识ID
    lzhshtp_reporter_id BIGINT NOT NULL,               -- 提交反馈的用户ID
    lzhshtp_feedback_type ENUM('bug_report', 'suggestion', 'complaint', 'abuse_report') NOT NULL, -- 反馈类型
    lzhshtp_related_entity_type VARCHAR(50),           -- 如果是举报，则指明举报对象类型（例如 'product', 'user'）
    lzhshtp_related_entity_id BIGINT,                  -- 关联的实体ID（例如：举报商品则为product_id）
    lzhshtp_content TEXT NOT NULL,                     -- 反馈或举报的详细内容
    lzhshtp_submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 提交时间
    lzhshtp_status ENUM('pending', 'in_progress', 'resolved', 'rejected') NOT NULL DEFAULT 'pending', -- 处理状态
    lzhshtp_admin_notes TEXT,                          -- 管理员处理时的备注
    lzhshtp_resolved_by_admin_id BIGINT                -- 处理该反馈的管理员用户ID
);

-- 14. 商品审核日志表 (tb_lzhshtp_product_audit_logs) - 保留原有表，扩展功能
CREATE TABLE tb_lzhshtp_product_audit_logs (
    lzhshtp_log_id BIGINT AUTO_INCREMENT PRIMARY KEY,       -- 审核日志唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 被审核的商品ID
    lzhshtp_auditor_id BIGINT,                             -- 执行审核操作的管理员用户ID（可为空，表示系统操作）
    lzhshtp_action_type ENUM('submitted_for_review', 'approved', 'rejected', 'relisted_by_admin', 'off_shelved_by_admin', 'info_updated_by_admin') NOT NULL, -- 审核操作类型
    lzhshtp_notes TEXT,                                    -- 管理员或系统留下的备注，例如拒绝原因、修改说明等
    lzhshtp_audit_date DATETIME DEFAULT CURRENT_TIMESTAMP   -- 审核操作发生的时间
);




-- ========================================-- ========================================



-- 15. 自动审核记录表 (tb_lzhshtp_auto_audit_records)
CREATE TABLE tb_lzhshtp_auto_audit_records (
    lzhshtp_record_id BIGINT AUTO_INCREMENT PRIMARY KEY,    -- 自动审核记录唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 被审核的商品ID
    lzhshtp_text_audit_result JSON,                        -- 文字审核结果JSON格式
    lzhshtp_image_audit_result JSON,                       -- 图片审核结果JSON格式
    lzhshtp_credit_audit_result JSON,                      -- 信用分审核结果JSON格式
    lzhshtp_price_audit_result JSON,                       -- 价格审核结果JSON格式
    lzhshtp_publishing_behavior_result JSON,               -- 发布行为审核结果JSON格式
    lzhshtp_final_decision ENUM('auto_approve', 'manual_review', 'auto_reject') NOT NULL, -- 最终决策
    lzhshtp_decision_reason TEXT,                          -- 决策原因
    lzhshtp_risk_score DECIMAL(5,2) DEFAULT 0,            -- 综合风险评分
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP -- 审核时间
);

-- 16. 人工审核任务表 (tb_lzhshtp_manual_audit_tasks)
CREATE TABLE tb_lzhshtp_manual_audit_tasks (
    lzhshtp_task_id BIGINT AUTO_INCREMENT PRIMARY KEY,     -- 人工审核任务唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 被审核的商品ID
    lzhshtp_seller_id BIGINT NOT NULL,                     -- 卖家用户ID
    lzhshtp_auto_audit_record_id BIGINT,                   -- 关联的自动审核记录ID
    lzhshtp_audit_reasons JSON,                            -- 进入人工审核的原因列表
    lzhshtp_status ENUM('pending', 'in_progress', 'completed', 'material_requested') DEFAULT 'pending', -- 任务状态
    lzhshtp_priority INT DEFAULT 0,                        -- 优先级（0-10，数字越大优先级越高）
    lzhshtp_admin_id BIGINT,                               -- 处理审核的管理员ID（管理员主动审核，关联tb_lzhshtp_users表，role='admin'）
    lzhshtp_admin_decision ENUM('approved', 'rejected', 'request_materials', 'escalate_to_second_review') NULL, -- 管理员决策
    lzhshtp_admin_comments TEXT,                           -- 管理员备注
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 任务创建时间
    lzhshtp_assigned_time DATETIME,                        -- 任务分配时间
    lzhshtp_completed_time DATETIME,                       -- 任务完成时间
    lzhshtp_deadline DATETIME                              -- 任务截止时间
);

-- 17. 二度复审任务表 (tb_lzhshtp_second_review_tasks)
CREATE TABLE tb_lzhshtp_second_review_tasks (
    lzhshtp_task_id BIGINT AUTO_INCREMENT PRIMARY KEY,     -- 二度复审任务唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 被审核的商品ID
    lzhshtp_manual_audit_task_id BIGINT NOT NULL,          -- 关联的人工审核任务ID
    lzhshtp_escalation_reason TEXT,                        -- 升级到二度复审的原因
    lzhshtp_status ENUM('pending', 'in_progress', 'completed', 'material_requested') DEFAULT 'pending', -- 任务状态
    lzhshtp_senior_admin_id BIGINT,                        -- 处理二度复审的管理员ID（管理员主动审核，关联tb_lzhshtp_users表，role='admin'）
    lzhshtp_admin_decision ENUM('approved', 'rejected', 'request_materials') NULL, -- 高级管理员决策
    lzhshtp_admin_comments TEXT,                           -- 高级管理员备注
    lzhshtp_all_previous_materials JSON,                   -- 所有之前的审核材料和记录
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 任务创建时间
    lzhshtp_assigned_time DATETIME,                        -- 任务分配时间
    lzhshtp_completed_time DATETIME,                       -- 任务完成时间
    lzhshtp_deadline DATETIME                              -- 任务截止时间
);

-- 18. 材料补充请求表 (tb_lzhshtp_material_requests)
CREATE TABLE tb_lzhshtp_material_requests (
    lzhshtp_request_id BIGINT AUTO_INCREMENT PRIMARY KEY,  -- 材料请求唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 关联的商品ID
    lzhshtp_requester_type ENUM('manual_audit', 'second_review') NOT NULL, -- 请求来源类型
    lzhshtp_requester_task_id BIGINT NOT NULL,             -- 请求来源任务ID
    lzhshtp_admin_id BIGINT NOT NULL,                      -- 发起材料请求的管理员ID（管理员主动操作，关联tb_lzhshtp_users表，role='admin'）
    lzhshtp_required_materials JSON NOT NULL,              -- 需要的材料列表
    lzhshtp_request_reason TEXT,                           -- 请求材料的原因
    lzhshtp_status ENUM('waiting', 'submitted', 'approved', 'rejected') DEFAULT 'waiting', -- 请求状态
    lzhshtp_request_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 请求时间
    lzhshtp_deadline DATETIME,                             -- 提交截止时间
    lzhshtp_seller_notified BOOLEAN DEFAULT FALSE          -- 是否已通知卖家
);

-- 19. 补充材料表 (tb_lzhshtp_supplementary_materials)
CREATE TABLE tb_lzhshtp_supplementary_materials (
    lzhshtp_material_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 补充材料唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 关联的商品ID
    lzhshtp_request_id BIGINT NOT NULL,                    -- 关联的材料请求ID
    lzhshtp_seller_id BIGINT NOT NULL,                     -- 提交材料的卖家ID
    lzhshtp_material_type VARCHAR(100),                    -- 材料类型（如：发票、授权书、证明文件等）
    lzhshtp_material_urls JSON,                            -- 材料文件URL列表
    lzhshtp_description TEXT,                              -- 材料说明
    lzhshtp_submit_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 提交时间
    lzhshtp_admin_reviewed BOOLEAN DEFAULT FALSE,          -- 管理员是否已查看
    lzhshtp_admin_comments TEXT                            -- 管理员对材料的评价
);

-- 20. 管理员审核统计表 (tb_lzhshtp_admin_audit_stats) - 可选表，用于统计管理员审核数据
CREATE TABLE tb_lzhshtp_admin_audit_stats (
    lzhshtp_stat_id BIGINT AUTO_INCREMENT PRIMARY KEY,     -- 统计记录唯一标识ID
    lzhshtp_admin_id BIGINT NOT NULL UNIQUE,               -- 管理员用户ID（关联tb_lzhshtp_users表，role='admin'）
    lzhshtp_total_audited INT DEFAULT 0,                   -- 累计审核商品数
    lzhshtp_total_approved INT DEFAULT 0,                  -- 累计审核通过数
    lzhshtp_total_rejected INT DEFAULT 0,                  -- 累计审核拒绝数
    lzhshtp_approval_rate DECIMAL(5,2) DEFAULT 0,          -- 审核通过率
    lzhshtp_current_workload INT DEFAULT 0,                -- 当前工作负载（正在处理的任务数）
    lzhshtp_last_audit_time DATETIME,                      -- 最后审核时间
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    lzhshtp_updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 更新时间
);

-- 21. 审核通知表 (tb_lzhshtp_audit_notifications)
CREATE TABLE tb_lzhshtp_audit_notifications (
    lzhshtp_notification_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 通知唯一标识ID
    lzhshtp_product_id BIGINT NOT NULL,                    -- 关联的商品ID
    lzhshtp_seller_id BIGINT NOT NULL,                     -- 接收通知的卖家ID
    lzhshtp_notification_type ENUM('material_request', 'audit_result', 'deadline_reminder') NOT NULL, -- 通知类型
    lzhshtp_title VARCHAR(200) NOT NULL,                   -- 通知标题
    lzhshtp_content TEXT NOT NULL,                         -- 通知内容
    lzhshtp_related_task_type ENUM('manual_audit', 'second_review', 'material_request') NULL, -- 关联的任务类型
    lzhshtp_related_task_id BIGINT NULL,                   -- 关联的任务ID
    lzhshtp_is_read BOOLEAN DEFAULT FALSE,                 -- 是否已读
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    lzhshtp_read_time DATETIME                             -- 阅读时间
);



-- 23. 敏感词库表 (tb_lzhshtp_sensitive_words)
CREATE TABLE tb_lzhshtp_sensitive_words (
    lzhshtp_word_id BIGINT AUTO_INCREMENT PRIMARY KEY,     -- 敏感词唯一标识ID
    lzhshtp_word VARCHAR(100) NOT NULL UNIQUE,             -- 敏感词内容
    lzhshtp_word_type ENUM('banned', 'sensitive', 'brand') NOT NULL, -- 词汇类型：违禁词、敏感词、品牌词、敏感类目
    lzhshtp_severity_level INT DEFAULT 1,                  -- 严重程度（1-10）
    lzhshtp_category VARCHAR(50),                          -- 分类（如：政治、色情、暴力等）
    lzhshtp_is_active BOOLEAN DEFAULT TRUE,                -- 是否启用
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    lzhshtp_created_by BIGINT                              -- 创建者ID
);

-- ========================================
-- 审核系统说明
-- ========================================
-- 审核流程：管理员主动审核，不是系统分配
-- 1. 商品进入人工审核队列后，管理员可以主动选择审核任务
-- 2. 管理员点击"开始审核"后，系统记录该管理员ID到对应任务
-- 3. 管理员可以要求补充材料、通过审核或拒绝审核
-- 4. 使用现有的tb_lzhshtp_users表中role='admin'的用户作为审核员
-- 5. 可选的tb_lzhshtp_admin_audit_stats表用于统计管理员的审核数据

-- ========================================
-- 商家评价表
-- ========================================
CREATE TABLE tb_lzhshtp_seller_reviews (
    lzhshtp_review_id BIGINT AUTO_INCREMENT PRIMARY KEY,        -- 评价记录唯一标识ID
    lzhshtp_order_id BIGINT NOT NULL UNIQUE,                   -- 订单ID，确保一个订单只能评价一次
    lzhshtp_buyer_id BIGINT NOT NULL,                          -- 买家用户ID
    lzhshtp_seller_id BIGINT NOT NULL,                         -- 卖家用户ID
    lzhshtp_product_id BIGINT NOT NULL,                        -- 商品ID
    lzhshtp_rating INT NOT NULL CHECK (lzhshtp_rating BETWEEN 1 AND 5), -- 评分：1-5星
    lzhshtp_review_content TEXT,                               -- 评价内容（可选）
    lzhshtp_review_tags JSON,                                  -- 评价标签（如：发货快、质量好、服务态度等）
    lzhshtp_is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,       -- 是否匿名评价
    lzhshtp_credit_score_change INT NOT NULL DEFAULT 0,        -- 此次评价导致的信用分变化
    lzhshtp_seller_credit_before INT NOT NULL,                 -- 评价前卖家信用分
    lzhshtp_seller_credit_after INT NOT NULL,                  -- 评价后卖家信用分
    lzhshtp_created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 评价时间
    lzhshtp_updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间

    -- 外键约束
    FOREIGN KEY (lzhshtp_order_id) REFERENCES tb_lzhshtp_orders(lzhshtp_order_id) ON DELETE CASCADE,
    FOREIGN KEY (lzhshtp_buyer_id) REFERENCES tb_lzhshtp_users(lzhshtp_user_id) ON DELETE CASCADE,
    FOREIGN KEY (lzhshtp_seller_id) REFERENCES tb_lzhshtp_users(lzhshtp_user_id) ON DELETE CASCADE,
    FOREIGN KEY (lzhshtp_product_id) REFERENCES tb_lzhshtp_products(lzhshtp_product_id) ON DELETE CASCADE,

    -- 索引
    INDEX idx_seller_reviews_seller_id (lzhshtp_seller_id),
    INDEX idx_seller_reviews_buyer_id (lzhshtp_buyer_id),
    INDEX idx_seller_reviews_order_id (lzhshtp_order_id),
    INDEX idx_seller_reviews_rating (lzhshtp_rating),
    INDEX idx_seller_reviews_created_time (lzhshtp_created_time)
);

-- ========================================
-- 信用分变更记录表
-- ========================================
CREATE TABLE tb_lzhshtp_credit_score_logs (
    lzhshtp_log_id BIGINT AUTO_INCREMENT PRIMARY KEY,          -- 日志记录唯一标识ID
    lzhshtp_user_id BIGINT NOT NULL,                          -- 用户ID
    lzhshtp_change_type ENUM('review', 'penalty', 'bonus', 'system') NOT NULL, -- 变更类型
    lzhshtp_change_reason VARCHAR(255) NOT NULL,               -- 变更原因
    lzhshtp_score_before INT NOT NULL,                         -- 变更前信用分
    lzhshtp_score_after INT NOT NULL,                          -- 变更后信用分
    lzhshtp_score_change INT NOT NULL,                         -- 信用分变化量（正数为加分，负数为减分）
    lzhshtp_related_order_id BIGINT,                          -- 相关订单ID（如果是评价导致的变更）
    lzhshtp_related_review_id BIGINT,                         -- 相关评价ID（如果是评价导致的变更）
    lzhshtp_operator_id BIGINT,                               -- 操作者ID（系统操作时为NULL）
    lzhshtp_created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间

    -- 外键约束
    FOREIGN KEY (lzhshtp_user_id) REFERENCES tb_lzhshtp_users(lzhshtp_user_id) ON DELETE CASCADE,
    FOREIGN KEY (lzhshtp_related_order_id) REFERENCES tb_lzhshtp_orders(lzhshtp_order_id) ON DELETE SET NULL,
    FOREIGN KEY (lzhshtp_related_review_id) REFERENCES tb_lzhshtp_seller_reviews(lzhshtp_review_id) ON DELETE SET NULL,
    FOREIGN KEY (lzhshtp_operator_id) REFERENCES tb_lzhshtp_users(lzhshtp_user_id) ON DELETE SET NULL,

    -- 索引
    INDEX idx_credit_logs_user_id (lzhshtp_user_id),
    INDEX idx_credit_logs_change_type (lzhshtp_change_type),
    INDEX idx_credit_logs_created_time (lzhshtp_created_time)
);

-- ========================================
-- 审核系统常量说明
-- ========================================
-- 审核流程固定，不需要配置表控制：
-- 1. 自动审核：文字审核 → 图片审核 → 信用分审核 → 价格审核
-- 2. 人工审核：管理员主动审核
-- 3. 二度复审：管理员最终审核
--
-- 固定阈值（可在代码中定义常量）：
-- - 信用分阈值：85分
-- - 每日最大发布数：10个
-- - 每周最大发布数：30个
-- - 人工审核截止时间：72小时
-- - 二度复审截止时间：120小时
-- - 材料提交截止时间：7天

-- 插入默认敏感词库
INSERT INTO tb_lzhshtp_sensitive_words (lzhshtp_word, lzhshtp_word_type, lzhshtp_severity_level, lzhshtp_category) VALUES
-- 违禁词
('枪支', 'banned', 10, '武器'),
('弹药', 'banned', 10, '武器'),
('毒品', 'banned', 10, '违禁药物'),
('大麻', 'banned', 10, '违禁药物'),
('冰毒', 'banned', 10, '违禁药物'),
('假币', 'banned', 10, '违法物品'),
('身份证', 'banned', 9, '证件'),
('护照', 'banned', 9, '证件'),
('发票', 'banned', 8, '票据'),
-- 敏感词
('高仿', 'sensitive', 7, '假货'),
('A货', 'sensitive', 7, '假货'),
('山寨', 'sensitive', 6, '假货'),
('代购', 'sensitive', 4, '来源可疑'),
('内部价', 'sensitive', 5, '价格可疑'),
('批发价', 'sensitive', 3, '销售方式'),
-- 品牌词
('iPhone', 'brand', 6, '电子产品'),
('华为', 'brand', 6, '电子产品'),
('小米', 'brand', 5, '电子产品'),
('Nike', 'brand', 6, '服装'),
('Adidas', 'brand', 6, '服装'),
('LV', 'brand', 8, '奢侈品'),
('Gucci', 'brand', 8, '奢侈品'),



-- ========================================-- ========================================



-- 24. 用户关注表 (tb_lzhshtp_user_follows)
CREATE TABLE tb_lzhshtp_user_follows (
    lzhshtp_follow_id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 关注记录唯一标识ID
    lzhshtp_follower_id BIGINT NOT NULL,               -- 关注者用户ID
    lzhshtp_following_id BIGINT NOT NULL,              -- 被关注者用户ID
    lzhshtp_followed_at DATETIME DEFAULT CURRENT_TIMESTAMP -- 关注时间
); 

-- 定时任务配置表
CREATE TABLE tb_lzhshtp_scheduled_task_configs (
    lzhshtp_task_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    lzhshtp_task_name VARCHAR(100) NOT NULL UNIQUE COMMENT '任务名称',
    lzhshtp_task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    lzhshtp_cron_expression VARCHAR(100) COMMENT 'Cron表达式',
    lzhshtp_is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    lzhshtp_config_params JSON COMMENT '任务参数JSON',
    lzhshtp_last_execution DATETIME COMMENT '上次执行时间',
    lzhshtp_next_execution DATETIME COMMENT '下次执行时间',
    lzhshtp_created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    lzhshtp_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ========================================
-- AI智能助手模块数据表
-- 基于qifanCode-ai-xuexi项目的ai_session和ai_message表
-- 保持原有UUID主键和字段结构，便于代码迁移
-- ========================================

-- 16. AI会话表 (tb_lzhshtp_session) - 对应原ai_session表
CREATE TABLE `tb_lzhshtp_ai_session` (
  `zhshtp_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'AI会话唯一标识ID(UUID格式)，对应原表id字段',
  `zhshtp_created_time` datetime(6) NOT NULL COMMENT '会话创建时间，对应原表created_time字段',
  `zhshtp_edited_time` datetime(6) NOT NULL COMMENT '会话最后编辑时间，对应原表edited_time字段',
  `zhshtp_creator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话创建者用户ID，对应原表creator_id字段，关联商城用户表',
  `zhshtp_editor_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话最后编辑者用户ID，对应原表editor_id字段',
  `zhshtp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话名称，对应原表name字段(如"商品咨询"、"订单查询"、"智能客服"等)',
  PRIMARY KEY (`zhshtp_id`) USING BTREE COMMENT 'AI会话表主键'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = 'AI智能助手会话表，存储用户与AI的对话会话信息，支持商品咨询、订单查询、客服等多种场景';

-- 17. AI消息表 (tb_lzhshtp_message) - 对应原ai_message表
CREATE TABLE `tb_lzhshtp_ai_message` (
  `zhshtp_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'AI消息唯一标识ID(UUID格式)，对应原表id字段',
  `zhshtp_created_time` datetime(6) NOT NULL COMMENT '消息创建时间，对应原表created_time字段',
  `zhshtp_edited_time` datetime(6) NOT NULL COMMENT '消息最后编辑时间，对应原表edited_time字段',
  `zhshtp_creator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息创建者用户ID，对应原表creator_id字段，关联商城用户表',
  `zhshtp_editor_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息最后编辑者用户ID，对应原表editor_id字段',
  `zhshtp_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息类型，对应原表type字段：user(用户消息)/assistant(AI助手回复)/system(系统消息)/function_call(函数调用)/function_result(函数结果)',
  `zhshtp_text_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息文本内容，对应原表text_content字段',
  `zhshtp_medias` json NULL COMMENT '媒体内容JSON格式，对应原表medias字段，包含图片链接、语音链接等多媒体信息',
  `zhshtp_ai_session_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属AI会话ID，对应原表ai_session_id字段，关联tb_lzhshtp_session表',
  PRIMARY KEY (`zhshtp_id`) USING BTREE COMMENT 'AI消息表主键',
  INDEX `tb_lzhshtp_message_zhshtp_ai_session_id_fk`(`zhshtp_ai_session_id` ASC) USING BTREE COMMENT '会话ID索引，用于查询会话下的所有消息，对应原表索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic COMMENT = 'AI智能助手消息表，存储用户与AI的具体对话消息内容，支持文本、图片、语音等多媒体消息类型';

-- ========================================
-- 退款和验货系统相关表
-- ========================================
-- ========================================
-- 退款和验货系统相关表（带完整注释版）
-- ========================================

-- 18. 退款申请表 (tb_lzhshtp_refund_requests)
CREATE TABLE tb_lzhshtp_refund_requests (
    lzhshtp_refund_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '退款申请唯一标识ID',
    lzhshtp_order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    lzhshtp_buyer_id BIGINT NOT NULL COMMENT '申请退款的买家ID',
    lzhshtp_seller_id BIGINT NOT NULL COMMENT '关联的卖家ID',
    lzhshtp_refund_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款金额',
    lzhshtp_refund_reason TEXT NOT NULL COMMENT '退款原因描述',
    lzhshtp_refund_type ENUM('refund_only', 'return_refund') NOT NULL COMMENT '退款类型：仅退款/退货退款',
    lzhshtp_status ENUM('pending_seller', 'seller_approved', 'seller_rejected', 'pending_admin', 'admin_approved', 'admin_rejected', 'completed', 'cancelled') NOT NULL DEFAULT 'pending_seller' COMMENT '退款申请状态',
    lzhshtp_seller_response TEXT COMMENT '商家处理意见',
    lzhshtp_admin_response TEXT COMMENT '管理员处理意见',
    lzhshtp_evidence_urls TEXT COMMENT '买家提供的证据图片URLs，JSON格式存储',
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请创建时间',
    lzhshtp_updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT='用户退款申请表';

-- 19. 管理员强制退款表 (tb_lzhshtp_admin_forced_refunds)
CREATE TABLE tb_lzhshtp_admin_forced_refunds (
    lzhshtp_forced_refund_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '强制退款记录ID',
    lzhshtp_refund_request_id BIGINT NOT NULL COMMENT '关联的退款申请ID',
    lzhshtp_seller_id BIGINT NOT NULL COMMENT '被强制退款的卖家ID',
    lzhshtp_required_amount DECIMAL(10,2) NOT NULL COMMENT '要求退款的金额',
    lzhshtp_status ENUM('pending', 'completed') NOT NULL DEFAULT 'pending' COMMENT '强制退款状态',
    lzhshtp_admin_id BIGINT NOT NULL COMMENT '执行强制退款的管理员ID',
    lzhshtp_admin_note TEXT COMMENT '管理员备注',
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    lzhshtp_completed_time DATETIME COMMENT '强制退款完成时间'
) COMMENT='管理员强制退款记录表';

-- 20. 官方验货记录表 (tb_lzhshtp_verification_records)
CREATE TABLE tb_lzhshtp_verification_records (
    lzhshtp_verification_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '验货记录ID',
    lzhshtp_order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    lzhshtp_verifier_id BIGINT NOT NULL COMMENT '验货管理员ID',
    lzhshtp_verification_status ENUM('waiting_goods', 'verifying', 'passed', 'failed', 'forwarded') NOT NULL DEFAULT 'waiting_goods' COMMENT '验货状态',
    lzhshtp_verification_result TEXT COMMENT '验货结果详细描述',
    lzhshtp_verification_images TEXT COMMENT '验货过程图片URLs，JSON格式存储',
    lzhshtp_received_time DATETIME COMMENT '平台收到验货商品的时间',
    lzhshtp_verified_time DATETIME COMMENT '验货完成时间',
    lzhshtp_forwarded_time DATETIME COMMENT '商品转发给买家的时间',
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'
    
) COMMENT='官方验货服务记录表';

-- 21. 物流跟踪表 (tb_lzhshtp_logistics_tracking)
CREATE TABLE tb_lzhshtp_logistics_tracking (
    lzhshtp_tracking_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '物流记录ID',
    lzhshtp_order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    lzhshtp_tracking_type ENUM('seller_to_buyer', 'seller_to_official', 'official_to_buyer', 'buyer_to_seller') NOT NULL COMMENT '物流类型：卖家→买家/卖家→官方/官方→买家/买家→卖家',
    lzhshtp_tracking_number VARCHAR(100) COMMENT '物流运单号',
    lzhshtp_current_status VARCHAR(50) COMMENT '当前物流状态',
    lzhshtp_current_location VARCHAR(255) COMMENT '货物当前位置',
    lzhshtp_logistics_company VARCHAR(100) COMMENT '物流公司名称',
    lzhshtp_created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    lzhshtp_updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT='订单物流跟踪信息表';