package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.VerificationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;

import java.util.List;

/**
 * 验货记录Mapper接口
 */
@Mapper
public interface VerificationRecordMapper extends BaseMapper<VerificationRecord> {
    
    /**
     * 根据订单ID查询验货记录
     */
    @Select("SELECT " +
            "lzhshtp_verification_id, lzhshtp_order_id, lzhshtp_verifier_id, lzhshtp_verification_status, " +
            "lzhshtp_verification_result, lzhshtp_verification_images, lzhshtp_received_time, " +
            "lzhshtp_verified_time, lzhshtp_forwarded_time, lzhshtp_created_time " +
            "FROM tb_lzhshtp_verification_records WHERE lzhshtp_order_id = #{orderId}")
    @Results({
        @Result(column = "lzhshtp_verification_id", property = "verificationId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_verifier_id", property = "verifierId"),
        @Result(column = "lzhshtp_verification_status", property = "verificationStatus"),
        @Result(column = "lzhshtp_verification_result", property = "verificationResult"),
        @Result(column = "lzhshtp_verification_images", property = "verificationImages"),
        @Result(column = "lzhshtp_received_time", property = "receivedTime"),
        @Result(column = "lzhshtp_verified_time", property = "verifiedTime"),
        @Result(column = "lzhshtp_forwarded_time", property = "forwardedTime"),
        @Result(column = "lzhshtp_created_time", property = "createdTime")
    })
    VerificationRecord selectByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据验货员ID查询待处理的验货记录
     */
    @Select("SELECT " +
            "lzhshtp_verification_id, lzhshtp_order_id, lzhshtp_verifier_id, lzhshtp_verification_status, " +
            "lzhshtp_verification_result, lzhshtp_verification_images, lzhshtp_received_time, " +
            "lzhshtp_verified_time, lzhshtp_forwarded_time, lzhshtp_created_time " +
            "FROM tb_lzhshtp_verification_records WHERE lzhshtp_verifier_id = #{verifierId} AND lzhshtp_verification_status IN ('waiting_goods', 'verifying') ORDER BY lzhshtp_created_time ASC")
    @Results({
        @Result(column = "lzhshtp_verification_id", property = "verificationId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_verifier_id", property = "verifierId"),
        @Result(column = "lzhshtp_verification_status", property = "verificationStatus"),
        @Result(column = "lzhshtp_verification_result", property = "verificationResult"),
        @Result(column = "lzhshtp_verification_images", property = "verificationImages"),
        @Result(column = "lzhshtp_received_time", property = "receivedTime"),
        @Result(column = "lzhshtp_verified_time", property = "verifiedTime"),
        @Result(column = "lzhshtp_forwarded_time", property = "forwardedTime"),
        @Result(column = "lzhshtp_created_time", property = "createdTime")
    })
    List<VerificationRecord> selectPendingByVerifierId(@Param("verifierId") Long verifierId);
    
    /**
     * 查询所有待处理的验货记录
     */
    @Select("SELECT " +
            "lzhshtp_verification_id, lzhshtp_order_id, lzhshtp_verifier_id, lzhshtp_verification_status, " +
            "lzhshtp_verification_result, lzhshtp_verification_images, lzhshtp_received_time, " +
            "lzhshtp_verified_time, lzhshtp_forwarded_time, lzhshtp_created_time " +
            "FROM tb_lzhshtp_verification_records WHERE lzhshtp_verification_status IN ('waiting_goods', 'verifying') ORDER BY lzhshtp_created_time ASC")
    @Results({
        @Result(column = "lzhshtp_verification_id", property = "verificationId"),
        @Result(column = "lzhshtp_order_id", property = "orderId"),
        @Result(column = "lzhshtp_verifier_id", property = "verifierId"),
        @Result(column = "lzhshtp_verification_status", property = "verificationStatus"),
        @Result(column = "lzhshtp_verification_result", property = "verificationResult"),
        @Result(column = "lzhshtp_verification_images", property = "verificationImages"),
        @Result(column = "lzhshtp_received_time", property = "receivedTime"),
        @Result(column = "lzhshtp_verified_time", property = "verifiedTime"),
        @Result(column = "lzhshtp_forwarded_time", property = "forwardedTime"),
        @Result(column = "lzhshtp_created_time", property = "createdTime")
    })
    List<VerificationRecord> selectAllPending();
}
