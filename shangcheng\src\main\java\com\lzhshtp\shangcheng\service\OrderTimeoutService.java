package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;

/**
 * 订单超时处理服务接口
 */
public interface OrderTimeoutService {
    
    /**
     * 取消超时订单
     * 
     * @param config 超时配置
     * @return 处理结果信息
     */
    String cancelExpiredOrders(OrderTimeoutConfigDTO config);
    
    /**
     * 取消超时订单（使用默认配置）
     * 
     * @return 处理结果信息
     */
    String cancelExpiredOrders();
    
    /**
     * 检查单个订单是否超时
     * 
     * @param orderId 订单ID
     * @return 是否超时
     */
    boolean isOrderExpired(Long orderId);
    
    /**
     * 手动取消指定订单
     * 
     * @param orderId 订单ID
     * @param reason 取消原因
     * @return 是否取消成功
     */
    boolean cancelOrderManually(Long orderId, String reason);
}
