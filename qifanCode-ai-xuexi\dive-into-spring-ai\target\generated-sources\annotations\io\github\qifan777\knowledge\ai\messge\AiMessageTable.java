package io.github.qifan777.knowledge.ai.messge;

import io.github.qifan777.knowledge.ai.session.AiSessionTable;
import io.github.qifan777.knowledge.user.UserTable;
import java.lang.Deprecated;
import java.lang.Override;
import java.lang.String;
import java.time.LocalDateTime;
import java.util.List;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;
import org.springframework.ai.chat.messages.MessageType;

@GeneratedBy(
        type = AiMessage.class
)
public class AiMessageTable extends AbstractTypedTable<AiMessage> implements AiMessageProps {
    public static final AiMessageTable $ = new AiMessageTable();

    public AiMessageTable() {
        super(AiMessage.class);
    }

    public AiMessageTable(AbstractTypedTable.DelayedOperation<AiMessage> delayedOperation) {
        super(AiMessage.class, delayedOperation);
    }

    public AiMessageTable(TableImplementor<AiMessage> table) {
        super(table);
    }

    protected AiMessageTable(AiMessageTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Str id() {
        return __get(AiMessageProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<LocalDateTime> createdTime() {
        return __get(AiMessageProps.CREATED_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<LocalDateTime> editedTime() {
        return __get(AiMessageProps.EDITED_TIME.unwrap());
    }

    @Override
    public UserTable editor() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiMessageProps.EDITOR.unwrap()));
        }
        return new UserTable(joinOperation(AiMessageProps.EDITOR.unwrap()));
    }

    @Override
    public UserTable editor(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiMessageProps.EDITOR.unwrap(), joinType));
        }
        return new UserTable(joinOperation(AiMessageProps.EDITOR.unwrap(), joinType));
    }

    @Override
    public PropExpression.Str editorId() {
        return __getAssociatedId(AiMessageProps.EDITOR.unwrap());
    }

    @Override
    public UserTable creator() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiMessageProps.CREATOR.unwrap()));
        }
        return new UserTable(joinOperation(AiMessageProps.CREATOR.unwrap()));
    }

    @Override
    public UserTable creator(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(AiMessageProps.CREATOR.unwrap(), joinType));
        }
        return new UserTable(joinOperation(AiMessageProps.CREATOR.unwrap(), joinType));
    }

    @Override
    public PropExpression.Str creatorId() {
        return __getAssociatedId(AiMessageProps.CREATOR.unwrap());
    }

    @Override
    public PropExpression.Cmp<MessageType> type() {
        return __get(AiMessageProps.TYPE.unwrap());
    }

    @Override
    public PropExpression.Str textContent() {
        return __get(AiMessageProps.TEXT_CONTENT.unwrap());
    }

    @Override
    public PropExpression<List<AiMessage.Media>> medias() {
        return __get(AiMessageProps.MEDIAS.unwrap());
    }

    @Override
    public AiSessionTable session() {
        __beforeJoin();
        if (raw != null) {
            return new AiSessionTable(raw.joinImplementor(AiMessageProps.SESSION.unwrap()));
        }
        return new AiSessionTable(joinOperation(AiMessageProps.SESSION.unwrap()));
    }

    @Override
    public AiSessionTable session(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new AiSessionTable(raw.joinImplementor(AiMessageProps.SESSION.unwrap(), joinType));
        }
        return new AiSessionTable(joinOperation(AiMessageProps.SESSION.unwrap(), joinType));
    }

    @Override
    public PropExpression.Str sessionId() {
        return __getAssociatedId(AiMessageProps.SESSION.unwrap());
    }

    @Override
    public AiMessageTableEx asTableEx() {
        return new AiMessageTableEx(this, null);
    }

    @Override
    public AiMessageTable __disableJoin(String reason) {
        return new AiMessageTable(this, reason);
    }

    @GeneratedBy(
            type = AiMessage.class
    )
    public static class Remote extends AbstractTypedTable<AiMessage> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(AiMessage.class, delayedOperation);
        }

        public Remote(TableImplementor<AiMessage> table) {
            super(table);
        }

        public PropExpression.Str id() {
            return __get(AiMessageProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<AiMessage> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
