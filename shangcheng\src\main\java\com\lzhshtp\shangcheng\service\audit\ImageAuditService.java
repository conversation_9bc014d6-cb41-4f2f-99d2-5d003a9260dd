package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片审核服务
 */
@Slf4j
@Service
public class ImageAuditService {

    @Autowired
    private AliyunImageAuditService aliyunImageAuditService;

    /**
     * 执行图片审核
     */
    public AuditResultDTO auditImages(Product product) {
        log.info("开始图片审核，商品ID: {}", product.getId());

        String imageUrls = product.getImageUrls();
        if (!StringUtils.hasText(imageUrls)) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(30)
                .reason("商品缺少图片，需要人工审核")
                .build();
        }

        // 解析图片URL列表
        List<String> imageUrlList = parseImageUrls(imageUrls);




        // 1. 检查图片内容（使用阿里云审核）
        AuditResultDTO contentResult = checkImageContentWithAliyun(imageUrlList);
        if (!contentResult.getPassed()) {
            return contentResult;
        }


        log.info("图片审核通过，商品ID: {}", product.getId());
        return AuditResultDTO.pass("image_audit");
    }

    /**
     * 解析图片URL列表
     */
    private List<String> parseImageUrls(String imageUrls) {
        List<String> urlList = new ArrayList<>();
        if (StringUtils.hasText(imageUrls)) {
            String[] urls = imageUrls.split(",");
            for (String url : urls) {
                String trimmedUrl = url.trim();
                if (StringUtils.hasText(trimmedUrl)) {
                    urlList.add(trimmedUrl);
                }
            }
        }
        return urlList;
    }


    /**
     * 使用阿里云检查图片内容
     */
    private AuditResultDTO checkImageContentWithAliyun(List<String> imageUrls) {
        // 检查阿里云服务是否可用
        if (!aliyunImageAuditService.isServiceAvailable()) {
            log.warn("阿里云内容安全服务不可用，跳过图片内容审核");
            return AuditResultDTO.pass("image_content_skip");
        }

        for (String imageUrl : imageUrls) {
            try {
                // 调用阿里云审核单张图片
                AuditResultDTO result = aliyunImageAuditService.auditSingleImage(imageUrl);

                // 如果有任何一张图片审核不通过，直接返回
                if (!result.getPassed()) {
                    log.info("图片审核不通过，图片URL: {}, 原因: {}", imageUrl, result.getReason());
                    return result;
                }

                log.debug("图片审核通过，图片URL: {}", imageUrl);

            } catch (Exception e) {
                log.error("图片审核异常，图片URL: {}", imageUrl, e);

                // 审核异常时，进入人工审核
                return AuditResultDTO.builder()
                    .passed(false)
                    .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                    .score(50)
                    .reason("图片审核异常，需要人工审核：" + e.getMessage())
                    .details(Map.of("error_image_url", imageUrl))
                    .build();
            }
        }

        // 所有图片都通过审核
        log.info("所有图片审核通过，图片数量: {}", imageUrls.size());
        return AuditResultDTO.pass("aliyun_image_content");
    }
    /**
     * 检查是否为有效的图片格式
     */
    private boolean isValidImageFormat(String url) {
        String lowerUrl = url.toLowerCase();
        return lowerUrl.endsWith(".jpg") ||
               lowerUrl.endsWith(".jpeg") ||
               lowerUrl.endsWith(".png") ||
               lowerUrl.endsWith(".gif") ||
               lowerUrl.endsWith(".webp");
    }

    /**
     * 检查是否为可疑的图片URL
     */
    private boolean isSuspiciousImageUrl(String url) {
        String lowerUrl = url.toLowerCase();

        // 检查是否包含可疑域名或路径
        String[] suspiciousDomains = {
            "suspicious-site.com",
            "fake-images.net",
            "temp-upload.org"
        };

        for (String domain : suspiciousDomains) {
            if (lowerUrl.contains(domain)) {
                return true;
            }
        }

        // 检查是否为外部链接（非本站图片）
        if (!lowerUrl.startsWith("http://localhost") &&
            !lowerUrl.startsWith("https://your-domain.com") &&
            !lowerUrl.startsWith("/uploads/")) {
            return true;
        }

        return false;
    }



    /**
     * 检查是否为低质量图片
     */
    private boolean isLowQualityImage(String url) {
        String lowerUrl = url.toLowerCase();

        // 基于文件名的简单质量判断
        return lowerUrl.contains("thumbnail") ||
               lowerUrl.contains("small") ||
               lowerUrl.contains("low") ||
               lowerUrl.contains("compressed");
    }
}
