package com.lzhshtp.shangcheng.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "sms.aliyun")
public class SmsConfig {
    
    /**
     * 阿里云API网关AppCode
     */
    private String appCode;
    
    /**
     * API主机地址
     */
    private String host = "https://dfsns.market.alicloudapi.com";
    
    /**
     * API路径
     */
    private String path = "/data/send_sms";
    
    /**
     * 模板ID
     */
    private String templateId = "CST_ptdie100";
    
    /**
     * 验证码有效期（分钟）
     */
    private int codeExpireMinutes = 5;
    
    /**
     * 发送限制间隔（分钟）
     */
    private int sendLimitMinutes = 1;
    
    /**
     * 每日发送限制次数
     */
    private int dailyLimit = 10;
}
