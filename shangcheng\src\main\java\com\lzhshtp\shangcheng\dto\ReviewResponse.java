package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评价响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReviewResponse {

    /**
     * 评价ID
     */
    private Long reviewId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 买家ID
     */
    private Long buyerId;

    /**
     * 买家名称（可能匿名）
     */
    private String buyerName;

    /**
     * 卖家ID
     */
    private Long sellerId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品标题
     */
    private String productTitle;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 评分：1-5星
     */
    private Integer rating;

    /**
     * 评价内容
     */
    private String reviewContent;

    /**
     * 评价标签
     */
    private List<String> reviewTags;

    /**
     * 是否匿名评价
     */
    private Boolean isAnonymous;

    /**
     * 信用分变化
     */
    private Integer creditScoreChange;

    /**
     * 评价前卖家信用分
     */
    private Integer sellerCreditBefore;

    /**
     * 评价后卖家信用分
     */
    private Integer sellerCreditAfter;

    /**
     * 评价时间
     */
    private LocalDateTime createdTime;

    /**
     * 获取显示的买家名称
     */
    public String getDisplayBuyerName() {
        if (isAnonymous != null && isAnonymous) {
            return "匿名用户";
        }
        return buyerName != null ? buyerName : "未知用户";
    }

    /**
     * 获取评分描述
     */
    public String getRatingDescription() {
        if (rating == null) {
            return "未评分";
        }
        
        switch (rating) {
            case 1:
                return "非常不满意";
            case 2:
                return "不满意";
            case 3:
                return "一般";
            case 4:
                return "满意";
            case 5:
                return "非常满意";
            default:
                return "未知评分";
        }
    }

    /**
     * 获取信用分变化描述
     */
    public String getCreditChangeDescription() {
        if (creditScoreChange == null || creditScoreChange == 0) {
            return "无变化";
        }
        
        if (creditScoreChange > 0) {
            return "+" + creditScoreChange + "分";
        } else {
            return creditScoreChange + "分";
        }
    }
}
