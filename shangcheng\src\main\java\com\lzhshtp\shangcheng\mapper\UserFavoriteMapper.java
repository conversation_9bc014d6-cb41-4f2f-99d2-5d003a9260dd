package com.lzhshtp.shangcheng.mapper;

import com.lzhshtp.shangcheng.dto.UserFavoriteDTO;
import com.lzhshtp.shangcheng.model.UserFavorite;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserFavoriteMapper {
    
    /**
     * 添加收藏
     */
    @Insert("INSERT INTO tb_lzhshtp_user_favorites (lzhshtp_user_id, lzhshtp_product_id, lzhshtp_favorited_at) " +
            "VALUES (#{lzhshtpUserId}, #{lzhshtpProductId}, #{lzhshtpFavoritedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "lzhshtpFavoriteId")
    int insert(UserFavorite userFavorite);
    
    /**
     * 删除收藏
     */
    @Delete("DELETE FROM tb_lzhshtp_user_favorites WHERE lzhshtp_favorite_id = #{favoriteId} AND lzhshtp_user_id = #{userId}")
    int delete(@Param("favoriteId") Long favoriteId, @Param("userId") Long userId);
    
    /**
     * 删除指定用户对指定商品的收藏
     */
    @Delete("DELETE FROM tb_lzhshtp_user_favorites WHERE lzhshtp_user_id = #{userId} AND lzhshtp_product_id = #{productId}")
    int deleteByUserAndProduct(@Param("userId") Long userId, @Param("productId") Long productId);
    
    /**
     * 查询用户是否已收藏商品
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_user_favorites WHERE lzhshtp_user_id = #{userId} AND lzhshtp_product_id = #{productId}")
    int checkFavorite(@Param("userId") Long userId, @Param("productId") Long productId);
    
    /**
     * 查询用户的收藏列表（包含商品信息）
     */
    @Select("SELECT f.lzhshtp_favorite_id as favoriteId, f.lzhshtp_user_id as userId, " +
            "f.lzhshtp_product_id as productId, f.lzhshtp_favorited_at as favoritedAt, " +
            "p.lzhshtp_title as productTitle, p.lzhshtp_description as productDescription, " +
            "p.lzhshtp_price as productPrice, p.lzhshtp_image_urls as productImage " +
            "FROM tb_lzhshtp_user_favorites f " +
            "JOIN tb_lzhshtp_products p ON f.lzhshtp_product_id = p.lzhshtp_product_id " +
            "WHERE f.lzhshtp_user_id = #{userId} " +
            "ORDER BY f.lzhshtp_favorited_at DESC")
    List<UserFavoriteDTO> findByUserId(@Param("userId") Long userId);
    
    /**
     * 统计商品被收藏次数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_user_favorites WHERE lzhshtp_product_id = #{productId}")
    int countByProductId(@Param("productId") Long productId);
} 