package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 评价请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReviewRequest {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 评分：1-5星
     */
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分不能低于1星")
    @Max(value = 5, message = "评分不能高于5星")
    private Integer rating;

    /**
     * 评价内容（可选）
     */
    @Size(max = 500, message = "评价内容不能超过500字")
    private String reviewContent;

    /**
     * 评价标签列表（可选）
     */
    private List<String> reviewTags;

    /**
     * 是否匿名评价
     */
    private Boolean isAnonymous = false;

    /**
     * 验证评价标签
     */
    public boolean isValidTags() {
        if (reviewTags == null || reviewTags.isEmpty()) {
            return true;
        }
        
        // 预定义的有效标签
        List<String> validTags = List.of(
            "发货快", "包装好", "质量好", "服务态度好", "物流快",
            "商品描述准确", "性价比高", "推荐购买", "会再次购买",
            "发货慢", "包装差", "质量差", "服务态度差", "物流慢",
            "商品描述不符", "性价比低", "不推荐", "不会再买"
        );
        
        return reviewTags.stream().allMatch(validTags::contains);
    }

    /**
     * 获取评分描述
     */
    public String getRatingDescription() {
        if (rating == null) {
            return "未评分";
        }
        
        switch (rating) {
            case 1:
                return "非常不满意";
            case 2:
                return "不满意";
            case 3:
                return "一般";
            case 4:
                return "满意";
            case 5:
                return "非常满意";
            default:
                return "未知评分";
        }
    }
}
