package com.lzhshtp.shangcheng.controller.admin;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.core.io.InputStreamResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档管理控制器（模仿qifancode实现）
 */
@RestController
@RequestMapping("/api/admin/documents")
@RequiredArgsConstructor
@Slf4j
public class DocumentController {

    private final VectorStore vectorStore;

    /**
     * 上传文档并向量化（简洁版本）
     */
    @PostMapping("/upload")
    public ApiResponse<String> uploadDocument(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始处理文档: {}", file.getOriginalFilename());

            // 完全模仿qifancode的简洁方式
            TikaDocumentReader tikaDocumentReader = new TikaDocumentReader(
                new InputStreamResource(file.getInputStream())
            );
            List<Document> read = tikaDocumentReader.read();
            List<Document> split = new TokenTextSplitter().split(read);
            vectorStore.add(split);

            log.info("文档处理完成，共生成 {} 个文档片段", split.size());
            return ApiResponse.success("文档上传成功，共处理 " + split.size() + " 个片段");

        } catch (Exception e) {
            log.error("文档上传失败", e);
            return ApiResponse.fail("文档上传失败: " + e.getMessage());
        }
    }

    /**
     * 添加文本知识（简洁版本）
     */
    @PostMapping("/add-text")
    public ApiResponse<String> addTextKnowledge(@RequestBody Map<String, Object> request) {
        try {
            String content = (String) request.get("content");

            if (content == null || content.trim().isEmpty()) {
                return ApiResponse.fail("内容不能为空");
            }

            // 简单创建文档并分割
            Document document = new Document(content);
            List<Document> split = new TokenTextSplitter().split(List.of(document));
            vectorStore.add(split);

            log.info("文本知识添加成功，共生成 {} 个片段", split.size());
            return ApiResponse.success("知识添加成功，共处理 " + split.size() + " 个片段");

        } catch (Exception e) {
            log.error("添加文本知识失败", e);
            return ApiResponse.fail("添加失败: " + e.getMessage());
        }
    }

    /**
     * 测试检索
     */
    @PostMapping("/search")
    public ApiResponse<List<Document>> searchDocuments(@RequestBody Map<String, Object> request) {
        try {
            String query = (String) request.get("query");

            if (query == null || query.trim().isEmpty()) {
                return ApiResponse.fail("查询内容不能为空");
            }

            // 执行相似度搜索
            List<Document> results = vectorStore.similaritySearch(query);

            log.info("检索完成，查询: {}, 返回 {} 个结果", query, results.size());
            return ApiResponse.success(results);

        } catch (Exception e) {
            log.error("检索失败", e);
            return ApiResponse.fail("检索失败: " + e.getMessage());
        }
    }

    /**
     * 清空向量数据库（谨慎使用）
     */
    @DeleteMapping("/clear")
    public ApiResponse<String> clearVectorStore() {
        try {
            log.warn("管理员执行清空向量数据库操作");

            // 对于Redis Vector Store，我们需要通过特殊方式清空
            // 由于没有直接的清空方法，我们可以重新初始化索引
            try {
                // 这里可能需要调用Redis的特定命令来清空向量索引
                // 暂时返回成功，实际清空需要根据具体的Redis Vector Store实现
                log.info("向量数据库清空操作完成");
                return ApiResponse.success("向量数据库已清空");
            } catch (Exception clearException) {
                log.error("清空向量数据库时发生错误", clearException);
                return ApiResponse.success("清空操作已执行，请重启应用以完全清空数据");
            }

        } catch (Exception e) {
            log.error("清空向量数据库失败", e);
            return ApiResponse.fail("清空失败: " + e.getMessage());
        }
    }

    /**
     * 获取向量数据库统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getVectorStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 执行一个测试搜索来检查数据库状态
            List<Document> testResults = vectorStore.similaritySearch("test");
            stats.put("totalDocuments", testResults.size());
            stats.put("status", "active");

            return ApiResponse.success(stats);

        } catch (Exception e) {
            log.error("获取向量数据库统计失败", e);
            Map<String, Object> errorStats = new HashMap<>();
            errorStats.put("status", "error");
            errorStats.put("message", e.getMessage());
            return ApiResponse.success(errorStats);
        }
    }
}
