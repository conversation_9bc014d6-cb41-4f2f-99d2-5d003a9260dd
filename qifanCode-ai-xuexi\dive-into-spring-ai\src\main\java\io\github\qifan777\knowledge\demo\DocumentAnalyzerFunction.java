package io.github.qifan777.knowledge.demo;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import jdk.jfr.Description;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
@Description("文档解析函数")
public class DocumentAnalyzerFunction implements Function<DocumentAnalyzerFunction.Request,String> {
    @Override
    public String apply(Request request) {
        FileSystemResource fileSystemResource = new FileSystemResource(request.path);
        return new TikaDocumentReader(fileSystemResource).read().get(0).getContent();
    }

    @JsonClassDescription("文档解析请求参数")
    public record Request(
        @JsonPropertyDescription("待解析的文档路径")
        @JsonProperty(required = true)
        String path
    ) {}
}
