package com.lzhshtp.shangcheng.dto.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 审核响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditResponseDTO {
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 审核状态
     */
    private String auditStatus;
    
    /**
     * 审核结果
     */
    private String auditResult;
    
    /**
     * 审核原因
     */
    private String auditReason;
    
    /**
     * 风险评分
     */
    private Double riskScore;
    
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    
    /**
     * 风险因素列表
     */
    private List<String> riskFactors;
    
    /**
     * 详细信息
     */
    private Map<String, Object> details;
}

/**
 * 人工审核任务响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ManualAuditTaskResponseDTO {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品标题
     */
    private String productTitle;
    
    /**
     * 卖家ID
     */
    private Long sellerId;
    
    /**
     * 卖家用户名
     */
    private String sellerUsername;
    
    /**
     * 审核原因
     */
    private List<String> auditReasons;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 截止时间
     */
    private LocalDateTime deadline;
    
    /**
     * 管理员ID
     */
    private Long adminId;
    
    /**
     * 管理员用户名
     */
    private String adminUsername;
    
    /**
     * 审核决策
     */
    private String adminDecision;
    
    /**
     * 审核备注
     */
    private String adminComments;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedTime;
}

/**
 * 测试商品响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class TestProductResponseDTO {
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品标题
     */
    private String title;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 商品价格
     */
    private String price;
    
    /**
     * 卖家ID
     */
    private Long sellerId;
    
    /**
     * 商品状态
     */
    private String status;
    
    /**
     * 提示信息
     */
    private String message;
    
    /**
     * 预期审核结果
     */
    private String expectedResult;
}
