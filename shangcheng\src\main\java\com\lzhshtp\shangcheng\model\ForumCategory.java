package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 论坛分类实体类
 */
@Data
@TableName("tb_lzhshtp_forum_categories")
public class ForumCategory {
    
    /**
     * 论坛分类ID
     */
    @TableId(value = "lzhshtp_forum_category_id", type = IdType.AUTO)
    private Integer forumCategoryId;
    
    /**
     * 分类名称
     */
    @TableField("lzhshtp_category_name")
    private String categoryName;
    
    /**
     * 分类描述
     */
    @TableField("lzhshtp_description")
    private String description;
} 