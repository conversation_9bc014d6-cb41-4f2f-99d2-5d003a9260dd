package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 信用分审核服务
 */
@Slf4j
@Service
public class CreditScoreAuditService {
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 执行信用分审核
     */
    public AuditResultDTO auditCreditScore(Long userId) {
        log.info("开始信用分审核，用户ID: {}", userId);
        
        User user = userMapper.selectById(userId);
        if (user == null) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(100)
                .reason("用户不存在")
                .build();
        }
        
        Integer creditScore = user.getCreditScore();
        if (creditScore == null) {
            creditScore = 100; // 默认信用分
        }
        
        Map<String, Object> details = new HashMap<>();
        details.put("credit_score", creditScore);
        details.put("threshold", AuditConstants.CREDIT_SCORE_THRESHOLD);
        
        // 信用分审核逻辑
        if (creditScore < 30) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(80)
                .reason("信用分极低（" + creditScore + "分），高风险用户")
                .details(details)
                .build();
        } else if (creditScore < 50) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(60)
                .reason("信用分很低（" + creditScore + "分），需要严格审核")
                .details(details)
                .build();
        } else if (creditScore < 70) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(40)
                .reason("信用分较低（" + creditScore + "分），需要人工审核")
                .details(details)
                .build();
        } else if (creditScore < AuditConstants.CREDIT_SCORE_THRESHOLD) {
            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.LOW)
                .score(15)
                .reason("信用分一般（" + creditScore + "分），轻微风险")
                .details(details)
                .build();
        }
        
        log.info("信用分审核通过，用户ID: {}, 信用分: {}", userId, creditScore);
        return AuditResultDTO.pass("credit_score");
    }
}
