import request from '@/utils/request'

const BASE_URL = '/messages'

/**
 * 获取或创建与指定用户的会话
 * @param {Number} userId - 对方用户ID
 * @returns {Promise} - 返回会话ID
 */
export function getOrCreateConversation(userId) {
  return request({
    url: `${BASE_URL}/conversations/with-user/${userId}`,
    method: 'get'
  })
}

/**
 * 管理员发送系统消息
 * @param {Object} data - 消息数据
 * @param {Number} data.receiverId - 接收者ID
 * @param {String} data.content - 消息内容
 * @returns {Promise} - 返回发送结果
 */
export function sendSystemMessage(data) {
  return request({
    url: `${BASE_URL}/admin/send-system`,
    method: 'post',
    data
  })
}

/**
 * 获取会话消息列表
 * @param {Number} conversationId - 会话ID
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页数量
 * @returns {Promise} - 返回消息列表
 */
export function getConversationMessages(conversationId, params) {
  return request({
    url: `${BASE_URL}/conversations/${conversationId}`,
    method: 'get',
    params
  })
} 