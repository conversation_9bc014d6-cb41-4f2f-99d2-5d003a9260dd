package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;

import com.lzhshtp.shangcheng.dto.OrderTimeoutConfigDTO;
import com.lzhshtp.shangcheng.model.ScheduledTaskConfig;
import com.lzhshtp.shangcheng.service.TaskConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 定时任务配置控制器
 */
@RestController
@RequestMapping("/api/admin/tasks")
@PreAuthorize("hasRole('ROLE_ADMIN')")
public class TaskConfigController {

    private static final Logger logger = LoggerFactory.getLogger(TaskConfigController.class);

    @Autowired
    private TaskConfigService taskConfigService;

    /**
     * 获取订单超时配置
     */
    @GetMapping("/order-timeout/config")
    public ApiResponse<OrderTimeoutConfigDTO> getOrderTimeoutConfig() {
        try {
            OrderTimeoutConfigDTO config = taskConfigService.getOrderTimeoutConfig();
            return ApiResponse.success("获取配置成功", config);
        } catch (Exception e) {
            logger.error("获取订单超时配置失败", e);
            return ApiResponse.fail("获取配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新订单超时配置
     */
    @PostMapping("/order-timeout/config")
    public ApiResponse<String> updateOrderTimeoutConfig(@RequestBody OrderTimeoutConfigDTO config) {
        try {
            logger.info("更新订单超时配置：{}", config);

            boolean success = taskConfigService.updateOrderTimeoutConfig(config);
            if (success) {
                return ApiResponse.success("配置更新成功");
            } else {
                return ApiResponse.fail("配置更新失败");
            }
        } catch (Exception e) {
            logger.error("更新订单超时配置失败", e);
            return ApiResponse.fail("更新配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有任务配置
     */
    @GetMapping("/configs")
    public ApiResponse<List<ScheduledTaskConfig>> getAllTaskConfigs() {
        try {
            List<ScheduledTaskConfig> configs = taskConfigService.getAllTaskConfigs();
            return ApiResponse.success("获取任务配置成功", configs);
        } catch (Exception e) {
            logger.error("获取任务配置失败", e);
            return ApiResponse.fail("获取任务配置失败：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用任务
     */
    @PostMapping("/{taskName}/toggle")
    public ApiResponse<String> toggleTask(@PathVariable String taskName, @RequestParam boolean enabled) {
        try {
            logger.info("切换任务状态：{}，目标状态：{}", taskName, enabled);

            boolean success = taskConfigService.toggleTask(taskName, enabled);
            if (success) {
                String message = enabled ? "任务已启用" : "任务已禁用";
                return ApiResponse.success(message);
            } else {
                return ApiResponse.fail("操作失败");
            }
        } catch (Exception e) {
            logger.error("切换任务状态失败，任务：{}，目标状态：{}", taskName, enabled, e);
            return ApiResponse.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行任务
     */
    @PostMapping("/{taskName}/execute")
    public ApiResponse<String> executeTask(@PathVariable String taskName) {
        try {
            logger.info("手动执行任务：{}", taskName);

            String result = taskConfigService.executeTaskManually(taskName);
            return ApiResponse.success("执行完成", result);
        } catch (Exception e) {
            logger.error("手动执行任务失败，任务：{}", taskName, e);
            return ApiResponse.fail("执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务配置详情
     */
    @GetMapping("/{taskName}/config")
    public ApiResponse<ScheduledTaskConfig> getTaskConfig(@PathVariable String taskName) {
        try {
            ScheduledTaskConfig config = taskConfigService.getTaskConfigByName(taskName);
            if (config != null) {
                return ApiResponse.success("获取任务配置成功", config);
            } else {
                return ApiResponse.fail("任务配置不存在");
            }
        } catch (Exception e) {
            logger.error("获取任务配置失败，任务：{}", taskName, e);
            return ApiResponse.fail("获取任务配置失败：" + e.getMessage());
        }
    }
}
