package com.lzhshtp.shangcheng.service.impl;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ForumPostRequest;
import com.lzhshtp.shangcheng.exception.BusinessException;
import com.lzhshtp.shangcheng.mapper.ForumCategoryMapper;
import com.lzhshtp.shangcheng.mapper.ForumCommentMapper;
import com.lzhshtp.shangcheng.mapper.ForumPostMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.ForumCategory;
import com.lzhshtp.shangcheng.model.ForumComment;
import com.lzhshtp.shangcheng.model.ForumPost;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ForumPostService; import com.lzhshtp.shangcheng.service.SearchService; // 临时注释掉
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 论坛帖子服务实现类
 * 临时完全禁用以便测试订单超时功能
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ForumPostServiceImpl extends ServiceImpl<ForumPostMapper, ForumPost> implements ForumPostService {

    private final ForumPostMapper forumPostMapper;
    private final UserMapper userMapper;
    private final ForumCategoryMapper forumCategoryMapper;
    private final ForumCommentMapper forumCommentMapper;
   private final SearchService searchService;

    /**
     * 创建帖子
     *
     * @param request 帖子请求
     * @param userId  用户ID
     * @return 帖子ID
     */
    @Override
    @Transactional
    public Long createPost(ForumPostRequest request, Long userId) {
        ForumPost post = ForumPost.builder()
                .title(request.getTitle())
                .content(request.getContent())
                .authorId(userId)
                .postedAt(LocalDateTime.now())
                .forumCategoryId(request.getForumCategoryId())
                .viewsCount(0)
                .isPinned(ForumPost.PinnedStatus.NOT_PINNED)
                .status(ForumPost.PostStatus.PUBLISHED)
                .build();

        save(post);

        // 异步同步到ElasticSearch - 临时禁用
        /*
        try {
            searchService.syncForumPostToES(post.getPostId());
            log.info("论坛帖子创建成功并同步到ES，ID: {}", post.getPostId());
        } catch (Exception e) {
            log.warn("论坛帖子同步到ES失败，ID: {}, 错误: {}", post.getPostId(), e.getMessage());
        }
        */
        log.info("论坛帖子创建成功，ID: {}", post.getPostId());

        return post.getPostId();
    }

    /**
     * 更新帖子
     *
     * @param postId  帖子ID
     * @param request 帖子请求
     * @param userId  用户ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updatePost(Long postId, ForumPostRequest request, Long userId) {
        ForumPost post = getById(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        // 检查是否是帖子作者
        if (!post.getAuthorId().equals(userId)) {
            throw new BusinessException("只有帖子作者才能编辑帖子");
        }

        post.setTitle(request.getTitle());
        post.setContent(request.getContent());
        post.setForumCategoryId(request.getForumCategoryId());

        boolean result = updateById(post);

        // 异步同步到ElasticSearch - 临时禁用
        if (result) {
            /*
            try {
                searchService.syncForumPostToES(postId);
                log.info("论坛帖子更新成功并同步到ES，ID: {}", postId);
            } catch (Exception e) {
                log.warn("论坛帖子同步到ES失败，ID: {}, 错误: {}", postId, e.getMessage());
            }
            */
            log.info("论坛帖子更新成功，ID: {}", postId);
        }

        return result;
    }

    /**
     * 删除帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deletePost(Long postId, Long userId) {
        ForumPost post = getById(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        // 检查是否是帖子作者
        if (!post.getAuthorId().equals(userId)) {
            throw new BusinessException("只有帖子作者才能删除帖子");
        }

        // 逻辑删除，将状态设置为下架
        post.setStatus(ForumPost.PostStatus.OFFLINE);
        boolean result = updateById(post);

        // 异步从ElasticSearch删除 - 临时禁用
        if (result) {
            /*
            try {
                searchService.deleteForumPostFromES(postId);
                log.info("论坛帖子删除成功并从ES中移除，ID: {}", postId);
            } catch (Exception e) {
                log.warn("论坛帖子从ES删除失败，ID: {}, 错误: {}", postId, e.getMessage());
            }
            */
            log.info("论坛帖子删除成功，ID: {}", postId);
        }

        return result;
    }

    /**
     * 获取帖子详情
     *
     * @param postId 帖子ID
     * @return 帖子详情
     */
    @Override
    public ForumPostDTO getPostDetail(Long postId) {
        // 使用XML中定义的方法获取帖子详情
        ForumPost post = forumPostMapper.selectPostDetail(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        // 增加浏览量
        increaseViewCount(postId);

        // 转换为DTO
        ForumPostDTO postDTO = convertToDTO(post);

        return postDTO;
    }

    /**
     * 分页查询帖子列表
     *
     * @param queryRequest 查询请求
     * @return 分页结果
     */
    @Override
    public IPage<ForumPostDTO> getPostList(ForumPostQueryRequest queryRequest) {
        // 创建分页对象
        Page<ForumPost> page = new Page<>(queryRequest.getPageNum(), queryRequest.getPageSize());

        // 使用自定义查询方法
        List<ForumPost> records = forumPostMapper.selectPostList(
                queryRequest.getCategoryId(),
                queryRequest.getAuthorId(),
                queryRequest.getKeyword(),
                queryRequest.getOnlyPinned(),
                queryRequest.getEnablePinned(),
                queryRequest.getOrderBy()
        );

        // 手动分页处理
        int start = (queryRequest.getPageNum() - 1) * queryRequest.getPageSize();
        int end = Math.min(start + queryRequest.getPageSize(), records.size());

        List<ForumPost> pageRecords = records.size() > start ? records.subList(start, end) : new ArrayList<>();

        // 设置分页结果
        page.setRecords(pageRecords);
        page.setTotal(records.size());

        // 转换为DTO
        return page.convert(this::convertToDTO);
    }

    /**
     * 设置帖子置顶状态
     *
     * @param postId   帖子ID
     * @param isPinned 是否置顶
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean setPinned(Long postId, boolean isPinned) {
        ForumPost post = getById(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        post.setIsPinned(isPinned ? ForumPost.PinnedStatus.PINNED : ForumPost.PinnedStatus.NOT_PINNED);
        return updateById(post);
    }

    /**
     * 设置帖子状态
     *
     * @param postId 帖子ID
     * @param status 状态
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean setStatus(Long postId, Integer status) {
        ForumPost post = getById(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }

        post.setStatus(status);
        return updateById(post);
    }

    /**
     * 增加帖子浏览量
     *
     * @param postId 帖子ID
     * @return 是否成功
     */
    @Override
    public boolean increaseViewCount(Long postId) {
        int rows = forumPostMapper.increaseViewCount(postId);
        return rows > 0;
    }

    /**
     * 将实体转换为DTO
     *
     * @param post 帖子实体
     * @return 帖子DTO
     */
    private ForumPostDTO convertToDTO(ForumPost post) {
        ForumPostDTO dto = new ForumPostDTO();
        dto.setPostId(post.getPostId());
        dto.setTitle(post.getTitle());
        dto.setContent(post.getContent());
        dto.setAuthorId(post.getAuthorId());
        dto.setPostedAt(post.getPostedAt());
        dto.setForumCategoryId(post.getForumCategoryId());
        dto.setViewsCount(post.getViewsCount());
        dto.setIsPinned(post.getIsPinned());
        dto.setStatus(post.getStatus());

        // 获取作者信息
        User author = userMapper.selectById(post.getAuthorId());
        if (author != null) {
            dto.setAuthorName(author.getUsername());
            dto.setAuthorAvatar(author.getAvatarUrl());
        }

        // 获取分类信息
        ForumCategory category = forumCategoryMapper.selectById(post.getForumCategoryId());
        if (category != null) {
            dto.setCategoryName(category.getCategoryName());
        }

        // 获取评论数量
        Integer commentCount = forumCommentMapper.countCommentsByPostId(post.getPostId());
        dto.setCommentCount(commentCount != null ? commentCount : 0);

        return dto;
    }
}
