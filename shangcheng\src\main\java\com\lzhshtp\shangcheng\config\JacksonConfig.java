package com.lzhshtp.shangcheng.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson配置类
 * 解决DashScope API兼容性问题
 */
@Configuration
public class JacksonConfig {

    /**
     * 配置全局ObjectMapper
     * 主要解决DashScope API返回的JSON中包含未知字段的问题
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Java时间模块
        mapper.registerModule(new JavaTimeModule());

        // 忽略未知字段 - 解决DashScope API的index字段问题
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 忽略空的Bean（序列化时）
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        // 允许接受空字符串作为null对象
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        return mapper;
    }
}
