<!DOCTYPE html>
<html>
<head>
    <title>WebSocket测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div>
        <button onclick="connectTest()">连接测试WebSocket</button>
        <button onclick="connectChat()">连接聊天WebSocket</button>
        <button onclick="disconnect()">断开连接</button>
    </div>
    
    <div>
        <input type="text" id="messageInput" placeholder="输入消息">
        <button onclick="sendMessage()">发送消息</button>
    </div>
    
    <div>
        <h3>连接状态:</h3>
        <div id="status">未连接</div>
    </div>
    
    <div>
        <h3>消息日志:</h3>
        <div id="messages" style="border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px;"></div>
    </div>

    <script>
        let websocket = null;
        
        function log(message) {
            const messages = document.getElementById('messages');
            const time = new Date().toLocaleTimeString();
            messages.innerHTML += `<div>[${time}] ${message}</div>`;
            messages.scrollTop = messages.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }
        
        function connectTest() {
            try {
                websocket = new WebSocket('ws://localhost:8080/ws/test');
                
                websocket.onopen = function() {
                    log('测试WebSocket连接成功');
                    updateStatus('已连接 (测试)');
                };
                
                websocket.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                };
                
                websocket.onclose = function(event) {
                    log('连接关闭: ' + event.code + ' - ' + event.reason);
                    updateStatus('已断开');
                };
                
                websocket.onerror = function(error) {
                    log('连接错误: ' + error);
                    updateStatus('连接错误');
                };
            } catch (error) {
                log('连接失败: ' + error);
            }
        }
        
        function connectChat() {
            const token = prompt('请输入JWT Token:');
            if (!token) return;
            
            try {
                websocket = new WebSocket(`ws://localhost:8080/ws/chat?token=${token}`);
                
                websocket.onopen = function() {
                    log('聊天WebSocket连接成功');
                    updateStatus('已连接 (聊天)');
                };
                
                websocket.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                };
                
                websocket.onclose = function(event) {
                    log('连接关闭: ' + event.code + ' - ' + event.reason);
                    updateStatus('已断开');
                };
                
                websocket.onerror = function(error) {
                    log('连接错误: ' + error);
                    updateStatus('连接错误');
                };
            } catch (error) {
                log('连接失败: ' + error);
            }
        }
        
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value;
            if (websocket && message) {
                websocket.send(message);
                log('发送消息: ' + message);
                input.value = '';
            }
        }
        
        // 页面加载时的提示
        log('页面加载完成，可以开始测试WebSocket连接');
    </script>
</body>
</html>
