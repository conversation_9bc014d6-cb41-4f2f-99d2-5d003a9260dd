<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ShippingAddressMapper">
    
    <!-- 结果映射 -->
    <resultMap id="addressResultMap" type="com.lzhshtp.shangcheng.model.ShippingAddress">
        <id property="addressId" column="lzhshtp_address_id"/>
        <result property="userId" column="lzhshtp_user_id"/>
        <result property="recipientName" column="lzhshtp_recipient_name"/>
        <result property="phoneNumber" column="lzhshtp_phone_number"/>
        <result property="province" column="lzhshtp_province"/>
        <result property="city" column="lzhshtp_city"/>
        <result property="district" column="lzhshtp_district"/>
        <result property="streetAddress" column="lzhshtp_street_address"/>
        <result property="postalCode" column="lzhshtp_postal_code"/>
        <result property="isDefault" column="lzhshtp_is_default"/>
        <result property="createdAt" column="lzhshtp_created_at"/>
        <result property="updatedAt" column="lzhshtp_updated_at"/>
    </resultMap>
    
    <!-- 根据用户ID获取所有收货地址 -->
    <select id="findByUserId" resultMap="addressResultMap">
        SELECT * FROM tb_lzhshtp_shipping_addresses
        WHERE lzhshtp_user_id = #{userId}
        ORDER BY lzhshtp_is_default DESC, lzhshtp_updated_at DESC
    </select>
    
    <!-- 根据ID获取收货地址 -->
    <select id="findById" resultMap="addressResultMap">
        SELECT * FROM tb_lzhshtp_shipping_addresses
        WHERE lzhshtp_address_id = #{addressId}
    </select>
    
    <!-- 新增收货地址 -->
    <insert id="insert" parameterType="com.lzhshtp.shangcheng.model.ShippingAddress" useGeneratedKeys="true" keyProperty="addressId">
        INSERT INTO tb_lzhshtp_shipping_addresses (
            lzhshtp_user_id,
            lzhshtp_recipient_name,
            lzhshtp_phone_number,
            lzhshtp_province,
            lzhshtp_city,
            lzhshtp_district,
            lzhshtp_street_address,
            lzhshtp_postal_code,
            lzhshtp_is_default
        ) VALUES (
            #{userId},
            #{recipientName},
            #{phoneNumber},
            #{province},
            #{city},
            #{district},
            #{streetAddress},
            #{postalCode},
            #{isDefault}
        )
    </insert>
    
    <!-- 更新收货地址 -->
    <update id="update" parameterType="com.lzhshtp.shangcheng.model.ShippingAddress">
        UPDATE tb_lzhshtp_shipping_addresses
        SET 
            lzhshtp_recipient_name = #{recipientName},
            lzhshtp_phone_number = #{phoneNumber},
            lzhshtp_province = #{province},
            lzhshtp_city = #{city},
            lzhshtp_district = #{district},
            lzhshtp_street_address = #{streetAddress},
            lzhshtp_postal_code = #{postalCode},
            lzhshtp_is_default = #{isDefault},
            lzhshtp_updated_at = NOW()
        WHERE lzhshtp_address_id = #{addressId}
    </update>
    
    <!-- 删除收货地址 -->
    <delete id="deleteById">
        DELETE FROM tb_lzhshtp_shipping_addresses
        WHERE lzhshtp_address_id = #{addressId}
    </delete>
    
    <!-- 将用户的所有地址设置为非默认 -->
    <update id="resetDefaultAddress">
        UPDATE tb_lzhshtp_shipping_addresses
        SET lzhshtp_is_default = FALSE
        WHERE lzhshtp_user_id = #{userId}
    </update>
    
    <!-- 设置默认地址 -->
    <update id="setDefaultAddress">
        UPDATE tb_lzhshtp_shipping_addresses
        SET lzhshtp_is_default = TRUE
        WHERE lzhshtp_address_id = #{addressId}
    </update>
    
    <!-- 获取用户的默认地址 -->
    <select id="findDefaultAddress" resultMap="addressResultMap">
        SELECT * FROM tb_lzhshtp_shipping_addresses
        WHERE lzhshtp_user_id = #{userId} AND lzhshtp_is_default = TRUE
        LIMIT 1
    </select>
    
    <!-- 检查地址是否属于指定用户 -->
    <select id="isAddressBelongsToUser" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM tb_lzhshtp_shipping_addresses
        WHERE lzhshtp_address_id = #{addressId} AND lzhshtp_user_id = #{userId}
    </select>
    
</mapper> 