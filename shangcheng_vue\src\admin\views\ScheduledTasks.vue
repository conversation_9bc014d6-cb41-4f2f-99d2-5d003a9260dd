<template>
  <div class="scheduled-tasks-container">
    <div class="page-header">
      <h1>定时任务管理</h1>
      <p class="page-description">管理系统定时任务，包括订单超时自动取消等功能</p>
    </div>

    <!-- 订单超时任务配置 -->
    <div class="task-card">
      <div class="task-header">
        <div class="task-info">
          <h2>订单超时自动取消</h2>
          <p class="task-description">自动取消超时未支付的订单，释放商品库存</p>
        </div>
        <div class="task-status">
          <el-switch
            v-model="taskConfig.enabled"
            @change="handleToggleTask"
            :loading="toggleLoading"
            active-text="启用"
            inactive-text="禁用"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </div>
      </div>

      <div class="task-config" v-if="taskConfig.enabled">
        <el-form :model="taskConfig" label-width="120px" class="config-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="超时时间">
                <el-select v-model="taskConfig.timeoutMinutes" placeholder="选择超时时间">
                  <el-option label="1分钟 (测试)" :value="1" />
                  <el-option label="30分钟" :value="30" />
                  <el-option label="60分钟" :value="60" />
                  <el-option label="120分钟" :value="120" />
                  <el-option label="180分钟" :value="180" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查频率">
                <el-select v-model="taskConfig.checkInterval" placeholder="选择检查频率">
                  <el-option label="每1分钟 (测试)" :value="1" />
                  <el-option label="每5分钟" :value="5" />
                  <el-option label="每10分钟" :value="10" />
                  <el-option label="每15分钟" :value="15" />
                  <el-option label="每30分钟" :value="30" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发送通知">
                <el-switch
                  v-model="taskConfig.sendNotification"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通知对象" v-if="taskConfig.sendNotification">
                <el-checkbox-group v-model="taskConfig.notificationTargets">
                  <el-checkbox label="buyer">买家</el-checkbox>
                  <el-checkbox label="seller">卖家</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleSaveConfig" :loading="saveLoading">
              保存配置
            </el-button>
            <el-button @click="handleExecuteTask" :loading="executeLoading">
              立即执行
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 执行历史 -->
      <div class="execution-history" v-if="taskConfig.enabled">
        <h3>最近执行记录</h3>
        <div class="history-item" v-if="lastExecution">
          <div class="execution-info">
            <span class="execution-time">最后执行：{{ formatDate(lastExecution.time) }}</span>
            <span class="execution-result" :class="lastExecution.success ? 'success' : 'error'">
              {{ lastExecution.result }}
            </span>
          </div>
        </div>
        <div v-else class="no-history">
          暂无执行记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getOrderTimeoutConfig,
  updateOrderTimeoutConfig,
  toggleTask,
  executeTask
} from '@/admin/api/tasks'

// 任务配置
const taskConfig = ref({
  enabled: true,
  timeoutMinutes: 30,
  checkInterval: 5,
  sendNotification: true,
  notificationMethods: ['message'],
  notificationTargets: ['buyer', 'seller']
})

// 加载状态
const toggleLoading = ref(false)
const saveLoading = ref(false)
const executeLoading = ref(false)

// 最后执行记录
const lastExecution = ref(null)

// 获取任务配置
const fetchTaskConfig = async () => {
  try {
    const response = await getOrderTimeoutConfig()
    if (response.success || response.code === 200) {
      taskConfig.value = response.data
    } else {
      ElMessage.error(response.message || '获取任务配置失败')
    }
  } catch (error) {
    console.error('获取任务配置失败:', error)
    ElMessage.error('获取任务配置失败')
  }
}

// 切换任务状态
const handleToggleTask = async () => {
  toggleLoading.value = true
  try {
    const response = await toggleTask('order_timeout_cancel', taskConfig.value.enabled)
    if (response.success || response.code === 200) {
      ElMessage.success(taskConfig.value.enabled ? '任务已启用' : '任务已禁用')
    } else {
      ElMessage.error(response.message || '操作失败')
      // 回滚状态
      taskConfig.value.enabled = !taskConfig.value.enabled
    }
  } catch (error) {
    console.error('切换任务状态失败:', error)
    ElMessage.error('操作失败')
    // 回滚状态
    taskConfig.value.enabled = !taskConfig.value.enabled
  } finally {
    toggleLoading.value = false
  }
}

// 保存配置
const handleSaveConfig = async () => {
  saveLoading.value = true
  try {
    const response = await updateOrderTimeoutConfig(taskConfig.value)
    if (response.success || response.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 立即执行任务
const handleExecuteTask = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要立即执行订单超时检查任务吗？',
      '执行确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    executeLoading.value = true
    const response = await executeTask('order_timeout_cancel')

    if (response.success || response.code === 200) {
      ElMessage.success('任务执行完成')
      // 更新执行记录
      lastExecution.value = {
        time: new Date(),
        success: true,
        result: response.data || '执行成功'
      }
    } else {
      ElMessage.error(response.message || '执行失败')
      lastExecution.value = {
        time: new Date(),
        success: false,
        result: response.message || '执行失败'
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行任务失败:', error)
      ElMessage.error('执行失败')
      lastExecution.value = {
        time: new Date(),
        success: false,
        result: '执行失败'
      }
    }
  } finally {
    executeLoading.value = false
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 页面加载时获取配置
onMounted(() => {
  fetchTaskConfig()
})
</script>

<style scoped>
.scheduled-tasks-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  color: #666;
  margin: 0;
}

.task-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.task-info h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.task-description {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.task-config {
  margin-bottom: 24px;
}

.config-form {
  max-width: 800px;
}

.execution-history {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.execution-history h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.history-item {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.execution-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.execution-time {
  color: #666;
  font-size: 14px;
}

.execution-result {
  font-size: 14px;
  font-weight: 500;
}

.execution-result.success {
  color: #67c23a;
}

.execution-result.error {
  color: #f56c6c;
}

.no-history {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}
</style>
