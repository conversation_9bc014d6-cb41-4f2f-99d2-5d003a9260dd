<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.SensitiveWordMapper">

    <!-- 结果映射 -->
    <resultMap id="SensitiveWordResultMap" type="com.lzhshtp.shangcheng.model.SensitiveWord">
        <id column="lzhshtp_word_id" property="wordId"/>
        <result column="lzhshtp_word" property="word"/>
        <result column="lzhshtp_word_type" property="wordType"/>
        <result column="lzhshtp_severity_level" property="severityLevel"/>
        <result column="lzhshtp_category" property="category"/>
        <result column="lzhshtp_is_active" property="isActive"/>
        <result column="lzhshtp_created_time" property="createdTime"/>
        <result column="lzhshtp_created_by" property="createdBy"/>
    </resultMap>

    <!-- 根据词类型查询敏感词 -->
    <select id="findByWordType" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_word_type = #{wordType} AND lzhshtp_is_active = true
        ORDER BY lzhshtp_severity_level DESC, lzhshtp_word
    </select>

    <!-- 查询所有启用的敏感词 -->
    <select id="findAllActive" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_is_active = true
        ORDER BY lzhshtp_word_type, lzhshtp_severity_level DESC, lzhshtp_word
    </select>

    <!-- 根据词查询敏感词 -->
    <select id="findByWord" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_word = #{word} AND lzhshtp_is_active = true
    </select>

    <!-- 根据类别查询敏感词 -->
    <select id="findByCategory" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_category = #{category} AND lzhshtp_is_active = true
        ORDER BY lzhshtp_severity_level DESC, lzhshtp_word
    </select>

    <!-- 根据严重程度查询敏感词 -->
    <select id="findBySeverityLevel" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_severity_level <![CDATA[>=]]> #{severityLevel} AND lzhshtp_is_active = true
        ORDER BY lzhshtp_severity_level DESC, lzhshtp_word
    </select>

    <!-- 模糊查询敏感词 -->
    <select id="findByWordLike" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_word LIKE CONCAT('%', #{keyword}, '%') AND lzhshtp_is_active = true
        ORDER BY lzhshtp_word_type, lzhshtp_severity_level DESC, lzhshtp_word
    </select>

    <!-- 统计各类型敏感词数量 -->
    <select id="countByWordType" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_word_type = #{wordType} AND lzhshtp_is_active = true
    </select>

    <!-- 批量查询敏感词 -->
    <select id="findByWords" resultMap="SensitiveWordResultMap">
        SELECT * FROM tb_lzhshtp_sensitive_words 
        WHERE lzhshtp_word IN
        <foreach collection="words" item="word" open="(" separator="," close=")">
            #{word}
        </foreach>
        AND lzhshtp_is_active = true
        ORDER BY lzhshtp_severity_level DESC
    </select>

</mapper>
