<template>
  <div class="auto-audit-view">
    <div class="page-header">
      <h2>自动审核查看</h2>
      <p class="page-description">查看商品自动审核结果和详细信息</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-row">
          <div class="form-item">
            <label>商品ID：</label>
            <input
              v-model="searchForm.productId"
              type="text"
              placeholder="请输入商品ID"
              class="form-input"
            />
          </div>
          <div class="form-item">
            <label>审核结果：</label>
            <select v-model="searchForm.decision" class="form-select">
              <option value="">全部</option>
              <option value="auto_approve">自动通过</option>
              <option value="auto_reject">自动拒绝</option>
              <option value="manual_review">人工审核</option>
            </select>
          </div>
          <div class="form-item">
            <label>审核时间：</label>
            <input
              v-model="searchForm.startDate"
              type="date"
              class="form-input"
            />
            <span class="date-separator">至</span>
            <input
              v-model="searchForm.endDate"
              type="date"
              class="form-input"
            />
          </div>
          <div class="form-item">
            <button @click="handleSearch" class="search-btn">搜索</button>
            <button @click="handleReset" class="reset-btn">重置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="total-count">共 {{ pagination.total }} 条记录</span>
      </div>

      <table class="data-table">
        <thead>
          <tr>
            <th>商品ID</th>
            <th>商品标题</th>
            <th>审核结果</th>
            <th>风险评分</th>
            <th>审核原因</th>
            <th>审核时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="record in auditRecords" :key="record.recordId">
            <td>{{ record.productId }}</td>
            <td class="product-title">{{ record.productTitle || '未知商品' }}</td>
            <td>
              <span
                class="status-badge"
                :class="getDecisionClass(record.finalDecision)"
              >
                {{ getDecisionText(record.finalDecision) }}
              </span>
            </td>
            <td>
              <span class="risk-score" :class="getRiskScoreClass(record.riskScore)">
                {{ record.riskScore || 0 }}
              </span>
            </td>
            <td class="reason-text">{{ record.decisionReason }}</td>
            <td>{{ formatDateTime(record.createdTime) }}</td>
            <td>
              <button @click="viewDetails(record)" class="action-btn view-btn">
                查看详情
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 分页 -->
      <div class="pagination">
        <button
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.current }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </span>
        <button
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>自动审核详情</h3>
          <button @click="closeDetailModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>商品ID：</label>
                <span>{{ selectedRecord?.productId }}</span>
              </div>
              <div class="detail-item">
                <label>审核结果：</label>
                <span
                  class="status-badge"
                  :class="getDecisionClass(selectedRecord?.finalDecision)"
                >
                  {{ getDecisionText(selectedRecord?.finalDecision) }}
                </span>
              </div>
              <div class="detail-item">
                <label>审核时间：</label>
                <span>{{ formatDateTime(selectedRecord?.createdTime) }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>审核详情</h4>
            <div class="audit-details">
              <div class="audit-item">
                <label>文字审核：</label>
                <div class="audit-result">
                  <div class="result-summary">{{ formatAuditResult(selectedRecord?.textAuditResult) }}</div>
                  <details class="result-details">
                    <summary>查看详细信息</summary>
                    <pre>{{ formatDetailedAuditResult(selectedRecord?.textAuditResult) }}</pre>
                  </details>
                </div>
              </div>
              <div class="audit-item">
                <label>图片审核：</label>
                <div class="audit-result">
                  <div class="result-summary">{{ formatAuditResult(selectedRecord?.imageAuditResult) }}</div>
                  <details class="result-details">
                    <summary>查看详细信息</summary>
                    <pre>{{ formatDetailedAuditResult(selectedRecord?.imageAuditResult) }}</pre>
                  </details>
                </div>
              </div>
              <div class="audit-item">
                <label>信用分审核：</label>
                <div class="audit-result">
                  <div class="result-summary">{{ formatAuditResult(selectedRecord?.creditAuditResult) }}</div>
                  <details class="result-details">
                    <summary>查看详细信息</summary>
                    <pre>{{ formatDetailedAuditResult(selectedRecord?.creditAuditResult) }}</pre>
                  </details>
                </div>
              </div>
              <div class="audit-item">
                <label>价格审核：</label>
                <div class="audit-result">
                  <div class="result-summary">{{ formatAuditResult(selectedRecord?.priceAuditResult) }}</div>
                  <details class="result-details">
                    <summary>查看详细信息</summary>
                    <pre>{{ formatDetailedAuditResult(selectedRecord?.priceAuditResult) }}</pre>
                  </details>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { autoAuditApi } from '@/admin/api/autoAudit'

// 响应式数据
const auditRecords = ref([])
const showDetailModal = ref(false)
const selectedRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  productId: '',
  decision: '',
  startDate: '',
  endDate: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 页面加载时获取数据
onMounted(() => {
  fetchAuditRecords()
})

// 获取审核记录
const fetchAuditRecords = async () => {
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...(searchForm.productId && { productId: searchForm.productId }),
      ...(searchForm.decision && { decision: searchForm.decision }),
      ...(searchForm.startDate && { startDate: searchForm.startDate }),
      ...(searchForm.endDate && { endDate: searchForm.endDate })
    }

    const result = await autoAuditApi.getRecords(params)

    if (result.success) {
      auditRecords.value = result.data.records || []
      pagination.total = result.data.total || 0
    } else {
      console.error('获取审核记录失败:', result.message)
      auditRecords.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取审核记录失败:', error)
    auditRecords.value = []
    pagination.total = 0
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchAuditRecords()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    productId: '',
    decision: '',
    startDate: '',
    endDate: ''
  })
  pagination.current = 1
  fetchAuditRecords()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= Math.ceil(pagination.total / pagination.pageSize)) {
    pagination.current = page
    fetchAuditRecords()
  }
}

// 查看详情
const viewDetails = (record) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedRecord.value = null
}

// 获取决策状态样式类
const getDecisionClass = (decision) => {
  const classMap = {
    'auto_approve': 'status-success',
    'auto_reject': 'status-danger',
    'manual_review': 'status-warning'
  }
  return classMap[decision] || ''
}

// 获取决策状态文本
const getDecisionText = (decision) => {
  const textMap = {
    'auto_approve': '自动通过',
    'auto_reject': '自动拒绝',
    'manual_review': '人工审核'
  }
  return textMap[decision] || '未知'
}

// 获取风险评分样式类
const getRiskScoreClass = (score) => {
  if (score >= 70) return 'risk-high'
  if (score >= 40) return 'risk-medium'
  return 'risk-low'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  // 处理后端返回的数组格式时间 [2025, 7, 24, 20, 15, 58]
  if (Array.isArray(dateTime) && dateTime.length >= 6) {
    // 注意：月份需要减1，因为JavaScript的月份是从0开始的
    const date = new Date(dateTime[0], dateTime[1] - 1, dateTime[2], dateTime[3], dateTime[4], dateTime[5])
    return date.toLocaleString('zh-CN')
  }

  // 处理标准日期字符串
  try {
    return new Date(dateTime).toLocaleString('zh-CN')
  } catch (error) {
    console.warn('日期格式化失败:', dateTime, error)
    return '-'
  }
}

// 格式化审核结果
const formatAuditResult = (result) => {
  if (!result) return '无数据'

  try {
    const parsed = JSON.parse(result)

    // 创建友好的显示格式
    const status = parsed.passed ? '✅ 通过' : '❌ 不通过'
    const score = `评分: ${parsed.score || 0}`
    const risk = `风险: ${getRiskLevelText(parsed.riskLevel)}`
    const reason = parsed.reason ? `原因: ${parsed.reason}` : ''

    return `${status} | ${score} | ${risk}${reason ? ' | ' + reason : ''}`
  } catch (error) {
    console.warn('解析审核结果失败:', result, error)
    return result
  }
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel) => {
  switch (riskLevel) {
    case 'HIGH': return '高风险'
    case 'MEDIUM': return '中风险'
    case 'LOW': return '低风险'
    default: return '未知'
  }
}

// 格式化详细审核结果（用于详情弹窗）
const formatDetailedAuditResult = (result) => {
  if (!result) return '无数据'

  try {
    const parsed = JSON.parse(result)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return result
  }
}
</script>

<style scoped>
.auto-audit-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-item label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-input, .form-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.date-separator {
  margin: 0 8px;
  color: #666;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn {
  background: #1890ff;
  color: white;
}

.reset-btn {
  background: #f5f5f5;
  color: #333;
  margin-left: 8px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.total-count {
  color: #666;
  font-size: 14px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.data-table th {
  background: #fafafa;
  font-weight: 500;
  color: #333;
}

.product-title {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-danger {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.risk-score {
  font-weight: 500;
}

.risk-low { color: #52c41a; }
.risk-medium { color: #faad14; }
.risk-high { color: #ff4d4f; }

.reason-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.view-btn {
  background: #1890ff;
  color: white;
}

.pagination {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  gap: 8px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.audit-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-item label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.audit-item pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

/* 新增样式 */
.audit-result {
  margin-top: 8px;
}

.result-summary {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.result-details {
  margin-top: 8px;
}

.result-details summary {
  cursor: pointer;
  color: #007bff;
  font-size: 12px;
  padding: 4px 0;
  user-select: none;
}

.result-details summary:hover {
  color: #0056b3;
}

.result-details pre {
  margin-top: 8px;
  font-size: 11px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
