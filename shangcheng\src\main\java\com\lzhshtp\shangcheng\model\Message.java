package com.lzhshtp.shangcheng.model;

import java.time.LocalDateTime;

public class Message {
    private Long lzhshtp_message_id;        // 消息唯一标识ID
    private Long lzhshtp_conversation_id;    // 所属会话ID
    private Long lzhshtp_sender_id;          // 消息发送方用户ID
    private String lzhshtp_content;          // 消息内容
    private LocalDateTime lzhshtp_sent_at;   // 消息发送时间
    private Boolean lzhshtp_is_read;         // 布尔值，表示消息是否已读
    private Boolean lzhshtp_is_system_message; // 布尔值，表示是否为系统消息

    // Getters and Setters
    public Long getLzhshtp_message_id() {
        return lzhshtp_message_id;
    }

    public void setLzhshtp_message_id(Long lzhshtp_message_id) {
        this.lzhshtp_message_id = lzhshtp_message_id;
    }

    public Long getLzhshtp_conversation_id() {
        return lzhshtp_conversation_id;
    }

    public void setLzhshtp_conversation_id(Long lzhshtp_conversation_id) {
        this.lzhshtp_conversation_id = lzhshtp_conversation_id;
    }

    public Long getLzhshtp_sender_id() {
        return lzhshtp_sender_id;
    }

    public void setLzhshtp_sender_id(Long lzhshtp_sender_id) {
        this.lzhshtp_sender_id = lzhshtp_sender_id;
    }

    public String getLzhshtp_content() {
        return lzhshtp_content;
    }

    public void setLzhshtp_content(String lzhshtp_content) {
        this.lzhshtp_content = lzhshtp_content;
    }

    public LocalDateTime getLzhshtp_sent_at() {
        return lzhshtp_sent_at;
    }

    public void setLzhshtp_sent_at(LocalDateTime lzhshtp_sent_at) {
        this.lzhshtp_sent_at = lzhshtp_sent_at;
    }

    public Boolean getLzhshtp_is_read() {
        return lzhshtp_is_read;
    }

    public void setLzhshtp_is_read(Boolean lzhshtp_is_read) {
        this.lzhshtp_is_read = lzhshtp_is_read;
    }

    public Boolean getLzhshtp_is_system_message() {
        return lzhshtp_is_system_message;
    }

    public void setLzhshtp_is_system_message(Boolean lzhshtp_is_system_message) {
        this.lzhshtp_is_system_message = lzhshtp_is_system_message;
    }
} 