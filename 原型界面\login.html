<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 二手交易平台</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF4D4F;
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            background-color: var(--white);
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            width: 100%;
            max-width: 420px;
            border: 1px solid var(--border-color);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header .logo {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }

        .login-header h1 {
            font-size: 16px;
            color: var(--text-color-light);
            font-weight: 400;
            margin-top: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-color-dark);
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.2);
        }

        .login-button {
            width: 100%;
            padding: 0.8rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            margin-top: 1rem;
            transition: background-color 0.2s ease;
        }

        .login-button:hover {
            background-color: var(--secondary-color);
        }

        .links {
            margin-top: 1.5rem;
            text-align: center;
            font-size: 14px;
        }

        .links a {
            color: var(--text-color-light);
            text-decoration: none;
            margin: 0 0.5rem;
            transition: color 0.2s ease;
        }

        .links a:hover {
            color: var(--primary-color);
        }
        
        .links span {
            color: #ddd;
        }

        .register-link {
            margin-top: 1.5rem;
            text-align: center;
            font-size: 14px;
        }

        .register-link a {
            color: var(--text-color-light);
            text-decoration: none;
            margin: 0 0.5rem;
            transition: color 0.2s ease;
        }

        .register-link a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <a href="index.html" class="logo">二手交易</a>
            <h1>用户登录</h1>
        </div>
        <form action="#" method="POST">
            <div class="form-group">
                <label for="username">用户名 / 手机号</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-button">登 录</button>
        </form>
        <div class="links">
            <a href="#forgot-password">忘记密码</a>
            <span>|</span>
            <a href="register.html">立即注册</a>
        </div>
        <div class="register-link">
            还没有账户？ <a href="register.html">立即注册</a>
        </div>
    </div>
</body>
</html> 