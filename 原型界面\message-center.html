<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息中心 - 二手交易平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FF4D4F; /* Red */
            --secondary-color: #FF7875;
            --text-color-dark: #333;
            --text-color-light: #666;
            --bg-color: #F5F5F5;
            --white: #FFFFFF;
            --border-color: #EFEFEF;
            --shadow-light: rgba(0,0,0,0.05);
            --notification-red: #E53935;
            --yellow-header: #FFD200;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 16px;
        }

        /* Top Nav - From Image */
        .top-nav {
            background: var(--white); 
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .top-nav .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }
        .top-nav .logo {
            font-size: 24px; 
            font-weight: bold;
            color: var(--primary-color);
            text-decoration: none;
        }
        .top-nav .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .top-nav .user-info a {
            color: var(--text-color-dark);
            text-decoration: none;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: color 0.2s ease;
        }
        .top-nav .user-info a:hover {
            color: var(--primary-color);
        }
        .top-nav .user-info svg {
            width: 18px;
            height: 18px;
        }

        /* Message Center Layout */
        .message-center-wrapper {
            display: flex;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-light);
            overflow: hidden;
            min-height: 70vh;
            max-height: 85vh;
        }

        .message-sidebar {
            flex-shrink: 0;
            width: 300px;
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
        }
        .sidebar-header .tab-buttons {
            display: flex;
            gap: 15px;
        }
        .sidebar-header .tab-buttons button {
            background: none;
            border: none;
            font-size: 16px;
            font-weight: 500;
            color: var(--text-color-light);
            cursor: pointer;
            position: relative;
            padding-bottom: 5px;
        }
        .sidebar-header .tab-buttons button.active {
            color: var(--text-color-dark);
        }
        .sidebar-header .tab-buttons button.active::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }
        .sidebar-header .tab-buttons button .badge {
            background-color: var(--notification-red);
            color: var(--white);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            position: absolute;
            top: -5px;
            right: -10px;
        }

        .sidebar-header .action-icons button {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-color-light);
            cursor: pointer;
            margin-left: 10px;
        }
        .sidebar-header .action-icons svg {
            width: 20px;
            height: 20px;
        }

        .conversation-list,
        .notification-list {
            flex-grow: 1;
            overflow-y: auto;
            padding: 0 0;
        }
        .conversation-list {
            display: flex; /* Ensure flex for scroll */
            flex-direction: column;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .list-item:hover {
            background-color: var(--bg-color);
        }
        .list-item.active {
            background-color: var(--bg-color);
            border-left: 3px solid var(--primary-color);
            padding-left: 17px; /* Adjust for border */
        }

        .list-item img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .item-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .item-content .top-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        .item-content .name {
            font-weight: 500;
            font-size: 16px;
            color: var(--text-color-dark);
        }
        .item-content .time {
            font-size: 12px;
            color: var(--text-color-light);
        }
        .item-content .last-message {
            font-size: 14px;
            color: var(--text-color-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .list-item .unread-count {
            background-color: var(--notification-red);
            color: var(--white);
            font-size: 11px;
            padding: 3px 7px;
            border-radius: 12px;
            margin-left: 10px;
            flex-shrink: 0;
        }

        /* Notification Specific Styles */
        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .notification-item:hover { background-color: var(--bg-color); }
        .notification-item.active { background-color: var(--bg-color); border-left: 3px solid var(--primary-color); padding-left: 17px; }

        .notification-icon {
            width: 40px;
            height: 40px;
            background-color: #FFEDD5; /* Light orange */
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .notification-icon svg {
            width: 24px;
            height: 24px;
            color: #F59E0B; /* Orange */
        }
        .notification-content .title {
            font-weight: 500;
            font-size: 15px;
            margin-bottom: 4px;
        }
        .notification-content .message {
            font-size: 13px;
            color: var(--text-color-medium);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .notification-content .time {
            font-size: 11px;
            color: var(--text-color-light);
            margin-top: 5px;
        }
        .notification-item .unread-dot {
            width: 8px;
            height: 8px;
            background-color: var(--notification-red);
            border-radius: 50%;
            margin-left: auto; /* Push to right */
            flex-shrink: 0;
        }


        .message-main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            color: var(--text-color-light);
        }
        .message-main-content img {
            width: 150px;
            height: 150px;
            margin-bottom: 20px;
        }
        .message-main-content h3 {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        .message-main-content p {
            font-size: 14px;
            line-height: 1.5;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .message-center-wrapper { flex-direction: column; min-height: unset; max-height: unset; }
            .message-sidebar { width: 100%; border-right: none; border-bottom: 1px solid var(--border-color); }
            .sidebar-header { flex-direction: column; align-items: flex-start; gap: 10px; }
            .sidebar-header .tab-buttons { width: 100%; justify-content: space-around; }
            .sidebar-header .action-icons { margin-top: 10px; }
            .message-main-content { padding: 40px 20px; }
            .top-nav .user-info { display: none; } /* Hide user info on small screens for simplicity */
            .top-nav .logo { font-size: 24px; }
        }

    </style>
</head>
<body>
    <nav class="top-nav">
        <div class="nav-content">
            <a href="main.html" class="logo">二手交易</a> <!-- Logo from main.html -->
            <div class="user-info">
                <a href="user-profile.html">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><path d="M20 21v-2a4 4 0 0 0-3-3.87m-2-6.48V3c0-1.66-1.34-3-3-3S7 1.34 7 3v2.13M12 21v-8M7 21v-4M17 21v-4"></path></svg>
                    <span>我的主页</span>
                </a>
                <a href="#">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect></svg>
                    <span>订单</span>
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="message-center-wrapper">
            <aside class="message-sidebar">
                <div class="sidebar-header">
                    <div class="tab-buttons">
                        <button id="messagesTab" class="active">消息 <span class="badge">44</span></button>
                        <button id="notificationsTab">通知消息 <span class="badge">44</span></button>
                    </div>
                    <div class="action-icons">
                        <button title="标记已读">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                        </button>
                        <button title="设置">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0-.33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1.51-1V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
                        </button>
                    </div>
                </div>

                <div class="conversation-list" id="conversationList">
                    <div class="list-item active">
                        <img src="https://via.placeholder.com/50/FF6347/FFFFFF?text=A" alt="User Avatar">
                        <div class="item-content">
                            <div class="top-line">
                                <span class="name">二次元新朋友</span>
                                <span class="time">05-31</span>
                            </div>
                            <span class="last-message">送你1个无门槛红包，快来领取吧！</span>
                        </div>
                        <span class="unread-count">44</span>
                    </div>
                    <div class="list-item">
                        <img src="https://via.placeholder.com/50/3498DB/FFFFFF?text=B" alt="User Avatar">
                        <div class="item-content">
                            <div class="top-line">
                                <span class="name">小李</span>
                                <span class="time">昨天</span>
                            </div>
                            <span class="last-message">好的，我在小区门口等你。</span>
                        </div>
                    </div>
                    <div class="list-item">
                        <img src="https://via.placeholder.com/50/27AE60/FFFFFF?text=C" alt="User Avatar">
                        <div class="item-content">
                            <div class="top-line">
                                <span class="name">官方客服</span>
                                <span class="time">2天前</span>
                            </div>
                            <span class="last-message">您好，关于您的退款申请已处理完成。</span>
                        </div>
                    </div>
                    <div class="list-item">
                        <img src="https://via.placeholder.com/50/E74C3C/FFFFFF?text=D" alt="User Avatar">
                        <div class="item-content">
                            <div class="top-line">
                                <span class="name">王阿姨</span>
                                <span class="time">上周</span>
                            </div>
                            <span class="last-message">请问这个电饭煲还在吗？</span>
                        </div>
                        <span class="unread-count">1</span>
                    </div>
                    <!-- More conversation items -->
                </div>

                <div class="notification-list" id="notificationList" style="display: none;">
                    <div class="notification-item active">
                        <div class="notification-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>
                        </div>
                        <div class="item-content notification-content">
                            <span class="title">系统通知</span>
                            <span class="message">您发布的"iPhone 14 Pro"被成功出售。</span>
                            <span class="time">05-30 14:30</span>
                        </div>
                        <div class="unread-dot"></div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h0a2 2 0 0 1-2-2V8.67a1 1 0 0 0-1.76-.84L6 6h0a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V8.67M20 18h0a2 2 0 0 1-2-2V8.67a1 1 0 0 0-1.76-.84L14 6h0a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V8.67"></path></svg>
                        </div>
                        <div class="item-content notification-content">
                            <span class="title">交易提醒</span>
                            <span class="message">您的订单"Apple Watch Ultra"已发货。</span>
                            <span class="time">05-29 10:00</span>
                        </div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4L15.6 9l-2.1 2.1a3 3 0 0 0 0 4.2l.4.4a3 3 0 0 0 4.2 0L20 13.4l2.1-.9a1 1 0 0 0 0-1.4L21 11l-1.4-1.4a1 1 0 0 0-1.4 0Z"></path><path d="M5.5 8.5 9 12l-2 3H2l3.5-3.5Z"></path><path d="M18 12a6 6 0 0 0-6-6H9l-1.5 1.5a.5.5 0 0 1-.7.7L5.5 8.5Z"></path></svg>
                        </div>
                        <div class="item-content notification-content">
                            <span class="title">活动通知</span>
                            <span class="message">二手交易平台夏季大促即将开始！</span>
                            <span class="time">05-28 09:00</span>
                        </div>
                        <div class="unread-dot"></div>
                    </div>
                    <!-- More notification items -->
                </div>
            </aside>

            <main class="message-main-content" id="messageMainContent">
                <img src="https://img.alicdn.com/imgextra/i4/O1CN01W22L2O1tC9vR2B75S_!!6000000005868-2-tps-200-200.png" alt="No contact selected">
                <h3>尚未选择任何联系人</h3>
                <p>快点左侧列表聊起来吧~</p>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const messagesTab = document.getElementById('messagesTab');
            const notificationsTab = document.getElementById('notificationsTab');
            const conversationList = document.getElementById('conversationList');
            const notificationList = document.getElementById('notificationList');
            const messageMainContent = document.getElementById('messageMainContent');

            const selectTab = (tabToActivate) => {
                if (tabToActivate === 'messages') {
                    messagesTab.classList.add('active');
                    notificationsTab.classList.remove('active');
                    conversationList.style.display = 'flex';
                    notificationList.style.display = 'none';
                } else if (tabToActivate === 'notifications') {
                    notificationsTab.classList.add('active');
                    messagesTab.classList.remove('active');
                    notificationList.style.display = 'flex';
                    conversationList.style.display = 'none';
                }
                // Reset main content to default empty state
                messageMainContent.innerHTML = `
                    <img src="https://img.alicdn.com/imgextra/i4/O1CN01W22L2O1tC9vR2B75S_!!6000000005868-2-tps-200-200.png" alt="No contact selected">
                    <h3>尚未选择任何联系人</h3>
                    <p>快点左侧列表聊起来吧~</p>
                `;
            };

            messagesTab.addEventListener('click', () => selectTab('messages'));
            notificationsTab.addEventListener('click', () => selectTab('notifications'));

            // Initial state
            selectTab('messages');

            // Basic logic for selecting an item (can be expanded for actual chat/notification view)
            const allListItems = document.querySelectorAll('.list-item, .notification-item');
            allListItems.forEach(item => {
                item.addEventListener('click', () => {
                    allListItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                    // In a real app, this would load chat/notification content
                    const type = item.classList.contains('list-item') ? '消息' : '通知';
                    const name = item.querySelector('.name, .title').textContent;
                    messageMainContent.innerHTML = `
                        <div style="padding: 20px; text-align: left; width: 100%;">
                            <h3 style="margin-bottom: 15px;">${type}详情：${name}</h3>
                            <p style="color: var(--text-color-dark);">这里是"${name}"的详细内容或聊天记录。</p>
                            <p style="font-size: 13px; color: var(--text-color-light); margin-top: 10px;">（此为演示内容，实际数据需后端提供）</p>
                        </div>
                    `;
                });
            });
        });
    </script>
</body>
</html> 