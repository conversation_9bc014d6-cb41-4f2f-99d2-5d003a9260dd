<template>
  <div class="feedback-dialog-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="feedback-dialog">
      <div class="feedback-dialog-header">
        <h3>用户反馈</h3>
        <button class="close-button" @click="close">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="feedback-dialog-body">
        <div class="form-group">
          <label for="feedbackType">反馈类型 <span class="required">*</span></label>
          <select id="feedbackType" v-model="form.feedbackType" class="form-control" :class="{ 'is-invalid': submitted && !form.feedbackType }">
            <option value="">请选择反馈类型</option>
            <option value="bug_report">问题反馈</option>
            <option value="suggestion">功能建议</option>
            <option value="complaint">投诉</option>
            <option value="abuse_report">举报</option>
          </select>
          <div class="invalid-feedback" v-if="submitted && !form.feedbackType">请选择反馈类型</div>
        </div>
        
        <!-- 举报相关选项，仅当类型为举报时显示 -->
        <div v-if="form.feedbackType === 'abuse_report'">
          <div class="form-group">
            <label for="relatedEntityType">举报对象类型</label>
            <select id="relatedEntityType" v-model="form.relatedEntityType" class="form-control">
              <option value="">请选择举报对象类型（可选）</option>
              <option value="user">用户</option>
              <option value="product">商品</option>
              <option value="post">论坛帖子</option>
              <option value="comment">评论</option>
            </select>
            <div class="form-hint">请在反馈内容中详细描述您要举报的对象信息</div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="content">反馈内容 <span class="required">*</span></label>
          <textarea id="content" v-model="form.content" class="form-control" :class="{ 'is-invalid': submitted && !form.content }" rows="5" placeholder="请详细描述您的反馈内容，至少10个字符"></textarea>
          <div class="invalid-feedback" v-if="submitted && !form.content">请输入反馈内容</div>
          <div class="invalid-feedback" v-if="submitted && form.content && form.content.length < 10">反馈内容至少需要10个字符</div>
          <div class="char-count" :class="{ 'text-danger': form.content.length > 1000 }">
            {{ form.content.length }}/1000
          </div>
        </div>
      </div>
      
      <div class="feedback-dialog-footer">
        <button class="btn btn-secondary" @click="close">取消</button>
        <button class="btn btn-primary" @click="submitFeedback" :disabled="loading">
          <span v-if="loading" class="spinner-border spinner-border-sm mr-1"></span>
          提交反馈
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import { submitFeedback as submitFeedbackAPI } from '@/api/feedback';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'success']);

const form = reactive({
  feedbackType: '',
  relatedEntityType: '',
  content: ''
});

const loading = ref(false);
const submitted = ref(false);

// 重置表单
const resetForm = () => {
  form.feedbackType = '';
  form.relatedEntityType = '';
  form.content = '';
  submitted.value = false;
};

// 关闭弹窗
const close = () => {
  emit('close');
  resetForm();
};

// 点击遮罩层关闭
const handleOverlayClick = () => {
  close();
};

// 提交反馈
const submitFeedback = async () => {
  submitted.value = true;
  
  // 表单验证
  if (!form.feedbackType) return;
  if (!form.content || form.content.length < 10 || form.content.length > 1000) return;
  
  loading.value = true;
  
  try {
    const response = await submitFeedbackAPI({
      feedbackType: form.feedbackType,
      relatedEntityType: form.relatedEntityType || null,
      content: form.content
    });
    
    if (response.code === 200) {
      // 提交成功
      emit('success', response.data);
      close();
    } else {
      // 提交失败
      alert(response.message || '提交失败，请稍后再试');
    }
  } catch (error) {
    console.error('提交反馈失败:', error);
    alert('提交失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 监听ESC键关闭弹窗
const handleEscKey = (event) => {
  if (event.key === 'Escape' && props.visible) {
    close();
  }
};

// 挂载和卸载ESC键监听
onMounted(() => {
  document.addEventListener('keydown', handleEscKey);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey);
});
</script>

<style scoped>
.feedback-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.feedback-dialog {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.feedback-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.feedback-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f5f5f5;
}

.feedback-dialog-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.feedback-dialog-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #eee;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
}

.required {
  color: #f56c6c;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #409eff;
}

.form-control.is-invalid {
  border-color: #f56c6c;
}

.invalid-feedback {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.form-hint {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.text-danger {
  color: #f56c6c;
}

.btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background-color: #409eff;
  color: white;
}

.btn-primary:hover {
  background-color: #66b1ff;
}

.btn-primary:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #f4f4f5;
  color: #606266;
}

.btn-secondary:hover {
  background-color: #e9e9eb;
}

.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 0.2em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
  margin-right: 6px;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 576px) {
  .feedback-dialog {
    width: 95%;
    max-height: 90vh;
  }
  
  .feedback-dialog-body {
    max-height: 60vh;
  }
}
</style> 