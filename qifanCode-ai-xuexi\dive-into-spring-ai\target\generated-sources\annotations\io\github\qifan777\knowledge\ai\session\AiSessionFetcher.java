package io.github.qifan777.knowledge.ai.session;

import io.github.qifan777.knowledge.ai.messge.AiMessage;
import io.github.qifan777.knowledge.ai.messge.AiMessageTable;
import io.github.qifan777.knowledge.user.User;
import io.github.qifan777.knowledge.user.UserTable;
import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.ListFieldConfig;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = AiSession.class
)
public class AiSessionFetcher extends AbstractTypedFetcher<AiSession, AiSessionFetcher> {
    public static final AiSessionFetcher $ = new AiSessionFetcher(null);

    private AiSessionFetcher(FetcherImpl<AiSession> base) {
        super(AiSession.class, base);
    }

    private AiSessionFetcher(AiSessionFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private AiSessionFetcher(AiSessionFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static AiSessionFetcher $from(Fetcher<AiSession> base) {
        return base instanceof AiSessionFetcher ? 
        	(AiSessionFetcher)base : 
        	new AiSessionFetcher((FetcherImpl<AiSession>)base);
    }

    @NewChain
    public AiSessionFetcher createdTime() {
        return add("createdTime");
    }

    @NewChain
    public AiSessionFetcher createdTime(boolean enabled) {
        return enabled ? add("createdTime") : remove("createdTime");
    }

    @NewChain
    public AiSessionFetcher editedTime() {
        return add("editedTime");
    }

    @NewChain
    public AiSessionFetcher editedTime(boolean enabled) {
        return enabled ? add("editedTime") : remove("editedTime");
    }

    @NewChain
    public AiSessionFetcher editor() {
        return add("editor");
    }

    @NewChain
    public AiSessionFetcher editor(boolean enabled) {
        return enabled ? add("editor") : remove("editor");
    }

    @NewChain
    public AiSessionFetcher editor(Fetcher<User> childFetcher) {
        return add("editor", childFetcher);
    }

    @NewChain
    public AiSessionFetcher editor(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("editor", childFetcher, fieldConfig);
    }

    @NewChain
    public AiSessionFetcher editor(IdOnlyFetchType idOnlyFetchType) {
        return add("editor", idOnlyFetchType);
    }

    @NewChain
    public AiSessionFetcher creator() {
        return add("creator");
    }

    @NewChain
    public AiSessionFetcher creator(boolean enabled) {
        return enabled ? add("creator") : remove("creator");
    }

    @NewChain
    public AiSessionFetcher creator(Fetcher<User> childFetcher) {
        return add("creator", childFetcher);
    }

    @NewChain
    public AiSessionFetcher creator(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("creator", childFetcher, fieldConfig);
    }

    @NewChain
    public AiSessionFetcher creator(IdOnlyFetchType idOnlyFetchType) {
        return add("creator", idOnlyFetchType);
    }

    @NewChain
    public AiSessionFetcher name() {
        return add("name");
    }

    @NewChain
    public AiSessionFetcher name(boolean enabled) {
        return enabled ? add("name") : remove("name");
    }

    @NewChain
    public AiSessionFetcher messages() {
        return add("messages");
    }

    @NewChain
    public AiSessionFetcher messages(boolean enabled) {
        return enabled ? add("messages") : remove("messages");
    }

    @NewChain
    public AiSessionFetcher messages(Fetcher<AiMessage> childFetcher) {
        return add("messages", childFetcher);
    }

    @NewChain
    public AiSessionFetcher messages(Fetcher<AiMessage> childFetcher,
            Consumer<ListFieldConfig<AiMessage, AiMessageTable>> fieldConfig) {
        return add("messages", childFetcher, fieldConfig);
    }

    @Override
    protected AiSessionFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new AiSessionFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected AiSessionFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new AiSessionFetcher(this, prop, fieldConfig);
    }
}
