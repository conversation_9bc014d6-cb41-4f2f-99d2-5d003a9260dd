package com.lzhshtp.shangcheng.mapper;


import com.lzhshtp.shangcheng.dto.statistics.LocationStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 统计数据Mapper
 */
@Mapper
public interface StatisticsMapper {

    // ==================== 用户统计 ====================

    /**
     * 获取总用户数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_users WHERE lzhshtp_is_active = TRUE")
    Long getTotalUsers();

    /**
     * 获取活跃用户数（最近N天登录过的用户）
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_users " +
            "WHERE lzhshtp_is_active = TRUE " +
            "AND lzhshtp_last_login_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)")
    Long getActiveUsers(@Param("days") Integer days);

    /**
     * 获取指定日期的新增用户数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_users " +
            "WHERE DATE(lzhshtp_registration_date) = #{date}")
    Long getDailyNewUsers(@Param("date") LocalDate date);

    /**
     * 获取用户角色分布
     */
    @Select("SELECT lzhshtp_role as role, COUNT(*) as count " +
            "FROM tb_lzhshtp_users " +
            "WHERE lzhshtp_is_active = TRUE " +
            "GROUP BY lzhshtp_role")
    List<Map<String, Object>> getUserRoleDistribution();

    /**
     * 获取用户地域分布Top N
     */
    @Select("SELECT lzhshtp_location as location, COUNT(*) as userCount, " +
            "ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM tb_lzhshtp_users WHERE lzhshtp_is_active = TRUE), 2) as percentage " +
            "FROM tb_lzhshtp_users " +
            "WHERE lzhshtp_is_active = TRUE AND lzhshtp_location IS NOT NULL " +
            "GROUP BY lzhshtp_location " +
            "ORDER BY userCount DESC " +
            "LIMIT #{limit}")
    List<LocationStatisticsDTO> getTopUserLocations(@Param("limit") Integer limit);

    // ==================== 商品统计 ====================

    /**
     * 获取总商品数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products")
    Long getTotalProducts();

    /**
     * 获取可用商品数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products WHERE lzhshtp_status = 'available'")
    Long getAvailableProducts();

    /**
     * 获取指定日期的新增商品数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_products " +
            "WHERE DATE(lzhshtp_posted_date) = #{date}")
    Long getDailyNewProducts(@Param("date") LocalDate date);

    /**
     * 获取商品状态分布
     */
    @Select("SELECT lzhshtp_status as status, COUNT(*) as count " +
            "FROM tb_lzhshtp_products " +
            "GROUP BY lzhshtp_status")
    List<Map<String, Object>> getProductStatusDistribution();

    /**
     * 获取商品分类分布
     */
    @Select("SELECT c.lzhshtp_category_name as categoryName, COUNT(p.lzhshtp_product_id) as productCount, " +
            "ROUND(COUNT(p.lzhshtp_product_id) * 100.0 / (SELECT COUNT(*) FROM tb_lzhshtp_products), 2) as percentage " +
            "FROM tb_lzhshtp_product_categories c " +
            "LEFT JOIN tb_lzhshtp_products p ON c.lzhshtp_category_id = p.lzhshtp_category_id " +
            "GROUP BY c.lzhshtp_category_id, c.lzhshtp_category_name " +
            "ORDER BY productCount DESC")
    List<Map<String, Object>> getProductCategoryDistribution();

    // ==================== 订单统计 ====================

    /**
     * 获取总订单数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_orders")
    Long getTotalOrders();

    /**
     * 获取已完成订单数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_orders WHERE lzhshtp_status = 'completed'")
    Long getCompletedOrders();

    /**
     * 获取总交易额
     */
    @Select("SELECT COALESCE(SUM(lzhshtp_total_amount), 0) FROM tb_lzhshtp_orders " +
            "WHERE lzhshtp_status IN ('completed', 'delivered')")
    BigDecimal getTotalRevenue();

    /**
     * 获取指定日期的订单数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_orders " +
            "WHERE DATE(lzhshtp_order_date) = #{date}")
    Long getDailyOrders(@Param("date") LocalDate date);

    /**
     * 获取指定日期的交易额
     */
    @Select("SELECT COALESCE(SUM(lzhshtp_total_amount), 0) FROM tb_lzhshtp_orders " +
            "WHERE DATE(lzhshtp_order_date) = #{date} " +
            "AND lzhshtp_status IN ('completed', 'delivered')")
    BigDecimal getDailyRevenue(@Param("date") LocalDate date);

    /**
     * 获取订单状态分布
     */
    @Select("SELECT lzhshtp_status as status, COUNT(*) as count " +
            "FROM tb_lzhshtp_orders " +
            "GROUP BY lzhshtp_status")
    List<Map<String, Object>> getOrderStatusDistribution();

    // ==================== 论坛统计 ====================

    /**
     * 获取总帖子数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_posts")
    Long getTotalForumPosts();

    /**
     * 获取总评论数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_comments")
    Long getTotalForumComments();

    /**
     * 获取指定日期的新增帖子数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_posts " +
            "WHERE DATE(lzhshtp_posted_at) = #{date}")
    Long getDailyNewForumPosts(@Param("date") LocalDate date);

    /**
     * 获取指定日期的新增评论数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_forum_comments " +
            "WHERE DATE(lzhshtp_commented_at) = #{date}")
    Long getDailyNewForumComments(@Param("date") LocalDate date);

    /**
     * 获取指定日期的帖子浏览量
     */
    @Select("SELECT COALESCE(SUM(lzhshtp_views_count), 0) FROM tb_lzhshtp_forum_posts " +
            "WHERE DATE(lzhshtp_posted_at) = #{date}")
    Long getDailyForumViews(@Param("date") LocalDate date);

    /**
     * 获取总浏览量
     */
    @Select("SELECT COALESCE(SUM(lzhshtp_views_count), 0) FROM tb_lzhshtp_forum_posts")
    Long getTotalForumViews();

    /**
     * 获取论坛分类统计
     */
    @Select("SELECT fc.lzhshtp_forum_category_id as categoryId, " +
            "fc.lzhshtp_category_name as categoryName, " +
            "COUNT(fp.lzhshtp_post_id) as postCount, " +
            "COALESCE(SUM(fp.lzhshtp_views_count), 0) as viewCount, " +
            "COALESCE((SELECT COUNT(*) FROM tb_lzhshtp_forum_comments fcom " +
            "          JOIN tb_lzhshtp_forum_posts fp2 ON fcom.lzhshtp_post_id = fp2.lzhshtp_post_id " +
            "          WHERE fp2.lzhshtp_forum_category_id = fc.lzhshtp_forum_category_id), 0) as commentCount, " +
            "ROUND(COUNT(fp.lzhshtp_post_id) * 100.0 / (SELECT COUNT(*) FROM tb_lzhshtp_forum_posts), 2) as percentage " +
            "FROM tb_lzhshtp_forum_categories fc " +
            "LEFT JOIN tb_lzhshtp_forum_posts fp ON fc.lzhshtp_forum_category_id = fp.lzhshtp_forum_category_id " +
            "GROUP BY fc.lzhshtp_forum_category_id, fc.lzhshtp_category_name " +
            "ORDER BY postCount DESC")
    List<Map<String, Object>> getForumCategoryStatistics();

    /**
     * 获取论坛帖子状态分布
     */
    @Select("SELECT lzhshtp_status as status, COUNT(*) as count " +
            "FROM tb_lzhshtp_forum_posts " +
            "GROUP BY lzhshtp_status")
    List<Map<String, Object>> getForumPostStatusDistribution();

    /**
     * 获取活跃论坛用户数（发过帖或评论的用户）
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM (" +
            "SELECT lzhshtp_author_id as user_id FROM tb_lzhshtp_forum_posts " +
            "UNION " +
            "SELECT lzhshtp_author_id as user_id FROM tb_lzhshtp_forum_comments" +
            ") as active_users")
    Long getActiveForumUsers();

    /**
     * 获取热门帖子（按浏览量）
     */
    @Select("SELECT fp.lzhshtp_post_id as postId, fp.lzhshtp_title as title, " +
            "fc.lzhshtp_category_name as categoryName, u.lzhshtp_username as authorName, " +
            "fp.lzhshtp_views_count as viewCount, " +
            "(SELECT COUNT(*) FROM tb_lzhshtp_forum_comments WHERE lzhshtp_post_id = fp.lzhshtp_post_id) as commentCount, " +
            "fp.lzhshtp_posted_at as postedAt " +
            "FROM tb_lzhshtp_forum_posts fp " +
            "LEFT JOIN tb_lzhshtp_forum_categories fc ON fp.lzhshtp_forum_category_id = fc.lzhshtp_forum_category_id " +
            "LEFT JOIN tb_lzhshtp_users u ON fp.lzhshtp_author_id = u.lzhshtp_user_id " +
            "WHERE fp.lzhshtp_status = 0 " +
            "ORDER BY fp.lzhshtp_views_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotPostsByViews(@Param("limit") Integer limit);

    /**
     * 获取热门帖子（按评论数）
     */
    @Select("SELECT fp.lzhshtp_post_id as postId, fp.lzhshtp_title as title, " +
            "fc.lzhshtp_category_name as categoryName, u.lzhshtp_username as authorName, " +
            "fp.lzhshtp_views_count as viewCount, " +
            "COUNT(fcom.lzhshtp_comment_id) as commentCount, " +
            "fp.lzhshtp_posted_at as postedAt " +
            "FROM tb_lzhshtp_forum_posts fp " +
            "LEFT JOIN tb_lzhshtp_forum_categories fc ON fp.lzhshtp_forum_category_id = fc.lzhshtp_forum_category_id " +
            "LEFT JOIN tb_lzhshtp_users u ON fp.lzhshtp_author_id = u.lzhshtp_user_id " +
            "LEFT JOIN tb_lzhshtp_forum_comments fcom ON fp.lzhshtp_post_id = fcom.lzhshtp_post_id " +
            "WHERE fp.lzhshtp_status = 0 " +
            "GROUP BY fp.lzhshtp_post_id " +
            "ORDER BY commentCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotPostsByComments(@Param("limit") Integer limit);

    // ==================== 收藏统计 ====================

    /**
     * 获取总收藏数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_user_favorites")
    Long getTotalFavorites();

    /**
     * 获取指定日期的新增收藏数
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_user_favorites " +
            "WHERE DATE(lzhshtp_favorited_at) = #{date}")
    Long getDailyNewFavorites(@Param("date") LocalDate date);

    // ==================== 热门商品统计 ====================

    /**
     * 获取热门商品（按收藏量）
     */
    @Select("SELECT p.lzhshtp_product_id as productId, p.lzhshtp_title as title, " +
            "c.lzhshtp_category_name as categoryName, p.lzhshtp_price as price, " +
            "u.lzhshtp_username as sellerName, COUNT(f.lzhshtp_favorite_id) as value, " +
            "'favorites' as valueType " +
            "FROM tb_lzhshtp_products p " +
            "LEFT JOIN tb_lzhshtp_user_favorites f ON p.lzhshtp_product_id = f.lzhshtp_product_id " +
            "LEFT JOIN tb_lzhshtp_product_categories c ON p.lzhshtp_category_id = c.lzhshtp_category_id " +
            "LEFT JOIN tb_lzhshtp_users u ON p.lzhshtp_seller_id = u.lzhshtp_user_id " +
            "WHERE p.lzhshtp_status = 'available' " +
            "GROUP BY p.lzhshtp_product_id " +
            "ORDER BY value DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotProductsByFavorites(@Param("limit") Integer limit);

    /**
     * 获取热门商品（按订单量）
     */
    @Select("SELECT p.lzhshtp_product_id as productId, p.lzhshtp_title as title, " +
            "c.lzhshtp_category_name as categoryName, p.lzhshtp_price as price, " +
            "u.lzhshtp_username as sellerName, COUNT(o.lzhshtp_order_id) as value, " +
            "'orders' as valueType " +
            "FROM tb_lzhshtp_products p " +
            "LEFT JOIN tb_lzhshtp_orders o ON p.lzhshtp_product_id = o.lzhshtp_product_id " +
            "LEFT JOIN tb_lzhshtp_product_categories c ON p.lzhshtp_category_id = c.lzhshtp_category_id " +
            "LEFT JOIN tb_lzhshtp_users u ON p.lzhshtp_seller_id = u.lzhshtp_user_id " +
            "GROUP BY p.lzhshtp_product_id " +
            "ORDER BY value DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotProductsByOrders(@Param("limit") Integer limit);
}
