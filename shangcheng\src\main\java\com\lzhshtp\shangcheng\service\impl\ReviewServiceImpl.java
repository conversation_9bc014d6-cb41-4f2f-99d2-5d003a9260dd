package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.mapper.*;
import com.lzhshtp.shangcheng.model.*;
import com.lzhshtp.shangcheng.service.ReviewService;
import com.lzhshtp.shangcheng.service.CreditScoreService;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评价服务实现类
 */
@Service
public class ReviewServiceImpl implements ReviewService {

    private static final Logger logger = LoggerFactory.getLogger(ReviewServiceImpl.class);

    @Autowired
    private SellerReviewMapper sellerReviewMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private CreditScoreService creditScoreService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 提交卖家评价
     */
    @Override
    @Transactional
    public ReviewResponse submitSellerReview(Long buyerId, ReviewRequest reviewRequest) {
        try {
            // 1. 验证评价权限
            if (!canReviewOrder(reviewRequest.getOrderId(), buyerId)) {
                throw new IllegalArgumentException("无权评价此订单或订单状态不允许评价");
            }

            // 2. 检查是否已评价
            if (hasReviewed(reviewRequest.getOrderId())) {
                throw new IllegalArgumentException("此订单已经评价过，不能重复评价");
            }

            // 3. 获取订单信息
            Order order = orderMapper.selectById(reviewRequest.getOrderId());
            if (order == null) {
                throw new IllegalArgumentException("订单不存在");
            }

            // 4. 获取卖家信息
            User seller = userMapper.selectById(order.getSellerId());
            if (seller == null) {
                throw new IllegalArgumentException("卖家不存在");
            }

            // 5. 防刷分检查
            if (!validateReviewFrequency(buyerId, order.getSellerId())) {
                throw new IllegalArgumentException("评价过于频繁，请稍后再试");
            }

            // 6. 计算信用分变化
            int scoreChange = calculateScoreChange(reviewRequest.getRating(), buyerId, order.getSellerId());
            int oldScore = seller.getCreditScore();
            int newScore = Math.max(10, Math.min(100, oldScore + scoreChange));

            // 7. 创建评价记录
            SellerReview review = SellerReview.builder()
                    .orderId(reviewRequest.getOrderId())
                    .buyerId(buyerId)
                    .sellerId(order.getSellerId())
                    .productId(order.getProductId())
                    .rating(reviewRequest.getRating())
                    .reviewContent(reviewRequest.getReviewContent())
                    .reviewTags(convertTagsToJson(reviewRequest.getReviewTags()))
                    .isAnonymous(reviewRequest.getIsAnonymous())
                    .creditScoreChange(scoreChange)
                    .sellerCreditBefore(oldScore)
                    .sellerCreditAfter(newScore)
                    .createdTime(LocalDateTime.now())
                    .build();

            sellerReviewMapper.insert(review);

            // 8. 更新卖家信用分
            creditScoreService.updateCreditScore(
                    order.getSellerId(),
                    scoreChange,
                    CreditScoreLog.ChangeType.REVIEW,
                    "买家评价：" + reviewRequest.getRating() + "星",
                    reviewRequest.getOrderId(),
                    review.getReviewId(),
                    buyerId
            );

            // 9. 构建响应
            return buildReviewResponse(review, order);

        } catch (Exception e) {
            logger.error("提交评价失败，买家ID: {}, 订单ID: {}", buyerId, reviewRequest.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 检查订单是否可以评价
     */
    @Override
    public boolean canReviewOrder(Long orderId, Long buyerId) {
        try {
            Order order = orderMapper.selectById(orderId);
            if (order == null) {
                return false;
            }

            // 检查是否为买家
            if (!order.getBuyerId().equals(buyerId)) {
                return false;
            }

            // 检查订单状态（只有已送达的订单才能评价）
            return Order.OrderStatus.COMPLETED.equals(order.getStatus());

        } catch (Exception e) {
            logger.error("检查评价权限失败，订单ID: {}, 买家ID: {}", orderId, buyerId, e);
            return false;
        }
    }

    /**
     * 检查订单是否已经评价过
     */
    @Override
    public boolean hasReviewed(Long orderId) {
        try {
            SellerReview review = sellerReviewMapper.selectByOrderId(orderId);
            return review != null;
        } catch (Exception e) {
            logger.error("检查评价状态失败，订单ID: {}", orderId, e);
            return false;
        }
    }

    /**
     * 获取订单的评价信息
     */
    @Override
    public ReviewResponse getOrderReview(Long orderId) {
        try {
            SellerReview review = sellerReviewMapper.selectByOrderId(orderId);
            if (review == null) {
                return null;
            }

            Order order = orderMapper.selectById(orderId);
            return buildReviewResponse(review, order);

        } catch (Exception e) {
            logger.error("获取订单评价失败，订单ID: {}", orderId, e);
            return null;
        }
    }

    /**
     * 获取卖家的信用信息
     */
    @Override
    public SellerCreditInfo getSellerCreditInfo(Long sellerId) {
        try {
            // 获取卖家基本信息
            User seller = userMapper.selectById(sellerId);
            if (seller == null) {
                return null;
            }

            // 获取评价统计
            Map<String, Object> statistics = sellerReviewMapper.getSellerReviewStatistics(sellerId);
            BigDecimal positiveRate = sellerReviewMapper.getSellerPositiveRate(sellerId);
            List<Map<String, Object>> ratingDistribution = sellerReviewMapper.getSellerRatingDistribution(sellerId);

            // 获取最近评价
            List<Map<String, Object>> recentReviewsData = sellerReviewMapper.getSellerRecentReviews(sellerId, 5);
            List<ReviewResponse> recentReviews = convertToReviewResponses(recentReviewsData);

            // 构建信用信息
            return SellerCreditInfo.builder()
                    .sellerId(sellerId)
                    .sellerName(seller.getUsername())
                    .creditScore(seller.getCreditScore())
                    .creditLevel(SellerCreditInfo.getCreditLevel(seller.getCreditScore()))
                    .creditLevelColor(SellerCreditInfo.getCreditLevelColor(seller.getCreditScore()))
                    .totalReviews(getIntValue(statistics, "totalReviews"))
                    .averageRating(getBigDecimalValue(statistics, "averageRating"))
                    .positiveRate(positiveRate)
                    .positiveReviews(getIntValue(statistics, "positiveReviews"))
                    .negativeReviews(getIntValue(statistics, "negativeReviews"))
                    .ratingDistribution(convertRatingDistribution(ratingDistribution))
                    .recentReviews(recentReviews)
                    .build();

        } catch (Exception e) {
            logger.error("获取卖家信用信息失败，卖家ID: {}", sellerId, e);
            return null;
        }
    }

    /**
     * 计算信用分变化
     */
    private int calculateScoreChange(int rating, Long buyerId, Long sellerId) {
        // 基础分数变化
        int baseChange;
        switch (rating) {
            case 1:
                baseChange = -5;  // 严重差评
                break;
            case 2:
                baseChange = -2;  // 一般差评
                break;
            case 3:
                baseChange = 0;   // 中性评价
                break;
            case 4:
                baseChange = 1;   // 好评
                break;
            case 5:
                baseChange = 2;   // 优秀好评
                break;
            default:
                baseChange = 0;
        }

        // TODO: 可以根据买家信用分、卖家历史评价等因素调整权重
        // 这里先使用基础分数
        return baseChange;
    }

    /**
     * 验证评价频率（防刷分）
     */
    private boolean validateReviewFrequency(Long buyerId, Long sellerId) {
        try {
            // 检查30天内对同一卖家的评价次数
            int recentReviews = sellerReviewMapper.countReviewsByBuyerInDays(buyerId, sellerId, 30);
            return recentReviews < 3; // 30天内最多3次评价
        } catch (Exception e) {
            logger.error("验证评价频率失败", e);
            return true; // 出错时允许评价
        }
    }

    /**
     * 转换标签为JSON
     */
    private String convertTagsToJson(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(tags);
        } catch (Exception e) {
            logger.error("转换标签为JSON失败", e);
            return null;
        }
    }

    /**
     * 构建评价响应
     */
    private ReviewResponse buildReviewResponse(SellerReview review, Order order) {
        try {
            User buyer = userMapper.selectById(review.getBuyerId());
            Product product = productMapper.selectById(review.getProductId());

            return ReviewResponse.builder()
                    .reviewId(review.getReviewId())
                    .orderId(review.getOrderId())
                    .buyerId(review.getBuyerId())
                    .buyerName(buyer != null ? buyer.getUsername() : "未知用户")
                    .sellerId(review.getSellerId())
                    .productId(review.getProductId())
                    .productTitle(product != null ? product.getTitle() : "未知商品")
                    .productImage(product != null ? product.getImageUrls(): null)
                    .rating(review.getRating())
                    .reviewContent(review.getReviewContent())
                    .reviewTags(parseTagsFromJson(review.getReviewTags()))
                    .isAnonymous(review.getIsAnonymous())
                    .creditScoreChange(review.getCreditScoreChange())
                    .sellerCreditBefore(review.getSellerCreditBefore())
                    .sellerCreditAfter(review.getSellerCreditAfter())
                    .createdTime(review.getCreatedTime())
                    .build();
        } catch (Exception e) {
            logger.error("构建评价响应失败", e);
            return null;
        }
    }

    /**
     * 从JSON解析标签
     */
    private List<String> parseTagsFromJson(String tagsJson) {
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            @SuppressWarnings("unchecked")
            List<String> tags = objectMapper.readValue(tagsJson, List.class);
            return tags != null ? tags : new ArrayList<>();
        } catch (Exception e) {
            logger.error("解析标签JSON失败", e);
            return new ArrayList<>();
        }
    }

    // 其他辅助方法...
    private Integer getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return BigDecimal.ZERO;
    }

    private Map<Integer, Integer> convertRatingDistribution(List<Map<String, Object>> distribution) {
        Map<Integer, Integer> result = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            result.put(i, 0);
        }

        for (Map<String, Object> item : distribution) {
            Integer rating = getIntValue(item, "rating");
            Integer count = getIntValue(item, "count");
            result.put(rating, count);
        }

        return result;
    }

    private List<ReviewResponse> convertToReviewResponses(List<Map<String, Object>> reviewsData) {
        // TODO: 实现转换逻辑
        return new ArrayList<>();
    }

    // 其他方法的实现将在下一步添加...
    @Override
    public List<ReviewResponse> getSellerReviews(Long sellerId, int page, int pageSize) {
        try {
            // 先简单实现：获取所有该卖家的评价，然后手动分页
            // 后续可以优化为数据库层面的分页
            List<SellerReview> allReviews = sellerReviewMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SellerReview>()
                    .eq("lzhshtp_seller_id", sellerId)
                    .orderByDesc("lzhshtp_created_time")
            );

            // 手动分页
            int start = (page - 1) * pageSize;
            int end = Math.min(start + pageSize, allReviews.size());
            List<SellerReview> pagedReviews = allReviews.subList(start, end);

            List<ReviewResponse> reviewResponses = new ArrayList<>();

            for (SellerReview review : pagedReviews) {
                // 获取订单信息
                Order order = orderMapper.selectById(review.getOrderId());
                ReviewResponse response = buildReviewResponse(review, order);
                if (response != null) {
                    reviewResponses.add(response);
                }
            }

            return reviewResponses;

        } catch (Exception e) {
            logger.error("获取卖家评价列表失败，卖家ID: {}", sellerId, e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<ReviewResponse> getBuyerReviews(Long buyerId, int page, int pageSize) {
        // TODO: 实现
        return new ArrayList<>();
    }

    @Override
    public ReviewStatistics getReviewStatistics(int days) {
        // TODO: 实现
        return null;
    }

    @Override
    public boolean detectAbnormalReviews(Long sellerId, int days) {
        // TODO: 实现
        return false;
    }

    @Override
    public boolean appealReview(Long reviewId, String reason, String evidence) {
        // TODO: 实现
        return false;
    }

    @Override
    public boolean handleReviewAppeal(Long reviewId, boolean approved, String adminNote) {
        // TODO: 实现
        return false;
    }
}
