package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 反馈状态更新请求对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackStatusUpdateRequest {
    
    /**
     * 新状态: pending(待处理), in_progress(处理中), resolved(已解决), rejected(已拒绝)
     */
    @NotBlank(message = "状态不能为空")
    private String status;
    
    /**
     * 管理员处理备注
     */
    @Size(max = 500, message = "备注长度不能超过500字符")
    private String adminNotes;
} 