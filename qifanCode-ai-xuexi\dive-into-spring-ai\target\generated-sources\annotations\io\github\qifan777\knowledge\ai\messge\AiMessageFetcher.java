package io.github.qifan777.knowledge.ai.messge;

import io.github.qifan777.knowledge.ai.session.AiSession;
import io.github.qifan777.knowledge.ai.session.AiSessionTable;
import io.github.qifan777.knowledge.user.User;
import io.github.qifan777.knowledge.user.UserTable;
import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = AiMessage.class
)
public class AiMessageFetcher extends AbstractTypedFetcher<AiMessage, AiMessageFetcher> {
    public static final AiMessageFetcher $ = new AiMessageFetcher(null);

    private AiMessageFetcher(FetcherImpl<AiMessage> base) {
        super(AiMessage.class, base);
    }

    private AiMessageFetcher(AiMessageFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private AiMessageFetcher(AiMessageFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static AiMessageFetcher $from(Fetcher<AiMessage> base) {
        return base instanceof AiMessageFetcher ? 
        	(AiMessageFetcher)base : 
        	new AiMessageFetcher((FetcherImpl<AiMessage>)base);
    }

    @NewChain
    public AiMessageFetcher createdTime() {
        return add("createdTime");
    }

    @NewChain
    public AiMessageFetcher createdTime(boolean enabled) {
        return enabled ? add("createdTime") : remove("createdTime");
    }

    @NewChain
    public AiMessageFetcher editedTime() {
        return add("editedTime");
    }

    @NewChain
    public AiMessageFetcher editedTime(boolean enabled) {
        return enabled ? add("editedTime") : remove("editedTime");
    }

    @NewChain
    public AiMessageFetcher editor() {
        return add("editor");
    }

    @NewChain
    public AiMessageFetcher editor(boolean enabled) {
        return enabled ? add("editor") : remove("editor");
    }

    @NewChain
    public AiMessageFetcher editor(Fetcher<User> childFetcher) {
        return add("editor", childFetcher);
    }

    @NewChain
    public AiMessageFetcher editor(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("editor", childFetcher, fieldConfig);
    }

    @NewChain
    public AiMessageFetcher editor(IdOnlyFetchType idOnlyFetchType) {
        return add("editor", idOnlyFetchType);
    }

    @NewChain
    public AiMessageFetcher creator() {
        return add("creator");
    }

    @NewChain
    public AiMessageFetcher creator(boolean enabled) {
        return enabled ? add("creator") : remove("creator");
    }

    @NewChain
    public AiMessageFetcher creator(Fetcher<User> childFetcher) {
        return add("creator", childFetcher);
    }

    @NewChain
    public AiMessageFetcher creator(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("creator", childFetcher, fieldConfig);
    }

    @NewChain
    public AiMessageFetcher creator(IdOnlyFetchType idOnlyFetchType) {
        return add("creator", idOnlyFetchType);
    }

    @NewChain
    public AiMessageFetcher type() {
        return add("type");
    }

    @NewChain
    public AiMessageFetcher type(boolean enabled) {
        return enabled ? add("type") : remove("type");
    }

    @NewChain
    public AiMessageFetcher textContent() {
        return add("textContent");
    }

    @NewChain
    public AiMessageFetcher textContent(boolean enabled) {
        return enabled ? add("textContent") : remove("textContent");
    }

    @NewChain
    public AiMessageFetcher medias() {
        return add("medias");
    }

    @NewChain
    public AiMessageFetcher medias(boolean enabled) {
        return enabled ? add("medias") : remove("medias");
    }

    @NewChain
    public AiMessageFetcher sessionId() {
        return add("sessionId");
    }

    @NewChain
    public AiMessageFetcher sessionId(boolean enabled) {
        return enabled ? add("sessionId") : remove("sessionId");
    }

    @NewChain
    public AiMessageFetcher sessionId(IdOnlyFetchType idOnlyFetchType) {
        return add("sessionId", idOnlyFetchType);
    }

    @NewChain
    public AiMessageFetcher session() {
        return add("session");
    }

    @NewChain
    public AiMessageFetcher session(boolean enabled) {
        return enabled ? add("session") : remove("session");
    }

    @NewChain
    public AiMessageFetcher session(Fetcher<AiSession> childFetcher) {
        return add("session", childFetcher);
    }

    @NewChain
    public AiMessageFetcher session(Fetcher<AiSession> childFetcher,
            Consumer<FieldConfig<AiSession, AiSessionTable>> fieldConfig) {
        return add("session", childFetcher, fieldConfig);
    }

    @NewChain
    public AiMessageFetcher session(IdOnlyFetchType idOnlyFetchType) {
        return add("session", idOnlyFetchType);
    }

    @Override
    protected AiMessageFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new AiMessageFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected AiMessageFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new AiMessageFetcher(this, prop, fieldConfig);
    }
}
