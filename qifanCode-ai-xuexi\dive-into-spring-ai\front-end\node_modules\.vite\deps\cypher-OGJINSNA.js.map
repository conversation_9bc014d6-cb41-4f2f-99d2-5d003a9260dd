{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/cypher.js"], "sourcesContent": ["var wordRegexp = function(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n};\n\nvar tokenBase = function(stream/*, state*/) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch ==='\"') {\n    stream.match(/^.*?\"/);\n    return \"string\";\n  }\n  if (ch === \"'\") {\n    stream.match(/^.*?'/);\n    return \"string\";\n  }\n  if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  } else if (ch === \"/\" && stream.eat(\"/\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if (stream.eat(\":\")) {\n      stream.eatWhile(/[\\w\\d_\\-]/);\n      return \"atom\";\n    }\n    var word = stream.current();\n    if (funcs.test(word)) return \"builtin\";\n    if (preds.test(word)) return \"def\";\n    if (keywords.test(word) || systemKeywords.test(word)) return \"keyword\";\n    return \"variable\";\n  }\n};\nvar pushContext = function(state, type, col) {\n  return state.context = {\n    prev: state.context,\n    indent: state.indent,\n    col: col,\n    type: type\n  };\n};\nvar popContext = function(state) {\n  state.indent = state.context.indent;\n  return state.context = state.context.prev;\n};\nvar curPunc;\nvar funcs = wordRegexp([\"abs\", \"acos\", \"allShortestPaths\", \"asin\", \"atan\", \"atan2\", \"avg\", \"ceil\", \"coalesce\", \"collect\", \"cos\", \"cot\", \"count\", \"degrees\", \"e\", \"endnode\", \"exp\", \"extract\", \"filter\", \"floor\", \"haversin\", \"head\", \"id\", \"keys\", \"labels\", \"last\", \"left\", \"length\", \"log\", \"log10\", \"lower\", \"ltrim\", \"max\", \"min\", \"node\", \"nodes\", \"percentileCont\", \"percentileDisc\", \"pi\", \"radians\", \"rand\", \"range\", \"reduce\", \"rel\", \"relationship\", \"relationships\", \"replace\", \"reverse\", \"right\", \"round\", \"rtrim\", \"shortestPath\", \"sign\", \"sin\", \"size\", \"split\", \"sqrt\", \"startnode\", \"stdev\", \"stdevp\", \"str\", \"substring\", \"sum\", \"tail\", \"tan\", \"timestamp\", \"toFloat\", \"toInt\", \"toString\", \"trim\", \"type\", \"upper\"]);\nvar preds = wordRegexp([\"all\", \"and\", \"any\", \"contains\", \"exists\", \"has\", \"in\", \"none\", \"not\", \"or\", \"single\", \"xor\"]);\nvar keywords = wordRegexp([\"as\", \"asc\", \"ascending\", \"assert\", \"by\", \"case\", \"commit\", \"constraint\", \"create\", \"csv\", \"cypher\", \"delete\", \"desc\", \"descending\", \"detach\", \"distinct\", \"drop\", \"else\", \"end\", \"ends\", \"explain\", \"false\", \"fieldterminator\", \"foreach\", \"from\", \"headers\", \"in\", \"index\", \"is\", \"join\", \"limit\", \"load\", \"match\", \"merge\", \"null\", \"on\", \"optional\", \"order\", \"periodic\", \"profile\", \"remove\", \"return\", \"scan\", \"set\", \"skip\", \"start\", \"starts\", \"then\", \"true\", \"union\", \"unique\", \"unwind\", \"using\", \"when\", \"where\", \"with\", \"call\", \"yield\"]);\nvar systemKeywords = wordRegexp([\"access\", \"active\", \"assign\", \"all\", \"alter\", \"as\", \"catalog\", \"change\", \"copy\", \"create\", \"constraint\", \"constraints\", \"current\", \"database\", \"databases\", \"dbms\", \"default\", \"deny\", \"drop\", \"element\", \"elements\", \"exists\", \"from\", \"grant\", \"graph\", \"graphs\", \"if\", \"index\", \"indexes\", \"label\", \"labels\", \"management\", \"match\", \"name\", \"names\", \"new\", \"node\", \"nodes\", \"not\", \"of\", \"on\", \"or\", \"password\", \"populated\", \"privileges\", \"property\", \"read\", \"relationship\", \"relationships\", \"remove\", \"replace\", \"required\", \"revoke\", \"role\", \"roles\", \"set\", \"show\", \"start\", \"status\", \"stop\", \"suspended\", \"to\", \"traverse\", \"type\", \"types\", \"user\", \"users\", \"with\", \"write\"]);\nvar operatorChars = /[*+\\-<>=&|~%^]/;\n\nexport const cypher = {\n  name: \"cypher\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      context: null,\n      indent: 0,\n      col: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && (state.context.align == null)) {\n        state.context.align = false;\n      }\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) {\n      return null;\n    }\n    var style = state.tokenize(stream, state);\n    if (style !== \"comment\" && state.context && (state.context.align == null) && state.context.type !== \"pattern\") {\n      state.context.align = true;\n    }\n    if (curPunc === \"(\") {\n      pushContext(state, \")\", stream.column());\n    } else if (curPunc === \"[\") {\n      pushContext(state, \"]\", stream.column());\n    } else if (curPunc === \"{\") {\n      pushContext(state, \"}\", stream.column());\n    } else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type === \"pattern\") {\n        popContext(state);\n      }\n      if (state.context && curPunc === state.context.type) {\n        popContext(state);\n      }\n    } else if (curPunc === \".\" && state.context && state.context.type === \"pattern\") {\n      popContext(state);\n    } else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type)) {\n        pushContext(state, \"pattern\", stream.column());\n      } else if (state.context.type === \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n    return style;\n  },\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar)) {\n      while (context && context.type === \"pattern\") {\n        context = context.prev;\n      }\n    }\n    var closing = context && firstChar === context.type;\n    if (!context) return 0;\n    if (context.type === \"keywords\") return null\n    if (context.align) return context.col + (closing ? 0 : 1);\n    return context.indent + (closing ? 0 : cx.unit);\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,aAAa,SAAS,OAAO;AAC/B,SAAO,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;AACxD;AAEA,IAAI,YAAY,SAAS,QAAmB;AAC1C,YAAU;AACV,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,OAAM,KAAK;AACb,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAK;AACd,WAAO,MAAM,OAAO;AACpB,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,KAAK,EAAE,GAAG;AAC/B,cAAU;AACV,WAAO;AAAA,EACT,WAAW,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACxC,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAAW,cAAc,KAAK,EAAE,GAAG;AACjC,WAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,SAAS;AACzB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,SAAS,WAAW;AAC3B,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,QAAQ;AAC1B,QAAI,MAAM,KAAK,IAAI;AAAG,aAAO;AAC7B,QAAI,MAAM,KAAK,IAAI;AAAG,aAAO;AAC7B,QAAI,SAAS,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI;AAAG,aAAO;AAC7D,WAAO;AAAA,EACT;AACF;AACA,IAAI,cAAc,SAAS,OAAO,MAAM,KAAK;AAC3C,SAAO,MAAM,UAAU;AAAA,IACrB,MAAM,MAAM;AAAA,IACZ,QAAQ,MAAM;AAAA,IACd;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,aAAa,SAAS,OAAO;AAC/B,QAAM,SAAS,MAAM,QAAQ;AAC7B,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AACA,IAAI;AACJ,IAAI,QAAQ,WAAW,CAAC,OAAO,QAAQ,oBAAoB,QAAQ,QAAQ,SAAS,OAAO,QAAQ,YAAY,WAAW,OAAO,OAAO,SAAS,WAAW,KAAK,WAAW,OAAO,WAAW,UAAU,SAAS,YAAY,QAAQ,MAAM,QAAQ,UAAU,QAAQ,QAAQ,UAAU,OAAO,SAAS,SAAS,SAAS,OAAO,OAAO,QAAQ,SAAS,kBAAkB,kBAAkB,MAAM,WAAW,QAAQ,SAAS,UAAU,OAAO,gBAAgB,iBAAiB,WAAW,WAAW,SAAS,SAAS,SAAS,gBAAgB,QAAQ,OAAO,QAAQ,SAAS,QAAQ,aAAa,SAAS,UAAU,OAAO,aAAa,OAAO,QAAQ,OAAO,aAAa,WAAW,SAAS,YAAY,QAAQ,QAAQ,OAAO,CAAC;AACxsB,IAAI,QAAQ,WAAW,CAAC,OAAO,OAAO,OAAO,YAAY,UAAU,OAAO,MAAM,QAAQ,OAAO,MAAM,UAAU,KAAK,CAAC;AACrH,IAAI,WAAW,WAAW,CAAC,MAAM,OAAO,aAAa,UAAU,MAAM,QAAQ,UAAU,cAAc,UAAU,OAAO,UAAU,UAAU,QAAQ,cAAc,UAAU,YAAY,QAAQ,QAAQ,OAAO,QAAQ,WAAW,SAAS,mBAAmB,WAAW,QAAQ,WAAW,MAAM,SAAS,MAAM,QAAQ,SAAS,QAAQ,SAAS,SAAS,QAAQ,MAAM,YAAY,SAAS,YAAY,WAAW,UAAU,UAAU,QAAQ,OAAO,QAAQ,SAAS,UAAU,QAAQ,QAAQ,SAAS,UAAU,UAAU,SAAS,QAAQ,SAAS,QAAQ,QAAQ,OAAO,CAAC;AACjjB,IAAI,iBAAiB,WAAW,CAAC,UAAU,UAAU,UAAU,OAAO,SAAS,MAAM,WAAW,UAAU,QAAQ,UAAU,cAAc,eAAe,WAAW,YAAY,aAAa,QAAQ,WAAW,QAAQ,QAAQ,WAAW,YAAY,UAAU,QAAQ,SAAS,SAAS,UAAU,MAAM,SAAS,WAAW,SAAS,UAAU,cAAc,SAAS,QAAQ,SAAS,OAAO,QAAQ,SAAS,OAAO,MAAM,MAAM,MAAM,YAAY,aAAa,cAAc,YAAY,QAAQ,gBAAgB,iBAAiB,UAAU,WAAW,YAAY,UAAU,QAAQ,SAAS,OAAO,QAAQ,SAAS,UAAU,QAAQ,aAAa,MAAM,YAAY,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO,CAAC;AAC9rB,IAAI,gBAAgB;AAEb,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,MAAM,WAAY,MAAM,QAAQ,SAAS,MAAO;AAClD,cAAM,QAAQ,QAAQ;AAAA,MACxB;AACA,YAAM,SAAS,OAAO,YAAY;AAAA,IACpC;AACA,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,aAAa,MAAM,WAAY,MAAM,QAAQ,SAAS,QAAS,MAAM,QAAQ,SAAS,WAAW;AAC7G,YAAM,QAAQ,QAAQ;AAAA,IACxB;AACA,QAAI,YAAY,KAAK;AACnB,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,IACzC,WAAW,YAAY,KAAK;AAC1B,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,IACzC,WAAW,YAAY,KAAK;AAC1B,kBAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,IACzC,WAAW,WAAW,KAAK,OAAO,GAAG;AACnC,aAAO,MAAM,WAAW,MAAM,QAAQ,SAAS,WAAW;AACxD,mBAAW,KAAK;AAAA,MAClB;AACA,UAAI,MAAM,WAAW,YAAY,MAAM,QAAQ,MAAM;AACnD,mBAAW,KAAK;AAAA,MAClB;AAAA,IACF,WAAW,YAAY,OAAO,MAAM,WAAW,MAAM,QAAQ,SAAS,WAAW;AAC/E,iBAAW,KAAK;AAAA,IAClB,WAAW,uBAAuB,KAAK,KAAK,KAAK,MAAM,SAAS;AAC9D,UAAI,SAAS,KAAK,MAAM,QAAQ,IAAI,GAAG;AACrC,oBAAY,OAAO,WAAW,OAAO,OAAO,CAAC;AAAA,MAC/C,WAAW,MAAM,QAAQ,SAAS,aAAa,CAAC,MAAM,QAAQ,OAAO;AACnE,cAAM,QAAQ,QAAQ;AACtB,cAAM,QAAQ,MAAM,OAAO,OAAO;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC;AAC/C,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,aAAO,WAAW,QAAQ,SAAS,WAAW;AAC5C,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AACA,QAAI,UAAU,WAAW,cAAc,QAAQ;AAC/C,QAAI,CAAC;AAAS,aAAO;AACrB,QAAI,QAAQ,SAAS;AAAY,aAAO;AACxC,QAAI,QAAQ;AAAO,aAAO,QAAQ,OAAO,UAAU,IAAI;AACvD,WAAO,QAAQ,UAAU,UAAU,IAAI,GAAG;AAAA,EAC5C;AACF;", "names": []}