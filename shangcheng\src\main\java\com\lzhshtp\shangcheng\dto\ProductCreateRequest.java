package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品创建请求DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductCreateRequest {
    
    @NotBlank(message = "商品标题不能为空")
    @Size(max = 255, message = "商品标题不能超过255个字符")
    private String title;
    
    @NotBlank(message = "商品描述不能为空")
    private String description;
    
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0")
    private Double price;
    
    @NotNull(message = "商品分类不能为空")
    private Integer categoryId;
    
    @NotBlank(message = "商品新旧程度不能为空")
    private String condition;
    
    @Size(max = 100, message = "地区信息不能超过100个字符")
    private String location;
    
    @NotBlank(message = "配送方式不能为空")
    private String deliveryMethod;
    
    // 商品图片，使用MultipartFile接收上传的文件
    private MultipartFile image;

    // 验货相关字段
    private Boolean supportOfficialVerification; // 是否提供官方验货服务

    @DecimalMin(value = "0.00", message = "验货费用不能为负数")
    private Double verificationFee; // 验货费用
} 