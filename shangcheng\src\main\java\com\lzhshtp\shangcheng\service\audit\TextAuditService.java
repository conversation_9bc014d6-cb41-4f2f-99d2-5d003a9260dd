package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.constants.AuditConstants;
import com.lzhshtp.shangcheng.dto.audit.AuditResultDTO;
import com.lzhshtp.shangcheng.mapper.SensitiveWordMapper;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.SensitiveWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文字审核服务
 */
@Slf4j
@Service
public class TextAuditService {

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    /**
     * 执行文字审核
     */
    public AuditResultDTO auditText(Product product) {
        log.info("开始文字审核，商品ID: {}", product.getId());

        String content = buildContentForAudit(product);
        if (!StringUtils.hasText(content)) {
            return AuditResultDTO.pass("text_audit");
        }

        // 获取所有敏感词
        List<SensitiveWord> allSensitiveWords = sensitiveWordMapper.findAllActive();

        // 检查违禁词
        AuditResultDTO bannedResult = checkBannedWords(content, allSensitiveWords);
        if (!bannedResult.getPassed()) {
            return bannedResult;
        }

        // 检查敏感词
        AuditResultDTO sensitiveResult = checkSensitiveWords(content, allSensitiveWords);
        if (!sensitiveResult.getPassed()) {
            return sensitiveResult;
        }

        // 检查品牌词
        AuditResultDTO brandResult = checkBrandWords(content, allSensitiveWords);
        if (!brandResult.getPassed()) {
            return brandResult;
        }

        log.info("文字审核通过，商品ID: {}", product.getId());
        return AuditResultDTO.pass("text_audit");
    }

    /**
     * 构建用于审核的文本内容
     */
    private String buildContentForAudit(Product product) {
        StringBuilder content = new StringBuilder();

        if (StringUtils.hasText(product.getTitle())) {
            content.append(product.getTitle()).append(" ");
        }

        if (StringUtils.hasText(product.getDescription())) {
            content.append(product.getDescription()).append(" ");
        }

        return content.toString().toLowerCase().trim();
    }

    /**
     * 检查违禁词
     */
    private AuditResultDTO checkBannedWords(String content, List<SensitiveWord> allWords) {
        List<String> violatedWords = new ArrayList<>();

        for (SensitiveWord word : allWords) {
            if ("banned".equals(word.getWordType()) && content.contains(word.getWord().toLowerCase())) {
                violatedWords.add(word.getWord());
            }
        }

        if (!violatedWords.isEmpty()) {
            Map<String, Object> details = new HashMap<>();
            details.put("violated_words", violatedWords);

            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.HIGH)
                .score(100)
                .reason("包含违禁词汇：" + String.join("、", violatedWords))
                .details(details)
                .build();
        }

        return AuditResultDTO.pass("banned_words");
    }

    /**
     * 检查敏感词
     */
    private AuditResultDTO checkSensitiveWords(String content, List<SensitiveWord> allWords) {
        List<String> sensitiveWords = new ArrayList<>();
        int maxSeverity = 0;

        for (SensitiveWord word : allWords) {
            if ("sensitive".equals(word.getWordType()) && content.contains(word.getWord().toLowerCase())) {
                sensitiveWords.add(word.getWord());
                maxSeverity = Math.max(maxSeverity, word.getSeverityLevel());
            }
        }

        if (!sensitiveWords.isEmpty()) {
            Map<String, Object> details = new HashMap<>();
            details.put("sensitive_words", sensitiveWords);
            details.put("max_severity", maxSeverity);

            // 根据严重程度决定风险等级
            AuditConstants.RiskLevel riskLevel;
            int score;

            if (maxSeverity >= 8) {
                riskLevel = AuditConstants.RiskLevel.HIGH;
                score = 80;
            } else if (maxSeverity >= 5) {
                riskLevel = AuditConstants.RiskLevel.MEDIUM;
                score = 50;
            } else {
                riskLevel = AuditConstants.RiskLevel.LOW;
                score = 20;
            }

            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(riskLevel)
                .score(score)
                .reason("包含敏感词汇：" + String.join("、", sensitiveWords))
                .details(details)
                .build();
        }

        return AuditResultDTO.pass("sensitive_words");
    }

    /**
     * 检查品牌词
     */
    private AuditResultDTO checkBrandWords(String content, List<SensitiveWord> allWords) {
        List<String> brandWords = new ArrayList<>();

        for (SensitiveWord word : allWords) {
            if ("brand".equals(word.getWordType()) && content.contains(word.getWord().toLowerCase())) {
                brandWords.add(word.getWord());
            }
        }

        if (!brandWords.isEmpty()) {
            Map<String, Object> details = new HashMap<>();
            details.put("brand_words", brandWords);

            return AuditResultDTO.builder()
                .passed(false)
                .riskLevel(AuditConstants.RiskLevel.MEDIUM)
                .score(40)
                .reason("包含品牌词汇，需要验证正品：" + String.join("、", brandWords))
                .details(details)
                .build();
        }

        return AuditResultDTO.pass("brand_words");
    }
}
