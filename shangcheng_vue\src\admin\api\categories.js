import request from '@/utils/request'

const API_URL = '/categories'

// 获取所有分类（平铺结构）
export const getAllCategories = async () => {
  return request({
    url: API_URL,
    method: 'get'
  })
}

// 获取所有分类（树形结构）
export const getAllCategoriesTree = async () => {
  return request({
    url: `${API_URL}/tree`,
    method: 'get'
  })
}

// 导出别名以匹配CategoryList.vue中的使用
export const getCategoriesTree = getAllCategoriesTree;

// 根据ID获取分类
export const getCategoryById = async (id) => {
  return request({
    url: `${API_URL}/${id}`,
    method: 'get'
  })
}

// 创建分类
export const createCategory = async (categoryData) => {
  return request({
    url: API_URL,
    method: 'post',
    data: categoryData
  })
}

// 更新分类
export const updateCategory = async (id, categoryData) => {
  return request({
    url: `${API_URL}/${id}`,
    method: 'put',
    data: categoryData
  })
}

// 删除分类
export const deleteCategory = async (id) => {
  return request({
    url: `${API_URL}/${id}`,
    method: 'delete'
  })
} 