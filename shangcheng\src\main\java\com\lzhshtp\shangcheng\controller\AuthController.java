package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.service.UserService;
import com.lzhshtp.shangcheng.utils.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;
    private final JwtUtils jwtUtils;

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 认证响应
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<AuthResponse>> register(@Valid @RequestBody RegisterRequest request) {
        try {
            AuthResponse response = userService.register(request);
            return ResponseEntity.ok(ApiResponse.success("注册成功", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("注册失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 管理员注册
     *
     * @param request 管理员注册请求
     * @return 认证响应
     */
    @PostMapping("/admin/register")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AuthResponse>> adminRegister(@Valid @RequestBody AdminRegisterRequest request) {
        try {
            AuthResponse response = userService.adminRegister(request);
            return ResponseEntity.ok(ApiResponse.success("管理员注册成功", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("管理员注册失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 认证响应
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<AuthResponse>> login(@Valid @RequestBody LoginRequest request) {
        try {
            AuthResponse response = userService.login(request);
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));
        } catch (IllegalArgumentException e) {
            // 检查错误消息是否包含"禁用"关键字
            if (e.getMessage().contains("禁用")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.fail(e.getMessage(), 403));
            }
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.fail("登录失败: 用户名或密码错误", 401));
        }
    }

    /**
     * 管理员登录
     *
     * @param request 管理员登录请求
     * @return 认证响应
     */
    @PostMapping("/admin/login")
    public ResponseEntity<ApiResponse<AuthResponse>> adminLogin(@Valid @RequestBody AdminLoginRequest request) {
        try {
            AuthResponse response = userService.adminLogin(request);
            return ResponseEntity.ok(ApiResponse.success("管理员登录成功", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.fail("管理员登录失败: 用户名或密码错误", 401));
        }
    }

    /**
     * 绑定手机号
     *
     * @param request 绑定手机号请求
     * @param httpRequest HTTP请求对象
     * @return 绑定结果
     */
    @PostMapping("/bind-phone")
    public ResponseEntity<ApiResponse<String>> bindPhone(
            @Valid @RequestBody BindPhoneRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从JWT Token中获取用户ID
            String token = httpRequest.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                Long userId = jwtUtils.getUserIdFromToken(token);

                boolean success = userService.bindPhone(userId, request);
                if (success) {
                    return ResponseEntity.ok(ApiResponse.success("手机号绑定成功"));
                } else {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.fail("手机号绑定失败，请检查验证码或手机号是否已被绑定", 400));
                }
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.fail("未授权访问", 401));
            }
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("绑定失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 短信验证码登录
     *
     * @param request 短信登录请求
     * @return 认证响应
     */
    @PostMapping("/sms-login")
    public ResponseEntity<ApiResponse<AuthResponse>> smsLogin(@Valid @RequestBody SmsLoginRequest request) {
        try {
            AuthResponse response = userService.smsLogin(request);
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.fail(e.getMessage(), 400));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.fail("登录失败: " + e.getMessage(), 500));
        }
    }
}
