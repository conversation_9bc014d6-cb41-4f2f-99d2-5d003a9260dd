package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.*;
import com.lzhshtp.shangcheng.model.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 认证响应
     */
    AuthResponse register(RegisterRequest request);
    
    /**
     * 管理员注册
     *
     * @param request 管理员注册请求
     * @return 认证响应
     */
    AuthResponse adminRegister(AdminRegisterRequest request);

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 认证响应
     */
    AuthResponse login(LoginRequest request);

    /**
     * 绑定手机号
     *
     * @param userId 用户ID
     * @param request 绑定手机号请求
     * @return 是否绑定成功
     */
    boolean bindPhone(Long userId, BindPhoneRequest request);

    /**
     * 短信验证码登录
     *
     * @param request 短信登录请求
     * @return 认证响应
     */
    AuthResponse smsLogin(SmsLoginRequest request);
    
    /**
     * 管理员登录
     *
     * @param request 管理员登录请求
     * @return 认证响应
     */
    AuthResponse adminLogin(AdminLoginRequest request);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    User findByEmail(String email);

    /**
     * 根据手机号查找用户
     *
     * @param phoneNumber 手机号
     * @return 用户对象
     */
    User findByPhoneNumber(String phoneNumber);

    /**
     * 根据ID查找用户
     *
     * @param userId 用户ID
     * @return 用户对象
     */
    User findById(Long userId);

    /**
     * 更新用户信息
     *
     * @param username 用户名
     * @param request 更新请求
     * @return 更新后的用户对象
     */
    User updateUserInfo(String username, UserUpdateRequest request);

    /**
     * 更新用户头像
     *
     * @param username 用户名
     * @param avatarUrl 头像URL
     * @return 更新后的用户对象
     */
    User updateAvatar(String username, String avatarUrl);
    
    /**
     * 获取用户列表（管理员专用）
     *
     * @param params 查询参数
     * @return 分页用户列表
     */
    PageResult<UserDTO> getUserList(UserQueryParams params);
    
    /**
     * 获取用户详情（管理员专用）
     *
     * @param userId 用户ID
     * @return 用户详情，包含统计数据
     */
    UserDetailDTO getUserDetail(Long userId);
    
    /**
     * 更新用户状态（管理员专用）
     *
     * @param request 用户状态更新请求
     * @return 更新后的用户
     */
    User updateUserStatus(UserStatusUpdateRequest request);
    
    /**
     * 批量更新用户状态（管理员专用）
     *
     * @param request 批量用户状态更新请求
     * @return 更新的用户数量
     */
    int batchUpdateUserStatus(BatchUserStatusUpdateRequest request);

    /**
     * 获取管理员用户列表（管理员专用）
     *
     * @param params 查询参数
     * @return 分页管理员用户列表
     */
    PageResult<UserDTO> getAdminList(UserQueryParams params);

    /**
     * 删除管理员（管理员专用）
     *
     * @param userId 要删除的管理员用户ID
     * @return 是否删除成功
     * @throws IllegalArgumentException 如果用户不存在或不是管理员
     */
    boolean deleteAdmin(Long userId);
    
    /**
     * 更新管理员信息（管理员专用）
     *
     * @param userId 要更新的管理员用户ID
     * @param request 管理员更新请求
     * @return 更新后的用户对象
     * @throws IllegalArgumentException 如果用户不存在或不是管理员
     */
    User updateAdmin(Long userId, AdminUpdateRequest request);

    /**
     * 检查用户是否可以发布商品
     *
     * @param username 用户名
     * @return 是否可以发布商品
     */
    boolean canPublishProduct(String username);
}
