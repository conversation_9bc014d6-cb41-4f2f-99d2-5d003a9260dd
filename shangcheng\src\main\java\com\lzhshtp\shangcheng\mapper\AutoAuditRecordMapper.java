package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 自动审核记录Mapper
 */
@Mapper
public interface AutoAuditRecordMapper extends BaseMapper<AutoAuditRecord> {
    
    /**
     * 根据商品ID查询自动审核记录
     */
    @Select("SELECT * FROM tb_lzhshtp_auto_audit_records WHERE lzhshtp_product_id = #{productId}")
    AutoAuditRecord findByProductId(@Param("productId") Long productId);

    /**
     * 根据条件查询审核记录（使用Map参数）
     * 实现在 AutoAuditRecordMapper.xml 中
     */
    List<AutoAuditRecord> findByConditions(Map<String, Object> params);

    /**
     * 根据条件统计审核记录数量
     * 实现在 AutoAuditRecordMapper.xml 中
     */
    Long countByConditions(Map<String, Object> params);

    /**
     * 根据决策类型统计数量
     * 实现在 AutoAuditRecordMapper.xml 中
     */
    Long countByDecision(@Param("decision") String decision, @Param("params") Map<String, Object> params);
}
