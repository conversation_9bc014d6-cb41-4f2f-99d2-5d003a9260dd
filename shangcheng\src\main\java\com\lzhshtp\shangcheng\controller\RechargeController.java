package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.RechargeRequest;
import com.lzhshtp.shangcheng.dto.RechargeResponse;
import com.lzhshtp.shangcheng.service.RechargeService;
import com.lzhshtp.shangcheng.utils.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 充值控制器
 */
@RestController
@RequestMapping("/api/recharge")
public class RechargeController {
    
    private static final Logger logger = LoggerFactory.getLogger(RechargeController.class);
    
    @Autowired
    private RechargeService rechargeService;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    /**
     * 发起充值
     */
    @PostMapping("/create")
    public ApiResponse<RechargeResponse> createRecharge(
            @Valid @RequestBody RechargeRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从JWT获取用户ID
            String token = httpRequest.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            Long userId = jwtUtils.getUserIdFromToken(token);
            
            RechargeResponse response = rechargeService.createRecharge(userId, request);
            return ApiResponse.success("充值订单创建成功", response);
        } catch (Exception e) {
            logger.error("创建充值订单失败", e);
            return ApiResponse.fail("创建充值订单失败：" + e.getMessage());
        }
    }
    
    /**
     * 支付宝支付回调
     */
    @PostMapping("/alipay/notify")
    public String alipayNotify(@RequestParam Map<String, String> params) {
        try {
            logger.info("收到支付宝回调：{}", params);
            boolean success = rechargeService.handleAlipayCallback(params);
            return success ? "success" : "fail";
        } catch (Exception e) {
            logger.error("处理支付宝回调失败", e);
            return "fail";
        }
    }
    
    /**
     * 支付宝支付返回页面
     */
    @GetMapping("/alipay/return")
    public void alipayReturn(HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("收到支付宝充值同步回调，开始处理...");

        try {
            // 将支付宝回调的参数转为Map
            Map<String, String> params = new HashMap<>();
            request.getParameterMap().forEach((key, values) -> {
                String value = values[0];
                params.put(key, value);
            });

            logger.info("支付宝充值支付返回参数：{}", params);

            // 【重要】在这里调用异步通知的处理逻辑，模拟充值成功
            // 在生产环境中，这部分逻辑应该只在异步通知中执行
            boolean success = rechargeService.handleAlipayCallback(params);
            if (success) {
                logger.info("同步回调触发充值状态更新成功。");
            } else {
                logger.warn("同步回调触发充值状态更新失败或已处理。");
            }
        } catch (Exception e) {
            logger.error("同步回调处理充值状态时发生异常", e);
            // 即使处理失败，也要确保用户能跳转到前端页面
        }

        // 从支付宝回调的请求中获取充值订单号
        String rechargeOrderNo = request.getParameter("out_trade_no");

        // 拼接前端充值成功页面的URL，并带上订单号作为查询参数
        String frontendSuccessUrl = "http://localhost:5000/profile?recharge=success&orderNo=" + rechargeOrderNo;

        // 重定向到前端的个人页面
        logger.info("充值处理完成，重定向到前端页面: {}", frontendSuccessUrl);
        response.sendRedirect(frontendSuccessUrl);
    }
}
