package com.lzhshtp.shangcheng.mapper;

import com.lzhshtp.shangcheng.model.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageMapper {

    int createMessage(Message message);

    List<Message> findMessagesByConversationId(@Param("conversationId") Long conversationId, 
                                              @Param("offset") int offset, 
                                              @Param("limit") int limit);

    int countMessagesByConversationId(@Param("conversationId") Long conversationId);

    Message findLatestMessageByConversationId(@Param("conversationId") Long conversationId);

    int markMessagesAsRead(@Param("conversationId") Long conversationId, @Param("currentUserId") Long currentUserId);

    int countUnreadMessages(@Param("conversationId") Long conversationId, @Param("userId") Long userId);

    int deleteMessage(@Param("messageId") Long messageId);
} 