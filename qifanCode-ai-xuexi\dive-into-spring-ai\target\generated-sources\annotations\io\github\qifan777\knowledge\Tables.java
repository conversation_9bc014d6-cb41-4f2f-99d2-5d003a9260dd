package io.github.qifan777.knowledge;

import io.github.qifan777.knowledge.ai.messge.AiMessageTable;
import io.github.qifan777.knowledge.ai.session.AiSessionTable;
import io.github.qifan777.knowledge.user.UserTable;
import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Tables {
    AiMessageTable AI_MESSAGE_TABLE = AiMessageTable.$;

    AiSessionTable AI_SESSION_TABLE = AiSessionTable.$;

    UserTable USER_TABLE = UserTable.$;
}
