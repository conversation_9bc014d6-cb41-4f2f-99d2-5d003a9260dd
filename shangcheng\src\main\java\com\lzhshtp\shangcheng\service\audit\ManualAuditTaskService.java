package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.audit.ManualAuditDecisionRequest;
import com.lzhshtp.shangcheng.mapper.ManualAuditTaskMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人工审核任务服务
 */
@Slf4j
@Service
public class ManualAuditTaskService {

    @Autowired
    private ManualAuditTaskMapper manualAuditTaskMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ManualAuditService manualAuditService;

    /**
     * 获取人工审核任务列表
     */
    public PageResult<ManualAuditTask> getManualAuditTasks(Integer page, Integer pageSize,
                                                          String status, Integer priority,
                                                          Long productId, Long adminId) {

        // 构建查询条件
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.hasText(status)) {
            params.put("status", status);
        }
        if (priority != null) {
            params.put("priority", priority);
        }
        if (productId != null) {
            params.put("productId", productId);
        }
        if (adminId != null) {
            params.put("adminId", adminId);
        }

        // 分页查询
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("limit", pageSize);

        List<ManualAuditTask> tasks = manualAuditTaskMapper.findByConditions(params);
        Long total = manualAuditTaskMapper.countByConditions(params);

        // 补充商品和用户信息
        for (ManualAuditTask task : tasks) {
            // 添加空值检查，防止空指针异常
            if (task == null) {
                log.warn("发现空的ManualAuditTask对象，跳过处理");
                continue;
            }

            // 检查productId是否为空
            if (task.getProductId() == null) {
                log.warn("ManualAuditTask的productId为空，taskId: {}", task.getTaskId());
                continue;
            }

            Product product = productMapper.selectById(task.getProductId());
            if (product != null) {
                task.setProductTitle(product.getTitle());
                task.setProductDescription(product.getDescription());
                task.setProductPrice(product.getPrice());
                task.setProductImages(product.getImageUrls());
            } else {
                log.warn("未找到对应的商品信息，productId: {}", task.getProductId());
            }

            // 检查sellerId是否为空
            if (task.getSellerId() != null) {
                User seller = userMapper.selectById(task.getSellerId());
                if (seller != null) {
                    task.setSellerNickname(seller.getUsername());
                } else {
                    log.warn("未找到对应的卖家信息，sellerId: {}", task.getSellerId());
                }
            } else {
                log.warn("ManualAuditTask的sellerId为空，taskId: {}", task.getTaskId());
            }
        }

        return new PageResult<>(tasks, total, page, pageSize);
    }

    /**
     * 获取人工审核统计数据
     */
    public Map<String, Object> getManualAuditStats() {
        Map<String, Object> params = new HashMap<>();

        // 获取各种状态的统计数据
        Long pendingCount = manualAuditTaskMapper.countByStatus("pending");
        Long processingCount = manualAuditTaskMapper.countByStatus("in_progress");
        Long completedCount = manualAuditTaskMapper.countByStatus("completed");

        // 获取超时任务数量
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Long overdueCount = manualAuditTaskMapper.countOverdueTasks(now);

        Map<String, Object> stats = new HashMap<>();
        stats.put("pending", pendingCount);
        stats.put("in_progress", processingCount);
        stats.put("completed", completedCount);
        stats.put("overdue", overdueCount);

        return stats;
    }

    /**
     * 认领审核任务
     */
    public boolean claimAuditTask(Long taskId, Long adminId) {
        ManualAuditTask task = manualAuditTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("审核任务不存在");
        }

        if (!"pending".equals(task.getStatus())) {
            return false; // 任务已被认领或已完成
        }

        // 更新任务状态 - 使用数据库ENUM中定义的值
        task.setStatus("in_progress");
        task.setAdminId(adminId);
        task.setAssignedTime(LocalDateTime.now()); // 使用assignedTime而不是claimedTime

        int updated = manualAuditTaskMapper.updateById(task);
        return updated > 0;
    }

    /**
     * 获取审核任务详情
     */
    public ManualAuditTask getAuditTaskDetail(Long taskId) {
        ManualAuditTask task = manualAuditTaskMapper.selectById(taskId);

        if (task != null) {
            // 补充商品信息
            if (task.getProductId() != null) {
                Product product = productMapper.selectById(task.getProductId());
                if (product != null) {
                    task.setProductTitle(product.getTitle());
                    task.setProductDescription(product.getDescription());
                    task.setProductPrice(product.getPrice());
                    task.setProductImages(product.getImageUrls());
                } else {
                    log.warn("未找到对应的商品信息，productId: {}", task.getProductId());
                }
            } else {
                log.warn("ManualAuditTask的productId为空，taskId: {}", taskId);
            }

            // 补充卖家信息
            if (task.getSellerId() != null) {
                User seller = userMapper.selectById(task.getSellerId());
                if (seller != null) {
                    task.setSellerNickname(seller.getUsername());
                } else {
                    log.warn("未找到对应的卖家信息，sellerId: {}", task.getSellerId());
                }
            } else {
                log.warn("ManualAuditTask的sellerId为空，taskId: {}", taskId);
            }
        }

        return task;
    }

    /**
     * 提交人工审核决策
     */
    public void submitManualAuditDecision(Long taskId, ManualAuditDecisionRequest request) {
        ManualAuditTask task = manualAuditTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("审核任务不存在");
        }

        if (!"in_progress".equals(task.getStatus())) {
            throw new RuntimeException("任务状态不正确，无法提交决策");
        }

        // 调用原有的审核服务处理决策
        manualAuditService.makeAuditDecision(taskId, request.getAdminId(), request.getDecision(), request.getComments());
    }

    /**
     * 获取审核任务的完整材料
     */
    public Map<String, Object> getAuditTaskMaterials(Long taskId) {
        ManualAuditTask task = manualAuditTaskMapper.selectById(taskId);

        if (task == null) {
            throw new RuntimeException("审核任务不存在");
        }

        Map<String, Object> materials = new HashMap<>();

        // 获取商品信息
        if (task.getProductId() != null) {
            Product product = productMapper.selectById(task.getProductId());
            materials.put("product", product);
            if (product == null) {
                log.warn("未找到对应的商品信息，productId: {}", task.getProductId());
            }
        } else {
            log.warn("ManualAuditTask的productId为空，taskId: {}", taskId);
            materials.put("product", null);
        }

        // 获取卖家信息
        if (task.getSellerId() != null) {
            User seller = userMapper.selectById(task.getSellerId());
            materials.put("seller", seller);
            if (seller == null) {
                log.warn("未找到对应的卖家信息，sellerId: {}", task.getSellerId());
            }
        } else {
            log.warn("ManualAuditTask的sellerId为空，taskId: {}", taskId);
            materials.put("seller", null);
        }

        // 获取审核原因
        materials.put("auditReasons", task.getAuditReasons());

        // 获取自动审核记录
        if (task.getAutoAuditRecordId() != null) {
            // TODO: 根据 autoAuditRecordId 获取自动审核结果
            materials.put("autoAuditRecordId", task.getAutoAuditRecordId());
        }

        return materials;
    }
}
