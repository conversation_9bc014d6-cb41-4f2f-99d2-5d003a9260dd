<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 二手交易平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            color: #333;
        }
        .register-container {
            background-color: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 100%;
            max-width: 400px;
            box-sizing: border-box;
            transform: translateY(0);
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        }
        .register-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        h2 {
            color: #e53935; /* Red color from index.html theme */
            margin-bottom: 30px;
            font-size: 2em;
            font-weight: 700;
        }
        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        .input-group input[type="tel"],
        .input-group input[type="text"],
        .input-group input[type="password"] {
            width: calc(100% - 20px);
            padding: 12px 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s, box-shadow 0.3s;
        }
        .input-group input[type="tel"]:focus,
        .input-group input[type="text"]:focus,
        .input-group input[type="password"]:focus {
            border-color: #e53935;
            box-shadow: 0 0 0 3px rgba(229, 57, 53, 0.2);
            outline: none;
        }
        .verification-input {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .verification-input input {
            /* flex-grow: 1; */
            /* width: auto; */
            flex: 2; /* 让输入框占据2份空间 */
        }
        .send-code-button {
            padding: 12px 15px;
            background-color: #e53935;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: background-color 0.3s ease, transform 0.2s ease;
            white-space: nowrap; /* Prevent text wrapping */
            flex: 1; /* 让按钮占据1份空间 */
        }
        .send-code-button:hover {
            background-color: #c62828;
            transform: translateY(-2px);
        }
        .register-button {
            width: 100%;
            padding: 15px;
            background-color: #e53935;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 700;
            margin-top: 20px;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .register-button:hover {
            background-color: #c62828;
            transform: translateY(-2px);
        }
        .login-link {
            margin-top: 25px;
            font-size: 0.95em;
        }
        .login-link a {
            color: #e53935;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .login-link a:hover {
            color: #c62828;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <h2>注册账户</h2>
        <form>
            <div class="input-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入您的用户名" required>
            </div>
            <div class="input-group">
                <label for="mobile">手机号码</label>
                <div class="verification-input">
                    <input type="tel" id="mobile" name="mobile" placeholder="请输入您的手机号码" required>
                    <button type="button" class="send-code-button">发送验证码</button>
                </div>
            </div>
            <div class="input-group">
                <label for="code">验证码</label>
                <input type="text" id="code" name="code" placeholder="请输入验证码" required>
            </div>
            <div class="input-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请创建密码" required>
            </div>
            <div class="input-group">
                <label for="confirm-password">确认密码</label>
                <input type="password" id="confirm-password" name="confirm-password" placeholder="请确认您的密码" required>
            </div>
            <button type="submit" class="register-button">注册</button>
        </form>
        <div class="login-link">
            已有账户？ <a href="login.html">立即登录</a>
        </div>
    </div>
</body>
</html> 