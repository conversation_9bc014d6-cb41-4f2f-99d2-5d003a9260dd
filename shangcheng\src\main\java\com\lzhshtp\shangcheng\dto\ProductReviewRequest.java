package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品评价请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductReviewRequest {
    
    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    private Long productId;
    
    /**
     * 评分（1-5星）
     */
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最小为1")
    @Max(value = 5, message = "评分最大为5")
    private Integer rating;
    
    /**
     * 评价内容
     */
    @Size(min = 5, max = 500, message = "评价内容长度应在5-500字符之间")
    private String comment;
} 