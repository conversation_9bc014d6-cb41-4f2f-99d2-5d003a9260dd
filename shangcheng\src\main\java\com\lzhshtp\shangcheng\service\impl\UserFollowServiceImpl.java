package com.lzhshtp.shangcheng.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.UserFollowDTO;
import com.lzhshtp.shangcheng.mapper.UserFollowMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.model.UserFollow;
import com.lzhshtp.shangcheng.service.UserFollowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户关注服务实现类
 */
@Service
public class UserFollowServiceImpl extends ServiceImpl<UserFollowMapper, UserFollow> implements UserFollowService {

    @Autowired
    private UserFollowMapper userFollowMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    @Transactional
    public boolean followUser(Long followerId, Long followingId) {
        // 检查是否已关注
        if (isFollowing(followerId, followingId)) {
            return true; // 已关注，直接返回成功
        }
        
        // 创建新的关注关系
        UserFollow userFollow = new UserFollow();
        userFollow.setFollowerId(followerId);
        userFollow.setFollowingId(followingId);
        userFollow.setFollowedAt(LocalDateTime.now());
        
        return userFollowMapper.insert(userFollow) > 0;
    }
    
    @Override
    @Transactional
    public boolean unfollowUser(Long followerId, Long followingId) {
        // 查询关注关系
        UserFollow userFollow = userFollowMapper.selectByFollowerIdAndFollowingId(followerId, followingId);
        if (userFollow == null) {
            return true; // 未关注，直接返回成功
        }
        
        // 删除关注关系
        return userFollowMapper.deleteById(userFollow.getFollowId()) > 0;
    }
    
    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        return userFollowMapper.selectByFollowerIdAndFollowingId(followerId, followingId) != null;
    }
    
    @Override
    public PageResult<UserFollowDTO> getFollowings(Long followerId, Integer page, Integer pageSize) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        
        // 创建分页对象
        Page<UserFollow> pageParam = new Page<>(page, pageSize);
        
        // 查询关注列表
        IPage<UserFollow> followPage = userFollowMapper.selectFollowingsByFollowerId(pageParam, followerId);
        
        // 转换为DTO
        List<UserFollowDTO> followDTOs = new ArrayList<>();
        for (UserFollow follow : followPage.getRecords()) {
            UserFollowDTO dto = new UserFollowDTO();
            dto.setFollowId(follow.getFollowId());
            dto.setFollowerId(follow.getFollowerId());
            dto.setFollowingId(follow.getFollowingId());
            dto.setFollowedAt(follow.getFollowedAt());
            
            // 获取关注者信息
            User follower = userMapper.selectById(follow.getFollowerId());
            if (follower != null) {
                dto.setFollowerUsername(follower.getUsername());
                dto.setFollowerAvatarUrl(follower.getAvatarUrl());
            }
            
            // 获取被关注者信息
            User following = userMapper.selectById(follow.getFollowingId());
            if (following != null) {
                dto.setFollowingUsername(following.getUsername());
                dto.setFollowingAvatarUrl(following.getAvatarUrl());
            }
            
            followDTOs.add(dto);
        }
        
        // 返回分页结果
        return new PageResult<>(followDTOs, followPage.getTotal(), page, pageSize);
    }
    
    @Override
    public PageResult<UserFollowDTO> getFollowers(Long followingId, Integer page, Integer pageSize) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        
        // 创建分页对象
        Page<UserFollow> pageParam = new Page<>(page, pageSize);
        
        // 查询粉丝列表
        IPage<UserFollow> followerPage = userFollowMapper.selectFollowersByFollowingId(pageParam, followingId);
        
        // 转换为DTO
        List<UserFollowDTO> followerDTOs = new ArrayList<>();
        for (UserFollow follow : followerPage.getRecords()) {
            UserFollowDTO dto = new UserFollowDTO();
            dto.setFollowId(follow.getFollowId());
            dto.setFollowerId(follow.getFollowerId());
            dto.setFollowingId(follow.getFollowingId());
            dto.setFollowedAt(follow.getFollowedAt());
            
            // 获取关注者信息
            User follower = userMapper.selectById(follow.getFollowerId());
            if (follower != null) {
                dto.setFollowerUsername(follower.getUsername());
                dto.setFollowerAvatarUrl(follower.getAvatarUrl());
            }
            
            // 获取被关注者信息
            User following = userMapper.selectById(follow.getFollowingId());
            if (following != null) {
                dto.setFollowingUsername(following.getUsername());
                dto.setFollowingAvatarUrl(following.getAvatarUrl());
            }
            
            followerDTOs.add(dto);
        }
        
        // 返回分页结果
        return new PageResult<>(followerDTOs, followerPage.getTotal(), page, pageSize);
    }
    
    @Override
    public Integer getFollowingsCount(Long followerId) {
        return userFollowMapper.countFollowingsByFollowerId(followerId);
    }
    
    @Override
    public Integer getFollowersCount(Long followingId) {
        return userFollowMapper.countFollowersByFollowingId(followingId);
    }
} 