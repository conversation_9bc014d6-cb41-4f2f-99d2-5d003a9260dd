package com.lzhshtp.shangcheng.dto.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 用户统计数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStatisticsDTO {
    
    // 用户注册趋势数据
    private List<String> registrationDates;     // 日期列表
    private List<Long> registrationCounts;      // 对应日期的注册数量
    private List<Long> cumulativeUsers;         // 累计用户数
    
    // 用户角色分布
    private Map<String, Long> roleDistribution; // 角色分布 {admin: 5, general_user: 1000, ai_customer_service: 2}
    
    // 用户状态分布
    private Long activeUsers;                   // 活跃用户数
    private Long inactiveUsers;                 // 非活跃用户数
    
    // 用户地域分布（Top 10）
    private List<LocationStatisticsDTO> topLocations;
    
    // 用户活跃度分析
    private List<String> activityDates;        // 活跃度统计日期
    private List<Long> dailyActiveUsers;       // 每日活跃用户数
    private List<Long> weeklyActiveUsers;      // 每周活跃用户数
    private List<Long> monthlyActiveUsers;     // 每月活跃用户数
    
    // 用户行为统计
    private Double avgProductsPerUser;          // 平均每用户发布商品数
    private Double avgOrdersPerUser;            // 平均每用户订单数
    private Double avgForumPostsPerUser;        // 平均每用户论坛帖子数
    
    // 新用户留存率
    private Map<String, Double> retentionRates; // {day1: 0.8, day7: 0.6, day30: 0.4}
    
    // 用户信用分布
    private Map<String, Long> creditScoreDistribution; // 信用分区间分布
}


