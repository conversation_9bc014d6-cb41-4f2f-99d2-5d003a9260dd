package com.lzhshtp.shangcheng.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lzhshtp.shangcheng.dto.ForumCommentDTO;
import com.lzhshtp.shangcheng.dto.ForumCommentRequest;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ForumPostRequest;
import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ForumCommentService;
import com.lzhshtp.shangcheng.service.ForumPostService;
import com.lzhshtp.shangcheng.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 论坛帖子控制器
 */
@RestController
@RequestMapping("/api/forum/posts")
@RequiredArgsConstructor
public class ForumPostController {

    private final ForumPostService forumPostService;
    private final ForumCommentService forumCommentService;
    private final UserService userService;

    /**
     * 创建帖子
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> createPost(@RequestBody @Valid ForumPostRequest request,
                                           @AuthenticationPrincipal UserDetails userDetails) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        Long userId = user.getUserId();
        Long postId = forumPostService.createPost(request, userId);
        return ApiResponse.success(postId);
    }

    /**
     * 更新帖子
     */
    @PutMapping("/{postId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> updatePost(@PathVariable Long postId,
                                              @RequestBody @Valid ForumPostRequest request,
                                              @AuthenticationPrincipal UserDetails userDetails) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        Long userId = user.getUserId();
        boolean result = forumPostService.updatePost(postId, request, userId);
        return ApiResponse.success(result);
    }

    /**
     * 删除帖子
     */
    @DeleteMapping("/{postId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> deletePost(@PathVariable Long postId,
                                              @AuthenticationPrincipal UserDetails userDetails) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        Long userId = user.getUserId();
        boolean result = forumPostService.deletePost(postId, userId);
        return ApiResponse.success(result);
    }

    /**
     * 获取帖子详情
     */
    @GetMapping("/{postId}")
    public ApiResponse<ForumPostDTO> getPostDetail(@PathVariable Long postId) {
        ForumPostDTO postDTO = forumPostService.getPostDetail(postId);
        return ApiResponse.success(postDTO);
    }

    /**
     * 分页查询帖子列表
     */
    @GetMapping
    public ApiResponse<IPage<ForumPostDTO>> getPostList(ForumPostQueryRequest queryRequest) {
        IPage<ForumPostDTO> page = forumPostService.getPostList(queryRequest);
        return ApiResponse.success(page);
    }

    /**
     * 设置帖子置顶状态
     */
    @PutMapping("/{postId}/pinned")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Boolean> setPinned(@PathVariable Long postId, @RequestParam Boolean isPinned) {
        boolean result = forumPostService.setPinned(postId, isPinned);
        return ApiResponse.success(result);
    }

    /**
     * 设置帖子状态（管理员操作）
     */
    @PutMapping("/{postId}/status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ApiResponse<Boolean> setStatus(@PathVariable Long postId, @RequestParam Integer status) {
        boolean result = forumPostService.setStatus(postId, status);
        return ApiResponse.success(result);
    }

    /**
     * 获取帖子的评论列表
     */
    @GetMapping("/{postId}/comments")
    public ApiResponse<List<ForumCommentDTO>> getComments(@PathVariable Long postId) {
        List<ForumCommentDTO> comments = forumCommentService.getCommentsByPostId(postId);
        return ApiResponse.success(comments);
    }

    /**
     * 发表评论
     */
    @PostMapping("/comments")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Long> createComment(@RequestBody @Valid ForumCommentRequest request,
                                              @AuthenticationPrincipal UserDetails userDetails) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        Long userId = user.getUserId();
        Long commentId = forumCommentService.createComment(request, userId);
        return ApiResponse.success(commentId);
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/comments/{commentId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> deleteComment(@PathVariable Long commentId,
                                                 @AuthenticationPrincipal UserDetails userDetails) {
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            return ApiResponse.fail("用户不存在");
        }
        Long userId = user.getUserId();
        boolean result = forumCommentService.deleteComment(commentId, userId);
        return ApiResponse.success(result);
    }
} 