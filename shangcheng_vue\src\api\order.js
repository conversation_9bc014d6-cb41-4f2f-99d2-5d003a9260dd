import request from '@/utils/request';

/**
 * 创建订单
 * @param {Object} data 订单数据
 * @returns {Promise} 请求Promise
 */
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'post',
    data
  });
}

/**
 * 获取订单详情
 * @param {Number} id 订单ID
 * @returns {Promise} 请求Promise
 */
export function getOrderById(id) {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  });
}

/**
 * 获取订单支付信息
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function getPaymentInfo(orderId) {
  return request({
    url: `/orders/${orderId}/payment-info`,
    method: 'get'
  });
}

/**
 * 使用余额支付订单
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function payWithBalance(orderId) {
  return request({
    url: `/orders/${orderId}/pay/balance`,
    method: 'post'
  });
}

/**
 * 获取买家订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getBuyerOrders(params) {
  return request({
    url: '/orders/buyer',
    method: 'get',
    params
  });
}

/**
 * 获取卖家订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getSellerOrders(params) {
  return request({
    url: '/orders/seller',
    method: 'get',
    params
  });
}

/**
 * 取消订单
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function cancelOrder(orderId) {
  return request({
    url: `/orders/${orderId}/cancel`,
    method: 'post'
  });
}

/**
 * 卖家发货
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function shipOrder(orderId) {
  return request({
    url: `/orders/${orderId}/ship`,
    method: 'post'
  });
}

/**
 * 买家确认收货（完成订单）
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function completeOrder(orderId) {
  return request({
    url: `/orders/${orderId}/complete`,
    method: 'post'
  });
}

/**
 * 更新订单状态
 * @param {Number} orderId 订单ID
 * @param {String} status 订单状态
 * @returns {Promise} 请求Promise
 */
export function updateOrderStatus(orderId, status) {
  return request({
    url: `/orders/${orderId}/status`,
    method: 'put',
    params: { status }
  });
}

/**
 * 创建验货记录
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function createVerificationRecord(orderId) {
  return request({
    url: `/orders/${orderId}/verification`,
    method: 'post'
  });
}

/**
 * 获取订单物流信息
 * @param {Number} orderId 订单ID
 * @returns {Promise} 请求Promise
 */
export function getOrderLogistics(orderId) {
  return request({
    url: `/orders/${orderId}/logistics`,
    method: 'get'
  });
}
