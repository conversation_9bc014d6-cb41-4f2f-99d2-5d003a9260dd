<template>
  <div class="search-page">
    <!-- 顶部导航 -->
    <nav class="top-nav">
        <div class="container nav-content">
            <router-link to="/home" class="logo">易转</router-link>
            <div class="search-area">
                <div class="search-bar-container">
                    <div class="search-bar">
                        <input
                          v-model="searchKeyword"
                          type="text"
                          class="search-input"
                          placeholder="搜索想要的商品..."
                          @keyup.enter="performSearch"
                          @input="onSearchInput"
                          @focus="showSuggestions = true"
                          @blur="hideSuggestions"
                        >
                        <button class="search-button" @click="performSearch">搜索</button>
                    </div>

                    <!-- 搜索建议下拉框 -->
                    <div v-if="showSuggestions && suggestions.length > 0" class="search-suggestions">
                        <div
                          v-for="(suggestion, index) in suggestions"
                          :key="index"
                          class="suggestion-item"
                          @mousedown="selectSuggestion(suggestion)"
                        >
                            <i class="suggestion-icon">🔍</i>
                            <span class="suggestion-text">{{ suggestion }}</span>
                        </div>
                    </div>
                </div>
                <div class="search-tags">
                    <a v-for="link in quickSearchLinks" :key="link" href="#" @click.prevent="quickSearch(link)">{{ link }}</a>
                </div>
            </div>
            <div class="nav-right">
                <router-link to="/profile" class="user-avatar" title="个人中心">
                    <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
                </router-link>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 筛选区域 -->
        <div class="filter-section">
          <!-- 排序标签 -->
          <div class="filter-tabs">
            <button
              v-for="filter in filterTabs"
              :key="filter.key"
              :class="['filter-tab', { active: currentFilter === filter.key }]"
              @click="switchFilter(filter.key)"
            >
              {{ filter.label }}
              <span v-if="filter.icon && currentFilter === filter.key" class="sort-icon">{{ filter.icon }}</span>
              <span v-if="currentFilter === filter.key" class="filter-active-indicator"></span>
            </button>
          </div>

          <!-- 高级筛选条件 -->
          <div class="advanced-filters">
            <div class="filter-row">
              <!-- 新旧程度筛选 -->
              <div class="filter-group">
                <span class="filter-label">新旧程度：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !searchFilters.condition }]"
                    @click="toggleSearchFilter('condition', null)">
                    不限
                  </button>
                  <button
                    v-for="condition in conditionOptions"
                    :key="condition.value"
                    :class="['filter-btn', { active: searchFilters.condition === condition.value }]"
                    @click="toggleSearchFilter('condition', condition.value)">
                    {{ condition.label }}
                  </button>
                </div>
              </div>

              <!-- 交易方式筛选 -->
              <div class="filter-group">
                <span class="filter-label">交易方式：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !searchFilters.deliveryMethod }]"
                    @click="toggleSearchFilter('deliveryMethod', null)">
                    不限
                  </button>
                  <button
                    v-for="method in deliveryMethodOptions"
                    :key="method.value"
                    :class="['filter-btn', { active: searchFilters.deliveryMethod === method.value }]"
                    @click="toggleSearchFilter('deliveryMethod', method.value)">
                    {{ method.label }}
                  </button>
                </div>
              </div>

              <!-- 区域筛选 -->
              <div class="filter-group">
                <span class="filter-label">区域：</span>
                <div class="filter-options">
                  <button
                    :class="['filter-btn', { active: !searchFilters.location }]"
                    @click="toggleSearchFilter('location', null)">
                    不限
                  </button>
                  <button
                    :class="['filter-btn', { active: searchFilters.location === 'same_city' }]"
                    @click="toggleSearchFilter('location', 'same_city')">
                    同城
                  </button>
                  <el-select
                    v-model="searchFilters.location"
                    placeholder="选择城市"
                    clearable
                    @change="onLocationChange"
                    style="width: 120px; margin-left: 8px;">
                    <el-option
                      v-for="city in cityOptions"
                      :key="city.value"
                      :label="city.label"
                      :value="city.value">
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 当前筛选条件显示 -->
            <div v-if="hasActiveSearchFilters" class="active-filters">
              <span class="active-filters-label">当前筛选：</span>
              <el-tag
                v-if="searchFilters.condition"
                closable
                @close="toggleSearchFilter('condition', null)"
                type="primary">
                {{ getConditionLabel(searchFilters.condition) }}
              </el-tag>
              <el-tag
                v-if="searchFilters.deliveryMethod"
                closable
                @close="toggleSearchFilter('deliveryMethod', null)"
                type="success">
                {{ getDeliveryMethodLabel(searchFilters.deliveryMethod) }}
              </el-tag>
              <el-tag
                v-if="searchFilters.location"
                closable
                @close="toggleSearchFilter('location', null)"
                type="warning">
                {{ getLocationLabel(searchFilters.location) }}
              </el-tag>
              <el-button
                size="small"
                type="danger"
                link
                @click="clearAllSearchFilters">
                清空筛选
              </el-button>
            </div>
          </div>

          <div v-if="products.length > 0" class="filter-result-hint">
            {{ getFilterHint() }}
          </div>
        </div>

        <!-- 商品展示区域 -->
        <div class="products-section">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <span>正在搜索商品...</span>
          </div>

          <!-- 商品网格 -->
          <div v-else-if="products.length > 0" class="products-grid">
            <div
              v-for="product in products"
              :key="product.id"
              class="product-card"
              @click="goToProduct(product.id)"
            >
              <div class="product-image">
                <img
                  :src="product.imageUrls && product.imageUrls.length > 0 ? product.imageUrls[0] : 'https://via.placeholder.com/300x200'"
                  :alt="product.title"
                />
              </div>
              <div class="product-info">
                <h3 class="product-title">{{ product.title }}</h3>
                <div class="product-price">
                  <span class="current-price">¥{{ product.price }}</span>
                </div>
                <div class="product-meta">
                  <div class="seller-info">
                    <img
                      :src="product.sellerAvatar || defaultAvatar"
                      :alt="product.sellerName"
                      class="seller-avatar"
                    >
                    <span class="seller-name">{{ product.sellerName || '未知卖家' }}</span>
                  </div>
                  <div class="product-location" v-if="product.location">
                    <span>{{ product.location }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">🔍</div>
            <h3>没有找到相关商品</h3>
            <p>试试其他关键词或筛选条件</p>
          </div>

          <!-- 搜索结果统计和加载更多 -->
          <div v-if="products.length > 0" class="search-results-info">
            <div class="results-count">找到 {{ totalRecords }} 个相关商品</div>

            <!-- 加载中指示器 -->
            <div v-if="loadingMore" class="loading-more">
              <div class="loading-spinner small"></div>
              <span>加载中...</span>
            </div>
            
            <!-- 没有更多数据提示 -->
            <div v-if="!hasMore && !loadingMore" class="no-more-products">
              已经到底啦，没有更多商品了~
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 悬浮操作按钮 -->
    <FloatingActionButtons />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import FloatingActionButtons from '@/components/FloatingActionButtons.vue'
import { searchProducts, getProductSearchSuggestions } from '@/api/search' // 改为使用ElasticSearch API
import { searchProducts as fallbackSearchProducts } from '@/api/product' // 保留原API作为降级

const router = useRouter()
const route = useRoute()

// 用户状态管理
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
// 默认头像 - 使用内置SVG
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 响应式数据
const searchKeyword = ref('')
const loading = ref(false)
const loadingMore = ref(false)
const products = ref([])
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(20)
const totalPages = ref(0)
const totalRecords = ref(0)

// 搜索建议相关
const suggestions = ref([])
const showSuggestions = ref(false)
const suggestionTimer = ref(null)

// 筛选相关
const currentFilter = ref('all')
// 排序方向 (ASC: 升序, DESC: 降序)
const sortDirection = ref('asc')

// 搜索筛选条件
const searchFilters = ref({
  condition: null,        // 新旧程度
  deliveryMethod: null,   // 交易方式
  location: null          // 区域
})

// 筛选选项数据
const conditionOptions = ref([
  { value: '全新', label: '全新' },
  { value: '九成新', label: '九成新' },
  { value: '八成新', label: '八成新' },
  { value: '七成新', label: '七成新' },
  { value: '六成新及以下', label: '六成新及以下' }
])

const deliveryMethodOptions = ref([
  { value: '同城配送', label: '同城配送' },
  { value: '快递邮寄', label: '快递邮寄' },
  { value: '线下自提', label: '线下自提' }
])

const cityOptions = ref([
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '杭州', label: '杭州' },
  { value: '南京', label: '南京' },
  { value: '武汉', label: '武汉' },
  { value: '成都', label: '成都' },
  { value: '西安', label: '西安' },
  { value: '重庆', label: '重庆' }
])

// 快速搜索链接
const quickSearchLinks = ref([
  '数码产品', '服装配饰', '家居用品', '运动户外', '美妆护肤', '图书音像', '母婴用品', '汽车用品'
])

// 筛选标签
const filterTabs = ref([
  { key: 'all', label: '综合' },
  { key: 'new', label: '最新', icon: '↓' },
  { key: 'price', label: '价格', icon: '↑' }
])

// 方法
const performSearch = async () => {
  loading.value = true
  currentPage.value = 1
  hasMore.value = true
  products.value = []

  try {
    // 构建查询参数
    const params = {
      keyword: searchKeyword.value,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据当前筛选添加排序参数
    if (currentFilter.value === 'new') {
      params.sortBy = 'date'
      params.sortDirection = sortDirection.value
    } else if (currentFilter.value === 'price') {
      params.sortBy = 'price'
      params.sortDirection = sortDirection.value
    }

    // 添加搜索筛选条件
    if (searchFilters.value.condition) {
      params.condition = searchFilters.value.condition
    }

    if (searchFilters.value.deliveryMethod) {
      params.deliveryMethod = searchFilters.value.deliveryMethod
    }

    if (searchFilters.value.location) {
      if (searchFilters.value.location === 'same_city') {
        // 同城筛选 - 使用用户的location字段
        if (userInfo.value && userInfo.value.location) {
          params.location = userInfo.value.location
          console.log('设置同城筛选，用户所在地:', params.location)
        } else {
          console.warn('用户未设置所在地，无法进行同城筛选')
          // 提示用户设置所在地
          ElMessage.warning('请先在个人中心设置您的所在地，才能使用同城筛选功能')
          // 重置同城筛选状态
          searchFilters.value.location = null
          return
        }
      } else {
        params.location = searchFilters.value.location
      }
    }

    console.log('搜索参数:', params)
    console.log('当前筛选条件:', searchFilters.value)

    // 优先使用ElasticSearch搜索，失败时降级到数据库搜索
    let response
    try {
      console.log('使用ElasticSearch搜索...')
      response = await searchProducts(params)
    } catch (error) {
      console.warn('ElasticSearch搜索失败，降级到数据库搜索:', error)
      response = await fallbackSearchProducts(params)
    }

    if (response && response.code === 200 && response.data) {
      const productData = response.data

      if (productData.records && productData.records.length > 0) {
        products.value = productData.records
        totalPages.value = productData.pages
        totalRecords.value = productData.total

        hasMore.value = currentPage.value < productData.pages
      } else {
        products.value = []
        hasMore.value = false
        totalPages.value = 0
        totalRecords.value = 0
      }
    } else {
      console.error('搜索商品返回格式错误', response)
      products.value = []
      hasMore.value = false
    }
  } catch (error) {
    console.error('搜索商品失败:', error)
    products.value = []
    hasMore.value = false
  } finally {
    loading.value = false
  }
}

// 搜索建议相关方法
const onSearchInput = () => {
  // 清除之前的定时器
  if (suggestionTimer.value) {
    clearTimeout(suggestionTimer.value)
  }

  // 设置新的定时器，防抖处理
  suggestionTimer.value = setTimeout(async () => {
    if (searchKeyword.value && searchKeyword.value.length >= 2) {
      try {
        const response = await getProductSearchSuggestions(searchKeyword.value)
        if (response && response.code === 200) {
          suggestions.value = response.data || []
          showSuggestions.value = suggestions.value.length > 0
        }
      } catch (error) {
        console.warn('获取搜索建议失败:', error)
        suggestions.value = []
        showSuggestions.value = false
      }
    } else {
      suggestions.value = []
      showSuggestions.value = false
    }
  }, 300) // 300ms防抖
}

const selectSuggestion = (suggestion) => {
  searchKeyword.value = suggestion
  showSuggestions.value = false
  performSearch()
}

const hideSuggestions = () => {
  // 延迟隐藏，给点击事件时间执行
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const quickSearch = (keyword) => {
  searchKeyword.value = keyword
  performSearch()

  // 更新URL参数，但不重新加载页面
  router.replace({
    query: { ...route.query, q: keyword }
  })
}

const switchFilter = (filterKey) => {
  // 如果点击的是当前激活的筛选项，切换排序方向
  if (currentFilter.value === filterKey) {
    // 切换排序方向
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    
    // 更新筛选标签图标
    updateFilterIcons();
  } else {
    // 更新当前筛选
    currentFilter.value = filterKey
    
    // 设置默认排序方向
    if (filterKey === 'new') {
      // 最新默认降序（新的在前）
      sortDirection.value = 'desc';
    } else if (filterKey === 'price') {
      // 价格默认升序（便宜的在前）
      sortDirection.value = 'asc';
    }
    
    // 更新筛选标签图标
    updateFilterIcons();
  }
  
  // 重置分页参数
  currentPage.value = 1
  hasMore.value = true
  
  // 重新搜索
  performSearch()
}

// 更新筛选标签的图标
const updateFilterIcons = () => {
  if (currentFilter.value === 'new') {
    filterTabs.value[1].icon = sortDirection.value === 'asc' ? '↑' : '↓';
  } else if (currentFilter.value === 'price') {
    filterTabs.value[2].icon = sortDirection.value === 'asc' ? '↑' : '↓';
  }
}

const goToProduct = (productId) => {
  router.push(`/product/${productId}`)
}

const loadMore = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  currentPage.value++

  try {
    // 构建查询参数
    const params = {
      keyword: searchKeyword.value,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据当前筛选添加排序参数
    if (currentFilter.value === 'new') {
      params.sortBy = 'date'
      params.sortDirection = sortDirection.value
    } else if (currentFilter.value === 'price') {
      params.sortBy = 'price'
      params.sortDirection = sortDirection.value
    }

    console.log('加载更多参数:', params)

    // 调用API加载更多商品
    const response = await searchProducts(params)

    if (response && response.code === 200 && response.data) {
      const productData = response.data

      if (productData.records && productData.records.length > 0) {
        products.value = [...products.value, ...productData.records]
        hasMore.value = currentPage.value < productData.pages
      } else {
        hasMore.value = false
      }
    } else {
      console.error('加载更多商品返回格式错误', response)
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载更多商品失败:', error)
    hasMore.value = false
  } finally {
    loadingMore.value = false
  }
}

// 检测滚动到底部
const checkScrollBottom = () => {
  // 检查是否滚动到接近底部
  const scrollPosition = window.scrollY + window.innerHeight;
  const pageHeight = document.documentElement.scrollHeight;
  
  // 当滚动到距离底部100px时加载更多
  if (pageHeight - scrollPosition < 100 && hasMore.value && !loading.value && !loadingMore.value) {
    loadMore();
  }
};

// 获取当前筛选的提示文本
const getFilterHint = () => {
  switch(currentFilter.value) {
    case 'new':
      return `按最新发布时间${sortDirection.value === 'asc' ? '从旧到新' : '从新到旧'}排序`;
    case 'price':
      return `按价格${sortDirection.value === 'asc' ? '从低到高' : '从高到低'}排序`;
    default:
      return '按综合排序';
  }
}

// 监听路由参数变化
watch(() => route.query.q, (newKeyword) => {
  if (newKeyword) {
    searchKeyword.value = newKeyword
    performSearch()
  }
})

// 计算属性：是否有激活的搜索筛选条件
const hasActiveSearchFilters = computed(() => {
  return searchFilters.value.condition || searchFilters.value.deliveryMethod || searchFilters.value.location
})

// 搜索筛选相关方法
const toggleSearchFilter = (filterType, value) => {
  searchFilters.value[filterType] = searchFilters.value[filterType] === value ? null : value

  // 重置分页并重新搜索
  currentPage.value = 1
  hasMore.value = true
  products.value = []

  performSearch()
}

const onLocationChange = (value) => {
  searchFilters.value.location = value

  // 重置分页并重新搜索
  currentPage.value = 1
  hasMore.value = true
  products.value = []

  performSearch()
}

const clearAllSearchFilters = () => {
  searchFilters.value = {
    condition: null,
    deliveryMethod: null,
    location: null
  }

  // 重置分页并重新搜索
  currentPage.value = 1
  hasMore.value = true
  products.value = []

  performSearch()
}

// 获取标签显示文本的方法
const getConditionLabel = (value) => {
  const option = conditionOptions.value.find(opt => opt.value === value)
  return option ? option.label : value
}

const getDeliveryMethodLabel = (value) => {
  const option = deliveryMethodOptions.value.find(opt => opt.value === value)
  return option ? option.label : value
}

const getLocationLabel = (value) => {
  if (value === 'same_city') return '同城'
  return value
}

// 生命周期
onMounted(() => {
  // 从路由参数获取搜索关键词
  if (route.query.q) {
    searchKeyword.value = route.query.q
    performSearch()
  }
  
  // 添加滚动监听
  window.addEventListener('scroll', checkScrollBottom);
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', checkScrollBottom);
})
</script>

<style scoped>
/* 全局样式 */
.search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --primary-color: #FF4D4F;
  --secondary-color: #FF7875;
  --text-color-dark: #333;
  --text-color-light: #666;
  --bg-color: #F5F5F5;
  --white: #FFFFFF;
  --border-color: #EFEFEF;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 顶部导航 */
.top-nav {
  background: var(--white);
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.search-area {
  flex-grow: 1;
  margin: 0 32px;
  max-width: 600px;
  position: relative; /* 为搜索建议定位 */
}

.search-bar-container {
  position: relative;
}

.search-bar {
  display: flex;
  border: 2px solid var(--primary-color);
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border: none;
  background: none;
  padding: 10px 20px;
  width: 100%;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
}

.search-button {
  background: var(--primary-color);
  border: none;
  color: white;
  padding: 0 24px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}

.search-tags {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 14px;
}

.search-tags a {
  color: var(--text-color-light);
  text-decoration: none;
}

.search-tags a:hover {
  color: var(--primary-color);
}

.nav-right {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: transform 0.2s ease;
}

.avatar-image:hover {
  transform: scale(1.1);
}

/* 主要内容区域 */
.main-content {
  padding: 20px 0;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
  position: relative; /* Added for positioning the indicator */
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-tab.active {
  background: var(--primary-color);
  color: white;
  font-weight: 500;
}

.filter-tab:hover:not(.active) {
  background: #f5f5f5;
}

.filter-active-indicator {
  position: absolute;
  bottom: -4px; /* Adjust as needed */
  left: 50%;
  transform: translateX(-50%);
  width: 20px; /* Adjust width to match button padding */
  height: 3px; /* Adjust height for the indicator */
  background-color: white; /* Indicator color */
  border-radius: 2px;
}

.sort-icon {
  font-size: 12px;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.filter-result-hint {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 商品展示区域 */
.products-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin-bottom: 0;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.product-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #eee;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.product-info {
  padding: 16px;
}

.product-title {
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 12px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  margin-bottom: 12px;
}

.current-price {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  margin-right: 8px;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.seller-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.seller-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.seller-name {
  font-size: 14px;
  color: #666;
}

.product-stats {
  font-size: 12px;
  color: #999;
}

.product-location {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.product-location:before {
  content: "📍";
  margin-right: 4px;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: #333;
}

.empty-state p {
  font-size: 14px;
}

/* 加载更多 */
.load-more-section {
  width: 100%;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 32px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

/* 搜索结果统计 */
.search-results-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eee;
  font-size: 14px;
  color: #666;
}

.results-count {
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.no-more-products {
  font-style: italic;
  color: #999;
  text-align: center;
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    flex-direction: column;
    gap: 12px;
  }

  .search-area {
    margin: 0;
    max-width: none;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .filter-tabs, .category-tags {
    flex-wrap: wrap;
  }

  .sort-view-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .product-image {
    height: 160px;
  }

  .container {
    padding: 0 12px;
  }
}

/* 搜索建议样式 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  margin-right: 8px;
  font-size: 14px;
  opacity: 0.6;
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

/* 高级筛选样式 */
.advanced-filters {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
}

.filter-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.filter-btn {
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 14px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.filter-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.active-filters {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.active-filters-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .advanced-filters {
    padding: 12px;
  }

  .filter-row {
    gap: 10px;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .filter-label {
    min-width: auto;
    font-size: 13px;
  }

  .filter-options {
    width: 100%;
  }

  .filter-btn {
    font-size: 11px;
    padding: 3px 10px;
  }

  .active-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 高级筛选样式 */
.advanced-filters {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
}

.filter-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.filter-btn {
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 14px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.filter-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.active-filters {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.active-filters-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .advanced-filters {
    padding: 12px;
  }

  .filter-row {
    gap: 10px;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .filter-label {
    min-width: auto;
    font-size: 13px;
  }

  .filter-options {
    width: 100%;
  }

  .filter-btn {
    font-size: 11px;
    padding: 3px 10px;
  }

  .active-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
