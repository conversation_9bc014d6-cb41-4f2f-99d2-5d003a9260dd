package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    
    /**
     * 查询所有一级分类（parentId为null的分类）
     * 
     * @return 一级分类列表
     */
    List<Category> selectRootCategories();
    
    /**
     * 根据父分类ID查询子分类
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<Category> selectCategoriesByParentId(@Param("parentId") Integer parentId);

    /**
     * 根据分类名称查询分类
     *
     * @param name 分类名称
     * @return 分类对象
     */
    Category findByName(@Param("name") String name);
}