import request from '../utils/request'

/**
 * 管理员验货管理API
 */

// 获取待验货列表
export const getPendingVerifications = () => {
  return request({
    url: '/verifications/pending/all',
    method: 'get'
  })
}

// 获取所有验货记录（带分页）
export const getAllVerifications = (params) => {
  return request({
    url: '/verifications/admin/list',
    method: 'get',
    params
  })
}

// 更新验货状态
export const updateVerificationStatus = (verificationId, status) => {
  return request({
    url: `/verifications/${verificationId}/status`,
    method: 'post',
    data: { status }
  })
}

// 提交验货结果
export const submitVerificationResult = (data) => {
  return request({
    url: '/verifications/result',
    method: 'post',
    data
  })
}

// 验货通过，转发给买家
export const forwardToBuyer = (verificationId) => {
  return request({
    url: `/verifications/forward/${verificationId}`,
    method: 'post'
  })
}

// 获取验货详情
export const getVerificationDetail = (verificationId) => {
  return request({
    url: `/verifications/${verificationId}`,
    method: 'get'
  })
}

// 根据订单ID获取验货记录
export const getVerificationByOrderId = (orderId) => {
  return request({
    url: `/verifications/order/${orderId}`,
    method: 'get'
  })
}

// 获取验货统计数据
export const getVerificationStatistics = () => {
  return request({
    url: '/verifications/admin/statistics',
    method: 'get'
  })
}

// 批量处理验货
export const batchHandleVerifications = (data) => {
  return request({
    url: '/verifications/admin/batch',
    method: 'post',
    data
  })
}

// 上传验货图片
export const uploadVerificationImages = (formData) => {
  return request({
    url: '/verifications/upload/images',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出验货数据
export const exportVerificationData = (params) => {
  return request({
    url: '/verifications/admin/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
