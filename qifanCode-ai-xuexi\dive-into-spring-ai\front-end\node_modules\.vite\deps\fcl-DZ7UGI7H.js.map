{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/fcl.js"], "sourcesContent": ["var keywords = {\n  \"term\": true,\n  \"method\": true, \"accu\": true,\n  \"rule\": true, \"then\": true, \"is\": true, \"and\": true, \"or\": true,\n  \"if\": true, \"default\": true\n};\n\nvar start_blocks = {\n  \"var_input\": true,\n  \"var_output\": true,\n  \"fuzzify\": true,\n  \"defuzzify\": true,\n  \"function_block\": true,\n  \"ruleblock\": true\n};\n\nvar end_blocks = {\n  \"end_ruleblock\": true,\n  \"end_defuzzify\": true,\n  \"end_function_block\": true,\n  \"end_fuzzify\": true,\n  \"end_var\": true\n};\n\nvar atoms = {\n  \"true\": true, \"false\": true, \"nan\": true,\n  \"real\": true, \"min\": true, \"max\": true, \"cog\": true, \"cogs\": true\n};\n\nvar isOperatorChar = /[+\\-*&^%:=<>!|\\/]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (/[\\d\\.]/.test(ch)) {\n    if (ch == \".\") {\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    } else if (ch == \"0\") {\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    } else {\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    }\n    return \"number\";\n  }\n\n  if (ch == \"/\" || ch == \"(\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n\n  var cur = stream.current().toLowerCase();\n  if (keywords.propertyIsEnumerable(cur) ||\n      start_blocks.propertyIsEnumerable(cur) ||\n      end_blocks.propertyIsEnumerable(cur)) {\n    return \"keyword\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if ((ch == \"/\" || ch == \")\") && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\n\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\n\nfunction popContext(state) {\n  if (!state.context.prev) return;\n  var t = state.context.type;\n  if (t == \"end_block\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const fcl = {\n  name: \"fcl\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    var cur = stream.current().toLowerCase();\n\n    if (start_blocks.propertyIsEnumerable(cur)) pushContext(state, stream.column(), \"end_block\");\n    else if (end_blocks.propertyIsEnumerable(cur))  popContext(state);\n\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return 0;\n    var ctx = state.context;\n\n    var closing = end_blocks.propertyIsEnumerable(textAfter);\n    if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"(*\", close: \"*)\"}}\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,UAAU;AAAA,EAAM,QAAQ;AAAA,EACxB,QAAQ;AAAA,EAAM,QAAQ;AAAA,EAAM,MAAM;AAAA,EAAM,OAAO;AAAA,EAAM,MAAM;AAAA,EAC3D,MAAM;AAAA,EAAM,WAAW;AACzB;AAEA,IAAI,eAAe;AAAA,EACjB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,aAAa;AACf;AAEA,IAAI,aAAa;AAAA,EACf,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,WAAW;AACb;AAEA,IAAI,QAAQ;AAAA,EACV,QAAQ;AAAA,EAAM,SAAS;AAAA,EAAM,OAAO;AAAA,EACpC,QAAQ;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAAA,EAAM,QAAQ;AAC/D;AAEA,IAAI,iBAAiB;AAErB,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,SAAS,KAAK,EAAE,GAAG;AACrB,QAAI,MAAM,KAAK;AACb,aAAO,MAAM,4BAA4B;AAAA,IAC3C,WAAW,MAAM,KAAK;AACpB,aAAO,MAAM,mBAAmB,KAAK,OAAO,MAAM,UAAU;AAAA,IAC9D,OAAO;AACL,aAAO,MAAM,qCAAqC;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,oBAAoB;AAEpC,MAAI,MAAM,OAAO,QAAQ,EAAE,YAAY;AACvC,MAAI,SAAS,qBAAqB,GAAG,KACjC,aAAa,qBAAqB,GAAG,KACrC,WAAW,qBAAqB,GAAG,GAAG;AACxC,WAAO;AAAA,EACT;AACA,MAAI,MAAM,qBAAqB,GAAG;AAAG,WAAO;AAC5C,SAAO;AACT;AAGA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,SAAK,MAAM,OAAO,MAAM,QAAQ,UAAU;AACxC,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,OAAO;AACd;AAEA,SAAS,YAAY,OAAO,KAAK,MAAM;AACrC,SAAO,MAAM,UAAU,IAAI,QAAQ,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AAEA,SAAS,WAAW,OAAO;AACzB,MAAI,CAAC,MAAM,QAAQ;AAAM;AACzB,MAAI,IAAI,MAAM,QAAQ;AACtB,MAAI,KAAK;AACP,UAAM,WAAW,MAAM,QAAQ;AACjC,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AAIO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,KAAK;AAAA,MACjD,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,IAAI,SAAS;AAAM,YAAI,QAAQ;AACnC,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,cAAc;AAAA,IACtB;AACA,QAAI,OAAO,SAAS;AAAG,aAAO;AAE9B,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS;AAAW,aAAO;AAC/B,QAAI,IAAI,SAAS;AAAM,UAAI,QAAQ;AAEnC,QAAI,MAAM,OAAO,QAAQ,EAAE,YAAY;AAEvC,QAAI,aAAa,qBAAqB,GAAG;AAAG,kBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AAAA,aAClF,WAAW,qBAAqB,GAAG;AAAI,iBAAW,KAAK;AAEhE,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,YAAY,aAAa,MAAM,YAAY;AAAM,aAAO;AAClE,QAAI,MAAM,MAAM;AAEhB,QAAI,UAAU,WAAW,qBAAqB,SAAS;AACvD,QAAI,IAAI;AAAO,aAAO,IAAI,UAAU,UAAU,IAAI;AAAA;AAC7C,aAAO,IAAI,YAAY,UAAU,IAAI,GAAG;AAAA,EAC/C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": []}