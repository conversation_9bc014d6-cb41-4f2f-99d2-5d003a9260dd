# 商品助手Agent使用指南

## 🎉 功能概述

商品助手Agent是商城平台的智能助手，能够理解用户的自然语言需求，并调用相应的工具来提供准确的商品信息和建议。

## 🚀 已实现功能

### 1. 智能商品搜索
- **功能**: 根据关键词、分类、价格范围等条件搜索商品
- **示例**: "搜索价格在2000-4000的iPhone手机"
- **支持参数**: 关键词、分类ID、价格范围、新旧程度、地区等

### 2. 商品详情查询
- **功能**: 获取指定商品的详细信息
- **示例**: "查看商品ID 1的详细信息"
- **返回信息**: 标题、价格、描述、分类、新旧程度、地区、卖家等

### 3. 商品比较分析
- **功能**: 比较多个商品的特性和价格
- **示例**: "比较商品ID 1,2,3的性价比"
- **支持**: 最多同时比较5个商品

### 4. 相似商品推荐
- **功能**: 基于商品特征推荐相似商品
- **示例**: "推荐与商品1相似的商品"
- **支持**: 可指定推荐数量（默认5个，最多10个）

### 5. 分类信息查询
- **功能**: 获取商品分类的树形结构
- **示例**: "显示所有商品分类"
- **返回**: 完整的分类层级结构

## 🛠️ 技术架构

```
用户输入 → AI模型 → 意图识别 → Agent选择 → 工具调用 → 结果整合 → 格式化输出
```

### 核心组件

1. **@Agent注解**: 标识Agent组件
2. **AbstractAgent**: 提供Agent基础功能
3. **ProductAssistant**: 商品助手具体实现
4. **Function Calling**: Spring AI的工具调用机制

### 集成方式

```java
// 在AiChatRequest中启用Agent
{
  "content": "用户消息",
  "params": {
    "enableAgent": true,     // 启用Agent功能
    "enableMemory": true,    // 启用记忆功能
    "historyMessageCount": 10
  }
}
```

## 📝 API接口

### 1. 聊天接口（推荐）
```http
POST /api/ai/chat/stream
Content-Type: application/json
Authorization: Bearer {token}

{
  "content": "搜索iPhone手机",
  "params": {
    "enableAgent": true,
    "enableMemory": true
  }
}
```

### 2. 测试接口
```http
# 测试商品搜索
GET /api/agent/test/product/search?keyword=iPhone&categoryId=1&minPrice=1000&maxPrice=5000

# 测试商品详情
GET /api/agent/test/product/{productId}

# 测试商品比较
GET /api/agent/test/product/compare?productIds=1,2,3

# 测试商品推荐
GET /api/agent/test/product/recommend/{productId}?limit=5

# 测试分类列表
GET /api/agent/test/categories
```

## 💬 对话示例

### 商品搜索
```
用户: "我想找一台二手MacBook，价格在5000-8000之间"
Agent: 🔍 找到 8 个相关商品（第1页，共1页）：

📦 MacBook Air M1 256GB
💰 价格：¥6,800.00
📍 地区：北京
🏷️ 分类：电脑办公
📅 发布时间：2024-01-15T10:30:00
🔗 商品ID：2001

📦 MacBook Pro 13寸 512GB
💰 价格：¥7,500.00
📍 地区：上海
🏷️ 分类：电脑办公
📅 发布时间：2024-01-14T15:20:00
🔗 商品ID：2002
```

### 商品详情
```
用户: "详细介绍一下商品2001"
Agent: 📦 商品详情
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🏷️ 标题：MacBook Air M1 256GB
💰 价格：¥6,800.00
📝 描述：九成新MacBook Air，M1芯片，性能优秀
🏷️ 分类：电脑办公
🔧 新旧程度：九成新
📍 地区：北京
🚚 配送方式：快递配送
👤 卖家：李四
📅 发布时间：2024-01-15T10:30:00
🔗 商品ID：2001
🖼️ 图片数量：3张
```

### 商品比较
```
用户: "比较一下商品2001和2002"
Agent: ⚖️ 商品比较结果：

📦 MacBook Air M1 256GB | 💰 ¥6,800.00 | 🔧 九成新 | 📍 北京 | 🔗 ID:2001
📦 MacBook Pro 13寸 512GB | 💰 ¥7,500.00 | 🔧 八成新 | 📍 上海 | 🔗 ID:2002

💡 建议：MacBook Air M1性价比更高，适合日常办公；MacBook Pro性能更强，适合专业用户。
```

## 🎯 使用场景

1. **商品发现**: "帮我找一台游戏笔记本"
2. **价格筛选**: "1000-3000元的手机有哪些"
3. **商品对比**: "这几个商品哪个更值得买"
4. **购买咨询**: "这个商品怎么样，值得买吗"
5. **相似推荐**: "还有类似的商品吗"

## 🔧 开发扩展

### 添加新的工具函数
```java
@Description("获取商品评价信息")
public String getProductReviews(
    @JsonPropertyDescription("商品ID") Long productId,
    @JsonPropertyDescription("评价数量限制") Integer limit
) {
    // 实现获取商品评价的逻辑
    return "评价信息";
}
```

### 自定义系统提示词
```java
private static final String CUSTOM_PROMPT = """
    你是一个专业的商品顾问，具有以下特点：
    1. 对商品有深入的了解
    2. 能够提供客观的购买建议
    3. 关注用户的实际需求
    4. 提供性价比分析
    """;
```

## 🚀 性能优化

1. **缓存机制**: 对频繁查询的商品信息进行缓存
2. **异步处理**: 大量数据查询使用异步处理
3. **分页控制**: 搜索结果支持分页，避免一次返回过多数据
4. **错误处理**: 完善的异常处理和降级机制

## 📊 监控指标

- Agent调用次数
- 工具函数使用频率
- 响应时间
- 用户满意度
- 错误率

## 🔮 未来规划

1. **多模态支持**: 支持图片识别和语音交互
2. **个性化推荐**: 基于用户历史行为的个性化推荐
3. **实时价格监控**: 价格变化提醒和趋势分析
4. **智能客服**: 集成订单助手和客服助手
5. **数据分析**: 用户行为分析和商品热度统计

## 🎉 总结

商品助手Agent为商城平台带来了智能化的用户体验，通过自然语言交互，用户可以更便捷地找到心仪的商品。Agent的模块化设计使得功能扩展变得简单，为未来的智能化升级奠定了基础。

立即体验商品助手Agent，让AI为您的购物之旅保驾护航！🛒✨
