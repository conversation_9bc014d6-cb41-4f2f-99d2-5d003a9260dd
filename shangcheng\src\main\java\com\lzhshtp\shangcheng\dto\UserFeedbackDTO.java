package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户反馈/举报数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackDTO {
    
    /**
     * 反馈记录唯一标识ID
     */
    private Long feedbackId;
    
    /**
     * 提交反馈的用户ID
     */
    private Long reporterId;
    
    /**
     * 提交反馈的用户名
     */
    private String reporterName;
    
    /**
     * 反馈类型: bug_report(问题反馈), suggestion(建议), complaint(投诉), abuse_report(举报)
     */
    private String feedbackType;
    
    /**
     * 反馈类型的中文描述
     */
    private String feedbackTypeDesc;
    
    /**
     * 如果是举报，则指明举报对象类型（例如 'product', 'user', 'post'）
     */
    private String relatedEntityType;
    
    /**
     * 关联的实体ID（例如：举报商品则为product_id）
     */
    private Long relatedEntityId;
    
    /**
     * 关联实体的名称或标题（如商品名称、用户名等）
     */
    private String relatedEntityName;
    
    /**
     * 反馈或举报的详细内容
     */
    private String content;
    
    /**
     * 提交时间
     */
    private LocalDateTime submittedAt;
    
    /**
     * 处理状态: pending(待处理), in_progress(处理中), resolved(已解决), rejected(已拒绝)
     */
    private String status;
    
    /**
     * 状态的中文描述
     */
    private String statusDesc;
    
    /**
     * 管理员处理时的备注
     */
    private String adminNotes;
    
    /**
     * 处理该反馈的管理员用户ID
     */
    private Long resolvedByAdminId;
    
    /**
     * 处理该反馈的管理员用户名
     */
    private String resolvedByAdminName;
} 