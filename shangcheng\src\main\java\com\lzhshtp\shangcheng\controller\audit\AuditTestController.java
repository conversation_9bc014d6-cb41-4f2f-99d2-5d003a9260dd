package com.lzhshtp.shangcheng.controller.audit;



import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.SensitiveWordMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.SensitiveWord;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.audit.ProductAuditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审核功能测试控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/audit/test")
@Tag(name = "审核测试", description = "审核功能测试接口")
public class AuditTestController {

    @Autowired
    private ProductAuditService productAuditService;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    @Autowired
    private com.lzhshtp.shangcheng.service.audit.AliyunImageAuditService aliyunImageAuditServiceV2;

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查审核测试服务是否正常")
    public ApiResponse<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "healthy");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "审核测试服务运行正常");
        return ApiResponse.success("服务健康", result);
    }



    /**
     * 创建测试商品
     */
    @PostMapping("/create-test-product")
    @Operation(summary = "创建测试商品", description = "创建用于测试审核功能的商品")
    public ApiResponse<Map<String, Object>> createTestProduct(
            @RequestParam(defaultValue = "正常商品") String title,
            @RequestParam(defaultValue = "这是一个正常的商品描述") String description,
            @RequestParam(defaultValue = "100.00") String price,
            @RequestParam(defaultValue = "2") Long sellerId) {

        try {
            // 检查用户是否存在
            User seller = userMapper.selectById(sellerId);
            if (seller == null) {
                return ApiResponse.fail("卖家用户不存在，请先创建用户");
            }

            // 创建测试商品
            Product product = new Product();
            product.setTitle(title);
            product.setDescription(description);
            product.setPrice(new BigDecimal(price));
            product.setCategoryId(2);
            product.setCondition("全新");
            product.setLocation("测试地址");
            product.setDeliveryMethod("快递");
            product.setImageUrls("test-image.jpg");
            product.setPostedDate(LocalDateTime.now());
            product.setStatus("pending_review");
            product.setSellerId(sellerId);

            productMapper.insert(product);

            Map<String, Object> result = new HashMap<>();
            result.put("productId", product.getId());
            result.put("title", product.getTitle());
            result.put("description", product.getDescription());
            result.put("price", product.getPrice());
            result.put("sellerId", product.getSellerId());

            return ApiResponse.success("测试商品创建成功", result);

        } catch (Exception e) {
            log.error("创建测试商品失败", e);
            return ApiResponse.fail("创建测试商品失败：" + e.getMessage());
        }
    }

    /**
     * 测试自动审核
     */
    @PostMapping("/auto-audit/{productId}")
    @Operation(summary = "测试自动审核", description = "对指定商品执行自动审核")
    public ApiResponse<String> testAutoAudit(@PathVariable Long productId) {
        try {
            productAuditService.submitProductForAudit(productId);
            return ApiResponse.success("自动审核已执行，请查看商品状态和审核记录");
        } catch (Exception e) {
            log.error("测试自动审核失败", e);
            return ApiResponse.fail("测试自动审核失败：" + e.getMessage());
        }
    }

    /**
     * 创建包含敏感词的测试商品
     */
    @PostMapping("/create-sensitive-product")
    @Operation(summary = "创建敏感词商品", description = "创建包含敏感词的测试商品")
    public ApiResponse<Map<String, Object>> createSensitiveProduct(
            @RequestParam(defaultValue = "1") Long sellerId) {

        try {
            // 创建包含敏感词的商品
            Product product = new Product();
            product.setTitle("高仿iPhone手机");  // 包含敏感词"高仿"和品牌词"iPhone"
            product.setDescription("这是一个高仿的iPhone，质量很好，A货");  // 包含多个敏感词
            product.setPrice(new BigDecimal("500.00"));
            product.setCategoryId(1);
            product.setCondition("全新");
            product.setLocation("测试地址");
            product.setDeliveryMethod("快递");
            product.setImageUrls("test-image.jpg");
            product.setPostedDate(LocalDateTime.now());
            product.setStatus("pending_review");
            product.setSellerId(sellerId);

            productMapper.insert(product);

            Map<String, Object> result = new HashMap<>();
            result.put("productId", product.getId());
            result.put("title", product.getTitle());
            result.put("description", product.getDescription());
            result.put("message", "已创建包含敏感词的测试商品，可用于测试文字审核功能");

            return ApiResponse.success("敏感词测试商品创建成功", result);

        } catch (Exception e) {
            log.error("创建敏感词商品失败", e);
            return ApiResponse.fail("创建敏感词商品失败：" + e.getMessage());
        }
    }

    /**
     * 创建违禁词测试商品
     */
    @PostMapping("/create-banned-product")
    @Operation(summary = "创建违禁词商品", description = "创建包含违禁词的测试商品")
    public ApiResponse<Map<String, Object>> createBannedProduct(
            @RequestParam(defaultValue = "1") Long sellerId) {

        try {
            // 创建包含违禁词的商品
            Product product = new Product();
            product.setTitle("特殊商品");
            product.setDescription("这是一个包含枪支的商品描述");  // 包含违禁词"枪支"
            product.setPrice(new BigDecimal("1000.00"));
            product.setCategoryId(1);
            product.setCondition("全新");
            product.setLocation("测试地址");
            product.setDeliveryMethod("快递");
            product.setImageUrls("test-image.jpg");
            product.setPostedDate(LocalDateTime.now());
            product.setStatus("pending_review");
            product.setSellerId(sellerId);

            productMapper.insert(product);

            Map<String, Object> result = new HashMap<>();
            result.put("productId", product.getId());
            result.put("title", product.getTitle());
            result.put("description", product.getDescription());
            result.put("message", "已创建包含违禁词的测试商品，应该会被自动拒绝");

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("创建违禁词商品失败", e);
            return ApiResponse.fail("创建违禁词商品失败：" + e.getMessage());
        }
    }

    /**
     * 查看敏感词库
     */
    @GetMapping("/sensitive-words")
    @Operation(summary = "查看敏感词库", description = "查看当前系统中的敏感词库")
    public ApiResponse<Map<String, Object>> getSensitiveWords() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("banned_words", sensitiveWordMapper.findByWordType("banned"));
            result.put("sensitive_words", sensitiveWordMapper.findByWordType("sensitive"));
            result.put("brand_words", sensitiveWordMapper.findByWordType("brand"));

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取敏感词库失败", e);
            return ApiResponse.fail("获取敏感词库失败：" + e.getMessage());
        }
    }

    /**
     * 添加敏感词
     */
    @PostMapping("/add-sensitive-word")
    @Operation(summary = "添加敏感词", description = "向敏感词库添加新的敏感词")
    public ApiResponse<Void> addSensitiveWord(
            @RequestParam String word,
            @RequestParam String wordType,
            @RequestParam(defaultValue = "5") Integer severityLevel,
            @RequestParam(defaultValue = "测试") String category) {

        try {
            SensitiveWord sensitiveWord = SensitiveWord.builder()
                .word(word)
                .wordType(wordType)
                .severityLevel(severityLevel)
                .category(category)
                .isActive(true)
                .createdTime(LocalDateTime.now())
                .createdBy(1L)
                .build();

            sensitiveWordMapper.insert(sensitiveWord);
            return ApiResponse.successVoid("敏感词添加成功");

        } catch (Exception e) {
            log.error("添加敏感词失败", e);
            return ApiResponse.fail("添加敏感词失败：" + e.getMessage());
        }
    }

    /**
     * 验证商品是否存在（用于测试删除功能）
     */
    @GetMapping("/verify-product/{productId}")
    @Operation(summary = "验证商品是否存在", description = "检查商品是否还在数据库中")
    public ApiResponse<Map<String, Object>> verifyProductExists(@PathVariable Long productId) {
        try {
            Product product = productMapper.selectById(productId);

            Map<String, Object> result = new HashMap<>();
            result.put("productId", productId);
            result.put("exists", product != null);

            if (product != null) {
                result.put("title", product.getTitle());
                result.put("status", product.getStatus());
                result.put("sellerId", product.getSellerId());
            }

            return ApiResponse.success("查询完成", result);

        } catch (Exception e) {
            log.error("验证商品存在性失败", e);
            return ApiResponse.fail("验证失败：" + e.getMessage());
        }
    }

    /**
     * 测试阿里云图片审核
     */
    @PostMapping("/image-audit")
    @Operation(summary = "测试图片审核", description = "测试阿里云图片审核功能")
    public ApiResponse<Map<String, Object>> testImageAudit(
            @RequestParam String imageUrl) {

        try {
            log.info("开始测试图片审核，图片URL: {}", imageUrl);

            // 调用阿里云图片审核服务V2
            com.lzhshtp.shangcheng.dto.audit.AuditResultDTO auditResult =
                aliyunImageAuditServiceV2.auditSingleImage(imageUrl);

            Map<String, Object> result = new HashMap<>();
            result.put("imageUrl", imageUrl);
            result.put("passed", auditResult.getPassed());
            result.put("score", auditResult.getScore());
            result.put("reason", auditResult.getReason());
            result.put("riskLevel", auditResult.getRiskLevel());
            result.put("details", auditResult.getDetails());
            result.put("riskFactors", auditResult.getRiskFactors());

            log.info("图片审核完成，结果: {}", auditResult.getPassed() ? "通过" : "不通过");

            return ApiResponse.success("图片审核完成", result);

        } catch (Exception e) {
            log.error("图片审核测试失败", e);
            return ApiResponse.fail("图片审核测试失败：" + e.getMessage());
        }
    }

    /**
     * 检查阿里云配置
     */
    @GetMapping("/aliyun-config")
    @Operation(summary = "检查阿里云配置", description = "检查阿里云内容安全服务配置状态")
    public ApiResponse<Map<String, Object>> checkAliyunConfig() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 检查服务是否可用
            boolean serviceAvailable = false;
            String configStatus = "未配置";
            String errorDetails = null;

            try {
                // 尝试调用一个简单的测试
                String testImageUrl = "https://via.placeholder.com/100x100.jpg";
                com.lzhshtp.shangcheng.dto.audit.AuditResultDTO testResult =
                    aliyunImageAuditServiceV2.auditSingleImage(testImageUrl);

                serviceAvailable = true;
                configStatus = "已配置且可用";

            } catch (Exception e) {
                serviceAvailable = false;
                errorDetails = e.getMessage();

                // 分析具体错误原因
                if (errorDetails.contains("you haven't activated the commodity")) {
                    configStatus = "❌ 服务未开通：请在阿里云控制台开通内容安全服务";
                } else if (errorDetails.contains("parameter invalid(service)")) {
                    configStatus = "❌ 服务参数无效：使用的服务名称不被支持，请检查服务开通状态";
                } else if (errorDetails.contains("InvalidAccessKeyId")) {
                    configStatus = "❌ AccessKey ID 无效";
                } else if (errorDetails.contains("SignatureDoesNotMatch")) {
                    configStatus = "❌ AccessKey Secret 错误";
                } else if (errorDetails.contains("401")) {
                    configStatus = "❌ 认证失败：请检查AccessKey配置和服务开通状态";
                } else if (errorDetails.contains("403")) {
                    configStatus = "❌ 权限不足：请检查AccessKey权限";
                } else if (errorDetails.contains("408")) {
                    configStatus = "❌ 请求超时：网络连接问题";
                } else {
                    configStatus = "❌ 配置错误：" + errorDetails;
                }
            }

            result.put("serviceAvailable", serviceAvailable);
            result.put("configStatus", configStatus);
            result.put("serviceType", aliyunImageAuditServiceV2.getClass().getSimpleName());
            result.put("checkTime", LocalDateTime.now());

            if (errorDetails != null) {
                result.put("errorDetails", errorDetails);
            }

            // 添加解决方案建议
            if (!serviceAvailable) {
                List<String> solutions = new ArrayList<>();
                if (errorDetails.contains("you haven't activated the commodity")) {
                    solutions.add("1. 访问 https://yundun.console.aliyun.com/ 开通内容安全服务");
                    solutions.add("2. 购买内容安全资源包或开启按量付费");
                    solutions.add("3. 确保账户有足够余额");
                } else if (errorDetails.contains("AccessKey")) {
                    solutions.add("1. 检查 application.yml 中的 AccessKey 配置");
                    solutions.add("2. 确认 AccessKey 是否正确且有效");
                    solutions.add("3. 在RAM控制台重新生成 AccessKey");
                }
                result.put("solutions", solutions);
            }

            return ApiResponse.success("配置检查完成", result);

        } catch (Exception e) {
            log.error("检查阿里云配置失败", e);
            return ApiResponse.fail("检查阿里云配置失败：" + e.getMessage());
        }
    }

    /**
     * 网络连通性测试
     */
    @GetMapping("/network-test")
    @Operation(summary = "网络连通性测试", description = "测试到阿里云和目标图片的网络连通性")
    public ApiResponse<Map<String, Object>> testNetworkConnectivity() {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> tests = new ArrayList<>();

            // 测试1: 阿里云API连通性
            Map<String, Object> aliyunTest = new HashMap<>();
            aliyunTest.put("name", "阿里云API连通性");
            try {
                // 这里可以添加实际的阿里云API连通性测试
                // 例如调用一个简单的API来验证连接
                boolean connected = aliyunImageAuditServiceV2.isServiceAvailable();
                aliyunTest.put("success", connected);
                aliyunTest.put("message", connected ? "连接正常" : "连接失败");
            } catch (Exception e) {
                aliyunTest.put("success", false);
                aliyunTest.put("message", "连接异常: " + e.getMessage());
            }
            tests.add(aliyunTest);

            // 测试2: 测试图片URL可访问性
            Map<String, Object> imageTest = new HashMap<>();
            imageTest.put("name", "测试图片URL可访问性");
            try {
                String testImageUrl = "https://via.placeholder.com/300x200.jpg";
                // 这里可以添加HTTP请求测试图片是否可访问
                imageTest.put("success", true);
                imageTest.put("message", "测试图片可访问");
                imageTest.put("testUrl", testImageUrl);
            } catch (Exception e) {
                imageTest.put("success", false);
                imageTest.put("message", "图片访问失败: " + e.getMessage());
            }
            tests.add(imageTest);

            // 测试3: DNS解析测试
            Map<String, Object> dnsTest = new HashMap<>();
            dnsTest.put("name", "DNS解析测试");
            try {
                java.net.InetAddress.getByName("green.cn-shanghai.aliyuncs.com");
                dnsTest.put("success", true);
                dnsTest.put("message", "DNS解析正常");
            } catch (Exception e) {
                dnsTest.put("success", false);
                dnsTest.put("message", "DNS解析失败: " + e.getMessage());
            }
            tests.add(dnsTest);

            result.put("tests", tests);
            result.put("testTime", LocalDateTime.now());

            return ApiResponse.success("网络连通性测试完成", result);

        } catch (Exception e) {
            log.error("网络连通性测试失败", e);
            return ApiResponse.fail("网络连通性测试失败：" + e.getMessage());
        }
    }

    /**
     * 阿里云配置验证和设置指南
     */
    @GetMapping("/aliyun-setup-guide")
    @Operation(summary = "阿里云配置指南", description = "获取阿里云内容安全配置指南和当前配置状态")
    public ApiResponse<Map<String, Object>> getAliyunSetupGuide() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 当前配置状态
            Map<String, Object> currentConfig = new HashMap<>();
            currentConfig.put("accessKeyId", "LTAI5t81d8QLMMhMFfD3W2FN".substring(0, 8) + "****");
            currentConfig.put("endpoint", "green.cn-shanghai.aliyuncs.com");
            currentConfig.put("regionId", "cn-shanghai");

            // 配置步骤
            List<Map<String, Object>> steps = new ArrayList<>();

            Map<String, Object> step1 = new HashMap<>();
            step1.put("step", 1);
            step1.put("title", "获取AccessKey");
            step1.put("description", "登录阿里云控制台 → 右上角头像 → AccessKey管理 → 创建AccessKey");
            step1.put("url", "https://ram.console.aliyun.com/users");
            steps.add(step1);

            Map<String, Object> step2 = new HashMap<>();
            step2.put("step", 2);
            step2.put("title", "开通内容安全服务");
            step2.put("description", "访问内容安全控制台，开通服务并确保有足够余额");
            step2.put("url", "https://yundun.console.aliyun.com/");
            steps.add(step2);

            Map<String, Object> step3 = new HashMap<>();
            step3.put("step", 3);
            step3.put("title", "配置权限");
            step3.put("description", "在RAM控制台为AccessKey添加 AliyunYundunGreenWebFullAccess 权限");
            step3.put("url", "https://ram.console.aliyun.com/policies");
            steps.add(step3);

            Map<String, Object> step4 = new HashMap<>();
            step4.put("step", 4);
            step4.put("title", "修改配置文件");
            step4.put("description", "在 application.yml 中配置你的 AccessKey ID 和 Secret");
            step4.put("configPath", "shangcheng/src/main/resources/application.yml");
            steps.add(step4);

            // 可用的endpoint
            List<Map<String, String>> endpoints = new ArrayList<>();
            endpoints.add(Map.of("region", "华东1（杭州）", "endpoint", "green.cn-hangzhou.aliyuncs.com"));
            endpoints.add(Map.of("region", "华东2（上海）", "endpoint", "green.cn-shanghai.aliyuncs.com"));
            endpoints.add(Map.of("region", "华北2（北京）", "endpoint", "green.cn-beijing.aliyuncs.com"));
            endpoints.add(Map.of("region", "华南1（深圳）", "endpoint", "green.cn-shenzhen.aliyuncs.com"));

            result.put("currentConfig", currentConfig);
            result.put("setupSteps", steps);
            result.put("availableEndpoints", endpoints);
            result.put("configExample", getConfigExample());

            return ApiResponse.success("配置指南获取成功", result);

        } catch (Exception e) {
            log.error("获取配置指南失败", e);
            return ApiResponse.fail("获取配置指南失败：" + e.getMessage());
        }
    }

    /**
     * 阿里云权限和服务状态详细检查
     */
    @GetMapping("/aliyun-detailed-check")
    @Operation(summary = "阿里云详细检查", description = "详细检查阿里云配置、权限和服务状态")
    public ApiResponse<Map<String, Object>> detailedAliyunCheck() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 配置检查
            Map<String, Object> configCheck = new HashMap<>();
            configCheck.put("accessKeyId", "LTAI5tLz2GedqiZbCDzYAjZ7".substring(0, 8) + "****");
            configCheck.put("endpoint", "green.cn-shanghai.aliyuncs.com");
            configCheck.put("regionId", "cn-shanghai");

            // 2. 服务开通状态检查
            Map<String, Object> serviceCheck = new HashMap<>();
            serviceCheck.put("requiredService", "lvwang_cip_public_cn");
            serviceCheck.put("description", "阿里云内容安全-图片审核服务");
            serviceCheck.put("consoleUrl", "https://yundun.console.aliyun.com/");

            // 3. 权限检查建议
            List<Map<String, String>> permissionSuggestions = new ArrayList<>();

            Map<String, String> perm1 = new HashMap<>();
            perm1.put("permission", "AliyunYundunGreenWebFullAccess");
            perm1.put("description", "内容安全完整权限");
            perm1.put("required", "是");
            permissionSuggestions.add(perm1);

            Map<String, String> perm2 = new HashMap<>();
            perm2.put("permission", "AliyunYundunGreenWebReadOnlyAccess");
            perm2.put("description", "内容安全只读权限");
            perm2.put("required", "否（测试用）");
            permissionSuggestions.add(perm2);

            // 4. 解决步骤
            List<String> solutionSteps = new ArrayList<>();
            solutionSteps.add("1. 访问 https://yundun.console.aliyun.com/ 确认内容安全服务已开通");
            solutionSteps.add("2. 在内容安全控制台中，确认图片审核功能已激活");
            solutionSteps.add("3. 访问 https://ram.console.aliyun.com/ 检查AccessKey权限");
            solutionSteps.add("4. 为AccessKey添加 AliyunYundunGreenWebFullAccess 权限");
            solutionSteps.add("5. 确认账户余额充足或已购买资源包");
            solutionSteps.add("6. 等待5-10分钟让权限生效");

            // 5. 常见错误码说明
            Map<String, String> errorCodes = new HashMap<>();
            errorCodes.put("lvwang_cip_public_cn", "图片审核服务未开通");
            errorCodes.put("403", "AccessKey权限不足");
            errorCodes.put("400", "请求参数错误");
            errorCodes.put("408", "图片下载超时");

            result.put("configCheck", configCheck);
            result.put("serviceCheck", serviceCheck);
            result.put("permissionSuggestions", permissionSuggestions);
            result.put("solutionSteps", solutionSteps);
            result.put("errorCodes", errorCodes);
            result.put("checkTime", LocalDateTime.now());

            return ApiResponse.success("详细检查完成", result);

        } catch (Exception e) {
            log.error("阿里云详细检查失败", e);
            return ApiResponse.fail("详细检查失败：" + e.getMessage());
        }
    }

    private Map<String, String> getConfigExample() {
        Map<String, String> example = new HashMap<>();
        example.put("yml", """
            aliyun:
              content-security:
                access-key-id: 你的AccessKey_ID
                access-key-secret: 你的AccessKey_Secret
                endpoint: green.cn-shanghai.aliyuncs.com
                region-id: cn-shanghai
            """);
        example.put("env", """
            # 环境变量方式（推荐）
            ALIYUN_CONTENT_SECURITY_ACCESS_KEY_ID=你的AccessKey_ID
            ALIYUN_CONTENT_SECURITY_ACCESS_KEY_SECRET=你的AccessKey_Secret
            """);
        return example;
    }
}
