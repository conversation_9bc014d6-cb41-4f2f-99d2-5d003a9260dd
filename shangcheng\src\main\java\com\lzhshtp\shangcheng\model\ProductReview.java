package com.lzhshtp.shangcheng.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品评价实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductReview {
    
    /**
     * 商品评价唯一标识ID
     */
    private Long lzhshtpReviewId;
    
    /**
     * 被评价的商品ID
     */
    private Long lzhshtpProductId;
    
    /**
     * 发布评价的用户ID
     */
    private Long lzhshtpReviewerId;
    
    /**
     * 被评价商品的卖家ID
     */
    private Long lzhshtpSellerId;
    
    /**
     * 评分（例如：1-5星）
     */
    private Integer lzhshtpRating;
    
    /**
     * 评价内容
     */
    private String lzhshtpComment;
    
    /**
     * 评价发布时间
     */
    private LocalDateTime lzhshtpReviewDate;
} 