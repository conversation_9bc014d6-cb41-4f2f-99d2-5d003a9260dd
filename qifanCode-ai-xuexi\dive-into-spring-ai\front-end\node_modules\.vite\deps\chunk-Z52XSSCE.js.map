{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/javascript.js"], "sourcesContent": ["function mkJavaScript(parserConfig) {\n  var statementIndent = parserConfig.statementIndent;\n  var jsonldMode = parserConfig.jsonld;\n  var jsonMode = parserConfig.json || jsonldMode;\n  var isTS = parserConfig.typescript;\n  var wordRE = parserConfig.wordCharacters || /[\\w$\\xa1-\\uffff]/;\n\n  // Tokenizer\n\n  var keywords = function(){\n    function kw(type) {return {type: type, style: \"keyword\"};}\n    var A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\"), D = kw(\"keyword d\");\n    var operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"};\n\n    return {\n      \"if\": kw(\"if\"), \"while\": A, \"with\": A, \"else\": B, \"do\": B, \"try\": B, \"finally\": B,\n      \"return\": D, \"break\": D, \"continue\": D, \"new\": kw(\"new\"), \"delete\": C, \"void\": C, \"throw\": C,\n      \"debugger\": kw(\"debugger\"), \"var\": kw(\"var\"), \"const\": kw(\"var\"), \"let\": kw(\"var\"),\n      \"function\": kw(\"function\"), \"catch\": kw(\"catch\"),\n      \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n      \"in\": operator, \"typeof\": operator, \"instanceof\": operator,\n      \"true\": atom, \"false\": atom, \"null\": atom, \"undefined\": atom, \"NaN\": atom, \"Infinity\": atom,\n      \"this\": kw(\"this\"), \"class\": kw(\"class\"), \"super\": kw(\"atom\"),\n      \"yield\": C, \"export\": kw(\"export\"), \"import\": kw(\"import\"), \"extends\": C,\n      \"await\": C\n    };\n  }();\n\n  var isOperatorChar = /[+\\-*&%=<>!?|~^@]/;\n  var isJsonldKeyword = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)\"/;\n\n  function readRegexp(stream) {\n    var escaped = false, next, inSet = false;\n    while ((next = stream.next()) != null) {\n      if (!escaped) {\n        if (next == \"/\" && !inSet) return;\n        if (next == \"[\") inSet = true;\n        else if (inSet && next == \"]\") inSet = false;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n  }\n\n  // Used as scratch variables to communicate multiple values without\n  // consing up tons of objects.\n  var type, content;\n  function ret(tp, style, cont) {\n    type = tp; content = cont;\n    return style;\n  }\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\" && stream.match(/^\\d[\\d_]*(?:[eE][+\\-]?[\\d_]+)?/)) {\n      return ret(\"number\", \"number\");\n    } else if (ch == \".\" && stream.match(\"..\")) {\n      return ret(\"spread\", \"meta\");\n    } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n      return ret(ch);\n    } else if (ch == \"=\" && stream.eat(\">\")) {\n      return ret(\"=>\", \"operator\");\n    } else if (ch == \"0\" && stream.match(/^(?:x[\\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/)) {\n      return ret(\"number\", \"number\");\n    } else if (/\\d/.test(ch)) {\n      stream.match(/^[\\d_]*(?:n|(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)?/);\n      return ret(\"number\", \"number\");\n    } else if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      } else if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return ret(\"comment\", \"comment\");\n      } else if (expressionAllowed(stream, state, 1)) {\n        readRegexp(stream);\n        stream.match(/^\\b(([gimyus])(?![gimyus]*\\2))+\\b/);\n        return ret(\"regexp\", \"string.special\");\n      } else {\n        stream.eat(\"=\");\n        return ret(\"operator\", \"operator\", stream.current());\n      }\n    } else if (ch == \"`\") {\n      state.tokenize = tokenQuasi;\n      return tokenQuasi(stream, state);\n    } else if (ch == \"#\" && stream.peek() == \"!\") {\n      stream.skipToEnd();\n      return ret(\"meta\", \"meta\");\n    } else if (ch == \"#\" && stream.eatWhile(wordRE)) {\n      return ret(\"variable\", \"property\")\n    } else if (ch == \"<\" && stream.match(\"!--\") ||\n               (ch == \"-\" && stream.match(\"->\") && !/\\S/.test(stream.string.slice(0, stream.start)))) {\n      stream.skipToEnd()\n      return ret(\"comment\", \"comment\")\n    } else if (isOperatorChar.test(ch)) {\n      if (ch != \">\" || !state.lexical || state.lexical.type != \">\") {\n        if (stream.eat(\"=\")) {\n          if (ch == \"!\" || ch == \"=\") stream.eat(\"=\")\n        } else if (/[<>*+\\-|&?]/.test(ch)) {\n          stream.eat(ch)\n          if (ch == \">\") stream.eat(ch)\n        }\n      }\n      if (ch == \"?\" && stream.eat(\".\")) return ret(\".\")\n      return ret(\"operator\", \"operator\", stream.current());\n    } else if (wordRE.test(ch)) {\n      stream.eatWhile(wordRE);\n      var word = stream.current()\n      if (state.lastType != \".\") {\n        if (keywords.propertyIsEnumerable(word)) {\n          var kw = keywords[word]\n          return ret(kw.type, kw.style, word)\n        }\n        if (word == \"async\" && stream.match(/^(\\s|\\/\\*([^*]|\\*(?!\\/))*?\\*\\/)*[\\[\\(\\w]/, false))\n          return ret(\"async\", \"keyword\", word)\n      }\n      return ret(\"variable\", \"variable\", word)\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next;\n      if (jsonldMode && stream.peek() == \"@\" && stream.match(isJsonldKeyword)){\n        state.tokenize = tokenBase;\n        return ret(\"jsonld-keyword\", \"meta\");\n      }\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) break;\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (!escaped) state.tokenize = tokenBase;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return ret(\"comment\", \"comment\");\n  }\n\n  function tokenQuasi(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (!escaped && (next == \"`\" || next == \"$\" && stream.eat(\"{\"))) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    return ret(\"quasi\", \"string.special\", stream.current());\n  }\n\n  var brackets = \"([{}])\";\n  // This is a crude lookahead trick to try and notice that we're\n  // parsing the argument patterns for a fat-arrow function before we\n  // actually hit the arrow token. It only works if the arrow is on\n  // the same line as the arguments and there's no strange noise\n  // (comments) in between. Fallback is to only notice when we hit the\n  // arrow, and not declare the arguments as locals for the arrow\n  // body.\n  function findFatArrow(stream, state) {\n    if (state.fatArrowAt) state.fatArrowAt = null;\n    var arrow = stream.string.indexOf(\"=>\", stream.start);\n    if (arrow < 0) return;\n\n    if (isTS) { // Try to skip TypeScript return type declarations after the arguments\n      var m = /:\\s*(?:\\w+(?:<[^>]*>|\\[\\])?|\\{[^}]*\\})\\s*$/.exec(stream.string.slice(stream.start, arrow))\n      if (m) arrow = m.index\n    }\n\n    var depth = 0, sawSomething = false;\n    for (var pos = arrow - 1; pos >= 0; --pos) {\n      var ch = stream.string.charAt(pos);\n      var bracket = brackets.indexOf(ch);\n      if (bracket >= 0 && bracket < 3) {\n        if (!depth) { ++pos; break; }\n        if (--depth == 0) { if (ch == \"(\") sawSomething = true; break; }\n      } else if (bracket >= 3 && bracket < 6) {\n        ++depth;\n      } else if (wordRE.test(ch)) {\n        sawSomething = true;\n      } else if (/[\"'\\/`]/.test(ch)) {\n        for (;; --pos) {\n          if (pos == 0) return\n          var next = stream.string.charAt(pos - 1)\n          if (next == ch && stream.string.charAt(pos - 2) != \"\\\\\") { pos--; break }\n        }\n      } else if (sawSomething && !depth) {\n        ++pos;\n        break;\n      }\n    }\n    if (sawSomething && !depth) state.fatArrowAt = pos;\n  }\n\n  // Parser\n\n  var atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true,\n                     \"regexp\": true, \"this\": true, \"import\": true, \"jsonld-keyword\": true};\n\n  function JSLexical(indented, column, type, align, prev, info) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.prev = prev;\n    this.info = info;\n    if (align != null) this.align = align;\n  }\n\n  function inScope(state, varname) {\n    for (var v = state.localVars; v; v = v.next)\n      if (v.name == varname) return true;\n    for (var cx = state.context; cx; cx = cx.prev) {\n      for (var v = cx.vars; v; v = v.next)\n        if (v.name == varname) return true;\n    }\n  }\n\n  function parseJS(state, style, type, content, stream) {\n    var cc = state.cc;\n    // Communicate our context to the combinators.\n    // (Less wasteful than consing up a hundred closures on every call.)\n    cx.state = state; cx.stream = stream; cx.marked = null; cx.cc = cc; cx.style = style;\n\n    if (!state.lexical.hasOwnProperty(\"align\"))\n      state.lexical.align = true;\n\n    while(true) {\n      var combinator = cc.length ? cc.pop() : jsonMode ? expression : statement;\n      if (combinator(type, content)) {\n        while(cc.length && cc[cc.length - 1].lex)\n          cc.pop()();\n        if (cx.marked) return cx.marked;\n        if (type == \"variable\" && inScope(state, content)) return \"variableName.local\";\n        return style;\n      }\n    }\n  }\n\n  // Combinator utils\n\n  var cx = {state: null, column: null, marked: null, cc: null};\n  function pass() {\n    for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n  }\n  function cont() {\n    pass.apply(null, arguments);\n    return true;\n  }\n  function inList(name, list) {\n    for (var v = list; v; v = v.next) if (v.name == name) return true\n    return false;\n  }\n  function register(varname) {\n    var state = cx.state;\n    cx.marked = \"def\";\n    if (state.context) {\n      if (state.lexical.info == \"var\" && state.context && state.context.block) {\n        // FIXME function decls are also not block scoped\n        var newContext = registerVarScoped(varname, state.context)\n        if (newContext != null) {\n          state.context = newContext\n          return\n        }\n      } else if (!inList(varname, state.localVars)) {\n        state.localVars = new Var(varname, state.localVars)\n        return\n      }\n    }\n    // Fall through means this is global\n    if (parserConfig.globalVars && !inList(varname, state.globalVars))\n      state.globalVars = new Var(varname, state.globalVars)\n  }\n  function registerVarScoped(varname, context) {\n    if (!context) {\n      return null\n    } else if (context.block) {\n      var inner = registerVarScoped(varname, context.prev)\n      if (!inner) return null\n      if (inner == context.prev) return context\n      return new Context(inner, context.vars, true)\n    } else if (inList(varname, context.vars)) {\n      return context\n    } else {\n      return new Context(context.prev, new Var(varname, context.vars), false)\n    }\n  }\n\n  function isModifier(name) {\n    return name == \"public\" || name == \"private\" || name == \"protected\" || name == \"abstract\" || name == \"readonly\"\n  }\n\n  // Combinators\n\n  function Context(prev, vars, block) { this.prev = prev; this.vars = vars; this.block = block }\n  function Var(name, next) { this.name = name; this.next = next }\n\n  var defaultVars = new Var(\"this\", new Var(\"arguments\", null))\n  function pushcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, false)\n    cx.state.localVars = defaultVars\n  }\n  function pushblockcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, true)\n    cx.state.localVars = null\n  }\n  pushcontext.lex = pushblockcontext.lex = true\n  function popcontext() {\n    cx.state.localVars = cx.state.context.vars\n    cx.state.context = cx.state.context.prev\n  }\n  popcontext.lex = true\n  function pushlex(type, info) {\n    var result = function() {\n      var state = cx.state, indent = state.indented;\n      if (state.lexical.type == \"stat\") indent = state.lexical.indented;\n      else for (var outer = state.lexical; outer && outer.type == \")\" && outer.align; outer = outer.prev)\n        indent = outer.indented;\n      state.lexical = new JSLexical(indent, cx.stream.column(), type, null, state.lexical, info);\n    };\n    result.lex = true;\n    return result;\n  }\n  function poplex() {\n    var state = cx.state;\n    if (state.lexical.prev) {\n      if (state.lexical.type == \")\")\n        state.indented = state.lexical.indented;\n      state.lexical = state.lexical.prev;\n    }\n  }\n  poplex.lex = true;\n\n  function expect(wanted) {\n    function exp(type) {\n      if (type == wanted) return cont();\n      else if (wanted == \";\" || type == \"}\" || type == \")\" || type == \"]\") return pass();\n      else return cont(exp);\n    };\n    return exp;\n  }\n\n  function statement(type, value) {\n    if (type == \"var\") return cont(pushlex(\"vardef\", value), vardef, expect(\";\"), poplex);\n    if (type == \"keyword a\") return cont(pushlex(\"form\"), parenExpr, statement, poplex);\n    if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n    if (type == \"keyword d\") return cx.stream.match(/^\\s*$/, false) ? cont() : cont(pushlex(\"stat\"), maybeexpression, expect(\";\"), poplex);\n    if (type == \"debugger\") return cont(expect(\";\"));\n    if (type == \"{\") return cont(pushlex(\"}\"), pushblockcontext, block, poplex, popcontext);\n    if (type == \";\") return cont();\n    if (type == \"if\") {\n      if (cx.state.lexical.info == \"else\" && cx.state.cc[cx.state.cc.length - 1] == poplex)\n        cx.state.cc.pop()();\n      return cont(pushlex(\"form\"), parenExpr, statement, poplex, maybeelse);\n    }\n    if (type == \"function\") return cont(functiondef);\n    if (type == \"for\") return cont(pushlex(\"form\"), pushblockcontext, forspec, statement, popcontext, poplex);\n    if (type == \"class\" || (isTS && value == \"interface\")) {\n      cx.marked = \"keyword\"\n      return cont(pushlex(\"form\", type == \"class\" ? type : value), className, poplex)\n    }\n    if (type == \"variable\") {\n      if (isTS && value == \"declare\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else if (isTS && (value == \"module\" || value == \"enum\" || value == \"type\") && cx.stream.match(/^\\s*\\w/, false)) {\n        cx.marked = \"keyword\"\n        if (value == \"enum\") return cont(enumdef);\n        else if (value == \"type\") return cont(typename, expect(\"operator\"), typeexpr, expect(\";\"));\n        else return cont(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), block, poplex, poplex)\n      } else if (isTS && value == \"namespace\") {\n        cx.marked = \"keyword\"\n        return cont(pushlex(\"form\"), expression, statement, poplex)\n      } else if (isTS && value == \"abstract\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else {\n        return cont(pushlex(\"stat\"), maybelabel);\n      }\n    }\n    if (type == \"switch\") return cont(pushlex(\"form\"), parenExpr, expect(\"{\"), pushlex(\"}\", \"switch\"), pushblockcontext,\n                                      block, poplex, poplex, popcontext);\n    if (type == \"case\") return cont(expression, expect(\":\"));\n    if (type == \"default\") return cont(expect(\":\"));\n    if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, maybeCatchBinding, statement, poplex, popcontext);\n    if (type == \"export\") return cont(pushlex(\"stat\"), afterExport, poplex);\n    if (type == \"import\") return cont(pushlex(\"stat\"), afterImport, poplex);\n    if (type == \"async\") return cont(statement)\n    if (value == \"@\") return cont(expression, statement)\n    return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n  }\n  function maybeCatchBinding(type) {\n    if (type == \"(\") return cont(funarg, expect(\")\"))\n  }\n  function expression(type, value) {\n    return expressionInner(type, value, false);\n  }\n  function expressionNoComma(type, value) {\n    return expressionInner(type, value, true);\n  }\n  function parenExpr(type) {\n    if (type != \"(\") return pass()\n    return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex)\n  }\n  function expressionInner(type, value, noComma) {\n    if (cx.state.fatArrowAt == cx.stream.start) {\n      var body = noComma ? arrowBodyNoComma : arrowBody;\n      if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, expect(\"=>\"), body, popcontext);\n      else if (type == \"variable\") return pass(pushcontext, pattern, expect(\"=>\"), body, popcontext);\n    }\n\n    var maybeop = noComma ? maybeoperatorNoComma : maybeoperatorComma;\n    if (atomicTypes.hasOwnProperty(type)) return cont(maybeop);\n    if (type == \"function\") return cont(functiondef, maybeop);\n    if (type == \"class\" || (isTS && value == \"interface\")) { cx.marked = \"keyword\"; return cont(pushlex(\"form\"), classExpression, poplex); }\n    if (type == \"keyword c\" || type == \"async\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeop);\n    if (type == \"operator\" || type == \"spread\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"[\") return cont(pushlex(\"]\"), arrayLiteral, poplex, maybeop);\n    if (type == \"{\") return contCommasep(objprop, \"}\", null, maybeop);\n    if (type == \"quasi\") return pass(quasi, maybeop);\n    if (type == \"new\") return cont(maybeTarget(noComma));\n    return cont();\n  }\n  function maybeexpression(type) {\n    if (type.match(/[;\\}\\)\\],]/)) return pass();\n    return pass(expression);\n  }\n\n  function maybeoperatorComma(type, value) {\n    if (type == \",\") return cont(maybeexpression);\n    return maybeoperatorNoComma(type, value, false);\n  }\n  function maybeoperatorNoComma(type, value, noComma) {\n    var me = noComma == false ? maybeoperatorComma : maybeoperatorNoComma;\n    var expr = noComma == false ? expression : expressionNoComma;\n    if (type == \"=>\") return cont(pushcontext, noComma ? arrowBodyNoComma : arrowBody, popcontext);\n    if (type == \"operator\") {\n      if (/\\+\\+|--/.test(value) || isTS && value == \"!\") return cont(me);\n      if (isTS && value == \"<\" && cx.stream.match(/^([^<>]|<[^<>]*>)*>\\s*\\(/, false))\n        return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, me);\n      if (value == \"?\") return cont(expression, expect(\":\"), expr);\n      return cont(expr);\n    }\n    if (type == \"quasi\") { return pass(quasi, me); }\n    if (type == \";\") return;\n    if (type == \"(\") return contCommasep(expressionNoComma, \")\", \"call\", me);\n    if (type == \".\") return cont(property, me);\n    if (type == \"[\") return cont(pushlex(\"]\"), maybeexpression, expect(\"]\"), poplex, me);\n    if (isTS && value == \"as\") { cx.marked = \"keyword\"; return cont(typeexpr, me) }\n    if (type == \"regexp\") {\n      cx.state.lastType = cx.marked = \"operator\"\n      cx.stream.backUp(cx.stream.pos - cx.stream.start - 1)\n      return cont(expr)\n    }\n  }\n  function quasi(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasi);\n    return cont(maybeexpression, continueQuasi);\n  }\n  function continueQuasi(type) {\n    if (type == \"}\") {\n      cx.marked = \"string.special\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasi);\n    }\n  }\n  function arrowBody(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expression);\n  }\n  function arrowBodyNoComma(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expressionNoComma);\n  }\n  function maybeTarget(noComma) {\n    return function(type) {\n      if (type == \".\") return cont(noComma ? targetNoComma : target);\n      else if (type == \"variable\" && isTS) return cont(maybeTypeArgs, noComma ? maybeoperatorNoComma : maybeoperatorComma)\n      else return pass(noComma ? expressionNoComma : expression);\n    };\n  }\n  function target(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorComma); }\n  }\n  function targetNoComma(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorNoComma); }\n  }\n  function maybelabel(type) {\n    if (type == \":\") return cont(poplex, statement);\n    return pass(maybeoperatorComma, expect(\";\"), poplex);\n  }\n  function property(type) {\n    if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n  }\n  function objprop(type, value) {\n    if (type == \"async\") {\n      cx.marked = \"property\";\n      return cont(objprop);\n    } else if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      if (value == \"get\" || value == \"set\") return cont(getterSetter);\n      var m // Work around fat-arrow-detection complication for detecting typescript typed arrow params\n      if (isTS && cx.state.fatArrowAt == cx.stream.start && (m = cx.stream.match(/^\\s*:\\s*/, false)))\n        cx.state.fatArrowAt = cx.stream.pos + m[0].length\n      return cont(afterprop);\n    } else if (type == \"number\" || type == \"string\") {\n      cx.marked = jsonldMode ? \"property\" : (cx.style + \" property\");\n      return cont(afterprop);\n    } else if (type == \"jsonld-keyword\") {\n      return cont(afterprop);\n    } else if (isTS && isModifier(value)) {\n      cx.marked = \"keyword\"\n      return cont(objprop)\n    } else if (type == \"[\") {\n      return cont(expression, maybetype, expect(\"]\"), afterprop);\n    } else if (type == \"spread\") {\n      return cont(expressionNoComma, afterprop);\n    } else if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(objprop);\n    } else if (type == \":\") {\n      return pass(afterprop)\n    }\n  }\n  function getterSetter(type) {\n    if (type != \"variable\") return pass(afterprop);\n    cx.marked = \"property\";\n    return cont(functiondef);\n  }\n  function afterprop(type) {\n    if (type == \":\") return cont(expressionNoComma);\n    if (type == \"(\") return pass(functiondef);\n  }\n  function commasep(what, end, sep) {\n    function proceed(type, value) {\n      if (sep ? sep.indexOf(type) > -1 : type == \",\") {\n        var lex = cx.state.lexical;\n        if (lex.info == \"call\") lex.pos = (lex.pos || 0) + 1;\n        return cont(function(type, value) {\n          if (type == end || value == end) return pass()\n          return pass(what)\n        }, proceed);\n      }\n      if (type == end || value == end) return cont();\n      if (sep && sep.indexOf(\";\") > -1) return pass(what)\n      return cont(expect(end));\n    }\n    return function(type, value) {\n      if (type == end || value == end) return cont();\n      return pass(what, proceed);\n    };\n  }\n  function contCommasep(what, end, info) {\n    for (var i = 3; i < arguments.length; i++)\n      cx.cc.push(arguments[i]);\n    return cont(pushlex(end, info), commasep(what, end), poplex);\n  }\n  function block(type) {\n    if (type == \"}\") return cont();\n    return pass(statement, block);\n  }\n  function maybetype(type, value) {\n    if (isTS) {\n      if (type == \":\") return cont(typeexpr);\n      if (value == \"?\") return cont(maybetype);\n    }\n  }\n  function maybetypeOrIn(type, value) {\n    if (isTS && (type == \":\" || value == \"in\")) return cont(typeexpr)\n  }\n  function mayberettype(type) {\n    if (isTS && type == \":\") {\n      if (cx.stream.match(/^\\s*\\w+\\s+is\\b/, false)) return cont(expression, isKW, typeexpr)\n      else return cont(typeexpr)\n    }\n  }\n  function isKW(_, value) {\n    if (value == \"is\") {\n      cx.marked = \"keyword\"\n      return cont()\n    }\n  }\n  function typeexpr(type, value) {\n    if (value == \"keyof\" || value == \"typeof\" || value == \"infer\" || value == \"readonly\") {\n      cx.marked = \"keyword\"\n      return cont(value == \"typeof\" ? expressionNoComma : typeexpr)\n    }\n    if (type == \"variable\" || value == \"void\") {\n      cx.marked = \"type\"\n      return cont(afterType)\n    }\n    if (value == \"|\" || value == \"&\") return cont(typeexpr)\n    if (type == \"string\" || type == \"number\" || type == \"atom\") return cont(afterType);\n    if (type == \"[\") return cont(pushlex(\"]\"), commasep(typeexpr, \"]\", \",\"), poplex, afterType)\n    if (type == \"{\") return cont(pushlex(\"}\"), typeprops, poplex, afterType)\n    if (type == \"(\") return cont(commasep(typearg, \")\"), maybeReturnType, afterType)\n    if (type == \"<\") return cont(commasep(typeexpr, \">\"), typeexpr)\n    if (type == \"quasi\") return pass(quasiType, afterType)\n  }\n  function maybeReturnType(type) {\n    if (type == \"=>\") return cont(typeexpr)\n  }\n  function typeprops(type) {\n    if (type.match(/[\\}\\)\\]]/)) return cont()\n    if (type == \",\" || type == \";\") return cont(typeprops)\n    return pass(typeprop, typeprops)\n  }\n  function typeprop(type, value) {\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\"\n      return cont(typeprop)\n    } else if (value == \"?\" || type == \"number\" || type == \"string\") {\n      return cont(typeprop)\n    } else if (type == \":\") {\n      return cont(typeexpr)\n    } else if (type == \"[\") {\n      return cont(expect(\"variable\"), maybetypeOrIn, expect(\"]\"), typeprop)\n    } else if (type == \"(\") {\n      return pass(functiondecl, typeprop)\n    } else if (!type.match(/[;\\}\\)\\],]/)) {\n      return cont()\n    }\n  }\n  function quasiType(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasiType);\n    return cont(typeexpr, continueQuasiType);\n  }\n  function continueQuasiType(type) {\n   if (type == \"}\") {\n      cx.marked = \"string.special\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasiType);\n    }\n  }\n  function typearg(type, value) {\n    if (type == \"variable\" && cx.stream.match(/^\\s*[?:]/, false) || value == \"?\") return cont(typearg)\n    if (type == \":\") return cont(typeexpr)\n    if (type == \"spread\") return cont(typearg)\n    return pass(typeexpr)\n  }\n  function afterType(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n    if (value == \"|\" || type == \".\" || value == \"&\") return cont(typeexpr)\n    if (type == \"[\") return cont(typeexpr, expect(\"]\"), afterType)\n    if (value == \"extends\" || value == \"implements\") { cx.marked = \"keyword\"; return cont(typeexpr) }\n    if (value == \"?\") return cont(typeexpr, expect(\":\"), typeexpr)\n  }\n  function maybeTypeArgs(_, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n  }\n  function typeparam() {\n    return pass(typeexpr, maybeTypeDefault)\n  }\n  function maybeTypeDefault(_, value) {\n    if (value == \"=\") return cont(typeexpr)\n  }\n  function vardef(_, value) {\n    if (value == \"enum\") {cx.marked = \"keyword\"; return cont(enumdef)}\n    return pass(pattern, maybetype, maybeAssign, vardefCont);\n  }\n  function pattern(type, value) {\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(pattern) }\n    if (type == \"variable\") { register(value); return cont(); }\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"[\") return contCommasep(eltpattern, \"]\");\n    if (type == \"{\") return contCommasep(proppattern, \"}\");\n  }\n  function proppattern(type, value) {\n    if (type == \"variable\" && !cx.stream.match(/^\\s*:/, false)) {\n      register(value);\n      return cont(maybeAssign);\n    }\n    if (type == \"variable\") cx.marked = \"property\";\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"}\") return pass();\n    if (type == \"[\") return cont(expression, expect(']'), expect(':'), proppattern);\n    return cont(expect(\":\"), pattern, maybeAssign);\n  }\n  function eltpattern() {\n    return pass(pattern, maybeAssign)\n  }\n  function maybeAssign(_type, value) {\n    if (value == \"=\") return cont(expressionNoComma);\n  }\n  function vardefCont(type) {\n    if (type == \",\") return cont(vardef);\n  }\n  function maybeelse(type, value) {\n    if (type == \"keyword b\" && value == \"else\") return cont(pushlex(\"form\", \"else\"), statement, poplex);\n  }\n  function forspec(type, value) {\n    if (value == \"await\") return cont(forspec);\n    if (type == \"(\") return cont(pushlex(\")\"), forspec1, poplex);\n  }\n  function forspec1(type) {\n    if (type == \"var\") return cont(vardef, forspec2);\n    if (type == \"variable\") return cont(forspec2);\n    return pass(forspec2)\n  }\n  function forspec2(type, value) {\n    if (type == \")\") return cont()\n    if (type == \";\") return cont(forspec2)\n    if (value == \"in\" || value == \"of\") { cx.marked = \"keyword\"; return cont(expression, forspec2) }\n    return pass(expression, forspec2)\n  }\n  function functiondef(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondef);}\n    if (type == \"variable\") {register(value); return cont(functiondef);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, statement, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondef)\n  }\n  function functiondecl(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondecl);}\n    if (type == \"variable\") {register(value); return cont(functiondecl);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondecl)\n  }\n  function typename(type, value) {\n    if (type == \"keyword\" || type == \"variable\") {\n      cx.marked = \"type\"\n      return cont(typename)\n    } else if (value == \"<\") {\n      return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex)\n    }\n  }\n  function funarg(type, value) {\n    if (value == \"@\") cont(expression, funarg)\n    if (type == \"spread\") return cont(funarg);\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(funarg); }\n    if (isTS && type == \"this\") return cont(maybetype, maybeAssign)\n    return pass(pattern, maybetype, maybeAssign);\n  }\n  function classExpression(type, value) {\n    // Class expressions may have an optional name.\n    if (type == \"variable\") return className(type, value);\n    return classNameAfter(type, value);\n  }\n  function className(type, value) {\n    if (type == \"variable\") {register(value); return cont(classNameAfter);}\n  }\n  function classNameAfter(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, classNameAfter)\n    if (value == \"extends\" || value == \"implements\" || (isTS && type == \",\")) {\n      if (value == \"implements\") cx.marked = \"keyword\";\n      return cont(isTS ? typeexpr : expression, classNameAfter);\n    }\n    if (type == \"{\") return cont(pushlex(\"}\"), classBody, poplex);\n  }\n  function classBody(type, value) {\n    if (type == \"async\" ||\n        (type == \"variable\" &&\n         (value == \"static\" || value == \"get\" || value == \"set\" || (isTS && isModifier(value))) &&\n         cx.stream.match(/^\\s+#?[\\w$\\xa1-\\uffff]/, false))) {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      return cont(classfield, classBody);\n    }\n    if (type == \"number\" || type == \"string\") return cont(classfield, classBody);\n    if (type == \"[\")\n      return cont(expression, maybetype, expect(\"]\"), classfield, classBody)\n    if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (isTS && type == \"(\") return pass(functiondecl, classBody)\n    if (type == \";\" || type == \",\") return cont(classBody);\n    if (type == \"}\") return cont();\n    if (value == \"@\") return cont(expression, classBody)\n  }\n  function classfield(type, value) {\n    if (value == \"!\" || value == \"?\") return cont(classfield)\n    if (type == \":\") return cont(typeexpr, maybeAssign)\n    if (value == \"=\") return cont(expressionNoComma)\n    var context = cx.state.lexical.prev, isInterface = context && context.info == \"interface\"\n    return pass(isInterface ? functiondecl : functiondef)\n  }\n  function afterExport(type, value) {\n    if (value == \"*\") { cx.marked = \"keyword\"; return cont(maybeFrom, expect(\";\")); }\n    if (value == \"default\") { cx.marked = \"keyword\"; return cont(expression, expect(\";\")); }\n    if (type == \"{\") return cont(commasep(exportField, \"}\"), maybeFrom, expect(\";\"));\n    return pass(statement);\n  }\n  function exportField(type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(expect(\"variable\")); }\n    if (type == \"variable\") return pass(expressionNoComma, exportField);\n  }\n  function afterImport(type) {\n    if (type == \"string\") return cont();\n    if (type == \"(\") return pass(expression);\n    if (type == \".\") return pass(maybeoperatorComma);\n    return pass(importSpec, maybeMoreImports, maybeFrom);\n  }\n  function importSpec(type, value) {\n    if (type == \"{\") return contCommasep(importSpec, \"}\");\n    if (type == \"variable\") register(value);\n    if (value == \"*\") cx.marked = \"keyword\";\n    return cont(maybeAs);\n  }\n  function maybeMoreImports(type) {\n    if (type == \",\") return cont(importSpec, maybeMoreImports)\n  }\n  function maybeAs(_type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(importSpec); }\n  }\n  function maybeFrom(_type, value) {\n    if (value == \"from\") { cx.marked = \"keyword\"; return cont(expression); }\n  }\n  function arrayLiteral(type) {\n    if (type == \"]\") return cont();\n    return pass(commasep(expressionNoComma, \"]\"));\n  }\n  function enumdef() {\n    return pass(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), commasep(enummember, \"}\"), poplex, poplex)\n  }\n  function enummember() {\n    return pass(pattern, maybeAssign);\n  }\n\n  function isContinuedStatement(state, textAfter) {\n    return state.lastType == \"operator\" || state.lastType == \",\" ||\n      isOperatorChar.test(textAfter.charAt(0)) ||\n      /[,.]/.test(textAfter.charAt(0));\n  }\n\n  function expressionAllowed(stream, state, backUp) {\n    return state.tokenize == tokenBase &&\n      /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\\[{}\\(,;:]|=>)$/.test(state.lastType) ||\n      (state.lastType == \"quasi\" && /\\{\\s*$/.test(stream.string.slice(0, stream.pos - (backUp || 0))))\n  }\n\n  // Interface\n\n  return {\n    name: parserConfig.name,\n\n    startState: function(indentUnit) {\n      var state = {\n        tokenize: tokenBase,\n        lastType: \"sof\",\n        cc: [],\n        lexical: new JSLexical(-indentUnit, 0, \"block\", false),\n        localVars: parserConfig.localVars,\n        context: parserConfig.localVars && new Context(null, null, false),\n        indented: 0\n      };\n      if (parserConfig.globalVars && typeof parserConfig.globalVars == \"object\")\n        state.globalVars = parserConfig.globalVars;\n      return state;\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (!state.lexical.hasOwnProperty(\"align\"))\n          state.lexical.align = false;\n        state.indented = stream.indentation();\n        findFatArrow(stream, state);\n      }\n      if (state.tokenize != tokenComment && stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (type == \"comment\") return style;\n      state.lastType = type == \"operator\" && (content == \"++\" || content == \"--\") ? \"incdec\" : type;\n      return parseJS(state, style, type, content, stream);\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize == tokenComment || state.tokenize == tokenQuasi) return null;\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical, top\n      // Kludge to prevent 'maybelse' from blocking lexical scope pops\n      if (!/^\\s*else\\b/.test(textAfter)) for (var i = state.cc.length - 1; i >= 0; --i) {\n        var c = state.cc[i];\n        if (c == poplex) lexical = lexical.prev;\n        else if (c != maybeelse && c != popcontext) break;\n      }\n      while ((lexical.type == \"stat\" || lexical.type == \"form\") &&\n             (firstChar == \"}\" || ((top = state.cc[state.cc.length - 1]) &&\n                                   (top == maybeoperatorComma || top == maybeoperatorNoComma) &&\n                                   !/^[,\\.=+\\-*:?[\\(]/.test(textAfter))))\n        lexical = lexical.prev;\n      if (statementIndent && lexical.type == \")\" && lexical.prev.type == \"stat\")\n        lexical = lexical.prev;\n      var type = lexical.type, closing = firstChar == type;\n\n      if (type == \"vardef\") return lexical.indented + (state.lastType == \"operator\" || state.lastType == \",\" ? lexical.info.length + 1 : 0);\n      else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n      else if (type == \"form\") return lexical.indented + cx.unit;\n      else if (type == \"stat\")\n        return lexical.indented + (isContinuedStatement(state, textAfter) ? statementIndent || cx.unit : 0);\n      else if (lexical.info == \"switch\" && !closing && parserConfig.doubleIndentSwitch != false)\n        return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? cx.unit : 2 * cx.unit);\n      else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n      else return lexical.indented + (closing ? 0 : cx.unit);\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*(?:case .*?:|default:|\\{|\\})$/,\n      commentTokens: jsonMode ? undefined : {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n      wordChars: \"$\"\n    }\n  };\n};\n\nexport const javascript = mkJavaScript({name: \"javascript\"})\nexport const json = mkJavaScript({name: \"json\", json: true})\nexport const jsonld = mkJavaScript({name: \"json\", jsonld: true})\nexport const typescript = mkJavaScript({name: \"typescript\", typescript: true})\n"], "mappings": ";AAAA,SAAS,aAAa,cAAc;AAClC,MAAI,kBAAkB,aAAa;AACnC,MAAI,aAAa,aAAa;AAC9B,MAAI,WAAW,aAAa,QAAQ;AACpC,MAAI,OAAO,aAAa;AACxB,MAAI,SAAS,aAAa,kBAAkB;AAI5C,MAAI,WAAW,WAAU;AACvB,aAAS,GAAGA,OAAM;AAAC,aAAO,EAAC,MAAMA,OAAM,OAAO,UAAS;AAAA,IAAE;AACzD,QAAI,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW;AACrF,QAAI,WAAW,GAAG,UAAU,GAAG,OAAO,EAAC,MAAM,QAAQ,OAAO,OAAM;AAElE,WAAO;AAAA,MACL,MAAM,GAAG,IAAI;AAAA,MAAG,SAAS;AAAA,MAAG,QAAQ;AAAA,MAAG,QAAQ;AAAA,MAAG,MAAM;AAAA,MAAG,OAAO;AAAA,MAAG,WAAW;AAAA,MAChF,UAAU;AAAA,MAAG,SAAS;AAAA,MAAG,YAAY;AAAA,MAAG,OAAO,GAAG,KAAK;AAAA,MAAG,UAAU;AAAA,MAAG,QAAQ;AAAA,MAAG,SAAS;AAAA,MAC3F,YAAY,GAAG,UAAU;AAAA,MAAG,OAAO,GAAG,KAAK;AAAA,MAAG,SAAS,GAAG,KAAK;AAAA,MAAG,OAAO,GAAG,KAAK;AAAA,MACjF,YAAY,GAAG,UAAU;AAAA,MAAG,SAAS,GAAG,OAAO;AAAA,MAC/C,OAAO,GAAG,KAAK;AAAA,MAAG,UAAU,GAAG,QAAQ;AAAA,MAAG,QAAQ,GAAG,MAAM;AAAA,MAAG,WAAW,GAAG,SAAS;AAAA,MACrF,MAAM;AAAA,MAAU,UAAU;AAAA,MAAU,cAAc;AAAA,MAClD,QAAQ;AAAA,MAAM,SAAS;AAAA,MAAM,QAAQ;AAAA,MAAM,aAAa;AAAA,MAAM,OAAO;AAAA,MAAM,YAAY;AAAA,MACvF,QAAQ,GAAG,MAAM;AAAA,MAAG,SAAS,GAAG,OAAO;AAAA,MAAG,SAAS,GAAG,MAAM;AAAA,MAC5D,SAAS;AAAA,MAAG,UAAU,GAAG,QAAQ;AAAA,MAAG,UAAU,GAAG,QAAQ;AAAA,MAAG,WAAW;AAAA,MACvE,SAAS;AAAA,IACX;AAAA,EACF,EAAE;AAEF,MAAI,iBAAiB;AACrB,MAAI,kBAAkB;AAEtB,WAAS,WAAW,QAAQ;AAC1B,QAAI,UAAU,OAAO,MAAM,QAAQ;AACnC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,CAAC,SAAS;AACZ,YAAI,QAAQ,OAAO,CAAC;AAAO;AAC3B,YAAI,QAAQ;AAAK,kBAAQ;AAAA,iBAChB,SAAS,QAAQ;AAAK,kBAAQ;AAAA,MACzC;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AAAA,EACF;AAIA,MAAI,MAAM;AACV,WAAS,IAAI,IAAI,OAAOC,OAAM;AAC5B,WAAO;AAAI,cAAUA;AACrB,WAAO;AAAA,EACT;AACA,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,YAAM,WAAW,YAAY,EAAE;AAC/B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,WAAW,MAAM,OAAO,OAAO,MAAM,gCAAgC,GAAG;AACtE,aAAO,IAAI,UAAU,QAAQ;AAAA,IAC/B,WAAW,MAAM,OAAO,OAAO,MAAM,IAAI,GAAG;AAC1C,aAAO,IAAI,UAAU,MAAM;AAAA,IAC7B,WAAW,qBAAqB,KAAK,EAAE,GAAG;AACxC,aAAO,IAAI,EAAE;AAAA,IACf,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,aAAO,IAAI,MAAM,UAAU;AAAA,IAC7B,WAAW,MAAM,OAAO,OAAO,MAAM,uCAAuC,GAAG;AAC7E,aAAO,IAAI,UAAU,QAAQ;AAAA,IAC/B,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,aAAO,MAAM,kDAAkD;AAC/D,aAAO,IAAI,UAAU,QAAQ;AAAA,IAC/B,WAAW,MAAM,KAAK;AACpB,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAM,WAAW;AACjB,eAAO,aAAa,QAAQ,KAAK;AAAA,MACnC,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,eAAO,UAAU;AACjB,eAAO,IAAI,WAAW,SAAS;AAAA,MACjC,WAAW,kBAAkB,QAAQ,OAAO,CAAC,GAAG;AAC9C,mBAAW,MAAM;AACjB,eAAO,MAAM,mCAAmC;AAChD,eAAO,IAAI,UAAU,gBAAgB;AAAA,MACvC,OAAO;AACL,eAAO,IAAI,GAAG;AACd,eAAO,IAAI,YAAY,YAAY,OAAO,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF,WAAW,MAAM,KAAK;AACpB,YAAM,WAAW;AACjB,aAAO,WAAW,QAAQ,KAAK;AAAA,IACjC,WAAW,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK;AAC5C,aAAO,UAAU;AACjB,aAAO,IAAI,QAAQ,MAAM;AAAA,IAC3B,WAAW,MAAM,OAAO,OAAO,SAAS,MAAM,GAAG;AAC/C,aAAO,IAAI,YAAY,UAAU;AAAA,IACnC,WAAW,MAAM,OAAO,OAAO,MAAM,KAAK,KAC9B,MAAM,OAAO,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,OAAO,MAAM,GAAG,OAAO,KAAK,CAAC,GAAI;AAChG,aAAO,UAAU;AACjB,aAAO,IAAI,WAAW,SAAS;AAAA,IACjC,WAAW,eAAe,KAAK,EAAE,GAAG;AAClC,UAAI,MAAM,OAAO,CAAC,MAAM,WAAW,MAAM,QAAQ,QAAQ,KAAK;AAC5D,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAI,MAAM,OAAO,MAAM;AAAK,mBAAO,IAAI,GAAG;AAAA,QAC5C,WAAW,cAAc,KAAK,EAAE,GAAG;AACjC,iBAAO,IAAI,EAAE;AACb,cAAI,MAAM;AAAK,mBAAO,IAAI,EAAE;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,MAAM,OAAO,OAAO,IAAI,GAAG;AAAG,eAAO,IAAI,GAAG;AAChD,aAAO,IAAI,YAAY,YAAY,OAAO,QAAQ,CAAC;AAAA,IACrD,WAAW,OAAO,KAAK,EAAE,GAAG;AAC1B,aAAO,SAAS,MAAM;AACtB,UAAI,OAAO,OAAO,QAAQ;AAC1B,UAAI,MAAM,YAAY,KAAK;AACzB,YAAI,SAAS,qBAAqB,IAAI,GAAG;AACvC,cAAI,KAAK,SAAS,IAAI;AACtB,iBAAO,IAAI,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,QACpC;AACA,YAAI,QAAQ,WAAW,OAAO,MAAM,4CAA4C,KAAK;AACnF,iBAAO,IAAI,SAAS,WAAW,IAAI;AAAA,MACvC;AACA,aAAO,IAAI,YAAY,YAAY,IAAI;AAAA,IACzC;AAAA,EACF;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,UAAU,OAAO;AACrB,UAAI,cAAc,OAAO,KAAK,KAAK,OAAO,OAAO,MAAM,eAAe,GAAE;AACtE,cAAM,WAAW;AACjB,eAAO,IAAI,kBAAkB,MAAM;AAAA,MACrC;AACA,cAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,YAAI,QAAQ,SAAS,CAAC;AAAS;AAC/B,kBAAU,CAAC,WAAW,QAAQ;AAAA,MAChC;AACA,UAAI,CAAC;AAAS,cAAM,WAAW;AAC/B,aAAO,IAAI,UAAU,QAAQ;AAAA,IAC/B;AAAA,EACF;AAEA,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,WAAW,OAAO;AACtB,WAAO,KAAK,OAAO,KAAK,GAAG;AACzB,UAAI,MAAM,OAAO,UAAU;AACzB,cAAM,WAAW;AACjB;AAAA,MACF;AACA,iBAAY,MAAM;AAAA,IACpB;AACA,WAAO,IAAI,WAAW,SAAS;AAAA,EACjC;AAEA,WAAS,WAAW,QAAQ,OAAO;AACjC,QAAI,UAAU,OAAO;AACrB,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,CAAC,YAAY,QAAQ,OAAO,QAAQ,OAAO,OAAO,IAAI,GAAG,IAAI;AAC/D,cAAM,WAAW;AACjB;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,WAAO,IAAI,SAAS,kBAAkB,OAAO,QAAQ,CAAC;AAAA,EACxD;AAEA,MAAI,WAAW;AAQf,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,MAAM;AAAY,YAAM,aAAa;AACzC,QAAI,QAAQ,OAAO,OAAO,QAAQ,MAAM,OAAO,KAAK;AACpD,QAAI,QAAQ;AAAG;AAEf,QAAI,MAAM;AACR,UAAI,IAAI,6CAA6C,KAAK,OAAO,OAAO,MAAM,OAAO,OAAO,KAAK,CAAC;AAClG,UAAI;AAAG,gBAAQ,EAAE;AAAA,IACnB;AAEA,QAAI,QAAQ,GAAG,eAAe;AAC9B,aAAS,MAAM,QAAQ,GAAG,OAAO,GAAG,EAAE,KAAK;AACzC,UAAI,KAAK,OAAO,OAAO,OAAO,GAAG;AACjC,UAAI,UAAU,SAAS,QAAQ,EAAE;AACjC,UAAI,WAAW,KAAK,UAAU,GAAG;AAC/B,YAAI,CAAC,OAAO;AAAE,YAAE;AAAK;AAAA,QAAO;AAC5B,YAAI,EAAE,SAAS,GAAG;AAAE,cAAI,MAAM;AAAK,2BAAe;AAAM;AAAA,QAAO;AAAA,MACjE,WAAW,WAAW,KAAK,UAAU,GAAG;AACtC,UAAE;AAAA,MACJ,WAAW,OAAO,KAAK,EAAE,GAAG;AAC1B,uBAAe;AAAA,MACjB,WAAW,UAAU,KAAK,EAAE,GAAG;AAC7B,iBAAQ,EAAE,KAAK;AACb,cAAI,OAAO;AAAG;AACd,cAAI,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC;AACvC,cAAI,QAAQ,MAAM,OAAO,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM;AAAE;AAAO;AAAA,UAAM;AAAA,QAC1E;AAAA,MACF,WAAW,gBAAgB,CAAC,OAAO;AACjC,UAAE;AACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB,CAAC;AAAO,YAAM,aAAa;AAAA,EACjD;AAIA,MAAI,cAAc;AAAA,IAAC,QAAQ;AAAA,IAAM,UAAU;AAAA,IAAM,YAAY;AAAA,IAAM,UAAU;AAAA,IAC1D,UAAU;AAAA,IAAM,QAAQ;AAAA,IAAM,UAAU;AAAA,IAAM,kBAAkB;AAAA,EAAI;AAEvF,WAAS,UAAU,UAAU,QAAQD,OAAM,OAAO,MAAM,MAAM;AAC5D,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAOA;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,QAAI,SAAS;AAAM,WAAK,QAAQ;AAAA,EAClC;AAEA,WAAS,QAAQ,OAAO,SAAS;AAC/B,aAAS,IAAI,MAAM,WAAW,GAAG,IAAI,EAAE;AACrC,UAAI,EAAE,QAAQ;AAAS,eAAO;AAChC,aAASE,MAAK,MAAM,SAASA,KAAIA,MAAKA,IAAG,MAAM;AAC7C,eAAS,IAAIA,IAAG,MAAM,GAAG,IAAI,EAAE;AAC7B,YAAI,EAAE,QAAQ;AAAS,iBAAO;AAAA,IAClC;AAAA,EACF;AAEA,WAAS,QAAQ,OAAO,OAAOF,OAAMG,UAAS,QAAQ;AACpD,QAAI,KAAK,MAAM;AAGf,OAAG,QAAQ;AAAO,OAAG,SAAS;AAAQ,OAAG,SAAS;AAAM,OAAG,KAAK;AAAI,OAAG,QAAQ;AAE/E,QAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,YAAM,QAAQ,QAAQ;AAExB,WAAM,MAAM;AACV,UAAI,aAAa,GAAG,SAAS,GAAG,IAAI,IAAI,WAAW,aAAa;AAChE,UAAI,WAAWH,OAAMG,QAAO,GAAG;AAC7B,eAAM,GAAG,UAAU,GAAG,GAAG,SAAS,CAAC,EAAE;AACnC,aAAG,IAAI,EAAE;AACX,YAAI,GAAG;AAAQ,iBAAO,GAAG;AACzB,YAAIH,SAAQ,cAAc,QAAQ,OAAOG,QAAO;AAAG,iBAAO;AAC1D,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAIA,MAAI,KAAK,EAAC,OAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,IAAI,KAAI;AAC3D,WAAS,OAAO;AACd,aAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG;AAAK,SAAG,GAAG,KAAK,UAAU,CAAC,CAAC;AAAA,EACzE;AACA,WAAS,OAAO;AACd,SAAK,MAAM,MAAM,SAAS;AAC1B,WAAO;AAAA,EACT;AACA,WAAS,OAAO,MAAM,MAAM;AAC1B,aAAS,IAAI,MAAM,GAAG,IAAI,EAAE;AAAM,UAAI,EAAE,QAAQ;AAAM,eAAO;AAC7D,WAAO;AAAA,EACT;AACA,WAAS,SAAS,SAAS;AACzB,QAAI,QAAQ,GAAG;AACf,OAAG,SAAS;AACZ,QAAI,MAAM,SAAS;AACjB,UAAI,MAAM,QAAQ,QAAQ,SAAS,MAAM,WAAW,MAAM,QAAQ,OAAO;AAEvE,YAAI,aAAa,kBAAkB,SAAS,MAAM,OAAO;AACzD,YAAI,cAAc,MAAM;AACtB,gBAAM,UAAU;AAChB;AAAA,QACF;AAAA,MACF,WAAW,CAAC,OAAO,SAAS,MAAM,SAAS,GAAG;AAC5C,cAAM,YAAY,IAAI,IAAI,SAAS,MAAM,SAAS;AAClD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,aAAa,cAAc,CAAC,OAAO,SAAS,MAAM,UAAU;AAC9D,YAAM,aAAa,IAAI,IAAI,SAAS,MAAM,UAAU;AAAA,EACxD;AACA,WAAS,kBAAkB,SAAS,SAAS;AAC3C,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT,WAAW,QAAQ,OAAO;AACxB,UAAI,QAAQ,kBAAkB,SAAS,QAAQ,IAAI;AACnD,UAAI,CAAC;AAAO,eAAO;AACnB,UAAI,SAAS,QAAQ;AAAM,eAAO;AAClC,aAAO,IAAI,QAAQ,OAAO,QAAQ,MAAM,IAAI;AAAA,IAC9C,WAAW,OAAO,SAAS,QAAQ,IAAI,GAAG;AACxC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,IAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAAA,IACxE;AAAA,EACF;AAEA,WAAS,WAAW,MAAM;AACxB,WAAO,QAAQ,YAAY,QAAQ,aAAa,QAAQ,eAAe,QAAQ,cAAc,QAAQ;AAAA,EACvG;AAIA,WAAS,QAAQ,MAAM,MAAMC,QAAO;AAAE,SAAK,OAAO;AAAM,SAAK,OAAO;AAAM,SAAK,QAAQA;AAAA,EAAM;AAC7F,WAAS,IAAI,MAAM,MAAM;AAAE,SAAK,OAAO;AAAM,SAAK,OAAO;AAAA,EAAK;AAE9D,MAAI,cAAc,IAAI,IAAI,QAAQ,IAAI,IAAI,aAAa,IAAI,CAAC;AAC5D,WAAS,cAAc;AACrB,OAAG,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,WAAW,KAAK;AAC1E,OAAG,MAAM,YAAY;AAAA,EACvB;AACA,WAAS,mBAAmB;AAC1B,OAAG,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,WAAW,IAAI;AACzE,OAAG,MAAM,YAAY;AAAA,EACvB;AACA,cAAY,MAAM,iBAAiB,MAAM;AACzC,WAAS,aAAa;AACpB,OAAG,MAAM,YAAY,GAAG,MAAM,QAAQ;AACtC,OAAG,MAAM,UAAU,GAAG,MAAM,QAAQ;AAAA,EACtC;AACA,aAAW,MAAM;AACjB,WAAS,QAAQJ,OAAM,MAAM;AAC3B,QAAI,SAAS,WAAW;AACtB,UAAI,QAAQ,GAAG,OAAO,SAAS,MAAM;AACrC,UAAI,MAAM,QAAQ,QAAQ;AAAQ,iBAAS,MAAM,QAAQ;AAAA;AACpD,iBAAS,QAAQ,MAAM,SAAS,SAAS,MAAM,QAAQ,OAAO,MAAM,OAAO,QAAQ,MAAM;AAC5F,mBAAS,MAAM;AACjB,YAAM,UAAU,IAAI,UAAU,QAAQ,GAAG,OAAO,OAAO,GAAGA,OAAM,MAAM,MAAM,SAAS,IAAI;AAAA,IAC3F;AACA,WAAO,MAAM;AACb,WAAO;AAAA,EACT;AACA,WAAS,SAAS;AAChB,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,MAAM;AACtB,UAAI,MAAM,QAAQ,QAAQ;AACxB,cAAM,WAAW,MAAM,QAAQ;AACjC,YAAM,UAAU,MAAM,QAAQ;AAAA,IAChC;AAAA,EACF;AACA,SAAO,MAAM;AAEb,WAAS,OAAO,QAAQ;AACtB,aAAS,IAAIA,OAAM;AACjB,UAAIA,SAAQ;AAAQ,eAAO,KAAK;AAAA,eACvB,UAAU,OAAOA,SAAQ,OAAOA,SAAQ,OAAOA,SAAQ;AAAK,eAAO,KAAK;AAAA;AAC5E,eAAO,KAAK,GAAG;AAAA,IACtB;AAAC;AACD,WAAO;AAAA,EACT;AAEA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAIA,SAAQ;AAAO,aAAO,KAAK,QAAQ,UAAU,KAAK,GAAG,QAAQ,OAAO,GAAG,GAAG,MAAM;AACpF,QAAIA,SAAQ;AAAa,aAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,WAAW,MAAM;AAClF,QAAIA,SAAQ;AAAa,aAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,MAAM;AACvE,QAAIA,SAAQ;AAAa,aAAO,GAAG,OAAO,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,MAAM,GAAG,iBAAiB,OAAO,GAAG,GAAG,MAAM;AACrI,QAAIA,SAAQ;AAAY,aAAO,KAAK,OAAO,GAAG,CAAC;AAC/C,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,kBAAkB,OAAO,QAAQ,UAAU;AACtF,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,QAAIA,SAAQ,MAAM;AAChB,UAAI,GAAG,MAAM,QAAQ,QAAQ,UAAU,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC,KAAK;AAC5E,WAAG,MAAM,GAAG,IAAI,EAAE;AACpB,aAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,WAAW,QAAQ,SAAS;AAAA,IACtE;AACA,QAAIA,SAAQ;AAAY,aAAO,KAAK,WAAW;AAC/C,QAAIA,SAAQ;AAAO,aAAO,KAAK,QAAQ,MAAM,GAAG,kBAAkB,SAAS,WAAW,YAAY,MAAM;AACxG,QAAIA,SAAQ,WAAY,QAAQ,SAAS,aAAc;AACrD,SAAG,SAAS;AACZ,aAAO,KAAK,QAAQ,QAAQA,SAAQ,UAAUA,QAAO,KAAK,GAAG,WAAW,MAAM;AAAA,IAChF;AACA,QAAIA,SAAQ,YAAY;AACtB,UAAI,QAAQ,SAAS,WAAW;AAC9B,WAAG,SAAS;AACZ,eAAO,KAAK,SAAS;AAAA,MACvB,WAAW,SAAS,SAAS,YAAY,SAAS,UAAU,SAAS,WAAW,GAAG,OAAO,MAAM,UAAU,KAAK,GAAG;AAChH,WAAG,SAAS;AACZ,YAAI,SAAS;AAAQ,iBAAO,KAAK,OAAO;AAAA,iBAC/B,SAAS;AAAQ,iBAAO,KAAK,UAAU,OAAO,UAAU,GAAG,UAAU,OAAO,GAAG,CAAC;AAAA;AACpF,iBAAO,KAAK,QAAQ,MAAM,GAAG,SAAS,OAAO,GAAG,GAAG,QAAQ,GAAG,GAAG,OAAO,QAAQ,MAAM;AAAA,MAC7F,WAAW,QAAQ,SAAS,aAAa;AACvC,WAAG,SAAS;AACZ,eAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,WAAW,MAAM;AAAA,MAC5D,WAAW,QAAQ,SAAS,YAAY;AACtC,WAAG,SAAS;AACZ,eAAO,KAAK,SAAS;AAAA,MACvB,OAAO;AACL,eAAO,KAAK,QAAQ,MAAM,GAAG,UAAU;AAAA,MACzC;AAAA,IACF;AACA,QAAIA,SAAQ;AAAU,aAAO;AAAA,QAAK,QAAQ,MAAM;AAAA,QAAG;AAAA,QAAW,OAAO,GAAG;AAAA,QAAG,QAAQ,KAAK,QAAQ;AAAA,QAAG;AAAA,QACjE;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAQ;AAAA,MAAU;AACnE,QAAIA,SAAQ;AAAQ,aAAO,KAAK,YAAY,OAAO,GAAG,CAAC;AACvD,QAAIA,SAAQ;AAAW,aAAO,KAAK,OAAO,GAAG,CAAC;AAC9C,QAAIA,SAAQ;AAAS,aAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,mBAAmB,WAAW,QAAQ,UAAU;AAC/G,QAAIA,SAAQ;AAAU,aAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,MAAM;AACtE,QAAIA,SAAQ;AAAU,aAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,MAAM;AACtE,QAAIA,SAAQ;AAAS,aAAO,KAAK,SAAS;AAC1C,QAAI,SAAS;AAAK,aAAO,KAAK,YAAY,SAAS;AACnD,WAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,OAAO,GAAG,GAAG,MAAM;AAAA,EAC9D;AACA,WAAS,kBAAkBA,OAAM;AAC/B,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,OAAO,GAAG,CAAC;AAAA,EAClD;AACA,WAAS,WAAWA,OAAM,OAAO;AAC/B,WAAO,gBAAgBA,OAAM,OAAO,KAAK;AAAA,EAC3C;AACA,WAAS,kBAAkBA,OAAM,OAAO;AACtC,WAAO,gBAAgBA,OAAM,OAAO,IAAI;AAAA,EAC1C;AACA,WAAS,UAAUA,OAAM;AACvB,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,WAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,MAAM;AAAA,EAChE;AACA,WAAS,gBAAgBA,OAAM,OAAO,SAAS;AAC7C,QAAI,GAAG,MAAM,cAAc,GAAG,OAAO,OAAO;AAC1C,UAAI,OAAO,UAAU,mBAAmB;AACxC,UAAIA,SAAQ;AAAK,eAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,OAAO,IAAI,GAAG,MAAM,UAAU;AAAA,eAC5GA,SAAQ;AAAY,eAAO,KAAK,aAAa,SAAS,OAAO,IAAI,GAAG,MAAM,UAAU;AAAA,IAC/F;AAEA,QAAI,UAAU,UAAU,uBAAuB;AAC/C,QAAI,YAAY,eAAeA,KAAI;AAAG,aAAO,KAAK,OAAO;AACzD,QAAIA,SAAQ;AAAY,aAAO,KAAK,aAAa,OAAO;AACxD,QAAIA,SAAQ,WAAY,QAAQ,SAAS,aAAc;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,QAAQ,MAAM,GAAG,iBAAiB,MAAM;AAAA,IAAG;AACvI,QAAIA,SAAQ,eAAeA,SAAQ;AAAS,aAAO,KAAK,UAAU,oBAAoB,UAAU;AAChG,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,QAAQ,OAAO;AACxF,QAAIA,SAAQ,cAAcA,SAAQ;AAAU,aAAO,KAAK,UAAU,oBAAoB,UAAU;AAChG,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,cAAc,QAAQ,OAAO;AACxE,QAAIA,SAAQ;AAAK,aAAO,aAAa,SAAS,KAAK,MAAM,OAAO;AAChE,QAAIA,SAAQ;AAAS,aAAO,KAAK,OAAO,OAAO;AAC/C,QAAIA,SAAQ;AAAO,aAAO,KAAK,YAAY,OAAO,CAAC;AACnD,WAAO,KAAK;AAAA,EACd;AACA,WAAS,gBAAgBA,OAAM;AAC7B,QAAIA,MAAK,MAAM,YAAY;AAAG,aAAO,KAAK;AAC1C,WAAO,KAAK,UAAU;AAAA,EACxB;AAEA,WAAS,mBAAmBA,OAAM,OAAO;AACvC,QAAIA,SAAQ;AAAK,aAAO,KAAK,eAAe;AAC5C,WAAO,qBAAqBA,OAAM,OAAO,KAAK;AAAA,EAChD;AACA,WAAS,qBAAqBA,OAAM,OAAO,SAAS;AAClD,QAAI,KAAK,WAAW,QAAQ,qBAAqB;AACjD,QAAI,OAAO,WAAW,QAAQ,aAAa;AAC3C,QAAIA,SAAQ;AAAM,aAAO,KAAK,aAAa,UAAU,mBAAmB,WAAW,UAAU;AAC7F,QAAIA,SAAQ,YAAY;AACtB,UAAI,UAAU,KAAK,KAAK,KAAK,QAAQ,SAAS;AAAK,eAAO,KAAK,EAAE;AACjE,UAAI,QAAQ,SAAS,OAAO,GAAG,OAAO,MAAM,4BAA4B,KAAK;AAC3E,eAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,EAAE;AAC/D,UAAI,SAAS;AAAK,eAAO,KAAK,YAAY,OAAO,GAAG,GAAG,IAAI;AAC3D,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,QAAIA,SAAQ,SAAS;AAAE,aAAO,KAAK,OAAO,EAAE;AAAA,IAAG;AAC/C,QAAIA,SAAQ;AAAK;AACjB,QAAIA,SAAQ;AAAK,aAAO,aAAa,mBAAmB,KAAK,QAAQ,EAAE;AACvE,QAAIA,SAAQ;AAAK,aAAO,KAAK,UAAU,EAAE;AACzC,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,QAAQ,EAAE;AACnF,QAAI,QAAQ,SAAS,MAAM;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,UAAU,EAAE;AAAA,IAAE;AAC9E,QAAIA,SAAQ,UAAU;AACpB,SAAG,MAAM,WAAW,GAAG,SAAS;AAChC,SAAG,OAAO,OAAO,GAAG,OAAO,MAAM,GAAG,OAAO,QAAQ,CAAC;AACpD,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF;AACA,WAAS,MAAMA,OAAM,OAAO;AAC1B,QAAIA,SAAQ;AAAS,aAAO,KAAK;AACjC,QAAI,MAAM,MAAM,MAAM,SAAS,CAAC,KAAK;AAAM,aAAO,KAAK,KAAK;AAC5D,WAAO,KAAK,iBAAiB,aAAa;AAAA,EAC5C;AACA,WAAS,cAAcA,OAAM;AAC3B,QAAIA,SAAQ,KAAK;AACf,SAAG,SAAS;AACZ,SAAG,MAAM,WAAW;AACpB,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AACA,WAAS,UAAUA,OAAM;AACvB,iBAAa,GAAG,QAAQ,GAAG,KAAK;AAChC,WAAO,KAAKA,SAAQ,MAAM,YAAY,UAAU;AAAA,EAClD;AACA,WAAS,iBAAiBA,OAAM;AAC9B,iBAAa,GAAG,QAAQ,GAAG,KAAK;AAChC,WAAO,KAAKA,SAAQ,MAAM,YAAY,iBAAiB;AAAA,EACzD;AACA,WAAS,YAAY,SAAS;AAC5B,WAAO,SAASA,OAAM;AACpB,UAAIA,SAAQ;AAAK,eAAO,KAAK,UAAU,gBAAgB,MAAM;AAAA,eACpDA,SAAQ,cAAc;AAAM,eAAO,KAAK,eAAe,UAAU,uBAAuB,kBAAkB;AAAA;AAC9G,eAAO,KAAK,UAAU,oBAAoB,UAAU;AAAA,IAC3D;AAAA,EACF;AACA,WAAS,OAAO,GAAG,OAAO;AACxB,QAAI,SAAS,UAAU;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,kBAAkB;AAAA,IAAG;AAAA,EACnF;AACA,WAAS,cAAc,GAAG,OAAO;AAC/B,QAAI,SAAS,UAAU;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,oBAAoB;AAAA,IAAG;AAAA,EACrF;AACA,WAAS,WAAWA,OAAM;AACxB,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,SAAS;AAC9C,WAAO,KAAK,oBAAoB,OAAO,GAAG,GAAG,MAAM;AAAA,EACrD;AACA,WAAS,SAASA,OAAM;AACtB,QAAIA,SAAQ,YAAY;AAAC,SAAG,SAAS;AAAY,aAAO,KAAK;AAAA,IAAE;AAAA,EACjE;AACA,WAAS,QAAQA,OAAM,OAAO;AAC5B,QAAIA,SAAQ,SAAS;AACnB,SAAG,SAAS;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB,WAAWA,SAAQ,cAAc,GAAG,SAAS,WAAW;AACtD,SAAG,SAAS;AACZ,UAAI,SAAS,SAAS,SAAS;AAAO,eAAO,KAAK,YAAY;AAC9D,UAAI;AACJ,UAAI,QAAQ,GAAG,MAAM,cAAc,GAAG,OAAO,UAAU,IAAI,GAAG,OAAO,MAAM,YAAY,KAAK;AAC1F,WAAG,MAAM,aAAa,GAAG,OAAO,MAAM,EAAE,CAAC,EAAE;AAC7C,aAAO,KAAK,SAAS;AAAA,IACvB,WAAWA,SAAQ,YAAYA,SAAQ,UAAU;AAC/C,SAAG,SAAS,aAAa,aAAc,GAAG,QAAQ;AAClD,aAAO,KAAK,SAAS;AAAA,IACvB,WAAWA,SAAQ,kBAAkB;AACnC,aAAO,KAAK,SAAS;AAAA,IACvB,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,SAAG,SAAS;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB,WAAWA,SAAQ,KAAK;AACtB,aAAO,KAAK,YAAY,WAAW,OAAO,GAAG,GAAG,SAAS;AAAA,IAC3D,WAAWA,SAAQ,UAAU;AAC3B,aAAO,KAAK,mBAAmB,SAAS;AAAA,IAC1C,WAAW,SAAS,KAAK;AACvB,SAAG,SAAS;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB,WAAWA,SAAQ,KAAK;AACtB,aAAO,KAAK,SAAS;AAAA,IACvB;AAAA,EACF;AACA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,SAAQ;AAAY,aAAO,KAAK,SAAS;AAC7C,OAAG,SAAS;AACZ,WAAO,KAAK,WAAW;AAAA,EACzB;AACA,WAAS,UAAUA,OAAM;AACvB,QAAIA,SAAQ;AAAK,aAAO,KAAK,iBAAiB;AAC9C,QAAIA,SAAQ;AAAK,aAAO,KAAK,WAAW;AAAA,EAC1C;AACA,WAAS,SAAS,MAAM,KAAK,KAAK;AAChC,aAAS,QAAQA,OAAM,OAAO;AAC5B,UAAI,MAAM,IAAI,QAAQA,KAAI,IAAI,KAAKA,SAAQ,KAAK;AAC9C,YAAI,MAAM,GAAG,MAAM;AACnB,YAAI,IAAI,QAAQ;AAAQ,cAAI,OAAO,IAAI,OAAO,KAAK;AACnD,eAAO,KAAK,SAASA,OAAMK,QAAO;AAChC,cAAIL,SAAQ,OAAOK,UAAS;AAAK,mBAAO,KAAK;AAC7C,iBAAO,KAAK,IAAI;AAAA,QAClB,GAAG,OAAO;AAAA,MACZ;AACA,UAAIL,SAAQ,OAAO,SAAS;AAAK,eAAO,KAAK;AAC7C,UAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;AAAI,eAAO,KAAK,IAAI;AAClD,aAAO,KAAK,OAAO,GAAG,CAAC;AAAA,IACzB;AACA,WAAO,SAASA,OAAM,OAAO;AAC3B,UAAIA,SAAQ,OAAO,SAAS;AAAK,eAAO,KAAK;AAC7C,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,aAAa,MAAM,KAAK,MAAM;AACrC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,SAAG,GAAG,KAAK,UAAU,CAAC,CAAC;AACzB,WAAO,KAAK,QAAQ,KAAK,IAAI,GAAG,SAAS,MAAM,GAAG,GAAG,MAAM;AAAA,EAC7D;AACA,WAAS,MAAMA,OAAM;AACnB,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,WAAO,KAAK,WAAW,KAAK;AAAA,EAC9B;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAI,MAAM;AACR,UAAIA,SAAQ;AAAK,eAAO,KAAK,QAAQ;AACrC,UAAI,SAAS;AAAK,eAAO,KAAK,SAAS;AAAA,IACzC;AAAA,EACF;AACA,WAAS,cAAcA,OAAM,OAAO;AAClC,QAAI,SAASA,SAAQ,OAAO,SAAS;AAAO,aAAO,KAAK,QAAQ;AAAA,EAClE;AACA,WAAS,aAAaA,OAAM;AAC1B,QAAI,QAAQA,SAAQ,KAAK;AACvB,UAAI,GAAG,OAAO,MAAM,kBAAkB,KAAK;AAAG,eAAO,KAAK,YAAY,MAAM,QAAQ;AAAA;AAC/E,eAAO,KAAK,QAAQ;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,KAAK,GAAG,OAAO;AACtB,QAAI,SAAS,MAAM;AACjB,SAAG,SAAS;AACZ,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,WAAS,SAASA,OAAM,OAAO;AAC7B,QAAI,SAAS,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS,YAAY;AACpF,SAAG,SAAS;AACZ,aAAO,KAAK,SAAS,WAAW,oBAAoB,QAAQ;AAAA,IAC9D;AACA,QAAIA,SAAQ,cAAc,SAAS,QAAQ;AACzC,SAAG,SAAS;AACZ,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,QAAI,SAAS,OAAO,SAAS;AAAK,aAAO,KAAK,QAAQ;AACtD,QAAIA,SAAQ,YAAYA,SAAQ,YAAYA,SAAQ;AAAQ,aAAO,KAAK,SAAS;AACjF,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,KAAK,GAAG,GAAG,QAAQ,SAAS;AAC1F,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,WAAW,QAAQ,SAAS;AACvE,QAAIA,SAAQ;AAAK,aAAO,KAAK,SAAS,SAAS,GAAG,GAAG,iBAAiB,SAAS;AAC/E,QAAIA,SAAQ;AAAK,aAAO,KAAK,SAAS,UAAU,GAAG,GAAG,QAAQ;AAC9D,QAAIA,SAAQ;AAAS,aAAO,KAAK,WAAW,SAAS;AAAA,EACvD;AACA,WAAS,gBAAgBA,OAAM;AAC7B,QAAIA,SAAQ;AAAM,aAAO,KAAK,QAAQ;AAAA,EACxC;AACA,WAAS,UAAUA,OAAM;AACvB,QAAIA,MAAK,MAAM,UAAU;AAAG,aAAO,KAAK;AACxC,QAAIA,SAAQ,OAAOA,SAAQ;AAAK,aAAO,KAAK,SAAS;AACrD,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AACA,WAAS,SAASA,OAAM,OAAO;AAC7B,QAAIA,SAAQ,cAAc,GAAG,SAAS,WAAW;AAC/C,SAAG,SAAS;AACZ,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAW,SAAS,OAAOA,SAAQ,YAAYA,SAAQ,UAAU;AAC/D,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAWA,SAAQ,KAAK;AACtB,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAWA,SAAQ,KAAK;AACtB,aAAO,KAAK,OAAO,UAAU,GAAG,eAAe,OAAO,GAAG,GAAG,QAAQ;AAAA,IACtE,WAAWA,SAAQ,KAAK;AACtB,aAAO,KAAK,cAAc,QAAQ;AAAA,IACpC,WAAW,CAACA,MAAK,MAAM,YAAY,GAAG;AACpC,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAIA,SAAQ;AAAS,aAAO,KAAK;AACjC,QAAI,MAAM,MAAM,MAAM,SAAS,CAAC,KAAK;AAAM,aAAO,KAAK,SAAS;AAChE,WAAO,KAAK,UAAU,iBAAiB;AAAA,EACzC;AACA,WAAS,kBAAkBA,OAAM;AAChC,QAAIA,SAAQ,KAAK;AACd,SAAG,SAAS;AACZ,SAAG,MAAM,WAAW;AACpB,aAAO,KAAK,SAAS;AAAA,IACvB;AAAA,EACF;AACA,WAAS,QAAQA,OAAM,OAAO;AAC5B,QAAIA,SAAQ,cAAc,GAAG,OAAO,MAAM,YAAY,KAAK,KAAK,SAAS;AAAK,aAAO,KAAK,OAAO;AACjG,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ;AACrC,QAAIA,SAAQ;AAAU,aAAO,KAAK,OAAO;AACzC,WAAO,KAAK,QAAQ;AAAA,EACtB;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAI,SAAS;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,SAAS;AACtF,QAAI,SAAS,OAAOA,SAAQ,OAAO,SAAS;AAAK,aAAO,KAAK,QAAQ;AACrE,QAAIA,SAAQ;AAAK,aAAO,KAAK,UAAU,OAAO,GAAG,GAAG,SAAS;AAC7D,QAAI,SAAS,aAAa,SAAS,cAAc;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,QAAQ;AAAA,IAAE;AAChG,QAAI,SAAS;AAAK,aAAO,KAAK,UAAU,OAAO,GAAG,GAAG,QAAQ;AAAA,EAC/D;AACA,WAAS,cAAc,GAAG,OAAO;AAC/B,QAAI,SAAS;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,SAAS;AAAA,EACxF;AACA,WAAS,YAAY;AACnB,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AACA,WAAS,iBAAiB,GAAG,OAAO;AAClC,QAAI,SAAS;AAAK,aAAO,KAAK,QAAQ;AAAA,EACxC;AACA,WAAS,OAAO,GAAG,OAAO;AACxB,QAAI,SAAS,QAAQ;AAAC,SAAG,SAAS;AAAW,aAAO,KAAK,OAAO;AAAA,IAAC;AACjE,WAAO,KAAK,SAAS,WAAW,aAAa,UAAU;AAAA,EACzD;AACA,WAAS,QAAQA,OAAM,OAAO;AAC5B,QAAI,QAAQ,WAAW,KAAK,GAAG;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,OAAO;AAAA,IAAE;AAC7E,QAAIA,SAAQ,YAAY;AAAE,eAAS,KAAK;AAAG,aAAO,KAAK;AAAA,IAAG;AAC1D,QAAIA,SAAQ;AAAU,aAAO,KAAK,OAAO;AACzC,QAAIA,SAAQ;AAAK,aAAO,aAAa,YAAY,GAAG;AACpD,QAAIA,SAAQ;AAAK,aAAO,aAAa,aAAa,GAAG;AAAA,EACvD;AACA,WAAS,YAAYA,OAAM,OAAO;AAChC,QAAIA,SAAQ,cAAc,CAAC,GAAG,OAAO,MAAM,SAAS,KAAK,GAAG;AAC1D,eAAS,KAAK;AACd,aAAO,KAAK,WAAW;AAAA,IACzB;AACA,QAAIA,SAAQ;AAAY,SAAG,SAAS;AACpC,QAAIA,SAAQ;AAAU,aAAO,KAAK,OAAO;AACzC,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,QAAIA,SAAQ;AAAK,aAAO,KAAK,YAAY,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,WAAW;AAC9E,WAAO,KAAK,OAAO,GAAG,GAAG,SAAS,WAAW;AAAA,EAC/C;AACA,WAAS,aAAa;AACpB,WAAO,KAAK,SAAS,WAAW;AAAA,EAClC;AACA,WAAS,YAAY,OAAO,OAAO;AACjC,QAAI,SAAS;AAAK,aAAO,KAAK,iBAAiB;AAAA,EACjD;AACA,WAAS,WAAWA,OAAM;AACxB,QAAIA,SAAQ;AAAK,aAAO,KAAK,MAAM;AAAA,EACrC;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAIA,SAAQ,eAAe,SAAS;AAAQ,aAAO,KAAK,QAAQ,QAAQ,MAAM,GAAG,WAAW,MAAM;AAAA,EACpG;AACA,WAAS,QAAQA,OAAM,OAAO;AAC5B,QAAI,SAAS;AAAS,aAAO,KAAK,OAAO;AACzC,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,UAAU,MAAM;AAAA,EAC7D;AACA,WAAS,SAASA,OAAM;AACtB,QAAIA,SAAQ;AAAO,aAAO,KAAK,QAAQ,QAAQ;AAC/C,QAAIA,SAAQ;AAAY,aAAO,KAAK,QAAQ;AAC5C,WAAO,KAAK,QAAQ;AAAA,EACtB;AACA,WAAS,SAASA,OAAM,OAAO;AAC7B,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ;AACrC,QAAI,SAAS,QAAQ,SAAS,MAAM;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,YAAY,QAAQ;AAAA,IAAE;AAC/F,WAAO,KAAK,YAAY,QAAQ;AAAA,EAClC;AACA,WAAS,YAAYA,OAAM,OAAO;AAChC,QAAI,SAAS,KAAK;AAAC,SAAG,SAAS;AAAW,aAAO,KAAK,WAAW;AAAA,IAAE;AACnE,QAAIA,SAAQ,YAAY;AAAC,eAAS,KAAK;AAAG,aAAO,KAAK,WAAW;AAAA,IAAE;AACnE,QAAIA,SAAQ;AAAK,aAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,cAAc,WAAW,UAAU;AAC1H,QAAI,QAAQ,SAAS;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,WAAW;AAAA,EACnG;AACA,WAAS,aAAaA,OAAM,OAAO;AACjC,QAAI,SAAS,KAAK;AAAC,SAAG,SAAS;AAAW,aAAO,KAAK,YAAY;AAAA,IAAE;AACpE,QAAIA,SAAQ,YAAY;AAAC,eAAS,KAAK;AAAG,aAAO,KAAK,YAAY;AAAA,IAAE;AACpE,QAAIA,SAAQ;AAAK,aAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,cAAc,UAAU;AAC/G,QAAI,QAAQ,SAAS;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,YAAY;AAAA,EACpG;AACA,WAAS,SAASA,OAAM,OAAO;AAC7B,QAAIA,SAAQ,aAAaA,SAAQ,YAAY;AAC3C,SAAG,SAAS;AACZ,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAW,SAAS,KAAK;AACvB,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,MAAM;AAAA,IAC5D;AAAA,EACF;AACA,WAAS,OAAOA,OAAM,OAAO;AAC3B,QAAI,SAAS;AAAK,WAAK,YAAY,MAAM;AACzC,QAAIA,SAAQ;AAAU,aAAO,KAAK,MAAM;AACxC,QAAI,QAAQ,WAAW,KAAK,GAAG;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,MAAM;AAAA,IAAG;AAC7E,QAAI,QAAQA,SAAQ;AAAQ,aAAO,KAAK,WAAW,WAAW;AAC9D,WAAO,KAAK,SAAS,WAAW,WAAW;AAAA,EAC7C;AACA,WAAS,gBAAgBA,OAAM,OAAO;AAEpC,QAAIA,SAAQ;AAAY,aAAO,UAAUA,OAAM,KAAK;AACpD,WAAO,eAAeA,OAAM,KAAK;AAAA,EACnC;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAIA,SAAQ,YAAY;AAAC,eAAS,KAAK;AAAG,aAAO,KAAK,cAAc;AAAA,IAAE;AAAA,EACxE;AACA,WAAS,eAAeA,OAAM,OAAO;AACnC,QAAI,SAAS;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,cAAc;AAC5F,QAAI,SAAS,aAAa,SAAS,gBAAiB,QAAQA,SAAQ,KAAM;AACxE,UAAI,SAAS;AAAc,WAAG,SAAS;AACvC,aAAO,KAAK,OAAO,WAAW,YAAY,cAAc;AAAA,IAC1D;AACA,QAAIA,SAAQ;AAAK,aAAO,KAAK,QAAQ,GAAG,GAAG,WAAW,MAAM;AAAA,EAC9D;AACA,WAAS,UAAUA,OAAM,OAAO;AAC9B,QAAIA,SAAQ,WACPA,SAAQ,eACP,SAAS,YAAY,SAAS,SAAS,SAAS,SAAU,QAAQ,WAAW,KAAK,MACnF,GAAG,OAAO,MAAM,0BAA0B,KAAK,GAAI;AACtD,SAAG,SAAS;AACZ,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,QAAIA,SAAQ,cAAc,GAAG,SAAS,WAAW;AAC/C,SAAG,SAAS;AACZ,aAAO,KAAK,YAAY,SAAS;AAAA,IACnC;AACA,QAAIA,SAAQ,YAAYA,SAAQ;AAAU,aAAO,KAAK,YAAY,SAAS;AAC3E,QAAIA,SAAQ;AACV,aAAO,KAAK,YAAY,WAAW,OAAO,GAAG,GAAG,YAAY,SAAS;AACvE,QAAI,SAAS,KAAK;AAChB,SAAG,SAAS;AACZ,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,QAAI,QAAQA,SAAQ;AAAK,aAAO,KAAK,cAAc,SAAS;AAC5D,QAAIA,SAAQ,OAAOA,SAAQ;AAAK,aAAO,KAAK,SAAS;AACrD,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,QAAI,SAAS;AAAK,aAAO,KAAK,YAAY,SAAS;AAAA,EACrD;AACA,WAAS,WAAWA,OAAM,OAAO;AAC/B,QAAI,SAAS,OAAO,SAAS;AAAK,aAAO,KAAK,UAAU;AACxD,QAAIA,SAAQ;AAAK,aAAO,KAAK,UAAU,WAAW;AAClD,QAAI,SAAS;AAAK,aAAO,KAAK,iBAAiB;AAC/C,QAAI,UAAU,GAAG,MAAM,QAAQ,MAAM,cAAc,WAAW,QAAQ,QAAQ;AAC9E,WAAO,KAAK,cAAc,eAAe,WAAW;AAAA,EACtD;AACA,WAAS,YAAYA,OAAM,OAAO;AAChC,QAAI,SAAS,KAAK;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,WAAW,OAAO,GAAG,CAAC;AAAA,IAAG;AAChF,QAAI,SAAS,WAAW;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,YAAY,OAAO,GAAG,CAAC;AAAA,IAAG;AACvF,QAAIA,SAAQ;AAAK,aAAO,KAAK,SAAS,aAAa,GAAG,GAAG,WAAW,OAAO,GAAG,CAAC;AAC/E,WAAO,KAAK,SAAS;AAAA,EACvB;AACA,WAAS,YAAYA,OAAM,OAAO;AAChC,QAAI,SAAS,MAAM;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,OAAO,UAAU,CAAC;AAAA,IAAG;AAC7E,QAAIA,SAAQ;AAAY,aAAO,KAAK,mBAAmB,WAAW;AAAA,EACpE;AACA,WAAS,YAAYA,OAAM;AACzB,QAAIA,SAAQ;AAAU,aAAO,KAAK;AAClC,QAAIA,SAAQ;AAAK,aAAO,KAAK,UAAU;AACvC,QAAIA,SAAQ;AAAK,aAAO,KAAK,kBAAkB;AAC/C,WAAO,KAAK,YAAY,kBAAkB,SAAS;AAAA,EACrD;AACA,WAAS,WAAWA,OAAM,OAAO;AAC/B,QAAIA,SAAQ;AAAK,aAAO,aAAa,YAAY,GAAG;AACpD,QAAIA,SAAQ;AAAY,eAAS,KAAK;AACtC,QAAI,SAAS;AAAK,SAAG,SAAS;AAC9B,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,WAAS,iBAAiBA,OAAM;AAC9B,QAAIA,SAAQ;AAAK,aAAO,KAAK,YAAY,gBAAgB;AAAA,EAC3D;AACA,WAAS,QAAQ,OAAO,OAAO;AAC7B,QAAI,SAAS,MAAM;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,UAAU;AAAA,IAAG;AAAA,EACvE;AACA,WAAS,UAAU,OAAO,OAAO;AAC/B,QAAI,SAAS,QAAQ;AAAE,SAAG,SAAS;AAAW,aAAO,KAAK,UAAU;AAAA,IAAG;AAAA,EACzE;AACA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,SAAQ;AAAK,aAAO,KAAK;AAC7B,WAAO,KAAK,SAAS,mBAAmB,GAAG,CAAC;AAAA,EAC9C;AACA,WAAS,UAAU;AACjB,WAAO,KAAK,QAAQ,MAAM,GAAG,SAAS,OAAO,GAAG,GAAG,QAAQ,GAAG,GAAG,SAAS,YAAY,GAAG,GAAG,QAAQ,MAAM;AAAA,EAC5G;AACA,WAAS,aAAa;AACpB,WAAO,KAAK,SAAS,WAAW;AAAA,EAClC;AAEA,WAAS,qBAAqB,OAAO,WAAW;AAC9C,WAAO,MAAM,YAAY,cAAc,MAAM,YAAY,OACvD,eAAe,KAAK,UAAU,OAAO,CAAC,CAAC,KACvC,OAAO,KAAK,UAAU,OAAO,CAAC,CAAC;AAAA,EACnC;AAEA,WAAS,kBAAkB,QAAQ,OAAO,QAAQ;AAChD,WAAO,MAAM,YAAY,aACvB,iFAAiF,KAAK,MAAM,QAAQ,KACnG,MAAM,YAAY,WAAW,SAAS,KAAK,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,UAAU,EAAE,CAAC;AAAA,EAClG;AAIA,SAAO;AAAA,IACL,MAAM,aAAa;AAAA,IAEnB,YAAY,SAAS,YAAY;AAC/B,UAAI,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,IAAI,CAAC;AAAA,QACL,SAAS,IAAI,UAAU,CAAC,YAAY,GAAG,SAAS,KAAK;AAAA,QACrD,WAAW,aAAa;AAAA,QACxB,SAAS,aAAa,aAAa,IAAI,QAAQ,MAAM,MAAM,KAAK;AAAA,QAChE,UAAU;AAAA,MACZ;AACA,UAAI,aAAa,cAAc,OAAO,aAAa,cAAc;AAC/D,cAAM,aAAa,aAAa;AAClC,aAAO;AAAA,IACT;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,OAAO,IAAI,GAAG;AAChB,YAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,gBAAM,QAAQ,QAAQ;AACxB,cAAM,WAAW,OAAO,YAAY;AACpC,qBAAa,QAAQ,KAAK;AAAA,MAC5B;AACA,UAAI,MAAM,YAAY,gBAAgB,OAAO,SAAS;AAAG,eAAO;AAChE,UAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,UAAI,QAAQ;AAAW,eAAO;AAC9B,YAAM,WAAW,QAAQ,eAAe,WAAW,QAAQ,WAAW,QAAQ,WAAW;AACzF,aAAO,QAAQ,OAAO,OAAO,MAAM,SAAS,MAAM;AAAA,IACpD;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAWE,KAAI;AACrC,UAAI,MAAM,YAAY,gBAAgB,MAAM,YAAY;AAAY,eAAO;AAC3E,UAAI,MAAM,YAAY;AAAW,eAAO;AACxC,UAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,UAAU,MAAM,SAAS;AAE3E,UAAI,CAAC,aAAa,KAAK,SAAS;AAAG,iBAAS,IAAI,MAAM,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAChF,cAAI,IAAI,MAAM,GAAG,CAAC;AAClB,cAAI,KAAK;AAAQ,sBAAU,QAAQ;AAAA,mBAC1B,KAAK,aAAa,KAAK;AAAY;AAAA,QAC9C;AACA,cAAQ,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,YAC1C,aAAa,QAAS,MAAM,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC,OAClC,OAAO,sBAAsB,OAAO,yBACrC,CAAC,mBAAmB,KAAK,SAAS;AAC7D,kBAAU,QAAQ;AACpB,UAAI,mBAAmB,QAAQ,QAAQ,OAAO,QAAQ,KAAK,QAAQ;AACjE,kBAAU,QAAQ;AACpB,UAAIF,QAAO,QAAQ,MAAM,UAAU,aAAaA;AAEhD,UAAIA,SAAQ;AAAU,eAAO,QAAQ,YAAY,MAAM,YAAY,cAAc,MAAM,YAAY,MAAM,QAAQ,KAAK,SAAS,IAAI;AAAA,eAC1HA,SAAQ,UAAU,aAAa;AAAK,eAAO,QAAQ;AAAA,eACnDA,SAAQ;AAAQ,eAAO,QAAQ,WAAWE,IAAG;AAAA,eAC7CF,SAAQ;AACf,eAAO,QAAQ,YAAY,qBAAqB,OAAO,SAAS,IAAI,mBAAmBE,IAAG,OAAO;AAAA,eAC1F,QAAQ,QAAQ,YAAY,CAAC,WAAW,aAAa,sBAAsB;AAClF,eAAO,QAAQ,YAAY,sBAAsB,KAAK,SAAS,IAAIA,IAAG,OAAO,IAAIA,IAAG;AAAA,eAC7E,QAAQ;AAAO,eAAO,QAAQ,UAAU,UAAU,IAAI;AAAA;AAC1D,eAAO,QAAQ,YAAY,UAAU,IAAIA,IAAG;AAAA,IACnD;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe;AAAA,MACf,eAAe,WAAW,SAAY,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,MACnF,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,MACxD,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEO,IAAM,aAAa,aAAa,EAAC,MAAM,aAAY,CAAC;AACpD,IAAM,OAAO,aAAa,EAAC,MAAM,QAAQ,MAAM,KAAI,CAAC;AACpD,IAAM,SAAS,aAAa,EAAC,MAAM,QAAQ,QAAQ,KAAI,CAAC;AACxD,IAAM,aAAa,aAAa,EAAC,MAAM,cAAc,YAAY,KAAI,CAAC;", "names": ["type", "cont", "cx", "content", "block", "value"]}