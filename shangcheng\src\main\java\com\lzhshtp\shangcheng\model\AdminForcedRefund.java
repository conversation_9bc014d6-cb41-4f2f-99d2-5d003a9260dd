package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 管理员强制退款实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_admin_forced_refunds")
public class AdminForcedRefund {
    
    @TableId(value = "lzhshtp_forced_refund_id", type = IdType.AUTO)
    private Long forcedRefundId;
    
    @TableField("lzhshtp_refund_request_id")
    private Long refundRequestId;
    
    @TableField("lzhshtp_seller_id")
    private Long sellerId;
    
    @TableField("lzhshtp_required_amount")
    private BigDecimal requiredAmount;
    
    @TableField("lzhshtp_status")
    private String status;
    
    @TableField("lzhshtp_admin_id")
    private Long adminId;
    
    @TableField("lzhshtp_admin_note")
    private String adminNote;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    @TableField("lzhshtp_completed_time")
    private LocalDateTime completedTime;
    
    /**
     * 强制退款状态枚举
     */
    public static class ForcedRefundStatus {
        public static final String PENDING = "pending";
        public static final String COMPLETED = "completed";
    }
}
