package com.lzhshtp.shangcheng.config;

import com.aliyun.imageaudit20191230.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云图片审核配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.content-security")
@Data
public class AliyunContentSecurityConfig {

    private String accessKeyId;
    private String accessKeySecret;
    private String endpoint;
    private String regionId;

    /**
     * 创建阿里云图片审核客户端
     */
    @Bean
    public Client imageAuditClient() throws Exception {
        // 验证配置是否完整
        if (this.accessKeyId == null || this.accessKeyId.trim().isEmpty()) {
            throw new IllegalArgumentException("阿里云 AccessKeyId 未配置，请检查 application.yml 中的 aliyun.content-security.access-key-id 配置");
        }

        if (this.accessKeySecret == null || this.accessKeySecret.trim().isEmpty()) {
            throw new IllegalArgumentException("阿里云 AccessKeySecret 未配置，请检查 application.yml 中的 aliyun.content-security.access-key-secret 配置");
        }

        if (this.endpoint == null || this.endpoint.trim().isEmpty()) {
            throw new IllegalArgumentException("阿里云 Endpoint 未配置，请检查 application.yml 中的 aliyun.content-security.endpoint 配置");
        }

        // 使用注入的配置属性创建客户端
        Config config = new Config()
            .setAccessKeyId(this.accessKeyId.trim())
            .setAccessKeySecret(this.accessKeySecret.trim());
        config.endpoint = this.endpoint.trim();

        // 添加日志以便调试
        System.out.println("=== 阿里云图片审核配置 ===");
        System.out.println("AccessKeyId: " + (this.accessKeyId.length() > 8 ? this.accessKeyId.substring(0, 8) + "****" : "****"));
        System.out.println("Endpoint: " + this.endpoint);
        System.out.println("RegionId: " + (this.regionId != null ? this.regionId : "cn-shanghai"));
        System.out.println("配置来源: application.yml -> aliyun.content-security");
        System.out.println("========================");

        return new Client(config);
    }
}
