package com.lzhshtp.shangcheng.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Component
public class OssUtil {
    private static final Logger logger = LoggerFactory.getLogger(OssUtil.class);
    
    private final OSS ossClient;
    
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    
    @Value("${aliyun.oss.endpoint}")
    private String endpoint;
    
    public OssUtil(OSS ossClient) {
        this.ossClient = ossClient;
    }
    
    public String uploadFile(MultipartFile file, String directory) throws IOException {
        logger.info("开始上传文件到OSS，原始文件名: {}, 目录: {}", file.getOriginalFilename(), directory);
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = directory + "/" + UUID.randomUUID().toString() + extension;
        
        logger.info("生成的OSS文件名: {}", fileName);
        
        // 创建上传文件的元信息
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());
        metadata.setContentLength(file.getSize());
        
        // 上传文件
        try (InputStream inputStream = file.getInputStream()) {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream, metadata);
            ossClient.putObject(putObjectRequest);
            logger.info("文件上传到OSS成功");
        }
        
        // 返回文件访问路径
        String fileUrl = "https://" + bucketName + "." + endpoint + "/" + fileName;
        logger.info("文件访问URL: {}", fileUrl);
        return fileUrl;
    }
    
    public void deleteFile(String fileUrl) {
        try {
            // 从URL中提取对象名称
            // URL格式通常为：https://bucketName.endpoint/objectName
            if (fileUrl == null || fileUrl.isEmpty()) {
                logger.warn("尝试删除空的文件URL");
                return;
            }
            
            logger.info("准备删除OSS文件，URL: {}", fileUrl);
            
            // 解析URL获取对象名称
            String objectName;
            if (fileUrl.contains("://")) {
                // 处理完整URL
                String urlWithoutProtocol = fileUrl.split("://")[1];
                // 跳过域名部分
                int firstSlashIndex = urlWithoutProtocol.indexOf('/');
                if (firstSlashIndex != -1) {
                    objectName = urlWithoutProtocol.substring(firstSlashIndex + 1);
                    logger.info("从URL中解析出对象名称: {}", objectName);
                } else {
                    logger.warn("无效的URL格式，无法提取对象名称: {}", fileUrl);
                    return; // 无效URL
                }
            } else {
                // 假设已经是对象名称
                objectName = fileUrl;
                logger.info("直接使用提供的对象名称: {}", objectName);
            }
            
            // 删除对象
            if (objectName != null && !objectName.isEmpty()) {
                logger.info("删除OSS文件: bucket={}, objectName={}", bucketName, objectName);
        ossClient.deleteObject(bucketName, objectName);
                logger.info("OSS文件删除成功");
            }
        } catch (Exception e) {
            logger.error("删除OSS文件失败: {}", e.getMessage(), e);
        }
    }
} 