{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/smalltalk.js"], "sourcesContent": ["var specialChars = /[+\\-\\/\\\\*~<>=@%|&?!.,:;^]/;\nvar keywords = /true|false|nil|self|super|thisContext/;\n\nvar Context = function(tokenizer, parent) {\n  this.next = tokenizer;\n  this.parent = parent;\n};\n\nvar Token = function(name, context, eos) {\n  this.name = name;\n  this.context = context;\n  this.eos = eos;\n};\n\nvar State = function() {\n  this.context = new Context(next, null);\n  this.expectVariable = true;\n  this.indentation = 0;\n  this.userIndentationDelta = 0;\n};\n\nState.prototype.userIndent = function(indentation, indentUnit) {\n  this.userIndentationDelta = indentation > 0 ? (indentation / indentUnit - this.indentation) : 0;\n};\n\nvar next = function(stream, context, state) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '\"') {\n    token = nextComment(stream, new Context(nextComment, context));\n\n  } else if (aChar === '\\'') {\n    token = nextString(stream, new Context(nextString, context));\n\n  } else if (aChar === '#') {\n    if (stream.peek() === '\\'') {\n      stream.next();\n      token = nextSymbol(stream, new Context(nextSymbol, context));\n    } else {\n      if (stream.eatWhile(/[^\\s.{}\\[\\]()]/))\n        token.name = 'string.special';\n      else\n        token.name = 'meta';\n    }\n\n  } else if (aChar === '$') {\n    if (stream.next() === '<') {\n      stream.eatWhile(/[^\\s>]/);\n      stream.next();\n    }\n    token.name = 'string.special';\n\n  } else if (aChar === '|' && state.expectVariable) {\n    token.context = new Context(nextTemporaries, context);\n\n  } else if (/[\\[\\]{}()]/.test(aChar)) {\n    token.name = 'bracket';\n    token.eos = /[\\[{(]/.test(aChar);\n\n    if (aChar === '[') {\n      state.indentation++;\n    } else if (aChar === ']') {\n      state.indentation = Math.max(0, state.indentation - 1);\n    }\n\n  } else if (specialChars.test(aChar)) {\n    stream.eatWhile(specialChars);\n    token.name = 'operator';\n    token.eos = aChar !== ';'; // ; cascaded message expression\n\n  } else if (/\\d/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d]/);\n    token.name = 'number';\n\n  } else if (/[\\w_]/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d_]/);\n    token.name = state.expectVariable ? (keywords.test(stream.current()) ? 'keyword' : 'variable') : null;\n\n  } else {\n    token.eos = state.expectVariable;\n  }\n\n  return token;\n};\n\nvar nextComment = function(stream, context) {\n  stream.eatWhile(/[^\"]/);\n  return new Token('comment', stream.eat('\"') ? context.parent : context, true);\n};\n\nvar nextString = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextSymbol = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string.special', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextTemporaries = function(stream, context) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '|') {\n    token.context = context.parent;\n    token.eos = true;\n\n  } else {\n    stream.eatWhile(/[^|]/);\n    token.name = 'variable';\n  }\n\n  return token;\n};\n\nexport const smalltalk = {\n  name: \"smalltalk\",\n\n  startState: function() {\n    return new State;\n  },\n\n  token: function(stream, state) {\n    state.userIndent(stream.indentation(), stream.indentUnit);\n\n    if (stream.eatSpace()) {\n      return null;\n    }\n\n    var token = state.context.next(stream, state.context, state);\n    state.context = token.context;\n    state.expectVariable = token.eos;\n\n    return token.name;\n  },\n\n  blankLine: function(state, indentUnit) {\n    state.userIndent(0, indentUnit);\n  },\n\n  indent: function(state, textAfter, cx) {\n    var i = state.context.next === next && textAfter && textAfter.charAt(0) === ']' ? -1 : state.userIndentationDelta;\n    return (state.indentation + i) * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\]$/\n  }\n}\n"], "mappings": ";;;AAAA,IAAI,eAAe;AACnB,IAAI,WAAW;AAEf,IAAI,UAAU,SAAS,WAAW,QAAQ;AACxC,OAAK,OAAO;AACZ,OAAK,SAAS;AAChB;AAEA,IAAI,QAAQ,SAAS,MAAM,SAAS,KAAK;AACvC,OAAK,OAAO;AACZ,OAAK,UAAU;AACf,OAAK,MAAM;AACb;AAEA,IAAI,QAAQ,WAAW;AACrB,OAAK,UAAU,IAAI,QAAQ,MAAM,IAAI;AACrC,OAAK,iBAAiB;AACtB,OAAK,cAAc;AACnB,OAAK,uBAAuB;AAC9B;AAEA,MAAM,UAAU,aAAa,SAAS,aAAa,YAAY;AAC7D,OAAK,uBAAuB,cAAc,IAAK,cAAc,aAAa,KAAK,cAAe;AAChG;AAEA,IAAI,OAAO,SAAS,QAAQ,SAAS,OAAO;AAC1C,MAAI,QAAQ,IAAI,MAAM,MAAM,SAAS,KAAK;AAC1C,MAAI,QAAQ,OAAO,KAAK;AAExB,MAAI,UAAU,KAAK;AACjB,YAAQ,YAAY,QAAQ,IAAI,QAAQ,aAAa,OAAO,CAAC;AAAA,EAE/D,WAAW,UAAU,KAAM;AACzB,YAAQ,WAAW,QAAQ,IAAI,QAAQ,YAAY,OAAO,CAAC;AAAA,EAE7D,WAAW,UAAU,KAAK;AACxB,QAAI,OAAO,KAAK,MAAM,KAAM;AAC1B,aAAO,KAAK;AACZ,cAAQ,WAAW,QAAQ,IAAI,QAAQ,YAAY,OAAO,CAAC;AAAA,IAC7D,OAAO;AACL,UAAI,OAAO,SAAS,gBAAgB;AAClC,cAAM,OAAO;AAAA;AAEb,cAAM,OAAO;AAAA,IACjB;AAAA,EAEF,WAAW,UAAU,KAAK;AACxB,QAAI,OAAO,KAAK,MAAM,KAAK;AACzB,aAAO,SAAS,QAAQ;AACxB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,OAAO;AAAA,EAEf,WAAW,UAAU,OAAO,MAAM,gBAAgB;AAChD,UAAM,UAAU,IAAI,QAAQ,iBAAiB,OAAO;AAAA,EAEtD,WAAW,aAAa,KAAK,KAAK,GAAG;AACnC,UAAM,OAAO;AACb,UAAM,MAAM,SAAS,KAAK,KAAK;AAE/B,QAAI,UAAU,KAAK;AACjB,YAAM;AAAA,IACR,WAAW,UAAU,KAAK;AACxB,YAAM,cAAc,KAAK,IAAI,GAAG,MAAM,cAAc,CAAC;AAAA,IACvD;AAAA,EAEF,WAAW,aAAa,KAAK,KAAK,GAAG;AACnC,WAAO,SAAS,YAAY;AAC5B,UAAM,OAAO;AACb,UAAM,MAAM,UAAU;AAAA,EAExB,WAAW,KAAK,KAAK,KAAK,GAAG;AAC3B,WAAO,SAAS,QAAQ;AACxB,UAAM,OAAO;AAAA,EAEf,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC9B,WAAO,SAAS,SAAS;AACzB,UAAM,OAAO,MAAM,iBAAkB,SAAS,KAAK,OAAO,QAAQ,CAAC,IAAI,YAAY,aAAc;AAAA,EAEnG,OAAO;AACL,UAAM,MAAM,MAAM;AAAA,EACpB;AAEA,SAAO;AACT;AAEA,IAAI,cAAc,SAAS,QAAQ,SAAS;AAC1C,SAAO,SAAS,MAAM;AACtB,SAAO,IAAI,MAAM,WAAW,OAAO,IAAI,GAAG,IAAI,QAAQ,SAAS,SAAS,IAAI;AAC9E;AAEA,IAAI,aAAa,SAAS,QAAQ,SAAS;AACzC,SAAO,SAAS,MAAM;AACtB,SAAO,IAAI,MAAM,UAAU,OAAO,IAAI,GAAI,IAAI,QAAQ,SAAS,SAAS,KAAK;AAC/E;AAEA,IAAI,aAAa,SAAS,QAAQ,SAAS;AACzC,SAAO,SAAS,MAAM;AACtB,SAAO,IAAI,MAAM,kBAAkB,OAAO,IAAI,GAAI,IAAI,QAAQ,SAAS,SAAS,KAAK;AACvF;AAEA,IAAI,kBAAkB,SAAS,QAAQ,SAAS;AAC9C,MAAI,QAAQ,IAAI,MAAM,MAAM,SAAS,KAAK;AAC1C,MAAI,QAAQ,OAAO,KAAK;AAExB,MAAI,UAAU,KAAK;AACjB,UAAM,UAAU,QAAQ;AACxB,UAAM,MAAM;AAAA,EAEd,OAAO;AACL,WAAO,SAAS,MAAM;AACtB,UAAM,OAAO;AAAA,EACf;AAEA,SAAO;AACT;AAEO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO,IAAI;AAAA,EACb;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAM,WAAW,OAAO,YAAY,GAAG,OAAO,UAAU;AAExD,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,MAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,KAAK;AAC3D,UAAM,UAAU,MAAM;AACtB,UAAM,iBAAiB,MAAM;AAE7B,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,WAAW,SAAS,OAAO,YAAY;AACrC,UAAM,WAAW,GAAG,UAAU;AAAA,EAChC;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,IAAI,MAAM,QAAQ,SAAS,QAAQ,aAAa,UAAU,OAAO,CAAC,MAAM,MAAM,KAAK,MAAM;AAC7F,YAAQ,MAAM,cAAc,KAAK,GAAG;AAAA,EACtC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,EACjB;AACF;", "names": []}