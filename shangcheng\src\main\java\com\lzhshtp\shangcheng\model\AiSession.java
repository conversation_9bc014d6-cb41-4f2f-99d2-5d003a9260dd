package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI会话实体类
 * 对应数据库表: tb_lzhshtp_ai_session
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_ai_session")
public class AiSession {

    /**
     * AI会话唯一标识ID(UUID格式)
     */
    @TableId(value = "zhshtp_id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 会话创建时间
     */
    @TableField("zhshtp_created_time")
    private LocalDateTime createdTime;

    /**
     * 会话最后编辑时间
     */
    @TableField("zhshtp_edited_time")
    private LocalDateTime editedTime;

    /**
     * 会话创建者用户ID，关联商城用户表
     */
    @TableField("zhshtp_creator_id")
    private String creatorId;

    /**
     * 会话最后编辑者用户ID
     */
    @TableField("zhshtp_editor_id")
    private String editorId;

    /**
     * 会话名称(如"商品咨询"、"订单查询"、"智能客服"等)
     */
    @TableField("zhshtp_name")
    private String name;

    /**
     * 会话下的消息列表(不存储在数据库中，用于业务逻辑)
     */
    @TableField(exist = false)
    private List<AiMessage> messages;

    /**
     * 会话类型枚举
     */
    public enum SessionType {
        CUSTOMER_SERVICE("customer_service", "智能客服"),
        PRODUCT_CONSULTATION("product_consultation", "商品咨询"),
        ORDER_INQUIRY("order_inquiry", "订单查询"),
        GENERAL_CHAT("general_chat", "通用对话");

        private final String code;
        private final String description;

        SessionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static SessionType fromCode(String code) {
            for (SessionType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return GENERAL_CHAT;
        }
    }
}
