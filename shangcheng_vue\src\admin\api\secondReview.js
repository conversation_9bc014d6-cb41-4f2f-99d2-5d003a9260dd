import request from '@/utils/request'

/**
 * 二度复审任务API
 */
export const secondReviewApi = {
  /**
   * 获取二度复审任务列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.status - 任务状态
   * @param {string} params.escalationReason - 升级原因
   * @param {number} params.productId - 商品ID
   * @param {number} params.reviewerId - 复审员ID
   */
  getTasks(params = {}) {
    return request.get('/admin/audit/second-review/tasks', { params })
  },

  /**
   * 获取二度复审统计数据
   */
  getStats() {
    return request.get('/admin/audit/second-review/stats')
  },

  /**
   * 认领二度复审任务
   * @param {number} taskId - 任务ID
   * @param {number} reviewerId - 复审员ID
   */
  claimTask(taskId, reviewerId) {
    return request.post(`/admin/audit/second-review/tasks/${taskId}/claim`, { reviewerId })
  },

  /**
   * 获取二度复审任务详情
   * @param {number} taskId - 任务ID
   */
  getTaskDetail(taskId) {
    return request.get(`/admin/audit/second-review/tasks/${taskId}`)
  },

  /**
   * 提交二度复审决策
   * @param {number} taskId - 任务ID
   * @param {Object} decision - 复审决策
   * @param {number} decision.reviewerId - 复审员ID
   * @param {string} decision.decision - 决策结果 (approved/rejected/request_materials)
   * @param {string} decision.comments - 复审意见
   * @param {string} decision.riskLevel - 风险评估 (low/medium/high)
   * @param {string} decision.requiredMaterials - 要求补充的材料
   */
  submitDecision(taskId, decision) {
    return request.post(`/admin/audit/second-review/tasks/${taskId}/decision`, decision)
  },

  /**
   * 获取二度复审任务的所有材料
   * @param {number} taskId - 任务ID
   */
  getTaskMaterials(taskId) {
    return request.get(`/admin/audit/second-review/tasks/${taskId}/materials`)
  }
}

export default secondReviewApi
