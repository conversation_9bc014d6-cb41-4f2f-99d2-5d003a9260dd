import {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
} from "./chunk-NKGDBH44.js";
import "./chunk-E4D75S3B.js";
import "./chunk-TA37EJVK.js";
import "./chunk-TXPGJST7.js";
export {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
};
//# sourceMappingURL=dist-FQGA4NDN.js.map
