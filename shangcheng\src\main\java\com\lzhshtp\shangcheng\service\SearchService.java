package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;

import java.util.List;

/**
 * 搜索服务接口
 * 基于ElasticSearch的全文搜索功能
 */
public interface SearchService {

    // ==================== 商品搜索 ====================

    /**
     * 搜索商品
     * @param params 搜索参数
     * @return 搜索结果
     */
    PageResult<ProductDTO> searchProducts(ProductQueryParams params);

    /**
     * 获取商品搜索建议
     * @param prefix 搜索前缀
     * @return 建议列表
     */
    List<String> getProductSearchSuggestions(String prefix);

    /**
     * 获取相似商品推荐
     * @param productId 商品ID
     * @return 相似商品列表
     */
    List<ProductDTO> getSimilarProducts(Long productId);

    /**
     * 同步商品数据到ElasticSearch
     * @param productId 商品ID，为null时同步所有商品
     */
    void syncProductToES(Long productId);

    /**
     * 删除商品索引
     * @param productId 商品ID
     */
    void deleteProductFromES(Long productId);

    // ==================== 论坛帖子搜索 ====================

    /**
     * 搜索论坛帖子
     * @param queryRequest 搜索参数
     * @return 搜索结果
     */
    PageResult<ForumPostDTO> searchForumPosts(ForumPostQueryRequest queryRequest);

    /**
     * 获取帖子搜索建议
     * @param prefix 搜索前缀
     * @return 建议列表
     */
    List<String> getForumPostSearchSuggestions(String prefix);

    /**
     * 获取相似帖子推荐
     * @param postId 帖子ID
     * @return 相似帖子列表
     */
    List<ForumPostDTO> getSimilarForumPosts(Long postId);

    /**
     * 同步帖子数据到ElasticSearch
     * @param postId 帖子ID，为null时同步所有帖子
     */
    void syncForumPostToES(Long postId);

    /**
     * 删除帖子索引
     * @param postId 帖子ID
     */
    void deleteForumPostFromES(Long postId);

    // ==================== 搜索分析 ====================

    /**
     * 记录搜索行为
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param searchType 搜索类型（product/forum）
     */
    void recordSearchBehavior(String keyword, Long userId, String searchType);

    /**
     * 获取热门搜索词
     * @param limit 返回数量
     * @return 热门搜索词列表
     */
    List<String> getHotSearchKeywords(int limit);

    /**
     * 全量重建索引
     */
    void rebuildAllIndexes();
}
