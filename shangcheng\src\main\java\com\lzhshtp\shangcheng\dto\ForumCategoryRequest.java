package com.lzhshtp.shangcheng.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 论坛分类请求数据传输对象
 */
@Data
public class ForumCategoryRequest {
    /**
     * 分类名称
     */
    @NotNull(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称不能超过50个字符")
    private String categoryName;

    /**
     * 分类描述
     */
    private String description;
}
