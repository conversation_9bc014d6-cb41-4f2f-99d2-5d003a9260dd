package com.lzhshtp.shangcheng.controller;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.UserFollowDTO;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.UserFollowService;
import com.lzhshtp.shangcheng.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 用户关注控制器
 */
@RestController
@RequestMapping("/api/follows")
@RequiredArgsConstructor
public class UserFollowController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserFollowController.class);
    private final UserFollowService userFollowService;
    private final UserService userService;
    
    /**
     * 关注用户
     * 
     * @param userId 要关注的用户ID
     * @return 操作结果
     */
    @PostMapping("/{userId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> followUser(@PathVariable Long userId) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return ApiResponse.fail("用户未登录");
            }
            
            // 不能关注自己
            if (currentUser.getUserId().equals(userId)) {
                return ApiResponse.fail("不能关注自己");
            }
            
            // 检查要关注的用户是否存在
            User targetUser = userService.findById(userId);
            if (targetUser == null) {
                return ApiResponse.fail("要关注的用户不存在");
            }
            
            // 执行关注操作
            boolean success = userFollowService.followUser(currentUser.getUserId(), userId);
            if (success) {
                return ApiResponse.success("关注成功", null);
            } else {
                return ApiResponse.fail("关注失败");
            }
        } catch (Exception e) {
            logger.error("关注用户失败", e);
            return ApiResponse.fail("关注用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消关注用户
     * 
     * @param userId 要取消关注的用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{userId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Void> unfollowUser(@PathVariable Long userId) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return ApiResponse.fail("用户未登录");
            }
            
            // 执行取消关注操作
            boolean success = userFollowService.unfollowUser(currentUser.getUserId(), userId);
            if (success) {
                return ApiResponse.success("取消关注成功", null);
            } else {
                return ApiResponse.fail("取消关注失败");
            }
        } catch (Exception e) {
            logger.error("取消关注用户失败", e);
            return ApiResponse.fail("取消关注用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查当前用户是否关注了指定用户
     * 
     * @param userId 要检查的用户ID
     * @return 是否关注
     */
    @GetMapping("/check/{userId}")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<Boolean> checkFollowing(@PathVariable Long userId) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return ApiResponse.fail("用户未登录");
            }
            
            // 检查是否关注
            boolean isFollowing = userFollowService.isFollowing(currentUser.getUserId(), userId);
            return ApiResponse.success(isFollowing);
        } catch (Exception e) {
            logger.error("检查关注状态失败", e);
            return ApiResponse.fail("检查关注状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的关注列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 每页数量
     * @return 关注列表
     */
    @GetMapping("/followings/{userId}")
    public ApiResponse<PageResult<UserFollowDTO>> getFollowings(
            @PathVariable Long userId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }
            
            // 获取关注列表
            PageResult<UserFollowDTO> followings = userFollowService.getFollowings(userId, page, pageSize);
            return ApiResponse.success(followings);
        } catch (Exception e) {
            logger.error("获取关注列表失败", e);
            return ApiResponse.fail("获取关注列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的粉丝列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param pageSize 每页数量
     * @return 粉丝列表
     */
    @GetMapping("/followers/{userId}")
    public ApiResponse<PageResult<UserFollowDTO>> getFollowers(
            @PathVariable Long userId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }
            
            // 获取粉丝列表
            PageResult<UserFollowDTO> followers = userFollowService.getFollowers(userId, page, pageSize);
            return ApiResponse.success(followers);
        } catch (Exception e) {
            logger.error("获取粉丝列表失败", e);
            return ApiResponse.fail("获取粉丝列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的关注数量
     * 
     * @param userId 用户ID
     * @return 关注数量
     */
    @GetMapping("/followings/count/{userId}")
    public ApiResponse<Integer> getFollowingsCount(@PathVariable Long userId) {
        try {
            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }
            
            // 获取关注数量
            Integer count = userFollowService.getFollowingsCount(userId);
            return ApiResponse.success(count);
        } catch (Exception e) {
            logger.error("获取关注数量失败", e);
            return ApiResponse.fail("获取关注数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的粉丝数量
     * 
     * @param userId 用户ID
     * @return 粉丝数量
     */
    @GetMapping("/followers/count/{userId}")
    public ApiResponse<Integer> getFollowersCount(@PathVariable Long userId) {
        try {
            // 检查用户是否存在
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.fail("用户不存在");
            }
            
            // 获取粉丝数量
            Integer count = userFollowService.getFollowersCount(userId);
            return ApiResponse.success(count);
        } catch (Exception e) {
            logger.error("获取粉丝数量失败", e);
            return ApiResponse.fail("获取粉丝数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户的关注列表
     * 
     * @param page 页码
     * @param pageSize 每页数量
     * @return 关注列表
     */
    @GetMapping("/followings")
    @PreAuthorize("isAuthenticated()")
    public ApiResponse<PageResult<UserFollowDTO>> getCurrentUserFollowings(
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return ApiResponse.fail("用户未登录");
            }
            
            // 获取关注列表
            PageResult<UserFollowDTO> followings = userFollowService.getFollowings(currentUser.getUserId(), page, pageSize);
            return ApiResponse.success(followings);
        } catch (Exception e) {
            logger.error("获取当前用户关注列表失败", e);
            return ApiResponse.fail("获取关注列表失败: " + e.getMessage());
        }
    }
} 