package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.AiMessage;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI消息Mapper接口
 */
@Mapper
public interface AiMessageMapper extends BaseMapper<AiMessage> {

    /**
     * 根据会话ID查询消息列表，按创建时间升序
     *
     * @param sessionId 会话ID
     * @return 消息列表
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = #{sessionId} ORDER BY zhshtp_created_time ASC")
    List<AiMessage> findBySessionIdOrderByCreatedTimeAsc(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID查询最近N条消息，按创建时间升序
     *
     * @param sessionId 会话ID
     * @param limit     消息数量限制
     * @return 消息列表
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = #{sessionId} AND zhshtp_id IS NOT NULL AND zhshtp_type IS NOT NULL AND zhshtp_text_content IS NOT NULL ORDER BY zhshtp_created_time DESC LIMIT #{limit}")
    List<AiMessage> findRecentMessagesBySessionId(@Param("sessionId") String sessionId, @Param("limit") int limit);

    /**
     * 根据用户ID查询用户的所有消息，按创建时间倒序
     *
     * @param creatorId 创建者用户ID
     * @return 消息列表
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_message WHERE zhshtp_creator_id = #{creatorId} ORDER BY zhshtp_created_time DESC")
    List<AiMessage> findByCreatorIdOrderByCreatedTimeDesc(@Param("creatorId") String creatorId);

    /**
     * 根据消息类型查询消息列表
     *
     * @param sessionId 会话ID
     * @param type      消息类型
     * @return 消息列表
     */
    @Select("SELECT * FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = #{sessionId} AND zhshtp_type = #{type} ORDER BY zhshtp_created_time ASC")
    List<AiMessage> findBySessionIdAndType(@Param("sessionId") String sessionId, @Param("type") String type);

    /**
     * 统计会话的消息数量
     *
     * @param sessionId 会话ID
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = #{sessionId}")
    int countBySessionId(@Param("sessionId") String sessionId);

    /**
     * 删除会话的所有消息
     *
     * @param sessionId 会话ID
     * @return 删除的消息数量
     */
    @Delete("DELETE FROM tb_lzhshtp_ai_message WHERE zhshtp_ai_session_id = #{sessionId}")
    int deleteBySessionId(@Param("sessionId") String sessionId);
}
