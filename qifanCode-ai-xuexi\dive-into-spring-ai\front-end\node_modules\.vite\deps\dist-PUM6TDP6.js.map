{"version": 3, "sources": ["../../@lezer/yaml/dist/index.js", "../../@codemirror/lang-yaml/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst blockEnd = 63,\n  eof = 64,\n  DirectiveEnd = 1,\n  DocEnd = 2,\n  sequenceStartMark = 3,\n  sequenceContinueMark = 4,\n  explicitMapStartMark = 5,\n  explicitMapContinueMark = 6,\n  flowMapMark = 7,\n  mapStartMark = 65,\n  mapContinueMark = 66,\n  Literal = 8,\n  QuotedLiteral = 9,\n  Anchor = 10,\n  <PERSON>as = 11,\n  Tag = 12,\n  BlockLiteralContent = 13,\n  BracketL = 19,\n  FlowSequence = 20,\n  Colon = 29,\n  BraceL = 33,\n  FlowMapping = 34,\n  BlockLiteralHeader = 47;\n\nconst\n  type_Top = 0, // Top document level\n  type_Seq = 1, // Block sequence\n  type_Map = 2, // Block mapping\n  type_Flow = 3, // Inside flow content\n  type_Lit = 4; // Block literal with explicit indentation\n\nclass Context {\n  constructor(parent, depth, type) {\n    this.parent = parent;\n    this.depth = depth;\n    this.type = type;\n    this.hash = (parent ? parent.hash + parent.hash << 8 : 0) + depth + (depth << 4) + type;\n  }\n\n  static top = new Context(null, -1, type_Top)\n}\n\nfunction findColumn(input, pos) {\n  for (let col = 0, p = pos - input.pos - 1;; p--, col++) {\n    let ch = input.peek(p);\n    if (isBreakSpace(ch) || ch == -1) return col\n  }\n}\n\nfunction isNonBreakSpace(ch) {\n  return ch == 32 || ch == 9\n}\n\nfunction isBreakSpace(ch) {\n  return ch == 10 || ch == 13\n}\n\nfunction isSpace(ch) {\n  return isNonBreakSpace(ch) || isBreakSpace(ch)\n}\n\nfunction isSep(ch) {\n  return ch < 0 || isSpace(ch)\n}\n\nconst indentation = new ContextTracker({\n  start: Context.top,\n  reduce(context, term) {\n    return context.type == type_Flow && (term == FlowSequence || term == FlowMapping) ? context.parent : context\n  },\n  shift(context, term, stack, input) {\n    if (term == sequenceStartMark)\n      return new Context(context, findColumn(input, input.pos), type_Seq)\n    if (term == mapStartMark || term == explicitMapStartMark)\n      return new Context(context, findColumn(input, input.pos), type_Map)\n    if (term == blockEnd)\n      return context.parent\n    if (term == BracketL || term == BraceL)\n      return new Context(context, 0, type_Flow)\n    if (term == BlockLiteralContent && context.type == type_Lit)\n      return context.parent\n    if (term == BlockLiteralHeader) {\n      let indent = /[1-9]/.exec(input.read(input.pos, stack.pos));\n      if (indent) return new Context(context, context.depth + (+indent[0]), type_Lit)\n    }\n    return context\n  },\n  hash(context) { return context.hash }\n});\n\nfunction three(input, ch, off = 0) {\n  return input.peek(off) == ch && input.peek(off + 1) == ch && input.peek(off + 2) == ch && isSep(input.peek(off + 3))\n}\n\nconst newlines = new ExternalTokenizer((input, stack) => {\n  if (input.next == -1 && stack.canShift(eof))\n    return input.acceptToken(eof)\n  let prev = input.peek(-1);\n  if ((isBreakSpace(prev) || prev < 0) && stack.context.type != type_Flow) {\n    if (three(input, 45 /* '-' */)) {\n      if (stack.canShift(blockEnd)) input.acceptToken(blockEnd);\n      else return input.acceptToken(DirectiveEnd, 3)\n    }\n    if (three(input, 46 /* '.' */)) {\n      if (stack.canShift(blockEnd)) input.acceptToken(blockEnd);\n      else return input.acceptToken(DocEnd, 3)\n    }\n    let depth = 0;\n    while (input.next == 32 /* ' ' */) { depth++; input.advance(); }\n    if ((depth < stack.context.depth ||\n         depth == stack.context.depth && stack.context.type == type_Seq &&\n         (input.next != 45 /* '-' */ || !isSep(input.peek(1)))) &&\n        // Not blank\n        input.next != -1 && !isBreakSpace(input.next) && input.next != 35 /* '#' */)\n      input.acceptToken(blockEnd, -depth);\n  }\n}, {contextual: true});\n\nconst blockMark = new ExternalTokenizer((input, stack) => {\n  if (stack.context.type == type_Flow) {\n    if (input.next == 63 /* '?' */) {\n      input.advance();\n      if (isSep(input.next)) input.acceptToken(flowMapMark);\n    }\n    return\n  }\n  if (input.next == 45 /* '-' */) {\n    input.advance();\n    if (isSep(input.next))\n      input.acceptToken(stack.context.type == type_Seq && stack.context.depth == findColumn(input, input.pos - 1)\n                        ? sequenceContinueMark : sequenceStartMark);\n  } else if (input.next == 63 /* '?' */) {\n    input.advance();\n    if (isSep(input.next))\n      input.acceptToken(stack.context.type == type_Map && stack.context.depth == findColumn(input, input.pos - 1)\n                        ? explicitMapContinueMark : explicitMapStartMark);\n  } else {\n    let start = input.pos;\n    // Scan over a potential key to see if it is followed by a colon.\n    for (;;) {\n      if (isNonBreakSpace(input.next)) {\n        if (input.pos == start) return\n        input.advance();\n      } else if (input.next == 33 /* '!' */) {\n        readTag(input);\n      } else if (input.next == 38 /* '&' */) {\n        readAnchor(input);\n      } else if (input.next == 42 /* '*' */) {\n        readAnchor(input);\n        break\n      } else if (input.next == 39 /* \"'\" */ || input.next == 34 /* '\"' */) {\n        if (readQuoted(input, true)) break\n        return\n      } else if (input.next == 91 /* '[' */ || input.next == 123 /* '{' */) {\n        if (!scanBrackets(input)) return\n        break\n      } else {\n        readPlain(input, true, false, 0);\n        break\n      }\n    }\n    while (isNonBreakSpace(input.next)) input.advance();\n    if (input.next == 58 /* ':' */) {\n      if (input.pos == start && stack.canShift(Colon)) return\n      let after = input.peek(1);\n      if (isSep(after))\n        input.acceptTokenTo(stack.context.type == type_Map && stack.context.depth == findColumn(input, start)\n                            ? mapContinueMark : mapStartMark, start);\n    }\n  }\n}, {contextual: true});\n\nfunction uriChar(ch) {\n  return ch > 32 && ch < 127 && ch != 34 && ch != 37 && ch != 44 && ch != 60 &&\n    ch != 62 && ch != 92 && ch != 94 && ch != 96 && ch != 123 && ch != 124 && ch != 125\n}\n\nfunction hexChar(ch) {\n  return ch >= 48 && ch <= 57 || ch >= 97 && ch <= 102 || ch >= 65 && ch <= 70\n}\n\nfunction readUriChar(input, quoted) {\n  if (input.next == 37 /* '%' */) {\n    input.advance();\n    if (hexChar(input.next)) input.advance();\n    if (hexChar(input.next)) input.advance();\n    return true\n  } else if (uriChar(input.next) || quoted && input.next == 44 /* ',' */) {\n    input.advance();\n    return true\n  }\n  return false\n}\n\nfunction readTag(input) {\n  input.advance(); // !\n  if (input.next == 60 /* '<' */) {\n    input.advance();\n    for (;;) {\n      if (!readUriChar(input, true)) {\n        if (input.next == 62 /* '>' */) input.advance();\n        break\n      }\n    }\n  } else {\n    while (readUriChar(input, false)) {}\n  }\n}\n\nfunction readAnchor(input) {\n  input.advance();\n  while (!isSep(input.next) && charTag(input.tag) != \"f\") input.advance();\n}\n  \nfunction readQuoted(input, scan) {\n  let quote = input.next, lineBreak = false, start = input.pos;\n  input.advance();\n  for (;;) {\n    let ch = input.next;\n    if (ch < 0) break\n    input.advance();\n    if (ch == quote) {\n      if (ch == 39 /* \"'\" */) {\n        if (input.next == 39) input.advance();\n        else break\n      } else {\n        break\n      }\n    } else if (ch == 92 /* \"\\\\\" */ && quote == 34 /* '\"' */) {\n      if (input.next >= 0) input.advance();\n    } else if (isBreakSpace(ch)) {\n      if (scan) return false\n      lineBreak = true;\n    } else if (scan && input.pos >= start + 1024) {\n      return false\n    }\n  }\n  return !lineBreak\n}\n\nfunction scanBrackets(input) {\n  for (let stack = [], end = input.pos + 1024;;) {\n    if (input.next == 91 /* '[' */ || input.next == 123 /* '{' */) {\n      stack.push(input.next);\n      input.advance();\n    } else if (input.next == 39 /* \"'\" */ || input.next == 34 /* '\"' */) {\n      if (!readQuoted(input, true)) return false\n    } else if (input.next == 93 /* ']' */ || input.next == 125 /* '}' */) {\n      if (stack[stack.length - 1] != input.next - 2) return false\n      stack.pop();\n      input.advance();\n      if (!stack.length) return true\n    } else if (input.next < 0 || input.pos > end || isBreakSpace(input.next)) {\n      return false\n    } else {\n      input.advance();\n    }\n  }\n}\n\n// \"Safe char\" info for char codes 33 to 125. s: safe, i: indicator, f: flow indicator\nconst charTable = \"iiisiiissisfissssssssssssisssiiissssssssssssssssssssssssssfsfssissssssssssssssssssssssssssfif\";\n\nfunction charTag(ch) {\n  if (ch < 33) return \"u\"\n  if (ch > 125) return \"s\"\n  return charTable[ch - 33]\n}\n\nfunction isSafe(ch, inFlow) {\n  let tag = charTag(ch);\n  return tag != \"u\" && !(inFlow && tag == \"f\")\n}\n\nfunction readPlain(input, scan, inFlow, indent) {\n  if (charTag(input.next) == \"s\" ||\n      (input.next == 63 /* '?' */ || input.next == 58 /* ':' */ || input.next == 45 /* '-' */) &&\n      isSafe(input.peek(1), inFlow)) {\n    input.advance();\n  } else {\n    return false\n  }\n  let start = input.pos;\n  for (;;) {\n    let next = input.next, off = 0, lineIndent = indent + 1;\n    while (isSpace(next)) {\n      if (isBreakSpace(next)) {\n        if (scan) return false\n        lineIndent = 0;\n      } else {\n        lineIndent++;\n      }\n      next = input.peek(++off);\n    }\n    let safe = next >= 0 &&\n        (next == 58 /* ':' */ ? isSafe(input.peek(off + 1), inFlow) :\n         next == 35 /* '#' */ ? input.peek(off - 1) != 32 /* ' ' */ :\n         isSafe(next, inFlow));\n    if (!safe || !inFlow && lineIndent <= indent ||\n        lineIndent == 0 && !inFlow && (three(input, 45, off) || three(input, 46, off)))\n      break\n    if (scan && charTag(next) == \"f\") return false\n    for (let i = off; i >= 0; i--) input.advance();\n    if (scan && input.pos > start + 1024) return false\n  }\n  return true\n}\n\nconst literals = new ExternalTokenizer((input, stack) => {\n  if (input.next == 33 /* '!' */) {\n    readTag(input);\n    input.acceptToken(Tag);\n  } else if (input.next == 38 /* '&' */ || input.next == 42 /* '*' */) {\n    let token = input.next == 38 ? Anchor : Alias;\n    readAnchor(input);\n    input.acceptToken(token);\n  } else if (input.next == 39 /* \"'\" */ || input.next == 34 /* '\"' */) {\n    readQuoted(input, false);\n    input.acceptToken(QuotedLiteral);\n  } else if (readPlain(input, false, stack.context.type == type_Flow, stack.context.depth)) {\n    input.acceptToken(Literal);\n  }\n});\n\nconst blockLiteral = new ExternalTokenizer((input, stack) => {\n  let indent = stack.context.type == type_Lit ? stack.context.depth : -1, upto = input.pos;\n  scan: for (;;) {\n    let depth = 0, next = input.next;\n    while (next == 32 /* ' ' */) next = input.peek(++depth);\n    if (!depth && (three(input, 45, depth) || three(input, 46, depth))) break\n    if (!isBreakSpace(next)) {\n      if (indent < 0) indent = Math.max(stack.context.depth + 1, depth);\n      if (depth < indent) break\n    }\n    for (;;) {\n      if (input.next < 0) break scan\n      let isBreak = isBreakSpace(input.next);\n      input.advance();\n      if (isBreak) continue scan\n      upto = input.pos;\n    }\n  }\n  input.acceptTokenTo(BlockLiteralContent, upto);\n});\n\nconst yamlHighlighting = styleTags({\n  DirectiveName: tags.keyword,\n  DirectiveContent: tags.attributeValue,\n  \"DirectiveEnd DocEnd\": tags.meta,\n  QuotedLiteral: tags.string,\n  BlockLiteralHeader: tags.special(tags.string),\n  BlockLiteralContent: tags.content,\n  Literal: tags.content,\n  \"Key/Literal Key/QuotedLiteral\": tags.definition(tags.propertyName),\n  \"Anchor Alias\": tags.labelName,\n  Tag: tags.typeName,\n  Comment: tags.lineComment,\n  \": , -\": tags.separator,\n  \"?\": tags.punctuation,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"5lQ!ZQgOOO#PQfO'#CpO#uQfO'#DOOOQR'#Dv'#DvO$qQgO'#DRO%gQdO'#DUO%nQgO'#DUO&ROaO'#D[OOQR'#Du'#DuO&{QgO'#D^O'rQgO'#D`OOQR'#Dt'#DtO(iOqO'#DbOOQP'#Dj'#DjO(zQaO'#CmO)YQgO'#CmOOQP'#Cm'#CmQ)jQaOOQ)uQgOOQ]QgOOO*PQdO'#CrO*nQdO'#CtOOQO'#Dw'#DwO+]Q`O'#CxO+hQdO'#CwO+rQ`O'#CwOOQO'#Cv'#CvO+wQdO'#CvOOQO'#Cq'#CqO,UQ`O,59[O,^QfO,59[OOQR,59[,59[OOQO'#Cx'#CxO,eQ`O'#DPO,pQdO'#DPOOQO'#Dx'#DxO,zQdO'#DxO-XQ`O,59jO-aQfO,59jOOQR,59j,59jOOQR'#DS'#DSO-hQcO,59mO-sQgO'#DVO.TQ`O'#DVO.YQcO,59pOOQR'#DX'#DXO#|QfO'#DWO.hQcO'#DWOOQR,59v,59vO.yOWO,59vO/OOaO,59vO/WOaO,59vO/cQgO'#D_OOQR,59x,59xO0VQgO'#DaOOQR,59z,59zOOQP,59|,59|O0yOaO,59|O1ROaO,59|O1aOqO,59|OOQP-E7h-E7hO1oQgO,59XOOQP,59X,59XO2PQaO'#DeO2_QgO'#DeO2oQgO'#DkOOQP'#Dk'#DkQ)jQaOOO3PQdO'#CsOOQO,59^,59^O3kQdO'#CuOOQO,59`,59`OOQO,59c,59cO4VQdO,59cO4aQdO'#CzO4kQ`O'#CzOOQO,59b,59bOOQU,5:Q,5:QOOQR1G.v1G.vO4pQ`O1G.vOOQU-E7d-E7dO4xQdO,59kOOQO,59k,59kO5SQdO'#DQO5^Q`O'#DQOOQO,5:d,5:dOOQU,5:R,5:ROOQR1G/U1G/UO5cQ`O1G/UOOQU-E7e-E7eO5kQgO'#DhO5xQcO1G/XOOQR1G/X1G/XOOQR,59q,59qO6TQgO,59qO6eQdO'#DiO6lQgO'#DiO7PQcO1G/[OOQR1G/[1G/[OOQR,59r,59rO#|QfO,59rOOQR1G/b1G/bO7_OWO1G/bO7dOaO1G/bOOQR,59y,59yOOQR,59{,59{OOQP1G/h1G/hO7lOaO1G/hO7tOaO1G/hO8POaO1G/hOOQP1G.s1G.sO8_QgO,5:POOQP,5:P,5:POOQP,5:V,5:VOOQP-E7i-E7iOOQO,59_,59_OOQO,59a,59aOOQO1G.}1G.}OOQO,59f,59fO8oQdO,59fOOQR7+$b7+$bP,XQ`O'#DfOOQO1G/V1G/VOOQO,59l,59lO8yQdO,59lOOQR7+$p7+$pP9TQ`O'#DgOOQR'#DT'#DTOOQR,5:S,5:SOOQR-E7f-E7fOOQR7+$s7+$sOOQR1G/]1G/]O9YQgO'#DYO9jQ`O'#DYOOQR,5:T,5:TO#|QfO'#DZO9oQcO'#DZOOQR-E7g-E7gOOQR7+$v7+$vOOQR1G/^1G/^OOQR7+$|7+$|O:QOWO7+$|OOQP7+%S7+%SO:VOaO7+%SO:_OaO7+%SOOQP1G/k1G/kOOQO1G/Q1G/QOOQO1G/W1G/WOOQR,59t,59tO:jQgO,59tOOQR,59u,59uO#|QfO,59uOOQR<<Hh<<HhOOQP<<Hn<<HnO:zOaO<<HnOOQR1G/`1G/`OOQR1G/a1G/aOOQPAN>YAN>Y\",\n  stateData: \";S~O!fOS!gOS^OS~OP_OQbORSOTUOWROXROYYOZZO[XOcPOqQO!PVO!V[O!cTO~O`cO~P]OVkOWROXROYeOZfO[dOcPOmhOqQO~OboO~P!bOVtOWROXROYeOZfO[dOcPOmrOqQO~OpwO~P#WORSOTUOWROXROYYOZZO[XOcPOqQO!PVO!cTO~OSvP!avP!bvP~P#|OWROXROYeOZfO[dOcPOqQO~OmzO~P%OOm!OOUzP!azP!bzP!dzP~P#|O^!SO!b!QO!f!TO!g!RO~ORSOTUOWROXROcPOqQO!PVO!cTO~OY!UOP!QXQ!QX!V!QX!`!QXS!QX!a!QX!b!QXU!QXm!QX!d!QX~P&aO[!WOP!SXQ!SX!V!SX!`!SXS!SX!a!SX!b!SXU!SXm!SX!d!SX~P&aO^!ZO!W![O!b!YO!f!]O!g!YO~OP!_O!V[OQaX!`aX~OPaXQaX!VaX!`aX~P#|OP!bOQ!cO!V[O~OP_O!V[O~P#|OWROXROY!fOcPOqQObfXmfXofXpfX~OWROXRO[!hOcPOqQObhXmhXohXphX~ObeXmlXoeX~ObkXokX~P%OOm!kO~Om!lObnPonP~P%OOb!pOo!oO~Ob!pO~P!bOm!sOosXpsX~OosXpsX~P%OOm!uOotPptP~P%OOo!xOp!yO~Op!yO~P#WOS!|O!a#OO!b#OO~OUyX!ayX!byX!dyX~P#|Om#QO~OU#SO!a#UO!b#UO!d#RO~Om#WOUzX!azX!bzX!dzX~O]#XO~O!b#XO!g#YO~O^#ZO!b#XO!g#YO~OP!RXQ!RX!V!RX!`!RXS!RX!a!RX!b!RXU!RXm!RX!d!RX~P&aOP!TXQ!TX!V!TX!`!TXS!TX!a!TX!b!TXU!TXm!TX!d!TX~P&aO!b#^O!g#^O~O^#_O!b#^O!f#`O!g#^O~O^#_O!W#aO!b#^O!g#^O~OPaaQaa!Vaa!`aa~P#|OP#cO!V[OQ!XX!`!XX~OP!XXQ!XX!V!XX!`!XX~P#|OP_O!V[OQ!_X!`!_X~P#|OWROXROcPOqQObgXmgXogXpgX~OWROXROcPOqQObiXmiXoiXpiX~Obkaoka~P%OObnXonX~P%OOm#kO~Ob#lOo!oO~Oosapsa~P%OOotXptX~P%OOm#pO~Oo!xOp#qO~OSwP!awP!bwP~P#|OS!|O!a#vO!b#vO~OUya!aya!bya!dya~P#|Om#xO~P%OOm#{OU}P!a}P!b}P!d}P~P#|OU#SO!a$OO!b$OO!d#RO~O]$QO~O!b$QO!g$RO~O!b$SO!g$SO~O^$TO!b$SO!g$SO~O^$TO!b$SO!f$UO!g$SO~OP!XaQ!Xa!V!Xa!`!Xa~P#|Obnaona~P%OOotapta~P%OOo!xO~OU|X!a|X!b|X!d|X~P#|Om$ZO~Om$]OU}X!a}X!b}X!d}X~O]$^O~O!b$_O!g$_O~O^$`O!b$_O!g$_O~OU|a!a|a!b|a!d|a~P#|O!b$cO!g$cO~O\",\n  goto: \",]!mPPPPPPPPPPPPPPPPP!nPP!v#v#|$`#|$c$f$j$nP%VPPP!v%Y%^%a%{&O%a&R&U&X&_&b%aP&e&{&e'O'RPP']'a'g'm's'y(XPPPPPPPP(_)e*X+c,VUaObcR#e!c!{ROPQSTUXY_bcdehknrtvz!O!U!W!_!b!c!f!h!k!l!s!u!|#Q#R#S#W#c#k#p#x#{$Z$]QmPR!qnqfPQThknrtv!k!l!s!u#R#k#pR!gdR!ieTlPnTjPnSiPnSqQvQ{TQ!mkQ!trQ!vtR#y#RR!nkTsQvR!wt!RWOSUXY_bcz!O!U!W!_!b!c!|#Q#S#W#c#x#{$Z$]RySR#t!|R|TR|UQ!PUR#|#SR#z#RR#z#SyZOSU_bcz!O!_!b!c!|#Q#S#W#c#x#{$Z$]R!VXR!XYa]O^abc!a!c!eT!da!eQnPR!rnQvQR!{vQ!}yR#u!}Q#T|R#}#TW^Obc!cS!^^!aT!aa!eQ!eaR#f!eW`Obc!cQxSS}U#SQ!`_Q#PzQ#V!OQ#b!_Q#d!bQ#s!|Q#w#QQ$P#WQ$V#cQ$Y#xQ$[#{Q$a$ZR$b$]xZOSU_bcz!O!_!b!c!|#Q#S#W#c#x#{$Z$]Q!VXQ!XYQ#[!UR#]!W!QWOSUXY_bcz!O!U!W!_!b!c!|#Q#S#W#c#x#{$Z$]pfPQThknrtv!k!l!s!u#R#k#pQ!gdQ!ieQ#g!fR#h!hSgPn^pQTkrtv#RQ!jhQ#i!kQ#j!lQ#n!sQ#o!uQ$W#kR$X#pQuQR!zv\",\n  nodeNames: \"⚠ DirectiveEnd DocEnd - - ? ? ? Literal QuotedLiteral Anchor Alias Tag BlockLiteralContent Comment Stream BOM Document ] [ FlowSequence Item Tagged Anchored Anchored Tagged FlowMapping Pair Key : Pair , } { FlowMapping Pair Pair BlockSequence Item Item BlockMapping Pair Pair Key Pair Pair BlockLiteral BlockLiteralHeader Tagged Anchored Anchored Tagged Directive DirectiveName DirectiveContent Document\",\n  maxTerm: 74,\n  context: indentation,\n  nodeProps: [\n    [\"isolate\", -3,8,9,14,\"\"],\n    [\"openedBy\", 18,\"[\",32,\"{\"],\n    [\"closedBy\", 19,\"]\",33,\"}\"]\n  ],\n  propSources: [yamlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 6,\n  tokenData: \"-Y~RnOX#PXY$QYZ$]Z]#P]^$]^p#Ppq$Qqs#Pst$btu#Puv$yv|#P|}&e}![#P![!]'O!]!`#P!`!a'i!a!}#P!}#O*g#O#P#P#P#Q+Q#Q#o#P#o#p+k#p#q'i#q#r,U#r;'S#P;'S;=`#z<%l?HT#P?HT?HU,o?HUO#PQ#UU!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PQ#kTOY#PZs#Pt;'S#P;'S;=`#z<%lO#PQ#}P;=`<%l#P~$VQ!f~XY$Qpq$Q~$bO!g~~$gS^~OY$bZ;'S$b;'S;=`$s<%lO$b~$vP;=`<%l$bR%OX!WQOX%kXY#PZ]%k]^#P^p%kpq#hq;'S%k;'S;=`&_<%lO%kR%rX!WQ!VPOX%kXY#PZ]%k]^#P^p%kpq#hq;'S%k;'S;=`&_<%lO%kR&bP;=`<%l%kR&lUoP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR'VUmP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR'p[!PP!WQOY#PZp#Ppq#hq{#P{|(f|}#P}!O(f!O!R#P!R![)p![;'S#P;'S;=`#z<%lO#PR(mW!PP!WQOY#PZp#Ppq#hq!R#P!R![)V![;'S#P;'S;=`#z<%lO#PR)^U!PP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR)wY!PP!WQOY#PZp#Ppq#hq{#P{|)V|}#P}!O)V!O;'S#P;'S;=`#z<%lO#PR*nUcP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR+XUbP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR+rUqP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR,]UpP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR,vU`P!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#P\",\n  tokenizers: [newlines, blockMark, literals, blockLiteral, 0, 1],\n  topRules: {\"Stream\":[0,15]},\n  tokenPrec: 0\n});\n\nexport { parser };\n", "import { parser as parser$1 } from '@lezer/yaml';\nimport { LRLanguage, indentNodeProp, delimitedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { parseMixed } from '@lezer/common';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { LRParser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"!vOQOPOOO]OPO'#C_OhOPO'#C^OOOO'#Cc'#CcOpOPO'#CaQOOOOOO{OPOOOOOO'#Cb'#CbO!WOPO'#C`O!`OPO,58xOOOO-E6a-E6aOOOO-E6`-E6`OOOO'#C_'#C_OOOO1G.d1G.d\",\n  stateData: \"!h~OXPOYROWTP~OWVXXRXYRX~OYVOXSP~OXROYROWTX~OXROYROWTP~OYVOXSX~OX[O~OXY~\",\n  goto: \"vWPPX[beioRUOQQOR]XRXQTTOUQWQRZWSSOURYS\",\n  nodeNames: \"⚠ Document Frontmatter DashLine FrontmatterContent Body\",\n  maxTerm: 10,\n  skippedNodes: [0],\n  repeatNodeCount: 2,\n  tokenData: \"$z~RXOYnYZ!^Z]n]^!^^}n}!O!i!O;'Sn;'S;=`!c<%lOn~qXOYnYZ!^Z]n]^!^^;'Sn;'S;=`!c<%l~n~On~~!^~!cOY~~!fP;=`<%ln~!lZOYnYZ!^Z]n]^!^^}n}!O#_!O;'Sn;'S;=`!c<%l~n~On~~!^~#bZOYnYZ!^Z]n]^!^^}n}!O$T!O;'Sn;'S;=`!c<%l~n~On~~!^~$WXOYnYZ$sZ]n]^$s^;'Sn;'S;=`!c<%l~n~On~~$s~$zOX~Y~\",\n  tokenizers: [0],\n  topRules: {\"Document\":[0,1]},\n  tokenPrec: 67\n});\n\n/**\nA language provider based on the [Lezer YAML\nparser](https://github.com/lezer-parser/yaml), extended with\nhighlighting and indentation information.\n*/\nconst yamlLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"yaml\",\n    parser: /*@__PURE__*/parser$1.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Stream: cx => {\n                    for (let before = cx.node.resolve(cx.pos, -1); before && before.to >= cx.pos; before = before.parent) {\n                        if (before.name == \"BlockLiteralContent\" && before.from < before.to)\n                            return cx.baseIndentFor(before);\n                        if (before.name == \"BlockLiteral\")\n                            return cx.baseIndentFor(before) + cx.unit;\n                        if (before.name == \"BlockSequence\" || before.name == \"BlockMapping\")\n                            return cx.column(before.from, 1);\n                        if (before.name == \"QuotedLiteral\")\n                            return null;\n                        if (before.name == \"Literal\") {\n                            let col = cx.column(before.from, 1);\n                            if (col == cx.lineIndent(before.from, 1))\n                                return col; // Start on own line\n                            if (before.to > cx.pos)\n                                return null;\n                        }\n                    }\n                    return null;\n                },\n                FlowMapping: /*@__PURE__*/delimitedIndent({ closing: \"}\" }),\n                FlowSequence: /*@__PURE__*/delimitedIndent({ closing: \"]\" }),\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"FlowMapping FlowSequence\": foldInside,\n                \"BlockSequence Pair BlockLiteral\": (node, state) => ({ from: state.doc.lineAt(node.from).to, to: node.to })\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"#\" },\n        indentOnInput: /^\\s*[\\]\\}]$/,\n    }\n});\n/**\nLanguage support for YAML.\n*/\nfunction yaml() {\n    return new LanguageSupport(yamlLanguage);\n}\nconst frontmatterLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"yaml-frontmatter\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [/*@__PURE__*/styleTags({ DashLine: tags.meta })]\n    })\n});\n/**\nReturns language support for a document parsed as `config.content`\nwith an optional YAML \"frontmatter\" delimited by lines that\ncontain three dashes.\n*/\nfunction yamlFrontmatter(config) {\n    let { language, support } = config.content instanceof LanguageSupport ? config.content\n        : { language: config.content, support: [] };\n    return new LanguageSupport(frontmatterLanguage.configure({\n        wrap: parseMixed(node => {\n            return node.name == \"FrontmatterContent\" ? { parser: yamlLanguage.parser }\n                : node.name == \"Body\" ? { parser: language.parser }\n                    : null;\n        })\n    }), support);\n}\n\nexport { yaml, yamlFrontmatter, yamlLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,IAAM,WAAW;AAAjB,IACE,MAAM;AADR,IAEE,eAAe;AAFjB,IAGE,SAAS;AAHX,IAIE,oBAAoB;AAJtB,IAKE,uBAAuB;AALzB,IAME,uBAAuB;AANzB,IAOE,0BAA0B;AAP5B,IAQE,cAAc;AARhB,IASE,eAAe;AATjB,IAUE,kBAAkB;AAVpB,IAWE,UAAU;AAXZ,IAYE,gBAAgB;AAZlB,IAaE,SAAS;AAbX,IAcE,QAAQ;AAdV,IAeE,MAAM;AAfR,IAgBE,sBAAsB;AAhBxB,IAiBE,WAAW;AAjBb,IAkBE,eAAe;AAlBjB,IAmBE,QAAQ;AAnBV,IAoBE,SAAS;AApBX,IAqBE,cAAc;AArBhB,IAsBE,qBAAqB;AAEvB,IACE,WAAW;AADb,IAEE,WAAW;AAFb,IAGE,WAAW;AAHb,IAIE,YAAY;AAJd,IAKE,WAAW;AAEb,IAAM,WAAN,MAAM,SAAQ;AAAA,EACZ,YAAY,QAAQ,OAAO,MAAM;AAC/B,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ,SAAS,OAAO,OAAO,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS,KAAK;AAAA,EACrF;AAGF;AADE,cARI,UAQG,OAAM,IAAI,SAAQ,MAAM,IAAI,QAAQ;AAR7C,IAAM,UAAN;AAWA,SAAS,WAAW,OAAO,KAAK;AAC9B,WAAS,MAAM,GAAG,IAAI,MAAM,MAAM,MAAM,KAAI,KAAK,OAAO;AACtD,QAAI,KAAK,MAAM,KAAK,CAAC;AACrB,QAAI,aAAa,EAAE,KAAK,MAAM;AAAI,aAAO;AAAA,EAC3C;AACF;AAEA,SAAS,gBAAgB,IAAI;AAC3B,SAAO,MAAM,MAAM,MAAM;AAC3B;AAEA,SAAS,aAAa,IAAI;AACxB,SAAO,MAAM,MAAM,MAAM;AAC3B;AAEA,SAAS,QAAQ,IAAI;AACnB,SAAO,gBAAgB,EAAE,KAAK,aAAa,EAAE;AAC/C;AAEA,SAAS,MAAM,IAAI;AACjB,SAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B;AAEA,IAAM,cAAc,IAAI,eAAe;AAAA,EACrC,OAAO,QAAQ;AAAA,EACf,OAAO,SAAS,MAAM;AACpB,WAAO,QAAQ,QAAQ,cAAc,QAAQ,gBAAgB,QAAQ,eAAe,QAAQ,SAAS;AAAA,EACvG;AAAA,EACA,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,QAAI,QAAQ;AACV,aAAO,IAAI,QAAQ,SAAS,WAAW,OAAO,MAAM,GAAG,GAAG,QAAQ;AACpE,QAAI,QAAQ,gBAAgB,QAAQ;AAClC,aAAO,IAAI,QAAQ,SAAS,WAAW,OAAO,MAAM,GAAG,GAAG,QAAQ;AACpE,QAAI,QAAQ;AACV,aAAO,QAAQ;AACjB,QAAI,QAAQ,YAAY,QAAQ;AAC9B,aAAO,IAAI,QAAQ,SAAS,GAAG,SAAS;AAC1C,QAAI,QAAQ,uBAAuB,QAAQ,QAAQ;AACjD,aAAO,QAAQ;AACjB,QAAI,QAAQ,oBAAoB;AAC9B,UAAI,SAAS,QAAQ,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC;AAC1D,UAAI;AAAQ,eAAO,IAAI,QAAQ,SAAS,QAAQ,QAAS,CAAC,OAAO,CAAC,GAAI,QAAQ;AAAA,IAChF;AACA,WAAO;AAAA,EACT;AAAA,EACA,KAAK,SAAS;AAAE,WAAO,QAAQ;AAAA,EAAK;AACtC,CAAC;AAED,SAAS,MAAM,OAAO,IAAI,MAAM,GAAG;AACjC,SAAO,MAAM,KAAK,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,CAAC,KAAK,MAAM,MAAM,KAAK,MAAM,CAAC,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,CAAC,CAAC;AACrH;AAEA,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI,MAAM,QAAQ,MAAM,MAAM,SAAS,GAAG;AACxC,WAAO,MAAM,YAAY,GAAG;AAC9B,MAAI,OAAO,MAAM,KAAK,EAAE;AACxB,OAAK,aAAa,IAAI,KAAK,OAAO,MAAM,MAAM,QAAQ,QAAQ,WAAW;AACvE,QAAI;AAAA,MAAM;AAAA,MAAO;AAAA;AAAA,IAAY,GAAG;AAC9B,UAAI,MAAM,SAAS,QAAQ;AAAG,cAAM,YAAY,QAAQ;AAAA;AACnD,eAAO,MAAM,YAAY,cAAc,CAAC;AAAA,IAC/C;AACA,QAAI;AAAA,MAAM;AAAA,MAAO;AAAA;AAAA,IAAY,GAAG;AAC9B,UAAI,MAAM,SAAS,QAAQ;AAAG,cAAM,YAAY,QAAQ;AAAA;AACnD,eAAO,MAAM,YAAY,QAAQ,CAAC;AAAA,IACzC;AACA,QAAI,QAAQ;AACZ,WAAO,MAAM,QAAQ,IAAc;AAAE;AAAS,YAAM,QAAQ;AAAA,IAAG;AAC/D,SAAK,QAAQ,MAAM,QAAQ,SACtB,SAAS,MAAM,QAAQ,SAAS,MAAM,QAAQ,QAAQ,aACrD,MAAM,QAAQ,MAAgB,CAAC,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,IAEpD,MAAM,QAAQ,MAAM,CAAC,aAAa,MAAM,IAAI,KAAK,MAAM,QAAQ;AACjE,YAAM,YAAY,UAAU,CAAC,KAAK;AAAA,EACtC;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,IAAM,YAAY,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACxD,MAAI,MAAM,QAAQ,QAAQ,WAAW;AACnC,QAAI,MAAM,QAAQ,IAAc;AAC9B,YAAM,QAAQ;AACd,UAAI,MAAM,MAAM,IAAI;AAAG,cAAM,YAAY,WAAW;AAAA,IACtD;AACA;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,IAAc;AAC9B,UAAM,QAAQ;AACd,QAAI,MAAM,MAAM,IAAI;AAClB,YAAM,YAAY,MAAM,QAAQ,QAAQ,YAAY,MAAM,QAAQ,SAAS,WAAW,OAAO,MAAM,MAAM,CAAC,IACtF,uBAAuB,iBAAiB;AAAA,EAChE,WAAW,MAAM,QAAQ,IAAc;AACrC,UAAM,QAAQ;AACd,QAAI,MAAM,MAAM,IAAI;AAClB,YAAM,YAAY,MAAM,QAAQ,QAAQ,YAAY,MAAM,QAAQ,SAAS,WAAW,OAAO,MAAM,MAAM,CAAC,IACtF,0BAA0B,oBAAoB;AAAA,EACtE,OAAO;AACL,QAAI,QAAQ,MAAM;AAElB,eAAS;AACP,UAAI,gBAAgB,MAAM,IAAI,GAAG;AAC/B,YAAI,MAAM,OAAO;AAAO;AACxB,cAAM,QAAQ;AAAA,MAChB,WAAW,MAAM,QAAQ,IAAc;AACrC,gBAAQ,KAAK;AAAA,MACf,WAAW,MAAM,QAAQ,IAAc;AACrC,mBAAW,KAAK;AAAA,MAClB,WAAW,MAAM,QAAQ,IAAc;AACrC,mBAAW,KAAK;AAChB;AAAA,MACF,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,IAAc;AACnE,YAAI,WAAW,OAAO,IAAI;AAAG;AAC7B;AAAA,MACF,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,KAAe;AACpE,YAAI,CAAC,aAAa,KAAK;AAAG;AAC1B;AAAA,MACF,OAAO;AACL,kBAAU,OAAO,MAAM,OAAO,CAAC;AAC/B;AAAA,MACF;AAAA,IACF;AACA,WAAO,gBAAgB,MAAM,IAAI;AAAG,YAAM,QAAQ;AAClD,QAAI,MAAM,QAAQ,IAAc;AAC9B,UAAI,MAAM,OAAO,SAAS,MAAM,SAAS,KAAK;AAAG;AACjD,UAAI,QAAQ,MAAM,KAAK,CAAC;AACxB,UAAI,MAAM,KAAK;AACb,cAAM,cAAc,MAAM,QAAQ,QAAQ,YAAY,MAAM,QAAQ,SAAS,WAAW,OAAO,KAAK,IAC9E,kBAAkB,cAAc,KAAK;AAAA,IAC/D;AAAA,EACF;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,SAAS,QAAQ,IAAI;AACnB,SAAO,KAAK,MAAM,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MACtE,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM;AACpF;AAEA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM;AAC5E;AAEA,SAAS,YAAY,OAAO,QAAQ;AAClC,MAAI,MAAM,QAAQ,IAAc;AAC9B,UAAM,QAAQ;AACd,QAAI,QAAQ,MAAM,IAAI;AAAG,YAAM,QAAQ;AACvC,QAAI,QAAQ,MAAM,IAAI;AAAG,YAAM,QAAQ;AACvC,WAAO;AAAA,EACT,WAAW,QAAQ,MAAM,IAAI,KAAK,UAAU,MAAM,QAAQ,IAAc;AACtE,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,OAAO;AACtB,QAAM,QAAQ;AACd,MAAI,MAAM,QAAQ,IAAc;AAC9B,UAAM,QAAQ;AACd,eAAS;AACP,UAAI,CAAC,YAAY,OAAO,IAAI,GAAG;AAC7B,YAAI,MAAM,QAAQ;AAAc,gBAAM,QAAQ;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,YAAY,OAAO,KAAK,GAAG;AAAA,IAAC;AAAA,EACrC;AACF;AAEA,SAAS,WAAW,OAAO;AACzB,QAAM,QAAQ;AACd,SAAO,CAAC,MAAM,MAAM,IAAI,KAAK,QAAQ,MAAM,GAAG,KAAK;AAAK,UAAM,QAAQ;AACxE;AAEA,SAAS,WAAW,OAAO,MAAM;AAC/B,MAAI,QAAQ,MAAM,MAAM,YAAY,OAAO,QAAQ,MAAM;AACzD,QAAM,QAAQ;AACd,aAAS;AACP,QAAI,KAAK,MAAM;AACf,QAAI,KAAK;AAAG;AACZ,UAAM,QAAQ;AACd,QAAI,MAAM,OAAO;AACf,UAAI,MAAM,IAAc;AACtB,YAAI,MAAM,QAAQ;AAAI,gBAAM,QAAQ;AAAA;AAC/B;AAAA,MACP,OAAO;AACL;AAAA,MACF;AAAA,IACF,WAAW,MAAM,MAAiB,SAAS,IAAc;AACvD,UAAI,MAAM,QAAQ;AAAG,cAAM,QAAQ;AAAA,IACrC,WAAW,aAAa,EAAE,GAAG;AAC3B,UAAI;AAAM,eAAO;AACjB,kBAAY;AAAA,IACd,WAAW,QAAQ,MAAM,OAAO,QAAQ,MAAM;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAEA,SAAS,aAAa,OAAO;AAC3B,WAAS,QAAQ,CAAC,GAAG,MAAM,MAAM,MAAM,UAAQ;AAC7C,QAAI,MAAM,QAAQ,MAAgB,MAAM,QAAQ,KAAe;AAC7D,YAAM,KAAK,MAAM,IAAI;AACrB,YAAM,QAAQ;AAAA,IAChB,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,IAAc;AACnE,UAAI,CAAC,WAAW,OAAO,IAAI;AAAG,eAAO;AAAA,IACvC,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,KAAe;AACpE,UAAI,MAAM,MAAM,SAAS,CAAC,KAAK,MAAM,OAAO;AAAG,eAAO;AACtD,YAAM,IAAI;AACV,YAAM,QAAQ;AACd,UAAI,CAAC,MAAM;AAAQ,eAAO;AAAA,IAC5B,WAAW,MAAM,OAAO,KAAK,MAAM,MAAM,OAAO,aAAa,MAAM,IAAI,GAAG;AACxE,aAAO;AAAA,IACT,OAAO;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;AAGA,IAAM,YAAY;AAElB,SAAS,QAAQ,IAAI;AACnB,MAAI,KAAK;AAAI,WAAO;AACpB,MAAI,KAAK;AAAK,WAAO;AACrB,SAAO,UAAU,KAAK,EAAE;AAC1B;AAEA,SAAS,OAAO,IAAI,QAAQ;AAC1B,MAAI,MAAM,QAAQ,EAAE;AACpB,SAAO,OAAO,OAAO,EAAE,UAAU,OAAO;AAC1C;AAEA,SAAS,UAAU,OAAO,MAAM,QAAQ,QAAQ;AAC9C,MAAI,QAAQ,MAAM,IAAI,KAAK,QACtB,MAAM,QAAQ,MAAgB,MAAM,QAAQ,MAAgB,MAAM,QAAQ,OAC3E,OAAO,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG;AACjC,UAAM,QAAQ;AAAA,EAChB,OAAO;AACL,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,MAAM;AAClB,aAAS;AACP,QAAI,OAAO,MAAM,MAAM,MAAM,GAAG,aAAa,SAAS;AACtD,WAAO,QAAQ,IAAI,GAAG;AACpB,UAAI,aAAa,IAAI,GAAG;AACtB,YAAI;AAAM,iBAAO;AACjB,qBAAa;AAAA,MACf,OAAO;AACL;AAAA,MACF;AACA,aAAO,MAAM,KAAK,EAAE,GAAG;AAAA,IACzB;AACA,QAAI,OAAO,QAAQ,MACd,QAAQ,KAAe,OAAO,MAAM,KAAK,MAAM,CAAC,GAAG,MAAM,IACzD,QAAQ,KAAe,MAAM,KAAK,MAAM,CAAC,KAAK,KAC9C,OAAO,MAAM,MAAM;AACxB,QAAI,CAAC,QAAQ,CAAC,UAAU,cAAc,UAClC,cAAc,KAAK,CAAC,WAAW,MAAM,OAAO,IAAI,GAAG,KAAK,MAAM,OAAO,IAAI,GAAG;AAC9E;AACF,QAAI,QAAQ,QAAQ,IAAI,KAAK;AAAK,aAAO;AACzC,aAAS,IAAI,KAAK,KAAK,GAAG;AAAK,YAAM,QAAQ;AAC7C,QAAI,QAAQ,MAAM,MAAM,QAAQ;AAAM,aAAO;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI,MAAM,QAAQ,IAAc;AAC9B,YAAQ,KAAK;AACb,UAAM,YAAY,GAAG;AAAA,EACvB,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,IAAc;AACnE,QAAI,QAAQ,MAAM,QAAQ,KAAK,SAAS;AACxC,eAAW,KAAK;AAChB,UAAM,YAAY,KAAK;AAAA,EACzB,WAAW,MAAM,QAAQ,MAAgB,MAAM,QAAQ,IAAc;AACnE,eAAW,OAAO,KAAK;AACvB,UAAM,YAAY,aAAa;AAAA,EACjC,WAAW,UAAU,OAAO,OAAO,MAAM,QAAQ,QAAQ,WAAW,MAAM,QAAQ,KAAK,GAAG;AACxF,UAAM,YAAY,OAAO;AAAA,EAC3B;AACF,CAAC;AAED,IAAM,eAAe,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC3D,MAAI,SAAS,MAAM,QAAQ,QAAQ,WAAW,MAAM,QAAQ,QAAQ,IAAI,OAAO,MAAM;AACrF;AAAM,eAAS;AACb,UAAI,QAAQ,GAAG,OAAO,MAAM;AAC5B,aAAO,QAAQ;AAAc,eAAO,MAAM,KAAK,EAAE,KAAK;AACtD,UAAI,CAAC,UAAU,MAAM,OAAO,IAAI,KAAK,KAAK,MAAM,OAAO,IAAI,KAAK;AAAI;AACpE,UAAI,CAAC,aAAa,IAAI,GAAG;AACvB,YAAI,SAAS;AAAG,mBAAS,KAAK,IAAI,MAAM,QAAQ,QAAQ,GAAG,KAAK;AAChE,YAAI,QAAQ;AAAQ;AAAA,MACtB;AACA,iBAAS;AACP,YAAI,MAAM,OAAO;AAAG,gBAAM;AAC1B,YAAI,UAAU,aAAa,MAAM,IAAI;AACrC,cAAM,QAAQ;AACd,YAAI;AAAS,mBAAS;AACtB,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AACA,QAAM,cAAc,qBAAqB,IAAI;AAC/C,CAAC;AAED,IAAM,mBAAmB,UAAU;AAAA,EACjC,eAAe,KAAK;AAAA,EACpB,kBAAkB,KAAK;AAAA,EACvB,uBAAuB,KAAK;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,oBAAoB,KAAK,QAAQ,KAAK,MAAM;AAAA,EAC5C,qBAAqB,KAAK;AAAA,EAC1B,SAAS,KAAK;AAAA,EACd,iCAAiC,KAAK,WAAW,KAAK,YAAY;AAAA,EAClE,gBAAgB,KAAK;AAAA,EACrB,KAAK,KAAK;AAAA,EACV,SAAS,KAAK;AAAA,EACd,SAAS,KAAK;AAAA,EACd,KAAK,KAAK;AAAA,EACV,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,GAAE,IAAG,EAAE;AAAA,IACxB,CAAC,YAAY,IAAG,KAAI,IAAG,GAAG;AAAA,IAC1B,CAAC,YAAY,IAAG,KAAI,IAAG,GAAG;AAAA,EAC5B;AAAA,EACA,aAAa,CAAC,gBAAgB;AAAA,EAC9B,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,UAAU,WAAW,UAAU,cAAc,GAAG,CAAC;AAAA,EAC9D,UAAU,EAAC,UAAS,CAAC,GAAE,EAAE,EAAC;AAAA,EAC1B,WAAW;AACb,CAAC;;;AC7XD,IAAMA,UAAsB,SAAS,YAAY;AAAA,EAC/C,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,CAAC;AAAA,EACd,UAAU,EAAC,YAAW,CAAC,GAAE,CAAC,EAAC;AAAA,EAC3B,WAAW;AACb,CAAC;AAOD,IAAM,eAA4B,WAAW,OAAO;AAAA,EAChD,MAAM;AAAA,EACN,QAAqB,OAAS,UAAU;AAAA,IACpC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,QAAQ,QAAM;AACV,mBAAS,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,EAAE,GAAG,UAAU,OAAO,MAAM,GAAG,KAAK,SAAS,OAAO,QAAQ;AAClG,gBAAI,OAAO,QAAQ,yBAAyB,OAAO,OAAO,OAAO;AAC7D,qBAAO,GAAG,cAAc,MAAM;AAClC,gBAAI,OAAO,QAAQ;AACf,qBAAO,GAAG,cAAc,MAAM,IAAI,GAAG;AACzC,gBAAI,OAAO,QAAQ,mBAAmB,OAAO,QAAQ;AACjD,qBAAO,GAAG,OAAO,OAAO,MAAM,CAAC;AACnC,gBAAI,OAAO,QAAQ;AACf,qBAAO;AACX,gBAAI,OAAO,QAAQ,WAAW;AAC1B,kBAAI,MAAM,GAAG,OAAO,OAAO,MAAM,CAAC;AAClC,kBAAI,OAAO,GAAG,WAAW,OAAO,MAAM,CAAC;AACnC,uBAAO;AACX,kBAAI,OAAO,KAAK,GAAG;AACf,uBAAO;AAAA,YACf;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,aAA0B,gBAAgB,EAAE,SAAS,IAAI,CAAC;AAAA,QAC1D,cAA2B,gBAAgB,EAAE,SAAS,IAAI,CAAC;AAAA,MAC/D,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,4BAA4B;AAAA,QAC5B,mCAAmC,CAAC,MAAM,WAAW,EAAE,MAAM,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,GAAG;AAAA,MAC7G,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,MAAM,IAAI;AAAA,IAC3B,eAAe;AAAA,EACnB;AACJ,CAAC;AAID,SAAS,OAAO;AACZ,SAAO,IAAI,gBAAgB,YAAY;AAC3C;AACA,IAAM,sBAAmC,WAAW,OAAO;AAAA,EACvD,MAAM;AAAA,EACN,QAAqBA,QAAO,UAAU;AAAA,IAClC,OAAO,CAAc,UAAU,EAAE,UAAU,KAAK,KAAK,CAAC,CAAC;AAAA,EAC3D,CAAC;AACL,CAAC;AAMD,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,EAAE,UAAU,QAAQ,IAAI,OAAO,mBAAmB,kBAAkB,OAAO,UACzE,EAAE,UAAU,OAAO,SAAS,SAAS,CAAC,EAAE;AAC9C,SAAO,IAAI,gBAAgB,oBAAoB,UAAU;AAAA,IACrD,MAAM,WAAW,UAAQ;AACrB,aAAO,KAAK,QAAQ,uBAAuB,EAAE,QAAQ,aAAa,OAAO,IACnE,KAAK,QAAQ,SAAS,EAAE,QAAQ,SAAS,OAAO,IAC5C;AAAA,IACd,CAAC;AAAA,EACL,CAAC,GAAG,OAAO;AACf;", "names": ["parser"]}