<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ForumPostMapper">
    
    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.ForumPost">
        <id column="lzhshtp_post_id" property="postId" />
        <result column="lzhshtp_title" property="title" />
        <result column="lzhshtp_content" property="content" />
        <result column="lzhshtp_author_id" property="authorId" />
        <result column="lzhshtp_posted_at" property="postedAt" />
        <result column="lzhshtp_forum_category_id" property="forumCategoryId" />
        <result column="lzhshtp_views_count" property="viewsCount" />
        <result column="lzhshtp_is_pinned" property="isPinned" />
        <result column="lzhshtp_status" property="status" />
    </resultMap>
    
    <!-- 增加帖子浏览量 -->
    <update id="increaseViewCount">
        UPDATE tb_lzhshtp_forum_posts 
        SET lzhshtp_views_count = lzhshtp_views_count + 1 
        WHERE lzhshtp_post_id = #{postId}
    </update>
    
    <!-- 查询帖子列表（带分页和条件筛选） -->
    <select id="selectPostList" resultMap="BaseResultMap">
        SELECT 
            p.lzhshtp_post_id,
            p.lzhshtp_title,
            p.lzhshtp_content,
            p.lzhshtp_author_id,
            p.lzhshtp_posted_at,
            p.lzhshtp_forum_category_id,
            p.lzhshtp_views_count,
            p.lzhshtp_is_pinned,
            p.lzhshtp_status
        FROM 
            tb_lzhshtp_forum_posts p
        <where>
            <!-- 默认只查询已发布的帖子 -->
            p.lzhshtp_status = 0
            
            <!-- 根据分类过滤 -->
            <if test="categoryId != null">
                AND p.lzhshtp_forum_category_id = #{categoryId}
            </if>
            
            <!-- 根据作者过滤 -->
            <if test="authorId != null">
                AND p.lzhshtp_author_id = #{authorId}
            </if>
            
            <!-- 根据关键词搜索 -->
            <if test="keyword != null and keyword != ''">
                AND (
                    p.lzhshtp_title LIKE CONCAT('%', #{keyword}, '%')
                    OR p.lzhshtp_content LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            
            <!-- 是否只看置顶 -->
            <if test="onlyPinned != null and onlyPinned == true">
                AND p.lzhshtp_is_pinned = 1
            </if>
        </where>
        
        <!-- 排序 -->
        <choose>
            <when test="orderBy == 'posted_at'">
                ORDER BY p.lzhshtp_posted_at DESC
            </when>
            <when test="orderBy == 'views_count'">
                ORDER BY p.lzhshtp_views_count DESC
            </when>
            <when test="orderBy == 'random'">
                <if test="enablePinned != null and enablePinned == true">
                    ORDER BY p.lzhshtp_is_pinned DESC, RAND()
                </if>
                <if test="enablePinned == null or enablePinned == false">
                    ORDER BY RAND()
                </if>
            </when>
            <otherwise>
                ORDER BY p.lzhshtp_is_pinned DESC, p.lzhshtp_posted_at DESC
            </otherwise>
        </choose>
    </select>
    
    <!-- 查询帖子详情 -->
    <select id="selectPostDetail" resultMap="BaseResultMap">
        SELECT 
            p.lzhshtp_post_id,
            p.lzhshtp_title,
            p.lzhshtp_content,
            p.lzhshtp_author_id,
            p.lzhshtp_posted_at,
            p.lzhshtp_forum_category_id,
            p.lzhshtp_views_count,
            p.lzhshtp_is_pinned,
            p.lzhshtp_status
        FROM 
            tb_lzhshtp_forum_posts p
        WHERE 
            p.lzhshtp_post_id = #{postId}
    </select>
    
</mapper> 