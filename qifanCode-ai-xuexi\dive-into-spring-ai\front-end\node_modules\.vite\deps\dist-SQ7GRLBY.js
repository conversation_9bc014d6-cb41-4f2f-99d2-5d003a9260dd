import {
  defineCSSCompletionSource
} from "./chunk-ZPVSXOHE.js";
import {
  ContextTracker,
  ExternalTokenizer,
  LRLanguage,
  LRParser,
  LanguageSupport,
  continuedIndent,
  foldInside,
  foldNodeProp,
  indentNodeProp,
  styleTags,
  tags
} from "./chunk-TA37EJVK.js";
import "./chunk-TXPGJST7.js";

// node_modules/@lezer/sass/dist/index.js
var indent = 154;
var dedent = 155;
var descendantOp = 156;
var InterpolationEnd = 1;
var InterpolationContinue = 2;
var Unit = 3;
var callee = 157;
var identifier = 158;
var VariableName = 4;
var InterpolationStart = 5;
var newline = 159;
var blankLineStart = 160;
var eof = 161;
var whitespace = 162;
var LineComment = 6;
var Comment = 7;
var IndentedMixin = 8;
var IndentedInclude = 9;
var Dialect_indented = 0;
var space = [
  9,
  10,
  11,
  12,
  13,
  32,
  133,
  160,
  5760,
  8192,
  8193,
  8194,
  8195,
  8196,
  8197,
  8198,
  8199,
  8200,
  8201,
  8202,
  8232,
  8233,
  8239,
  8287,
  12288
];
var colon = 58;
var parenL = 40;
var underscore = 95;
var bracketL = 91;
var dash = 45;
var period = 46;
var hash = 35;
var percent = 37;
var braceL = 123;
var braceR = 125;
var slash = 47;
var asterisk = 42;
var newlineChar = 10;
var equals = 61;
var plus = 43;
var and = 38;
function isAlpha(ch) {
  return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161;
}
function isDigit(ch) {
  return ch >= 48 && ch <= 57;
}
function startOfComment(input) {
  let next;
  return input.next == slash && ((next = input.peek(1)) == slash || next == asterisk);
}
var spaces = new ExternalTokenizer((input, stack) => {
  if (stack.dialectEnabled(Dialect_indented)) {
    let prev;
    if (input.next < 0 && stack.canShift(eof)) {
      input.acceptToken(eof);
    } else if (((prev = input.peek(-1)) == newlineChar || prev < 0) && stack.canShift(blankLineStart)) {
      let spaces2 = 0;
      while (input.next != newlineChar && space.includes(input.next)) {
        input.advance();
        spaces2++;
      }
      if (input.next == newlineChar || startOfComment(input))
        input.acceptToken(blankLineStart, -spaces2);
      else if (spaces2)
        input.acceptToken(whitespace);
    } else if (input.next == newlineChar) {
      input.acceptToken(newline, 1);
    } else if (space.includes(input.next)) {
      input.advance();
      while (input.next != newlineChar && space.includes(input.next))
        input.advance();
      input.acceptToken(whitespace);
    }
  } else {
    let length = 0;
    while (space.includes(input.next)) {
      input.advance();
      length++;
    }
    if (length)
      input.acceptToken(whitespace);
  }
}, { contextual: true });
var comments = new ExternalTokenizer((input, stack) => {
  if (!startOfComment(input))
    return;
  input.advance();
  if (stack.dialectEnabled(Dialect_indented)) {
    let indentedComment = -1;
    for (let off = 1; ; off++) {
      let prev = input.peek(-off - 1);
      if (prev == newlineChar || prev < 0) {
        indentedComment = off + 1;
        break;
      } else if (!space.includes(prev)) {
        break;
      }
    }
    if (indentedComment > -1) {
      let block = input.next == asterisk, end = 0;
      input.advance();
      while (input.next >= 0) {
        if (input.next == newlineChar) {
          input.advance();
          let indented = 0;
          while (input.next != newlineChar && space.includes(input.next)) {
            indented++;
            input.advance();
          }
          if (indented < indentedComment) {
            end = -indented - 1;
            break;
          }
        } else if (block && input.next == asterisk && input.peek(1) == slash) {
          end = 2;
          break;
        } else {
          input.advance();
        }
      }
      input.acceptToken(block ? Comment : LineComment, end);
      return;
    }
  }
  if (input.next == slash) {
    while (input.next != newlineChar && input.next >= 0)
      input.advance();
    input.acceptToken(LineComment);
  } else {
    input.advance();
    while (input.next >= 0) {
      let { next } = input;
      input.advance();
      if (next == asterisk && input.next == slash) {
        input.advance();
        break;
      }
    }
    input.acceptToken(Comment);
  }
});
var indentedMixins = new ExternalTokenizer((input, stack) => {
  if ((input.next == plus || input.next == equals) && stack.dialectEnabled(Dialect_indented))
    input.acceptToken(input.next == equals ? IndentedMixin : IndentedInclude, 1);
});
var indentation = new ExternalTokenizer((input, stack) => {
  if (!stack.dialectEnabled(Dialect_indented))
    return;
  let cDepth = stack.context.depth;
  if (input.next < 0 && cDepth) {
    input.acceptToken(dedent);
    return;
  }
  let prev = input.peek(-1);
  if (prev == newlineChar) {
    let depth = 0;
    while (input.next != newlineChar && space.includes(input.next)) {
      input.advance();
      depth++;
    }
    if (depth != cDepth && input.next != newlineChar && !startOfComment(input)) {
      if (depth < cDepth)
        input.acceptToken(dedent, -depth);
      else
        input.acceptToken(indent);
    }
  }
});
var identifiers = new ExternalTokenizer((input, stack) => {
  for (let inside = false, dashes = 0, i = 0; ; i++) {
    let { next } = input;
    if (isAlpha(next) || next == dash || next == underscore || inside && isDigit(next)) {
      if (!inside && (next != dash || i > 0))
        inside = true;
      if (dashes === i && next == dash)
        dashes++;
      input.advance();
    } else if (next == hash && input.peek(1) == braceL) {
      input.acceptToken(InterpolationStart, 2);
      break;
    } else {
      if (inside)
        input.acceptToken(next == parenL ? callee : dashes == 2 && stack.canShift(VariableName) ? VariableName : identifier);
      break;
    }
  }
});
var interpolationEnd = new ExternalTokenizer((input) => {
  if (input.next == braceR) {
    input.advance();
    while (isAlpha(input.next) || input.next == dash || input.next == underscore || isDigit(input.next))
      input.advance();
    if (input.next == hash && input.peek(1) == braceL)
      input.acceptToken(InterpolationContinue, 2);
    else
      input.acceptToken(InterpolationEnd);
  }
});
var descendant = new ExternalTokenizer((input) => {
  if (space.includes(input.peek(-1))) {
    let { next } = input;
    if (isAlpha(next) || next == underscore || next == hash || next == period || next == bracketL || next == colon || next == dash || next == and)
      input.acceptToken(descendantOp);
  }
});
var unitToken = new ExternalTokenizer((input) => {
  if (!space.includes(input.peek(-1))) {
    let { next } = input;
    if (next == percent) {
      input.advance();
      input.acceptToken(Unit);
    }
    if (isAlpha(next)) {
      do {
        input.advance();
      } while (isAlpha(input.next));
      input.acceptToken(Unit);
    }
  }
});
function IndentLevel(parent, depth) {
  this.parent = parent;
  this.depth = depth;
  this.hash = (parent ? parent.hash + parent.hash << 8 : 0) + depth + (depth << 4);
}
var topIndent = new IndentLevel(null, 0);
var trackIndent = new ContextTracker({
  start: topIndent,
  shift(context, term, stack, input) {
    if (term == indent)
      return new IndentLevel(context, stack.pos - input.pos);
    if (term == dedent)
      return context.parent;
    return context;
  },
  hash(context) {
    return context.hash;
  }
});
var cssHighlighting = styleTags({
  "AtKeyword import charset namespace keyframes media supports include mixin use forward extend at-root": tags.definitionKeyword,
  "Keyword selector": tags.keyword,
  "ControlKeyword": tags.controlKeyword,
  NamespaceName: tags.namespace,
  KeyframeName: tags.labelName,
  TagName: tags.tagName,
  "ClassName Suffix": tags.className,
  PseudoClassName: tags.constant(tags.className),
  IdName: tags.labelName,
  "FeatureName PropertyName": tags.propertyName,
  AttributeName: tags.attributeName,
  NumberLiteral: tags.number,
  KeywordQuery: tags.keyword,
  UnaryQueryOp: tags.operatorKeyword,
  "CallTag ValueName": tags.atom,
  VariableName: tags.variableName,
  SassVariableName: tags.special(tags.variableName),
  Callee: tags.operatorKeyword,
  Unit: tags.unit,
  "UniversalSelector NestingSelector IndentedMixin IndentedInclude": tags.definitionOperator,
  MatchOp: tags.compareOperator,
  "ChildOp SiblingOp, LogicOp": tags.logicOperator,
  BinOp: tags.arithmeticOperator,
  "Important Global Default": tags.modifier,
  Comment: tags.blockComment,
  LineComment: tags.lineComment,
  ColorLiteral: tags.color,
  "ParenthesizedContent StringLiteral": tags.string,
  "InterpolationStart InterpolationContinue InterpolationEnd": tags.meta,
  ': "..."': tags.punctuation,
  "PseudoOp #": tags.derefOperator,
  "; ,": tags.separator,
  "( )": tags.paren,
  "[ ]": tags.squareBracket,
  "{ }": tags.brace
});
var spec_identifier = { __proto__: null, not: 62, only: 62, using: 179, as: 189, with: 193, without: 193, hide: 207, show: 207, from: 230, to: 232, if: 245, through: 251, in: 257 };
var spec_callee = { __proto__: null, url: 80, "url-prefix": 80, domain: 80, regexp: 80, lang: 94, "nth-child": 94, "nth-last-child": 94, "nth-of-type": 94, "nth-last-of-type": 94, dir: 94, "host-context": 94, selector: 172 };
var spec_AtKeyword = { __proto__: null, "@import": 156, "@include": 176, "@mixin": 182, "@function": 182, "@use": 186, "@extend": 196, "@at-root": 200, "@forward": 204, "@media": 210, "@charset": 214, "@namespace": 218, "@keyframes": 224, "@supports": 236, "@if": 240, "@else": 242, "@for": 248, "@each": 254, "@while": 260, "@debug": 264, "@warn": 264, "@error": 264, "@return": 264 };
var parser = LRParser.deserialize({
  version: 14,
  states: "LvQ`Q+tOOO#fQ+tOOP#mOpOOOOQ#U'#Ch'#ChO#rQ(pO'#CjOOQ#U'#Ci'#CiO%_Q)QO'#FxO%rQ.jO'#CnO&jQ#dO'#DWO'aQ(pO'#CgO'hQ)OO'#DYO'sQ#dO'#DaO'xQ#dO'#DeO'}Q#dO'#DnOOQ#U'#Fx'#FxO(SQ(pO'#FxO(ZQ(nO'#DrO%rQ.jO'#DzO%rQ.jO'#EVO%rQ.jO'#EYO%rQ.jO'#E[O(`Q)OO'#EaO)QQ)OO'#EcO%rQ.jO'#EeO)_Q)OO'#EhO%rQ.jO'#EjO)yQ)OO'#ElO*UQ#dO'#EoO*ZQ)OO'#EuO*oQ)OO'#FVOOQ&Z'#Fw'#FwOOQ&Y'#FY'#FYO*yQ(nO'#FYQ`Q+tOOO%rQ.jO'#EwO+UQ(nO'#E{O+ZQ)OO'#FOO%rQ.jO'#FRO%rQ.jO'#FTOOQ&Z'#Fa'#FaO+cQ+uO'#GRO+pQ(oO'#GRQOQ#SOOP,UO#SO'#FvPOOO)CAk)CAkOOQ#U'#Cm'#CmOOQ#U,59W,59WOOQ#i'#Cp'#CpO%rQ.jO'#CsO,dQ.wO'#CuO/PQ.^O,59YO%rQ.jO'#CzOOQ#S'#DO'#DOO/bQ(nO'#DTOOQ#i'#Fz'#FzO/gQ(nO'#C}OOQ#U'#DX'#DXOOQ#U,59r,59rO&jQ#dO,59rO/lQ)OO,59tO'sQ#dO,59{O'xQ#dO,5:PO(`Q)OO,5:TO(`Q)OO,5:VO(`Q)OO,5:WO(`Q)OO'#F`O/wQ(nO,59RO0SQ+tO'#DpO0ZQ#TO'#DpOOQ&Z,59R,59ROOQ#U'#D['#D[OOQ#S'#D_'#D_OOQ#U,59t,59tO0`Q(nO,59tO0eQ(nO,59tOOQ#U'#Dc'#DcOOQ#U,59{,59{OOQ#S'#Dg'#DgO0jQ9`O,5:POOQ#U'#Do'#DoOOQ#U,5:Y,5:YO1jQ.jO,5:^O1tQ.jO,5:fO2mQ.jO,5:qO2zQ.YO,5:tO3]Q.jO,5:vOOQ#U'#Cj'#CjO4UQ(pO,5:{O4cQ(pO,5:}OOQ&Z,5:},5:}O4jQ)OO,5:}O4oQ.jO,5;POOQ#S'#D}'#D}O5_Q)OO'#ESO5fQ(nO'#GTO*ZQ)OO'#ERO5zQ(nO'#ETOOQ#S'#GU'#GUO/zQ(nO,5;SO3cQ.YO,5;UOOQ#d'#En'#EnO*yQ(nO,5;WO6PQ)OO,5;WOOQ#S'#Eq'#EqO6XQ(nO,5;ZO6^Q(nO,5;aO6iQ(nO,5;qOOQ&Z'#GS'#GSOOQ&Y,5;t,5;tOOQ&Y-E9W-E9WO2zQ.YO,5;cO6wQ)OO,5;gO6|Q)OO'#GWO7UQ)OO,5;jO2zQ.YO,5;mO3cQ.YO,5;oOOQ&Z-E9_-E9_O7ZQ(oO,5<mO7oQ+uO'#FdO7ZQ(oO,5<mPOO#S'#FX'#FXP8VO#SO,5<bPOOO,5<b,5<bO8eQ.YO,59_OOQ#i,59a,59aO%rQ.jO,59cO%rQ.jO,59hO%rQ.jO'#F]O8sQ#WO1G.tOOQ#k1G.t1G.tO8{Q.oO,59fO;eQ! lO,59oO<bQ.jO'#DPOOQ#i,59i,59iOOQ#U1G/^1G/^OOQ#U1G/`1G/`O0`Q(nO1G/`O0eQ(nO1G/`OOQ#U1G/g1G/gO<lQ9`O1G/kO=VQ(pO1G/oO=yQ(pO1G/qO>mQ(pO1G/rO?aQ(pO,5;zOOQ#S-E9^-E9^OOQ&Z1G.m1G.mO?nQ(nO,5:[O?sQ+uO,5:[O?zQ)OO'#D`<EMAIL>'#D^OOQ#U1G/k1G/kO%rQ.jO1G/kO@}Q.jO'#DtOAXQ.kO1G/xOOQ#T1G/x1G/xO*yQ(nO1G0QOBUQ+uO'#GSOOQ&Z1G0]1G0]O/gQ(nO1G0]OOQ&Z1G0`1G0`OOQ&Z1G0b1G0bO/gQ(nO1G0bODqQ)OO1G0bOOQ&Z1G0g1G0gOOQ&Z1G0i1G0iODyQ)OO1G0iOEOQ(nO1G0iOETQ)OO1G0kOOQ&Z1G0k1G0kOEcQ.jO'#FfOEsQ#dO1G0kOExQ(nO'#D}OFTQ(nO,5:jOFYQ(nO,5:nO*ZQ)OO,5:lOFbQ)OO'#FeOFuQ(nO,5<oOGWQ(nO,5:mO(`Q)OO,5:oOOQ&Z1G0n1G0nOOQ&Z1G0p1G0pOOQ&Z1G0r1G0rO*yQ(nO1G0rOGoQ)OO'#ErOOQ&Z1G0u1G0uOOQ&Z1G0{1G0{OOQ&Z1G1]1G1]OG}Q+uO1G0}O%rQ.jO1G1ROJjQ)OO'#FjOJuQ)OO,5<rO%rQ.jO1G1UOOQ&Z1G1X1G1XOOQ&Z1G1Z1G1ZOJ}Q(oO1G2XOKcQ+uO,5<OOOQ#T,5<O,5<OOOQ#T-E9b-E9bPOO#S-E9V-E9VPOOO1G1|1G1|OOQ#i1G.y1G.yOKyQ.oO1G.}OOQ#i1G/S1G/SONcQ.^O,5;wOOQ#W-E9Z-E9ZOOQ#k7+$`7+$`ONtQ(nO1G/ZONyQ.jO'#FZO!!TQ.jO'#F}O!#lQ.jO'#FzO!#sQ(nO,59kOOQ#U7+$z7+$zOOQ#U7+%V7+%VO%rQ.jO7+%VOOQ&Z1G/v1G/vO!#xQ#TO1G/vO!#}Q(pO'#GPO!$XQ(nO,59zO!$^Q.jO'#GOO!$hQ(nO,59xO!$mQ.YO7+%VO!${Q.YO'#FzO!%^Q(nO,5:`OOQ#T,5:`,5:`O!%fQ.kO'#FcO%rQ.jO'#FcO!'VQ.kO7+%dOOQ#T7+%d7+%dOOQ&Z7+%l7+%lO6iQ(nO7+%wO*yQ(nO7+%|OOQ#d'#E_'#E_O!'yQ)OO7+%|O!(XQ(nO7+&TO*ZQ)OO7+&TOOQ#d-E9d-E9dOOQ&Z7+&V7+&VO!(^Q.jO'#GVOOQ#d,5<Q,5<QODtQ(nO7+&VO%rQ.jO1G0UOOQ#S1G0Y1G0YOOQ#S1G0W1G0WO!(xQ(nO,5<POOQ#S-E9c-E9cO!)^Q(pO1G0ZOOQ&Z7+&^7+&^O!)eQ(vO'#CuO/zQ(nO'#FhO!)pQ)OO,5;^OOQ&Z,5;^,5;^O!*OQ+uO7+&iO!,kQ)OO7+&iO!,vQ.jO7+&mOOQ#d,5<U,5<UOOQ#d-E9h-E9hO2zQ.YO7+&pOOQ#T1G1j1G1jOOQ#i7+$u7+$uOOQ#d-E9X-E9XO!-XQ.jO'#F[O!-fQ(nO,5<iO!-fQ(nO,5<iO%rQ.jO,5<iOOQ#i1G/V1G/VO!-nQ.YO<<HqOOQ&Z7+%b7+%bO!-|Q)OO'#F_O!.WQ(nO,5<kOOQ#U1G/f1G/fO!.`Q.jO'#F^O!.jQ(nO,5<jOOQ#U1G/d1G/dOOQ#U<<Hq<<HqO0rQ.jO,5;|O!.rQ(nO'#FbOOQ#S-E9`-E9`OOQ#T1G/z1G/zO!.wQ.kO,5;}OOQ#e-E9a-E9aOOQ#T<<IO<<IOOOQ&Z<<Ic<<IcOOQ&Z<<Ih<<IhO/gQ(nO<<IhO*ZQ)OO<<IoO!0hQ(nO<<IoO!0pQ.jO'#FgO!1TQ)OO,5<qOETQ)OO<<IqO!1fQ.jO7+%pOOQ#S7+%u7+%uOOQ#d,5<S,5<SOOQ#d-E9f-E9fOOQ&Z1G0x1G0xOOQ&Z-E9g-E9gO!,kQ)OO<<JTO%rQ.jO,5<TOOQ&Z<<JT<<JTO%rQ.jO<<JXOOQ&Z<<J[<<J[O!1mQ.jO,5;vO!1zQ.jO,5;vOOQ#S-E9Y-E9YO!2RQ(nO1G2TO!2ZQ.jO1G2TOOQ#UAN>]AN>]O!2eQ(pO,5;yOOQ#S-E9]-E9]O!2oQ.jO,5;xOOQ#S-E9[-E9[O!2yQ.YO1G1hO!3_Q(nO1G1hO*yQ(nOAN?SO!3jQ(nOAN?ZO/zQ(nOAN?ZO!3rQ.jO,5<ROOQ#d-E9e-E9eOETQ)OOAN?]OOQ&ZAN?]AN?]OOQ#S<<I[<<I[P!4^Q)OO'#FiOOQ&ZAN?oAN?oO2zQ.YO1G1oO2zQ.YOAN?sOOQ#S1G1b1G1bO%rQ.jO1G1bO!4cQ(nO7+'oOOQ#S7+'S7+'SOOQ&ZG24nG24nO/zQ(nOG24uOOQ&ZG24uG24uOOQ&ZG24wG24wOOQ&Z7+'Z7+'ZOOQ&ZG25_G25_O!4kQ.jO7+&|OOQ&ZLD*aLD*a",
  stateData: "!4{~O$hOSVOSUOS$fQQ~OS`OTVOWcOXbO_UOc`OtYO}YO!UZO!Y[O!omO!paO!zbO!}cO#PdO#UeO#WfO#YgO#]hO#_iO#ajO#dkO#jlO#lrO#psO#stO#vuO#xvO$dSO$mRO$pWO$t]O~O$_$uP~P`O$f{O~Ot^Xt!gXv^X}^X!U^X!Y^X!^^X!a^X!e^X$b^X$e^X$p^X~Ot$lXv$lX}$lX!U$lX!Y$lX!^$lX!a$lX!e$lX$b$lX$e$lX$p$lX~O$d}O!l$lX$g$lXf$lXe$lX~P$jOS!WOTVO_!WOc!WOf!QOh!WOj!WOo!TOx!VO$c!UO$d!PO$o!RO~O$d!YO~Ot!]O}!]O!U!^O!Y!_O!^!`O!a!bO!e!eO$b!aO$e!fO$p![O~Ov!cO~P&oO!P!lO$c!iO$d!hO~O$d!mO~O$d!oO~O$d!qO~Ot!sO~P$jOt!sO~OTVO_UOtYO}YO!UZO!Y[O$d!xO$mRO$pWO$t]O~Of!|O!e!eO$e!fO~P(`OTVOc#TOf#POo#RO!x#SO$d#OO!e$wP$e$wP~Oj#XOx!VO$d#WO~O$d#ZO~OTVOc#TOf#POo#RO!x#SO$d#OO~O!l$wP$g$wP~P)_O!l#_O$e#_O$g#_O~Oc#cO~Oc#dO#t$zP~O$_$uX!m$uX$a$uX~P`O!l#_O$e#_O$g#_O$_$uX!m$uX$a$uX~OU#lOV#lO$e#nO$h#lO~OR#pOPiXQiXliXmiX$piXTiXciXfiXoiX!liX!xiX$diX$eiX$giX!eiX!{iX#QiX#SiX#ZiXeiXSiX_iXhiXjiXviXxiX!iiX!jiX!kiX$ciX$oiX$_iXuiX!WiX#hiX#qiX!miX$aiX~OP#uOQ#sOl#qOm#qO$p#rO~Of#wO~Of#xO~O!P#}O$c!iO$d!hO~Ov!cO!e!eO$e!fO~O!m$uP~P`O$`$XO~Of$YO~Of$ZO~O!W$[O![$]O~OS!WOTVO_!WOc!WOf$^Oh!WOj!WOo!TOx!VO$c!UO$d!PO$o!RO~O!e!eO$e!fO~P0rOl#qOm#qO$p#rO!l$wP$e$wP$g$wP~P*ZOl#qOm#qO!l#_O$g#_O$p#rO~O!e!eO!{$dO$e$bO~P2[Ol#qOm#qO!e!eO$e!fO$p#rO~O#Q$hO#S$gO$e#_O~P2[Ot!]O}!]O!U!^O!Y!_O!^!`O!a!bO$b!aO$p![O~O!l#_O$e#_O$g#_O~P3jOf$kO~P&oO#S$lO~O#Q$pO#Z$oO$e#_O~P2[OTVOc#TOf#POo#RO!x#SO~O$d$qO~P4|Om$tOv$uO!e$wX$e$wX!l$wX$g$wX~Of$xO~Oj$|Ox!VO~O!e$}O~Om$tO!e!eO$e!fO~O!e!eO!l#_O$e$bO$g#_O~O#g%SO~Ov%TO#t$zX~O#t%VO~O!l#_O$e#_O$g#_O$_$ua!m$ua$a$ua~O!l$WX$_$WX$e$WX$g$WX!m$WX$a$WX~P`OU#lOV#lO$e%_O$h#lO~Oe%`Ol#qOm#qO$p#rO~OP%eOQ#sO~Ol#qOm#qO$p#rOPnaQnaTnacnafnaona!lna!xna$dna$ena$gna!ena!{na#Qna#Sna#ZnaenaSna_nahnajnavnaxna!ina!jna!kna$cna$ona$_nauna!Wna#hna#qna!mna$ana~Oj%fOy%fO~OS!WOTVO_!WOf!QOh!WOj!WOo!TOx!VO$c!UO$d!PO$o!RO~Oc%iOe$qP~P;mO!W%lO![%mO~Ot!]O}!]O!U!^O!Y!_O$p![O~Ov!]i!^!]i!a!]i!e!]i$b!]i$e!]i!l!]i$g!]if!]ie!]i~P<tOv!_i!^!_i!a!_i!e!_i$b!_i$e!_i!l!_i$g!_if!_ie!_i~P<tOv!`i!^!`i!a!`i!e!`i$b!`i$e!`i!l!`i$g!`if!`ie!`i~P<tOv$Sa!e$Sa$e$Sa~P3jO!m%nO~O$a$uP~P`Oe$sP~P(`Oe$rP~P%rOS!WOTVO_!WOc!WOf!QOh!WOo!TOx!VO$c!UO$d!PO$o!RO~Oe%wOj%uO~P@YOl#qOm#qOv%yO!i%{O!j%{O!k%{O$p#rO!l!fi$e!fi$g!fi$_!fi!m!fi$a!fi~P%rO$`$XOS$vXT$vXW$vXX$vX_$vXc$vXt$vX}$vX!U$vX!Y$vX!o$vX!p$vX!z$vX!}$vX#P$vX#U$vX#W$vX#Y$vX#]$vX#_$vX#a$vX#d$vX#j$vX#l$vX#p$vX#s$vX#v$vX#x$vX$_$vX$d$vX$m$vX$p$vX$t$vX!m$vX!l$vX$e$vX$g$vX$a$vX~O$d!PO$m&PO~O#S&RO~Ot&SO~O!l#_O#Z$oO$e#_O$g#_O~O!l$yP#Z$yP$e$yP$g$yP~P%rO$d!PO~Oe!qXm!qXt!sX~Ot&YO~Oe&ZOm$tO~Ov$XX!e$XX$e$XX!l$XX$g$XX~P*ZOv$uO!e$wa$e$wa!l$wa$g$wa~Om$tOv!ua!e!ua$e!ua!l!ua$g!uae!ua~O!m&dO#g&bO#h&bO$o&aO~O#m&fOS#kiT#kiW#kiX#ki_#kic#kit#ki}#ki!U#ki!Y#ki!o#ki!p#ki!z#ki!}#ki#P#ki#U#ki#W#ki#Y#ki#]#ki#_#ki#a#ki#d#ki#j#ki#l#ki#p#ki#s#ki#v#ki#x#ki$_#ki$d#ki$m#ki$p#ki$t#ki!m#ki!l#ki$e#ki$g#ki$a#ki~Oc&hOv$^X#t$^X~Ov%TO#t$za~O!l#_O$e#_O$g#_O$_$ui!m$ui$a$ui~O!l$Wa$_$Wa$e$Wa$g$Wa!m$Wa$a$Wa~P`O$p#rOPkiQkilkimkiTkickifkioki!lki!xki$dki$eki$gki!eki!{ki#Qki#Ski#ZkiekiSki_kihkijkivkixki!iki!jki!kki$cki$oki$_kiuki!Wki#hki#qki!mki$aki~Ol#qOm#qO$p#rOP$PaQ$Pa~Oe&lO~Ol#qOm#qO$p#rOS#}XT#}X_#}Xc#}Xe#}Xf#}Xh#}Xj#}Xo#}Xu#}Xv#}Xx#}X$c#}X$d#}X$o#}X~Ou&pOv&nOe$qX~P%rOS$nXT$nX_$nXc$nXe$nXf$nXh$nXj$nXl$nXm$nXo$nXu$nXv$nXx$nX$c$nX$d$nX$o$nX$p$nX~Ot&qO~P!!bOe&rO~O$a&tO~Ov&uOe$sX~P3jOe&wO~Ov&xOe$rX~P%rOe&zO~Ol#qOm#qO!W&{O$p#rO~Ot&|Oe$nXl$nXm$nX$p$nX~Oe'POj&}O~Ol#qOm#qO$p#rOS$VXT$VX_$VXc$VXf$VXh$VXj$VXo$VXv$VXx$VX!i$VX!j$VX!k$VX!l$VX$c$VX$d$VX$e$VX$g$VX$o$VX$_$VX!m$VX$a$VX~Ov%yO!i'SO!j'SO!k'SO!l!fq$e!fq$g!fq$_!fq!m!fq$a!fq~P%rO!l#_O#S'VO$e#_O$g#_O~Ot'WO~Ol#qOm#qOv'YO$p#rO!l$yX#Z$yX$e$yX$g$yX~Om$tOv$Xa!e$Xa$e$Xa!l$Xa$g$Xa~Oe'^O~P3jOR#pO!eiX$eiX~O!m'aO#g&bO#h&bO$o&aO~O#m'cOS#kqT#kqW#kqX#kq_#kqc#kqt#kq}#kq!U#kq!Y#kq!o#kq!p#kq!z#kq!}#kq#P#kq#U#kq#W#kq#Y#kq#]#kq#_#kq#a#kq#d#kq#j#kq#l#kq#p#kq#s#kq#v#kq#x#kq$_#kq$d#kq$m#kq$p#kq$t#kq!m#kq!l#kq$e#kq$g#kq$a#kq~O!e!eO#n'dO$e!fO~Ol#qOm#qO#h'fO#q'fO$p#rO~Oc'iOe$OXv$OX~P;mOv&nOe$qa~Ol#qOm#qO!W'mO$p#rO~Oe$RXv$RX~P(`Ov&uOe$sa~Oe$QXv$QX~P%rOv&xOe$ra~Ot&|O~Ol#qOm#qO$p#rOS$VaT$Va_$Vac$Vaf$Vah$Vaj$Vao$Vav$Vax$Va!i$Va!j$Va!k$Va!l$Va$c$Va$d$Va$e$Va$g$Va$o$Va$_$Va!m$Va$a$Va~Oe'vOm$tO~Ov$ZX!l$ZX#Z$ZX$e$ZX$g$ZX~P%rOv'YO!l$ya#Z$ya$e$ya$g$ya~Oe'{O~P%rOu(QOe$Oav$Oa~P%rOt(RO~P!!bOv&nOe$qi~Ov&nOe$qi~P%rOe$Rav$Ra~P3jOe$Qav$Qa~P%rOl#qOm#qOv(TO$p#rOe$Uij$Ui~Ov(TOe$Uij$Ui~Oe(VOm$tO~Ol#qOm#qO$p#rOv$Za!l$Za#Z$Za$e$Za$g$Za~O#n'dO~Ov&nOe$qq~Oe$Oqv$Oq~P%rO$o$pl!al~",
  goto: "9z${PPPPPPPPPPP$|%W%W%kP%W&O&RP'sPP(xP)wP(xPP(xP(x(x*z+yPPP,VPP%W-[%WP-bP-h-n-t%WP-zP%WP.QP%WP%W%WP%W.W.ZP/l0O0YPPPPP$|PP'g'g0`'g'g'g'gP$|PP$|P$|PP0cP$|P$|P$|PP$|P$|P$|P0i$|P0l0oPP$|P$|PPP$|PP$|PP$|P$|P$|P0r0x1O1n1|2S2Y2`2f2r2x3O3Y3`3j3p3v3|PPPPPPPPPPP4S4V4cP5YPP7a7d7gP7j7s9P9Y9t9wanOPqx!e#j$X%Zs^OPefqx!`!a!b!c!e#j$X$Y$x%Z&usTOPefqx!`!a!b!c!e#j$X$Y$x%Z&uR!OUb^ef!`!a!b!c$Y$x&u`_OPqx!e#j$X%Z!x!WVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([e#Thlm!t#P#R$t$u&S'W!x!WVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([Q&Q$hR&X$p!y!WVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([!x!WVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([T&b$}&c!y!XVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([Q#y!XQ%}$dQ&O$gR't'V!x!WVabcdgiruv!Q!T!s#q#r#s#x$Z$]$^$_$o%S%V%h%m%r%y%z&Y&n&q&x&|'Y']'d'f'h'l'p(R([Q#XjR$|#YQ!ZWR#z![Q!jYR#{!]Q#{!lR%k#}Q!kYR#|!]Q#{!kR%k#|Q!nZR$O!^Q!p[R$P!_R!r]Q!gXQ!{fQ$V!dQ$`!sQ$c!uQ$e!vQ$j!zQ$y#UQ%P#]Q%Q#^Q%R#bQ%W#fQ'T%}Q'_&bQ'e&fQ'g&jQ'}'cQ(W'vQ(Y(OQ(Z(PR(](VSpOqUyP!e$XQ#ixQ%[#jR&k%Za`OPqx!e#j$X%ZQ$`!sR's&|R$r#PQ&Q$hR'[&XR#YjR#[kR%O#[Q#m{R%^#mQqOR#aqQ%h#xQ%r$Z^&m%h%r']'h'l'p([Q']&YQ'h&nQ'l&qQ'p&xR([(RQ&o%hU'j&o'k(SQ'k&pR(S'lQ#t!SR%d#tQ&y%rR'q&yQ&v%pR'o&vQ!dXR$U!dUxP!e$XS#hx%ZR%Z#jQ%v$^R'O%vQ%z$_R'R%zQ#kyQ%Y#iT%]#k%YQ$v#QR&^$vQ$m!}S&T$m'yR'y'[Q'Z&VR'x'ZQ&c$}R'`&cQ&e%RR'b&eQ%U#dR&i%UR|QSoOq]wPx!e#j$X%Z`XOPqx!e#j$X%ZQ!yeQ!zfQ$Q!`Q$R!aQ$S!bQ$T!cQ%p$YQ&_$xR'n&uQ!SVQ!taQ!ubQ!vcQ!wdQ!}gQ#ViQ#brQ#fuQ#gvS#o!Q$^Q#v!TQ$_!sQ%a#qQ%b#rQ%c#sl%g#x$Z%h%r&Y&n&q&x']'h'l'p(R([Q%t$]S%x$_%zQ&V$oQ&g%SQ&j%VQ&s%mQ'Q%yQ'r&|Q'w'YQ(O'dR(P'fR%j#xR%s$ZR%q$YQzPQ$W!eR%o$XQ#`pW#jy#i#k%YQ$c!uQ$f!wQ$i!yQ$n!}Q$z#VQ${#XQ%Q#^Q%X#gQ%|$aQ&U$mQ&`$|Q'T%}S'U&O&QQ'z'[Q(U'tR(X'yQ#UhQ#^mR$a!tU#Qhm!tQ#]lQ$s#PQ$w#RQ&[$tQ&]$uQ'X&SR'u'WR&W$oR#et",
  nodeNames: "⚠ InterpolationEnd InterpolationContinue Unit VariableName InterpolationStart LineComment Comment IndentedMixin IndentedInclude StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector SuffixedSelector Suffix Interpolation SassVariableName ValueName ) ( ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp LogicOp UnaryExpression LogicOp NamespacedValue CallExpression Callee ArgList : ... , CallLiteral CallTag ParenthesizedContent ClassSelector ClassName PseudoClassSelector :: PseudoClassName PseudoClassName ArgList PseudoClassName ArgList IdSelector # IdName ] AttributeSelector [ AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp PlaceholderSelector ClassName Block { Declaration PropertyName Map Important Global Default ; } ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery UnaryQuery ParenthesizedQuery SelectorQuery selector IncludeStatement include Keyword MixinStatement mixin UseStatement use Keyword Star Keyword ExtendStatement extend RootStatement at-root ForwardStatement forward Keyword MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList Keyword Keyword SupportsStatement supports IfStatement ControlKeyword ControlKeyword Keyword ForStatement ControlKeyword Keyword EachStatement ControlKeyword Keyword WhileStatement ControlKeyword OutputStatement ControlKeyword AtRule Styles",
  maxTerm: 180,
  context: trackIndent,
  nodeProps: [
    ["openedBy", 1, "InterpolationStart", 5, "InterpolationEnd", 21, "(", 75, "{"],
    ["isolate", -3, 6, 7, 26, ""],
    ["closedBy", 22, ")", 67, "}"]
  ],
  propSources: [cssHighlighting],
  skippedNodes: [0, 6, 7, 135],
  repeatNodeCount: 18,
  tokenData: "!!p~RyOq#rqr$jrs0jst2^tu8{uv;hvw;{wx<^xy={yz>^z{>c{|>||}Co}!ODQ!O!PDo!P!QFY!Q![Fk![!]Gf!]!^Hb!^!_Hs!_!`I[!`!aIs!a!b#r!b!cJt!c!}#r!}#OL^#O#P#r#P#QLo#Q#RMQ#R#T#r#T#UMg#U#c#r#c#dNx#d#o#r#o#p! _#p#qMQ#q#r! p#r#s!!R#s;'S#r;'S;=`!!j<%lO#rW#uSOy$Rz;'S$R;'S;=`$d<%lO$RW$WSyWOy$Rz;'S$R;'S;=`$d<%lO$RW$gP;=`<%l$RY$m[Oy$Rz!_$R!_!`%c!`#W$R#W#X%v#X#Z$R#Z#[)Z#[#]$R#]#^,V#^;'S$R;'S;=`$d<%lO$RY%jSyWlQOy$Rz;'S$R;'S;=`$d<%lO$RY%{UyWOy$Rz#X$R#X#Y&_#Y;'S$R;'S;=`$d<%lO$RY&dUyWOy$Rz#Y$R#Y#Z&v#Z;'S$R;'S;=`$d<%lO$RY&{UyWOy$Rz#T$R#T#U'_#U;'S$R;'S;=`$d<%lO$RY'dUyWOy$Rz#i$R#i#j'v#j;'S$R;'S;=`$d<%lO$RY'{UyWOy$Rz#`$R#`#a(_#a;'S$R;'S;=`$d<%lO$RY(dUyWOy$Rz#h$R#h#i(v#i;'S$R;'S;=`$d<%lO$RY(}S!kQyWOy$Rz;'S$R;'S;=`$d<%lO$RY)`UyWOy$Rz#`$R#`#a)r#a;'S$R;'S;=`$d<%lO$RY)wUyWOy$Rz#c$R#c#d*Z#d;'S$R;'S;=`$d<%lO$RY*`UyWOy$Rz#U$R#U#V*r#V;'S$R;'S;=`$d<%lO$RY*wUyWOy$Rz#T$R#T#U+Z#U;'S$R;'S;=`$d<%lO$RY+`UyWOy$Rz#`$R#`#a+r#a;'S$R;'S;=`$d<%lO$RY+yS!jQyWOy$Rz;'S$R;'S;=`$d<%lO$RY,[UyWOy$Rz#a$R#a#b,n#b;'S$R;'S;=`$d<%lO$RY,sUyWOy$Rz#d$R#d#e-V#e;'S$R;'S;=`$d<%lO$RY-[UyWOy$Rz#c$R#c#d-n#d;'S$R;'S;=`$d<%lO$RY-sUyWOy$Rz#f$R#f#g.V#g;'S$R;'S;=`$d<%lO$RY.[UyWOy$Rz#h$R#h#i.n#i;'S$R;'S;=`$d<%lO$RY.sUyWOy$Rz#T$R#T#U/V#U;'S$R;'S;=`$d<%lO$RY/[UyWOy$Rz#b$R#b#c/n#c;'S$R;'S;=`$d<%lO$RY/sUyWOy$Rz#h$R#h#i0V#i;'S$R;'S;=`$d<%lO$RY0^S!iQyWOy$Rz;'S$R;'S;=`$d<%lO$R~0mWOY0jZr0jrs1Vs#O0j#O#P1[#P;'S0j;'S;=`2W<%lO0j~1[Oj~~1_RO;'S0j;'S;=`1h;=`O0j~1kXOY0jZr0jrs1Vs#O0j#O#P1[#P;'S0j;'S;=`2W;=`<%l0j<%lO0j~2ZP;=`<%l0jZ2cY!UPOy$Rz!Q$R!Q![3R![!c$R!c!i3R!i#T$R#T#Z3R#Z;'S$R;'S;=`$d<%lO$RY3WYyWOy$Rz!Q$R!Q![3v![!c$R!c!i3v!i#T$R#T#Z3v#Z;'S$R;'S;=`$d<%lO$RY3{YyWOy$Rz!Q$R!Q![4k![!c$R!c!i4k!i#T$R#T#Z4k#Z;'S$R;'S;=`$d<%lO$RY4rYhQyWOy$Rz!Q$R!Q![5b![!c$R!c!i5b!i#T$R#T#Z5b#Z;'S$R;'S;=`$d<%lO$RY5iYhQyWOy$Rz!Q$R!Q![6X![!c$R!c!i6X!i#T$R#T#Z6X#Z;'S$R;'S;=`$d<%lO$RY6^YyWOy$Rz!Q$R!Q![6|![!c$R!c!i6|!i#T$R#T#Z6|#Z;'S$R;'S;=`$d<%lO$RY7TYhQyWOy$Rz!Q$R!Q![7s![!c$R!c!i7s!i#T$R#T#Z7s#Z;'S$R;'S;=`$d<%lO$RY7xYyWOy$Rz!Q$R!Q![8h![!c$R!c!i8h!i#T$R#T#Z8h#Z;'S$R;'S;=`$d<%lO$RY8oShQyWOy$Rz;'S$R;'S;=`$d<%lO$R_9O`Oy$Rz}$R}!O:Q!O!Q$R!Q![:Q![!_$R!_!`;T!`!c$R!c!}:Q!}#R$R#R#S:Q#S#T$R#T#o:Q#o;'S$R;'S;=`$d<%lO$RZ:X^yWcROy$Rz}$R}!O:Q!O!Q$R!Q![:Q![!c$R!c!}:Q!}#R$R#R#S:Q#S#T$R#T#o:Q#o;'S$R;'S;=`$d<%lO$R[;[S![SyWOy$Rz;'S$R;'S;=`$d<%lO$RZ;oS$tPlQOy$Rz;'S$R;'S;=`$d<%lO$RZ<QS_ROy$Rz;'S$R;'S;=`$d<%lO$R~<aWOY<^Zw<^wx1Vx#O<^#O#P<y#P;'S<^;'S;=`=u<%lO<^~<|RO;'S<^;'S;=`=V;=`O<^~=YXOY<^Zw<^wx1Vx#O<^#O#P<y#P;'S<^;'S;=`=u;=`<%l<^<%lO<^~=xP;=`<%l<^Z>QSfROy$Rz;'S$R;'S;=`$d<%lO$R~>cOe~_>jU$mPlQOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RZ?TWlQ!aPOy$Rz!O$R!O!P?m!P!Q$R!Q![Br![;'S$R;'S;=`$d<%lO$RZ?rUyWOy$Rz!Q$R!Q![@U![;'S$R;'S;=`$d<%lO$RZ@]YyW$oROy$Rz!Q$R!Q![@U![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RZAQYyWOy$Rz{$R{|Ap|}$R}!OAp!O!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZAuUyWOy$Rz!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZB`UyW$oROy$Rz!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZBy[yW$oROy$Rz!O$R!O!P@U!P!Q$R!Q![Br![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RZCtSvROy$Rz;'S$R;'S;=`$d<%lO$RZDVWlQOy$Rz!O$R!O!P?m!P!Q$R!Q![Br![;'S$R;'S;=`$d<%lO$RZDtW$pROy$Rz!O$R!O!PE^!P!Q$R!Q![@U![;'S$R;'S;=`$d<%lO$RYEcUyWOy$Rz!O$R!O!PEu!P;'S$R;'S;=`$d<%lO$RYE|SuQyWOy$Rz;'S$R;'S;=`$d<%lO$RYF_SlQOy$Rz;'S$R;'S;=`$d<%lO$RZFp[$oROy$Rz!O$R!O!P@U!P!Q$R!Q![Br![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RZGkUtROy$Rz![$R![!]G}!];'S$R;'S;=`$d<%lO$RXHUS}PyWOy$Rz;'S$R;'S;=`$d<%lO$RZHgS!lROy$Rz;'S$R;'S;=`$d<%lO$RYHxUlQOy$Rz!_$R!_!`%c!`;'S$R;'S;=`$d<%lO$R^IaU![SOy$Rz!_$R!_!`%c!`;'S$R;'S;=`$d<%lO$RZIzV!^PlQOy$Rz!_$R!_!`%c!`!aJa!a;'S$R;'S;=`$d<%lO$RXJhS!^PyWOy$Rz;'S$R;'S;=`$d<%lO$RXJwWOy$Rz!c$R!c!}Ka!}#T$R#T#oKa#o;'S$R;'S;=`$d<%lO$RXKh[!oPyWOy$Rz}$R}!OKa!O!Q$R!Q![Ka![!c$R!c!}Ka!}#T$R#T#oKa#o;'S$R;'S;=`$d<%lO$RXLcS!YPOy$Rz;'S$R;'S;=`$d<%lO$R^LtS!WUOy$Rz;'S$R;'S;=`$d<%lO$R[MTUOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RZMjUOy$Rz#b$R#b#cM|#c;'S$R;'S;=`$d<%lO$RZNRUyWOy$Rz#W$R#W#XNe#X;'S$R;'S;=`$d<%lO$RZNlSmRyWOy$Rz;'S$R;'S;=`$d<%lO$RZN{UOy$Rz#f$R#f#gNe#g;'S$R;'S;=`$d<%lO$RZ! dS!eROy$Rz;'S$R;'S;=`$d<%lO$RZ! uS!mROy$Rz;'S$R;'S;=`$d<%lO$R]!!WU!aPOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RW!!mP;=`<%l#r",
  tokenizers: [indentation, descendant, interpolationEnd, unitToken, identifiers, spaces, comments, indentedMixins, 0, 1, 2, 3],
  topRules: { "StyleSheet": [0, 10], "Styles": [1, 134] },
  dialects: { indented: 0 },
  specialized: [{ term: 158, get: (value) => spec_identifier[value] || -1 }, { term: 157, get: (value) => spec_callee[value] || -1 }, { term: 77, get: (value) => spec_AtKeyword[value] || -1 }],
  tokenPrec: 3027
});

// node_modules/@codemirror/lang-sass/dist/index.js
var sassLanguage = LRLanguage.define({
  name: "sass",
  parser: parser.configure({
    props: [
      foldNodeProp.add({
        Block: foldInside,
        Comment(node, state) {
          return { from: node.from + 2, to: state.sliceDoc(node.to - 2, node.to) == "*/" ? node.to - 2 : node.to };
        }
      }),
      indentNodeProp.add({
        Declaration: continuedIndent()
      })
    ]
  }),
  languageData: {
    commentTokens: { block: { open: "/*", close: "*/" }, line: "//" },
    indentOnInput: /^\s*\}$/,
    wordChars: "$-"
  }
});
var indentedSassLanguage = sassLanguage.configure({
  dialect: "indented",
  props: [
    indentNodeProp.add({
      "Block RuleSet": (cx) => cx.baseIndent + cx.unit
    }),
    foldNodeProp.add({
      Block: (node) => ({ from: node.from, to: node.to })
    })
  ]
});
var sassCompletionSource = defineCSSCompletionSource((node) => node.name == "VariableName" || node.name == "SassVariableName");
function sass(config) {
  return new LanguageSupport((config === null || config === void 0 ? void 0 : config.indented) ? indentedSassLanguage : sassLanguage, sassLanguage.data.of({ autocomplete: sassCompletionSource }));
}
export {
  sass,
  sassCompletionSource,
  sassLanguage
};
//# sourceMappingURL=dist-SQ7GRLBY.js.map
