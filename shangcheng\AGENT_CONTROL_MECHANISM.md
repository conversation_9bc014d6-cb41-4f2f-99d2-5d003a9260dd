# 🤖 Agent控制机制详解

## 概述

本文档详细说明了AI聊天系统中Agent功能的控制机制，包括如何启用/禁用Agent、参数传递、以及内部处理流程。

## 🔧 控制流程

### 1. 前端参数设置
前端通过`AiChatRequest`的`params.enableAgent`参数来控制是否使用Agent：

```javascript
// 启用Agent模式
const response = await fetch('/api/ai/chat/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    content: message,
    params: {
      enableAgent: true,      // 🔑 关键参数：启用Agent
      enableMemory: true,     // 启用记忆功能
      historyMessageCount: 10 // 历史消息数量
    }
  })
})
```

### 2. 控制器层处理
`AiChatController.chatStream()`方法接收请求并转发给服务层：

```java
@PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
@PreAuthorize("isAuthenticated()")
public Flux<ServerSentEvent<AiChatResponse>> chatStream(@Valid @RequestBody AiChatRequest request) {
    Long userId = getCurrentUserId();
    
    // 直接转发给服务层，由服务层根据参数决定处理方式
    return aiChatService.chatStream(request, userId)
        .map(response -> ServerSentEvent.<AiChatResponse>builder()
            .data(response)
            .build());
}
```

### 3. 服务层路由决策
`AiChatServiceImpl.chatStream()`根据参数选择不同的处理方式：

```java
@Override
public Flux<AiChatResponse> chatStream(AiChatRequest request, Long userId) {
    // 🎯 核心路由逻辑
    if (request.getParams().getEnableAgent()) {
        return chatStreamWithAgent(request, userId);      // Agent模式
    } else if (request.isUseRAG()) {
        return chatStreamWithRAG(request, userId);        // RAG模式
    } else {
        return chatStreamNormal(request, userId);         // 普通模式
    }
}
```

## 📋 参数结构

### AiChatRequest结构
```java
public class AiChatRequest {
    private String sessionId;           // 会话ID
    private String content;             // 用户消息
    private ChatParams params;          // 🔑 聊天参数
    private Map<String, Object> contextData; // 上下文数据
    
    public static class ChatParams {
        private Boolean enableAgent = false;    // 🤖 是否启用Agent
        private Boolean enableRag = false;      // 📚 是否启用RAG
        private Boolean enableMemory = true;    // 🧠 是否启用记忆
        private Integer historyMessageCount = 10; // 历史消息数量
    }
}
```

### 参数优先级
1. **enableAgent = true** → Agent模式（最高优先级）
2. **enableRag = true** → RAG模式
3. **默认** → 普通聊天模式

## 🤖 Agent模式处理流程

### chatStreamWithAgent方法详解
```java
public Flux<AiChatResponse> chatStreamWithAgent(AiChatRequest request, Long userId) {
    return Flux.create(sink -> {
        try {
            // 1. 获取或创建会话
            AiSession session = getOrCreateSession(request, userId);
            
            // 2. 保存用户消息
            saveUserMessage(session.getId(), request.getContent(), 
                request.getMedias(), userId);
            
            // 3. 🔍 获取可用的Agent
            String[] agentBeanNames = getAvailableAgents();
            
            // 4. 构建聊天客户端
            ChatClient chatClient = ChatClient.create(chatModel);
            
            // 5. 构建提示词请求
            ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt()
                .system(getAgentSystemPrompt(session.getName()));
            
            // 6. 添加记忆功能
            if (request.getParams().getEnableMemory()) {
                MessageChatMemoryAdvisor memoryAdvisor = new MessageChatMemoryAdvisor(
                    chatMemory, session.getId(), 
                    request.getParams().getHistoryMessageCount());
                requestSpec = requestSpec.advisors(memoryAdvisor);
            }
            
            // 7. 🎯 添加Agent功能（Function Calling）
            if (agentBeanNames.length > 0) {
                requestSpec = requestSpec.functions(agentBeanNames);
            }
            
            // 8. 流式调用AI
            requestSpec
                .user(request.getContent())
                .stream()
                .content()
                .doOnNext(content -> {
                    // 实时返回内容片段
                    AiChatResponse response = AiChatResponse.stream(
                        session.getId(), messageId, content, false);
                    sink.next(response);
                })
                .doOnComplete(() -> {
                    // 完成时保存消息
                    saveAiMessage(session.getId(), fullResponse.toString(), userId);
                    sink.complete();
                });
                
        } catch (Exception e) {
            sink.error(e);
        }
    });
}
```

## 🔍 Agent发现机制

### getAvailableAgents方法
```java
private String[] getAvailableAgents() {
    try {
        // 🔍 扫描所有带@Agent注解的Bean
        Map<String, Object> agentBeans = applicationContext
            .getBeansWithAnnotation(Agent.class);
        return agentBeans.keySet().toArray(new String[0]);
    } catch (Exception e) {
        log.warn("获取Agent列表失败", e);
        return new String[0];
    }
}
```

### Agent注册机制
系统会自动发现所有标注了`@Agent`注解的Spring Bean：

```java
@Agent
@Component
public class ProductAssistant implements Function<Request, String> {
    // Agent实现
}
```

## 🎯 Function Calling集成

### Spring AI集成
```java
// 将Agent Bean名称传递给ChatClient
requestSpec = requestSpec.functions(agentBeanNames);

// Spring AI会自动：
// 1. 根据用户输入判断是否需要调用Function
// 2. 选择合适的Function进行调用
// 3. 将Function结果整合到回复中
```

### Agent系统提示词
```java
private String getAgentSystemPrompt(String sessionName) {
    String basePrompt = getSystemPrompt(sessionName);
    return basePrompt + "\n\n" + """
        你现在拥有强大的Agent能力，可以调用各种专业工具来帮助用户：
        
        🔍 商品搜索：根据关键词、价格、分类等条件搜索商品
        📦 商品详情：获取指定商品的详细信息
        ⚖️ 商品比较：比较多个商品的特性和价格
        💡 商品推荐：基于用户偏好推荐相似商品
        🏷️ 分类查询：查看商品分类信息
        
        请根据用户的需求智能选择合适的工具，并提供专业、准确的回复。
        """;
}
```

## 🔄 模式切换

### 前端切换逻辑
```javascript
// AiChatDialog.vue中的模式切换
const sendMessage = async () => {
    if (agentEnabled.value) {
        // Agent模式
        await callAgentAPI(message, aiMessage)
    } else {
        // 普通模式或RAG模式
        await sendAiMessageStream({
            content: message,
            sessionId: currentSessionId.value,
            useRAG: ragEnabled.value
        }, ...)
    }
}
```

### 参数构建
```javascript
// Agent模式参数
{
    content: message,
    params: {
        enableAgent: true,      // 启用Agent
        enableMemory: true,     // 启用记忆
        historyMessageCount: 10 // 历史消息数量
    }
}

// RAG模式参数
{
    content: message,
    sessionId: sessionId,
    useRAG: true               // 启用RAG
}
```

## 🎛️ 配置选项

### 可配置参数
- **enableAgent**: 是否启用Agent功能
- **enableMemory**: 是否启用对话记忆
- **historyMessageCount**: 携带的历史消息数量
- **modelOptions**: AI模型参数（温度、最大token等）

### 默认配置
```java
@Builder.Default
private Boolean enableAgent = false;    // 默认关闭Agent

@Builder.Default
private Boolean enableMemory = true;    // 默认开启记忆

@Builder.Default
private final Integer historyMessageCount = 10; // 默认10条历史消息
```

## 🔍 调试和监控

### 日志记录
```java
log.info("使用Agent模式处理用户请求: {}", request.getContent());
log.debug("可用Agent列表: {}", Arrays.toString(agentBeanNames));
```

### 错误处理
```java
.doOnError(error -> {
    log.error("Agent处理异常", error);
    AiChatResponse errorResponse = AiChatResponse.error(
        session.getId(), "Agent服务异常：" + error.getMessage());
    sink.next(errorResponse);
})
```

## 🎯 总结

Agent控制机制的核心是通过`enableAgent`参数来决定处理路径：

1. **前端设置**：`params.enableAgent = true`
2. **后端路由**：根据参数选择`chatStreamWithAgent`
3. **Agent发现**：自动扫描`@Agent`注解的Bean
4. **Function调用**：Spring AI自动处理Function Calling
5. **流式响应**：实时返回处理结果

这种设计实现了Agent功能的灵活控制和无缝集成！🚀
