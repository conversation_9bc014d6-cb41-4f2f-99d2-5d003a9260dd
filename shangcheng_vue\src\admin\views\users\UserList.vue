<template>
  <div class="user-list-container">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="handleImportUsers">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="primary" @click="refreshUserList">刷新</el-button>
      </div>
    </div>
    
    <!-- 搜索筛选区域 -->
    <div class="search-filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="用户名/邮箱/手机号/ID" 
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="全部角色" clearable>
            <el-option label="普通用户" value="general_user" />
            <el-option label="AI客服" value="ai_customer_service" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.isActive" placeholder="全部状态" clearable>
            <el-option label="正常" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 批量操作区域 -->
    <div class="batch-actions" v-if="selectedUsers.length > 0">
      <el-button type="warning" @click="handleBatchDisable">批量禁用</el-button>
      <el-button type="success" @click="handleBatchEnable">批量启用</el-button>
      <span class="selected-count">已选择 {{ selectedUsers.length }} 项</span>
    </div>
    
    <!-- 用户列表表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="userId" label="ID" width="80" />
      
      <el-table-column label="用户信息" min-width="200">
        <template #default="{ row }">
          <div class="user-info-cell">
            <el-avatar :size="40" :src="row.avatarUrl || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
            <div class="user-info-text">
              <div class="username">{{ row.username }}</div>
              <div class="email" v-if="row.email">{{ row.email }}</div>
              <div class="phone" v-if="row.phoneNumber">{{ row.phoneNumber }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="role" label="角色" width="120">
        <template #default="{ row }">
          <el-tag v-if="row.role === 'general_user'" type="success">普通用户</el-tag>
          <el-tag v-else-if="row.role === 'ai_customer_service'" type="warning">AI客服</el-tag>
          <el-tag v-else type="info">{{ row.role }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="isActive" label="状态" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.isActive" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="creditScore" label="信用分" width="100" />
      
      <el-table-column prop="registrationDate" label="注册时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.registrationDate) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastLoginDate" label="最后登录" width="180">
        <template #default="{ row }">
          {{ row.lastLoginDate ? formatDateTime(row.lastLoginDate) : '从未登录' }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button 
            size="small" 
            @click="viewUserDetail(row.userId)"
          >
            查看
          </el-button>
          
          <el-button 
            v-if="row.isActive" 
            size="small" 
            type="danger" 
            @click="handleDisableUser(row)"
          >
            禁用
          </el-button>
          
          <el-button 
            v-else 
            size="small" 
            type="success" 
            @click="handleEnableUser(row)"
          >
            启用
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页控件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 批量导入对话框 -->
    <ImportDialog
      :visible="importDialogVisible"
      @update:visible="importDialogVisible = $event"
      import-type="user"
      @import-success="handleImportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { getUserList, updateUserStatus, batchUpdateUserStatus } from '@/admin/api/users'
import ImportDialog from '@/admin/components/ImportDialog.vue'

const router = useRouter()
const loading = ref(false)
const userList = ref([])
const selectedUsers = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 导入相关
const importDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: '',
  isActive: ''
})

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: currentPage.value,
      pageSize: pageSize.value
    }
    
    const res = await getUserList(params)
    if (res.code === 200) {
      userList.value = res.data.records
      total.value = res.data.total
    } else {
      ElMessage.error(res.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新用户列表
const refreshUserList = () => {
  fetchUserList()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.role = ''
  searchForm.isActive = ''
  currentPage.value = 1
  fetchUserList()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchUserList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUserList()
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 查看用户详情
const viewUserDetail = (userId) => {
  router.push(`/admin/users/${userId}`)
}

// 禁用用户
const handleDisableUser = (user) => {
  ElMessageBox.confirm(`确定要禁用用户 "${user.username}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await updateUserStatus({
        userId: user.userId,
        isActive: false
      })
      
      if (res.code === 200) {
        ElMessage.success('禁用用户成功')
        fetchUserList()
      } else {
        ElMessage.error(res.message || '禁用用户失败')
      }
    } catch (error) {
      console.error('禁用用户失败:', error)
      ElMessage.error('禁用用户失败')
    }
  }).catch(() => {})
}

// 启用用户
const handleEnableUser = (user) => {
  ElMessageBox.confirm(`确定要启用用户 "${user.username}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(async () => {
    try {
      const res = await updateUserStatus({
        userId: user.userId,
        isActive: true
      })
      
      if (res.code === 200) {
        ElMessage.success('启用用户成功')
        fetchUserList()
      } else {
        ElMessage.error(res.message || '启用用户失败')
      }
    } catch (error) {
      console.error('启用用户失败:', error)
      ElMessage.error('启用用户失败')
    }
  }).catch(() => {})
}

// 批量禁用用户
const handleBatchDisable = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择用户')
    return
  }
  
  ElMessageBox.confirm(`确定要批量禁用选中的 ${selectedUsers.value.length} 个用户吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await batchUpdateUserStatus({
        userIds: selectedUsers.value.map(user => user.userId),
        isActive: false
      })
      
      if (res.code === 200) {
        ElMessage.success(`成功禁用 ${res.data.updatedCount} 个用户`)
        fetchUserList()
      } else {
        ElMessage.error(res.message || '批量禁用用户失败')
      }
    } catch (error) {
      console.error('批量禁用用户失败:', error)
      ElMessage.error('批量禁用用户失败')
    }
  }).catch(() => {})
}

// 批量启用用户
const handleBatchEnable = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择用户')
    return
  }
  
  ElMessageBox.confirm(`确定要批量启用选中的 ${selectedUsers.value.length} 个用户吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(async () => {
    try {
      const res = await batchUpdateUserStatus({
        userIds: selectedUsers.value.map(user => user.userId),
        isActive: true
      })
      
      if (res.code === 200) {
        ElMessage.success(`成功启用 ${res.data.updatedCount} 个用户`)
        fetchUserList()
      } else {
        ElMessage.error(res.message || '批量启用用户失败')
      }
    } catch (error) {
      console.error('批量启用用户失败:', error)
      ElMessage.error('批量启用用户失败')
    }
  }).catch(() => {})
}

// 打开导入对话框
const handleImportUsers = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = (result) => {
  ElMessage.success(`用户导入完成！成功导入 ${result.successCount} 个用户`)
  // 刷新用户列表
  fetchUserList()
}

// 初始化
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
}

.search-filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.batch-actions {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.selected-count {
  margin-left: 15px;
  color: #606266;
}

.user-info-cell {
  display: flex;
  align-items: center;
}

.user-info-text {
  margin-left: 10px;
}

.username {
  font-weight: bold;
}

.email, .phone {
  font-size: 12px;
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    display: flex;
    flex-direction: column;
  }
  
  .el-form-item {
    margin-right: 0;
  }
}
</style> 