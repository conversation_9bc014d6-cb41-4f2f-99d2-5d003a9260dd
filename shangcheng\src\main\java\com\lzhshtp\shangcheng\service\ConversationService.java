package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.ConversationDTO;
import com.lzhshtp.shangcheng.dto.PageResult;

import java.util.List;

public interface ConversationService {

    /**
     * 获取用户的所有会话列表
     * @param userId 当前用户ID
     * @return 会话DTO列表
     */
    List<ConversationDTO> getUserConversations(Long userId);

    /**
     * 获取或创建两个用户之间的会话
     * @param currentUserId 当前用户ID
     * @param otherUserId 对方用户ID
     * @return 会话ID
     */
    Long getOrCreateConversation(Long currentUserId, Long otherUserId);

    /**
     * 更新会话的最后一条消息预览
     * @param conversationId 会话ID
     * @param messagePreview 消息预览
     * @return 是否更新成功
     */
    boolean updateConversationPreview(Long conversationId, String messagePreview);

    /**
     * 增加接收方的未读消息计数
     * @param conversationId 会话ID
     * @param senderId 发送者ID
     * @return 是否更新成功
     */
    boolean incrementUnreadCount(Long conversationId, Long senderId);

    /**
     * 重置用户在会话中的未读消息计数
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 是否重置成功
     */
    boolean resetUnreadCount(Long conversationId, Long userId);

    /**
     * 检查用户是否是会话的参与者
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 是否是会话参与者
     */
    boolean isConversationParticipant(Long conversationId, Long userId);
} 