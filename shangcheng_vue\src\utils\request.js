import axios from 'axios';

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // 修改为相对路径，使用Vite代理
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 根据请求URL和当前用户角色选择合适的token
    let token;
    
    // 判断当前是在管理员页面还是普通用户页面
    const isAdminPage = window.location.pathname.startsWith('/admin');
    
    if (isAdminPage) {
      // 如果是管理员页面，使用adminToken
      token = localStorage.getItem('adminToken');
      console.log('请求拦截器: 使用管理员token', config.url);
    } else {
      // 如果是普通用户页面，使用普通token
      token = localStorage.getItem('token');
      console.log('请求拦截器: 使用普通用户token', config.url, token);
    }
    
    if (token) {
      // 设置请求头Authorization字段
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    
    // 添加时间戳参数，避免缓存问题
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      };
    }
    
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    
    // 直接返回ApiResponse对象，由业务代码处理具体逻辑
    return res;
  },
  error => {
    console.error('响应错误:', error.response ? error.response.data : error.message);
    
    // 401: 未授权
    if (error.response && error.response.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

export default service; 