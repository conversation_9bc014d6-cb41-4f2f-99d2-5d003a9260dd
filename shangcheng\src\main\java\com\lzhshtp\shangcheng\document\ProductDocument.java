package com.lzhshtp.shangcheng.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.DateFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品搜索文档
 * 用于ElasticSearch全文检索
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "products")
public class ProductDocument {

    @Id
    private Long productId;

    /**
     * 商品标题 - 主要搜索字段，权重最高
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;

    /**
     * 商品描述 - 次要搜索字段
     */
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String description;

    /**
     * 商品价格 - 用于范围查询和排序
     */
    @Field(type = FieldType.Double)
    private BigDecimal price;

    /**
     * 商品状态 - 用于过滤
     */
    @Field(type = FieldType.Integer)
    private Integer status;

    /**
     * 商品新旧程度 - 用于过滤
     */
    @Field(type = FieldType.Keyword)
    private String condition;

    /**
     * 商品位置 - 用于地理位置搜索
     */
    @Field(type = FieldType.Keyword)
    private String location;

    /**
     * 分类ID - 用于分类过滤
     */
    @Field(type = FieldType.Integer)
    private Integer categoryId;

    /**
     * 分类名称 - 用于搜索和显示
     */
    @Field(type = FieldType.Keyword)
    private String categoryName;

    /**
     * 卖家ID - 用于过滤
     */
    @Field(type = FieldType.Long)
    private Long sellerId;

    /**
     * 卖家用户名 - 用于搜索
     */
    @Field(type = FieldType.Keyword)
    private String sellerUsername;

    /**
     * 卖家头像 - 用于显示
     */
    @Field(type = FieldType.Text)
    private String sellerAvatar;

    /**
     * 配送方式 - 用于搜索和显示
     */
    @Field(type = FieldType.Keyword)
    private String deliveryMethod;

    /**
     * 发布时间 - 用于排序
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime postedDate;

    /**
     * 浏览次数 - 用于热度排序
     */
    @Field(type = FieldType.Integer)
    private Integer viewCount;

    /**
     * 收藏次数 - 用于热度排序
     */
    @Field(type = FieldType.Integer)
    private Integer favoriteCount;

    /**
     * 商品图片URL - 用于显示
     */
    @Field(type = FieldType.Keyword, index = false)
    private String imageUrl;

    /**
     * 搜索权重分数 - 综合评分
     */
    @Field(type = FieldType.Double)
    private Double searchScore;

    /**
     * 商品标签 - 用于标签搜索
     */
    @Field(type = FieldType.Keyword)
    private String[] tags;

    /**
     * 创建时间 - 用于索引管理
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime createdAt;

    /**
     * 更新时间 - 用于增量同步
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime updatedAt;
}
