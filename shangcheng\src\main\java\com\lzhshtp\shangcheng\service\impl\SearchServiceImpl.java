package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.document.ForumPostDocument;
import com.lzhshtp.shangcheng.document.ProductDocument;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.ProductQueryParams;
import com.lzhshtp.shangcheng.dto.ForumPostQueryRequest;
import com.lzhshtp.shangcheng.dto.ProductDTO;
import com.lzhshtp.shangcheng.dto.ForumPostDTO;

import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.ForumPostMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.mapper.CategoryMapper;
import com.lzhshtp.shangcheng.model.ForumPost;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.model.Category;
import com.lzhshtp.shangcheng.repository.search.ProductSearchRepository;
import com.lzhshtp.shangcheng.repository.search.ForumPostSearchRepository;
import com.lzhshtp.shangcheng.service.SearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

/**
 * 搜索服务实现类
 */
@Slf4j
@Service  // 重新启用，但提供空实现以避免ElasticSearch依赖
public class SearchServiceImpl implements SearchService {

     @Autowired
    private ProductSearchRepository productSearchRepository; // 临时注释掉

     @Autowired
     private ForumPostSearchRepository forumPostSearchRepository; // 临时注释掉

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ForumPostMapper forumPostMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    // ==================== 商品搜索 ====================

    @Override
    public PageResult<ProductDTO> searchProducts(ProductQueryParams params) {
        try {
            // 检查是否有高级筛选条件（新旧程度、交易方式、地区筛选）
            boolean hasAdvancedFilters = params.getCondition() != null ||
                                       params.getDeliveryMethod() != null ||
                                       params.getLocation() != null;

            if (hasAdvancedFilters) {
                // 如果有高级筛选条件，直接使用数据库查询，因为ES可能不支持这些字段
                log.info("检测到高级筛选条件，使用数据库查询: condition={}, deliveryMethod={}, location={}",
                        params.getCondition(), params.getDeliveryMethod(), params.getLocation());
                return fallbackToDbProductSearch(params);
            }

            // 构建分页参数
            int page = params.getPage() != null ? params.getPage() - 1 : 0; // ES从0开始
            int size = params.getPageSize() != null ? params.getPageSize() : 20;

            // 构建排序
            Sort sort = buildProductSort(params.getSortBy(), params.getSortDirection());
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<ProductDocument> esPage;

            // 根据搜索条件选择不同的查询方法
            if (StringUtils.hasText(params.getKeyword())) {
                // 关键词搜索
                if (params.getCategoryId() != null && params.getMinPrice() != null && params.getMaxPrice() != null) {
                    // 复合搜索：关键词 + 分类 + 价格范围
                    esPage = productSearchRepository.findByKeywordAndCategoryAndPriceRange(
                        params.getKeyword(), params.getCategoryId(),
                        params.getMinPrice(), params.getMaxPrice(), pageable);
                } else {
                    // 简单关键词搜索
                    esPage = productSearchRepository.findByTitleOrDescriptionContaining(
                        params.getKeyword(), pageable);
                }
            } else if (params.getCategoryId() != null) {
                // 分类搜索
                esPage = productSearchRepository.findByCategoryIdAndStatus(
                    params.getCategoryId(), 0, pageable);
            } else if (params.getMinPrice() != null && params.getMaxPrice() != null) {
                // 价格范围搜索
                esPage = productSearchRepository.findByPriceBetweenAndStatus(
                    params.getMinPrice(), params.getMaxPrice(), 0, pageable);
            } else if (params.getLocation() != null) {
                // 位置搜索
                esPage = productSearchRepository.findByLocationAndStatus(
                    params.getLocation(), 0, pageable);
            } else {
                // 默认搜索（最新商品）
                esPage = productSearchRepository.findByStatusOrderByPostedDateDesc(0, pageable);
            }

            // 转换Document为DTO
            List<ProductDTO> productDTOs = esPage.getContent().stream()
                .map(this::convertProductDocumentToDTO)
                .collect(Collectors.toList());

            // 转换为PageResult
            return new PageResult<>(
                productDTOs,
                (int) esPage.getTotalElements(),
                page + 1, // 转回1开始
                size
            );

        } catch (Exception e) {
            log.error("ElasticSearch商品搜索失败，降级到数据库查询: {}", e.getMessage());
            // 降级到原有的数据库查询
            return fallbackToDbProductSearch(params);
        }
    }

    @Override
    public List<String> getProductSearchSuggestions(String prefix) {
        try {
            if (!StringUtils.hasText(prefix) || prefix.length() < 2) {
                return List.of();
            }

            List<ProductDocument> suggestions = productSearchRepository.findSuggestionsByTitlePrefix(prefix);
            return suggestions.stream()
                .map(ProductDocument::getTitle)
                .distinct()
                .limit(10)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取商品搜索建议失败: {}", e.getMessage());
            return List.of();
        }
    }

    @Override
    public List<ProductDTO> getSimilarProducts(Long productId) {
        try {
            List<ProductDocument> documents = productSearchRepository.findSimilarProducts(productId);
            return documents.stream()
                .map(this::convertProductDocumentToDTO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取相似商品失败: {}", e.getMessage());
            return List.of();
        }
    }

    @Override
    public void syncProductToES(Long productId) {
        try {
            if (productId == null) {
                // 同步所有商品
                log.info("开始同步所有商品到ElasticSearch");
                syncAllProducts();
            } else {
                // 同步单个商品
                Product product = productMapper.selectProductDetail(productId);
                if (product != null) {
                    ProductDocument doc = convertToProductDocument(product);
                    productSearchRepository.save(doc);
                    log.info("商品同步到ES成功: {}", productId);
                }
            }
        } catch (Exception e) {
            log.error("同步商品到ES失败: {}", e.getMessage());
        }
    }

    @Override
    public void deleteProductFromES(Long productId) {
        try {
            productSearchRepository.deleteById(productId);
            log.info("从ES删除商品成功: {}", productId);
        } catch (Exception e) {
            log.error("从ES删除商品失败: {}", e.getMessage());
        }
    }

    // ==================== 论坛帖子搜索 ====================

    @Override
    public PageResult<ForumPostDTO> searchForumPosts(ForumPostQueryRequest queryRequest) {
        try {
            // 构建分页参数
            int page = queryRequest.getPageNum() - 1; // ES从0开始
            int size = queryRequest.getPageSize();

            // 构建排序
            Sort sort = buildForumPostSort(queryRequest.getOrderBy(), queryRequest.getOrderDirection());
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<ForumPostDocument> esPage;

            // 根据搜索条件选择不同的查询方法
            if (StringUtils.hasText(queryRequest.getKeyword())) {
                // 关键词搜索
                if (queryRequest.getCategoryId() != null) {
                    // 关键词 + 分类搜索
                    esPage = forumPostSearchRepository.findByKeywordAndCategory(
                        queryRequest.getKeyword(), queryRequest.getCategoryId(), pageable);
                } else {
                    // 简单关键词搜索
                    esPage = forumPostSearchRepository.findByTitleOrContentContaining(
                        queryRequest.getKeyword(), pageable);
                }
            } else if (queryRequest.getCategoryId() != null) {
                // 分类搜索
                esPage = forumPostSearchRepository.findByCategoryIdAndStatus(
                    queryRequest.getCategoryId(), 0, pageable);
            } else if (queryRequest.getAuthorId() != null) {
                // 作者搜索
                esPage = forumPostSearchRepository.findByAuthorIdAndStatus(
                    queryRequest.getAuthorId(), 0, pageable);
            } else if (Boolean.TRUE.equals(queryRequest.getOnlyPinned())) {
                // 只看置顶
                esPage = forumPostSearchRepository.findByIsPinnedTrueAndStatusOrderByPostedAtDesc(0, pageable);
            } else {
                // 默认搜索（最新帖子）
                esPage = forumPostSearchRepository.findByStatusOrderByPostedAtDesc(0, pageable);
            }

            // 转换Document为DTO
            List<ForumPostDTO> forumPostDTOs = esPage.getContent().stream()
                .map(this::convertForumPostDocumentToDTO)
                .collect(Collectors.toList());

            // 转换为PageResult
            return new PageResult<>(
                forumPostDTOs,
                (int) esPage.getTotalElements(),
                page + 1, // 转回1开始
                size
            );

        } catch (Exception e) {
            log.error("ElasticSearch帖子搜索失败，降级到数据库查询: {}", e.getMessage());
            // 降级到原有的数据库查询
            return fallbackToDbForumPostSearch(queryRequest);
        }
    }

    @Override
    public List<String> getForumPostSearchSuggestions(String prefix) {
        try {
            if (!StringUtils.hasText(prefix) || prefix.length() < 2) {
                return List.of();
            }

            List<ForumPostDocument> suggestions = forumPostSearchRepository.findSuggestionsByTitlePrefix(prefix);
            return suggestions.stream()
                .map(ForumPostDocument::getTitle)
                .distinct()
                .limit(10)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取帖子搜索建议失败: {}", e.getMessage());
            return List.of();
        }
    }

    @Override
    public List<ForumPostDTO> getSimilarForumPosts(Long postId) {
        try {
            List<ForumPostDocument> documents = forumPostSearchRepository.findSimilarPosts(postId);
            return documents.stream()
                .map(this::convertForumPostDocumentToDTO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取相似帖子失败: {}", e.getMessage());
            return List.of();
        }
    }

    @Override
    public void syncForumPostToES(Long postId) {
        try {
            if (postId == null) {
                // 同步所有帖子
                log.info("开始同步所有帖子到ElasticSearch");
                syncAllForumPosts();
            } else {
                // 同步单个帖子
                ForumPost post = forumPostMapper.selectById(postId);
                if (post != null) {
                    ForumPostDocument doc = convertToForumPostDocument(post);
                    forumPostSearchRepository.save(doc);
                    log.info("帖子同步到ES成功: {}", postId);
                }
            }
        } catch (Exception e) {
            log.error("同步帖子到ES失败: {}", e.getMessage());
        }
    }

    @Override
    public void deleteForumPostFromES(Long postId) {
        try {
            forumPostSearchRepository.deleteById(postId);
            log.info("从ES删除帖子成功: {}", postId);
        } catch (Exception e) {
            log.error("从ES删除帖子失败: {}", e.getMessage());
        }
    }

    // ==================== 搜索分析 ====================

    @Override
    public void recordSearchBehavior(String keyword, Long userId, String searchType) {
        // TODO: 实现搜索行为记录，可以存储到Redis或数据库
        log.info("记录搜索行为 - 用户: {}, 关键词: {}, 类型: {}", userId, keyword, searchType);
    }

    @Override
    public List<String> getHotSearchKeywords(int limit) {
        // TODO: 实现热门搜索词统计
        return List.of("iPhone", "MacBook", "小米", "华为", "二手手机");
    }

    @Override
    public void rebuildAllIndexes() {
        log.info("开始重建所有索引");
        try {
            // 清空现有索引
            productSearchRepository.deleteAll();
            forumPostSearchRepository.deleteAll();

            // 重新同步数据
            syncAllProducts();
            syncAllForumPosts();

            log.info("重建索引完成");
        } catch (Exception e) {
            log.error("重建索引失败: {}", e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建商品排序
     */
    private Sort buildProductSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortDirection) ?
            Sort.Direction.DESC : Sort.Direction.ASC;

        if ("price".equals(sortBy)) {
            return Sort.by(direction, "price");
        } else if ("date".equals(sortBy)) {
            return Sort.by(direction, "postedDate");
        } else if ("views".equals(sortBy)) {
            return Sort.by(direction, "viewCount");
        } else {
            // 默认按发布时间降序
            return Sort.by(Sort.Direction.DESC, "postedDate");
        }
    }

    /**
     * 构建论坛帖子排序
     */
    private Sort buildForumPostSort(String orderBy, String orderDirection) {
        Sort.Direction direction = "desc".equalsIgnoreCase(orderDirection) ?
            Sort.Direction.DESC : Sort.Direction.ASC;

        if ("views_count".equals(orderBy)) {
            return Sort.by(direction, "viewsCount");
        } else if ("posted_at".equals(orderBy)) {
            return Sort.by(direction, "postedAt");
        } else {
            // 默认按发布时间降序
            return Sort.by(Sort.Direction.DESC, "postedAt");
        }
    }

    /**
     * 降级到数据库商品搜索
     */
    private PageResult<ProductDTO> fallbackToDbProductSearch(ProductQueryParams params) {
        log.warn("使用数据库查询作为降级方案");
        try {
            // 使用ProductMapper的查询方法，支持所有筛选条件
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<Product> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(params.getPage(), params.getPageSize());

            com.baomidou.mybatisplus.core.metadata.IPage<Product> productPage =
                productMapper.selectProductsByCondition(page, params);

            // 转换为DTO
            List<ProductDTO> productDTOs = productPage.getRecords().stream()
                .map(this::convertProductToDTO)
                .collect(Collectors.toList());

            return new PageResult<>(
                productDTOs,
                (int) productPage.getTotal(),
                params.getPage(),
                params.getPageSize()
            );
        } catch (Exception e) {
            log.error("数据库查询也失败了: {}", e.getMessage());
            return new PageResult<>(List.of(), 0, 1, 20);
        }
    }

    /**
     * 降级到数据库帖子搜索
     */
    private PageResult<ForumPostDTO> fallbackToDbForumPostSearch(ForumPostQueryRequest queryRequest) {
        log.warn("使用数据库查询作为降级方案");
        return new PageResult<>(List.of(), 0, 1, 10);
    }

    // ==================== Document到DTO转换方法 ====================

    /**
     * 转换ProductDocument为ProductDTO
     */
    private ProductDTO convertProductDocumentToDTO(ProductDocument document) {
        ProductDTO dto = new ProductDTO();
        dto.setId(document.getProductId());
        dto.setTitle(document.getTitle());
        dto.setDescription(document.getDescription());
        dto.setPrice(document.getPrice());
        dto.setCategoryId(document.getCategoryId());
        dto.setCategoryName(document.getCategoryName());
        dto.setCondition(document.getCondition());
        dto.setLocation(document.getLocation());
        dto.setDeliveryMethod(document.getDeliveryMethod()); // 现在ES中有完整数据
        dto.setImageUrls(document.getImageUrl() != null ?
            java.util.Arrays.asList(document.getImageUrl()) :
            java.util.Collections.emptyList());
        dto.setPostedDate(document.getPostedDate());
        dto.setStatus(document.getStatus() == 0 ? "available" : "unavailable");
        dto.setSellerId(document.getSellerId());
        dto.setSellerName(document.getSellerUsername()); // 现在ES中有完整数据
        dto.setSellerAvatar(document.getSellerAvatar()); // 现在ES中有完整数据
        return dto;
    }

    /**
     * 转换ForumPostDocument为ForumPostDTO
     */
    private ForumPostDTO convertForumPostDocumentToDTO(ForumPostDocument document) {
        ForumPostDTO dto = new ForumPostDTO();
        dto.setPostId(document.getPostId());
        dto.setTitle(document.getTitle());
        dto.setContent(document.getContent());
        dto.setAuthorId(document.getAuthorId());
        dto.setAuthorName(document.getAuthorUsername());
        dto.setAuthorAvatar(null); // ES中没有存储头像信息
        dto.setForumCategoryId(document.getCategoryId());
        dto.setCategoryName(document.getCategoryName());
        dto.setPostedAt(document.getPostedAt());
        dto.setViewsCount(document.getViewsCount());
        dto.setIsPinned(document.getIsPinned() ? 1 : 0);
        dto.setStatus(document.getStatus());
        return dto;
    }

    /**
     * 转换Product实体为ProductDTO
     */
    private ProductDTO convertProductToDTO(Product product) {
        ProductDTO dto = new ProductDTO();
        dto.setId(product.getId());
        dto.setTitle(product.getTitle());
        dto.setDescription(product.getDescription());
        dto.setPrice(product.getPrice());
        dto.setCategoryId(product.getCategoryId());
        dto.setCondition(product.getCondition());
        dto.setLocation(product.getLocation());
        dto.setDeliveryMethod(product.getDeliveryMethod());
        dto.setImageUrls(Collections.singletonList(product.getImageUrls()));
        dto.setPostedDate(product.getPostedDate());
        dto.setStatus(product.getStatus());
        dto.setSellerId(product.getSellerId());
        dto.setSupportOfficialVerification(product.getSupportOfficialVerification());
        dto.setVerificationFee(product.getVerificationFee());

        // 获取分类名称
        if (product.getCategoryId() != null) {
            try {
                Category category = categoryMapper.selectById(product.getCategoryId());
                dto.setCategoryName(category != null ? category.getName() : "未知分类");
            } catch (Exception e) {
                dto.setCategoryName("未知分类");
            }
        }

        // 获取卖家信息
        if (product.getSellerId() != null) {
            try {
                User seller = userMapper.selectById(product.getSellerId());
                if (seller != null) {
                    dto.setSellerName(seller.getUsername());
                    dto.setSellerAvatar(seller.getAvatarUrl());
                }
            } catch (Exception e) {
                dto.setSellerName("未知用户");
            }
        }

        return dto;
    }

    /**
     * 从数据库分页查询商品
     */
    private List<Product> getProductsFromDatabase(int offset, int pageSize) {
        try {
            // 使用简单的selectList方法，避免复杂的分页查询
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Product> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            wrapper.last("LIMIT " + offset + ", " + pageSize); // 直接使用SQL LIMIT

            List<Product> products = ((com.baomidou.mybatisplus.core.mapper.BaseMapper<Product>) productMapper).selectList(wrapper);

            return products;
        } catch (Exception e) {
            log.error("从数据库查询商品失败: {}", e.getMessage(), e);
            // 如果还是失败，返回空列表，不影响其他功能
            return List.of();
        }
    }

    /**
     * 从数据库分页查询论坛帖子
     */
    private List<ForumPost> getForumPostsFromDatabase(int offset, int pageSize) {
        try {
            // 使用简单的selectList方法
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ForumPost> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            wrapper.eq("lzhshtp_status", 0); // 只同步已发布帖子
            wrapper.last("LIMIT " + offset + ", " + pageSize); // 直接使用SQL LIMIT

            List<ForumPost> posts = ((com.baomidou.mybatisplus.core.mapper.BaseMapper<ForumPost>) forumPostMapper).selectList(wrapper);

            return posts;
        } catch (Exception e) {
            log.error("从数据库查询论坛帖子失败: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * 转换Product为ProductDocument
     */
    private ProductDocument convertToProductDocument(Product product) {
        try {
            // 查询分类名称
            String categoryName = null;
            if (product.getCategoryId() != null) {
                categoryName = getCategoryNameById(product.getCategoryId());
            }

            // 查询卖家信息
            String sellerUsername = null;
            String sellerAvatar = null;
            if (product.getSellerId() != null) {
                UserInfo userInfo = getUserInfoById(product.getSellerId());
                sellerUsername = userInfo.username;
                sellerAvatar = userInfo.avatar;
            }

            return ProductDocument.builder()
                .productId(product.getId())
                .title(product.getTitle())
                .description(product.getDescription())
                .price(product.getPrice())
                .status(product.getStatus() != null ? (product.getStatus().equals("available") ? 0 : 1) : 0)
                .condition(product.getCondition())
                .location(product.getLocation())
                .categoryId(product.getCategoryId())
                .categoryName(categoryName) // 添加分类名称
                .sellerId(product.getSellerId())
                .sellerUsername(sellerUsername) // 添加卖家用户名
                .sellerAvatar(sellerAvatar) // 添加卖家头像
                .deliveryMethod(product.getDeliveryMethod()) // 添加配送方式
                .postedDate(product.getPostedDate())
                .viewCount(0)
                .favoriteCount(0)
                .imageUrl(product.getImageUrls())
                .searchScore(1.0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        } catch (Exception e) {
            log.error("转换Product为ProductDocument失败: {}", e.getMessage());
            // 返回基础版本，不包含关联数据
            return ProductDocument.builder()
                .productId(product.getId())
                .title(product.getTitle())
                .description(product.getDescription())
                .price(product.getPrice())
                .status(product.getStatus() != null ? (product.getStatus().equals("available") ? 0 : 1) : 0)
                .condition(product.getCondition())
                .location(product.getLocation())
                .categoryId(product.getCategoryId())
                .sellerId(product.getSellerId())
                .deliveryMethod(product.getDeliveryMethod())
                .postedDate(product.getPostedDate())
                .viewCount(0)
                .favoriteCount(0)
                .imageUrl(product.getImageUrls())
                .searchScore(1.0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        }
    }

    /**
     * 转换ForumPost为ForumPostDocument
     */
    private ForumPostDocument convertToForumPostDocument(ForumPost post) {
        return ForumPostDocument.builder()
            .postId(post.getPostId())
            .title(post.getTitle())
            .content(post.getContent())
            .authorId(post.getAuthorId())
            .categoryId(post.getForumCategoryId())
            .postedAt(post.getPostedAt())
            .viewsCount(post.getViewsCount())
            .isPinned(post.getIsPinned() != null && post.getIsPinned() == 1)
            .status(post.getStatus())
            .commentCount(0)
            .likeCount(0)
            .searchScore(1.0)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }

    /**
     * 同步所有商品到ES
     */
    private void syncAllProducts() {
        try {
            log.info("开始同步所有商品到ElasticSearch");

            // 分页查询所有商品
            int pageSize = 100;
            int currentPage = 1;
            int totalSynced = 0;

            while (true) {
                // 构建分页参数 - 使用简单的LIMIT OFFSET查询
                int offset = (currentPage - 1) * pageSize;

                // 直接使用SQL查询商品
                List<Product> products = getProductsFromDatabase(offset, pageSize);

                if (products.isEmpty()) {
                    break; // 没有更多数据
                }

                // 批量转换并保存到ES
                List<ProductDocument> documents = products.stream()
                    .map(this::convertToProductDocument)
                    .collect(Collectors.toList());

                productSearchRepository.saveAll(documents);

                totalSynced += products.size();
                log.info("已同步 {} 个商品到ES，当前页: {}", totalSynced, currentPage);

                if (products.size() < pageSize) {
                    break; // 最后一页
                }

                currentPage++;
            }

            log.info("商品同步完成，总计同步 {} 个商品", totalSynced);

        } catch (Exception e) {
            log.error("同步所有商品到ES失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 同步所有帖子到ES
     */
    private void syncAllForumPosts() {
        try {
            log.info("开始同步所有论坛帖子到ElasticSearch");

            // 分页查询所有帖子
            int pageSize = 100;
            int currentPage = 1;
            int totalSynced = 0;

            while (true) {
                // 构建分页参数
                int offset = (currentPage - 1) * pageSize;

                // 直接使用SQL查询帖子
                List<ForumPost> posts = getForumPostsFromDatabase(offset, pageSize);

                if (posts.isEmpty()) {
                    break; // 没有更多数据
                }

                // 批量转换并保存到ES
                List<ForumPostDocument> documents = posts.stream()
                    .map(this::convertToForumPostDocument)
                    .collect(Collectors.toList());

                forumPostSearchRepository.saveAll(documents);

                totalSynced += posts.size();
                log.info("已同步 {} 个帖子到ES，当前页: {}", totalSynced, currentPage);

                if (posts.size() < pageSize) {
                    break; // 最后一页
                }

                currentPage++;
            }

            log.info("论坛帖子同步完成，总计同步 {} 个帖子", totalSynced);

        } catch (Exception e) {
            log.error("同步所有帖子到ES失败: {}", e.getMessage(), e);
        }
    }

    // ==================== 辅助查询方法 ====================

    /**
     * 查询分类名称
     */
    private String getCategoryNameById(Integer categoryId) {
        if (categoryId == null) {
            return null;
        }
        try {
            // 使用真实的数据库查询
            com.lzhshtp.shangcheng.model.Category category = categoryMapper.selectById(categoryId);
            if (category != null) {
                return category.getName();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.warn("查询分类名称失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查询用户信息
     */
    private UserInfo getUserInfoById(Long userId) {
        if (userId == null) {
            return new UserInfo(null, null);
        }
        try {
            // 使用真实的数据库查询
            com.lzhshtp.shangcheng.model.User user = userMapper.selectById(userId);
            if (user != null) {
                return new UserInfo(user.getUsername(), user.getAvatarUrl());
            } else {
                return new UserInfo(null, null);
            }
        } catch (Exception e) {
            log.warn("查询用户信息失败: {}", e.getMessage());
            return new UserInfo(null, null);
        }
    }

    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        final String username;
        final String avatar;

        UserInfo(String username, String avatar) {
            this.username = username;
            this.avatar = avatar;
        }
    }
}
