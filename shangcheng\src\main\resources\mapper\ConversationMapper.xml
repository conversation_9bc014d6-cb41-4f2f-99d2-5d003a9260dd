<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ConversationMapper">

    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.Conversation">
        <id property="lzhshtp_conversation_id" column="lzhshtp_conversation_id"/>
        <result property="lzhshtp_user1_id" column="lzhshtp_user1_id"/>
        <result property="lzhshtp_user2_id" column="lzhshtp_user2_id"/>
        <result property="lzhshtp_created_at" column="lzhshtp_created_at"/>
        <result property="lzhshtp_updated_at" column="lzhshtp_updated_at"/>
        <result property="lzhshtp_last_message_preview" column="lzhshtp_last_message_preview"/>
        <result property="lzhshtp_unread_count_user1" column="lzhshtp_unread_count_user1"/>
        <result property="lzhshtp_unread_count_user2" column="lzhshtp_unread_count_user2"/>
    </resultMap>

    <insert id="createConversation" parameterType="com.lzhshtp.shangcheng.model.Conversation" useGeneratedKeys="true" keyProperty="lzhshtp_conversation_id">
        INSERT INTO tb_lzhshtp_conversations (lzhshtp_user1_id, lzhshtp_user2_id, lzhshtp_last_message_preview)
        VALUES (#{lzhshtp_user1_id}, #{lzhshtp_user2_id}, #{lzhshtp_last_message_preview})
    </insert>

    <select id="findConversationBetweenUsers" resultMap="BaseResultMap">
        SELECT * FROM tb_lzhshtp_conversations
        WHERE (lzhshtp_user1_id = #{userId1} AND lzhshtp_user2_id = #{userId2})
           OR (lzhshtp_user1_id = #{userId2} AND lzhshtp_user2_id = #{userId1})
    </select>

    <select id="findConversationsByUserId" resultMap="BaseResultMap">
        SELECT * FROM tb_lzhshtp_conversations
        WHERE lzhshtp_user1_id = #{userId} OR lzhshtp_user2_id = #{userId}
        ORDER BY lzhshtp_updated_at DESC
    </select>

    <select id="findConversationById" resultMap="BaseResultMap">
        SELECT * FROM tb_lzhshtp_conversations WHERE lzhshtp_conversation_id = #{conversationId}
    </select>

    <update id="updateConversationPreview">
        UPDATE tb_lzhshtp_conversations SET
        lzhshtp_last_message_preview = #{preview},
        lzhshtp_updated_at = NOW()
        WHERE lzhshtp_conversation_id = #{conversationId}
    </update>

    <update id="incrementUnreadCountUser1">
        UPDATE tb_lzhshtp_conversations SET
        lzhshtp_unread_count_user1 = lzhshtp_unread_count_user1 + 1
        WHERE lzhshtp_conversation_id = #{conversationId}
    </update>

    <update id="incrementUnreadCountUser2">
        UPDATE tb_lzhshtp_conversations SET
        lzhshtp_unread_count_user2 = lzhshtp_unread_count_user2 + 1
        WHERE lzhshtp_conversation_id = #{conversationId}
    </update>

    <update id="resetUnreadCountUser1">
        UPDATE tb_lzhshtp_conversations SET
        lzhshtp_unread_count_user1 = 0
        WHERE lzhshtp_conversation_id = #{conversationId}
    </update>

    <update id="resetUnreadCountUser2">
        UPDATE tb_lzhshtp_conversations SET
        lzhshtp_unread_count_user2 = 0
        WHERE lzhshtp_conversation_id = #{conversationId}
    </update>

</mapper> 