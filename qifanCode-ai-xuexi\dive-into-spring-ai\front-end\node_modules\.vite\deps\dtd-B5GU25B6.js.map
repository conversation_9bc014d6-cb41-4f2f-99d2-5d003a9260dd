{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/dtd.js"], "sourcesContent": ["var type;\nfunction ret(style, tp) {type = tp; return style;}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == \"<\" && stream.eat(\"!\") ) {\n    if (stream.eatWhile(/[\\-]/)) {\n      state.tokenize = tokenSGMLComment;\n      return tokenSGMLComment(stream, state);\n    } else if (stream.eatWhile(/[\\w]/)) return ret(\"keyword\", \"doindent\");\n  } else if (ch == \"<\" && stream.eat(\"?\")) { //xml declaration\n    state.tokenize = inBlock(\"meta\", \"?>\");\n    return ret(\"meta\", ch);\n  } else if (ch == \"#\" && stream.eatWhile(/[\\w]/)) return ret(\"atom\", \"tag\");\n  else if (ch == \"|\") return ret(\"keyword\", \"separator\");\n  else if (ch.match(/[\\(\\)\\[\\]\\-\\.,\\+\\?>]/)) return ret(null, ch);//if(ch === \">\") return ret(null, \"endtag\"); else\n  else if (ch.match(/[\\[\\]]/)) return ret(\"rule\", ch);\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  } else if (stream.eatWhile(/[a-zA-Z\\?\\+\\d]/)) {\n    var sc = stream.current();\n    if( sc.substr(sc.length-1,sc.length).match(/\\?|\\+/) !== null )stream.backUp(1);\n    return ret(\"tag\", \"tag\");\n  } else if (ch == \"%\" || ch == \"*\" ) return ret(\"number\", \"number\");\n  else {\n    stream.eatWhile(/[\\w\\\\\\-_%.{,]/);\n    return ret(null, null);\n  }\n}\n\nfunction tokenSGMLComment(stream, state) {\n  var dashes = 0, ch;\n  while ((ch = stream.next()) != null) {\n    if (dashes >= 2 && ch == \">\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    dashes = (ch == \"-\") ? dashes + 1 : 0;\n  }\n  return ret(\"comment\", \"comment\");\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return ret(\"string\", \"tag\");\n  };\n}\n\nfunction inBlock(style, terminator) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.match(terminator)) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      stream.next();\n    }\n    return style;\n  };\n}\n\nexport const dtd = {\n  name: \"dtd\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            baseIndent: 0,\n            stack: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    var context = state.stack[state.stack.length-1];\n    if (stream.current() == \"[\" || type === \"doindent\" || type == \"[\") state.stack.push(\"rule\");\n    else if (type === \"endtag\") state.stack[state.stack.length-1] = \"endtag\";\n    else if (stream.current() == \"]\" || type == \"]\" || (type == \">\" && context == \"rule\")) state.stack.pop();\n    else if (type == \"[\") state.stack.push(\"[\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var n = state.stack.length;\n\n    if( textAfter.charAt(0) === ']' )n--;\n    else if(textAfter.substr(textAfter.length-1, textAfter.length) === \">\"){\n      if(textAfter.substr(0,1) === \"<\") {}\n      else if( type == \"doindent\" && textAfter.length > 1 ) {}\n      else if( type == \"doindent\")n--;\n      else if( type == \">\" && textAfter.length > 1) {}\n      else if( type == \"tag\" && textAfter !== \">\") {}\n      else if( type == \"tag\" && state.stack[state.stack.length-1] == \"rule\")n--;\n      else if( type == \"tag\")n++;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\" && type === \">\")n--;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\") {}\n      else if( textAfter.substr(0,1) !== \"<\" && textAfter.substr(0,1) === \">\" )n=n-1;\n      else if( textAfter === \">\") {}\n      else n=n-1;\n      //over rule them all\n      if(type == null || type == \"]\")n--;\n    }\n\n    return state.baseIndent + n * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\]>]$/\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI;AACJ,SAAS,IAAI,OAAO,IAAI;AAAC,SAAO;AAAI,SAAO;AAAM;AAEjD,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAI;AACjC,QAAI,OAAO,SAAS,MAAM,GAAG;AAC3B,YAAM,WAAW;AACjB,aAAO,iBAAiB,QAAQ,KAAK;AAAA,IACvC,WAAW,OAAO,SAAS,MAAM;AAAG,aAAO,IAAI,WAAW,UAAU;AAAA,EACtE,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,UAAM,WAAW,QAAQ,QAAQ,IAAI;AACrC,WAAO,IAAI,QAAQ,EAAE;AAAA,EACvB,WAAW,MAAM,OAAO,OAAO,SAAS,MAAM;AAAG,WAAO,IAAI,QAAQ,KAAK;AAAA,WAChE,MAAM;AAAK,WAAO,IAAI,WAAW,WAAW;AAAA,WAC5C,GAAG,MAAM,sBAAsB;AAAG,WAAO,IAAI,MAAM,EAAE;AAAA,WACrD,GAAG,MAAM,QAAQ;AAAG,WAAO,IAAI,QAAQ,EAAE;AAAA,WACzC,MAAM,OAAQ,MAAM,KAAK;AAChC,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC,WAAW,OAAO,SAAS,gBAAgB,GAAG;AAC5C,QAAI,KAAK,OAAO,QAAQ;AACxB,QAAI,GAAG,OAAO,GAAG,SAAO,GAAE,GAAG,MAAM,EAAE,MAAM,OAAO,MAAM;AAAM,aAAO,OAAO,CAAC;AAC7E,WAAO,IAAI,OAAO,KAAK;AAAA,EACzB,WAAW,MAAM,OAAO,MAAM;AAAM,WAAO,IAAI,UAAU,QAAQ;AAAA,OAC5D;AACH,WAAO,SAAS,eAAe;AAC/B,WAAO,IAAI,MAAM,IAAI;AAAA,EACvB;AACF;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,SAAS,GAAG;AAChB,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,UAAU,KAAK,MAAM,KAAK;AAC5B,YAAM,WAAW;AACjB;AAAA,IACF;AACA,aAAU,MAAM,MAAO,SAAS,IAAI;AAAA,EACtC;AACA,SAAO,IAAI,WAAW,SAAS;AACjC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,cAAM,WAAW;AACjB;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,WAAO,IAAI,UAAU,KAAK;AAAA,EAC5B;AACF;AAEA,SAAS,QAAQ,OAAO,YAAY;AAClC,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,cAAM,WAAW;AACjB;AAAA,MACF;AACA,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MAAC,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,OAAO,CAAC;AAAA,IAAC;AAAA,EACnB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAAG,aAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AAExC,QAAI,UAAU,MAAM,MAAM,MAAM,MAAM,SAAO,CAAC;AAC9C,QAAI,OAAO,QAAQ,KAAK,OAAO,SAAS,cAAc,QAAQ;AAAK,YAAM,MAAM,KAAK,MAAM;AAAA,aACjF,SAAS;AAAU,YAAM,MAAM,MAAM,MAAM,SAAO,CAAC,IAAI;AAAA,aACvD,OAAO,QAAQ,KAAK,OAAO,QAAQ,OAAQ,QAAQ,OAAO,WAAW;AAAS,YAAM,MAAM,IAAI;AAAA,aAC9F,QAAQ;AAAK,YAAM,MAAM,KAAK,GAAG;AAC1C,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,IAAI,MAAM,MAAM;AAEpB,QAAI,UAAU,OAAO,CAAC,MAAM;AAAK;AAAA,aACzB,UAAU,OAAO,UAAU,SAAO,GAAG,UAAU,MAAM,MAAM,KAAI;AACrE,UAAG,UAAU,OAAO,GAAE,CAAC,MAAM,KAAK;AAAA,MAAC,WAC1B,QAAQ,cAAc,UAAU,SAAS,GAAI;AAAA,MAAC,WAC9C,QAAQ;AAAW;AAAA,eACnB,QAAQ,OAAO,UAAU,SAAS,GAAG;AAAA,MAAC,WACtC,QAAQ,SAAS,cAAc,KAAK;AAAA,MAAC,WACrC,QAAQ,SAAS,MAAM,MAAM,MAAM,MAAM,SAAO,CAAC,KAAK;AAAO;AAAA,eAC7D,QAAQ;AAAM;AAAA,eACd,cAAc,OAAO,MAAM,MAAM,MAAM,MAAM,SAAO,CAAC,KAAK,UAAU,SAAS;AAAI;AAAA,eACjF,cAAc,OAAO,MAAM,MAAM,MAAM,MAAM,SAAO,CAAC,KAAK,QAAQ;AAAA,MAAC,WACnE,UAAU,OAAO,GAAE,CAAC,MAAM,OAAO,UAAU,OAAO,GAAE,CAAC,MAAM;AAAK,YAAE,IAAE;AAAA,eACpE,cAAc,KAAK;AAAA,MAAC;AACxB,YAAE,IAAE;AAET,UAAG,QAAQ,QAAQ,QAAQ;AAAI;AAAA,IACjC;AAEA,WAAO,MAAM,aAAa,IAAI,GAAG;AAAA,EACnC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,EACjB;AACF;", "names": []}