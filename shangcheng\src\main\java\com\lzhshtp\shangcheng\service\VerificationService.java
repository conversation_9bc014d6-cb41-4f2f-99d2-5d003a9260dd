package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.dto.VerificationDTO;
import com.lzhshtp.shangcheng.dto.VerificationResultDTO;

import java.util.List;

/**
 * 验货服务接口
 */
public interface VerificationService {
    
    /**
     * 创建验货记录
     *
     * @param orderId 订单ID
     * @return 验货记录ID
     */
    Long createVerificationRecord(Long orderId);

    // 注意：验货付费方和费用信息可以从订单中获取，不需要单独传递
    
    /**
     * 更新验货状态
     * 
     * @param verificationId 验货记录ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateVerificationStatus(Long verificationId, String status);
    
    /**
     * 提交验货结果
     * 
     * @param verifierId 验货员ID
     * @param result 验货结果
     * @return 是否提交成功
     */
    boolean submitVerificationResult(Long verifierId, VerificationResultDTO result);
    
    /**
     * 验货通过，转发给买家
     * 
     * @param verificationId 验货记录ID
     * @return 是否转发成功
     */
    boolean forwardToBuyer(Long verificationId);
    
    /**
     * 验货失败，处理退款
     * 
     * @param verificationId 验货记录ID
     * @return 是否处理成功
     */
    boolean handleVerificationFailure(Long verificationId);
    
    /**
     * 查询待验货列表
     * 
     * @param verifierId 验货员ID（可选，为null时查询所有）
     * @return 待验货列表
     */
    List<VerificationDTO> getPendingVerifications(Long verifierId);
    
    /**
     * 根据订单ID查询验货记录
     *
     * @param orderId 订单ID
     * @return 验货记录
     */
    VerificationDTO getVerificationByOrderId(Long orderId);

    /**
     * 更新验货记录状态为已收货（商品送达验货中心）
     *
     * @param orderId 订单ID
     * @return 是否更新成功
     */
    boolean updateVerificationStatusToReceived(Long orderId);

    /**
     * 管理员查询验货记录列表
     *
     * @param page 页码
     * @param pageSize 页面大小
     * @param status 状态筛选
     * @param verificationPayer 付费方筛选
     * @param keyword 关键词搜索
     * @return 验货记录列表
     */
    List<VerificationDTO> getAdminVerificationList(int page, int pageSize, String status, String verificationPayer, String keyword);

    /**
     * 管理员查询验货统计数据
     *
     * @return 统计数据
     */
    Object getAdminVerificationStatistics();
}
