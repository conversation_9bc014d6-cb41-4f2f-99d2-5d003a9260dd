package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 验货DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationDTO {
    
    private Long verificationId;
    private Long orderId;
    private Long verifierId;
    private String verificationStatus;
    private String verificationResult;
    private List<String> verificationImages;
    private LocalDateTime receivedTime;
    private LocalDateTime verifiedTime;
    private LocalDateTime forwardedTime;
    private LocalDateTime createdTime;
    
    // 关联信息
    private String verifierName;
    private String productTitle;
    private String buyerName;
    private String sellerName;
}
