# 山城二手交易平台 - 数据统计系统

## 📊 系统概述

基于ECharts的管理员后台数据统计可视化系统，提供全面的平台运营数据分析和实时监控功能。

## 🎯 功能特性

### 1. 平台概览统计
- **核心指标卡片**：总用户数、总商品数、总订单数、总交易额
- **增长趋势**：今日vs昨日数据对比，增长率计算
- **健康度指标**：订单完成率、用户活跃率、商品可用率
- **7天趋势**：最近7天的关键指标小图表

### 2. 用户统计分析
- **注册趋势图**：用户注册量时间序列分析
- **角色分布饼图**：管理员、普通用户、AI客服分布
- **地域分布图**：用户地理位置分布热力图
- **活跃度分析**：日活、周活、月活用户统计

### 3. 商品统计分析
- **发布趋势图**：商品发布量时间序列
- **分类分布饼图**：各商品分类占比分析
- **状态分布环形图**：可用、已售、待审核等状态分布
- **价格区间分析**：商品价格分布柱状图

### 4. 交易统计分析
- **订单量趋势**：订单数量时间序列分析
- **交易金额趋势**：交易额面积图展示
- **订单状态分布**：各订单状态占比
- **热门商品排行**：按浏览量、收藏量、订单量排行

### 5. 论坛统计分析
- **活跃度趋势**：帖子、评论、浏览量趋势
- **分类热度雷达图**：各论坛分类活跃度对比
- **用户参与度**：发帖用户、评论用户统计

### 6. 实时数据监控
- **实时指标**：今日新增用户、商品、订单、交易额
- **系统状态**：在线用户数、系统负载、内存使用率
- **异常监控**：错误数、警告数、最后错误时间

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3** + **Composition API**
- **ECharts 5.x** - 数据可视化图表库
- **Element Plus** - UI组件库
- **Axios** - HTTP请求库

### 后端技术栈
- **Spring Boot** - 应用框架
- **Spring Security** - 权限控制
- **MyBatis** - 数据访问层
- **Redis** - 缓存支持
- **MySQL** - 数据存储

### 缓存策略
- **概览统计**：缓存5分钟
- **用户统计**：缓存10分钟
- **商品统计**：缓存10分钟
- **实时数据**：无缓存，30秒自动刷新

## 📁 文件结构

```
shangcheng_vue/src/admin/
├── api/
│   └── statistics.js              # 统计API接口
├── views/
│   ├── DashboardHome.vue          # 数据统计主页面
│   └── statistics/
│       ├── UserStatistics.vue     # 用户统计详情页
│       ├── ProductStatistics.vue  # 商品统计详情页
│       ├── OrderStatistics.vue    # 订单统计详情页
│       └── ForumStatistics.vue    # 论坛统计详情页
└── components/
    ├── StatCard.vue               # 统计卡片组件
    ├── TrendChart.vue             # 趋势图表组件
    └── RealtimePanel.vue          # 实时数据面板

shangcheng/src/main/java/com/lzhshtp/shangcheng/
├── controller/
│   └── AdminStatisticsController.java    # 统计控制器
├── service/
│   ├── AdminStatisticsService.java       # 统计服务接口
│   └── impl/
│       └── AdminStatisticsServiceImpl.java # 统计服务实现
├── mapper/
│   └── StatisticsMapper.java             # 统计数据访问层
└── dto/statistics/
    ├── OverviewStatisticsDTO.java         # 概览统计DTO
    ├── UserStatisticsDTO.java             # 用户统计DTO
    ├── ProductStatisticsDTO.java          # 商品统计DTO
    ├── OrderStatisticsDTO.java            # 订单统计DTO
    ├── ForumStatisticsDTO.java            # 论坛统计DTO
    └── RealtimeStatisticsDTO.java         # 实时统计DTO
```

## 🚀 快速开始

### 1. 后端配置

#### 添加依赖
```xml
<!-- Redis缓存支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- 缓存注解支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-cache</artifactId>
</dependency>
```

#### 配置缓存
```yaml
spring:
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

#### 启用缓存
```java
@SpringBootApplication
@EnableCaching
public class ShangchengApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShangchengApplication.class, args);
    }
}
```

### 2. 前端配置

#### 安装依赖
```bash
npm install echarts
```

#### 路由配置
```javascript
// router/index.js
{
  path: '/admin/dashboard',
  component: () => import('@/admin/views/Dashboard.vue'),
  children: [
    {
      path: '',
      name: 'DashboardHome',
      component: () => import('@/admin/views/DashboardHome.vue')
    }
  ]
}
```

## 📈 图表配置示例

### 用户注册趋势图
```javascript
const userTrendOption = {
  title: { text: '用户注册趋势' },
  tooltip: { trigger: 'axis' },
  legend: { data: ['新增用户', '累计用户'] },
  xAxis: { type: 'category', data: dates },
  yAxis: [
    { type: 'value', name: '新增用户' },
    { type: 'value', name: '累计用户' }
  ],
  series: [
    {
      name: '新增用户',
      type: 'line',
      data: newUsers,
      smooth: true
    },
    {
      name: '累计用户',
      type: 'line',
      yAxisIndex: 1,
      data: totalUsers,
      smooth: true
    }
  ]
}
```

### 商品分类分布饼图
```javascript
const categoryOption = {
  title: { text: '商品分类分布' },
  tooltip: { trigger: 'item' },
  series: [{
    name: '商品分类',
    type: 'pie',
    radius: ['40%', '70%'],
    data: categoryData,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}
```

## 🔧 性能优化

### 1. 数据缓存
- 使用Redis缓存统计结果
- 设置合理的缓存过期时间
- 实现缓存预热机制

### 2. 数据库优化
- 为统计查询添加索引
- 使用数据库视图预计算统计数据
- 实现数据分页和限制

### 3. 前端优化
- 图表懒加载和按需渲染
- 使用虚拟滚动处理大数据量
- 实现图表resize自适应

## 🚨 注意事项

1. **权限控制**：所有统计接口都需要管理员权限
2. **数据安全**：敏感统计数据需要脱敏处理
3. **性能监控**：监控统计查询的执行时间
4. **错误处理**：完善的异常处理和用户提示
5. **数据一致性**：确保统计数据的准确性和实时性

## 📞 技术支持

如有问题，请联系开发团队或查看相关文档。
