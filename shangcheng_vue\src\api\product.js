import request from '@/utils/request';

// 获取商品列表（分页）
export function getProducts(params) {
  return request({
    url: '/products',
    method: 'get',
    params
  });
}

// 获取当前用户发布的商品
export function getUserProducts(params) {
  return request({
    url: '/products/user',
    method: 'get',
    params
  });
}

// 搜索商品
export function searchProducts(params) {
  return request({
    url: '/products/search',
    method: 'get',
    params
  });
}

// 获取商品详情
export function getProductById(id) {
  return request({
    url: `/products/${id}`,
    method: 'get'
  });
}

// 创建商品
export function createProduct(data) {
  return request({
    url: '/products',
    method: 'post',
    data
  });
}

// 更新商品
export function updateProduct(id, data) {
  return request({
    url: `/products/${id}`,
    method: 'put',
    data
  });
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}`,
    method: 'delete'
  });
}

// 更新商品状态
export function updateProductStatus(id, status) {
  return request({
    url: `/products/${id}/status`,
    method: 'put',
    params: { status }
  });
}

// 上传商品（带图片）
export function uploadProduct(formData) {
  return request({
    url: '/products/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 获取推荐商品
export function getRecommendedProducts(excludeProductId, limit = 8) {
  return request({
    url: '/products/recommended',
    method: 'get',
    params: {
      excludeProductId,
      limit
    }
  });
} 

// 更新商品图片
export function updateProductImage(id, formData) {
  return request({
    url: `/products/${id}/image`,
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
} 