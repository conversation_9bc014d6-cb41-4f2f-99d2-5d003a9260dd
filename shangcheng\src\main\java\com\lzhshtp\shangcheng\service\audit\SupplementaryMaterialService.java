package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.mapper.SupplementaryMaterialMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.SupplementaryMaterial;
import com.lzhshtp.shangcheng.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 补充材料服务
 */
@Slf4j
@Service
public class SupplementaryMaterialService {

    @Autowired
    private SupplementaryMaterialMapper supplementaryMaterialMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 根据材料请求ID获取补充材料
     */
    public List<SupplementaryMaterial> getMaterialsByRequestId(Long requestId) {
        log.info("获取材料请求的补充材料，请求ID: {}", requestId);
        
        List<SupplementaryMaterial> materials = supplementaryMaterialMapper.findByRequestId(requestId);
        
        // 补充卖家信息并过滤null值
        List<SupplementaryMaterial> validMaterials = new ArrayList<>();
        for (SupplementaryMaterial material : materials) {
            if (material != null) {
                enrichMaterialWithSellerInfo(material);
                validMaterials.add(material);
            } else {
                log.warn("发现null的补充材料对象");
            }
        }

        return validMaterials;
    }

    /**
     * 根据商品ID获取补充材料
     */
    public List<SupplementaryMaterial> getMaterialsByProductId(Long productId) {
        log.info("获取商品的补充材料，商品ID: {}", productId);
        
        List<SupplementaryMaterial> materials = supplementaryMaterialMapper.findByProductId(productId);
        
        // 补充卖家信息并过滤null值
        List<SupplementaryMaterial> validMaterials = new ArrayList<>();
        for (SupplementaryMaterial material : materials) {
            if (material != null) {
                enrichMaterialWithSellerInfo(material);
                validMaterials.add(material);
            } else {
                log.warn("发现null的补充材料对象");
            }
        }

        return validMaterials;
    }

    /**
     * 根据卖家ID获取补充材料
     */
    public List<SupplementaryMaterial> getMaterialsBySellerId(Long sellerId) {
        log.info("获取卖家的补充材料，卖家ID: {}", sellerId);
        
        List<SupplementaryMaterial> materials = supplementaryMaterialMapper.findBySellerId(sellerId);
        
        // 补充卖家信息并过滤null值
        List<SupplementaryMaterial> validMaterials = new ArrayList<>();
        for (SupplementaryMaterial material : materials) {
            if (material != null) {
                enrichMaterialWithSellerInfo(material);
                validMaterials.add(material);
            } else {
                log.warn("发现null的补充材料对象");
            }
        }

        return validMaterials;
    }

    /**
     * 获取未审核的补充材料
     */
    public List<SupplementaryMaterial> getUnreviewedMaterials() {
        log.info("获取未审核的补充材料");
        
        List<SupplementaryMaterial> materials = supplementaryMaterialMapper.findUnreviewedMaterials();
        
        // 补充卖家信息并过滤null值
        List<SupplementaryMaterial> validMaterials = new ArrayList<>();
        for (SupplementaryMaterial material : materials) {
            if (material != null) {
                enrichMaterialWithSellerInfo(material);
                validMaterials.add(material);
            } else {
                log.warn("发现null的补充材料对象");
            }
        }

        return validMaterials;
    }

    /**
     * 创建补充材料
     */
    @Transactional
    public SupplementaryMaterial createSupplementaryMaterial(Long productId, Long requestId, 
                                                           Long sellerId, String materialType,
                                                           String materialUrls, String description) {
        log.info("创建补充材料，商品ID: {}, 请求ID: {}, 卖家ID: {}", productId, requestId, sellerId);

        SupplementaryMaterial material = SupplementaryMaterial.builder()
                .productId(productId)
                .requestId(requestId)
                .sellerId(sellerId)
                .materialType(materialType)
                .materialUrls(materialUrls)
                .description(description)
                .submitTime(LocalDateTime.now())
                .adminReviewed(false)
                .build();

        supplementaryMaterialMapper.insert(material);

        log.info("补充材料创建成功，材料ID: {}", material.getMaterialId());
        return material;
    }

    /**
     * 根据材料ID获取补充材料
     */
    public SupplementaryMaterial getMaterialById(Long materialId) {
        log.info("获取补充材料，材料ID: {}", materialId);

        SupplementaryMaterial material = supplementaryMaterialMapper.selectById(materialId);
        if (material != null) {
            enrichMaterialWithSellerInfo(material);
        }

        return material;
    }

    /**
     * 删除补充材料
     */
    @Transactional
    public void deleteMaterial(Long materialId) {
        log.info("删除补充材料，材料ID: {}", materialId);

        supplementaryMaterialMapper.deleteById(materialId);
    }

    /**
     * 更新管理员审核状态
     */
    @Transactional
    public void updateReviewStatus(Long materialId, Boolean reviewed, String comments) {
        log.info("更新补充材料审核状态，材料ID: {}, 已审核: {}", materialId, reviewed);

        supplementaryMaterialMapper.updateReviewStatus(materialId, reviewed, comments);
    }

    /**
     * 补充卖家信息
     */
    private void enrichMaterialWithSellerInfo(SupplementaryMaterial material) {
        if (material.getSellerId() != null) {
            User seller = userMapper.selectById(material.getSellerId());
            if (seller != null) {
                material.setSellerNickname(seller.getUsername());
            }
        }
    }
}
