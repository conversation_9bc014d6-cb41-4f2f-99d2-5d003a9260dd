import request from '@/utils/request'

/**
 * AI聊天相关API
 */

/**
 * 创建AI会话
 * @param {string} sessionName - 会话名称
 * @returns {Promise}
 */
export function createAiSession(sessionName = '智能客服') {
  return request({
    url: '/ai/sessions',
    method: 'post',
    params: {
      sessionName
    }
  })
}

/**
 * 获取AI会话列表
 * @returns {Promise}
 */
export function getAiSessions() {
  return request({
    url: '/ai/sessions',
    method: 'get'
  })
}

/**
 * 获取会话消息历史
 * @param {string} sessionId - 会话ID
 * @param {number} page - 页码
 * @param {number} size - 每页大小
 * @returns {Promise}
 */
export function getSessionMessages(sessionId, page = 1, size = 20) {
  return request({
    url: `/ai/sessions/${sessionId}/messages`,
    method: 'get',
    params: {
      page,
      size
    }
  })
}

/**
 * 发送AI消息（非流式）
 * @param {Object} data - 消息数据
 * @param {string} data.content - 消息内容
 * @param {string} data.sessionId - 会话ID（可选）
 * @param {Object} data.params - 参数配置
 * @returns {Promise}
 */
export function sendAiMessage(data) {
  return request({
    url: '/ai/chat',
    method: 'post',
    data: {
      content: data.content,
      sessionId: data.sessionId,
      params: {
        enableMemory: true,
        enableRag: data.useRAG || false,
        historyMessageCount: 10,
        ...data.params
      }
    }
  })
}

/**
 * 发送AI消息（流式）- 完全重写，使用最简单的方式
 * @param {Object} data - 消息数据
 * @param {Function} onMessage - 接收消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onComplete - 完成回调
 */
export async function sendAiMessageStream(data, onMessage, onError, onComplete) {
  try {
    const token = localStorage.getItem('token')

    const response = await fetch('/api/ai/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify({
        content: data.content,
        sessionId: data.sessionId,
        params: {
          enableMemory: true,
          enableRag: data.useRAG || false,
          historyMessageCount: 10,
          ...data.params
        }
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = '' // 添加缓冲区来处理分块边界问题

    // 处理单行数据的函数
    function processLine(line) {
      if (line.startsWith('data:')) {
        const jsonStr = line.substring(5).trim() // 移除 "data:" 前缀

        if (jsonStr) {
          try {
            const jsonData = JSON.parse(jsonStr)

            // 处理内容
            if (jsonData.content !== undefined && jsonData.content !== null) {
              onMessage && onMessage(jsonData.content)
            }

            // 检查是否结束
            if (jsonData.isEnd === true) {
              onComplete && onComplete()
              return
            }

            // 检查错误
            if (jsonData.error) {
              console.error('AI服务器错误:', jsonData.error)
              onError && onError(new Error(jsonData.error))
              return
            }

          } catch (parseError) {
            console.warn('AI响应解析失败:', parseError, '原始数据:', line)
          }
        }
      }
    }

    // 处理缓冲区数据的函数
    function processBuffer(bufferData) {
      const lines = bufferData.split('\n')
      for (let line of lines) {
        processLine(line.trim())
      }
    }

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          // 处理缓冲区中剩余的数据
          if (buffer.trim()) {
            processBuffer(buffer)
          }
          onComplete && onComplete()
          break
        }

        // 解码数据块并添加到缓冲区
        const chunk = decoder.decode(value, { stream: true })
        buffer += chunk

        // 处理完整的行
        const lines = buffer.split('\n')

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || ''

        // 处理完整的行
        for (let line of lines) {
          processLine(line.trim())
        }
      }
    } catch (error) {
      console.error('流式读取错误:', error)
      onError && onError(error)
    } finally {
      reader.releaseLock()
    }

  } catch (error) {
    console.error('AI流式请求失败:', error)
    onError && onError(error)
  }
}

/**
 * 删除AI会话
 * @param {string} sessionId - 会话ID
 * @returns {Promise}
 */
export function deleteAiSession(sessionId) {
  return request({
    url: `/ai/sessions/${sessionId}`,
    method: 'delete'
  })
}

/**
 * 清空会话历史
 * @param {string} sessionId - 会话ID
 * @returns {Promise}
 */
export function clearSessionHistory(sessionId) {
  return request({
    url: `/ai/sessions/${sessionId}/messages`,
    method: 'delete'
  })
}
