package com.lzhshtp.shangcheng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 商家信息DTO，包含商家基本信息和统计数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SellerDTO {
    // 基本信息
    private Long userId;
    private String username;
    private String avatarUrl;
    private LocalDateTime registrationDate;
    private Integer creditScore;
    private String bio;
    private String location;
    
    // 统计信息
    private Integer productCount;  // 在售商品数量
    private Integer soldCount;     // 已售出商品数量
    private Integer favoriteCount; // 被收藏数量
} 