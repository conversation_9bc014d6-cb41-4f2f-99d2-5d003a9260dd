package com.lzhshtp.shangcheng.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 人工审核任务实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_lzhshtp_manual_audit_tasks")
public class ManualAuditTask {
    
    @TableId(value = "lzhshtp_task_id", type = IdType.AUTO)
    private Long taskId;
    
    @TableField("lzhshtp_product_id")
    private Long productId;
    
    @TableField("lzhshtp_seller_id")
    private Long sellerId;
    
    @TableField("lzhshtp_auto_audit_record_id")
    private Long autoAuditRecordId;
    
    @TableField("lzhshtp_audit_reasons")
    private String auditReasons;  // JSON格式
    
    @TableField("lzhshtp_status")
    private String status;  // pending, in_progress, completed, material_requested
    
    @TableField("lzhshtp_priority")
    private Integer priority;
    
    @TableField("lzhshtp_admin_id")
    private Long adminId;  // 处理审核的管理员ID
    
    @TableField("lzhshtp_admin_decision")
    private String adminDecision;  // approved, rejected, request_materials, escalate_to_second_review
    
    @TableField("lzhshtp_admin_comments")
    private String adminComments;
    
    @TableField("lzhshtp_created_time")
    private LocalDateTime createdTime;
    
    @TableField("lzhshtp_assigned_time")
    private LocalDateTime assignedTime;
    
    @TableField("lzhshtp_completed_time")
    private LocalDateTime completedTime;
    
    @TableField("lzhshtp_deadline")
    private LocalDateTime deadline;

    // 以下字段用于前端显示，不存储在数据库中
    @TableField(exist = false)
    private String productTitle;

    @TableField(exist = false)
    private String productDescription;

    @TableField(exist = false)
    private BigDecimal productPrice;

    @TableField(exist = false)
    private String productImages;

    @TableField(exist = false)
    private String sellerNickname;

    @TableField(exist = false)
    private LocalDateTime claimedTime;
}
