package com.lzhshtp.shangcheng.service.audit;

import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.mapper.AutoAuditRecordMapper;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.model.AutoAuditRecord;
import com.lzhshtp.shangcheng.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动审核记录服务
 */
@Slf4j
@Service
public class AutoAuditRecordService {
    
    @Autowired
    private AutoAuditRecordMapper autoAuditRecordMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private AutoAuditEngine autoAuditEngine;
    
    /**
     * 获取自动审核记录列表
     */
    public PageResult<AutoAuditRecord> getAutoAuditRecords(Integer page, Integer pageSize, 
                                                          Long productId, String decision, 
                                                          String startDate, String endDate) {
        
        // 构建查询条件
        Map<String, Object> params = new HashMap<>();
        if (productId != null) {
            params.put("productId", productId);
        }
        if (StringUtils.hasText(decision)) {
            params.put("finalDecision", decision);
        }
        if (StringUtils.hasText(startDate)) {
            params.put("startDate", startDate + " 00:00:00");
        }
        if (StringUtils.hasText(endDate)) {
            params.put("endDate", endDate + " 23:59:59");
        }
        
        // 分页查询
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("limit", pageSize);
        
        List<AutoAuditRecord> records = autoAuditRecordMapper.findByConditions(params);
        Long total = autoAuditRecordMapper.countByConditions(params);

        // 过滤空记录
        if (records != null) {
            records = records.stream()
                .filter(record -> record != null && record.getProductId() != null)
                .collect(Collectors.toList());
        }
        
        // 补充商品信息
        for (AutoAuditRecord record : records) {
            // 添加空值检查
            if (record == null) {
                log.warn("发现空的审核记录，跳过处理");
                continue;
            }

            if (record.getProductId() == null) {
                log.warn("审核记录的商品ID为空，记录ID: {}", record.getRecordId());
                continue;
            }

            Product product = productMapper.selectById(record.getProductId());
            if (product != null) {
                record.setProductTitle(product.getTitle());
                record.setProductPrice(product.getPrice());
            } else {
                log.warn("找不到商品信息，商品ID: {}", record.getProductId());
                record.setProductTitle("商品不存在");
                record.setProductPrice(null);
            }
        }
        
        return new PageResult<>(records, total, page, pageSize);
    }
    
    /**
     * 获取自动审核记录详情
     */
    public AutoAuditRecord getAutoAuditRecordDetail(Long recordId) {
        AutoAuditRecord record = autoAuditRecordMapper.selectById(recordId);
        
        if (record != null) {
            // 补充商品信息
            Product product = productMapper.selectById(record.getProductId());
            if (product != null) {
                record.setProductTitle(product.getTitle());
                record.setProductPrice(product.getPrice());
                record.setProductDescription(product.getDescription());
                record.setProductImages(product.getImageUrls());
            }
        }
        
        return record;
    }
    
    /**
     * 获取自动审核统计数据
     */
    public Map<String, Object> getAutoAuditStats(String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(startDate)) {
            params.put("startDate", startDate + " 00:00:00");
        }
        if (StringUtils.hasText(endDate)) {
            params.put("endDate", endDate + " 23:59:59");
        }
        
        // 获取各种决策的统计数据
        Long autoApproveCount = autoAuditRecordMapper.countByDecision("auto_approve", params);
        Long autoRejectCount = autoAuditRecordMapper.countByDecision("auto_reject", params);
        Long manualReviewCount = autoAuditRecordMapper.countByDecision("manual_review", params);
        Long totalCount = autoApproveCount + autoRejectCount + manualReviewCount;
        
        // 获取今日统计
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Map<String, Object> todayParams = new HashMap<>();
        todayParams.put("startDate", today + " 00:00:00");
        todayParams.put("endDate", today + " 23:59:59");
        
        Long todayTotal = autoAuditRecordMapper.countByConditions(todayParams);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("autoApprove", autoApproveCount);
        stats.put("autoReject", autoRejectCount);
        stats.put("manualReview", manualReviewCount);
        stats.put("total", totalCount);
        stats.put("todayTotal", todayTotal);
        
        // 计算通过率
        if (totalCount > 0) {
            double approveRate = (double) autoApproveCount / totalCount * 100;
            stats.put("approveRate", Math.round(approveRate * 100.0) / 100.0);
        } else {
            stats.put("approveRate", 0.0);
        }
        
        return stats;
    }
    
    /**
     * 重新执行自动审核
     */
    public AutoAuditRecord reauditProduct(Long productId) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new RuntimeException("商品不存在");
        }
        
        // 执行自动审核
        return autoAuditEngine.performAutoAudit(product);
    }
}
