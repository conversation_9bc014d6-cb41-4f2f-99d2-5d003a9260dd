{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/coffeescript.js"], "sourcesContent": ["var ERRORCLASS = \"error\";\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar operators = /^(?:->|=>|\\+[+=]?|-[\\-=]?|\\*[\\*=]?|\\/[\\/=]?|[=!]=|<[><]?=?|>>?=?|%=?|&=?|\\|=?|\\^=?|\\~|!|\\?|(or|and|\\|\\||&&|\\?)=)/;\nvar delimiters = /^(?:[()\\[\\]{},:`=;]|\\.\\.?\\.?)/;\nvar identifiers = /^[_A-Za-z$][_A-Za-z$0-9]*/;\nvar atProp = /^@[_A-Za-z$][_A-Za-z$0-9]*/;\n\nvar wordOperators = wordRegexp([\"and\", \"or\", \"not\",\n                                \"is\", \"isnt\", \"in\",\n                                \"instanceof\", \"typeof\"]);\nvar indentKeywords = [\"for\", \"while\", \"loop\", \"if\", \"unless\", \"else\",\n                      \"switch\", \"try\", \"catch\", \"finally\", \"class\"];\nvar commonKeywords = [\"break\", \"by\", \"continue\", \"debugger\", \"delete\",\n                      \"do\", \"in\", \"of\", \"new\", \"return\", \"then\",\n                      \"this\", \"@\", \"throw\", \"when\", \"until\", \"extends\"];\n\nvar keywords = wordRegexp(indentKeywords.concat(commonKeywords));\n\nindentKeywords = wordRegexp(indentKeywords);\n\n\nvar stringPrefixes = /^('{3}|\\\"{3}|['\\\"])/;\nvar regexPrefixes = /^(\\/{3}|\\/)/;\nvar commonConstants = [\"Infinity\", \"NaN\", \"undefined\", \"null\", \"true\", \"false\", \"on\", \"off\", \"yes\", \"no\"];\nvar constants = wordRegexp(commonConstants);\n\n// Tokenizers\nfunction tokenBase(stream, state) {\n  // Handle scope changes\n  if (stream.sol()) {\n    if (state.scope.align === null) state.scope.align = false;\n    var scopeOffset = state.scope.offset;\n    if (stream.eatSpace()) {\n      var lineOffset = stream.indentation();\n      if (lineOffset > scopeOffset && state.scope.type == \"coffee\") {\n        return \"indent\";\n      } else if (lineOffset < scopeOffset) {\n        return \"dedent\";\n      }\n      return null;\n    } else {\n      if (scopeOffset > 0) {\n        dedent(stream, state);\n      }\n    }\n  }\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var ch = stream.peek();\n\n  // Handle docco title comment (single line)\n  if (stream.match(\"####\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Handle multi line comments\n  if (stream.match(\"###\")) {\n    state.tokenize = longComment;\n    return state.tokenize(stream, state);\n  }\n\n  // Single line comment\n  if (ch === \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Handle number literals\n  if (stream.match(/^-?[0-9\\.]/, false)) {\n    var floatLiteral = false;\n    // Floats\n    if (stream.match(/^-?\\d*\\.\\d+(e[\\+\\-]?\\d+)?/i)) {\n      floatLiteral = true;\n    }\n    if (stream.match(/^-?\\d+\\.\\d*/)) {\n      floatLiteral = true;\n    }\n    if (stream.match(/^-?\\.\\d+/)) {\n      floatLiteral = true;\n    }\n\n    if (floatLiteral) {\n      // prevent from getting extra . on 1..\n      if (stream.peek() == \".\"){\n        stream.backUp(1);\n      }\n      return \"number\";\n    }\n    // Integers\n    var intLiteral = false;\n    // Hex\n    if (stream.match(/^-?0x[0-9a-f]+/i)) {\n      intLiteral = true;\n    }\n    // Decimal\n    if (stream.match(/^-?[1-9]\\d*(e[\\+\\-]?\\d+)?/)) {\n      intLiteral = true;\n    }\n    // Zero by itself with no other piece of number.\n    if (stream.match(/^-?0(?![\\dx])/i)) {\n      intLiteral = true;\n    }\n    if (intLiteral) {\n      return \"number\";\n    }\n  }\n\n  // Handle strings\n  if (stream.match(stringPrefixes)) {\n    state.tokenize = tokenFactory(stream.current(), false, \"string\");\n    return state.tokenize(stream, state);\n  }\n  // Handle regex literals\n  if (stream.match(regexPrefixes)) {\n    if (stream.current() != \"/\" || stream.match(/^.*\\//, false)) { // prevent highlight of division\n      state.tokenize = tokenFactory(stream.current(), true, \"string.special\");\n      return state.tokenize(stream, state);\n    } else {\n      stream.backUp(1);\n    }\n  }\n\n\n\n  // Handle operators and delimiters\n  if (stream.match(operators) || stream.match(wordOperators)) {\n    return \"operator\";\n  }\n  if (stream.match(delimiters)) {\n    return \"punctuation\";\n  }\n\n  if (stream.match(constants)) {\n    return \"atom\";\n  }\n\n  if (stream.match(atProp) || state.prop && stream.match(identifiers)) {\n    return \"property\";\n  }\n\n  if (stream.match(keywords)) {\n    return \"keyword\";\n  }\n\n  if (stream.match(identifiers)) {\n    return \"variable\";\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return ERRORCLASS;\n}\n\nfunction tokenFactory(delimiter, singleline, outclass) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      stream.eatWhile(/[^'\"\\/\\\\]/);\n      if (stream.eat(\"\\\\\")) {\n        stream.next();\n        if (singleline && stream.eol()) {\n          return outclass;\n        }\n      } else if (stream.match(delimiter)) {\n        state.tokenize = tokenBase;\n        return outclass;\n      } else {\n        stream.eat(/['\"\\/]/);\n      }\n    }\n    if (singleline) {\n      state.tokenize = tokenBase;\n    }\n    return outclass;\n  };\n}\n\nfunction longComment(stream, state) {\n  while (!stream.eol()) {\n    stream.eatWhile(/[^#]/);\n    if (stream.match(\"###\")) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    stream.eatWhile(\"#\");\n  }\n  return \"comment\";\n}\n\nfunction indent(stream, state, type = \"coffee\") {\n  var offset = 0, align = false, alignOffset = null;\n  for (var scope = state.scope; scope; scope = scope.prev) {\n    if (scope.type === \"coffee\" || scope.type == \"}\") {\n      offset = scope.offset + stream.indentUnit;\n      break;\n    }\n  }\n  if (type !== \"coffee\") {\n    align = null;\n    alignOffset = stream.column() + stream.current().length;\n  } else if (state.scope.align) {\n    state.scope.align = false;\n  }\n  state.scope = {\n    offset: offset,\n    type: type,\n    prev: state.scope,\n    align: align,\n    alignOffset: alignOffset\n  };\n}\n\nfunction dedent(stream, state) {\n  if (!state.scope.prev) return;\n  if (state.scope.type === \"coffee\") {\n    var _indent = stream.indentation();\n    var matched = false;\n    for (var scope = state.scope; scope; scope = scope.prev) {\n      if (_indent === scope.offset) {\n        matched = true;\n        break;\n      }\n    }\n    if (!matched) {\n      return true;\n    }\n    while (state.scope.prev && state.scope.offset !== _indent) {\n      state.scope = state.scope.prev;\n    }\n    return false;\n  } else {\n    state.scope = state.scope.prev;\n    return false;\n  }\n}\n\nfunction tokenLexer(stream, state) {\n  var style = state.tokenize(stream, state);\n  var current = stream.current();\n\n  // Handle scope changes.\n  if (current === \"return\") {\n    state.dedent = true;\n  }\n  if (((current === \"->\" || current === \"=>\") && stream.eol())\n      || style === \"indent\") {\n    indent(stream, state);\n  }\n  var delimiter_index = \"[({\".indexOf(current);\n  if (delimiter_index !== -1) {\n    indent(stream, state, \"])}\".slice(delimiter_index, delimiter_index+1));\n  }\n  if (indentKeywords.exec(current)){\n    indent(stream, state);\n  }\n  if (current == \"then\"){\n    dedent(stream, state);\n  }\n\n\n  if (style === \"dedent\") {\n    if (dedent(stream, state)) {\n      return ERRORCLASS;\n    }\n  }\n  delimiter_index = \"])}\".indexOf(current);\n  if (delimiter_index !== -1) {\n    while (state.scope.type == \"coffee\" && state.scope.prev)\n      state.scope = state.scope.prev;\n    if (state.scope.type == current)\n      state.scope = state.scope.prev;\n  }\n  if (state.dedent && stream.eol()) {\n    if (state.scope.type == \"coffee\" && state.scope.prev)\n      state.scope = state.scope.prev;\n    state.dedent = false;\n  }\n\n  return style == \"indent\" || style == \"dedent\" ? null : style;\n}\n\nexport const coffeeScript = {\n  name: \"coffeescript\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scope: {offset: 0, type:\"coffee\", prev: null, align: false},\n      prop: false,\n      dedent: 0\n    };\n  },\n\n  token: function(stream, state) {\n    var fillAlign = state.scope.align === null && state.scope;\n    if (fillAlign && stream.sol()) fillAlign.align = false;\n\n    var style = tokenLexer(stream, state);\n    if (style && style != \"comment\") {\n      if (fillAlign) fillAlign.align = true;\n      state.prop = style == \"punctuation\" && stream.current() == \".\"\n    }\n\n    return style;\n  },\n\n  indent: function(state, text) {\n    if (state.tokenize != tokenBase) return 0;\n    var scope = state.scope;\n    var closer = text && \"])}\".indexOf(text.charAt(0)) > -1;\n    if (closer) while (scope.type == \"coffee\" && scope.prev) scope = scope.prev;\n    var closes = closer && scope.type === text.charAt(0);\n    if (scope.align)\n      return scope.alignOffset - (closes ? 1 : 0);\n    else\n      return (closes ? scope.prev : scope).offset;\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,aAAa;AAEjB,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,SAAS;AAEb,IAAI,gBAAgB,WAAW;AAAA,EAAC;AAAA,EAAO;AAAA,EAAM;AAAA,EACb;AAAA,EAAM;AAAA,EAAQ;AAAA,EACd;AAAA,EAAc;AAAQ,CAAC;AACvD,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAO;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAU;AAAA,EACxC;AAAA,EAAU;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAO;AAClE,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAS;AAAA,EAAM;AAAA,EAAY;AAAA,EAAY;AAAA,EACvC;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EAAU;AAAA,EACnC;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAS;AAEtE,IAAI,WAAW,WAAW,eAAe,OAAO,cAAc,CAAC;AAE/D,iBAAiB,WAAW,cAAc;AAG1C,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB,CAAC,YAAY,OAAO,aAAa,QAAQ,QAAQ,SAAS,MAAM,OAAO,OAAO,IAAI;AACxG,IAAI,YAAY,WAAW,eAAe;AAG1C,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,OAAO,IAAI,GAAG;AAChB,QAAI,MAAM,MAAM,UAAU;AAAM,YAAM,MAAM,QAAQ;AACpD,QAAI,cAAc,MAAM,MAAM;AAC9B,QAAI,OAAO,SAAS,GAAG;AACrB,UAAI,aAAa,OAAO,YAAY;AACpC,UAAI,aAAa,eAAe,MAAM,MAAM,QAAQ,UAAU;AAC5D,eAAO;AAAA,MACT,WAAW,aAAa,aAAa;AACnC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,OAAO;AACL,UAAI,cAAc,GAAG;AACnB,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,MAAM,MAAM,GAAG;AACxB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,KAAK,GAAG;AACvB,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AACrC,QAAI,eAAe;AAEnB,QAAI,OAAO,MAAM,4BAA4B,GAAG;AAC9C,qBAAe;AAAA,IACjB;AACA,QAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,qBAAe;AAAA,IACjB;AACA,QAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,qBAAe;AAAA,IACjB;AAEA,QAAI,cAAc;AAEhB,UAAI,OAAO,KAAK,KAAK,KAAI;AACvB,eAAO,OAAO,CAAC;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,aAAa;AAEjB,QAAI,OAAO,MAAM,iBAAiB,GAAG;AACnC,mBAAa;AAAA,IACf;AAEA,QAAI,OAAO,MAAM,2BAA2B,GAAG;AAC7C,mBAAa;AAAA,IACf;AAEA,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,mBAAa;AAAA,IACf;AACA,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAM,WAAW,aAAa,OAAO,QAAQ,GAAG,OAAO,QAAQ;AAC/D,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAEA,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,QAAI,OAAO,QAAQ,KAAK,OAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAC3D,YAAM,WAAW,aAAa,OAAO,QAAQ,GAAG,MAAM,gBAAgB;AACtE,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,OAAO;AACL,aAAO,OAAO,CAAC;AAAA,IACjB;AAAA,EACF;AAKA,MAAI,OAAO,MAAM,SAAS,KAAK,OAAO,MAAM,aAAa,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,MAAM,KAAK,MAAM,QAAQ,OAAO,MAAM,WAAW,GAAG;AACnE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,WAAO;AAAA,EACT;AAGA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,aAAa,WAAW,YAAY,UAAU;AACrD,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,aAAO,SAAS,WAAW;AAC3B,UAAI,OAAO,IAAI,IAAI,GAAG;AACpB,eAAO,KAAK;AACZ,YAAI,cAAc,OAAO,IAAI,GAAG;AAC9B,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,cAAM,WAAW;AACjB,eAAO;AAAA,MACT,OAAO;AACL,eAAO,IAAI,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,QAAI,YAAY;AACd,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,SAAO,CAAC,OAAO,IAAI,GAAG;AACpB,WAAO,SAAS,MAAM;AACtB,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,WAAO,SAAS,GAAG;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,OAAO,QAAQ,OAAO,OAAO,UAAU;AAC9C,MAAI,SAAS,GAAG,QAAQ,OAAO,cAAc;AAC7C,WAAS,QAAQ,MAAM,OAAO,OAAO,QAAQ,MAAM,MAAM;AACvD,QAAI,MAAM,SAAS,YAAY,MAAM,QAAQ,KAAK;AAChD,eAAS,MAAM,SAAS,OAAO;AAC/B;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,UAAU;AACrB,YAAQ;AACR,kBAAc,OAAO,OAAO,IAAI,OAAO,QAAQ,EAAE;AAAA,EACnD,WAAW,MAAM,MAAM,OAAO;AAC5B,UAAM,MAAM,QAAQ;AAAA,EACtB;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA,MAAM,MAAM;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,CAAC,MAAM,MAAM;AAAM;AACvB,MAAI,MAAM,MAAM,SAAS,UAAU;AACjC,QAAI,UAAU,OAAO,YAAY;AACjC,QAAI,UAAU;AACd,aAAS,QAAQ,MAAM,OAAO,OAAO,QAAQ,MAAM,MAAM;AACvD,UAAI,YAAY,MAAM,QAAQ;AAC5B,kBAAU;AACV;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AACA,WAAO,MAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,SAAS;AACzD,YAAM,QAAQ,MAAM,MAAM;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,OAAO;AACL,UAAM,QAAQ,MAAM,MAAM;AAC1B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,MAAI,UAAU,OAAO,QAAQ;AAG7B,MAAI,YAAY,UAAU;AACxB,UAAM,SAAS;AAAA,EACjB;AACA,OAAM,YAAY,QAAQ,YAAY,SAAS,OAAO,IAAI,KACnD,UAAU,UAAU;AACzB,WAAO,QAAQ,KAAK;AAAA,EACtB;AACA,MAAI,kBAAkB,MAAM,QAAQ,OAAO;AAC3C,MAAI,oBAAoB,IAAI;AAC1B,WAAO,QAAQ,OAAO,MAAM,MAAM,iBAAiB,kBAAgB,CAAC,CAAC;AAAA,EACvE;AACA,MAAI,eAAe,KAAK,OAAO,GAAE;AAC/B,WAAO,QAAQ,KAAK;AAAA,EACtB;AACA,MAAI,WAAW,QAAO;AACpB,WAAO,QAAQ,KAAK;AAAA,EACtB;AAGA,MAAI,UAAU,UAAU;AACtB,QAAI,OAAO,QAAQ,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,oBAAkB,MAAM,QAAQ,OAAO;AACvC,MAAI,oBAAoB,IAAI;AAC1B,WAAO,MAAM,MAAM,QAAQ,YAAY,MAAM,MAAM;AACjD,YAAM,QAAQ,MAAM,MAAM;AAC5B,QAAI,MAAM,MAAM,QAAQ;AACtB,YAAM,QAAQ,MAAM,MAAM;AAAA,EAC9B;AACA,MAAI,MAAM,UAAU,OAAO,IAAI,GAAG;AAChC,QAAI,MAAM,MAAM,QAAQ,YAAY,MAAM,MAAM;AAC9C,YAAM,QAAQ,MAAM,MAAM;AAC5B,UAAM,SAAS;AAAA,EACjB;AAEA,SAAO,SAAS,YAAY,SAAS,WAAW,OAAO;AACzD;AAEO,IAAM,eAAe;AAAA,EAC1B,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,OAAO,EAAC,QAAQ,GAAG,MAAK,UAAU,MAAM,MAAM,OAAO,MAAK;AAAA,MAC1D,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,YAAY,MAAM,MAAM,UAAU,QAAQ,MAAM;AACpD,QAAI,aAAa,OAAO,IAAI;AAAG,gBAAU,QAAQ;AAEjD,QAAI,QAAQ,WAAW,QAAQ,KAAK;AACpC,QAAI,SAAS,SAAS,WAAW;AAC/B,UAAI;AAAW,kBAAU,QAAQ;AACjC,YAAM,OAAO,SAAS,iBAAiB,OAAO,QAAQ,KAAK;AAAA,IAC7D;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,MAAM;AAC5B,QAAI,MAAM,YAAY;AAAW,aAAO;AACxC,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,QAAQ,MAAM,QAAQ,KAAK,OAAO,CAAC,CAAC,IAAI;AACrD,QAAI;AAAQ,aAAO,MAAM,QAAQ,YAAY,MAAM;AAAM,gBAAQ,MAAM;AACvE,QAAI,SAAS,UAAU,MAAM,SAAS,KAAK,OAAO,CAAC;AACnD,QAAI,MAAM;AACR,aAAO,MAAM,eAAe,SAAS,IAAI;AAAA;AAEzC,cAAQ,SAAS,MAAM,OAAO,OAAO;AAAA,EACzC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}