package com.lzhshtp.shangcheng.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.lzhshtp.shangcheng.dto.ProductReviewDTO;
import com.lzhshtp.shangcheng.dto.ProductReviewRequest;
import com.lzhshtp.shangcheng.mapper.ProductMapper;
import com.lzhshtp.shangcheng.mapper.ProductReviewMapper;
import com.lzhshtp.shangcheng.mapper.UserMapper;
import com.lzhshtp.shangcheng.model.Product;
import com.lzhshtp.shangcheng.model.ProductReview;
import com.lzhshtp.shangcheng.model.User;
import com.lzhshtp.shangcheng.service.ProductReviewService;

/**
 * 商品评价Service实现类
 */
@Service
public class ProductReviewServiceImpl implements ProductReviewService {

    @Autowired
    private ProductReviewMapper productReviewMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional
    public ProductReviewDTO addReview(Long userId, ProductReviewRequest request) {
        // 查询商品信息
        Product product = productMapper.selectProductDetail(request.getProductId());
        if (product == null) {
            throw new IllegalArgumentException("商品不存在");
        }


        // 创建评价对象
        ProductReview review = ProductReview.builder()
                .lzhshtpProductId(request.getProductId())
                .lzhshtpReviewerId(userId)
                .lzhshtpSellerId(product.getSellerId())
                .lzhshtpRating(request.getRating())
                .lzhshtpComment(request.getComment())
                .lzhshtpReviewDate(LocalDateTime.now())
                .build();

        // 保存评价
        productReviewMapper.insert(review);

        // 查询用户信息
        User user = userMapper.selectById(userId);

        // 返回DTO
        return convertToDTO(review, user);
    }

    @Override
    public List<ProductReviewDTO> getReviewsByProductId(Long productId) {
        List<ProductReview> reviews = productReviewMapper.selectByProductId(productId);
        return convertToDTOList(reviews);
    }

    @Override
    public ProductReviewDTO getReviewById(Long reviewId) {
        ProductReview review = productReviewMapper.selectById(reviewId);
        if (review == null) {
            return null;
        }

        User user = userMapper.selectById(review.getLzhshtpReviewerId());
        return convertToDTO(review, user);
    }

    @Override
    public List<ProductReviewDTO> getReviewsByUserId(Long userId) {
        List<ProductReview> reviews = productReviewMapper.selectByReviewerId(userId);
        return convertToDTOList(reviews);
    }

    @Override
    @Transactional
    public boolean deleteReview(Long userId, Long reviewId) {
        ProductReview review = productReviewMapper.selectById(reviewId);
        if (review == null) {
            return false;
        }

        // 检查是否是评价的作者
        if (!review.getLzhshtpReviewerId().equals(userId)) {
            return false;
        }

        return productReviewMapper.deleteById(reviewId) > 0;
    }

    @Override
    @Transactional
    public ProductReviewDTO updateReview(Long userId, Long reviewId, ProductReviewRequest request) {
        ProductReview review = productReviewMapper.selectById(reviewId);
        if (review == null) {
            throw new IllegalArgumentException("评价不存在");
        }

        // 检查是否是评价的作者
        if (!review.getLzhshtpReviewerId().equals(userId)) {
            throw new IllegalArgumentException("您无权修改此评价");
        }

        // 更新评价
        review.setLzhshtpRating(request.getRating());
        review.setLzhshtpComment(request.getComment());

        productReviewMapper.update(review);

        // 查询用户信息
        User user = userMapper.selectById(userId);

        return convertToDTO(review, user);
    }

    @Override
    public int getReviewCount(Long productId) {
        return productReviewMapper.countByProductId(productId);
    }

    @Override
    public Double getAverageRating(Long productId) {
        Double avgRating = productReviewMapper.getAverageRatingByProductId(productId);
        return avgRating != null ? avgRating : 0.0;
    }

    /**
     * 将评价实体转换为DTO
     *
     * @param review 评价实体
     * @param user 用户实体
     * @return 评价DTO
     */
    private ProductReviewDTO convertToDTO(ProductReview review, User user) {
        return ProductReviewDTO.builder()
                .reviewId(review.getLzhshtpReviewId())
                .productId(review.getLzhshtpProductId())
                .reviewerId(review.getLzhshtpReviewerId())
                .reviewerName(user != null ? user.getUsername() : "未知用户")
                .reviewerAvatar(user != null ? user.getAvatarUrl() : null)
                .rating(review.getLzhshtpRating())
                .comment(review.getLzhshtpComment())
                .reviewDate(review.getLzhshtpReviewDate())
                .build();
    }

    /**
     * 将评价实体列表转换为DTO列表
     *
     * @param reviews 评价实体列表
     * @return 评价DTO列表
     */
    private List<ProductReviewDTO> convertToDTOList(List<ProductReview> reviews) {
        List<ProductReviewDTO> dtoList = new ArrayList<>();

        for (ProductReview review : reviews) {
            User user = userMapper.selectById(review.getLzhshtpReviewerId());
            dtoList.add(convertToDTO(review, user));
        }

        return dtoList;
    }
}
