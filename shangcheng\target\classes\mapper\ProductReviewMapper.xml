<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lzhshtp.shangcheng.mapper.ProductReviewMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.lzhshtp.shangcheng.model.ProductReview">
        <id column="lzhshtp_review_id" property="lzhshtpReviewId" />
        <result column="lzhshtp_product_id" property="lzhshtpProductId" />
        <result column="lzhshtp_reviewer_id" property="lzhshtpReviewerId" />
        <result column="lzhshtp_seller_id" property="lzhshtpSellerId" />
        <result column="lzhshtp_rating" property="lzhshtpRating" />
        <result column="lzhshtp_comment" property="lzhshtpComment" />
        <result column="lzhshtp_review_date" property="lzhshtpReviewDate" />
    </resultMap>
    
    <!-- 通用列 -->
    <sql id="Base_Column_List">
        lzhshtp_review_id, lzhshtp_product_id, lzhshtp_reviewer_id, lzhshtp_seller_id, 
        lzhshtp_rating, lzhshtp_comment, lzhshtp_review_date
    </sql>
    
    <!-- 添加商品评价 -->
    <insert id="insert" parameterType="com.lzhshtp.shangcheng.model.ProductReview" useGeneratedKeys="true" keyProperty="lzhshtpReviewId">
        INSERT INTO tb_lzhshtp_product_reviews (
            lzhshtp_product_id, lzhshtp_reviewer_id, lzhshtp_seller_id, 
            lzhshtp_rating, lzhshtp_comment, lzhshtp_review_date
        ) VALUES (
            #{lzhshtpProductId}, #{lzhshtpReviewerId}, #{lzhshtpSellerId}, 
            #{lzhshtpRating}, #{lzhshtpComment}, #{lzhshtpReviewDate}
        )
    </insert>
    
    <!-- 根据商品ID查询评价列表 -->
    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_product_id = #{productId}
        ORDER BY 
            lzhshtp_review_date DESC
    </select>
    
    <!-- 根据评价ID查询评价 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_review_id = #{reviewId}
    </select>
    
    <!-- 根据用户ID查询该用户发布的评价 -->
    <select id="selectByReviewerId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_reviewer_id = #{reviewerId}
        ORDER BY 
            lzhshtp_review_date DESC
    </select>
    
    <!-- 删除评价 -->
    <delete id="deleteById">
        DELETE FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_review_id = #{reviewId}
    </delete>
    
    <!-- 更新评价 -->
    <update id="update" parameterType="com.lzhshtp.shangcheng.model.ProductReview">
        UPDATE 
            tb_lzhshtp_product_reviews
        SET 
            lzhshtp_rating = #{lzhshtpRating},
            lzhshtp_comment = #{lzhshtpComment}
        WHERE 
            lzhshtp_review_id = #{lzhshtpReviewId}
    </update>
    
    <!-- 获取商品的评价总数 -->
    <select id="countByProductId" resultType="java.lang.Integer">
        SELECT 
            COUNT(*)
        FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_product_id = #{productId}
    </select>
    
    <!-- 获取商品的平均评分 -->
    <select id="getAverageRatingByProductId" resultType="java.lang.Double">
        SELECT 
            AVG(lzhshtp_rating)
        FROM 
            tb_lzhshtp_product_reviews
        WHERE 
            lzhshtp_product_id = #{productId}
    </select>
</mapper> 