package com.lzhshtp.shangcheng.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 评价统计信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReviewStatistics {

    /**
     * 统计天数
     */
    private Integer days;

    /**
     * 总评价数
     */
    private Integer totalReviews;

    /**
     * 好评数（4-5星）
     */
    private Integer positiveReviews;

    /**
     * 中评数（3星）
     */
    private Integer neutralReviews;

    /**
     * 差评数（1-2星）
     */
    private Integer negativeReviews;

    /**
     * 好评率
     */
    private BigDecimal positiveRate;

    /**
     * 平均评分
     */
    private BigDecimal averageRating;

    /**
     * 星级分布
     */
    private Map<Integer, Integer> ratingDistribution;

    /**
     * 信用分变化统计
     */
    private Map<String, Object> creditScoreChanges;

    /**
     * 每日评价数量趋势
     */
    private Map<String, Integer> dailyReviewTrend;

    /**
     * 异常评价检测结果
     */
    private Map<String, Object> abnormalDetection;

    /**
     * 获取好评率百分比字符串
     */
    public String getPositiveRateString() {
        if (positiveRate == null) {
            return "0%";
        }
        return positiveRate.setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 获取平均评分字符串
     */
    public String getAverageRatingString() {
        if (averageRating == null) {
            return "0.0";
        }
        return averageRating.setScale(1, BigDecimal.ROUND_HALF_UP).toString();
    }
}
