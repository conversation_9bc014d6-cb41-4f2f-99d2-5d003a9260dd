{"version": 3, "sources": ["../../@codemirror/lang-angular/dist/index.js"], "sourcesContent": ["import { LRLanguage, LanguageSupport } from '@codemirror/language';\nimport { html } from '@codemirror/lang-html';\nimport { javascriptLanguage } from '@codemirror/lang-javascript';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\nimport { ExternalTokenizer, LRParser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Text = 1,\n  attributeContentSingle = 33,\n  attributeContentDouble = 34,\n  scriptAttributeContentSingle = 35,\n  scriptAttributeContentDouble = 36;\n\nconst text = /*@__PURE__*/new ExternalTokenizer(input => {\n    let start = input.pos;\n    for (;;) {\n        if (input.next == 10 /* Ch.Newline */) {\n            input.advance();\n            break;\n        }\n        else if (input.next == 123 /* Ch.BraceL */ && input.peek(1) == 123 /* Ch.BraceL */ || input.next < 0) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.pos > start)\n        input.acceptToken(Text);\n});\nfunction attrContent(quote, token, script) {\n    return new ExternalTokenizer(input => {\n        let start = input.pos;\n        while (input.next != quote && input.next >= 0 &&\n            (script || input.next != 38 /* Ch.Ampersand */ && (input.next != 123 /* Ch.BraceL */ || input.peek(1) != 123 /* Ch.BraceL */)))\n            input.advance();\n        if (input.pos > start)\n            input.acceptToken(token);\n    });\n}\nconst attrSingle = /*@__PURE__*/attrContent(39 /* Ch.SingleQuote */, attributeContentSingle, false);\nconst attrDouble = /*@__PURE__*/attrContent(34 /* Ch.DoubleQuote */, attributeContentDouble, false);\nconst scriptAttrSingle = /*@__PURE__*/attrContent(39 /* Ch.SingleQuote */, scriptAttributeContentSingle, true);\nconst scriptAttrDouble = /*@__PURE__*/attrContent(34 /* Ch.DoubleQuote */, scriptAttributeContentDouble, true);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"(jOVOqOOOeQpOOOvO!bO'#CaOOOP'#Cx'#CxQVOqOOO!OQpO'#CfO!WQpO'#ClO!]QpO'#CrO!bQpO'#CsOOQO'#Cv'#CvQ!gQpOOQ!lQpOOQ!qQpOOOOOV,58{,58{O!vOpO,58{OOOP-E6v-E6vO!{QpO,59QO#TQpO,59QOOQO,59W,59WO#YQpO,59^OOQO,59_,59_O#_QpOOO#_QpOOO#gQpOOOOOV1G.g1G.gO#oQpO'#CyO#tQpO1G.lOOQO1G.l1G.lO#|QpO1G.lOOQO1G.x1G.xO$UO`O'#DUO$ZOWO'#DUOOQO'#Co'#CoQOQpOOOOQO'#Cu'#CuO$`OtO'#CwO$qOrO'#CwOOQO,59e,59eOOQO-E6w-E6wOOQO7+$W7+$WO%SQpO7+$WO%[QpO7+$WOOOO'#Cp'#CpO%aOpO,59pOOOO'#Cq'#CqO%fOpO,59pOOOS'#Cz'#CzO%kOtO,59cOOQO,59c,59cOOOQ'#C{'#C{O%|OrO,59cO&_QpO<<GrOOQO<<Gr<<GrOOQO1G/[1G/[OOOS-E6x-E6xOOQO1G.}1G.}OOOQ-E6y-E6yOOQOAN=^AN=^\",\n  stateData: \"&d~OvOS~OPROSQOVROWRO~OZTO[XO^VOaUOhWO~OR]OU^O~O[`O^aO~O[bO~O[cO~O[dO~ObeO~ObfO~ObgO~ORhO~O]kOwiO~O[lO~O_mO~OynOzoO~OysOztO~O[uO~O]wOwiO~O_yOwiO~OtzO~Os|O~OSQOV!OOW!OOr!OOy!QO~OSQOV!ROW!ROq!ROz!QO~O_!TOwiO~O]!UO~Oy!VO~Oz!VO~OSQOV!OOW!OOr!OOy!XO~OSQOV!ROW!ROq!ROz!XO~O]!ZO~O\",\n  goto: \"#dyPPPPPzPPPP!WPPPPP!WPP!Z!^!a!d!dP!g!j!m!p!v#Q#WPPPPPPPP#^SROSS!Os!PT!Rt!SRYPRqeR{nR}oRZPRqfR[PRqgQSOR_SQj`SvjxRxlQ!PsR!W!PQ!StR!Y!SQpeRrf\",\n  nodeNames: \"⚠ Text Content }} {{ Interpolation InterpolationContent Entity InvalidEntity Attribute BoundAttributeName [ Identifier ] ( ) ReferenceName # Is ExpressionAttributeValue AttributeInterpolation AttributeInterpolation EventName DirectiveName * StatementAttributeValue AttributeName AttributeValue\",\n  maxTerm: 42,\n  nodeProps: [\n    [\"openedBy\", 3,\"{{\",15,\"(\"],\n    [\"closedBy\", 4,\"}}\",14,\")\"],\n    [\"isolate\", -4,5,19,25,27,\"\"]\n  ],\n  skippedNodes: [0],\n  repeatNodeCount: 4,\n  tokenData: \"0r~RyOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mqr#rrs%jst&Qtv#rvw&hwx)zxy*byz*xz{+`{}#r}!O+v!O!P-]!P!Q#r!Q![+v![!]+v!]!_#r!_!`-s!`!c#r!c!}+v!}#O.Z#O#P#r#P#Q.q#Q#R#r#R#S+v#S#T#r#T#o+v#o#p/X#p#q#r#q#r0Z#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vQ#wTUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rQ$ZSO#q#r#r;'S#r;'S;=`$g<%lO#rQ$jP;=`<%l#rR$t[UQvPOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mq#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR%qTyPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&XTaPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&oXUQWPOp'[pq#rq!]'[!]!^#r!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R'aXUQOp'[pq#rq!]'[!]!^'|!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R(TTVPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR(gXOp'[pq#rq!]'[!]!^'|!^#q'[#q#r)S#r;'S'[;'S;=`)t<%lO'[P)VUOp)Sq!])S!]!^)i!^;'S)S;'S;=`)n<%lO)SP)nOVPP)qP;=`<%l)SR)wP;=`<%l'[R*RTzPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR*iT^PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+PT_PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+gThPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+}b[PUQO}#r}!O+v!O!Q#r!Q![+v![!]+v!]!c#r!c!}+v!}#R#r#R#S+v#S#T#r#T#o+v#o#q#r#q#r$W#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vR-YP;=`<%l+vR-dTwPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR-zTUQbPO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.bTZPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.xT]PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/^VUQO#o#r#o#p/s#p#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/zTSPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#r~0^TO#q#r#q#r0m#r;'S#r;'S;=`$g<%lO#r~0rOR~\",\n  tokenizers: [text, attrSingle, attrDouble, scriptAttrSingle, scriptAttrDouble, 0, 1],\n  topRules: {\"Content\":[0,2],\"Attribute\":[1,9]},\n  tokenPrec: 0\n});\n\nconst exprParser = /*@__PURE__*/javascriptLanguage.parser.configure({\n    top: \"SingleExpression\"\n});\nconst baseParser = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/styleTags({\n            Text: tags.content,\n            Is: tags.definitionOperator,\n            AttributeName: tags.attributeName,\n            \"AttributeValue ExpressionAttributeValue StatementAttributeValue\": tags.attributeValue,\n            Entity: tags.character,\n            InvalidEntity: tags.invalid,\n            \"BoundAttributeName/Identifier\": tags.attributeName,\n            \"EventName/Identifier\": /*@__PURE__*/tags.special(tags.attributeName),\n            \"ReferenceName/Identifier\": tags.variableName,\n            \"DirectiveName/Identifier\": tags.keyword,\n            \"{{ }}\": tags.brace,\n            \"( )\": tags.paren,\n            \"[ ]\": tags.bracket,\n            \"# '*'\": tags.punctuation\n        })\n    ]\n});\nconst exprMixed = { parser: exprParser }, statementMixed = { parser: javascriptLanguage.parser };\nconst textParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/parseMixed((node, input) => node.name == \"InterpolationContent\" ? exprMixed : null),\n});\nconst attrParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/parseMixed((node, input) => {\n        var _a;\n        return node.name == \"InterpolationContent\" ? exprMixed\n            : node.name != \"AttributeInterpolation\" ? null\n                : ((_a = node.node.parent) === null || _a === void 0 ? void 0 : _a.name) == \"StatementAttributeValue\" ? statementMixed : exprMixed;\n    }),\n    top: \"Attribute\"\n});\nconst textMixed = { parser: textParser }, attrMixed = { parser: attrParser };\nconst baseHTML = /*@__PURE__*/html();\nfunction mkAngular(language) {\n    return language.configure({ wrap: parseMixed(mixAngular) }, \"angular\");\n}\n/**\nA language provider for Angular Templates.\n*/\nconst angularLanguage = /*@__PURE__*/mkAngular(baseHTML.language);\nfunction mixAngular(node, input) {\n    switch (node.name) {\n        case \"Attribute\":\n            return /^[*#(\\[]|\\{\\{/.test(input.read(node.from, node.to)) ? attrMixed : null;\n        case \"Text\":\n            return textMixed;\n    }\n    return null;\n}\n/**\nAngular Template language support.\n*/\nfunction angular(config = {}) {\n    let base = baseHTML;\n    if (config.base) {\n        if (config.base.language.name != \"html\" || !(config.base.language instanceof LRLanguage))\n            throw new RangeError(\"The base option must be the result of calling html(...)\");\n        base = config.base;\n    }\n    return new LanguageSupport(base.language == baseHTML.language ? angularLanguage : mkAngular(base.language), [base.support, base.language.data.of({\n            closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n            indentOnInput: /^\\s*[\\}\\]]$/\n        })]);\n}\n\nexport { angular, angularLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA,IAAM,OAAO;AAAb,IACE,yBAAyB;AAD3B,IAEE,yBAAyB;AAF3B,IAGE,+BAA+B;AAHjC,IAIE,+BAA+B;AAEjC,IAAM,OAAoB,IAAI,kBAAkB,WAAS;AACrD,MAAI,QAAQ,MAAM;AAClB,aAAS;AACL,QAAI,MAAM,QAAQ,IAAqB;AACnC,YAAM,QAAQ;AACd;AAAA,IACJ,WACS,MAAM,QAAQ,OAAuB,MAAM,KAAK,CAAC,KAAK,OAAuB,MAAM,OAAO,GAAG;AAClG;AAAA,IACJ;AACA,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,MAAM,MAAM;AACZ,UAAM,YAAY,IAAI;AAC9B,CAAC;AACD,SAAS,YAAY,OAAO,OAAO,QAAQ;AACvC,SAAO,IAAI,kBAAkB,WAAS;AAClC,QAAI,QAAQ,MAAM;AAClB,WAAO,MAAM,QAAQ,SAAS,MAAM,QAAQ,MACvC,UAAU,MAAM,QAAQ,OAA0B,MAAM,QAAQ,OAAuB,MAAM,KAAK,CAAC,KAAK;AACzG,YAAM,QAAQ;AAClB,QAAI,MAAM,MAAM;AACZ,YAAM,YAAY,KAAK;AAAA,EAC/B,CAAC;AACL;AACA,IAAM,aAA0B,YAAY,IAAyB,wBAAwB,KAAK;AAClG,IAAM,aAA0B,YAAY,IAAyB,wBAAwB,KAAK;AAClG,IAAM,mBAAgC,YAAY,IAAyB,8BAA8B,IAAI;AAC7G,IAAM,mBAAgC,YAAY,IAAyB,8BAA8B,IAAI;AAG7G,IAAM,SAAsB,SAAS,YAAY;AAAA,EAC/C,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,YAAY,GAAE,MAAK,IAAG,GAAG;AAAA,IAC1B,CAAC,YAAY,GAAE,MAAK,IAAG,GAAG;AAAA,IAC1B,CAAC,WAAW,IAAG,GAAE,IAAG,IAAG,IAAG,EAAE;AAAA,EAC9B;AAAA,EACA,cAAc,CAAC,CAAC;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,MAAM,YAAY,YAAY,kBAAkB,kBAAkB,GAAG,CAAC;AAAA,EACnF,UAAU,EAAC,WAAU,CAAC,GAAE,CAAC,GAAE,aAAY,CAAC,GAAE,CAAC,EAAC;AAAA,EAC5C,WAAW;AACb,CAAC;AAED,IAAM,aAA0B,mBAAmB,OAAO,UAAU;AAAA,EAChE,KAAK;AACT,CAAC;AACD,IAAM,aAA0B,OAAO,UAAU;AAAA,EAC7C,OAAO;AAAA,IACU,UAAU;AAAA,MACnB,MAAM,KAAK;AAAA,MACX,IAAI,KAAK;AAAA,MACT,eAAe,KAAK;AAAA,MACpB,mEAAmE,KAAK;AAAA,MACxE,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,iCAAiC,KAAK;AAAA,MACtC,wBAAqC,KAAK,QAAQ,KAAK,aAAa;AAAA,MACpE,4BAA4B,KAAK;AAAA,MACjC,4BAA4B,KAAK;AAAA,MACjC,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAClB,CAAC;AAAA,EACL;AACJ,CAAC;AACD,IAAM,YAAY,EAAE,QAAQ,WAAW;AAAvC,IAA0C,iBAAiB,EAAE,QAAQ,mBAAmB,OAAO;AAC/F,IAAM,aAA0B,WAAW,UAAU;AAAA,EACjD,MAAmB,WAAW,CAAC,MAAM,UAAU,KAAK,QAAQ,yBAAyB,YAAY,IAAI;AACzG,CAAC;AACD,IAAM,aAA0B,WAAW,UAAU;AAAA,EACjD,MAAmB,WAAW,CAAC,MAAM,UAAU;AAC3C,QAAI;AACJ,WAAO,KAAK,QAAQ,yBAAyB,YACvC,KAAK,QAAQ,2BAA2B,SAClC,KAAK,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,4BAA4B,iBAAiB;AAAA,EACrI,CAAC;AAAA,EACD,KAAK;AACT,CAAC;AACD,IAAM,YAAY,EAAE,QAAQ,WAAW;AAAvC,IAA0C,YAAY,EAAE,QAAQ,WAAW;AAC3E,IAAM,WAAwB,KAAK;AACnC,SAAS,UAAU,UAAU;AACzB,SAAO,SAAS,UAAU,EAAE,MAAM,WAAW,UAAU,EAAE,GAAG,SAAS;AACzE;AAIA,IAAM,kBAA+B,UAAU,SAAS,QAAQ;AAChE,SAAS,WAAW,MAAM,OAAO;AAC7B,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,aAAO,gBAAgB,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,CAAC,IAAI,YAAY;AAAA,IAC9E,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,SAAS,CAAC,GAAG;AAC1B,MAAI,OAAO;AACX,MAAI,OAAO,MAAM;AACb,QAAI,OAAO,KAAK,SAAS,QAAQ,UAAU,EAAE,OAAO,KAAK,oBAAoB;AACzE,YAAM,IAAI,WAAW,yDAAyD;AAClF,WAAO,OAAO;AAAA,EAClB;AACA,SAAO,IAAI,gBAAgB,KAAK,YAAY,SAAS,WAAW,kBAAkB,UAAU,KAAK,QAAQ,GAAG,CAAC,KAAK,SAAS,KAAK,SAAS,KAAK,GAAG;AAAA,IACzI,eAAe,EAAE,UAAU,CAAC,KAAK,KAAK,GAAG,EAAE;AAAA,IAC3C,eAAe;AAAA,EACnB,CAAC,CAAC,CAAC;AACX;", "names": []}