<template>
  <div class="second-review-view">
    <div class="page-header">
      <h2>二度复审</h2>
      <p class="page-description">处理升级到二度复审的商品，需要高级管理员审核</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ stats.pending }}</div>
        <div class="stat-label">待复审</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.in_progress }}</div>
        <div class="stat-label">复审中</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.approved }}</div>
        <div class="stat-label">最终通过</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.rejected }}</div>
        <div class="stat-label">最终拒绝</div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-form">
        <div class="form-row">
          <div class="form-item">
            <label>复审状态：</label>
            <select v-model="filterForm.status" class="form-select">
              <option value="">全部</option>
              <option value="pending">待复审</option>
              <option value="in_progress">复审中</option>
              <option value="completed">已完成</option>
            </select>
          </div>
          <div class="form-item">
            <label>升级原因：</label>
            <select v-model="filterForm.escalationReason" class="form-select">
              <option value="">全部</option>
              <option value="price">价格相关</option>
              <option value="credit">信用分问题</option>
              <option value="manual">人工升级</option>
            </select>
          </div>
          <div class="form-item">
            <label>商品ID：</label>
            <input
              v-model="filterForm.productId"
              type="text"
              placeholder="请输入商品ID"
              class="form-input"
            />
          </div>
          <div class="form-item">
            <button @click="handleFilter" class="search-btn">筛选</button>
            <button @click="handleResetFilter" class="reset-btn">重置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 复审任务列表 -->
    <div class="review-list">
      <div class="list-header">
        <span class="total-count">共 {{ pagination.total }} 个复审任务</span>
        <button @click="refreshTasks" class="refresh-btn">刷新</button>
      </div>

      <div class="review-cards">
        <div
          v-for="task in validReviewTasks"
          :key="task.taskId"
          class="review-card"
          :class="getTaskCardClass(task)"
        >
          <div class="card-header">
            <div class="task-info">
              <span class="task-id">复审任务 #{{ task.taskId }}</span>
              <span class="status-badge" :class="getStatusClass(task.status)">
                {{ getStatusText(task.status) }}
              </span>
              <span class="escalation-badge">
                {{ getEscalationReasonText(task.escalationReason) }}
              </span>
            </div>
          </div>

          <div class="card-content">
            <!-- 商品信息 -->
            <div class="product-section">
              <h4>商品信息</h4>
              <div class="product-info">
                <div class="info-item">
                  <label>商品ID：</label>
                  <span>{{ task.productId }}</span>
                </div>
                <div class="info-item">
                  <label>商品标题：</label>
                  <span>{{ task.productTitle }}</span>
                </div>
                <div class="info-item">
                  <label>商品价格：</label>
                  <span class="price">¥{{ task.productPrice }}</span>
                </div>
              </div>
            </div>

            <!-- 升级原因 -->
            <div class="escalation-section">
              <h4>升级原因</h4>
              <div class="escalation-reason">
                {{ task.escalationReason }}
              </div>
            </div>

            <!-- 审核历史 -->
            <div class="history-section">
              <h4>审核历史</h4>
              <div class="audit-timeline">
                <div class="timeline-item">
                  <div class="timeline-dot auto"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">自动审核</div>
                    <div class="timeline-desc">{{ task.autoAuditResult }}</div>
                    <div class="timeline-time">{{ formatDateTime(task.autoAuditTime) }}</div>
                  </div>
                </div>
                <div class="timeline-item">
                  <div class="timeline-dot manual"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">人工审核</div>
                    <div class="timeline-desc">{{ task.manualAuditResult }}</div>
                    <div class="timeline-time">{{ formatDateTime(task.manualAuditTime) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-actions">
            <template v-if="task.status === 'pending'">
              <button @click="claimReviewTask(task)" class="action-btn claim-btn">
                认领复审
              </button>
            </template>
            <template v-else-if="task.status === 'in_progress' && task.reviewerId === currentReviewerId">
              <button @click="processReview(task)" class="action-btn process-btn">
                进行复审
              </button>
            </template>
            <template v-else>
              <button @click="viewReviewDetail(task)" class="action-btn view-btn">
                查看详情
              </button>
            </template>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.current }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
        </span>
        <button
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 复审处理弹窗 -->
    <div v-if="showReviewModal" class="modal-overlay" @click="closeReviewModal">
      <div class="modal-content review-modal" @click.stop>
        <div class="modal-header">
          <h3>二度复审处理 - 任务 #{{ selectedTask?.taskId }}</h3>
          <button @click="closeReviewModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <!-- 完整商品信息 -->
          <div class="section">
            <h4>商品完整信息</h4>
            <div class="complete-product-info">
              <div class="product-images">
                <img
                  v-for="(image, index) in parseProductImages(selectedTask?.productImages)"
                  :key="index"
                  :src="image"
                  alt="商品图片"
                  class="product-image"
                />
              </div>
              <div class="product-details">
                <div class="detail-row">
                  <label>商品标题：</label>
                  <span>{{ selectedTask?.productTitle }}</span>
                </div>
                <div class="detail-row">
                  <label>商品描述：</label>
                  <span>{{ selectedTask?.productDescription }}</span>
                </div>
                <div class="detail-row">
                  <label>商品价格：</label>
                  <span class="price">¥{{ selectedTask?.productPrice }}</span>
                </div>
                <div class="detail-row">
                  <label>商品分类：</label>
                  <span>{{ selectedTask?.categoryName }}</span>
                </div>
                <div class="detail-row">
                  <label>商品状态：</label>
                  <span>{{ selectedTask?.productCondition }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 完整审核材料 -->
          <div class="section">
            <div class="audit-materials">
              <div class="material-item">
                <h5>升级原因</h5>
                <div class="escalation-detail">{{ selectedTask?.escalationReason }}</div>
              </div>
            </div>
          </div>

          <!-- 补充材料 -->
          <div class="section">
            <h4>补充材料 ({{ materialRequests.length }})</h4>
            <div v-if="materialRequests.length === 0" style="color: #999; font-style: italic; padding: 10px;">
              暂无补充材料请求
            </div>
            <div v-if="materialRequests.length > 0" class="material-requests">
              <div
                v-for="request in materialRequests"
                :key="request.requestId"
                class="material-request-item"
              >
                <div class="request-header">
                  <span class="request-status" :class="request.status">
                    {{ getRequestStatusText(request.status) }}
                  </span>
                </div>
                <div class="request-reason">
                  <strong>请求原因：</strong>{{ request.requestReason }}
                </div>
                <div class="required-materials">
                  <strong>需要材料：</strong>
                  <ul>
                    <li v-for="material in parseRequiredMaterials(request.requiredMaterials)" :key="material">
                      {{ material }}
                    </li>
                  </ul>
                </div>

                <!-- 卖家提交的材料 -->
                <div v-if="request.materials && request.materials.length > 0" class="submitted-materials">
                  <strong>已提交材料：</strong>
                  <div class="materials-list">
                    <div
                      v-for="material in request.materials"
                      :key="material.materialId"
                      class="material-item"
                    >
                      <div class="material-header">
                        <span class="material-type">{{ material.materialType }}</span>
                        <span class="material-time">{{ formatDateTime(material.submitTime) }}</span>
                      </div>
                      <div class="material-description">{{ material.description }}</div>
                      <div class="material-files">
                        <a
                          v-for="(url, index) in parseMaterialUrls(material.materialUrls)"
                          :key="index"
                          :href="url"
                          target="_blank"
                          class="material-file-link"
                        >
                          文件{{ index + 1 }}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 无材料提示 -->
                <div v-else class="no-materials">
                  <span class="text-muted">卖家暂未提交材料</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 二度复审决策 -->
          <div class="section">
            <h4>二度复审决策</h4>
            <div class="review-decision-form">
              <div class="form-item">
                <label>最终决策：</label>
                <select v-model="reviewDecision.decision" class="form-select">
                  <option value="">请选择</option>
                  <option value="approved">最终通过</option>
                  <option value="rejected">最终拒绝</option>
                  <option value="request_materials">要求补充材料</option>
                </select>
              </div>
              <div class="form-item">
                <label>复审意见：</label>
                <textarea
                  v-model="reviewDecision.comments"
                  placeholder="请输入详细的复审意见..."
                  class="form-textarea"
                  rows="5"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeReviewModal" class="cancel-btn">取消</button>
          <!-- 只有当前复审员认领的进行中任务才能提交复审结果 -->
          <button
            v-if="selectedTask?.status === 'in_progress' && selectedTask?.reviewerId === currentReviewerId"
            @click="submitReviewDecision"
            class="submit-btn"
            :disabled="!reviewDecision.decision || !reviewDecision.comments"
          >
            提交复审结果
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch, computed } from 'vue'
import { secondReviewApi } from '@/admin/api/secondReview'
import { useAdminStore } from '@/admin/stores/admin'
import manualAuditApi from '@/admin/api/manualAudit'

// 使用admin store
const adminStore = useAdminStore()

// 响应式数据
const reviewTasks = ref([])
const showReviewModal = ref(false)
const selectedTask = ref(null)
// 当前复审员ID，从store获取
const currentReviewerId = computed(() => {
  return adminStore.adminInfo?.userId || null
})

// 统计数据
const stats = reactive({
  pending: 0,
  in_progress: 0,
  approved: 0,
  rejected: 0
})

// 添加调试信息
console.log('初始化stats对象:', stats)

// 防止重复调用的标志
let isLoadingStats = false

// 监控stats变化
watch(stats, (newStats, oldStats) => {
  console.log('stats发生变化:', {
    old: oldStats,
    new: newStats,
    stack: new Error().stack
  })
}, { deep: true })

// 过滤有效的复审任务
const validReviewTasks = computed(() => {
  return reviewTasks.value.filter(task => task && task.taskId)
})

// 补充材料相关
const materialRequests = ref([])
const supplementaryMaterials = ref([])
const showMaterialsModal = ref(false)

// 筛选表单
const filterForm = reactive({
  status: '',
  escalationReason: '',
  productId: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 4,
  total: 0
})

// 复审决策表单
const reviewDecision = reactive({
  decision: '',
  comments: '',
  riskLevel: ''
})

// 页面加载时获取数据
onMounted(async () => {
  console.log('onMounted 被调用')

  // 确保获取管理员信息
  if (!adminStore.adminInfo) {
    try {
      await adminStore.fetchAdminInfo()
      console.log('获取管理员信息成功:', adminStore.adminInfo)
    } catch (error) {
      console.error('获取管理员信息失败:', error)
    }
  }

  console.log('当前复审员ID:', currentReviewerId.value)

  fetchReviewTasks()
  fetchStats()
  console.log('onMounted 完成')
})

// 获取复审任务
const fetchReviewTasks = async () => {
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...(filterForm.status && { status: filterForm.status }),
      ...(filterForm.escalationReason && { escalationReason: filterForm.escalationReason }),
      ...(filterForm.productId && { productId: filterForm.productId })
    }

    const result = await secondReviewApi.getTasks(params)

    if (result.success) {
      // 过滤掉null值
      const tasks = (result.data.records || []).filter(task => task && task.taskId)
      reviewTasks.value = tasks
      pagination.total = result.data.total || 0
      console.log('获取到的任务数据:', tasks)
    } else {
      console.error('获取复审任务失败:', result.message)
      reviewTasks.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取复审任务失败:', error)
    reviewTasks.value = []
    pagination.total = 0
  }
}

// 获取统计数据
const fetchStats = async () => {
  if (isLoadingStats) {
    console.log('fetchStats 已在执行中，跳过重复调用')
    return
  }

  isLoadingStats = true
  const timestamp = new Date().toISOString()
  try {
    console.log(`[${timestamp}] 开始获取统计数据...`)
    const result = await secondReviewApi.getStats()
    console.log(`[${timestamp}] 统计数据API响应:`, result)

    if (result.success) {
      console.log(`[${timestamp}] 更新前的stats:`, { ...stats })
      console.log(`[${timestamp}] API返回的数据:`, result.data)

      // 使用Object.assign确保响应式更新
      Object.assign(stats, {
        pending: result.data.pending || 0,
        in_progress: result.data.in_progress || 0,
        approved: result.data.approved || 0,
        rejected: result.data.rejected || 0
      })

      console.log(`[${timestamp}] 更新后的stats:`, { ...stats })

      // 强制触发响应式更新
      await nextTick()
      console.log(`[${timestamp}] nextTick后的stats:`, { ...stats })
    } else {
      console.error(`[${timestamp}] 获取统计数据失败:`, result.message)
    }
  } catch (error) {
    console.error(`[${timestamp}] 获取统计数据失败:`, error)
  } finally {
    isLoadingStats = false
    console.log(`[${timestamp}] fetchStats 执行完成，重置标志`)
  }
}

// 筛选
const handleFilter = () => {
  pagination.current = 1
  fetchReviewTasks()
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    escalationReason: '',
    productId: ''
  })
  pagination.current = 1
  fetchReviewTasks()
}

// 刷新任务
const refreshTasks = () => {
  fetchReviewTasks()
  fetchStats()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= Math.ceil(pagination.total / pagination.pageSize)) {
    pagination.current = page
    fetchReviewTasks()
  }
}

// 认领复审任务
const claimReviewTask = async (task) => {
  if (!currentReviewerId.value) {
    console.error('无法获取当前用户ID')
    return
  }

  try {
    const result = await secondReviewApi.claimTask(task.taskId, currentReviewerId.value)

    if (result.success) {
      // 更新任务状态
      task.status = 'in_progress'
      task.reviewerId = currentReviewerId.value
      task.claimedTime = new Date().toISOString()

      console.log('复审任务认领成功')
      fetchStats()
    } else {
      console.error('认领复审任务失败:', result.message)
    }
  } catch (error) {
    console.error('认领复审任务失败:', error)
  }
}

// 进行复审处理
const processReview = async (task) => {
  try {
    // 获取任务详情
    const result = await secondReviewApi.getTaskDetail(task.taskId)

    if (result.success) {
      selectedTask.value = result.data
      showReviewModal.value = true

      // 重置复审决策表单
      reviewDecision.decision = ''
      reviewDecision.comments = ''
      reviewDecision.riskLevel = ''

      // 获取补充材料
      await fetchMaterialRequests(result.data.productId)
    } else {
      console.error('获取任务详情失败:', result.message)
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
  }
}

// 查看复审详情
const viewReviewDetail = async (task) => {
  try {
    // 获取任务详情
    const result = await secondReviewApi.getTaskDetail(task.taskId)

    if (result.success) {
      selectedTask.value = result.data
      showReviewModal.value = true

      // 如果是已完成的任务，显示之前的决策
      if (task.status === 'completed') {
        reviewDecision.decision = result.data.finalDecision || ''
        reviewDecision.comments = result.data.reviewComments || ''
        reviewDecision.riskLevel = result.data.riskLevel || ''
      }
    } else {
      console.error('获取任务详情失败:', result.message)
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
  }
}

// 关闭复审弹窗
const closeReviewModal = () => {
  showReviewModal.value = false
  selectedTask.value = null
}

// 提交复审决策
const submitReviewDecision = async () => {
  if (!currentReviewerId.value) {
    console.error('无法获取当前用户ID')
    return
  }

  try {
    const decision = {
      reviewerId: currentReviewerId.value,
      decision: reviewDecision.decision,
      comments: reviewDecision.comments,
      riskLevel: reviewDecision.riskLevel
    }

    const result = await secondReviewApi.submitDecision(selectedTask.value.taskId, decision)

    if (result.success) {
      console.log('复审决策提交成功')
      closeReviewModal()
      refreshTasks()
    } else {
      console.error('提交复审决策失败:', result.message)
    }
  } catch (error) {
    console.error('提交复审决策失败:', error)
  }
}

// 工具函数
const getTaskCardClass = (task) => {
  const classes = []
  if (task.status === 'in_progress' && task.reviewerId === currentReviewerId.value) {
    classes.push('my-task')
  }
  if (isOverdue(task.deadline)) {
    classes.push('overdue')
  }
  return classes.join(' ')
}

const getStatusClass = (status) => {
  const classMap = {
    'pending': 'status-pending',
    'in_progress': 'status-processing',
    'completed': 'status-completed',
    'material_requested': 'status-material_requested'
  }
  return classMap[status] || ''
}

const getStatusText = (status) => {
  const textMap = {
    'pending': '待复审',
    'in_progress': '复审中',
    'completed': '已完成'
  }
  return textMap[status] || '未知'
}

const getEscalationReasonText = (reason) => {
  if (reason.includes('价格')) return '价格风险'
  if (reason.includes('信用分')) return '信用风险'
  if (reason.includes('人工')) return '人工升级'
  return '其他原因'
}

const getCreditScoreClass = (score) => {
  if (score >= 85) return 'credit-high'
  if (score >= 50) return 'credit-medium'
  return 'credit-low'
}

const isOverdue = (deadline) => {
  return new Date(deadline) < new Date()
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const parseProductImages = (images) => {
  if (!images) return []
  return images.split(',').map(img => {
    const trimmedImg = img.trim()
    // 如果已经是完整URL，直接返回；否则添加/uploads/前缀
    if (trimmedImg.startsWith('http://') || trimmedImg.startsWith('https://')) {
      return trimmedImg
    }
    return `/uploads/${trimmedImg}`
  })
}

const formatAuditMaterial = (material) => {
  if (!material) return '无数据'
  try {
    return JSON.stringify(material, null, 2)
  } catch {
    return material.toString()
  }
}

// 获取材料请求
const fetchMaterialRequests = async (productId) => {
  try {
    const result = await manualAuditApi.getMaterialRequests(productId)
    if (result.success) {
      materialRequests.value = result.data || []

      // 获取每个请求的补充材料
      for (const request of materialRequests.value) {
        const materialsResult = await manualAuditApi.getSupplementaryMaterials(request.requestId)
        if (materialsResult.success) {
          request.materials = materialsResult.data || []
        }
      }
    }
  } catch (error) {
    console.error('获取材料请求失败:', error)
    materialRequests.value = []
  }
}

// 查看补充材料
const viewMaterials = () => {
  showMaterialsModal.value = true
}

// 关闭材料弹窗
const closeMaterialsModal = () => {
  showMaterialsModal.value = false
}

// 解析需要的材料
const parseRequiredMaterials = (materials) => {
  if (!materials) return []
  try {
    return JSON.parse(materials)
  } catch (e) {
    return [materials]
  }
}

// 解析材料文件URL
const parseMaterialUrls = (urls) => {
  if (!urls) return []
  try {
    return JSON.parse(urls)
  } catch (e) {
    return [urls]
  }
}

// 获取请求状态文本
const getRequestStatusText = (status) => {
  const textMap = {
    'waiting': '等待提交',
    'submitted': '已提交',
    'approved': '已批准',
    'rejected': '已拒绝'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped>
.second-review-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #722ed1;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-item label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-input, .form-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn {
  background: #722ed1;
  color: white;
}

.reset-btn {
  background: #f5f5f5;
  color: #333;
  margin-left: 8px;
}

.review-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-count {
  color: #666;
  font-size: 14px;
}

.refresh-btn {
  padding: 6px 12px;
  background: #f5f5f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.review-cards {
  padding: 20px;
  display: grid;
  gap: 20px;
}

.review-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;
}

.review-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.review-card.my-task {
  border-color: #722ed1;
  background: #f9f0ff;
}

.review-card.overdue {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-info {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.task-id {
  font-weight: 500;
  color: #333;
}

.status-badge, .escalation-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background: #f0f0f0;
  color: #666;
}

.status-processing {
  background: #f9f0ff;
  color: #722ed1;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.escalation-badge {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.task-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.deadline.overdue {
  color: #ff4d4f;
  font-weight: 500;
}

.card-content {
  margin-bottom: 16px;
}

.product-section, .escalation-section, .history-section {
  margin-bottom: 16px;
}

.product-section h4, .escalation-section h4, .history-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.product-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.info-item label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.price {
  color: #ff4d4f;
  font-weight: 500;
}

.credit-score {
  font-weight: 500;
}

.credit-high { color: #52c41a; }
.credit-medium { color: #faad14; }
.credit-low { color: #ff4d4f; }

.escalation-reason {
  background: #fff7e6;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.audit-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 4px;
  flex-shrink: 0;
}

.timeline-dot.auto {
  background: #1890ff;
}

.timeline-dot.manual {
  background: #faad14;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-time {
  color: #999;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.claim-btn {
  background: #722ed1;
  color: white;
}

.process-btn {
  background: #52c41a;
  color: white;
}

.view-btn {
  background: #f5f5f5;
  color: #333;
}

.pagination {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

.section {
  margin-bottom: 24px;
}

.section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.complete-product-info {
  display: flex;
  gap: 20px;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.product-details, .seller-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  gap: 12px;
  font-size: 14px;
}

.detail-row label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.audit-materials {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.material-item h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.material-item pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.escalation-detail {
  background: #fff7e6;
  padding: 12px;
  border-radius: 4px;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.review-decision-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn, .submit-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #333;
}

.submit-btn {
  background: #722ed1;
  color: white;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 补充材料样式 */
.material-requests {
  margin-top: 10px;
}

.material-request-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.request-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.request-status.waiting {
  background: #fff3cd;
  color: #856404;
}

.request-status.submitted {
  background: #d1ecf1;
  color: #0c5460;
}

.request-status.approved {
  background: #d4edda;
  color: #155724;
}

.request-status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.request-reason {
  margin-bottom: 10px;
  line-height: 1.5;
}

.required-materials ul {
  margin: 5px 0 0 20px;
  padding: 0;
}

.required-materials li {
  margin-bottom: 3px;
}

.submitted-materials {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.materials-list {
  margin-top: 10px;
}

.material-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 10px;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.material-type {
  font-weight: bold;
  color: #333;
}

.material-time {
  font-size: 12px;
  color: #666;
}

.material-description {
  margin-bottom: 8px;
  color: #555;
  line-height: 1.4;
}

.material-files {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.material-file-link {
  display: inline-block;
  padding: 4px 8px;
  background: #f0f0f0;
  color: #333;
  text-decoration: none;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.2s;
}

.material-file-link:hover {
  background: #007bff;
  color: white;
}

.no-materials {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}
</style>
