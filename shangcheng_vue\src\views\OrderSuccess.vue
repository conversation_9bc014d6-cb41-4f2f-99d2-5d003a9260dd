<template>
  <div class="order-success-page">
    <!-- 顶部导航栏 -->
    <nav class="top-nav">
      <div class="container nav-content">
        <router-link to="/home" class="logo" style="color: #FF0000;">易转</router-link>
        <div class="nav-right">
          <router-link to="/profile" class="user-avatar" title="个人中心">
            <img :src="userInfo?.avatarUrl || defaultAvatar" alt="用户头像" class="avatar-image">
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container main-content">
      <div class="success-card">
        <div class="success-icon">
          <i class="el-icon-check"></i>
          <span class="success-icon-circle"></span>
        </div>
        <h1 class="success-title">支付成功</h1>
        <p class="success-message">您的订单已支付成功，感谢您的购买！</p>

        <div class="order-info" v-if="orderInfo">
          <h2>订单信息</h2>
          <div class="info-item">
            <span class="label">订单编号：</span>
            <span class="value">{{ orderInfo.orderId }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品名称：</span>
            <span class="value">{{ orderInfo.productTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">支付金额：</span>
            <span class="value price">¥{{ orderInfo.totalAmount }}</span>
          </div>
          <div class="info-item">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatDate(orderInfo.orderDate) }}</span>
          </div>
        </div>

        <div class="loading-state" v-else-if="loading">
          <div class="spinner"></div>
          <p>正在加载订单信息...</p>
        </div>

        <div class="error-message" v-else-if="error">
          <p>{{ error }}</p>
        </div>

        <div class="action-buttons">
          <router-link to="/profile" class="btn view-orders-btn">查看我的订单</router-link>
          <router-link to="/home" class="btn continue-shopping-btn">继续购物</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { getOrderById } from '@/api/order';
import { ElMessage } from 'element-plus';

const route = useRoute();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 默认头像
const defaultAvatar = computed(() => {
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100"><rect width="100" height="100" fill="%23FF0000"/><circle cx="50" cy="35" r="20" fill="%23FFFFFF"/><path d="M50,65 C30,65 15,80 15,100 L85,100 C85,80 70,65 50,65 Z" fill="%23FFFFFF"/></svg>`;
});

// 订单信息
const orderInfo = ref(null);
const loading = ref(false);
const error = ref('');

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取订单信息
const fetchOrderInfo = async (orderId) => {
  loading.value = true;
  error.value = '';

  try {
    const response = await getOrderById(orderId);
    if (response && response.code === 200 && response.data) {
      orderInfo.value = response.data;
    } else {
      error.value = '获取订单信息失败';
      ElMessage.error('获取订单信息失败');
    }
  } catch (err) {
    console.error('获取订单详情失败:', err);
    error.value = '获取订单信息失败，请稍后重试';
    ElMessage.error('获取订单信息失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 从URL参数中获取订单ID
  const orderId = route.query.orderId;
  if (orderId) {
    fetchOrderInfo(orderId);
  } else {
    error.value = '未找到订单信息';
  }
});
</script>

<style scoped>
.order-success-page {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  --primary-color: #FF4D4F;
  --secondary-color: #FF7875;
  --success-color: #52c41a;
  --text-color-dark: #333;
  --text-color-light: #666;
  --bg-color: #F5F5F5;
  --white: #FFFFFF;
  --border-color: #EFEFEF;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 顶部导航 */
.top-nav {
  background: var(--white);
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 36px;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.nav-right {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
  transition: transform 0.2s ease;
}

.avatar-image:hover {
  transform: scale(1.1);
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.success-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  text-align: center;
}

.success-icon {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
}

.success-icon-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(82, 196, 26, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon i {
  font-size: 40px;
  color: var(--success-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.success-icon::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: var(--success-color);
}

.success-title {
  font-size: 24px;
  color: var(--success-color);
  margin-bottom: 10px;
}

.success-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
}

.order-info {
  text-align: left;
  border-top: 1px solid #eee;
  padding-top: 20px;
  margin-bottom: 30px;
}

.order-info h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.label {
  width: 100px;
  color: #666;
}

.value {
  flex: 1;
  color: #333;
}

.value.price {
  color: var(--primary-color);
  font-weight: bold;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.btn {
  padding: 10px 25px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s;
}

.view-orders-btn {
  background-color: var(--primary-color);
  color: white;
}

.view-orders-btn:hover {
  background-color: var(--secondary-color);
}

.continue-shopping-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.continue-shopping-btn:hover {
  background-color: #e9e9e9;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: var(--primary-color);
  padding: 20px 0;
}
</style>
