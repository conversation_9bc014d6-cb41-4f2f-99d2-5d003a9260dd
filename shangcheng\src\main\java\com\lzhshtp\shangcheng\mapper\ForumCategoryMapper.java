package com.lzhshtp.shangcheng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lzhshtp.shangcheng.model.ForumCategory;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 论坛分类数据访问层接口
 */
public interface ForumCategoryMapper extends BaseMapper<ForumCategory> {

    /**
     * 查询所有论坛分类
     *
     * @return 论坛分类列表
     */
    @Select("SELECT * FROM tb_lzhshtp_forum_categories ORDER BY lzhshtp_forum_category_id ASC")
    @Results({
        @Result(property = "forumCategoryId", column = "lzhshtp_forum_category_id"),
        @Result(property = "categoryName", column = "lzhshtp_category_name"),
        @Result(property = "description", column = "lzhshtp_description")
    })
    List<ForumCategory> findAllCategories();

    /**
     * 根据分类名称查询论坛分类
     *
     * @param categoryName 分类名称
     * @return 论坛分类对象
     */
    @Select("SELECT * FROM tb_lzhshtp_forum_categories WHERE lzhshtp_category_name = #{categoryName}")
    @Results({
        @Result(property = "forumCategoryId", column = "lzhshtp_forum_category_id"),
        @Result(property = "categoryName", column = "lzhshtp_category_name"),
        @Result(property = "description", column = "lzhshtp_description")
    })
    ForumCategory findByCategoryName(String categoryName);
} 