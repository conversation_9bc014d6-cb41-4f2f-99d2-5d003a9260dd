package com.lzhshtp.shangcheng.controller.audit;

import com.lzhshtp.shangcheng.dto.ApiResponse;
import com.lzhshtp.shangcheng.dto.PageResult;
import com.lzhshtp.shangcheng.dto.audit.ManualAuditDecisionRequest;
import com.lzhshtp.shangcheng.model.ManualAuditTask;
import com.lzhshtp.shangcheng.service.audit.ManualAuditTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 人工审核任务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/audit/manual")
@CrossOrigin(origins = "*")
public class ManualAuditTaskController {

    @Autowired
    private ManualAuditTaskService manualAuditTaskService;

    /**
     * 获取人工审核任务列表
     */
    @GetMapping("/tasks")
    public ApiResponse<PageResult<ManualAuditTask>> getManualAuditTasks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "6") Integer pageSize,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Integer priority,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) Long adminId) {

        try {
            PageResult<ManualAuditTask> result = manualAuditTaskService.getManualAuditTasks(
                page, pageSize, status, priority, productId, adminId);

            return ApiResponse.success("获取人工审核任务成功", result);

        } catch (Exception e) {
            log.error("获取人工审核任务失败", e);
            return ApiResponse.fail("获取人工审核任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取人工审核统计数据
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getManualAuditStats() {
        try {
            Map<String, Object> stats = manualAuditTaskService.getManualAuditStats();
            return ApiResponse.success("获取统计数据成功", stats);

        } catch (Exception e) {
            log.error("获取人工审核统计数据失败", e);
            return ApiResponse.fail("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 认领审核任务
     */
    @PostMapping("/tasks/{taskId}/claim")
    public ApiResponse<String> claimAuditTask(
            @PathVariable Long taskId,
            @RequestBody Map<String, Long> request) {

        try {
            Long adminId = request.get("adminId");
            boolean success = manualAuditTaskService.claimAuditTask(taskId, adminId);

            if (success) {
                return ApiResponse.success("任务认领成功");
            } else {
                return ApiResponse.fail("任务认领失败，可能已被其他管理员认领");
            }

        } catch (Exception e) {
            log.error("认领审核任务失败，taskId: {}", taskId, e);
            return ApiResponse.fail("认领任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取审核任务详情
     */
    @GetMapping("/tasks/{taskId}")
    public ApiResponse<ManualAuditTask> getAuditTaskDetail(@PathVariable Long taskId) {
        try {
            ManualAuditTask task = manualAuditTaskService.getAuditTaskDetail(taskId);

            if (task == null) {
                return ApiResponse.fail("审核任务不存在");
            }

            return ApiResponse.success("获取任务详情成功", task);

        } catch (Exception e) {
            log.error("获取审核任务详情失败，taskId: {}", taskId, e);
            return ApiResponse.fail("获取任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 提交人工审核决策
     */
    @PostMapping("/tasks/{taskId}/decision")
    public ApiResponse<String> submitManualAuditDecision(
            @PathVariable Long taskId,
            @RequestBody ManualAuditDecisionRequest request) {

        try {
            manualAuditTaskService.submitManualAuditDecision(taskId, request);
            return ApiResponse.success("审核决策提交成功");

        } catch (Exception e) {
            log.error("提交审核决策失败，taskId: {}", taskId, e);
            return ApiResponse.fail("提交审核决策失败：" + e.getMessage());
        }
    }

    /**
     * 获取审核任务的完整材料
     */
    @GetMapping("/tasks/{taskId}/materials")
    public ApiResponse<Map<String, Object>> getAuditTaskMaterials(@PathVariable Long taskId) {
        try {
            Map<String, Object> materials = manualAuditTaskService.getAuditTaskMaterials(taskId);
            return ApiResponse.success("获取审核材料成功", materials);

        } catch (Exception e) {
            log.error("获取审核材料失败，taskId: {}", taskId, e);
            return ApiResponse.fail("获取审核材料失败：" + e.getMessage());
        }
    }
}
