<template>
  <div class="refund-management">
    <header class="header">
      <h1>退款管理</h1>
      <div class="nav-actions">
        <router-link to="/orders">返回订单</router-link>
        <router-link to="/home">返回首页</router-link>
      </div>
    </header>

    <div class="tabs">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        :class="{ active: activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </button>
    </div>

    <div class="refund-list">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="refunds.length === 0" class="empty">暂无退款记录</div>
      <div v-else>
        <div v-for="refund in refunds" :key="refund.refundId" class="refund-item">
          <div class="refund-header">
            <span class="refund-id">退款单号：{{ refund.refundId }}</span>
            <span class="refund-status" :class="getStatusClass(refund.status)">
              {{ getStatusText(refund.status) }}
            </span>
          </div>

          <div class="refund-content">
            <div class="refund-info">
              <h3>{{ refund.productTitle }}</h3>
              <p class="refund-amount">退款金额：¥{{ refund.refundAmount }}</p>
              <p class="refund-type">退款类型：{{ getRefundTypeText(refund.refundType) }}</p>
              <p class="refund-reason">退款原因：{{ refund.refundReason }}</p>
              <p class="refund-date">申请时间：{{ formatDate(refund.createdTime) }}</p>
            </div>

            <div class="refund-actions">
              <!-- 买家操作 -->
              <template v-if="activeTab === 'buyer'">
                <button
                  v-if="refund.status === 'seller_rejected'"
                  @click="requestAdminIntervention(refund)"
                  class="btn-warning"
                >
                  申请管理员介入
                </button>
              </template>

              <!-- 卖家操作 -->
              <template v-if="activeTab === 'seller'">
                <button
                  v-if="refund.status === 'pending_seller'"
                  @click="handleRefund(refund, true)"
                  class="btn-success"
                >
                  同意退款
                </button>
                <button
                  v-if="refund.status === 'pending_seller'"
                  @click="showRejectDialog(refund)"
                  class="btn-danger"
                >
                  拒绝退款
                </button>
              </template>

              <!-- 管理员操作 -->
              <template v-if="activeTab === 'admin'">
                <button
                  v-if="refund.status === 'pending_admin'"
                  @click="handleAdminRefund(refund, true)"
                  class="btn-success"
                >
                  同意强制退款
                </button>
                <button
                  v-if="refund.status === 'pending_admin'"
                  @click="showAdminRejectDialog(refund)"
                  class="btn-danger"
                >
                  拒绝退款
                </button>
              </template>
            </div>
          </div>

          <!-- 响应信息 -->
          <div v-if="refund.sellerResponse || refund.adminResponse" class="response-info">
            <div v-if="refund.sellerResponse" class="seller-response">
              <strong>商家回复：</strong>{{ refund.sellerResponse }}
            </div>
            <div v-if="refund.adminResponse" class="admin-response">
              <strong>管理员回复：</strong>{{ refund.adminResponse }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拒绝退款对话框 -->
    <div v-if="showReject" class="modal-overlay" @click="closeRejectDialog">
      <div class="modal-content" @click.stop>
        <h3>拒绝退款</h3>
        <form @submit.prevent="submitReject">
          <div class="form-group">
            <label>拒绝理由</label>
            <textarea
              v-model="rejectReason"
              rows="4"
              required
              placeholder="请说明拒绝退款的理由"
            ></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" :disabled="submitting" class="btn-danger">
              {{ submitting ? '提交中...' : '确认拒绝' }}
            </button>
            <button type="button" @click="closeRejectDialog" class="btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 管理员介入对话框 -->
    <div v-if="showIntervention" class="modal-overlay" @click="closeInterventionDialog">
      <div class="modal-content" @click.stop>
        <h3>申请管理员介入</h3>
        <form @submit.prevent="submitIntervention">
          <div class="form-group">
            <label>补充证据</label>
            <textarea
              v-model="interventionEvidence"
              rows="4"
              required
              placeholder="请提供更多证据和说明"
            ></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" :disabled="submitting" class="btn-warning">
              {{ submitting ? '提交中...' : '提交申请' }}
            </button>
            <button type="button" @click="closeInterventionDialog" class="btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import {
  getRefundList,
  handleSellerRefund,
  handleAdminRefund,
  requestAdminIntervention
} from '@/api/refund';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('buyer');
const refunds = ref([]);
const showReject = ref(false);
const showIntervention = ref(false);
const selectedRefund = ref(null);
const rejectReason = ref('');
const interventionEvidence = ref('');

const tabs = ref([
  { key: 'buyer', label: '我的退款申请' },
  { key: 'seller', label: '待处理退款' }
]);

// 如果是管理员，添加管理员标签
if (userStore.userInfo?.role === 'admin') {
  tabs.value.push({ key: 'admin', label: '管理员审核' });
}

// 加载退款列表
const loadRefunds = async () => {
  loading.value = true;
  try {
    const response = await getRefundList(activeTab.value);
    refunds.value = response.data || [];
  } catch (error) {
    console.error('加载退款列表失败:', error);
    alert('加载退款列表失败');
  } finally {
    loading.value = false;
  }
};

// 切换标签
const switchTab = (tab) => {
  activeTab.value = tab;
  loadRefunds();
};

// 处理退款（商家）
const handleRefund = async (refund, approved) => {
  try {
    await handleSellerRefund({
      refundId: refund.refundId,
      approved,
      response: approved ? '同意退款' : rejectReason.value
    });
    alert(approved ? '已同意退款' : '已拒绝退款');
    loadRefunds();
  } catch (error) {
    console.error('处理退款失败:', error);
    alert('处理退款失败：' + (error.response?.data?.message || error.message));
  }
};

// 显示拒绝对话框
const showRejectDialog = (refund) => {
  selectedRefund.value = refund;
  rejectReason.value = '';
  showReject.value = true;
};

// 关闭拒绝对话框
const closeRejectDialog = () => {
  showReject.value = false;
  selectedRefund.value = null;
  rejectReason.value = '';
};

// 提交拒绝
const submitReject = async () => {
  submitting.value = true;
  try {
    await handleRefund(selectedRefund.value, false);
    closeRejectDialog();
  } catch (error) {
    console.error('拒绝退款失败:', error);
    alert('拒绝退款失败：' + (error.response?.data?.message || error.message));
  } finally {
    submitting.value = false;
  }
};

// 申请管理员介入
const requestAdminIntervention = (refund) => {
  selectedRefund.value = refund;
  interventionEvidence.value = '';
  showIntervention.value = true;
};

// 关闭管理员介入对话框
const closeInterventionDialog = () => {
  showIntervention.value = false;
  selectedRefund.value = null;
  interventionEvidence.value = '';
};

// 提交管理员介入申请
const submitIntervention = async () => {
  submitting.value = true;
  try {
    await requestAdminIntervention(selectedRefund.value.refundId, interventionEvidence.value);
    alert('已申请管理员介入');
    closeInterventionDialog();
    loadRefunds();
  } catch (error) {
    console.error('申请管理员介入失败:', error);
    alert('申请管理员介入失败：' + (error.response?.data?.message || error.message));
  } finally {
    submitting.value = false;
  }
};

// 管理员处理退款
const handleAdminRefund = async (refund, approved) => {
  const response = approved ? '管理员同意强制退款' : '管理员拒绝退款申请';
  try {
    await handleAdminRefund({
      refundId: refund.refundId,
      approved,
      response
    });
    alert(approved ? '已同意强制退款' : '已拒绝退款申请');
    loadRefunds();
  } catch (error) {
    console.error('管理员处理退款失败:', error);
    alert('处理失败：' + (error.response?.data?.message || error.message));
  }
};

// 显示管理员拒绝对话框
const showAdminRejectDialog = (refund) => {
  selectedRefund.value = refund;
  rejectReason.value = '';
  showReject.value = true;
};

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    'pending_seller': 'status-pending',
    'seller_approved': 'status-approved',
    'seller_rejected': 'status-rejected',
    'pending_admin': 'status-admin',
    'admin_approved': 'status-approved',
    'admin_rejected': 'status-rejected',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  };
  return statusMap[status] || '';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending_seller': '等待商家处理',
    'seller_approved': '商家已同意',
    'seller_rejected': '商家已拒绝',
    'pending_admin': '等待管理员审核',
    'admin_approved': '管理员已同意',
    'admin_rejected': '管理员已拒绝',
    'completed': '退款完成',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

// 获取退款类型文本
const getRefundTypeText = (type) => {
  return type === 'refund_only' ? '仅退款' : '退货退款';
};

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString();
};

onMounted(() => {
  loadRefunds();
});
</script>

<style scoped>
.refund-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.nav-actions a {
  margin-left: 10px;
  padding: 8px 15px;
  background-color: #2196F3;
  color: white;
  text-decoration: none;
  border-radius: 4px;
}

.tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tabs button {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tabs button.active {
  color: #2196F3;
  border-bottom-color: #2196F3;
}

.refund-item {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.refund-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending { background-color: #ff9800; color: white; }
.status-approved { background-color: #4caf50; color: white; }
.status-rejected { background-color: #f44336; color: white; }
.status-admin { background-color: #9c27b0; color: white; }
.status-completed { background-color: #8bc34a; color: white; }
.status-cancelled { background-color: #607d8b; color: white; }

.refund-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.refund-info {
  flex: 1;
}

.refund-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.refund-amount {
  color: #f44336;
  font-weight: bold;
  font-size: 18px;
  margin: 5px 0;
}

.refund-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.refund-actions {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

.response-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.seller-response,
.admin-response {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

/* 按钮样式 */
.btn-success { background-color: #4caf50; color: white; }
.btn-danger { background-color: #f44336; color: white; }
.btn-warning { background-color: #ff9800; color: white; }
.btn-secondary { background-color: #9e9e9e; color: white; }

button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 5px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style>
