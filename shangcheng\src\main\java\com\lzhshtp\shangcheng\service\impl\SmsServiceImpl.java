package com.lzhshtp.shangcheng.service.impl;

import com.lzhshtp.shangcheng.config.SmsConfig;
import com.lzhshtp.shangcheng.service.SmsService;
import com.lzhshtp.shangcheng.util.HttpUtils;
import com.lzhshtp.shangcheng.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 短信服务实现类
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    
    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private RedisUtils redisUtils;
    
    // Redis Key前缀
    private static final String SMS_CODE_PREFIX = "sms:code:";
    private static final String SMS_LIMIT_PREFIX = "sms:limit:";
    private static final String SMS_DAILY_PREFIX = "sms:daily:";
    
    @Override
    public boolean sendVerificationCode(String phone, String type) {
        try {
            log.info("开始发送短信验证码，手机号：{}，类型：{}", phone, type);
            
            // 1. 检查发送限制
            if (!canSendCode(phone)) {
                log.warn("手机号 {} 发送验证码过于频繁", phone);
                return false;
            }
            
            // 2. 检查每日发送限制
            if (!checkDailyLimit(phone)) {
                log.warn("手机号 {} 今日发送次数已达上限", phone);
                return false;
            }
            
            // 3. 生成验证码
            String code = generateVerificationCode();
            log.info("生成验证码：{}", code);
            
            // 4. 调用阿里云短信API
            boolean sendResult = callSmsApi(phone, code);
            
            if (sendResult) {
                // 5. 存储验证码到Redis
                saveCodeToRedis(phone, code, type);
                
                // 6. 设置发送限制
                setSendLimit(phone);
                
                // 7. 增加每日发送计数
                increaseDailyCount(phone);
                
                log.info("短信验证码发送成功，手机号：{}", phone);
                return true;
            } else {
                log.error("短信验证码发送失败，手机号：{}", phone);
                return false;
            }
            
        } catch (Exception e) {
            log.error("发送短信验证码异常，手机号：{}", phone, e);
            return false;
        }
    }
    
    @Override
    public boolean verifyCode(String phone, String code, String type) {
        try {
            String key = SMS_CODE_PREFIX + type + ":" + phone;
            Object savedCodeObj = redisUtils.get(key);

            if (savedCodeObj == null) {
                log.warn("验证码不存在或已过期，手机号：{}，类型：{}", phone, type);
                return false;
            }

            String savedCode = savedCodeObj.toString();
            if (savedCode.equals(code)) {
                // 验证成功，删除验证码
                redisUtils.del(key);
                log.info("验证码验证成功，手机号：{}，类型：{}", phone, type);
                return true;
            } else {
                log.warn("验证码错误，手机号：{}，类型：{}，输入：{}，正确：{}", phone, type, code, savedCode);
                return false;
            }

        } catch (Exception e) {
            log.error("验证验证码异常，手机号：{}，类型：{}", phone, type, e);
            return false;
        }
    }
    
    @Override
    public boolean canSendCode(String phone) {
        String key = SMS_LIMIT_PREFIX + phone;
        return !redisUtils.hasKey(key);
    }

    @Override
    public long getRemainingWaitTime(String phone) {
        String key = SMS_LIMIT_PREFIX + phone;
        long expire = redisUtils.getExpire(key);
        return expire > 0 ? expire : 0;
    }
    
    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(999999));
    }
    
    /**
     * 调用阿里云短信API
     */
    private boolean callSmsApi(String phone, String code) {
        try {
            String host = smsConfig.getHost();
            String path = smsConfig.getPath();
            String method = "POST";
            String appcode = smsConfig.getAppCode();
            
            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "APPCODE " + appcode);
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            
            // 设置请求参数
            Map<String, String> querys = new HashMap<>();
            Map<String, String> bodys = new HashMap<>();
            bodys.put("content", "code:" + code);
            bodys.put("template_id", smsConfig.getTemplateId());
            bodys.put("phone_number", phone);
            
            log.info("调用短信API，手机号：{}，验证码：{}", phone, code);
            
            // 发送请求
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity, "UTF-8");
            
            log.info("短信API响应：{}", result);
            
            // 简单判断是否成功（根据实际API响应格式调整）
            return result.contains("\"status\":\"OK\"") || result.contains("success");
            
        } catch (Exception e) {
            log.error("调用短信API异常", e);
            return false;
        }
    }
    
    /**
     * 保存验证码到Redis
     */
    private void saveCodeToRedis(String phone, String code, String type) {
        String key = SMS_CODE_PREFIX + type + ":" + phone;
        long expireSeconds = smsConfig.getCodeExpireMinutes() * 60;
        redisUtils.set(key, code, expireSeconds);
        log.info("验证码已保存到Redis，key：{}，过期时间：{}分钟", key, smsConfig.getCodeExpireMinutes());
    }
    
    /**
     * 设置发送限制
     */
    private void setSendLimit(String phone) {
        String key = SMS_LIMIT_PREFIX + phone;
        long expireSeconds = smsConfig.getSendLimitMinutes() * 60;
        redisUtils.set(key, "1", expireSeconds);
        log.info("设置发送限制，key：{}，限制时间：{}分钟", key, smsConfig.getSendLimitMinutes());
    }
    
    /**
     * 检查每日发送限制
     */
    private boolean checkDailyLimit(String phone) {
        String key = SMS_DAILY_PREFIX + phone;
        Object countObj = redisUtils.get(key);
        int currentCount = countObj != null ? Integer.parseInt(countObj.toString()) : 0;
        return currentCount < smsConfig.getDailyLimit();
    }
    
    /**
     * 增加每日发送计数
     */
    private void increaseDailyCount(String phone) {
        String key = SMS_DAILY_PREFIX + phone;
        long count = redisUtils.incr(key, 1);
        if (count == 1) {
            // 第一次发送，设置过期时间为24小时
            redisUtils.expire(key, 24 * 60 * 60); // 24小时转换为秒
        }
        log.info("增加每日发送计数，手机号：{}，当前计数：{}", phone, count);
    }
}
