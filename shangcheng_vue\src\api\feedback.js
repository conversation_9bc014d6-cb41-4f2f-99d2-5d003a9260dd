import request from '@/utils/request'

/**
 * 提交用户反馈
 * @param {Object} data 反馈数据
 * @returns {Promise}
 */
export function submitFeedback(data) {
  return request({
    url: '/feedback',
    method: 'post',
    data
  })
}

/**
 * 获取用户反馈列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getFeedbackList(params) {
  return request({
    url: '/feedback',
    method: 'get',
    params
  })
}

/**
 * 获取反馈详情
 * @param {Number} feedbackId 反馈ID
 * @returns {Promise}
 */
export function getFeedbackDetail(feedbackId) {
  return request({
    url: `/feedback/${feedbackId}`,
    method: 'get'
  })
}

/**
 * 更新反馈状态（管理员功能）
 * @param {Number} feedbackId 反馈ID
 * @param {Object} data 状态更新数据
 * @returns {Promise}
 */
export function updateFeedbackStatus(feedbackId, data) {
  return request({
    url: `/feedback/${feedbackId}/status`,
    method: 'put',
    data
  })
}

/**
 * 删除反馈
 * @param {Number} feedbackId 反馈ID
 * @returns {Promise}
 */
export function deleteFeedback(feedbackId) {
  return request({
    url: `/feedback/${feedbackId}`,
    method: 'delete'
  })
}
