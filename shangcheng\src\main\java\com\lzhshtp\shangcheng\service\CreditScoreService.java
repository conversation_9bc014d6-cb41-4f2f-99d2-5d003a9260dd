package com.lzhshtp.shangcheng.service;

import com.lzhshtp.shangcheng.model.CreditScoreLog;

import java.util.List;
import java.util.Map;

/**
 * 信用分管理服务接口
 */
public interface CreditScoreService {

    /**
     * 更新用户信用分
     * 
     * @param userId 用户ID
     * @param change 分数变化量
     * @param changeType 变更类型
     * @param reason 变更原因
     * @param relatedOrderId 相关订单ID（可选）
     * @param relatedReviewId 相关评价ID（可选）
     * @param operatorId 操作者ID（可选）
     * @return 更新后的信用分
     */
    int updateCreditScore(Long userId, int change, CreditScoreLog.ChangeType changeType, 
                         String reason, Long relatedOrderId, Long relatedReviewId, Long operatorId);

    /**
     * 更新用户信用分（简化版本）
     * 
     * @param userId 用户ID
     * @param change 分数变化量
     * @param changeType 变更类型
     * @param reason 变更原因
     * @return 更新后的信用分
     */
    int updateCreditScore(Long userId, int change, CreditScoreLog.ChangeType changeType, String reason);

    /**
     * 获取用户信用分变更历史
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 变更历史列表
     */
    List<Map<String, Object>> getUserCreditHistory(Long userId, int limit);

    /**
     * 获取用户信用分统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 统计信息
     */
    Map<String, Object> getUserCreditStatistics(Long userId, int days);

    /**
     * 获取系统信用分统计（管理员用）
     * 
     * @param days 统计天数
     * @return 统计信息
     */
    List<Map<String, Object>> getSystemCreditStatistics(int days);

    /**
     * 获取信用等级
     * 
     * @param score 信用分
     * @return 信用等级
     */
    String getCreditLevel(int score);

    /**
     * 获取信用等级颜色
     * 
     * @param score 信用分
     * @return 颜色代码
     */
    String getCreditLevelColor(int score);

    /**
     * 获取信用等级描述
     * 
     * @param score 信用分
     * @return 等级描述
     */
    String getCreditLevelDescription(int score);

    /**
     * 检查用户信用分变更频率（异常检测）
     * 
     * @param userId 用户ID
     * @param hours 小时数
     * @return 变更次数
     */
    int checkCreditChangeFrequency(Long userId, int hours);

    /**
     * 批量更新信用分（系统维护用）
     * 
     * @param updates 更新列表
     * @return 成功更新的数量
     */
    int batchUpdateCreditScore(List<Map<String, Object>> updates);

    /**
     * 重置用户信用分
     * 
     * @param userId 用户ID
     * @param newScore 新的信用分
     * @param reason 重置原因
     * @param operatorId 操作者ID
     * @return 是否成功
     */
    boolean resetCreditScore(Long userId, int newScore, String reason, Long operatorId);

    /**
     * 获取信用分排行榜
     * 
     * @param limit 限制数量
     * @return 排行榜列表
     */
    List<Map<String, Object>> getCreditScoreRanking(int limit);
}
